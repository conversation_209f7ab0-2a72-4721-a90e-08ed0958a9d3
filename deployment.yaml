apiVersion: apps/v1
kind: Deployment
metadata:
  name: ${APP_NAME}
  labels:
    app: ${APP_NAME}
  namespace: ${RUN_ENV}
spec:
  replicas: ${APP_REPLICAS}
  selector:
    matchLabels:
      app: ${APP_NAME}
  template:
    metadata:
      labels:
        app: ${APP_NAME}
    spec:
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - preference:
                matchExpressions:
                  - key: monitor-mem
                    operator: Lt
                    values:
                      - "80"
              weight: 100
      containers:
        - name: ${APP_NAME}
          image: ${CICD_IMAGE}:${CICD_EXECUTION_SEQUENCE}
          ports:
            - containerPort: ${APP_PORT}
---
kind: Service
apiVersion: v1
metadata:
  name: ${APP_NAME}-service
  namespace: ${RUN_ENV}
spec:
  selector:
    app: ${APP_NAME}
  type: NodePort
  ports:
    - protocol: TCP
      port: ${APP_PORT}
      targetPort: ${APP_PORT}