package com.gok.base.admin.feign;

import com.gok.base.admin.common.PmsConstants;
import com.gok.components.common.util.R;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 业务一体化Feign
 *
 * <AUTHOR>
 * @date 2018/6/28
 */
@FeignClient(contextId = "remotePmsService", value = PmsConstants.PMS_SERVICE)
public interface RemotePmsService {

    /**
     *
     * @param from 是否内部调用
     * @return {@link R}
     */
    //@PostMapping("/project-payment/updateLockStatus")
    //R<Void> projectUpdateLockStatus(@RequestHeader(FinancialConstants.FROM) String from);

   
}
