# 滴滴项目同步功能说明

## 功能概述

本功能实现了项目信息与滴滴平台的双向同步，支持智能判断新增和更新操作。

## 核心功能

### 1. 智能同步逻辑

`syncProjectToDidi` 方法会根据是否存在关联关系自动选择操作类型：

- **首次同步（新增）**：如果项目与滴滴项目没有关联关系，调用滴滴的 `addProject` API
- **后续同步（更新）**：如果项目已存在关联关系，调用滴滴的 `updateProject` API

### 2. 关联关系管理

#### 数据库表结构
```sql
CREATE TABLE `dd_project_relation` (
    id bigint(20) NOT NULL COMMENT '主键ID',
    project_id bigint(20) NOT NULL COMMENT '项目id',
    project_no varchar(64) NOT NULL COMMENT '项目编码',
    didi_project_id varchar(64) NOT NULL COMMENT '滴滴项目id',
    sync_time datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '同步时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='项目与滴滴项目关联表';
```

#### 关联关系操作
- **新增操作成功后**：自动保存项目ID、项目编码和滴滴项目ID的关联关系
- **更新操作成功后**：自动更新同步时间（利用数据库的 `ON UPDATE CURRENT_TIMESTAMP` 特性）

## 使用方式

### 基本调用
```java
// 自动判断新增或更新
boolean success = didiSyncService.syncProjectToDidi(projectInfo);
```

### 批量同步
```java
// 批量同步所有项目
didiSyncService.batchSyncProjects();
```

### 手动更新
```java
// 强制使用更新API
boolean updateSuccess = didiSyncService.updateProjectToDidi(projectInfo);
```

## 执行流程

### 新增流程
1. 检查项目是否存在关联关系
2. 不存在关联关系 → 调用 `didiClient.addProject()`
3. 同步成功 → 保存关联关系到 `dd_project_relation` 表
4. 记录详细日志

### 更新流程
1. 检查项目是否存在关联关系
2. 存在关联关系 → 调用 `didiClient.updateProject()`
3. 同步成功 → 更新 `dd_project_relation` 表的同步时间
4. 记录详细日志

## 日志说明

### 关键日志信息
- `检测到已存在关联关系，将进行更新操作` - 表示将使用更新API
- `项目新增成功` / `项目更新成功` - 表示滴滴API调用成功
- `项目关联关系保存成功` - 表示新增操作后关联关系保存成功
- `项目同步时间更新成功` - 表示更新操作后同步时间更新成功

### 错误日志
- `项目新增失败` / `项目更新失败` - 滴滴API调用失败
- `项目关联关系保存失败` - 关联关系保存失败
- `项目同步时间更新失败` - 同步时间更新失败

## 配置参数

```yaml
didi:
  sync:
    project:
      manager: true  # 是否同步项目经理信息
```

## 注意事项

1. **幂等性**：多次调用同一项目的同步方法是安全的
2. **事务性**：滴滴API调用和本地数据库操作不在同一事务中
3. **错误处理**：API调用失败不会影响本地数据
4. **并发控制**：`syncProjectToDidi` 方法使用 `synchronized` 关键字保证线程安全

## 扩展功能

### 查询关联关系
```java
// 根据项目ID查询
DdProjectRelation relation = ddProjectRelationService.getByProjectId(projectId);

// 根据项目编码查询
DdProjectRelation relation = ddProjectRelationService.getByProjectNo(projectNo);

// 根据滴滴项目ID查询
DdProjectRelation relation = ddProjectRelationService.getByDidiProjectId(didiProjectId);
```

### 手动管理关联关系
```java
// 保存或更新关联关系
boolean saved = ddProjectRelationService.saveOrUpdateRelation(projectId, projectNo, didiProjectId);

// 更新同步时间
boolean updated = ddProjectRelationService.updateSyncTime(projectId);
```

## 测试

运行测试类验证功能：
```bash
# 运行关联关系服务测试
mvn test -Dtest=DdProjectRelationServiceTest
```

## 故障排查

1. **同步失败**：检查滴滴API配置和网络连接
2. **关联关系异常**：检查数据库表结构和权限
3. **重复同步**：检查是否存在并发调用
4. **数据不一致**：检查滴滴API返回的项目ID是否正确

