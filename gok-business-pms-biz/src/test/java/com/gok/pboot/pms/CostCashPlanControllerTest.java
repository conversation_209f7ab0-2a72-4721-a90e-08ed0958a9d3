package com.gok.pboot.pms;

import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.cost.controller.CostCashPlanController;
import com.gok.pboot.pms.cost.entity.dto.CostCashPlanDetailDTO;
import com.gok.pboot.pms.cost.entity.dto.CostCashPlanSaveDTO;
import com.gok.pboot.pms.cost.service.ICostCashPlanService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class CostCashPlanControllerTest {

    @Mock
    private ICostCashPlanService costCashPlanService;

    @InjectMocks
    private CostCashPlanController costCashPlanController;

    private CostCashPlanSaveDTO dto;

    @Before
    public void setUp() {
        dto = new CostCashPlanSaveDTO();
        dto.setProjectId(1L);
        
        List<CostCashPlanDetailDTO> planList = new ArrayList<CostCashPlanDetailDTO>();
        CostCashPlanDetailDTO detail = new CostCashPlanDetailDTO();
        detail.setTimeMonth(1);
        detail.setMonthIncome(new BigDecimal("10000"));
        detail.setLaborCost(new BigDecimal("5000"));
        detail.setExpenseCost(new BigDecimal("2000"));
        detail.setOutsourcingCost(new BigDecimal("1000"));
        planList.add(detail);
        
        dto.setPlanList(planList);
    }

    @Test
    public void testSaveCostCashPlan_Success() {
        // 准备测试数据
        doNothing().when(costCashPlanService).saveCostCashPlan(any(CostCashPlanSaveDTO.class));

        // 执行测试
        ApiResult result = costCashPlanController.saveCostCashPlan(dto);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        verify(costCashPlanService, times(1)).saveCostCashPlan(dto);
    }

    @Test
    public void testSaveCostCashPlan_WithEmptyDTO() {
        // 准备测试数据
        CostCashPlanSaveDTO emptyDto = new CostCashPlanSaveDTO();
        doNothing().when(costCashPlanService).saveCostCashPlan(any(CostCashPlanSaveDTO.class));

        // 执行测试
        ApiResult result = costCashPlanController.saveCostCashPlan(emptyDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        verify(costCashPlanService, times(1)).saveCostCashPlan(emptyDto);
    }

    @Test
    public void testSaveCostCashPlan_WithNullPlanList() {
        // 准备测试数据
        dto.setPlanList(null);
        doNothing().when(costCashPlanService).saveCostCashPlan(any(CostCashPlanSaveDTO.class));

        // 执行测试
        ApiResult result = costCashPlanController.saveCostCashPlan(dto);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        verify(costCashPlanService, times(1)).saveCostCashPlan(dto);
    }

    @Test
    public void testSaveCostCashPlan_WithEmptyPlanList() {
        // 准备测试数据
        dto.setPlanList(new ArrayList<CostCashPlanDetailDTO>());
        doNothing().when(costCashPlanService).saveCostCashPlan(any(CostCashPlanSaveDTO.class));

        // 执行测试
        ApiResult result = costCashPlanController.saveCostCashPlan(dto);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        verify(costCashPlanService, times(1)).saveCostCashPlan(dto);
    }
}