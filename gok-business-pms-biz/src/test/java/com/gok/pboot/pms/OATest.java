package com.gok.pboot.pms;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.gok.pboot.pms.entity.CustomerBusiness;
import com.gok.pboot.pms.entity.CustomerBusinessUnit;
import com.gok.pboot.pms.service.ICustomerBusinessService;
import com.gok.pboot.pms.service.ICustomerBusinessUnitService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@Slf4j
@SpringBootTest(classes = PmsServiceApplication.class)
public class OATest {
    @Autowired
    private ICustomerBusinessService customerBusinessService;

    @Autowired
    private ICustomerBusinessUnitService customerBusinessUnitService;

    @Test
    void addCustomerBusinessSyncOA() {
        CustomerBusiness customerBusiness = new CustomerBusiness();
        customerBusiness.setName("西游记");
        customerBusiness.setId(IdWorker.getId());
        customerBusinessService.addCustomerBusinessSyncOA(customerBusiness);
    }

    @Test
    void updateCustomerBusinessSyncOA() {
        CustomerBusiness customerBusiness = new CustomerBusiness();
        customerBusiness.setName("经营单元测试111");
        customerBusiness.setId(1856515954364162049l);
        customerBusinessService.updateCustomerBusinessSyncOA(customerBusiness);
    }

    @Test
    void deleteCustomerBusinessSyncOA() {
        customerBusinessService.deleteCustomerBusinessSyncOA("1856515954364162049");
    }

    @Test
    void addCustomerBusinessUnitSyncOA() {
        CustomerBusinessUnit customerBusinessUnit = new CustomerBusinessUnit();
        customerBusinessUnit.setUnitName("经营单元测试二");
        customerBusinessUnit.setId(IdWorker.getId());
        customerBusinessUnit.setBusinessId(1856515954364162049l);
        customerBusinessUnitService.addCustomerBusinessUnitSyncOA(customerBusinessUnit);
    }

    @Test
    void updateCustomerBusinessUnitSyncOA() {
        CustomerBusinessUnit customerBusinessUnit = new CustomerBusinessUnit();
        customerBusinessUnit.setUnitName("经营单元测试一");
        customerBusinessUnit.setId(1856528048174243841l);
        customerBusinessUnit.setBusinessId(1856530385563344897l);
        customerBusinessUnitService.updateCustomerBusinessUnitSyncOA(customerBusinessUnit);
    }

    @Test
    void deleteCustomerBusinessUnitSyncOA() {
        customerBusinessUnitService.deleteCustomerBusinessUnitSyncOA("1856515954364162049");
    }

    @Test
    void saveSelectItemSyncOA() {
        customerBusinessUnitService.saveSelectItemSyncOA();
    }
}
