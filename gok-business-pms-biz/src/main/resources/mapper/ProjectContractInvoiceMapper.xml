<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.gok.pboot.pms.mapper.ProjectContractInvoiceMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.gok.pboot.pms.entity.domain.ProjectContractInvoice" id="projectContractInvoiceRecordsMap">
        <result property="id" column="id"/>
        <result property="requestid" column="requestid"/>
        <result property="fpdm" column="fpdm"/>
        <result property="fphm" column="fphm"/>
        <result property="kplx" column="kplx"/>
        <result property="fpzt" column="fpzt"/>
        <result property="fprq" column="fprq"/>
        <result property="fpje" column="fpje"/>
        <result property="kpsl" column="kpsl"/>
        <result property="fpsmj" column="fpsmj"/>
        <result property="nodeoperator" column="nodeoperator"/>
    </resultMap>
    <select id="getContractInvoiceVoListByContractIds"
            resultType="com.gok.pboot.pms.entity.vo.ContractInvoiceVo">
        select  a.id as mainid,c.* from  contract_ledger a
        LEFT JOIN  contract_ledger_detail b on  a.id=b.mainid
        LEFT JOIN  contract_invoice c on  b.id=c.llhtmxskbh
        where a.id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>


</mapper>