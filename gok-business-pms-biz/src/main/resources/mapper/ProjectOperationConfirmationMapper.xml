<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.gok.pboot.pms.mapper.ProjectOperationConfirmationMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.gok.pboot.pms.entity.domain.ProjectOperationConfirmation" id="projectOperationConfirmationMap">
        <result property="id" column="id"/>
        <result column="project_id" property="projectId"/>
        <result column="user_id" property="userId"/>
        <result column="user_name" property="userName"/>
        <result column="role" property="role"/>
        <result column="operation_type" property="operationType"/>
        <result column="confirm_time" property="confirmTime"/>
        <result column="reminder_date" property="reminderDate"/>
        <result column="creator" property="creator"/>
        <result column="create_id" property="creatorId"/>
        <result column="modifier" property="modifier"/>
        <result column="modifier_id" property="modifierId"/>
        <result column="ctime" property="ctime"/>
        <result column="mtime" property="mtime"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

    <select id="getProjectRoleIdMap" resultType="java.util.Map">
        SELECT
            pi.manager_user_id AS managerUserId,
            pi.salesman_user_id AS salesmanUserId,
            mr1.leader_id AS managerUserLeaderId,
            mr2.leader_id AS salesmanUserLeaderId
        FROM
            project_info AS pi
                LEFT JOIN mhour_roster AS mr1 ON mr1.id = pi.manager_user_id
                LEFT JOIN mhour_roster AS mr2 ON mr2.id = pi.salesman_user_id
        WHERE
            pi.id = #{projectId}
    </select>


</mapper>