<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.mapper.OperatingRecordMapper">


        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.gok.pboot.pms.entity.OperatingRecord">
        <id column="id" property="id"/>
        <result column="operationType" property="operationType"/>
        <result column="operationInfo" property="operationInfo"/>
        <result column="taskId" property="taskId"/>
        <result column="operator" property="operator"/>
        <result column="creator" property="creator"/>
        <result column="creatorId" property="creatorId"/>
        <result column="modifier" property="modifier"/>
        <result column="modifierId" property="modifierId"/>
        <result column="ctime" property="ctime"/>
        <result column="mtime" property="mtime"/>
        <result column="delFlag" property="delFlag"/>
    </resultMap>


        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
					a.id AS 'id',
					a.operation_type AS 'operationType',
					a.operation_info AS 'operationInfo',
					a.task_id AS 'taskId',
					a.operator AS 'operator',
					a.creator AS 'creator',
					a.creator_id AS 'creatorId',
					a.modifier AS 'modifier',
					a.modifier_id AS 'modifierId',
					a.ctime AS 'ctime',
					a.mtime AS 'mtime',
                    a.del_flag AS 'delFlag'
        </sql>
    
    <sql id="join"></sql>

    <select id="findList" resultType="com.gok.pboot.pms.entity.OperatingRecord">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mhour_operating_record a
        <include refid="join"/>
        <where>
            a.del_flag = 0
        </where>
        ORDER BY a.id desc
    </select>

    <!--逻辑删除-->
    <update id="deleteByLogic">
        UPDATE mhour_operating_record SET
        del_flag = 1
        WHERE id = #{id}
    </update>


    <insert id="batchSave">
        INSERT INTO mhour_operating_record (
                id,
                operation_type,
                operation_info,
                task_id,
                operator,
                creator,
                creator_id,
                modifier,
                modifier_id,
                ctime,
                mtime,
                del_flag
        )VALUES
        <foreach collection="poList" item="item" separator=",">
            (
                    #{item.id},
                    #{item.operationType},
                    #{item.operationInfo},
                    #{item.taskId},
                    #{item.operator},
                    #{item.creator},
                    #{item.creatorId},
                    #{item.modifier},
                    #{item.modifierId},
                    #{item.ctime},
                    #{item.mtime},
                    #{item.delFlag}
            )
        </foreach>
    </insert>

    <update id="batchDel">
        update mhour_operating_record set del_flag = 1 where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <insert id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            UPDATE mhour_operating_record  SET
                    id = #{item.id},
                    operation_type = #{item.operationType},
                    operation_info = #{item.operationInfo},
                    task_id = #{item.taskId},
                    operator = #{item.operator},
                    creator = #{item.creator},
                    creator_id = #{item.creatorId},
                    modifier = #{item.modifier},
                    modifier_id = #{item.modifierId},
                    ctime = #{item.ctime},
                    mtime = #{item.mtime},
                    del_flag = #{item.delFlag}
            where id = #{item.id}
        </foreach>
    </insert>

    <!--批量更新非空字段-->
    <update id="updateBatch" parameterType="arraylist">
        update mhour_operating_record
        <trim prefix="set" suffixOverrides=",">
                    <trim prefix=" operation_type =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.operationType!=null">
                                when id=#{item.id} then #{item.operationType}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" operation_info =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.operationInfo!=null">
                                when id=#{item.id} then #{item.operationInfo}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" task_id =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.taskId!=null">
                                when id=#{item.id} then #{item.taskId}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" operator =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.operator!=null">
                                when id=#{item.id} then #{item.operator}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" modifier =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.modifier!=null">
                                when id=#{item.id} then #{item.modifier}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" modifier_id =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.modifierId!=null">
                                when id=#{item.id} then #{item.modifierId}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" mtime =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.mtime!=null">
                                when id=#{item.id} then #{item.mtime}
                            </if>
                        </foreach>
                    </trim>
        </trim>

        where
        id in
        <foreach collection="list" separator="," item="item" open="(" close=")">
            #{item.id}
        </foreach>
    </update>

    <select id="findOperatingRecordPage" resultType="com.gok.pboot.pms.entity.vo.OperatingRecordPageVO">
        SELECT
                a.ctime AS 'ctime',
                a.operation_type AS 'operationType',
                (CASE   WHEN a.operation_type =  0 THEN '移除参与人'
                        WHEN a.operation_type =  1 THEN '添加参与人'
                        WHEN a.operation_type =  2 THEN '状态变更'
                        WHEN a.operation_type =  3 THEN '移除负责人'
                        WHEN a.operation_type =  4 THEN '添加负责人'
                        END) AS 'operationTypeName',
                a.task_id AS 'taskId',
                a.operation_info AS 'operationInfo',
                a.operator AS 'operator'
        FROM mhour_operating_record a
        <where>
            a.del_flag = 0
        AND a.task_id = #{id}
        </where>
        ORDER BY a.ctime desc
    </select>

</mapper>
