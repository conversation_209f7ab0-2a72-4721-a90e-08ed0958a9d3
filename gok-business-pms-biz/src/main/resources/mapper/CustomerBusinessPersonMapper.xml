<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.mapper.CustomerBusinessPersonMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.gok.pboot.pms.entity.CustomerBusinessPerson">
        <id column="id" property="id" />
        <result column="business_id" property="businessId" />
        <result column="manager_id" property="managerId" />
        <result column="manager_name" property="managerName" />
        <result column="manager_dept_id" property="managerDeptId" />
        <result column="manager_dept_name" property="managerDeptName"/>
        <result column="manager_role" property="managerRole" />
        <result column="creator" property="creator" />
        <result column="creator_id" property="creatorId" />
        <result column="modifier" property="modifier" />
        <result column="modifier_id" property="modifierId" />
        <result column="ctime" property="ctime" />
        <result column="mtime" property="mtime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, business_id, manager_id, manager_name, manager_dept_id,manager_dept_name, manager_role, creator, creator_id, modifier, modifier_id, ctime, mtime, del_flag
    </sql>

  <!--  批量逻辑删除  -->
  <update id="batchDel">
    UPDATE customer_business_person SET del_flag = 1 WHERE id IN
    <foreach collection="list" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </update>


  <select id="findId" resultType="java.lang.Long">
    select distinct
    t.business_id
    from (
    select
    a.business_id
    from
    customer_business_person a
    <where>
      a.del_flag = 0
      <if test='filter.scope != "all"'>
        and (
        a.manager_id in
        <foreach collection="filter.userIdList" item="uid" open="(" separator="," close=")">
          #{uid}
        </foreach>
        )
      </if>
    </where>
    ) t
  </select>


  <delete id="deletedByBusinessId">
    delete from customer_business_person  WHERE business_id = #{businessId}
  </delete>

</mapper>
