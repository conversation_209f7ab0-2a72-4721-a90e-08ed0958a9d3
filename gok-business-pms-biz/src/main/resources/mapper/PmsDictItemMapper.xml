<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.gok.pboot.pms.mapper.PmsDictItemMapper">

    <select id="findAll" resultType="com.gok.pboot.pms.entity.domain.PmsDictItem">
        SELECT
            a.dict_id,
            a.dict_type,
            a.item_value,
            a.label
        FROM pms_dict_item a
        WHERE del_flag = 0 AND a.parent_id = -1;
    </select>

    <select id="findSubItem" resultType="com.gok.pboot.pms.entity.domain.PmsDictItem">
        SELECT
            a.dict_id,
            a.dict_type,
            a.item_value,
            a.label
        FROM pms_dict_item a
        WHERE del_flag = 0 AND a.parent_id != -1;
    </select>

    <select id="findDictItemByDictType" resultType="com.gok.pboot.pms.entity.domain.PmsDictItem">
        SELECT
            a.dict_id,
            a.dict_type,
            a.item_value,
            a.label
        FROM pms_dict_item a
        WHERE a.del_flag = 0 AND a.dict_type = #{dictType};
    </select>
    <select id="findDictMapByTypeList" resultType="com.gok.pboot.pms.entity.domain.PmsDictItem">
        SELECT
            a.dict_id,
            a.dict_type,
            a.item_value,
            a.label
        FROM pms_dict_item a
        WHERE a.del_flag = 0
        AND a.parent_id = -1
        AND a.dict_type IN
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="findDictItemMultiLevelByDictType"
            resultType="com.gok.pboot.pms.entity.domain.PmsDictItemMultiLevel">
        SELECT
            a.id,
            a.dict_type,
            a.item_value AS "value",
            a.label,
            a.parent_id
        FROM pms_dict_item a
        WHERE a.del_flag = 0 AND a.dict_type = #{dictType}
        ORDER BY a.id
    </select>

</mapper>