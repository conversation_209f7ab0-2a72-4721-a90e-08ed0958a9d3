<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.cost.mapper.CostDeliverTaskMapper">

    <select id="findByCostDeliverTaskDTO" resultType="com.gok.pboot.pms.cost.entity.vo.CostDeliverTaskVO">
        SELECT
            t1.id,
            t1.parent_id,
            t1.project_id,
            t1.project_name,
            t1.task_name,
            t1.task_type,
            t1.task_level,
            t1.task_status,
            t1.task_desc,
            t1.account_id,
            t1.account_oa_id,
            t1.account_name,
            t1.tax_rate,
            t1.budget_cost as budgetCostStr,
            t1.disassembly_type,
            t1.manager_id,
            t1.manager_name,
            t1.start_date,
            t1.end_date,
            IF(t1.disassembly_type = 0, t1.estimated_hours, NULL) as estimated_hours,
            IF(t1.disassembly_type = 0, t1.normal_hours, NULL) as normal_hours,
            IF(t1.disassembly_type = 0, t1.work_overtime_hours, NULL) as work_overtime_hours,
            IF(t1.disassembly_type = 0, t1.rest_overtime_hours, NULL) as rest_overtime_hours,
            IF(t1.disassembly_type = 0, t1.holiday_overtime_hours, NULL) as holiday_overtime_hours,
            t1.actual_labor_cost as actualLaborCostStr,
            t1.income,
            t1.submit_completion_time,
            t1.audit_time,
            t1.completion_time,
            t1.return_reason,
            t1.return_time,
            t1.return_user_id as returnerId,
            t1.return_user_name as returnerName,
            t1.task_category,
            t1.default_conf,
            t1.creator_id,
            t1.creator,
            t3.id AS top_level_id,
            t1.ctime,
            t1.evaluation_status
        FROM
            cost_deliver_task AS t1
            LEFT JOIN cost_deliver_task AS t2 ON t1.parent_id = t2.id
            LEFT JOIN cost_deliver_task AS t3 ON t2.parent_id = t3.id
        WHERE
            t1.del_flag = ${@<EMAIL>()}
        <if test="query.projectId != null">
            AND t1.project_id = #{query.projectId}
        </if>
        <if test="query.projectIds != null and query.projectIds.size() > 0 ">
            AND t1.project_id IN
            <foreach collection="query.projectIds" item="projectId" open="(" separator="," close=")">
                #{projectId}
            </foreach>
        </if>
        <if test="query.managerId != null">
            AND t1.manager_id = #{query.managerId}
        </if>
        <if test="query.userId != null">
            AND (t1.manager_id = #{query.userId} or t1.creator_id = #{query.userId})
        </if>
        <if test="query.parentId != null">
            AND t1.parent_id = #{query.parentId}
        </if>
        <if test="query.startDate != null and query.endDate != null">
            AND ((t1.start_date BETWEEN #{query.startDate} AND #{query.endDate}) OR (t1.end_date BETWEEN #{query.startDate} AND #{query.endDate}))
        </if>
        <if test="query.startSubmitCompletionTime != null and query.endSubmitCompletionTime != null">
        AND t1.submit_completion_time >= #{query.startSubmitCompletionTime} AND t1.submit_completion_time &lt;= #{query.endSubmitCompletionTime}
        </if>
        <if test="query.taskStatus != null">
            AND t1.task_status = #{query.taskStatus}
        </if>
        <if test="query.projectName != null and query.projectName != ''">
            AND t1.project_name like CONCAT('%',#{query.projectName},'%')
        </if>
          <if test="query.projectHeaderId !=null and query.projectHeaderId !=''">
            and (t2.manager_id = #{query.projectHeaderId} or t3.manager_id = #{query.projectHeaderId})
          </if>
        <if test="query.taskStatusList != null and query.taskStatusList.size() > 0">
        AND t1.task_status IN
        <foreach collection="query.taskStatusList" item="status" open="(" separator="," close=")">
          #{status}
        </foreach>
        </if>
        <if test="query.approvalStatus == 0 and query.projectManagerApproval ==1 ">
          and (t1.task_status =${@<EMAIL>()} or t3.task_status =${@<EMAIL>()})
        </if>
        <if test="query.approvalStatus == 1 and query.projectManagerApproval ==1 ">
          and (t1.task_status =${@<EMAIL>()})
        </if>
        <if test="query.taskLevel != null">
            AND t1.task_level = #{query.taskLevel}
        </if>
        <if test="query.taskType != null">
            and t1.task_type = #{query.taskType}
        </if>
        ORDER BY t1.task_category,
                 t1.ctime DESC,
                 t1.id
    </select>

    <select id="findByIdList" resultType="com.gok.pboot.pms.cost.entity.vo.CostDeliverTaskVO">
        SELECT
        id,
        parent_id,
        project_id,
        project_name,
        task_name,
        task_type,
        task_level,
        task_status,
        task_desc,
        account_id,
        account_oa_id,
        account_name,
        tax_rate,
        budget_cost as budgetCostStr,
        disassembly_type,
        manager_id,
        manager_name,
        start_date,
        end_date,
        deliver_desc,
        complete_files,
        IF(disassembly_type = 0, estimated_hours, NULL) as estimated_hours,
        IF(disassembly_type = 0, normal_hours, NULL) as normal_hours,
        IF(disassembly_type = 0, work_overtime_hours, NULL) as work_overtime_hours,
        IF(disassembly_type = 0, rest_overtime_hours, NULL) as rest_overtime_hours,
        IF(disassembly_type = 0, holiday_overtime_hours, NULL) as holiday_overtime_hours,
        actual_labor_cost as actualLaborCostStr,
        income,
        submit_completion_time,
        audit_time,
        completion_time,
        return_reason,
        return_time,
        return_user_id,
        return_user_name,
        task_category,
        evaluation_status
        FROM
        cost_deliver_task
        <where>
            <if test="idList != null and idList.size > 0">
                id IN
                <foreach collection="idList" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="parentIdList != null and parentIdList.size > 0">
                parent_id IN
                <foreach collection="parentIdList" item="parentId" index="index" open="(" close=")" separator=",">
                    #{parentId}
                </foreach>
            </if>
            <if test="createId != null">
                AND creator_id = #{createId}
            </if>
            <if test="taskType != null">
                AND task_type = #{taskType}
            </if>
            AND del_flag = ${@<EMAIL>()}
        </where>
        ORDER BY ctime DESC
    </select>
  <select id="findTaskApprovalList" resultType="com.gok.pboot.pms.cost.entity.vo.CostDeliverApprovalVO">
    select t.project_id, t.project_name
    from cost_deliver_task t
    inner join project_info p on p.id = t.project_id
    left join cost_deliver_task t2 on t2.parent_id = t.id and t2.del_flag = ${@<EMAIL>()}
    WHERE
    t.del_flag = ${@<EMAIL>()}
    <if test="query.projectName != null and query.projectName != ''">
      AND t.project_name like CONCAT('%',#{query.projectName},'%')
    </if>
     <if test="query.approvalStatus == 0" >
       and ((p.manager_user_id = #{query.managerId} and t.task_status = ${@<EMAIL>()} and t.task_level = 1)
       or (t.manager_id =#{query.managerId} and t2.task_status in ( ${@<EMAIL>()},  ${@<EMAIL>()})
       and t.task_level in(1, 2)))
     </if>
    <if test="query.approvalStatus == 1" >
      and ((p.manager_user_id = #{query.managerId} and t.task_status = ${@<EMAIL>()} and t.task_level = 1)
      or (t.manager_id =#{query.managerId}
        and t2.task_status in ( ${@<EMAIL>()},
                                ${@<EMAIL>()},
                                ${@<EMAIL>()})
      and t.task_level in(1, 2)))
    </if>
    group by t.project_id, t.project_name
    ORDER BY t.ctime DESC
  </select>


  <select id="findProjectManagerTask" resultType="com.gok.pboot.pms.cost.entity.domain.CostDeliverTask">
    select t.*
    from cost_deliver_task t
    inner join project_info p on p.id = t.project_id
    WHERE t.del_flag = ${@<EMAIL>()}
    and p.manager_user_id = #{query.projectManagerUserId}
    and t.task_level = 1
    and t.task_status =#{query.taskStatus}
    and t.project_id = #{query.projectId}
  </select>

  <select id="findTaskAndParent" resultType="com.gok.pboot.pms.cost.entity.domain.CostDeliverTask">
    select t1.*
    from cost_deliver_task t1 left join cost_deliver_task t2 on t1.parent_id = t2.id and t2.del_flag = ${@<EMAIL>()}
    WHERE
      t1.del_flag = ${@<EMAIL>()}
    <if test="query.projectId != null">
      AND t1.project_id = #{query.projectId}
    </if>
    <if test="query.taskStatusList != null and query.taskStatusList.size() > 0">
      AND t1.task_status IN
      <foreach collection="query.taskStatusList" item="status" open="(" separator="," close=")">
        #{status}
      </foreach>
    </if>
    <if test="query.managerId !=null and query.managerId !=''">
      and t2.manager_id = #{query.managerId}
    </if>
    <if test="query.taskLevelList != null and query.taskLevelList.size() > 0">
      AND t1.task_level IN
      <foreach collection="query.taskLevelList" item="taskLevel" open="(" separator="," close=")">
        #{taskLevel}
      </foreach>
    </if>
    ORDER BY t1.ctime DESC
  </select>

    <!-- 查询当前用户可用的工单列表 -->
    <select id="findByUserIdForDailyPaperEntry" resultType="com.gok.pboot.pms.cost.entity.vo.CostDeliverTaskVO">
        SELECT
            t.id,
            t.project_id,
            t.project_name,
            t.task_name,
            t.task_type,
            t.task_status,
            t.manager_id,
            t.manager_name
        FROM cost_deliver_task t
        WHERE t.del_flag = 0
        AND t.manager_id = #{userId}
        AND t.task_status != 1  <!-- 排除已完成的工单 -->
        AND t.start_date &lt;= #{date}
        AND (t.end_date IS NULL OR t.end_date >= #{date})
        ORDER BY t.ctime DESC
    </select>

    <!-- 异常工单查询 -->
    <select id="findAbnormalPage" resultType="com.gok.pboot.pms.cost.entity.vo.CostTaskAbnormalVO">
        SELECT
            t.id,
            t.task_name AS taskName,
            p.id AS projectId,
            p.item_name AS projectName,
            t.task_category,
            t.task_desc,
            t.disassembly_type,
            t.manager_id,
            t.manager_name,
            t.start_date,
            t.end_date,
            t.task_status,
            t.submit_completion_time as submitCompletionTime,
            t.audit_time,
            p.manager_user_id AS projectManagerId,
            p.manager_user_name AS projectManagerName,
            COALESCE(pt.manager_id, p.manager_user_id) AS reviewerId,
            COALESCE(pt.manager_name, p.manager_user_name) AS reviewerName,
            CASE
                WHEN t.end_date &lt; CURRENT_DATE AND t.task_status = 1 THEN 1
                WHEN t.submit_completion_time IS NOT NULL AND CURRENT_DATE &gt; t.submit_completion_time AND t.task_status in(2,4,6) THEN 2
                WHEN t.disassembly_type = 1 AND t.start_date &lt;= CURRENT_DATE + 60 AND t.end_date &gt;= CURRENT_DATE AND t.task_status = 0 THEN 3
                WHEN t.evaluation_status = 0 AND t.task_status = 7 and t.disassembly_type = 0 THEN 4
                ELSE NULL
            END as abnormalType
        FROM cost_deliver_task t
        INNER JOIN project_info p ON t.project_id = p.id
        LEFT JOIN cost_deliver_task pt ON t.parent_id = pt.id
        WHERE t.del_flag = ${@<EMAIL>()}
        <if test="request.startDate != null">
            AND t.start_date &lt;= #{request.endDate}
            AND t.end_date &gt;= #{request.startDate}
        </if>
        <if test="request.taskOwnerIds != null and request.taskOwnerIds.size() > 0">
            AND t.manager_id IN
            <foreach collection="request.taskOwnerIds" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
        <if test="request.reviewerIds != null and request.reviewerIds.size() > 0">
            AND (
                (t.parent_id IS NOT NULL AND pt.manager_id IN
                    <foreach collection="request.reviewerIds" item="userId" open="(" separator="," close=")">
                        #{userId}
                    </foreach>
                )
                OR
                (t.parent_id IS NULL AND p.manager_user_id IN
                    <foreach collection="request.reviewerIds" item="userId" open="(" separator="," close=")">
                        #{userId}
                    </foreach>
                )
            )
        </if>
        <if test="request.projectName != null and request.projectName != ''">
            AND p.item_name LIKE CONCAT('%', #{request.projectName}, '%')
        </if>
        <if test="request.submitStartDate != null">
            AND t.submit_completion_time >= #{request.submitStartDate}
        </if>
        <if test="request.submitEndDate != null">
            AND t.submit_completion_time &lt;= #{request.submitEndDate}
        </if>
        <if test="request.abnormalType != null">
            AND (
                (t.end_date &lt; CURRENT_DATE AND t.task_status = 1 AND 1 = #{request.abnormalType})
                OR (t.submit_completion_time IS NOT NULL AND CURRENT_DATE &gt; t.submit_completion_time AND t.task_status in(2,4,6) AND 2 = #{request.abnormalType})
                OR (t.disassembly_type = 1 AND t.start_date &lt;= CURRENT_DATE + 60 AND t.end_date &gt;= CURRENT_DATE AND t.task_status = 0 AND 3 = #{request.abnormalType})
                OR (t.evaluation_status = 0 AND t.task_status = 7 and t.disassembly_type = 0 AND 4 = #{request.abnormalType})
            )
        </if>
        <if test="request.abnormalType == null ">
            AND (
                (t.end_date &lt; CURRENT_DATE AND t.task_status = 1)
                OR (t.submit_completion_time IS NOT NULL AND CURRENT_DATE &gt; t.submit_completion_time AND t.task_status in(2,4,6))
                OR (t.disassembly_type = 1 AND t.start_date &lt;= CURRENT_DATE + 60 AND t.end_date &gt;= CURRENT_DATE AND t.task_status = 0)
                OR (t.evaluation_status = 0 AND t.task_status = 7 and t.disassembly_type = 0)
            )
        </if>
        ORDER BY t.start_date, t.ctime
    </select>


<select id="findPreManagerTask" resultType="com.gok.pboot.pms.cost.entity.domain.CostDeliverTask">
    select t.*
    from cost_deliver_task t
    inner join project_info p on p.id = t.project_id
    WHERE t.del_flag = ${@<EMAIL>()}
    and case
        when p.pre_sale_user_id != t.manager_id then p.pre_sale_user_id = #{query.projectManagerUserId}
        <if test="query.subUserIds != null and query.subUserIds.size() > 0">
            when p.pre_sale_user_id = t.manager_id then p.pre_sale_user_id in
            <foreach collection="query.subUserIds" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
        end
    and t.task_level = 1
    <if test="query.projectId != null">
        and t.project_id = #{query.projectId}
    </if>
    <if test="query.projectIds != null and query.projectIds.size() != 0">
        and t.project_id in
        <foreach collection="query.projectIds" item="projectId" open="(" separator="," close=")">
            #{projectId}
        </foreach>
    </if>
  </select>
    <select id="getMaxSequenceNumberByDate" resultType="java.lang.Integer">
        SELECT COALESCE(MAX(CAST(SUBSTRING(task_no, -4) AS SIGNED)), 0)
        FROM cost_deliver_task
        WHERE task_no LIKE CONCAT(#{prefix}, '-%-%-%', #{dateStr}, '-%')
    </select>

    <select id="countAbnormalGroupByType" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            abnormalType,
            COUNT(1) AS count
        FROM (
            SELECT
                t.id,
                CASE
                    WHEN t.end_date &lt; CURRENT_DATE  AND t.task_status = 1 THEN 1
                    WHEN t.submit_completion_time IS NOT NULL AND CURRENT_DATE &gt; t.submit_completion_time AND t.task_status in(2,4,6) THEN 2
                    WHEN t.disassembly_type = 1 AND t.start_date &lt;= CURRENT_DATE + 60 AND t.end_date &gt;= CURRENT_DATE AND t.task_status = 0 THEN 3
                    WHEN t.evaluation_status = 0 AND t.task_status = 7 and t.disassembly_type = 0 THEN 4
                END as abnormalType
            FROM cost_deliver_task t
            INNER JOIN project_info p ON t.project_id = p.id
            WHERE t.del_flag = ${@<EMAIL>()}
            <if test="request.startDate != null">
                AND t.start_date &lt;= #{request.endDate}
                AND t.end_date &gt;= #{request.startDate}
            </if>
            <if test="request.taskOwnerIds != null and request.taskOwnerIds.size() > 0">
                AND t.manager_id IN
                <foreach collection="request.taskOwnerIds" item="userId" open="(" separator="," close=")">
                    #{userId}
                </foreach>
            </if>
            <if test="request.reviewerIds != null and request.reviewerIds.size() > 0">
                AND (
                    (t.parent_id IS NOT NULL AND (
                        SELECT pt.manager_id FROM cost_deliver_task pt WHERE pt.id = t.parent_id
                    ) IN
                    <foreach collection="request.reviewerIds" item="userId" open="(" separator="," close=")">
                        #{userId}
                    </foreach>
                    )
                    OR
                    (t.parent_id IS NULL AND p.manager_user_id IN
                    <foreach collection="request.reviewerIds" item="userId" open="(" separator="," close=")">
                        #{userId}
                    </foreach>
                    )
                )
            </if>
            <if test="request.projectName != null and request.projectName != ''">
                AND p.item_name LIKE CONCAT('%', #{request.projectName}, '%')
            </if>
            <if test="request.submitStartDate != null">
                AND t.submit_completion_time >= #{request.submitStartDate}
            </if>
            <if test="request.submitEndDate != null">
                AND t.submit_completion_time &lt;= #{request.submitEndDate}
            </if>
            <if test="request.abnormalType != null">
                AND CASE
                    WHEN t.end_date &lt; CURRENT_DATE  AND t.task_status = 1 THEN 1
                    WHEN t.submit_completion_time IS NOT NULL AND CURRENT_DATE &gt; t.submit_completion_time AND t.task_status in(2,4,6) THEN 2
                    WHEN t.disassembly_type = 1 AND t.start_date &lt;= CURRENT_DATE + 60 AND t.end_date &gt;= CURRENT_DATE AND t.task_status = 0 THEN 3
                    WHEN t.evaluation_status = 0 AND t.task_status = 7 and t.disassembly_type = 0 THEN 4
                END = #{request.abnormalType}
            </if>
        ) t
        WHERE abnormalType IS NOT NULL
        GROUP BY abnormalType
    </select>

    <select id="findTaskApprovalProjectPage" resultType="com.gok.pboot.pms.cost.entity.vo.CostDeliverApprovalVO">
        select t.project_id, t.project_name
        from cost_deliver_task t
        left join project_info p on p.id = t.project_id
        WHERE
        t.del_flag = ${@<EMAIL>()}
        and t.disassembly_type=0
        <if test="query.projectName != null and query.projectName != ''">
            AND t.project_name like CONCAT('%',#{query.projectName},'%')
        </if>
        <if test="query.approvalStatus == 0">
            and (
            (p.manager_user_id = #{query.userId} and t.task_status in
            (${@<EMAIL>()},
            ${@<EMAIL>()}))
            or (t.creator_id = #{query.userId} and t.task_status =
            ${@<EMAIL>()})
            )
        </if>
        <if test="query.approvalStatus == 1">
            and (
            (p.manager_user_id = #{query.userId} and t.task_status =
            ${@<EMAIL>()})
            or (t.creator_id = #{query.userId}
            and t.task_status in (${@<EMAIL>()},
            ${@<EMAIL>()}))
            )
        </if>
        group by t.project_id, t.project_name
        ORDER BY t.ctime DESC
    </select>

    <select id="findTaskApprovalTasks" resultType="com.gok.pboot.pms.cost.entity.domain.CostDeliverTask">
        select t.*
        from cost_deliver_task t
        left join project_info p on p.id = t.project_id
        WHERE
        t.del_flag = ${@<EMAIL>()}
        and t.disassembly_type = 0
        <if test="query.approvalStatus == 0" >
            and (
            (p.manager_user_id = #{query.userId} and t.task_status in
            (${@<EMAIL>()},
            ${@<EMAIL>()}))
            or (t.creator_id = #{query.userId} and t.task_status =
            ${@<EMAIL>()})
            )
        </if>
        <if test="query.approvalStatus == 1" >
            and (
            (p.manager_user_id = #{query.userId} and t.task_status = ${@<EMAIL>()})
            or (t.creator_id = #{query.userId}
            and t.task_status in (${@<EMAIL>()},
            ${@<EMAIL>()}))
            )
        </if>
        <if test="query.projectIds != null and query.projectIds.size() > 0">
            AND t.project_id IN
            <foreach collection="query.projectIds" item="projectId" open="(" separator="," close=")">
                #{projectId}
            </foreach>
        </if>
    </select>

    <select id="findProjectApprovalPage" resultType="com.gok.pboot.pms.cost.entity.vo.CostDeliverTaskVO">
        select
        t.id,
        t.parent_id,
        t.project_id,
        t.project_name,
        t.task_name,
        t.task_type,
        t.task_level,
        t.task_status,
        t.task_desc,
        t.account_id,
        t.account_oa_id,
        t.account_name,
        t.tax_rate,
        t.budget_cost as budgetCostStr,
        t.disassembly_type,
        t.manager_id,
        t.manager_name,
        t.start_date,
        t.end_date,
        IF(t.disassembly_type = 0, t.estimated_hours, NULL) as estimated_hours,
        IF(t.disassembly_type = 0, t.normal_hours, NULL) as normal_hours,
        IF(t.disassembly_type = 0, t.work_overtime_hours, NULL) as work_overtime_hours,
        IF(t.disassembly_type = 0, t.rest_overtime_hours, NULL) as rest_overtime_hours,
        IF(t.disassembly_type = 0, t.holiday_overtime_hours, NULL) as holiday_overtime_hours,
        t.actual_labor_cost as actualLaborCostStr,
        t.income,
        t.submit_completion_time,
        t.audit_time,
        t.completion_time,
        t.return_reason,
        t.return_time,
        t.task_category,
        t.evaluation_status,
        t.creator_id,
        t.creator
        from cost_deliver_task t
        left join project_info p on p.id = t.project_id
        <where>
            t.project_id = #{query.projectId}
            and task_type = 1
            and t.disassembly_type = 0
            and t.del_flag = ${@<EMAIL>()}
            <if test="query.approvalStatus == 0">
                and ((p.manager_user_id = #{query.userId} and
                t.task_status in (${@<EMAIL>()}
                <if test="query.personal == null or !query.personal">
                    ,${@<EMAIL>()}
                </if>
                ))
                or (t.creator_id = #{query.userId} and
                t.task_status = ${@<EMAIL>()}))
            </if>
            <if test="query.approvalStatus == 1">
                and ( (t.creator_id = #{query.userId} and t.task_status in
                (${@<EMAIL>()},
                ${@<EMAIL>()}))
                or (
                p.manager_user_id = #{query.userId} and
                t.task_status = ${@<EMAIL>()}
                ) )
            </if>

            <if test="query.startDate != null and query.endDate != null">
                AND ((t.start_date BETWEEN #{query.startDate}
                AND #{query.endDate}) OR (t.end_date BETWEEN
                #{query.startDate} AND #{query.endDate}))
            </if>
            <if test="query.startSubmitCompletionTime != null and query.endSubmitCompletionTime != null">
                AND t.submit_completion_time >= #{query.startSubmitCompletionTime}
                AND t.submit_completion_time &lt;= #{query.endSubmitCompletionTime}
            </if>
            <if test="query.taskStatusList != null and query.taskStatusList.size() > 0">
                AND t.task_status IN
                <foreach collection="query.taskStatusList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
        </where>
    </select>
</mapper>
