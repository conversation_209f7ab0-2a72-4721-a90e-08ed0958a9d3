<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.mapper.PrivilegeMapper">


    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.gok.pboot.pms.entity.Privilege">
        <id column="id" property="id"/>
        <result column="userId" property="userId"/>
        <result column="userName" property="userName"/>
        <result column="privilegeType" property="privilegeType"/>
        <result column="projectId" property="projectId"/>
        <result column="deptId" property="deptId"/>
        <result column="creator" property="creator"/>
        <result column="creatorId" property="creatorId"/>
        <result column="modifier" property="modifier"/>
        <result column="modifierId" property="modifierId"/>
        <result column="ctime" property="ctime"/>
        <result column="mtime" property="mtime"/>
        <result column="delFlag" property="delFlag"/>
    </resultMap>

    <!-- 分页查询映射结果 -->
    <resultMap id="BaseResultMapPrivilegeFindPageVo" type="com.gok.pboot.pms.entity.vo.PrivilegeFindPageVO">
        <id column="projectId" property="projectId"/>
        <result column="projectCode" property="projectCode"/>
        <result column="projectName" property="projectName"/>
        <result column="projectStatusName" property="projectStatusName"/>
        <result column="salesmanUserName" property="salesmanUserName"/>
        <result column="managerUserName" property="managerUserName"/>
        <result column="salesmanUserId" property="salesmanUserId"/>
        <result column="managerUserId" property="managerUserId"/>
        <result column="preSalesmanUserId" property="preSalesmanUserId"/>
        <result column="preSalesmanUserName" property="preSalesmanUserName"/>
        <result column="ctime" property="ctime"/>
        <result column="mtime" property="mtime"/>
        <collection property="mhourAuditor"
                    ofType="com.gok.pboot.pms.entity.vo.PrivilegeUserInfoVO"
                    javaType="java.util.List">
            <result column="userId" property="userId"/>
            <result column="name" property="name"/>
        </collection>
    </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
					a.id AS 'id',
					a.user_id AS 'userId',
					a.user_name AS 'userName',
					a.privilege_type AS 'privilegeType',
					a.project_id AS 'projectId',
					a.dept_id AS 'deptId',
					a.creator AS 'creator',
					a.creator_id AS 'creatorId',
					a.modifier AS 'modifier',
					a.modifier_id AS 'modifierId',
					a.ctime AS 'ctime',
					a.mtime AS 'mtime',
                    a.del_flag AS 'delFlag'
        </sql>

    <sql id="join"></sql>

    <select id="findList" resultType="com.gok.pboot.pms.entity.Privilege">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mhour_privilege a
        <include refid="join"/>
        <where>
            a.del_flag = 0
        </where>
        ORDER BY a.id desc
    </select>

    <!--逻辑删除-->
    <update id="deleteByLogic">
        UPDATE mhour_privilege SET
        del_flag = 1
        WHERE id = #{id}
    </update>


    <insert id="batchSave">
        INSERT INTO mhour_privilege (
                id,
                user_id,
                user_name,
                privilege_type,
                project_id,
                dept_id,
                creator,
                creator_id,
                modifier,
                modifier_id,
                ctime,
                mtime,
                del_flag
        )VALUES
        <foreach collection="poList" item="item" separator=",">
            (
                    #{item.id},
                    #{item.userId},
                    #{item.userName},
                    #{item.privilegeType},
                    #{item.projectId},
                    #{item.deptId},
                    #{item.creator},
                    #{item.creatorId},
                    #{item.modifier},
                    #{item.modifierId},
                    #{item.ctime},
                    #{item.mtime},
                    #{item.delFlag}
            )
        </foreach>
    </insert>

    <update id="batchDel">
        update mhour_privilege set del_flag = 1 where project_id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <insert id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            UPDATE mhour_privilege  SET
                    id = #{item.id},
                    user_id = #{item.userId},
                    user_name = #{item.userName},
                    privilege_type = #{item.privilegeType},
                    project_id = #{item.projectId},
                    dept_id = #{item.deptId},
                    creator = #{item.creator},
                    creator_id = #{item.creatorId},
                    modifier = #{item.modifier},
                    modifier_id = #{item.modifierId},
                    ctime = #{item.ctime},
                    mtime = #{item.mtime},
                    del_flag = #{item.delFlag}
            where id = #{item.id}
        </foreach>
    </insert>

    <!--批量更新非空字段-->
    <update id="updateBatch" parameterType="arraylist">
        update mhour_privilege
        <trim prefix="set" suffixOverrides=",">
                    <trim prefix=" user_id =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.userId!=null">
                                when id=#{item.id} then #{item.userId}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" user_name =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.userName!=null">
                                when id=#{item.id} then #{item.userName}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" privilege_type =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.privilegeType!=null">
                                when id=#{item.id} then #{item.privilegeType}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" project_id =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.projectId!=null">
                                when id=#{item.id} then #{item.projectId}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" dept_id =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.deptId!=null">
                                when id=#{item.id} then #{item.deptId}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" modifier =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.modifier!=null">
                                when id=#{item.id} then #{item.modifier}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" modifier_id =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.modifierId!=null">
                                when id=#{item.id} then #{item.modifierId}
                            </if>
                        </foreach>
                    </trim>
                    <trim prefix=" mtime =case" suffix="end,">
                        <foreach collection="list" item="item" index="index">
                            <if test="item.mtime!=null">
                                when id=#{item.id} then #{item.mtime}
                            </if>
                        </foreach>
                    </trim>
        </trim>

        where
        id in
        <foreach collection="list" separator="," item="item" open="(" close=")">
            #{item.id}
        </foreach>
    </update>

    <update id="batchDelByPjIdAndUserIdsAndType">
        update mhour_privilege set del_flag = 1 where project_id = #{projectId}
        AND user_id in
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        AND privilege_type = #{privilegeType}
    </update>

    <select id="adminConfigFindPageVo" resultMap="BaseResultMapAdminConfigFindPageVo">
        SELECT
        t1.project_id AS 'projectId',
        t1.item_no AS 'projectCode',
        t1.item_name AS 'projectName',
        t1.project_status AS 'projectStatusName',
        t1.project_salesperson AS 'salesmanUserName',
        t1.manager_user_name AS 'managerUserName',
        t1.manager_user_id AS 'managerUserId',
        t1.salesman_user_id AS 'salesmanUserId',
        t1.pre_sale_user_id AS 'preSalesmanUserId',
        t1.pre_sale_user_name AS 'preSalesmanUserName',
        t1.user_id AS 'userId',
        t1.privilege_type AS 'privilegeType',
        t1.user_name AS 'name',
        t2.ctime AS 'ctime',
        t2.mtime AS 'mtime'
        FROM (
        SELECT
        mp.project_id ,
        mpj.item_no,
        mpj.item_name,
        mpj.project_status,
        mpj.project_salesperson,
        mpj.pre_sale_user_name,
        mpj.manager_user_name,
        mpj.manager_user_id,
        mpj.salesman_user_id,
        mpj.pre_sale_user_id,
        mp.user_id,
        mp.privilege_type,
        mp.user_name
        FROM mhour_privilege mp
        LEFT JOIN project_info mpj ON mpj.id = mp.project_id
        <where>
            mp.del_flag = 0
            <if test="privilegeFindPageDTO.projectCode != null and  privilegeFindPageDTO.projectCode != ''">
                AND mpj.item_no like concat('%',#{privilegeFindPageDTO.projectCode},'%')
            </if>
            <if test="privilegeFindPageDTO.projectName != null and  privilegeFindPageDTO.projectName != '' ">
                AND mpj.item_name like concat('%',#{privilegeFindPageDTO.projectName},'%')
            </if>
            <if test="privilegeFindPageDTO.name != null and  privilegeFindPageDTO.name != '' ">
                AND( mp.user_name like concat('%',#{privilegeFindPageDTO.name},'%')
                OR mpj.manager_user_name like concat('%',#{privilegeFindPageDTO.name},'%')
                OR mpj.project_salesperson like concat('%',#{privilegeFindPageDTO.name},'%')
                )
            </if>
        </where>
        ORDER BY mpj.item_name
        ) t1 RIGHT JOIN (
        SELECT
        mp.project_id ,
        MIN(mp.ctime) AS 'ctime',
        MAX(mp.mtime) AS 'mtime'
        FROM mhour_privilege mp
        LEFT JOIN project_info mpj ON mpj.id = mp.project_id
        WHERE mp.del_flag = 0
        AND mp.privilege_type IN
        (${@com.gok.pboot.pms.enumeration.PrivilegeTypeEnum@PROJECT_OPERATOR.getValue()},
        ${@com.gok.pboot.pms.enumeration.PrivilegeTypeEnum@PROJECT_AUDITOR.getValue()})
        <if test="privilegeFindPageDTO.projectCode != null and  privilegeFindPageDTO.projectCode != ''">
            AND mpj.item_no like concat('%',#{privilegeFindPageDTO.projectCode},'%')
        </if>
        <if test="privilegeFindPageDTO.projectName != null and  privilegeFindPageDTO.projectName != '' ">
            AND mpj.item_name like concat('%',#{privilegeFindPageDTO.projectName},'%')
        </if>
        <if test="privilegeFindPageDTO.name != null and  privilegeFindPageDTO.name != '' ">
            AND( mp.user_name like concat('%',#{privilegeFindPageDTO.name},'%')
            OR mpj.manager_user_name like concat('%',#{privilegeFindPageDTO.name},'%')
            OR mpj.project_salesperson like concat('%',#{privilegeFindPageDTO.name},'%')
            )
        </if>
        GROUP BY mp.project_id
        ORDER BY mp.ctime DESC
        LIMIT #{adapter.begin} , #{adapter.size}
        ) t2 ON t1.project_id = t2.project_id
        <!--<if test="dataScope.isAll == false">
            <if test="dataScope.userIdList != null and dataScope.userIdList.size() != 0">
                WHERE t1.manager_user_id IN
                <foreach collection="dataScope.userIdList" item="uId" open="(" separator="," close=")">
                    #{uId}
                </foreach>
                AND t1.salesman_user_id IN
                <foreach collection="dataScope.userIdList" item="uId" open="(" separator="," close=")">
                    #{uId}
                </foreach>
                AND t1.pre_sale_user_id IN
                <foreach collection="dataScope.userIdList" item="uId" open="(" separator="," close=")">
                    #{uId}
                </foreach>
            </if>
        </if>-->
    </select>

    <!-- 分页查询映射结果 -->
    <resultMap id="BaseResultMapAdminConfigFindPageVo" type="com.gok.pboot.pms.entity.vo.AdminConfigFindPageVo">
        <id column="projectId" property="projectId"/>
        <result column="projectCode" property="projectCode"/>
        <result column="projectName" property="projectName"/>
        <result column="projectStatusName" property="projectStatusName"/>
        <result column="salesmanUserName" property="salesmanUserName"/>
        <result column="managerUserName" property="managerUserName"/>
        <result column="salesmanUserId" property="salesmanUserId"/>
        <result column="managerUserId" property="managerUserId"/>
        <result column="preSalesmanUserId" property="preSalesmanUserId"/>
        <result column="preSalesmanUserName" property="preSalesmanUserName"/>
        <result column="ctime" property="ctime"/>
        <result column="mtime" property="mtime"/>
        <collection property="userList"
                    ofType="com.gok.pboot.pms.entity.vo.PrivilegeUserInfoVO"
                    javaType="java.util.List">
            <result column="userId" property="userId"/>
            <result column="privilegeType" property="privilegeType"/>
            <result column="name" property="name"/>
        </collection>
    </resultMap>

    <select id="totalMap" resultType="map">
        SELECT
        COUNT(1) AS 'total'
        FROM (
        SELECT mpj.id
        FROM mhour_privilege mp
        INNER JOIN project_info mpj ON mpj.id = mp.project_id
        <where>
            mp.del_flag = 0
            <if test="privilegeFindPageDTO.projectCode != null and  privilegeFindPageDTO.projectCode != ''">
                AND mpj.item_no like concat('%',#{privilegeFindPageDTO.projectCode},'%')
            </if>
            <if test="privilegeFindPageDTO.projectName != null and  privilegeFindPageDTO.projectName != '' ">
                AND mpj.item_name like concat('%',#{privilegeFindPageDTO.projectName},'%')
            </if>
            <if test="privilegeFindPageDTO.name != null and  privilegeFindPageDTO.name != '' ">
                AND( mp.user_name like concat('%',#{privilegeFindPageDTO.name},'%')
                OR mpj.manager_user_name like concat('%',#{privilegeFindPageDTO.name},'%')
                OR mpj.project_salesperson like concat('%',#{privilegeFindPageDTO.name},'%')
                )
            </if>
            <!--<if test="dataScope.isAll == false">
                <if test="dataScope.userIdList != null and dataScope.userIdList.size() != 0">
                    AND mpj.manager_user_id IN
                    <foreach collection="dataScope.userIdList" item="uId" open="(" separator="," close=")">
                        #{uId}
                    </foreach>
                    AND mpj.salesman_user_id IN
                    <foreach collection="dataScope.userIdList" item="uId" open="(" separator="," close=")">
                        #{uId}
                    </foreach>
                    AND mpj.pre_sale_user_id IN
                    <foreach collection="dataScope.userIdList" item="uId" open="(" separator="," close=")">
                        #{uId}
                    </foreach>
                </if>
            </if>-->
        </where>
        GROUP BY mpj.id
        ) t1
    </select>

    <select id="getPrivilegeByProjectIdAndPrivilegeType" resultType="com.gok.pboot.pms.entity.Privilege">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mhour_privilege a
        <where>
            a.del_flag = 0
            <if test="projectIds != null and projectIds.size() > 0">
                AND a.project_id IN
                <foreach collection="projectIds" separator="," item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="privilegeType != null">
                AND a.privilege_type = #{privilegeType}
            </if>
        </where>
        GROUP BY a.project_id
        ORDER BY a.id
    </select>
    <select id="getByProjectId" resultType="com.gok.pboot.pms.entity.Privilege">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mhour_privilege a
        <where>
            a.del_flag = 0
            <if test="projectIds != null and projectIds.size() > 0">
                AND a.project_id IN
                <foreach collection="projectIds" separator="," item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectAuditProjectId" resultType="java.lang.Long">
        SELECT DISTINCT r.`projectId`
        FROM (
                 SELECT
                     pro.id projectId
                 FROM mhour_privilege pri
                 LEFT JOIN project_info pro ON pri.project_id = pro.id
                 WHERE pri.del_flag = 0
                   AND pri.user_id = #{userId}
                   AND pri.privilege_type = 1
                   AND pro.project_status IN (0, 1, 2, 6, 7)
                 UNION ALL
                 SELECT
                     pro.id projectId
                 FROM project_info pro
                 WHERE 1 = 1
                   AND (
                       (project_status IN (0, 1) AND (salesman_user_id = #{userId} OR pre_sale_user_id = #{userId}))
                     OR (project_status IN (2, 6, 7) AND manager_user_id = #{userId})
                    )
        ) r
    </select>

    <select id="selectOperatorProjectId" resultType="java.lang.Long">
        SELECT project_id
        FROM `mhour_privilege`
        WHERE del_flag = 0
          AND user_id = #{userId}
          AND privilege_type = ${@com.gok.pboot.pms.enumeration.PrivilegeTypeEnum@PROJECT_OPERATOR.getValue()}
    </select>

    <select id="findToSyncMember" resultType="com.gok.pboot.pms.entity.domain.ProjectStakeholderMember">
        SELECT
            mp.id as id,
            mp.project_id as projectId,
            mp.user_id as memberId,
            mp.user_name as memberName,
            NULL as deptName,
            NULL as position,
            3 as roleType,
            NULL as duty,
            NULL as remark,
            0 as syncOaType,
            mp.creator as creator,
            mp.creator_id as creatorId,
            mp.modifier as modifier,
            mp.modifier_id as modifierId,
            mp.ctime as ctime,
            mp.mtime as mtime,
            0 as delFlag
        FROM mhour_privilege mp
        WHERE
            mp.del_flag = 0
          AND mp.privilege_type = 3
        GROUP BY mp.id;
    </select>


</mapper>
