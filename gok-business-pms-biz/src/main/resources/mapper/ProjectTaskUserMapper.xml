<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.gok.pboot.pms.mapper.ProjectTaskUserMapper">

    <sql id="baseColumnList">
        a.id,
        a.task_id,
        a.user_id,
        a.user_name,
        a.creator,
        a.creator_id,
        a.modifier,
        a.modifier_id,
        a.ctime,
        a.mtime
    </sql>

    <insert id="save">
        INSERT INTO project_task_user
        (id, task_id, user_id, user_name, creator, creator_id,
         modifier, modifier_id, ctime, mtime)
        VALUES (#{id}, #{taskId}, #{userId}, #{userName}, #{creator}, #{creatorId},
                #{modifier}, #{modifierId}, #{ctime}, #{mtime})
    </insert>

    <update id="delete">
        update project_task_user
        set del_flag=1
        where task_id = #{taskId}
    </update>

    <select id="findByTaskId" resultType="com.gok.pboot.pms.entity.domain.ProjectTaskUser">
        SELECT
            <include refid="baseColumnList"/>
        FROM project_task_user a
        WHERE
            del_flag = 0
        AND
            task_id = #{taskId}
    </select>

    <select id="findAllUserIdByProjectIds" resultType="java.lang.Long">
        SELECT tu.user_id
        FROM project_task_user tu
        LEFT JOIN project_task t
        ON t.id = tu.task_id
        LEFT JOIN mhour_project p
        ON p.id = t.project_id
        <where>
            t.del_flag = 0
            AND tu.del_flag = 0
            AND p.del_flag = 0
            AND p.id IN
            <foreach collection="projectIds" item="pId" open="(" close=")" separator=",">
                #{pId}
            </foreach>
        </where>
    </select>
</mapper>