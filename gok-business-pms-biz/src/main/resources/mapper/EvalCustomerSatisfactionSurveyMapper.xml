<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.eval.mapper.EvalCustomerSatisfactionSurveyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.gok.pboot.pms.eval.entity.domain.EvalCustomerSatisfactionSurvey">
        <id column="id" property="id"/>
        <id column="project_id" property="projectId"/>
        <result column="customer_name" property="customerName"/>
        <result column="stakeholder_id" property="stakeholderId"/>
        <result column="contact_phone" property="contactPhone"/>
        <result column="send_status" property="sendStatus"/>
        <result column="survey_date" property="surveyDate"/>
        <result column="release_date" property="releaseDate"/>
        <result column="total_score" property="totalScore"/>
        <result column="eval_status" property="evalStatus"/>
        <result column="problem_response_result" property="problemResponseResult"/>
        <result column="design_scheme_result" property="designSchemeResult"/>
        <result column="progress_control_result" property="progressControlResult"/>
        <result column="quality_control_result" property="qualityControlResult"/>
        <result column="service_attitude_communication_result" property="serviceAttitudeCommunicationResult"/>
        <result column="other_suggestion" property="otherSuggestion"/>
        <result column="creator" property="creator"/>
        <result column="creator_id" property="creatorId"/>
        <result column="modifier" property="modifier"/>
        <result column="modifier_id" property="modifierId"/>
        <result column="ctime" property="ctime"/>
        <result column="mtime" property="mtime"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, project_id, customer_name, stakeholder_id, contact_phone, send_status, survey_date, release_date, total_score, eval_status,
        problem_response_result, design_scheme_result, progress_control_result, 
        quality_control_result, service_attitude_communication_result, other_suggestion,
        creator, creator_id, modifier, modifier_id, ctime, mtime, del_flag
    </sql>

</mapper> 