<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.gok.pboot.pms.mapper.TomorrowPlanPaperEntryMapper">
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        pe.id AS 'id',
        pe.project_id AS 'projectId',
        pe.project_name AS 'projectName',
        pe.task_id AS 'taskId',
        pe.task_name AS 'taskName',
        pe.old_task_flag AS 'oldTaskFlag',
        pe.daily_paper_id AS 'dailyPaperId',
        pe.approval_status AS 'approvalStatus',
        pe.submission_date AS 'submissionDate',
        pe.description AS 'description',
        pe.approval_reason AS 'approvalReason',
        pe.user_id AS 'userId',
        pe.user_real_name AS 'userRealName',
        pe.user_dept_id AS 'userDeptId',
        pe.creator AS 'creator',
        pe.creator_id AS 'creatorId',
        pe.modifier AS 'modifier',
        pe.modifier_id AS 'modifierId',
        pe.ctime AS 'ctime',
        pe.mtime AS 'mtime',
        pe.del_flag AS 'delFlag',
        pe.work_type AS 'workType'
    </sql>

    <!-- 通用查询结果列 -->
    <sql id="Select_Column_List">
        pe.id AS 'id',
        pe.project_id AS 'projectId',
        pe.project_name AS 'projectName',
        pe.task_id AS 'taskId',
        pe.task_name AS 'taskName',
        pe.old_task_flag AS 'oldTaskFlag',
        pe.daily_paper_id AS 'dailyPaperId',
        pe.approval_status AS 'approvalStatus',
        pe.submission_date AS 'submissionDate',
        pe.description AS 'description',
        pe.approval_reason AS 'approvalReason',
        pe.user_id AS 'userId',
        pe.user_real_name AS 'userRealName',
        pe.user_dept_id AS 'userDeptId',
        pe.creator AS 'creator',
        pe.creator_id AS 'creatorId',
        pe.modifier AS 'modifier',
        pe.modifier_id AS 'modifierId',
        pe.ctime AS 'ctime',
        pe.mtime AS 'mtime',
        pe.del_flag AS 'delFlag',
        pe.work_type AS 'workType',
        b.submission_date AS selectDate
    </sql>

    <update id="updateTaskNameByTaskId">
        UPDATE mhour_tomorrow_plan_paper_entry
        SET
            task_name = #{taskName}
        WHERE
            task_id = #{taskId}
    </update>

    <select id="batchSave">
        INSERT INTO mhour_tomorrow_plan_paper_entry
        (
        id,
        project_id,
        project_name,
        task_id,
        task_name,
        daily_paper_id,
        approval_status,
        submission_date,
        description,
        approval_reason,
        user_id,
        user_real_name,
        user_dept_id,
        creator,
        creator_id,
        modifier,
        modifier_id,
        ctime,
        mtime,
        del_flag,
        work_type,
        old_task_flag
        )
        VALUES
        <foreach collection="poList" item="po" separator=",">
            (
            #{po.id},
            #{po.projectId},
            #{po.projectName},
            #{po.taskId},
            #{po.taskName},
            #{po.dailyPaperId},
            #{po.approvalStatus},
            #{po.submissionDate},
            #{po.description},
            #{po.approvalReason},
            #{po.userId},
            #{po.userRealName},
            #{po.userDeptId},
            #{po.creator},
            #{po.creatorId},
            #{po.modifier},
            #{po.modifierId},
            #{po.ctime},
            #{po.mtime},
            #{po.delFlag},
            #{po.workType},
            #{po.oldTaskFlag}
            )
        </foreach>
    </select>
    <insert id="batchUpdate">
        <foreach collection="poList" item="item" separator=";">
            UPDATE mhour_tomorrow_plan_paper_entry
            SET
            id = #{item.id},
            project_id = #{item.projectId},
            project_name = #{item.projectName},
            task_id = #{item.taskId},
            task_name = #{item.taskName},
            daily_paper_id = #{item.dailyPaperId},
            approval_status = #{item.approvalStatus},
            submission_date = #{item.submissionDate},
            description = #{item.description},
            approval_reason = #{item.approvalReason},
            user_id = #{item.userId},
            user_real_name = #{item.userRealName},
            user_dept_id = #{item.userDeptId},
            modifier = #{item.modifier},
            modifier_id = #{item.modifierId},
            mtime = #{item.mtime},
            del_flag = #{item.delFlag},
            work_type = #{item.workType}
            WHERE
            id = #{item.id}
        </foreach>
    </insert>
    <select id="findByDailyPaperId" resultType="com.gok.pboot.pms.entity.domain.TomorrowPlanPaperEntry">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mhour_tomorrow_plan_paper_entry pe
        WHERE
        pe.del_flag = 0
        AND
        pe.daily_paper_id = #{dailyPaperId}
        ORDER BY pe.ctime DESC
    </select>
    <select id="findByFilter" resultType="com.gok.pboot.pms.entity.domain.TomorrowPlanPaperEntry">
        SELECT
        pe.id AS 'id',
        pe.project_name AS 'projectName',
        pe.task_id AS 'taskId',
        pe.user_real_name AS 'userRealName',
        pe.task_name AS 'taskName',
        pe.submission_date AS 'submissionDate',
        pe.description AS 'description',
        pe.approval_reason AS 'approvalReason',
        pe.approval_status AS 'approvalStatus',
        pe.work_type AS 'workType',
        pe.daily_paper_id AS 'dailyPaperId',
        pe.user_id AS 'userId',
        pe.project_id AS 'projectId'
        FROM
        mhour_tomorrow_plan_paper_entry pe
        <where>
            <if test="filter.userIds != null and filter.userIds.size() > 0">
                AND pe.user_id IN
                <foreach collection="filter.userIds" separator="," item="userId" open="(" close=")">
                    #{userId}
                </foreach>
            </if>
            <if test="filter.taskIds != null and filter.taskIds.size() > 0">
                AND pe.task_id IN
                <foreach collection="filter.taskIds" separator="," item="taskId" open="(" close=")">
                    #{taskId}
                </foreach>
            </if>
            <if test="filter.userId !=null and filter.userId!=''">
                and pe.user_id =#{filter.userId}
            </if>
            <if test="filter.startTime !=null and filter.startTime!=''">
                and pe.submission_date &gt;=#{filter.startTime}
            </if>
            <if test="filter.endTime !=null and filter.endTime!=''">
                and pe.submission_date &lt;= #{filter.endTime}
            </if>
        </where>
    </select>


    <!--获取昨日计划-->
    <select id="getYesterdayPlanByIds" resultType="com.gok.pboot.pms.entity.vo.DailyReviewProjectAuditPageVO">
        <foreach collection="voList" item="vo" index="idx">
            <if test="idx != 0">
                UNION ALL
            </if>
            SELECT
            id,
            getYesterDayPlan(#{vo.userId}, #{vo.projectId}, #{vo.taskId}, #{vo.submissionDate}) yesterdayPlan
            FROM mhour_daily_paper_entry
            <where>
                del_flag = 0
                AND id =#{vo.id}
            </where>
        </foreach>
    </select>


    <select id="findBySubmissionDatesAndUserIds" resultType="com.gok.pboot.pms.entity.vo.TomorrowPlanPaperVo">
        SELECT
        <include refid="Select_Column_List"/>
        FROM mhour_tomorrow_plan_paper_entry pe
        INNER JOIN (
        SELECT
        t1.user_id,
        t1.submission_date,
        MAX( t2.submission_date ) AS previous_submission_date
        FROM
        mhour_tomorrow_plan_paper_entry t1
        LEFT JOIN mhour_tomorrow_plan_paper_entry t2 ON t1.user_id = t2.user_id
        AND t2.submission_date &lt; t1.submission_date
        WHERE
        t1.del_flag = 0
        AND t2.del_flag = 0
        AND (t1.approval_status = 2 OR t1.approval_status = 4)
        AND (t2.approval_status = 2 OR t2.approval_status = 4)
        AND t1.submission_date IN
        <foreach collection="submissionDates" item="submissionDate" open="(" close=")" separator=",">
            #{submissionDate}
        </foreach>
        AND t1.user_id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        GROUP BY
        t1.user_id,
        t1.submission_date
        ) b ON pe.user_id = b.user_id
        AND pe.submission_date = b.previous_submission_date
        AND (pe.approval_status = 2 OR pe.approval_status = 4)
        AND pe.del_flag = 0
    </select>

    <select id="findBySubmissionDateRangeAndUserIds"
            resultType="com.gok.pboot.pms.entity.domain.TomorrowPlanPaperEntry">
        SELECT
            <include refid="Base_Column_List"/>
        FROM mhour_tomorrow_plan_paper_entry pe
        WHERE
            del_flag = 0
            AND submission_date BETWEEN #{startDate} AND #{endDate}
            AND user_id IN
            <foreach collection="userIds" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
    </select>

</mapper>