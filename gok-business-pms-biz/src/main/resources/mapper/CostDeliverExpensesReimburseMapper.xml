<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.cost.mapper.CostDeliverExpensesReimburseMapper">
  <resultMap id="BaseResultMap" type="com.gok.pboot.pms.cost.entity.domain.CostDeliverExpensesReimburse">
    <!--@mbg.generated-->
    <!--@Table cost_deliver_expenses_reimburse-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="recipient_user_name" jdbcType="VARCHAR" property="recipientUserName" />
    <result column="budget_id" jdbcType="BIGINT" property="budgetId" />
    <result column="tax_rate" jdbcType="INTEGER" property="taxRate" />
    <result column="account_id" jdbcType="BIGINT" property="accountId" />
    <result column="account_name" jdbcType="VARCHAR" property="accountName" />
    <result column="account_category_id" jdbcType="BIGINT" property="accountCategoryId" />
    <result column="account_category_name" jdbcType="VARCHAR" property="accountCategoryName" />
    <result column="account_item_id" jdbcType="BIGINT" property="accountItemId" />
    <result column="account_item_name" jdbcType="VARCHAR" property="accountItemName" />
    <result column="reimburse_money" jdbcType="DECIMAL" property="reimburseMoney" />
    <result column="expenses_desc" jdbcType="VARCHAR" property="expensesDesc" />
    <result column="request_id" jdbcType="BIGINT" property="requestId" />
    <result column="request_name" jdbcType="VARCHAR" property="requestName" />
    <result column="applicant_id" jdbcType="BIGINT" property="applicantId" />
    <result column="applicant_name" jdbcType="VARCHAR" property="applicantName" />
    <result column="applicant_time" jdbcType="VARCHAR" property="applicantTime" />
    <result column="request_status" jdbcType="INTEGER" property="requestStatus" />
    <result column="request_type" jdbcType="INTEGER" property="requestType" />
    <result column="filing_time" jdbcType="VARCHAR" property="filingTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="creator_id" jdbcType="BIGINT" property="creatorId" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="modifier_id" jdbcType="BIGINT" property="modifierId" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, project_id, project_name, recipient_user_name,budget_id,tax_rate, account_id, account_name, account_category_id,
    account_category_name,account_item_id, account_item_name, reimburse_money, expenses_desc, request_id, request_name,
    applicant_id, applicant_name, applicant_time, filing_time,request_status,request_type, creator, creator_id, modifier,
    modifier_id, ctime, mtime, del_flag
  </sql>

    <select id="selReimburseList" resultType="com.gok.pboot.pms.cost.entity.domain.CostDeliverExpensesReimburse">
        select *
        from cost_deliver_expenses_reimburse
        where project_id = #{projectId}
          and ((request_type = '0' and account_category_id in (1, 2))
                        OR (request_type = '2' and account_category_id = 0))
    </select>

    <select id="selPurchaseList" resultType="com.gok.pboot.pms.cost.entity.domain.CostDeliverExpensesReimburse">
        select *
        from cost_deliver_expenses_reimburse
        where project_id = #{projectId}
          and ((request_type = '0' and account_category_id = 3)
                or
               request_type = '1'
                )
    </select>

  <select id="findExpensesReimburse" resultType="com.gok.pboot.pms.cost.entity.vo.DeliverExpensesReimburseListVO">
      select *
      from cost_deliver_expenses_reimburse
              where project_id = #{dto.projectId}
                 and ((request_type = '0' and account_category_id in (1, 2))
                        OR (request_type = '2' and account_category_id = 0))
      <if test="dto.recipientUserName != null and dto.recipientUserName != ''">
          and recipient_user_name like CONCAT('%',#{dto.recipientUserName}
                  ,'%')
      </if>
      <if test="dto.oaId != null and dto.oaId != ''">
          and account_id =#{dto.oaId}
      </if>
      <if test="dto.applicantStartTime != null and dto.applicantStartTime != ''">
          and applicant_time &gt;= #{dto.applicantStartTime}
      </if>
      <if test="dto.applicantEndTime != null and dto.applicantEndTime != ''">
          and applicant_time &lt;= #{dto.applicantEndTime}
      </if>
  </select>

  <select id="selFilePurchaseList" resultType="com.gok.pboot.pms.cost.entity.domain.CostDeliverExpensesReimburse">
        select *
        from cost_deliver_expenses_reimburse
        where project_id = #{projectId}
          and ((request_type = '0' and account_category_id = 3)
                or
               request_type = '1'
                )
        and request_status = '3'
  </select>

</mapper>