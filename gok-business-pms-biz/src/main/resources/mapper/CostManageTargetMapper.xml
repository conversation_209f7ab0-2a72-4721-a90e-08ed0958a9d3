<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.cost.mapper.CostManageTargetMapper">


    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.gok.pboot.pms.cost.entity.domain.CostManageTarget">
        <id column="id" property="id"/>
        <result column="version_id" property="versionId"/>
        <result column="project_id" property="projectId"/>
        <result column="project_requirements" property="projectRequirements"/>
        <result column="delivery_requirements" property="deliveryRequirements"/>
        <result column="delivery_items" property="deliveryItems"/>
        <result column="delivery_deadline" property="deliveryDeadline"/>
        <result column="delivery_place" property="deliveryPlace"/>
        <result column="warranty_period" property="warrantyPeriod"/>
        <result column="secrecy_requirements" property="secrecyRequirements"/>
        <result column="other_requirements" property="otherRequirements"/>
        <result column="creator" property="creator"/>
        <result column="creator_id" property="creatorId"/>
        <result column="modifier" property="modifier"/>
        <result column="modifier_id" property="modifierId"/>
        <result column="ctime" property="ctime"/>
        <result column="mtime" property="mtime"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, version_id, project_id, project_requirements, delivery_requirements, delivery_items, delivery_deadline, delivery_place, warranty_period, secrecy_requirements, other_requirements, creator, creator_id, modifier, modifier_id, ctime, mtime, del_flag
    </sql>
    <select id="getCostManageTargetInfo" resultType="com.gok.pboot.pms.cost.entity.vo.CostManageTargetVO"
            parameterType="com.gok.pboot.pms.cost.entity.dto.CostManageTargetDTO">
        select cmt.id,
        cmv.request_id,
        cmv.request_name,
        cmv.status,
        cmt.version_id,
        cmv.version_name,
        cmt.project_id,
        cmt.project_requirements,
        cmt.delivery_requirements,
        cmt.delivery_items,
        cmt.delivery_deadline,
        cmt.delivery_place,
        cmt.warranty_period,
        cmt.secrecy_requirements,
        cmt.other_requirements,
        cmt.detail_files
        from cost_manage_target as cmt
        left join cost_manage_version as cmv on cmt.version_id = cmv.id
        where cmv.version_type = 0
        <if test="projectId != null">
            and cmt.project_id = #{projectId}
        </if>
        <if test="versionId != null">
            and cmt.version_id = #{versionId}
        </if>
        <if test="versionId == null">
            and cmv.version_status = 0
        </if>
        order by cmt.ctime desc limit 1
    </select>

</mapper>
