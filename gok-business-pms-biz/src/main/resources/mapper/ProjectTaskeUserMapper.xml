<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.gok.pboot.pms.mapper.ProjectTaskeUserMapper">

    <select id="findListByTaskIds" resultType="com.gok.pboot.pms.entity.domain.ProjectTaskeUser">
        select tu.id,
               tu.task_id,
               tu.user_id,
               tu.user_name,
               tu.task_role
        from project_taske_user tu
        where 1 = 1
        and tu.del_flag = 0
        <if test="taskIds != null and taskIds.size() > 0">
            and tu.task_id in
            <foreach item="item" index="index" collection="taskIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="findListByTaskId" resultType="com.gok.pboot.pms.entity.domain.ProjectTaskeUser">
        select tu.id,
                tu.task_id,
                tu.user_id,
                tu.user_name,
                tu.task_role
        from project_taske_user tu
        where 1 = 1
        and tu.del_flag = 0
        and tu.task_id = #{taskId}
        <if test="taskRole != null">
            and task_role = #{taskRole}
        </if>
    </select>

    <select id="findPageByTaskId" resultType="com.gok.pboot.pms.entity.domain.ProjectTaskeUser">
        select tu.id,
        tu.task_id,
        tu.user_id,
        tu.user_name,
        tu.task_role,
        tu.dept_name,
        tu.position
        from project_taske_user tu
        where 1 = 1
        and tu.del_flag = 0
        and tu.task_id = #{taskId}
        <if test="taskRole != null">
            and task_role = #{taskRole}
        </if>
    </select>

    <update id="logicDeleteByTaskId">
        update project_taske_user
        set del_flag = 1
        where task_id = #{taskId}
    </update>

    <delete id="physicalDeleteByTaskId">
        delete from project_taske_user
        where task_id = #{taskId}
        <if test="taskRole != null">
            and task_role = #{taskRole}
        </if>
    </delete>

    <insert id="batchSave">
        insert into project_taske_user
            (
                id,
                task_id,
                user_id,
                user_name,
                task_role,
                creator,
                creator_id,
                modifier,
                modifier_id,
                ctime,
                mtime,
                del_flag,
                dept_name,
                position
            )
        values
            <foreach collection="list" item="item" index="index" separator=",">
                (
                    #{item.id},
                    #{item.taskId},
                    #{item.userId},
                    #{item.userName},
                    #{item.taskRole},
                    #{item.creator},
                    #{item.creatorId},
                    #{item.modifier},
                    #{item.modifierId},
                    #{item.ctime},
                    #{item.mtime},
                    #{item.delFlag},
                    #{item.deptName},
                    #{item.position}
                )
            </foreach>
    </insert>

    <sql id="baseColumnList">
        a.id,
        a.task_id,
        a.user_id,
        a.user_name,
        a.task_role,
        a.creator,
        a.creator_id,
        a.modifier,
        a.modifier_id,
        a.ctime,
        a.mtime,
        a.del_flag
    </sql>

    <select id="findByUserId" resultType="com.gok.pboot.pms.entity.domain.ProjectTaskeUser">
        SELECT
            <include refid="baseColumnList"/>
        FROM project_taske_user a
            del_flag = 0
        AND
            user_id = #{userId}
    </select>

    <select id="findTaskIdByUserIdAndTaskRole" resultType="java.lang.Long">
        SELECT task_id
        FROM project_taske_user
        WHERE
            del_flag = 0
        AND
            user_id = #{userId}
        AND
            task_role = #{taskRole}
    </select>

    <select id="findByTaskIdsAndTaskRole" resultType="com.gok.pboot.pms.entity.domain.ProjectTaskeUser">
        SELECT
            <include refid="baseColumnList"/>
        FROM project_taske_user a
        WHERE
            del_flag = 0
        AND task_id IN
        <foreach item="item" index="index" collection="taskIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND
            task_role = #{taskRole}
    </select>

    <select id="findLeadersByTaskIds" resultType="com.gok.pboot.pms.entity.domain.ProjectTaskeUser">
        select
            <include refid="baseColumnList"/>
        from project_taske_user a
        where a.del_flag = 0
        and a.task_role = 0
        and a.task_id in
        <foreach item="item" index="index" collection="taskIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="findByTaskIdsAndTaskRoleAndUserId"
            resultType="com.gok.pboot.pms.entity.domain.ProjectTaskeUser">
        SELECT
            <include refid="baseColumnList"/>
        FROM project_taske_user a
        WHERE
            del_flag = 0
        AND task_id IN
            <foreach item="item" index="index" collection="taskIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        AND
            task_role = #{taskRole}
        AND
            user_id = #{userId}
    </select>

    <delete id="batchDelByUserIds">
        delete from project_taske_user
        where 1 = 1
        and task_id = #{taskId}
        <if test="userIds != null and userIds.size() > 0">
            and user_id in
            <foreach item="item" index="index" collection="userIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        and task_role = #{taskRole}
    </delete>

</mapper>