<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.mapper.ProjectBusinessMilestonesMapper">

<select id="selEvalProjectData" resultType="com.gok.pboot.pms.entity.domain.ProjectBusinessMilestones">
    SELECT
        project_id,
        MAX( expected_complete_date ) AS expectedCompleteDate,
        MAX( actual_complete_date ) AS actualCompleteDate
    FROM
        project_business_milestones
    WHERE
        del_flag = 0
    <if test="projectIds != null and projectIds.size() > 0">
        AND project_id IN
        <foreach collection="projectIds" separator="," item="item" open="(" close=")">
            #{item}
        </foreach>
    </if>
    GROUP BY
        project_id;
    </select>

    <select id="selConfirmedData" resultType="com.gok.pboot.pms.entity.vo.ProjectDataConfirmedVO">
        SELECT
            t1.project_id AS id,
            MAX(contract_name) AS contractName,
            MAX(expected_complete_date) AS acceptanceDate,
            MAX(actual_complete_date) AS warrantyPeriodStartDate,
            pi.zbqy AS warrantyMonths
        FROM (
                 (
                     SELECT
                         project_id,
                         contract_name,
                         NULL AS expected_complete_date,
                         NULL AS actual_complete_date
                     FROM
                         project_business_milestones
                     WHERE
                         del_flag = ${@<EMAIL>()}
                       AND account_type = "质保款"
                       AND project_id = #{projectId}
                     ORDER BY
                         actual_complete_date DESC
                         LIMIT 1
                 )
                 UNION ALL
                 (
                     SELECT
                         project_id,
                         NULL AS contract_name,
                         NULL AS expected_complete_date,
                         actual_complete_date
                     FROM
                         project_business_milestones
                     WHERE
                         del_flag = ${@<EMAIL>()}
                       AND if_finish = 0
                       AND project_id = #{projectId}
                     ORDER BY
                         actual_complete_date DESC
                         LIMIT 1
                 )
                 UNION ALL
                 (
                     SELECT
                         project_id,
                         NULL AS contract_name,
                         actual_complete_date AS expected_complete_date,
                         NULL AS actual_complete_date
                     FROM
                         project_business_milestones
                     WHERE
                         del_flag = ${@<EMAIL>()}
                       AND account_type = "终验款"
                       AND project_id = #{projectId}
                     ORDER BY
                         actual_complete_date DESC
                         LIMIT 1
                 )
             ) AS t1
                 LEFT JOIN project_info AS pi ON t1.project_id = pi.id
        GROUP BY
            t1.project_id,
            pi.zbqy
    </select>

</mapper>
