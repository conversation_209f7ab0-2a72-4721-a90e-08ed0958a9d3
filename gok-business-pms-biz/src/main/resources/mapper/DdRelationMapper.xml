<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.mapper.DdRelationMapper">

    

    <!-- 通用字段 -->
    <sql id="Base_Column_List">
        id, relate_id, relate_type, didi_id, sync_time
    </sql>

    <!-- 根据关联ID和类型查询关联关系 -->
    <select id="selectByRelateIdAndType" resultType="com.gok.pboot.pms.entity.domain.DdRelation">
        SELECT
        <include refid="Base_Column_List"/>
        FROM dd_relation
        WHERE relate_id = #{relateId} AND relate_type = #{relateType}
        LIMIT 1
    </select>


    <!-- 根据滴滴项目ID和关联类型查询关联关系 -->
    <select id="selectByDidiIdAndType" resultType="com.gok.pboot.pms.entity.domain.DdRelation">
        SELECT
        <include refid="Base_Column_List"/>
        FROM dd_relation
        WHERE didi_project_id = #{didiProjectId} AND relate_type = #{relateType}
        LIMIT 1
    </select>

    <!-- 更新同步时间 -->
    <update id="updateSyncTimeByRelateIdAndType">
        UPDATE dd_relation
        SET sync_time = NOW()
        WHERE relate_id = #{relateId} AND relate_type = #{relateType}
    </update>



</mapper>
