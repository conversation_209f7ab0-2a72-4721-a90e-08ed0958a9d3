<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.gok.pboot.pms.mapper.ProjectContractAmendmentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.gok.pboot.pms.entity.domain.ProjectContractAmendment">
        <id column="id" property="id"/>
        <result column="request_id" property="requestId"/>
        <result column="project_id" property="projectId"/>
        <result column="applicat_id" property="applicatId"/>
        <result column="applicat" property="applicat"/>
        <result column="process_type" property="processType"/>
        <result column="status" property="status"/>
        <result column="name" property="name"/>
        <result column="filing_date_time" property="filingDateTime"/>
        <result column="ctime" property="ctime"/>
        <result column="llhtmc" property="llhtmc"/>
        <result column="bglx" property="bglx"/>
        <result column="xsjhtqdsj" property="xsjhtqdsj"/>
        <result column="bgxyyjsl" property="bgxyyjsl"/>
        <result column="htfj" property="htfj"/>
        <result column="htfj_image_file_id" property="htfjImageFileId"/>
        <result column="htfj_image_file_name" property="htfjImageFileName"/>
        <result column="xhtfj" property="xhtfj"/>
        <result column="xhtfj_image_file_id" property="xhtfjImageFileId"/>
        <result column="xhtfj_image_file_name" property="xhtfjImageFileName"/>
        <result column="htfjygz" property="htfjygz"/>
        <result column="htfjygz_image_file_id" property="htfjygzImageFileId"/>
        <result column="htfjygz_image_file_name" property="htfjygzImageFileName"/>
        <result column="flowno" property="flowno"/>
        <result column="xyjsl" property="xyjsl"/>
        <result column="sjhtbh" property="sjhtbh"/>
        <result column="bgxyqdrq" property="bgxyqdrq"/>
        <result column="bgyy" property="bgyy"/>
        <result column="bgnrxqjsm" property="bgnrxqjsm"/>
        <result column="remark" property="remark"/>
        <result column="bgbcjczzxyfjygz" property="bgbcjczzxyfjygz"/>
        <result column="bgbcjczzxyfjygz_image_file_id" property="bgbcjczzxyfjygzImageFileId"/>
        <result column="bgbcjczzxyfjygz_image_file_name" property="bgbcjczzxyfjygzImageFileName"/>
        <result column="nodeoperator" property="nodeoperator"/>
    </resultMap>

</mapper>


