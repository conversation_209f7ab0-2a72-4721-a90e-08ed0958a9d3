<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.mapper.CommonMapper">
    <sql id="splitIntsStr">
        <bind name="ints" value="@com.gok.pboot.pms.Util.MBUtils@splitToIntArray(${intsStr})"/>
        <foreach collection="ints" item="i" open="(" separator="," close=")">
            #{i}
        </foreach>
    </sql>

    <sql id="splitLongsStr">
        <bind name="longs" value="@com.gok.pboot.pms.Util.MBUtils@splitToLongArray(${longsStr})"/>
        <foreach collection="longs" item="l" open="(" separator="," close=")">
            #{l}
        </foreach>
    </sql>

    <sql id="splitStr">
        <bind name="str" value="${str}.split(',')"/>
        <foreach collection="str" item="s" open="(" separator="," close=")">
            #{s}
        </foreach>
    </sql>
</mapper>