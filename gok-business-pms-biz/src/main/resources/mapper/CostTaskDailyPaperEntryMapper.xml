<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.cost.mapper.CostTaskDailyPaperEntryMapper">
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        pe.id AS 'id',
        pe.project_id AS 'projectId',
        pe.project_name AS 'projectName',
        pe.task_id AS 'taskId',
        pe.task_name AS 'taskName',
        pe.daily_paper_id AS 'dailyPaperId',
        pe.approval_status AS 'approvalStatus',
        pe.submission_date AS 'submissionDate',
        pe.normal_hours AS 'normalHours',
        pe.added_hours AS 'addedHours',
        pe.work_overtime_hours AS 'workOvertimeHours',
        pe.rest_overtime_hours AS 'restOvertimeHours',
        pe.holiday_overtime_hours AS 'holidayOvertimeHours',
        pe.description AS 'description',
        pe.approval_reason AS 'approvalReason',
        pe.user_id AS 'userId',
        pe.user_real_name AS 'userRealName',
        pe.user_dept_id AS 'userDeptId',
        pe.creator AS 'creator',
        pe.creator_id AS 'creatorId',
        pe.modifier AS 'modifier',
        pe.modifier_id AS 'modifierId',
        pe.ctime AS 'ctime',
        pe.mtime AS 'mtime',
        pe.del_flag AS 'delFlag',
        pe.work_type AS 'workType',
        pe.old_task_flag AS 'oldTaskFlag'
    </sql>

    <insert id="batchSave">
        INSERT INTO cost_task_daily_paper_entry
        (
        id,
        project_id,
        project_name,
        task_id,
        task_name,
        daily_paper_id,
        approval_status,
        submission_date,
        normal_hours,
        added_hours,
        work_overtime_hours,
        rest_overtime_hours,
        holiday_overtime_hours,
        description,
        approval_reason,
        user_id,
        user_real_name,
        user_dept_id,
        creator,
        creator_id,
        modifier,
        modifier_id,
        ctime,
        mtime,
        del_flag,
        work_type,
        old_task_flag,
        actual_labor_cost
        )
        VALUES
        <foreach collection="poList" item="po" separator=",">
            (
            #{po.id},
            #{po.projectId},
            #{po.projectName},
            #{po.taskId},
            #{po.taskName},
            #{po.dailyPaperId},
            #{po.approvalStatus},
            #{po.submissionDate},
            #{po.normalHours},
            #{po.addedHours},
            #{po.workOvertimeHours},
            #{po.restOvertimeHours},
            #{po.holidayOvertimeHours},
            #{po.description},
            #{po.approvalReason},
            #{po.userId},
            #{po.userRealName},
            #{po.userDeptId},
            #{po.creator},
            #{po.creatorId},
            #{po.modifier},
            #{po.modifierId},
            #{po.ctime},
            #{po.mtime},
            #{po.delFlag},
            #{po.workType},
            #{po.oldTaskFlag},
            #{po.actualLaborCost}
            )
        </foreach>
    </insert>


    <insert id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            UPDATE cost_task_daily_paper_entry
            SET
            id = #{item.id},
            project_id = #{item.projectId},
            project_name = #{item.projectName},
            task_id = #{item.taskId},
            task_name = #{item.taskName},
            daily_paper_id = #{item.dailyPaperId},
            approval_status = #{item.approvalStatus},
            submission_date = #{item.submissionDate},
            normal_hours = #{item.normalHours},
            added_hours = #{item.addedHours},
            work_overtime_hours = #{item.workOvertimeHours},
            rest_overtime_hours = #{item.restOvertimeHours},
            holiday_overtime_hours = #{item.holidayOvertimeHours},
            description = #{item.description},
            approval_reason = #{item.approvalReason},
            user_id = #{item.userId},
            user_real_name = #{item.userRealName},
            user_dept_id = #{item.userDeptId},
            modifier = #{item.modifier},
            modifier_id = #{item.modifierId},
            mtime = #{item.mtime},
            del_flag = #{item.delFlag},
            work_type = #{item.workType},
            old_task_flag = #{item.oldTaskFlag},
            actual_labor_cost = #{item.actualLaborCost}
            WHERE
            id = #{item.id}
        </foreach>
    </insert>

    <select id="findList" resultType="com.gok.pboot.pms.cost.entity.domain.CostTaskDailyPaperEntry">
        SELECT
        <include refid="Base_Column_List"/>
        FROM cost_task_daily_paper_entry pe
        LEFT JOIN mhour_roster r ON pe.user_id = r.id
        <where>
            pe.del_flag = 0
            <if test="filter.onlyActiveFlag == true">
                AND pe.approval_status NOT IN (
                ${@<EMAIL>},
                ${@<EMAIL>}
                )
            </if>
            <if test="filter.approvalStatus != null">
                AND pe.approval_status = #{filter.approvalStatus}
            </if>
            <if test="filter.approvalStatusList != null">
                AND pe.approval_status IN
                <foreach collection="filter.approvalStatusList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="filter.projectId != null">
                AND pe.project_id = #{filter.projectId}
            </if>
            <if test="filter.projectIds != null and filter.projectIds.size() > 0">
                AND pe.project_id IN
                <foreach collection="filter.projectIds" item="pId" open="(" separator="," close=")">
                    #{pId}
                </foreach>
            </if>
            <if test="filter.projectName != null">
                <bind name="projectNameLike" value="'%' + filter.projectName + '%'"/>
                AND pe.project_name LIKE #{projectNameLike}
            </if>
            <if test="filter.taskName != null">
                <bind name="taskNameLike" value="'%' + filter.taskName + '%'"/>
                AND pe.task_name LIKE #{taskNameLike}
            </if>
            <if test="filter.submissionDateStart != null and filter.submissionDateEnd != null">
                AND pe.submission_date BETWEEN #{filter.submissionDateStart} AND #{filter.submissionDateEnd}
            </if>
            <if test="filter.startDate != null">
                AND pe.submission_date &gt;= #{filter.startDate}
            </if>
            <if test="filter.endDate != null">
                AND pe.submission_date &lt;= #{filter.endDate}
            </if>
            <if test="filter.filingDateRanges != null">
                <foreach collection="filter.filingDateRanges" item="range">
                    AND NOT (pe.submission_date BETWEEN #{range.first} AND #{range.second})
                </foreach>
            </if>
            <if test="filter.deptIds != null">
                AND r.dept_id IN
                <foreach collection="filter.deptIds" item="dId" open="(" separator="," close=")">
                    #{dId}
                </foreach>
            </if>
            <if test="filter.taskIds != null">
                AND pe.task_id IN
                <foreach collection="filter.taskIds" item="tId" open="(" separator="," close=")">
                    #{tId}
                </foreach>
            </if>
            <if test="filter.userIds != null">
                AND user_id IN
                <foreach collection="filter.userIds" item="uId" open="(" separator="," close=")">
                    #{uId}
                </foreach>
            </if>
            <if test="filter.userId != null">
                AND pe.user_id = #{filter.userId}
            </if>
            <if test="filter.userRealName != null">
                <bind name="userRealNameLike" value="'%' + filter.userRealName + '%'"/>
                AND pe.user_real_name LIKE #{userRealNameLike}
            </if>
            <if test="filter.username != null">
                <bind name="userRealNameLike" value="'%' + filter.username + '%'"/>
                AND pe.user_real_name LIKE #{userRealNameLike}
            </if>
            <if test="filter.reviewTriple != null">
                AND (
                pe.task_id IN
                <foreach collection="filter.reviewTriple.left" item="tId" separator="," open="(" close=")">
                    #{tId}
                </foreach>
                OR pe.user_id IN
                <foreach collection="filter.reviewTriple.middle" item="uId" separator="," open="(" close=")">
                    #{uId}
                </foreach>
                OR pe.project_id IN
                <foreach collection="filter.reviewTriple.right" item="pId" separator="," open="(" close=")">
                    #{pId}
                </foreach>
                )
            </if>
            <if test="filter.dailyPaperIds != null">
                AND pe.daily_paper_id IN
                <foreach collection="filter.dailyPaperIds" item="dId" open="(" separator="," close=")">
                    #{dId}
                </foreach>
            </if>
        </where>
        ORDER BY
        <if test="filter.orderBy == null">
            pe.id DESC
        </if>
        <if test="filter.orderBy != null">
            #{filter.orderBy}
        </if>
        <if test="filter.orderBy != null and filter.orderDesc == true">
            DESC
        </if>
    </select>

    <select id="findByDailyPaperId" resultType="com.gok.pboot.pms.cost.entity.domain.CostTaskDailyPaperEntry">
        SELECT
        <include refid="Base_Column_List"/>
        FROM cost_task_daily_paper_entry pe
        WHERE
        pe.del_flag = 0
        AND
        pe.daily_paper_id = #{dailyPaperId}
        ORDER BY pe.id
    </select>


    <update id="updateApprovalStatusById">
        UPDATE cost_task_daily_paper_entry
        SET
        approval_status = #{changeApprovalStatusDTO.approvalStatus},
        modifier = #{changeApprovalStatusDTO.approvalName},
        <if test="changeApprovalStatusDTO.approvalReason!= null and changeApprovalStatusDTO.approvalReason != '' ">
            approval_reason = #{changeApprovalStatusDTO.approvalReason},
        </if>
        modifier_id = #{changeApprovalStatusDTO.approvalId},
        mtime = #{changeApprovalStatusDTO.mtime}
        WHERE
        id = #{changeApprovalStatusDTO.id}
    </update>


    <update id="batchApproval">
        UPDATE cost_task_daily_paper_entry
        SET
        approval_status = 4,
        modifier = #{changeApprovalStatusDTO.approvalName},
        modifier_id = #{changeApprovalStatusDTO.approvalId},
        mtime = #{changeApprovalStatusDTO.mtime}
        WHERE
        id IN
        <foreach collection="ids" separator="," item="item" open="(" close=")">
            #{item}
        </foreach>
    </update>


    <select id="queryAllAndWorkCode" resultType="com.gok.pboot.pms.entity.vo.DailyPaperEntryExcelVO">
        SELECT
        pe.project_id AS 'projectId',
        pe.project_name AS 'projectName',
        pe.task_name AS 'taskName',
        pe.user_real_name AS 'userRealName',
        mr.work_code AS 'workCode',
        pe.submission_date AS 'submissionDate',
        pe.description AS 'description',
        pe.normal_hours AS 'normalHours',
        pe.added_hours AS 'addedHours',
        pe.normal_hours + pe.added_hours as 'totalHours',
        pe.user_id AS 'userId',
        pe.user_dept_id AS 'userDeptId',
        paper.user_status AS 'userStatus',
        pr.project_status AS 'projectStatus',
        (CASE WHEN pr.is_not_internal_project = 1 and pr.project_type = 0 THEN '公司信息化'
        WHEN pr.is_not_internal_project = 1 and pr.project_type = 1 THEN '通用课程开发'
        WHEN pr.is_not_internal_project = 1 and pr.project_type = 2 THEN '自研产品研发'
        WHEN pr.is_not_internal_project = 1 and pr.project_type = 3 THEN '标准化解决方案打造'
        WHEN pr.is_not_internal_project = 1 and pr.project_type = 4 THEN '专项人才供应链构建'
        WHEN pr.is_not_internal_project = 1 and pr.project_type = 99 THEN '部门工作'
        END) AS 'projectTypeName'
        FROM cost_task_daily_paper_entry pe
        left join mhour_roster mr on pe.user_id = mr.id
        left join cost_task_daily_paper paper on pe.daily_paper_id = paper.id
        LEFT JOIN project_info pr ON pr.id = pe.project_id
        WHERE
        pe.del_flag = 0
        AND
        pe.approval_status = 4
        <if test="filter.dailyPaperEntryIds != null and filter.dailyPaperEntryIds.size() > 0">
            AND pe.id IN
            <foreach collection="filter.dailyPaperEntryIds" item="dailyPaperEntryId" open="(" separator="," close=")">
                #{dailyPaperEntryId}
            </foreach>
        </if>
        <if test="filter.userIds != null and filter.userIds.size() > 0">
            AND pe.user_id IN
            <foreach collection="filter.userIds" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
        <if test="filter.taskIds != null and filter.taskIds.size() > 0">
            AND pe.task_id IN
            <foreach collection="filter.taskIds" item="taskId" open="(" separator="," close=")">
                #{taskId}
            </foreach>
        </if>
        <if test="filter.projectIds != null and filter.projectIds.size() > 0">
            AND pe.project_id IN
            <foreach collection="filter.projectIds" item="projectId" open="(" separator="," close=")">
                #{projectId}
            </foreach>
        </if>
        <if test="filter.startDate != null and filter.endDate != null">
            AND pe.submission_date BETWEEN #{filter.startDate} AND #{filter.endDate}
        </if>
        <if test="filter.deptIds != null and filter.deptIds != ''">
            AND pe.user_dept_id IN
            <include refid="com.gok.pboot.pms.mapper.CommonMapper.splitLongsStr">
                <property name="longsStr" value="filter.deptIds"/>
            </include>
        </if>
        <if test="filter.personnelStatus != null ">
            AND paper.user_status = #{filter.personnelStatus}
        </if>
        <if test="filter.isNotInternalProject != null ">
            AND pr.is_not_internal_project = #{filter.isNotInternalProject}
        </if>
        <if test="filter.projectTypeList != null and filter.projectTypeList.size()>0">
            AND pr.project_type IN
            <foreach collection="filter.projectTypeList" item="i" open="(" separator="," close=")">
                #{i}
            </foreach>
        </if>
        <if test="filter.projectStatusList != null and filter.projectStatusList.size()>0">
            AND pr.project_status IN
            <foreach collection="filter.projectStatusList" item="i" open="(" separator="," close=")">
                #{i}
            </foreach>
        </if>
    </select>


    <select id="selectFilingByDailyPaperIds" resultType="com.gok.pboot.pms.entity.dto.DailyPaperEntryFilingDTO">
        SELECT
        hdpe.id as dailyPaperEntryId,
        hdpe.daily_paper_id dailyPaperId,
        hdpe.submission_date submissionDate, -- 日报的填报日期
        IFNULL(hf.filed, 0) filed,
        normal_hours,
        added_hours
        FROM `cost_task_daily_paper_entry` hdpe
        LEFT JOIN mhour_filing hf ON hf.`year` = DATE_FORMAT(hdpe.submission_date, '%Y') AND hf.`month` =
        DATE_FORMAT(hdpe.submission_date, '%m')
        WHERE hdpe.del_flag = 0
        AND hdpe.id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="findBySubordinate" resultType="com.gok.pboot.pms.entity.vo.DailyReviewProjectAuditPageVO">
        SELECT
        pe.id AS 'id',
        pe.user_real_name AS 'userRealName',
        pe.project_name AS 'projectName',
        pe.task_name AS 'taskName',
        pe.task_id AS 'taskId',
        pe.submission_date AS 'submissionDate',
        pe.normal_hours AS 'normalHours',
        pe.added_hours AS 'addedHours',
        pe.description AS 'description',
        pe.approval_reason AS 'approvalReason',
        pe.approval_status AS 'approvalStatus',
        pe.work_type AS 'workType',
        pe.modifier AS 'approvalName',
        pe.daily_paper_id AS 'dailyPaperId',
        pe.user_id AS 'userId',
        pe.project_id AS 'projectId',
        pe.ctime commitDate,
        mdp.filling_state fillingState
        FROM
        cost_task_daily_paper_entry pe
        LEFT JOIN cost_task_daily_paper mdp ON pe.daily_paper_id = mdp.id
        <where>
            pe.approval_status !=0
            and pe.del_flag=0
            <if test="filter.userIds != null and filter.userIds.size() > 0">
                AND pe.user_id IN
                <foreach collection="filter.userIds" separator="," item="userId" open="(" close=")">
                    #{userId}
                </foreach>
            </if>
            <if test="filter.startTime !=null and filter.startTime!=''">
                and pe.submission_date &gt;=#{filter.startTime}
            </if>
            <if test="filter.endTime !=null and filter.endTime!=''">
                and pe.submission_date &lt;= #{filter.endTime}
            </if>
            <if test="filter.projectName != null">
                <bind value="'%' + filter.projectName + '%'" name="pNLike"/>
                AND pe.project_name LIKE #{pNLike}
            </if>
            <if test="filter.userName != null">
                <bind value="'%' + filter.userName + '%'" name="userName"/>
                AND pe.user_real_name LIKE #{userName}
            </if>
        </where>
        order by pe.submission_date desc, pe.mtime desc
    </select>
    <select id="findBySubordinateStatic" resultType="com.gok.pboot.pms.entity.vo.SubordinatePaperEntryStaticVO">
        SELECT
        pe.user_id userId,
        pe.submission_date submissionDate,
        pe.added_hours addedHours,
        pe.normal_hours normalHours,
        pe.work_overtime_hours workOvertimeHours,
        pe.rest_overtime_hours restOvertimeHours,
        pe.holiday_overtime_hours holidayOvertimeHours
        FROM
        cost_task_daily_paper_entry pe
        <where>
            pe.approval_status !=0
            and pe.del_flag=0
            <if test="filter.userIds != null and filter.userIds.size() > 0">
                AND pe.user_id IN
                <foreach collection="filter.userIds" separator="," item="userId" open="(" close=")">
                    #{userId}
                </foreach>
            </if>
            <if test="filter.startTime !=null and filter.startTime!=''">
                and pe.submission_date &gt;=#{filter.startTime}
            </if>
            <if test="filter.endTime !=null and filter.endTime!=''">
                and pe.submission_date &lt;= #{filter.endTime}
            </if>
            <if test="filter.projectName != null and filter.projectName!=''">
                <bind value="'%' + filter.projectName + '%'" name="pNLike"/>
                AND pe.project_name LIKE #{pNLike}
            </if>
            <if test="filter.userName != null and filter.userName!=''">
                <bind value="'%' + filter.userName + '%'" name="userName"/>
                AND pe.user_real_name LIKE #{userName}
            </if>
        </where>
    </select>
    <select id="findByPanel" resultType="com.gok.pboot.pms.entity.vo.DailPanelPaperPageVO">
        SELECT
        pe.id AS id,
        pe.user_real_name AS 'userRealName',
        pe.project_name AS 'projectName',
        pe.project_id AS 'projectId',
        pe.task_name AS 'taskName',
        pe.task_id AS 'taskId',
        pe.submission_date AS 'submissionDate',
        pe.normal_hours AS 'normalHours',
        pe.added_hours AS 'addedHours',
        pe.description AS 'description',
        pe.approval_status AS 'approvalStatus',
        pe.user_id AS 'userId',
        pro.manager_user_name AS 'managerUserName',
        pro.salesman_user_name AS 'salesmanUserName',
        pro.code AS 'code'
        FROM
        cost_task_daily_paper_entry pe
        LEFT JOIN mhour_project pro ON pe.project_id = pro.id and pro.del_flag = 0
        <where>
            pe.del_flag = 0
            <if test="filter.userId !=null and filter.userId!=''">
                and pe.user_id = #{filter.userId}
            </if>
            <if test="filter.startTime !=null and filter.startTime!=''">
                and pe.submission_date &gt;=#{filter.startTime}
            </if>
            <if test="filter.endTime !=null and filter.endTime!=''">
                and pe.submission_date &lt;= #{filter.endTime}
            </if>
            <if test="filter.projectName != null">
                <bind value="'%' + filter.projectName + '%'" name="pNLike"/>
                AND pe.project_name LIKE #{pNLike}
            </if>
            <if test="filter.taskName != null">
                <bind value="'%' + filter.taskName + '%'" name="tNLike"/>
                AND pe.task_name LIKE #{tNLike}
            </if>
        </where>
        ORDER BY
        pe.submission_date desc, CONVERT ( pe.project_name USING gbk )
    </select>
    <select id="panelSituationAnalysis" resultType="com.gok.pboot.pms.entity.vo.PanelProjectSituationAnalysisVO">
        SELECT SUM(hdpe.normal_hours) normalHours,
        SUM(hdpe.added_hours) addedHours,
        SUM(hdpe.work_overtime_hours) workOvertimeHours,
        SUM(hdpe.rest_overtime_hours) restOvertimeHours,
        SUM(hdpe.holiday_overtime_hours) holidayOvertimeHours
        FROM cost_task_daily_paper_entry hdpe
        WHERE del_flag = 0
        AND hdpe.user_id = #{filter.userId}
        <if test="filter.startTime != null and filter.endTime != null">
            and hdpe.submission_date &gt;= #{filter.startTime}
            and hdpe.submission_date &lt;= #{filter.endTime}
        </if>
        <if test="filter.projectName != null and filter.projectName != ''">
            and hdpe.project_name like concat('%', #{filter.projectName} ,'%')
        </if>
        <if test="filter.taskName != null">
            <bind value="'%' + filter.taskName + '%'" name="tNLike"/>
            AND hdpe.task_name LIKE #{tNLike}
        </if>
    </select>

    <select id="saturationAnalysisBySubmissionDateRangeAndDeptIdsAndUserIdsAndProjectIds"
            resultType="java.math.BigDecimal">
        SELECT
        COALESCE(SUM(hdpe.normal_hours+hdpe.added_hours),0) auditTime
        FROM cost_task_daily_paper_entry hdpe
        LEFT JOIN project_info mp ON mp.id = hdpe.project_id
        WHERE hdpe.del_flag = 0
        AND hdpe.approval_status = ${@<EMAIL>}
        AND hdpe.submission_date BETWEEN #{startDate} AND #{endDate}
        AND hdpe.user_dept_id IN
        <foreach collection="deptIds" item="deptId" open="(" close=")" separator=",">
            #{deptId}
        </foreach>
        AND hdpe.user_id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        <if test="projectIds != null">
            AND mp.id IN
            <foreach collection="projectIds" item="pId" open="(" separator="," close=")">
                #{pId}
            </foreach>
        </if>
    </select>

    <select id="findDSHPageByTaskIds" resultType="com.gok.pboot.pms.cost.entity.domain.CostTaskDailyPaperEntry">
        SELECT
        <include refid="Base_Column_List"/>
        FROM cost_task_daily_paper_entry pe
        LEFT JOIN mhour_filing hf on (year(pe.submission_date) = hf.year and month(pe.submission_date) = hf.month)
        WHERE
        pe.del_flag = 0
        AND pe.approval_status = 2
        AND
        pe.task_id IN
        <foreach collection="taskIds" open="(" separator="," close=")" item="taskId">
            #{taskId}
        </foreach>
        AND hf.del_flag = 0
        AND hf.filed = 0
    </select>

    <select id="findInvalidByTaskIds" resultType="com.gok.pboot.pms.cost.entity.domain.CostTaskDailyPaperEntry">
        SELECT
        <include refid="Base_Column_List"/>
        FROM cost_task_daily_paper_entry pe
        LEFT JOIN mhour_filing hf on (year(pe.submission_date) = hf.year and month(pe.submission_date) = hf.month)
        WHERE
        pe.del_flag = 0
        AND ((pe.approval_status = 2 AND hf.filed = 1) OR (pe.approval_status = 3 AND hf.filed = 1) OR
        (pe.approval_status = 1 AND hf.filed = 1))
        AND
        pe.task_id IN
        <foreach collection="taskIds" open="(" separator="," close=")" item="taskId">
            #{taskId}
        </foreach>
        AND hf.del_flag = 0
    </select>

    <select id="findInvalidPageByTaskIds" resultType="com.gok.pboot.pms.cost.entity.domain.CostTaskDailyPaperEntry">
        SELECT
        <include refid="Base_Column_List"/>
        FROM cost_task_daily_paper_entry pe
        LEFT JOIN mhour_filing hf on (year(pe.submission_date) = hf.year and month(pe.submission_date) = hf.month)
        WHERE
        pe.del_flag = 0
        AND ((pe.approval_status = 2 AND hf.filed = 1) OR (pe.approval_status = 3 AND hf.filed = 1) OR
        (pe.approval_status = 1 AND hf.filed = 1))
        AND
        pe.task_id IN
        <foreach collection="taskIds" open="(" separator="," close=")" item="taskId">
            #{taskId}
        </foreach>
        AND hf.del_flag = 0
    </select>


    <!-- 根据工单ID查询日报详情 -->
    <select id="findDailyDetailsByTaskId" resultType="com.gok.pboot.pms.cost.entity.vo.TaskDailyPaperDetailVO">
        SELECT
        e.submission_date,
        e.normal_hours,
        e.added_hours,
        e.description,
        e.approval_status,
        p.filling_state,
        p.submission_time as submissionTime,
        IF(e.approval_status = 3 or e.approval_status = 4, e.mtime,null) as auditTime
        FROM cost_task_daily_paper_entry e
        LEFT JOIN cost_task_daily_paper p ON e.daily_paper_id = p.id
        <where>
            e.del_flag = 0
            and e.task_id = #{taskId}
            <if test="approvalStatus != null">
                AND e.approval_status = #{approvalStatus}
            </if>
        </where>

        ORDER BY e.submission_date DESC
    </select>
</mapper>