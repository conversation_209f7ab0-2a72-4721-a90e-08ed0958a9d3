<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.mapper.FilingMapper">


    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.gok.pboot.pms.entity.Filing">
        <id column="id" property="id"/>
        <result column="year" property="year"/>
        <result column="month" property="month"/>
        <result column="filingStartDatetime" property="filingStartDatetime"/>
        <result column="filingEndDatetime" property="filingEndDatetime"/>
        <result column="filed" property="filed"/>
        <result column="operator" property="operator"/>
        <result column="creator" property="creator"/>
        <result column="creatorId" property="creatorId"/>
        <result column="modifier" property="modifier"/>
        <result column="modifierId" property="modifierId"/>
        <result column="ctime" property="ctime"/>
        <result column="mtime" property="mtime"/>
        <result column="delFlag" property="delFlag"/>
    </resultMap>


    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        a.id AS 'id',
					a.year AS 'year',
					a.month AS 'month',
					a.filing_start_datetime AS 'filingStartDatetime',
					a.filing_end_datetime AS 'filingEndDatetime',
					a.filed AS 'filed',
					a.operator AS 'operator',
					a.creator AS 'creator',
					a.creator_id AS 'creatorId',
					a.modifier AS 'modifier',
					a.modifier_id AS 'modifierId',
					a.ctime AS 'ctime',
					a.mtime AS 'mtime',
                    a.del_flag AS 'delFlag'
    </sql>

    <sql id="join"></sql>

    <select id="findList" resultType="com.gok.pboot.pms.entity.Filing">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mhour_filing a
        <include refid="join"/>
        <where>
            a.del_flag = 0
        </where>
        ORDER BY a.id desc
    </select>

    <!--逻辑删除-->
    <update id="deleteByLogic">
        UPDATE mhour_filing SET
            del_flag = 1
        WHERE id = #{id}
    </update>


    <insert id="batchSave">
        INSERT INTO mhour_filing (
        id,
        year,
        month,
        filing_start_datetime,
        filing_end_datetime,
        filed,
        operator,
        creator,
        creator_id,
        modifier,
        modifier_id,
        ctime,
        mtime,
        del_flag
        )VALUES
        <foreach collection="poList" item="item" separator=",">
            (
            #{item.id},
            #{item.year},
            #{item.month},
            #{item.filingStartDatetime},
            #{item.filingEndDatetime},
            #{item.filed},
            #{item.operator},
            #{item.creator},
            #{item.creatorId},
            #{item.modifier},
            #{item.modifierId},
            #{item.ctime},
            #{item.mtime},
            #{item.delFlag}
            )
        </foreach>
    </insert>

    <update id="batchDel">
        update mhour_filing set del_flag = 1 where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <insert id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            UPDATE mhour_filing  SET
            id = #{item.id},
            year = #{item.year},
            month = #{item.month},
            filing_start_datetime = #{item.filingStartDatetime},
            filing_end_datetime = #{item.filingEndDatetime},
            filed = #{item.filed},
            operator = #{item.operator},
            creator = #{item.creator},
            creator_id = #{item.creatorId},
            modifier = #{item.modifier},
            modifier_id = #{item.modifierId},
            ctime = #{item.ctime},
            mtime = #{item.mtime},
            del_flag = #{item.delFlag}
            where id = #{item.id}
        </foreach>
    </insert>

    <!--批量更新非空字段-->
    <update id="updateBatch" parameterType="arraylist">
        update mhour_filing
        <trim prefix="set" suffixOverrides=",">
            <trim prefix=" year =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.year!=null">
                        when id=#{item.id} then #{item.year}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" month =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.month!=null">
                        when id=#{item.id} then #{item.month}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" filing_start_datetime =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.filingStartDatetime!=null">
                        when id=#{item.id} then #{item.filingStartDatetime}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" filing_end_datetime =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.filingEndDatetime!=null">
                        when id=#{item.id} then #{item.filingEndDatetime}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" filed =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.filed!=null">
                        when id=#{item.id} then #{item.filed}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" operator =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.operator!=null">
                        when id=#{item.id} then #{item.operator}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" modifier =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.modifier!=null">
                        when id=#{item.id} then #{item.modifier}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" modifier_id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.modifierId!=null">
                        when id=#{item.id} then #{item.modifierId}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" mtime =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.mtime!=null">
                        when id=#{item.id} then #{item.mtime}
                    </if>
                </foreach>
            </trim>
        </trim>

        where
        id in
        <foreach collection="list" separator="," item="item" open="(" close=")">
            #{item.id}
        </foreach>
    </update>

    <update id="getCancelFileById">
        update mhour_filing set filed = 0 , operator = #{operator} where id = #{id}
    </update>

    <update id="getFileById">
        update mhour_filing set filed = 1 , operator = #{operator}  where id = #{id}
    </update>

    <select id="FilingFindPageVO" resultType="com.gok.pboot.pms.entity.vo.FilingFindPageVO">
        SELECT
        a.id AS 'id',
        CONCAT( a.year,'年') AS 'year',
        CONCAT( a.month,'月') AS 'month',
        a.filed AS 'filed',
        (CASE   WHEN a.filed = 0 THEN '未归档'
                WHEN a.filed = 1 THEN '已归档'
                END) AS 'filedName',
        a.operator AS 'operator',
        a.mtime AS 'mtime'
        FROM mhour_filing a
        <where>
            a.del_flag = 0
            <if test="filter.filed != null and  filter.filed != '' ">
                AND a.filed = #{filter.filed}
            </if>
        </where>
        ORDER BY a.filing_start_datetime desc
    </select>

    <select id="getFilingByDate" resultType="com.gok.pboot.pms.entity.Filing">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mhour_filing a
        <where>
            a.del_flag = 0
                AND DATE_FORMAT(a.filing_end_datetime, '%Y-%m-%d') &gt; DATE_FORMAT(#{date},'%Y-%m-%d')
                AND DATE_FORMAT(a.filing_start_datetime, '%Y-%m-%d') &lt;= DATE_FORMAT(#{date},'%Y-%m-%d')
        </where>
    </select>

    <select id="isFiledByDate" resultType="java.lang.Integer">
        SELECT filed
        FROM mhour_filing filingCheck
        WHERE
            del_flag = 0
        AND
            DATE_FORMAT(filing_end_datetime, '%Y-%m-%d') &gt; DATE_FORMAT(#{submissionDate},'%Y-%m-%d')
        AND
            DATE_FORMAT(filing_start_datetime, '%Y-%m-%d') &lt;= DATE_FORMAT(#{submissionDate},'%Y-%m-%d')
    </select>
    <select id="getMaxFiling" resultType="com.gok.pboot.pms.entity.Filing">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mhour_filing a
        <where>
            a.del_flag = 0 and filed=1
        </where>
        order by filing_end_datetime desc
        limit 1
    </select>

    <select id="findByDates" resultType="com.gok.pboot.pms.entity.Filing">
        SELECT
            <include refid="Base_Column_List"/>
        FROM mhour_filing a
        WHERE
            del_flag = 0
        AND (
            <foreach collection="dates" item="date" separator="OR">
                (
                    DATE_FORMAT(filing_end_datetime, '%Y-%m-%d') &gt; DATE_FORMAT(#{date}, '%Y-%m-%d')
                    AND DATE_FORMAT(filing_start_datetime, '%Y-%m-%d') &lt;= DATE_FORMAT(#{date}, '%Y-%m-%d')
                )
            </foreach>
        )
    </select>

    <select id="findByFiled" resultType="com.gok.pboot.pms.entity.Filing">
        SELECT
            <include refid="Base_Column_List"/>
        FROM mhour_filing a
        WHERE
            del_flag = 0
        AND filed = #{filed}
    </select>
</mapper>
