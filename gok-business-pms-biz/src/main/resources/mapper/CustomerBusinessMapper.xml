<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.mapper.CustomerBusinessMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.gok.pboot.pms.entity.CustomerBusiness">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="overview" property="overview" />
        <result column="structure_url" property="structureUrl" />
        <result column="report_url" property="reportUrl" />
        <result column="plan" property="plan" />
        <result column="creator" property="creator" />
        <result column="creator_id" property="creatorId" />
        <result column="modifier" property="modifier" />
        <result column="modifier_id" property="modifierId" />
        <result column="ctime" property="ctime" />
        <result column="mtime" property="mtime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, overview, structure_url, report_url, plan, creator, creator_id, modifier, modifier_id, ctime, mtime, del_flag
    </sql>

  <select id="findListPage" parameterType="com.gok.pboot.pms.entity.dto.CustomerBusinessDTO" resultMap="BaseResultMap">
    SELECT DISTINCT
    b.id, b.name, b.creator, b.creator_id, b.modifier, b.modifier_id, b.ctime, b.mtime, b.del_flag
    FROM customer_business b
    left join customer_business_person p on b.id = p.business_id
    left join customer_business_unit u on b.id = u.business_id
    <where>
      b.del_flag = 0
      <if test="filter.scope == null and filter.businessIdsInDataScope != null and filter.businessIdsInDataScope.size() > 0">
        and b.id in
        <foreach collection="filter.businessIdsInDataScope" item="pId" open="(" separator="," close=")">
          #{pId}
        </foreach>
      </if>
      <if test="filter.name != null">
        <bind name="nameLike" value="'%' + filter.name + '%'"/>
        and b.name LIKE #{nameLike}
      </if>
      <if test="filter.mangerName != null ">
        <bind name="mangerNameLike" value="'%' + filter.mangerName + '%'"/>
        AND (p.manager_name LIKE #{mangerNameLike} or u.unit_manager LIKE #{mangerNameLike})
        AND P.del_flag = 0 and u.del_flag = 0
      </if>
    </where>
    ORDER BY b.ctime DESC
  </select>
  <select id="findNameList"  parameterType="com.gok.pboot.pms.entity.dto.CustomerBusinessSearchDTO" resultType="com.gok.pboot.pms.entity.vo.CustomerBusinessListVO">
    select id as id, name as name from customer_business where del_flag = 0
    <if test="filter.key != null ">
      <bind name="keyLike" value="'%' + filter.key + '%'"/>
      AND name LIKE #{keyLike}
    </if>
    limit #{filter.limit}
  </select>

  <update id="updateById">
    update customer_business
    <trim prefix="SET" suffixOverrides=",">
      <if test="et.name != null">name = #{et.name},</if>
      <if test="et.overview != null">overview = #{et.overview},</if>
      <if test="et.structureUrl != null">structure_url = #{et.structureUrl},</if>
      <if test="et.reportUrl != null">report_url = #{et.reportUrl},</if>
      <if test="et.plan != null">plan = #{et.plan},</if>
      <if test="et.modifier != null">modifier = #{et.modifier},</if>
      <if test="et.modifierId != null">modifier_id = #{et.modifierId},</if>
      <if test="et.mtime != null">mtime = #{et.mtime},</if>
    </trim>
    where  id = #{et.id}
  </update>

</mapper>
