<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.gok.pboot.pms.mapper.ProjectTaskAttachmentMapper">
    <sql id="baseColumnList">
        a.id,
        a.project_id,
        a.task_id,
        a.file_id,
        a.file_name,
        a.creator,
        a.creator_id,
        a.modifier,
        a.modifier_id,
        a.ctime,
        a.mtime,
        a.del_flag
    </sql>

    <insert id="save">
        INSERT INTO project_task_attachment
        (id,project_id, task_id, file_id, file_name, creator, creator_id, modifier, modifier_id, ctime, mtime, del_flag)
        VALUES (#{id},#{projectId}, #{taskId}, #{fileId},#{fileName}, #{creator}, #{creatorId}, #{modifier},
                #{modifierId},
                #{ctime},
                #{mtime}, #{delFlag})
    </insert>

    <update id="delete">
        update project_task_attachment
        set del_flag = 1
        where task_id = #{taskId}
    </update>

    <select id="findByTaskId" resultType="com.gok.pboot.pms.entity.domain.ProjectTaskAttachment">
        SELECT
            <include refid="baseColumnList"/>
        FROM project_task_attachment a
        WHERE
            del_flag = 0
        AND
            task_id = #{taskId}
    </select>
</mapper>