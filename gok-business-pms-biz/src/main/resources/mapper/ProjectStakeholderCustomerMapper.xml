<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.gok.pboot.pms.mapper.ProjectStakeholderCustomerMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap id="baseResultMap" type="com.gok.pboot.pms.entity.domain.ProjectStakeholderCustomer">
        <result property="id" column="id"/>
        <result property="projectId" column="project_id"/>
        <result property="contact" column="contact"/>
        <result property="department" column="department"/>
        <result property="position" column="position"/>
        <result property="impactDegree" column="impact_degree"/>
        <result property="duty" column="duty"/>
        <result property="contactPhone" column="contact_phone"/>
        <result property="email" column="email"/>
        <result property="remark" column="remark"/>
        <result property="satisfactionSurvey" column="satisfaction_survey"/>
        <result property="creator" column="creator"/>
        <result property="creatorId" column="creator_id"/>
        <result property="modifier" column="modifier"/>
        <result property="modifierId" column="modifier_id"/>
        <result property="ctime" column="ctime"/>
        <result property="mtime" column="mtime"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <select id="getCustomerByProjectId" resultMap="baseResultMap">
        SELECT b.id,
               b.project_id,
               b.contact,
               b.department,
               b.position,
               b.impact_degree,
               b.duty,
               b.contact_phone,
               b.email,
               b.remark,
               b.satisfaction_survey
        FROM project_stakeholder_customer b
        where b.project_id = #{projectId}
          AND del_flag = 0
        order by b.ctime desc
    </select>

</mapper>