<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.mapper.CompensatoryLeaveDataMapper">


    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.gok.pboot.pms.entity.CompensatoryLeaveData">
        <id column="id" property="id"/>
        <result column="oaId" property="oaId"/>
        <result column="minuteData" property="minuteData"/>
        <result column="hourData" property="hourData"/>
        <result column="belongDate" property="belongDate"/>
        <result column="xmmc" property="xmmc"/>
        <result column="type" property="type"/>
    </resultMap>


    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        a.id AS 'id',
        a.oa_id AS 'oaId',
        a.minute_data AS 'minuteData',
        a.hour_data AS 'hourData',
        a.belong_date AS 'belongDate',
        a.xmmc AS 'xmmc',
        a.type AS 'type'
    </sql>

    <select id="findByDateTimeRangeAndUserId" resultType="com.gok.pboot.pms.entity.vo.CompensatoryLeaveDataVO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mhour_compensatory_leave_data a
        WHERE
         a.belong_date not in (select day_date from  mhour_holiday)
        <if test="type != null and type !='' ">
            and  a.type=#{type}
        </if>
        <if test="userId != null">
            and a.oa_id = #{userId}
        </if>
        <if test="startTime != null and endTime ==null ">
            AND  STR_TO_DATE(  a.belong_date, '%Y-%m-%d') >=  #{startTime}
        </if>
        <if test="startTime != null and endTime !=null ">
            AND  STR_TO_DATE(  a.belong_date, '%Y-%m-%d') >=  #{startTime}
            AND   #{endTime} >= STR_TO_DATE(  a.belong_date, '%Y-%m-%d')
        </if>
        <if test="startTime == null and endTime !=null ">
            AND  #{endTime} >= STR_TO_DATE(  a.belong_date, '%Y-%m-%d')
        </if>

    </select>

    <select id="findByDateTimeRangeAndUserIds" resultType="com.gok.pboot.pms.entity.vo.CompensatoryLeaveDataVO">
     SELECT
        <include refid="Base_Column_List"/>
        FROM mhour_compensatory_leave_data a
        WHERE
        a.belong_date not in (select day_date from  mhour_holiday)
        <if test="type != null and type !='' ">
            and  a.type=#{type}
        </if>
        <if test="userIds != null and userIds.size() > 0">
            and a.oa_id in
            <foreach collection="userIds" item="userId" separator="," open="(" close=")">
                #{userId}
            </foreach>
        </if>
        <if test="startTime != null and endTime ==null ">
            AND  STR_TO_DATE(  a.belong_date, '%Y-%m-%d') >=  #{startTime}
        </if>
        <if test="startTime != null and endTime !=null ">
            AND  STR_TO_DATE(  a.belong_date, '%Y-%m-%d') >=  #{startTime}
            AND   #{endTime} >= STR_TO_DATE(  a.belong_date, '%Y-%m-%d')
        </if>
        <if test="startTime == null and endTime !=null ">
            AND  #{endTime} >= STR_TO_DATE(  a.belong_date, '%Y-%m-%d')
        </if>

    </select>


    <select id="sumLeaveByDateTimeRangeAndUserIds" resultType="com.gok.pboot.pms.entity.vo.CompensatoryLeaveSumVO">
        select
            a.oa_id as userId,
            sum(a.hour_data)  as leaveSum
        from
        mhour_compensatory_leave_data a
        where
        a.belong_date not in (select day_date from  mhour_holiday)
        and a.oa_id in
        <foreach collection="userIds" item="userId" separator="," open="(" close=")">
            #{userId}
        </foreach>
        <if test="projectIds != null and projectIds.size() > 0">
            and a.xmmc in
            <foreach collection="projectIds" item="projectId" separator="," open="(" close=")">
                #{projectId}
            </foreach>
        </if>
        AND  STR_TO_DATE(  a.belong_date, '%Y-%m-%d %H:%i:%s') >=  #{startTime}
        AND  #{endTime} >= STR_TO_DATE(  a.belong_date, '%Y-%m-%d %H:%i:%s')
        AND  a.type=#{type}
        GROUP BY a.oa_id
    </select>

    <select id="sumLeaveDeptByDateTimeRangeAndUserIds"
            resultType="com.gok.pboot.pms.entity.vo.CompensatoryLeaveDeptSumVO">
        select
        b.dept_id as deptId,
        sum(a.hour_data)  as leaveSum
        from
        mhour_compensatory_leave_data a
        left join mhour_roster b on a.oa_id = b.oa_id
        where a.belong_date not in (select day_date from  mhour_holiday)
        and a.oa_id in
        <foreach collection="userIds" item="userId" separator="," open="(" close=")">
            #{userId}
        </foreach>
        <if test="projectIds != null and projectIds.size() > 0">
            and a.xmmc in
            <foreach collection="projectIds" item="projectId" separator="," open="(" close=")">
                #{projectId}
            </foreach>
        </if>
        AND  STR_TO_DATE(  a.belong_date, '%Y-%m-%d %H:%i:%s') >=  #{startTime}
        AND  #{endTime} >= STR_TO_DATE(  a.belong_date, '%Y-%m-%d %H:%i:%s')
        AND  a.type=#{type}
        GROUP BY b.dept_id
    </select>
    <select id="findTxByParams" resultType="com.gok.pboot.pms.entity.vo.CompensatoryLeaveDataVO">
        select
        <include refid="Base_Column_List"/>
        from mhour_compensatory_leave_data a
        <where>
            not exists (select 1 from mhour_holiday h where h.day_date = a.belong_date)
            <if test="filter.projectId != null">
                and a.xmmc = #{filter.projectId}
            </if>
            <if test="filter.startDate != null and filter.endDate != null">
                and a.belong_date between #{filter.startDate} and #{filter.endDate}
            </if>
            and a.oa_id in (
                select distinct user_id from  mhour_daily_paper_entry
                <where>
                    del_flag = 0
                    and approval_status in(
                        ${@<EMAIL>()},
                        ${@<EMAIL>()}
                    )
                    <if test="filter.projectId != null ">
                        and project_id = #{filter.projectId}
                    </if>
                    <if test="filter.startDate != null and filter.endDate != null">
                        and submission_date between #{filter.startDate} and #{filter.endDate}
                    </if>
                    <if test="filter.taskName != null">
                        and task_name like concat('%',#{filter.taskName},'%')
                    </if>
                    <if test="filter.userRealName != null and filter.userRealName != ''">
                        and user_real_name like concat('%',#{filter.userRealName},'%')
                    </if>
                    <if test="filter.taskIds != null and filter.taskIds != ''">
                        and task_id in
                        <include refid="com.gok.pboot.pms.mapper.CommonMapper.splitLongsStr">
                            <property name="longsStr" value="filter.taskIds"/>
                        </include>
                    </if>
                    <if test="filter.userIds != null and filter.userIds != ''">
                        and user_id in
                        <include refid="com.gok.pboot.pms.mapper.CommonMapper.splitLongsStr">
                            <property name="longsStr" value="filter.userIds"/>
                        </include>
                    </if>
                    <if test="filter.workType != null">
                        and work_type = #{filter.workType}
                    </if>
                </where>
            )
        </where>
    </select>

    <select id="selectPanelProjectPage" resultType="com.gok.pboot.pms.entity.vo.PanelProjectSituationVO">
        SELECT
        b.id projectId,
        b.item_name projectName,
        MIN(a.belong_date) startTime,
        MAX(a.belong_date) endTime,
        0 normalHours,
        0 workOvertimeHours,
        0 restOvertimeHours,
        0 holidayOvertimeHours,
        0 addedHours
        FROM mhour_compensatory_leave_data a left join
        project_info b on
        a.xmmc=b.id
        WHERE a.xmmc is not null
        AND a.oa_id = #{filter.userId}
        <if test="filter.startTime != null and filter.endTime != null">
            AND a.belong_date >= #{filter.startTime}
            AND  #{filter.endTime} >= a.belong_date
        </if>
        <if test="filter.projectName != null and filter.projectName != ''">
            AND b.item_name like concat('%', #{filter.projectName},'%')
        </if>
        GROUP BY b.id
        ORDER BY b.item_name
    </select>

    <select id="getCompensatoryHoursSum" resultType="java.math.BigDecimal">
        SELECT
        sum(a.hour_data)  as leaveSum
        FROM mhour_compensatory_leave_data a left join
        project_info b on
        a.xmmc=b.id
        WHERE a.xmmc is not null
        <if test="filter.userIds != null and filter.userIds.size() > 0">
            AND a.oa_id IN
            <foreach collection="filter.userIds" separator="," item="userId" open="(" close=")">
                #{userId}
            </foreach>
        </if>
        <if test="filter.startTime !=null and filter.startTime!=''">
            and a.belong_date &gt;=#{filter.startTime}
        </if>
        <if test="filter.endTime !=null and filter.endTime!=''">
            and a.belong_date &lt;= #{filter.endTime}
        </if>
        <if test="filter.userName != null and filter.userName!=''">
            <bind value="'%' + filter.userName + '%'" name="userName"/>
            AND a.name LIKE #{userName}
        </if>
        <if test="filter.type != null and filter.type!=''">
            AND a.type=#{filter.type}
        </if>
        <if test="filter.projectName != null and filter.projectName != ''">
            AND b.item_name like concat('%', #{filter.projectName},'%')
        </if>
    </select>





    <select id="projectLeaveByDateTimeRangeAndUserIds"
            resultType="com.gok.pboot.pms.entity.vo.CompensatoryLeaveProjectSumVO">
        select
        a.oa_id as userId,
        a.xmmc as projectId,
        sum(a.hour_data)  as leaveSum
        from
        mhour_compensatory_leave_data a
        where
        a.belong_date not in (select day_date from  mhour_holiday)
        and a.oa_id in
        <foreach collection="userIds" item="userId" separator="," open="(" close=")">
            #{userId}
        </foreach>
        <if test="projectIds != null and projectIds.size() > 0">
            and a.xmmc in
            <foreach collection="projectIds" item="projectId" separator="," open="(" close=")">
                #{projectId}
            </foreach>
        </if>
        AND  STR_TO_DATE(  a.belong_date, '%Y-%m-%d %H:%i:%s') >=  #{startTime}
        AND  #{endTime} >= STR_TO_DATE(  a.belong_date, '%Y-%m-%d %H:%i:%s')
        AND  a.type=#{type}
        GROUP BY a.oa_id,a.xmmc
    </select>
</mapper>
