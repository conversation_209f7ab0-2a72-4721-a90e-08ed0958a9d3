<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.cost.mapper.CostConfigSubsidyCustomMapper">



    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.gok.pboot.pms.cost.entity.domain.CostConfigSubsidyCustom">
        <id column="id" property="id" />
        <result column="version_id" property="versionId" />
        <result column="subsidy_name" property="subsidyName" />
        <result column="subsidy_price" property="subsidyPrice" />
        <result column="calculation_method" property="calculationMethod" />
        <result column="creator" property="creator" />
        <result column="creator_id" property="creatorId" />
        <result column="modifier" property="modifier" />
        <result column="modifier_id" property="modifierId" />
        <result column="ctime" property="ctime" />
        <result column="mtime" property="mtime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, version_id, subsidy_name, subsidy_price, calculation_method, creator, creator_id, modifier, modifier_id, ctime, mtime, del_flag
    </sql>
    <select id="getSubsidyCustomsByVersionId"
            resultType="com.gok.pboot.pms.cost.entity.vo.CostConfigSubsidyCustomVO">
        SELECT
            csc.id,
            csc.subsidy_name,
            csc.subsidy_price,
            csc.calculation_method,
            cv.version_name
        FROM cost_config_subsidy_custom csc
                 LEFT JOIN cost_config_version cv ON cv.id = csc.version_id AND cv.del_flag = 0
        WHERE csc.version_id = #{versionId}
          AND csc.del_flag = 0
        ORDER BY csc.id
    </select>

</mapper>
