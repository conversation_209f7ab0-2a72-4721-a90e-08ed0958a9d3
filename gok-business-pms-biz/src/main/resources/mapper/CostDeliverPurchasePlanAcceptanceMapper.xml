<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.cost.mapper.CostDeliverPurchasePlanAcceptanceMapper">
  <resultMap id="BaseResultMap" type="com.gok.pboot.pms.cost.entity.domain.CostDeliverPurchasePlanAcceptance">
    <!--@mbg.generated-->
    <!--@Table cost_deliver_purchase_plan_acceptance-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="cost_deliver_purchase_plan_id" jdbcType="BIGINT" property="costDeliverPurchasePlanId" />
    <result column="accept_payment_terms" jdbcType="VARCHAR" property="acceptPaymentTerms" />
    <result column="accept_status" jdbcType="TINYINT" property="acceptStatus" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="creator_id" jdbcType="BIGINT" property="creatorId" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="modifier_id" jdbcType="BIGINT" property="modifierId" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, cost_deliver_purchase_plan_id, accept_payment_terms, accept_status, creator, 
    creator_id, modifier, modifier_id, ctime, mtime, del_flag
  </sql>

  <insert id="batchSave">
  INSERT INTO cost_deliver_purchase_plan_acceptance(
        id,
        cost_deliver_purchase_plan_id,
        accept_payment_terms,
        accept_status
        )VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},
            #{item.costDeliverPurchasePlanId},
            #{item.acceptPaymentTerms},
            #{item.acceptStatus}
            )
        </foreach>
  </insert>

   <delete id="delByIds">
        update cost_deliver_purchase_plan_acceptance
        set del_flag = 1
                where
                del_flag = '0'
        <if test="idList != null and idList.size() != 0">
            and id in
            <foreach collection="idList" close=")" open="(" item="id" separator=",">
                #{id}
            </foreach>
        </if>
    </delete>

</mapper>