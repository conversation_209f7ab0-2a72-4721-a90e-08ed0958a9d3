<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.gok.pboot.pms.mapper.DailyPaperMapper">

    <!-- 日报一览表查询映射结果 -->
    <resultMap id="QCResultMap" type="com.gok.pboot.pms.entity.vo.DailyFindPageVO">
        <result column="userId" property="userId"/>
        <result column="personnelStatusName" property="personnelStatusName"/>
        <collection property="dailyPapers"
                    ofType="com.gok.pboot.pms.entity.vo.DailyPaperQcVO">
            <id column="id" property="id"/>
            <result column="submissionDate" property="submissionDate"/>
            <result column="workday" property="workday"/>
            <result column="fillingState" property="fillingState"/>
            <result column="dailyHourCount" property="dailyHourCount"/>
            <result column="addedHourCount" property="addedHourCount"/>
            <result column="approvalStatus" property="approvalStatus"/>
            <result column="hourData" property="hourData"/>
            <result column="type" property="type"/>
        </collection>
    </resultMap>


    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        paper.id AS 'id',
        paper.user_id AS 'userId',
        paper.user_status AS 'userStatus',
        paper.user_dept_id AS 'userDeptId',
        paper.submission_date AS 'submissionDate',
        paper.workday AS 'workday',
        paper.filling_state AS 'fillingState',
        paper.approval_status AS 'approvalStatus',
        paper.project_count AS 'projectCount',
        paper.task_count AS 'taskCount',
        paper.daily_hour_count AS 'dailyHourCount',
        paper.added_hour_count AS 'addedHourCount',
        paper.submission_time AS 'submissionTime',
        paper.creator AS 'creator',
        paper.creator_id AS 'creatorId',
        paper.modifier AS 'modifier',
        paper.modifier_id AS 'modifierId',
        paper.ctime AS 'ctime',
        paper.mtime AS 'mtime',
        paper.del_flag AS 'delFlag'
    </sql>

    <insert id="batchSave">
        INSERT INTO mhour_daily_paper
        (
        id,
        user_id,
        user_status,
        user_dept_id,
        submission_date,
        workday,
        filling_state,
        approval_status,
        project_count,
        task_count,
        daily_hour_count,
        added_hour_count,
        creator,
        creator_id,
        modifier,
        modifier_id,
        ctime,
        mtime,
        del_flag
        )
        VALUES
        <foreach collection="list" item="po" separator=",">
            (
            #{po.id},
            #{po.userId},
            #{po.userStatus},
            #{po.userDeptId},
            #{po.submissionDate},
            #{po.workday},
            #{po.fillingState},
            #{po.approvalStatus},
            #{po.projectCount},
            #{po.taskCount},
            #{po.dailyHourCount},
            #{po.addedHourCount},
            #{po.creator},
            #{po.creatorId},
            #{po.modifier},
            #{po.modifierId},
            #{po.ctime},
            #{po.mtime},
            #{po.delFlag}
            )
        </foreach>
    </insert>

    <insert id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            UPDATE mhour_daily_paper
            SET
            id = #{item.id},
            user_id = #{item.userId},
            user_status = #{item.userStatus},
            user_dept_id = #{item.userDeptId},
            submission_date = #{item.submissionDate},
            workday = #{item.workday},
            filling_state = #{item.fillingState},
            approval_status = #{item.approvalStatus},
            project_count = #{item.projectCount},
            task_count = #{item.taskCount},
            daily_hour_count = #{item.dailyHourCount},
            added_hour_count = #{item.addedHourCount},
            creator = #{item.creator},
            creator_id = #{item.creatorId},
            modifier = #{item.modifier},
            modifier_id = #{item.modifierId},
            ctime = #{item.ctime},
            mtime = #{item.mtime},
            del_flag = #{item.delFlag}
            WHERE
            id = #{item.id}
        </foreach>
    </insert>

    <update id="updateApprovalStatus">
        <foreach collection="papers" item="paper" separator=";">
            UPDATE mhour_daily_paper
            SET
                approval_status = #{paper.approvalStatus},
                mtime = #{paper.mtime},
                modifier = #{paper.modifier},
                modifier_id = #{paper.modifierId}
            WHERE
                del_flag = 0
            AND
                id = #{paper.id}
        </foreach>
    </update>

    <update id="cleanUpUseless">
        UPDATE mhour_daily_paper paper
        LEFT JOIN mhour_roster roster ON paper.user_id = roster.id
        SET paper.del_flag = 1
        WHERE
            paper.del_flag = 0
        AND
            paper.approval_status = ${@<EMAIL>}
        AND
            roster.end_date IS NOT NULL
        AND
            paper.submission_date &gt; roster.end_date
    </update>

    <select id="findList" resultType="com.gok.pboot.pms.entity.DailyPaper">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mhour_daily_paper paper
        <where>
            paper.del_flag = 0
        </where>
        ORDER BY paper.submission_date DESC
    </select>

    <select id="findBySubmissionDateAndUserId" resultType="com.gok.pboot.pms.entity.DailyPaper">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mhour_daily_paper paper
        WHERE
        paper.del_flag = 0
        AND
        paper.submission_date = #{submissionDate}
        <if test="userId != null">
            AND
            paper.user_id = #{userId}
        </if>
    </select>

    <select id="dailyFindPageFront" resultType="com.gok.pboot.pms.entity.vo.TaskUserInfoVO">
        SELECT
        p.user_id,
        p.user_dept_id AS 'dept_id'
        FROM mhour_daily_paper p
        LEFT JOIN mhour_roster r ON p.user_id = r.id
        <where>
            p.del_flag = 0
            <if test="dailyFindPageDTO.userIds != null and dailyFindPageDTO.userIds.size() > 0">
                AND p.user_id IN
                <foreach collection="dailyFindPageDTO.userIds" separator="," item="item" open="(" close=")">#{item}
                </foreach>
            </if>
            <if test="dailyFindPageDTO.personnelStatus != null ">
                AND r.employee_status = #{dailyFindPageDTO.personnelStatus}
            </if>
        </where>
        GROUP BY p.user_id,p.user_dept_id
    </select>

    <select id="dailyFindPage" resultMap="QCResultMap">
        SELECT t1.user_id AS 'userId',
        (CASE WHEN t1.user_status = 0 THEN '正式'
        WHEN t1.user_status = 1 THEN '实习'
        WHEN t1.user_status = 2 THEN '项目实习'
        WHEN t1.user_status = 3 THEN '外包'
        END) AS 'personnelStatusName',
        t2.id AS 'id',
        t2.submission_date AS 'submissionDate',
        t2.workday AS 'workday',
        t2.filling_state AS 'fillingState',
        t2.daily_hour_count AS 'dailyHourCount',
        t2.added_hour_count AS 'addedHourCount',
        t2.approval_status AS 'approvalStatus',
        t1.user_dept_id AS 'dept_id',
        t2.hour_data AS 'hourData',
        t2.type
        FROM (
        SELECT
        p.user_id,
        p.user_status,
        p.user_dept_id
        FROM mhour_daily_paper p
        <where>
            p.del_flag = 0
            <if test="dailyFindPageDTO.startTime != null">
                AND DATE_FORMAT(p.submission_date, '%Y-%m-%d') &gt;= DATE_FORMAT(#{dailyFindPageDTO.startTime},
                '%Y-%m-%d')
            </if>
            <if test="dailyFindPageDTO.endTime != null">
                AND DATE_FORMAT(p.submission_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{dailyFindPageDTO.endTime},
                '%Y-%m-%d')
            </if>
            <if test="dailyFindPageDTO.userIds != null and dailyFindPageDTO.userIds.size() > 0">
                AND p.user_id IN
                <foreach collection="dailyFindPageDTO.userIds" separator="," item="item" open="(" close=")">#{item}
                </foreach>
            </if>
        </where>
        GROUP BY p.user_id
        LIMIT #{adapter.begin} , #{adapter.size}
        ) t1 LEFT JOIN
        (
        SELECT
        p.user_id ,
        p.id ,
        p.submission_date ,
        p.workday,
        p.filling_state ,
        p.daily_hour_count,
        p.added_hour_count,
        p.approval_status,
        ol.hour_data,
        ol.type
        FROM mhour_daily_paper p
        LEFT JOIN (
        SELECT SUM( ol.hour_data ) AS hour_data, MIN( ol.type ) AS type, ol.oa_id, ol.belongdate
        FROM mhour_overtime_leave_data ol WHERE ol.type NOT IN ( 99,100 )
        GROUP BY ol.oa_id, ol.belongdate
        ) ol ON ol.belongdate = p.submission_date AND ol.oa_id = p.user_id
        LEFT JOIN mhour_roster r ON p.user_id = r.id
        <where>
            p.del_flag = 0
            <if test="dailyFindPageDTO.startTime != null">
                AND DATE_FORMAT(p.submission_date, '%Y-%m-%d') &gt;= DATE_FORMAT(#{dailyFindPageDTO.startTime},
                '%Y-%m-%d')
            </if>
            <if test="dailyFindPageDTO.endTime != null">
                AND DATE_FORMAT(p.submission_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{dailyFindPageDTO.endTime},
                '%Y-%m-%d')
            </if>
            <if test="dailyFindPageDTO.userIds != null and dailyFindPageDTO.userIds.size() > 0">
                AND p.user_id IN
                <foreach collection="dailyFindPageDTO.userIds" separator="," item="item" open="(" close=")">#{item}
                </foreach>
            </if>
            <if test="dailyFindPageDTO.personnelStatus != null ">
                AND r.employee_status = #{dailyFindPageDTO.personnelStatus}
            </if>
        </where>
        ) t2 ON t2.user_id = t1.user_id
        ORDER BY t2.submission_date,t2.user_id
    </select>

    <select id="dailyFindPageCount" resultType="long">
        SELECT
            p.user_id AS 'user_id'
        FROM mhour_daily_paper p
        LEFT JOIN mhour_roster r ON p.user_id = r.id
        <where>
            p.del_flag = 0
            <if test="dailyFindPageDTO.startTime != null">
                AND DATE_FORMAT(p.submission_date, '%Y-%m-%d') &gt;= DATE_FORMAT(#{dailyFindPageDTO.startTime},
                '%Y-%m-%d')
            </if>
            <if test="dailyFindPageDTO.endTime != null">
                AND DATE_FORMAT(p.submission_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{dailyFindPageDTO.endTime},
                '%Y-%m-%d')
            </if>
            <if test="dailyFindPageDTO.userIds != null and dailyFindPageDTO.userIds.size() > 0">
                AND p.user_id IN
                <foreach collection="dailyFindPageDTO.userIds" separator="," item="item" open="(" close=")">#{item}
                </foreach>
            </if>
            <if test="dailyFindPageDTO.personnelStatus != null ">
                AND r.employee_status = #{dailyFindPageDTO.personnelStatus}
            </if>
        </where>
        GROUP BY p.user_id
    </select>

    <select id="findBySubmissionDateRange" resultType="com.gok.pboot.pms.entity.vo.DailyPaperVO">
        SELECT
        <include refid="Base_Column_List"/>
        ,group_concat(paperEntry.approval_status) as approvalStatusStrings
        FROM mhour_daily_paper paper
        left join mhour_daily_paper_entry paperEntry on paper.id = paperEntry.daily_paper_id
        WHERE
        paper.del_flag = 0
        AND
        paper.submission_date BETWEEN #{startDate} AND #{endDate}
        <if test="userId != null">
            AND paper.user_id = #{userId}
        </if>
        <if test="projectNameLike != null and projectNameLike != ''">
            <bind name="projectName" value="'%' + projectNameLike + '%' "/>
            AND paperEntry.project_name like #{projectName}
        </if>
        <if test="approvalStatusTab != null">
            AND paper.approval_status = #{approvalStatusTab}
        </if>
        <if test="approvalStatusList != null and approvalStatusList.size() > 0">
            AND paper.approval_status IN
            <foreach collection="approvalStatusList" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY paper.id
    </select>

    <select id="findStatisticBySubmissionDateRange"
            resultType="com.gok.pboot.pms.common.join.DailyPapersStatisticMonthly">
        SELECT
        COUNT(*) AS total,
        SUM(daily_hour_count) AS normalHours,
        SUM(added_hour_count) AS addedHours
        FROM mhour_daily_paper paper
        <if test="projectName != null">
            LEFT JOIN (
            SELECT DISTINCT daily_paper_id, project_name
            FROM mhour_daily_paper_entry
            WHERE del_flag = 0
            AND
            id IN
            (SELECT DISTINCT MIN(id) FROM mhour_daily_paper_entry GROUP BY daily_paper_id)
            ) entry
            ON paper.id = entry.daily_paper_id
        </if>
        WHERE
        paper.del_flag = 0
        AND
        paper.submission_date BETWEEN #{startDate} AND #{endDate}
        AND
        paper.approval_status NOT IN (-1, 0)
        <if test="userId != null">
            AND
            paper.user_id = #{userId}
        </if>
        <if test="projectName != null">
            <bind name="projectNameLike" value="'%' + projectName + '%'"/>
            AND
            entry.project_name LIKE #{projectNameLike}
        </if>
    </select>

    <select id="findOneBeforeBySubmissionDateAndUserId" resultType="com.gok.pboot.pms.entity.DailyPaper">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mhour_daily_paper paper
        WHERE
        paper.del_flag = 0
        AND
        paper.submission_date &lt; #{submissionDate}
        AND
        paper.id IN (
        SELECT daily_paper_id
        FROM mhour_tomorrow_plan_paper_entry
        WHERE
        del_flag = 0
        )
        <if test="userId != null">
            AND
            paper.user_id = #{userId}
        </if>
        ORDER BY paper.submission_date DESC
        LIMIT 1
    </select>

    <select id="findBySubmissionDateAndUserIds" resultType="com.gok.pboot.pms.entity.DailyPaper">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mhour_daily_paper paper
        WHERE
        paper.del_flag = 0
        AND
        paper.submission_date = #{submissionDate}
        AND
        paper.user_id IN
        <foreach collection="userIds" item="userId" separator="," open="(" close=")">
            #{userId}
        </foreach>
    </select>

    <select id="findByDailyFindParams" resultType="com.gok.pboot.pms.entity.DailyPaper">
        SELECT
        paper.id AS "id",
        paper.user_id AS "userId",
        paper.user_status AS "userStatus",
        paper.user_dept_id AS "userDeptId",
        paper.user_dept_id AS "dept_id",
        paper.submission_date AS "submissionDate",
        paper.workday AS "workday",
        paper.filling_state AS "fillingState",
        paper.approval_status AS "approvalStatus",
        paper.project_count AS "projectCount",
        paper.task_count AS "taskCount",
        paper.added_hour_count AS "addedHourCount",
        paper.creator AS "creator",
        paper.creator_id AS "creatorId",
        paper.modifier AS "modifier",
        paper.modifier_id AS "modifierId",
        paper.ctime AS "ctime",
        paper.mtime AS "mtime",
        paper.del_flag AS "delFlag",
        if(paper.approval_status = 0,0,paper.daily_hour_count) AS "dailyHourCount"
        FROM mhour_daily_paper paper
        LEFT JOIN mhour_roster roster ON paper.user_id = roster.id
        WHERE
            paper.del_flag = 0
        AND
            paper.submission_date BETWEEN #{startDate} AND #{endDate}
        <if test="name != null">
            <bind name="nameLike" value="'%' + name + '%'"/>
            AND roster.alias_name LIKE #{nameLike}
        </if>
        <if test="personnelStatus != null">
            AND paper.user_status = #{personnelStatus}
        </if>
        <if test="userIds != null">
            AND paper.user_id IN
            <foreach collection="userIds" item="uId" separator="," open="(" close=")">
                #{uId}
            </foreach>
        </if>
        <if test="deptIds != null and deptIds.size()>0">
            AND roster.dept_id IN
            <foreach collection="deptIds" item="deptId" separator="," open="(" close=")">
                #{deptId}
            </foreach>
        </if>
        <if test="filterUserIds != null">
            AND paper.user_id NOT IN
            <foreach collection="filterUserIds" item="uId" separator="," open="(" close=")">
                #{uId}
            </foreach>
        </if>
    </select>
    <select id="findBySubmissionDateRangeAndUserIdsDataScope" resultType="com.gok.pboot.pms.entity.DailyPaper">
        SELECT
        paper.id AS "id",
        paper.user_id AS "userId",
        paper.user_status AS "userStatus",
        paper.user_dept_id AS "userDeptId",
        paper.user_dept_id AS "dept_id",
        paper.submission_date AS "submissionDate",
        paper.workday AS "workday",
        paper.filling_state AS "fillingState",
        paper.approval_status AS "approvalStatus",
        paper.project_count AS "projectCount",
        paper.task_count AS "taskCount",
        paper.added_hour_count AS "addedHourCount",
        paper.creator AS "creator",
        paper.creator_id AS "creatorId",
        paper.modifier AS "modifier",
        paper.modifier_id AS "modifierId",
        paper.ctime AS "ctime",
        paper.mtime AS "mtime",
        paper.del_flag AS "delFlag",
        if(paper.approval_status = 0,0,paper.daily_hour_count) AS "dailyHourCount"
        FROM mhour_daily_paper paper
        WHERE
        del_flag = 0
        AND
        submission_date BETWEEN #{startDate} AND #{endDate}
        AND
        user_id IN
        <foreach collection="userIds" open="(" separator="," close=")" item="uId">
            #{uId}
        </foreach>
    </select>

    <select id="findUnAuditDailyByTime" resultType="com.gok.pboot.pms.entity.DailyPaper">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mhour_daily_paper paper
        WHERE
        del_flag = 0 and approval_status IN (2,3,4)
        AND submission_date BETWEEN #{startDate} AND #{endDate}
    </select>
    <select id="saturationAnalysisBySubmissionDateRangeAndDeptIdsAndUserIdsAndProjectIds" resultType="java.math.BigDecimal">
        SELECT
        COALESCE(SUM(b.normal_hours+b.added_hours),0)  delaySubmit
        FROM mhour_daily_paper hdpe
        LEFT JOIN mhour_daily_paper_entry b on hdpe.id = b.daily_paper_id
        LEFT JOIN project_info mp ON mp.id = b.project_id
        WHERE hdpe.del_flag = 0
            and hdpe.approval_status = ${@<EMAIL>}
            and b.ctime> DATE_ADD(DATE(hdpe.submission_date), INTERVAL 1 DAY) + INTERVAL '14:00' HOUR_MINUTE
            and hdpe.submission_date &gt;= #{startDate}
            and hdpe.submission_date &lt;= #{endDate}
            AND hdpe.filling_state = ${@<EMAIL>}
            AND hdpe.user_dept_id IN
            <foreach collection="deptIds" item="deptId" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
            AND hdpe.user_id IN
            <foreach collection="userIds" item="uId" open="(" separator="," close=")">
                #{uId}
            </foreach>
        <if test="projectIds != null">
            AND mp.id IN
            <foreach collection="projectIds" item="pId" open="(" separator="," close=")">
                #{pId}
            </foreach>
        </if>
    </select>

    <select id="findSubmitUserIdList" resultType="java.lang.Long">
        SELECT DISTINCT pa.user_id AS 'userId'
        FROM mhour_daily_paper pa
        LEFT JOIN mhour_roster r ON pa.user_id = r.id
        WHERE
        pa.del_flag = 0
        AND pa.submission_date BETWEEN #{dto.startTime} AND #{dto.endTime}
        <if test="dto.personnelStatus != null">
            AND pa.user_status = #{dto.personnelStatus}
        </if>
        <if test="dto.deptIds != null and dto.deptIds.size() > 0">
            AND r.dept_id IN
            <foreach collection="dto.deptIds" separator="," item="deptId" open="(" close=")">
                #{deptId}
            </foreach>
        </if>
        <if test="dto.name != null">
            <bind name="nameLike" value="'%' + dto.name + '%'"/>
            AND r.alias_name LIKE #{nameLike}
        </if>
        ORDER BY CAST(r.alias_name AS char character set gbk)
    </select>

    <select id="findSubmitList" resultType="com.gok.pboot.pms.entity.DailyPaper">
        SELECT
            paper.id AS 'id',
        paper.user_id AS 'userId',
        paper.user_status AS 'userStatus',
        mr.dept_id AS 'userDeptId',
        paper.submission_date AS 'submissionDate',
        paper.workday AS 'workday',
        paper.filling_state AS 'fillingState',
        paper.approval_status AS 'approvalStatus',
        paper.project_count AS 'projectCount',
        paper.task_count AS 'taskCount',
        paper.daily_hour_count AS 'dailyHourCount',
        paper.added_hour_count AS 'addedHourCount',
        paper.submission_time AS 'submissionTime',
        paper.creator AS 'creator',
        paper.creator_id AS 'creatorId',
        paper.modifier AS 'modifier',
        paper.modifier_id AS 'modifierId',
        paper.ctime AS 'ctime',
        paper.mtime AS 'mtime',
        paper.del_flag AS 'delFlag'
        FROM mhour_daily_paper paper
        left join mhour_roster mr on paper.user_id = mr.id
        WHERE
                paper.del_flag = 0
        <if test="dto.userIds != null and dto.userIds.size() > 0">
            AND paper.user_id IN
            <foreach collection="dto.userIds" item="uId" separator="," open="(" close=")">
                #{uId}
            </foreach>
        </if>
        <if test="dto.deptIds != null and dto.deptIds.size() > 0">
            AND mr.dept_id IN
            <foreach collection="dto.deptIds" separator="," item="deptId" open="(" close=")">
                #{deptId}
            </foreach>
        </if>
        <if test="dto.startTime != null and dto.endTime != null">
            AND paper.submission_date BETWEEN #{dto.startTime} AND #{dto.endTime}
        </if>
    </select>

    <select id="findMayAbnormal" resultType="com.gok.pboot.pms.entity.DailyPaper">
        SELECT
            <include refid="Base_Column_List"/>
        FROM mhour_daily_paper paper
        WHERE
            paper.del_flag = 0
        AND
            paper.submission_date BETWEEN #{startDate} AND #{endDate}
        <if test="userIds != null">
            AND paper.user_id IN
            <foreach collection="userIds" open="(" separator="," close=")" item="uId">
                #{uId}
            </foreach>
        </if>
        <if test="filterUserIds != null">
            AND paper.user_id NOT IN
            <foreach collection="filterUserIds" open="(" separator="," close=")" item="uId">
                #{uId}
            </foreach>
        </if>
        AND (
                (
                    paper.approval_status = ${@<EMAIL>}
                ) OR
                (
                    paper.workday = ${@<EMAIL>} AND (
                        paper.daily_hour_count != ${@com.gok.pboot.pms.Util.BigDecimalUtils@SEVEN_DECIMAL} OR
                        paper.approval_status IN (
                            ${@<EMAIL>},
                            ${@<EMAIL>}
                        )
                    )
                )
        )
        ORDER BY paper.submission_date DESC, paper.user_id
    </select>

    <select id="findIdMapInDataScope" resultType="com.gok.pboot.pms.entity.DailyPaper">
        SELECT
            <include refid="Base_Column_List"/>
        FROM mhour_daily_paper paper
        WHERE
            del_flag = 0
        <if test="filter.filingDateRanges != null">
            <foreach collection="filter.filingDateRanges" item="range">
                AND NOT (submission_date BETWEEN #{range.first} AND #{range.second})
            </foreach>
        </if>
        <if test="filter.startDate != null">
            AND submission_date &gt;= #{filter.startDate}
        </if>
        <if test="filter.endDate != null">
            AND submission_date &lt;= #{filter.endDate}
        </if>
    </select>

    <select id="findIdBySubmissionDateAndUserId" resultType="java.lang.Long">
        SELECT
            <include refid="Base_Column_List"/>
        FROM mhour_daily_paper paper
        WHERE
            del_flag = 0
        AND
            submission_date = #{submissionDate}
        AND
            user_id = #{userId}
        LIMIT 1
    </select>

    <select id="findSubmissionDateByUserIdAndSubmissionDateRangeAndApprovalStatusNot" resultType="java.time.LocalDate">
        SELECT
            submission_date
        FROM mhour_daily_paper
        WHERE
            del_flag = 0
        AND
            user_id = #{userId}
        AND
            submission_date BETWEEN #{startDate} AND #{endDate}
        AND
            approval_status != #{approvalStatus}
    </select>

    <select id="findDailyExcelList" resultType="com.gok.pboot.pms.entity.vo.DailyExcelExportVO">
        SELECT
        paper.user_id AS 'userId',
        paper.user_status AS 'userStatus',
        mr.dept_id AS 'userDeptId',
        paper.submission_date AS 'submissionDate',
        paper.workday AS 'workday',
        paper.filling_state AS 'fillingState',
        paper.approval_status AS 'approvalStatus',
        paper.project_count AS 'projectCount',
        paper.daily_hour_count AS 'dailyHourCount',
        paper.added_hour_count AS 'addedHourCount',
        paper.submission_time AS 'submissionTime',
        sum(e.work_overtime_hours) AS 'workOvertimeHours',
        sum(e.rest_overtime_hours) AS 'restOvertimeHours',
        sum(e.holiday_overtime_hours) AS 'holidayOvertimeHours'
        FROM mhour_daily_paper paper
        left join mhour_daily_paper_entry e on paper.id = e.daily_paper_id
        left join mhour_roster mr on paper.user_id = mr.id
        WHERE
            paper.del_flag = 0
        AND paper.user_id IN
        <foreach collection="dto.userIds" item="uId" separator="," open="(" close=")">
            #{uId}
        </foreach>
        <if test="dto.deptIds != null and dto.deptIds.size() > 0">
            AND mr.dept_id IN
            <foreach collection="dto.deptIds" separator="," item="deptId" open="(" close=")">
                #{deptId}
            </foreach>
        </if>
        <if test="dto.startTime != null and dto.endTime != null">
            AND paper.submission_date BETWEEN #{dto.startTime} AND #{dto.endTime}
        </if>
        GROUP BY paper.id
        ORDER BY CAST(mr.alias_name AS char character set gbk),paper.submission_date ASC
    </select>
</mapper>
