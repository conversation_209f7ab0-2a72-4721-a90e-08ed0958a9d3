<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.gok.pboot.pms.mapper.ProjectStakeholderMemberMapper">

    <sql id="baseColumnList">
        a.id,
        a.project_id,
        a.member_id,
        a.member_name,
        a.dept_name,
        a.position,
        a.role_type,
        a.duty,
        a.remark,
        a.sync_oa_type,
        a.creator,
        a.creator_id,
        a.modifier,
        a.modifier_id,
        a.ctime,
        a.mtime,
        a.del_flag
    </sql>

    <delete id="logicalDelByIds">
--         delete from project_stakeholder_member
        update project_stakeholder_member
        set del_flag = 1
        where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.gok.pboot.pms.entity.domain.ProjectStakeholderMember" id="baseResultMap">
        <result property="id" column="id"/>
        <result property="projectId" column="project_id"/>
        <result property="memberId" column="member_id"/>
        <result property="memberName" column="member_name"/>
        <result property="deptName" column="dept_name"/>
        <result property="position" column="position"/>
        <result property="duty" column="duty"/>
        <result property="remark" column="remark"/>
        <result property="creator" column="creator"/>
        <result property="creatorId" column="creator_id"/>
        <result property="modifier" column="modifier"/>
        <result property="modifierId" column="modifier_id"/>
        <result property="ctime" column="ctime"/>
        <result property="mtime" column="mtime"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <select id="getMemberByProjectId" resultType="com.gok.pboot.pms.entity.vo.ProjectStakeholderMemberVO">
        SELECT
            b.id,
            b.project_id,
            b.member_id,
            b.member_name,
            b.dept_name,
            b.position,
            b.duty,
            b.remark
        FROM project_stakeholder_member b
        where
            b.project_id = #{projectId}
            <if test="roleType != null">
                AND role_type = #{roleType}
            </if>
          AND del_flag =0
        order by b.ctime desc
    </select>

    <select id="findProjectIdByMemberId" resultType="java.lang.Long">
        SELECT
            project_id
        FROM
            project_stakeholder_member
        WHERE
            del_flag = 0
        AND
            member_id = #{memberUserId}
    </select>

    <select id="findByProjectIdAndMemberId" resultType="com.gok.pboot.pms.entity.domain.ProjectStakeholderMember">
        SELECT
            <include refid="baseColumnList"/>
        FROM project_stakeholder_member a
        WHERE
            del_flag = 0
        AND
            member_id = #{memberId}
    </select>

    <select id="findByMemberId" resultType="com.gok.pboot.pms.entity.domain.ProjectStakeholderMember">
        SELECT
            <include refid="baseColumnList"/>
        FROM project_stakeholder_member a
        WHERE
            del_flag = 0
        AND
            member_id = #{memberId}
    </select>

    <select id="findList" resultType="com.gok.pboot.pms.entity.domain.ProjectStakeholderMember">
        SELECT
            <include refid="baseColumnList"/>
        FROM project_stakeholder_member a
        WHERE
            del_flag = 0
        <if test="filter.projectId != null">
            AND project_id = #{filter.projectId}
        </if>
        <if test="filter.memberName != null">
            <bind name="memberNameLike" value="'%' + filter.memberName + '%'"/>
            AND member_name LIKE #{memberNameLike}
        </if>
        <if test="filter.memberIds != null and filter.memberIds.size() > 0">
            and member_id in
            <foreach collection="filter.memberIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="filter.syncOaType != null">
            AND sync_oa_type = #{filter.syncOaType}
            AND role_type NOT IN (
                ${@com.gok.pboot.pms.enumeration.RoleTypeEnum@PROJECT_SALES_MANAGER.getValue()},
                ${@com.gok.pboot.pms.enumeration.RoleTypeEnum@PROJECT_PRE_SALES_MANAGER.getValue()},
                ${@com.gok.pboot.pms.enumeration.RoleTypeEnum@PROJECT_MANAGER.getValue()}
            )
        </if>
        ORDER BY role_type, ctime DESC
    </select>

    <select id="getMembersByProjectId" resultType="com.gok.pboot.pms.entity.vo.ProjectStakeholderMemberVO">
        SELECT
        b.id,
        b.project_id,
        b.member_id,
        b.member_name,
        b.dept_name,
        b.position,
        b.role_type,
        b.duty,
        b.remark,
        b.sync_oa_type
        FROM project_stakeholder_member b
        where
        b.project_id = #{projectId}
        AND b.del_flag = 0
    </select>

    <insert id="batchSync">
        insert into project_stakeholder_member
        (
            id,
            project_id,
            member_id,
            member_name,
            dept_name,
            position,
            role_type,
            duty,
            remark,
            sync_oa_type,
            creator,
            creator_id,
            modifier,
            modifier_id,
            ctime,
            mtime,
            del_flag
        )
        values
            <foreach collection="list" item="item" index="index" separator=",">
                (
                 #{item.id},
                 #{item.projectId},
                 #{item.memberId},
                 #{item.memberName},
                 #{item.deptName},
                 #{item.position},
                 #{item.roleType},
                 #{item.duty},
                 #{item.remark},
                 #{item.syncOaType},
                 #{item.creator},
                 #{item.creatorId},
                 #{item.modifier},
                 #{item.modifierId},
                 #{item.ctime},
                 #{item.mtime},
                 #{item.delFlag}
                )
            </foreach>
    </insert>

    <insert id="batchSave">
        insert into project_stakeholder_member
        (
        id,
        project_id,
        member_id,
        member_name,
        dept_name,
        position,
        role_type,
        duty,
        remark,
        sync_oa_type,
        creator,
        creator_id,
        modifier,
        modifier_id,
        ctime,
        mtime,
        del_flag
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.id},
            #{item.projectId},
            #{item.memberId},
            #{item.memberName},
            #{item.deptName},
            #{item.position},
            #{item.roleType},
            #{item.duty},
            #{item.remark},
            #{item.syncOaType},
            #{item.creator},
            #{item.creatorId},
            #{item.modifier},
            #{item.modifierId},
            #{item.ctime},
            #{item.mtime},
            #{item.delFlag}
            )
        </foreach>
    </insert>

    <update id="updateRoleType">
        update project_stakeholder_member
        set role_type = #{dto.roleType},
            modifier = #{dto.modifier},
            modifier_id = #{dto.modifierId},
            mtime = #{dto.mtime}
        where id = #{dto.id}
    </update>

    <update id="updateRemark">
        update project_stakeholder_member
        set remark = #{dto.remark},
            modifier = #{dto.modifier},
            modifier_id = #{dto.modifierId},
            mtime = #{dto.mtime}
        where id = #{dto.id}
    </update>

    <update id="updateRemarkByUserId">
        update project_stakeholder_member
        set remark = #{dto.remark},
            modifier = #{dto.modifier},
            modifier_id = #{dto.modifierId},
            mtime = #{dto.mtime}
        where member_id = #{dto.memberId}
        and project_id = #{dto.projectId}
        and role_type in (0, 1, 2, 5)
    </update>

    <update id="updateSyncOaTypeByProjectId">
        update project_stakeholder_member
        set sync_oa_type = 1
        where project_id = #{projectId}
        <if test="userIds != null and userIds.size() > 0">
            and member_id in
            <foreach collection="userIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>

    <select id="findSyncedOaUserIdsByProjectId" resultType="java.lang.Long">
        select member_id
        from project_stakeholder_member
        where project_id = #{projectId}
        and sync_oa_type = 1
    </select>

    <select id="findUnSyncOaUserIdsByProjectId" resultType="java.lang.Long">
        select member_id
        from project_stakeholder_member
        where project_id = #{projectId}
        and (sync_oa_type = 0 or sync_oa_type is null)
        and del_flag = 0
    </select>

    <select id="isOperationsAssistant" resultType="java.lang.Boolean">
        select exists(
            select id
            from project_stakeholder_member
            where project_id = #{projectId}
            and member_id = #{userId}
            and role_type = 3)
    </select>

    <select id="findToSyncDeptName" resultType="com.gok.pboot.pms.entity.domain.ProjectStakeholderMember">
        SELECT
        <include refid="baseColumnList"/>
        FROM project_stakeholder_member a
        inner join project_info p ON a.project_id = p.id
        WHERE
        a.del_flag = 0
        AND
        (p.project_status = 0 OR p.project_status = 2)
        group by a.id
    </select>

    <select id="getAddMembersByProject" resultType="com.gok.pboot.pms.entity.domain.ProjectStakeholderMember">
        SELECT
            a.id AS project_id,
            a.salesman_user_id AS member_id,
            a.project_salesperson AS member_name,
            0 AS role_type
        FROM
            project_info a
                LEFT JOIN project_stakeholder_member b ON a.salesman_user_id = b.member_id
                AND a.id = b.project_id
                AND b.role_type = 0
                AND b.del_flag = 0
        WHERE
            a.salesman_user_id IS NOT NULL
          AND b.id IS NULL
        UNION ALL
        SELECT
            a.id AS project_id,
            a.pre_sale_user_id AS member_id,
            a.pre_sale_user_name AS member_name,
            1 AS role_type
        FROM
            project_info a
                LEFT JOIN project_stakeholder_member b ON a.pre_sale_user_id = b.member_id
                AND a.id = b.project_id
                AND b.role_type = 1
                AND b.del_flag = 0
        WHERE
            a.pre_sale_user_id IS NOT NULL
          AND b.id IS NULL
        UNION ALL
        SELECT
            a.id AS project_id,
            a.manager_user_id AS member_id,
            a.manager_user_name AS member_name,
            2 AS role_type
        FROM
            project_info a
                LEFT JOIN project_stakeholder_member b ON a.manager_user_id = b.member_id
                AND a.id = b.project_id
                AND b.role_type = 2
                AND b.del_flag = 0
        WHERE
            a.manager_user_id IS NOT NULL
          AND b.id IS NULL
        UNION ALL
        SELECT
            a.id AS project_id,
            a.business_manager_id AS member_id,
            a.business_manager AS member_name,
            5 AS role_type
        FROM
            project_info a
                LEFT JOIN project_stakeholder_member b ON a.business_manager_id = b.member_id
                AND a.id = b.project_id
                AND b.role_type = 5
                AND b.del_flag = 0
        WHERE
            a.business_manager_id IS NOT NULL
          AND b.id IS NULL
    </select>


    <select id="getDelMembersByProject" resultType="java.lang.Long">
        SELECT
            a.id
        FROM
            project_stakeholder_member a
                LEFT JOIN project_info b ON a.member_id = b.salesman_user_id
                AND a.project_id = b.id
        WHERE
            a.role_type = 0
          AND a.del_flag = 0
          AND b.id IS NULL
        UNION ALL
        SELECT
            a.id
        FROM
            project_stakeholder_member a
                LEFT JOIN project_info b ON a.member_id = b.pre_sale_user_id
                AND a.project_id = b.id
        WHERE
            a.role_type = 1
          AND a.del_flag = 0
          AND b.id IS NULL
        UNION ALL
        SELECT
            a.id
        FROM
            project_stakeholder_member a
                LEFT JOIN project_info b ON a.member_id = b.manager_user_id
                AND a.project_id = b.id
        WHERE
            a.role_type = 2
          AND a.del_flag = 0
          AND b.id IS NULL
        UNION ALL
        SELECT
            a.id
        FROM
            project_stakeholder_member a
                LEFT JOIN project_info b ON a.member_id = b.business_manager_id
                AND a.project_id = b.id
        WHERE
            a.role_type = 5
          AND a.del_flag = 0
          AND b.id IS NULL
    </select>
    <select id="getOperatorRole" resultType="java.lang.Integer">
        SELECT b.role_type
        FROM project_stakeholder_member b
        where b.project_id = #{projectId}
          AND b.member_id = #{id}
          AND del_flag = 0
    </select>

    <select id="getProjectStakeholderMemberVOList"
            resultType="com.gok.pboot.pms.entity.vo.ProjectStakeholderMemberVO">
        SELECT
            pi.id,
            psm.member_id
        FROM project_info pi LEFT JOIN project_stakeholder_member psm ON pi.id = psm.project_id AND psm.del_flag = 0
        WHERE pi.project_status IN (0, 2)
          AND psm.member_id IS NOT NULL
    </select>
</mapper>