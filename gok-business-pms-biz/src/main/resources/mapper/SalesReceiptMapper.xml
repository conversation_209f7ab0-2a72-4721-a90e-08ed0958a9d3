<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.mapper.SalesReceiptMapper">

    <resultMap id="BaseResultMap" type="com.gok.pboot.pms.entity.domain.SalesReceipt">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="projectId" column="project_id" jdbcType="BIGINT"/>
        <result property="projectNo" column="project_no" jdbcType="VARCHAR"/>
        <result property="projectName" column="project_name" jdbcType="VARCHAR"/>
        <result property="projectStatus" column="project_status" jdbcType="VARCHAR"/>
        <result property="contractId" column="contract_id" jdbcType="BIGINT"/>
        <result property="contractName" column="contract_name" jdbcType="VARCHAR"/>
        <result property="contractCode" column="contract_code" jdbcType="VARCHAR"/>
        <result property="signingDate" column="signing_date" jdbcType="VARCHAR"/>
        <result property="contractMoney" column="contract_money" jdbcType="DECIMAL"/>
        <result property="accumulatedAmount" column="accumulated_amount" jdbcType="DECIMAL"/>
        <result property="collectionRatio" column="collection_ratio" jdbcType="VARCHAR"/>
        <result property="currentPaymentMoney" column="current_payment_money" jdbcType="DECIMAL"/>
        <result property="currentPaymentName" column="current_payment_name" jdbcType="VARCHAR"/>
        <result property="collectionDelayDays" column="collection_delay_days" jdbcType="VARCHAR"/>
        <result property="expectedDate" column="expected_date" jdbcType="VARCHAR"/>
        <result property="warningLevel" column="warning_level" jdbcType="VARCHAR"/>
        <result property="customerId" column="customer_id" jdbcType="BIGINT"/>
        <result property="customerName" column="customer_name" jdbcType="VARCHAR"/>
        <result property="salesmanUserId" column="salesman_user_id" jdbcType="BIGINT"/>
        <result property="salesmanUserName" column="salesman_user_name" jdbcType="VARCHAR"/>
        <result property="salesmanLeaderUserId" column="salesman_leader_user_id" jdbcType="BIGINT"/>
        <result property="managerUserId" column="manager_user_id" jdbcType="BIGINT"/>
        <result property="managerUserName" column="manager_user_name" jdbcType="VARCHAR"/>
        <result property="managerLeaderUserId" column="manager_leader_user_id" jdbcType="BIGINT"/>
        <result property="preSaleUserId" column="pre_sale_user_id" jdbcType="BIGINT" />
        <result property="preSaleLeaderUserId" column="pre_sale_leader_user_id" jdbcType="BIGINT" />
        <result property="headUserId" column="head_user_id" jdbcType="BIGINT"/>
        <result property="headUserName" column="head_user_name" jdbcType="VARCHAR"/>
        <result property="commissionerUserId" column="commissioner_user_id" jdbcType="BIGINT"/>
        <result property="commissionerUserName" column="commissioner_user_name" jdbcType="VARCHAR"/>
        <result property="attributableSubject" column="attributable_subject" jdbcType="VARCHAR"/>
        <result property="firstDepartmentId" column="first_department_id" jdbcType="BIGINT"/>
        <result property="firstDepartment" column="first_department" jdbcType="VARCHAR"/>
        <result property="secondDepartmentId" column="second_department_id" jdbcType="BIGINT"/>
        <result property="secondDepartment" column="second_department" jdbcType="VARCHAR"/>
        <result property="remarks" column="remarks" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
        <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
    </resultMap>

    <select id="querySalesReceiptPage" resultType="com.gok.pboot.pms.entity.domain.SalesReceipt">
        SELECT
        ID,
        project_id,
        customer_id,
        project_no,
        project_name,
        contract_code,
        project_status,
        contract_money,
        accumulated_amount,
        collection_ratio,
        current_payment_money,
        current_payment_name,
        collection_delay_days,
        expected_date,
        warning_level,
        salesman_user_name,
        customer_name,
        signing_date,
        attributable_subject,
        first_department,
        second_department,
        contract_id,
        contract_Name,
        manager_user_name
        FROM sales_receipt
        <where>
            del_flag = 0
            AND (contract_money IS NOT NULL AND contract_money != 0 AND contract_money != 0.0 AND contract_money != 0.00)
            AND contract_code IS NOT NULL
            <if test="dto.projectNameOrNo!=null and dto.projectNameOrNo!=''">
                AND
                (project_name like CONCAT('%',#{dto.projectNameOrNo},'%')
                OR
                project_no like CONCAT('%',#{dto.projectNameOrNo},'%'))
            </if>
            <if test="dto.contractNameOrNo!=null and dto.contractNameOrNo!=''">
                AND
                (contract_name like CONCAT('%',#{dto.contractNameOrNo},'%')
                OR
                contract_code like CONCAT('%',#{dto.contractNameOrNo},'%'))
            </if>
            <if test="dto.projectStatus!=null and dto.projectStatus!=''">
                AND
                project_status=#{dto.projectStatus}
            </if>
            <if test="dto.contractMoneyLow!=null">
                AND
                contract_money >= #{dto.contractMoneyLow}
            </if>
            <if test="dto.contractMoneyUp!=null">
                AND
                #{dto.contractMoneyUp} >= contract_money
            </if>
            <if test="dto.currentPaymentName!=null and dto.currentPaymentName!=''">
                AND
                current_payment_name=#{dto.currentPaymentName}
            </if>
            <if test="dto.warningLevel!=null">
                AND
                warning_level like #{dto.warningLevel}
            </if>
            <if test="dto.salesmanOrManageUserName!=null and dto.salesmanOrManageUserName!=''">
                AND
                CONCAT(salesman_user_name,manager_user_name) like CONCAT('%',#{dto.salesmanOrManageUserName},'%')
            </if>
            <if test="dto.authority">
                AND (salesman_user_id IN
                <foreach collection="dto.userId" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                OR manager_user_id IN
                <foreach collection="dto.userId" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                OR pre_sale_user_id IN
                <foreach collection="dto.userId" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                OR salesman_leader_user_id IN
                <foreach collection="dto.userId" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                OR manager_leader_user_id IN
                <foreach collection="dto.userId" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                OR pre_sale_leader_user_id IN
                <foreach collection="dto.userId" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                <if test="dto.authDeptIdList != null and dto.authDeptIdList.size() > 0">
                    OR first_department_id IN
                    <foreach collection="dto.authDeptIdList" item="item" index="index" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                    OR second_department_id IN
                    <foreach collection="dto.authDeptIdList" separator="," item="item" open="(" close=")">
                        #{item}
                    </foreach>
                </if>
                )
            </if>
            <if test="dto.attributableSubject != null and dto.attributableSubject != ''">
                AND attributable_subject = #{dto.attributableSubject}
            </if>
            <if test="dto.deptIdList != null and dto.deptIdList.size() > 0">
                AND (first_department_id IN
                <foreach collection="dto.deptIdList" separator="," open="(" close=")" item="item">
                    #{item}
                </foreach>
                OR second_department_id IN
                <foreach collection="dto.deptIdList" separator="," item="item" open="(" close=")">
                    #{item}
                </foreach>)
            </if>
        </where>
        ORDER BY CAST(collection_delay_days AS DECIMAL) DESC
    </select>

    <select id="currentPaymentNameBox" resultType="java.lang.String">
        SELECT DISTINCT current_payment_name
        FROM sales_receipt
    </select>

    <select id="projectStatusBox" resultType="java.lang.String">
        SELECT DISTINCT project_status
        FROM sales_receipt
    </select>

    <select id="querySalesReceiptList" resultType="com.gok.pboot.pms.entity.domain.SalesReceipt">
        SELECT
        ID,
        project_no,
        project_name,
        contract_code,
        project_status,
        contract_money,
        accumulated_amount,
        collection_ratio,
        current_payment_money,
        current_payment_name,
        collection_delay_days,
        expected_date,
        warning_level,
        salesman_user_name,
        customer_name,
        signing_date,
        attributable_subject,
        first_department,
        second_department,
        contract_id,
        contract_Name,
        manager_user_name
        FROM sales_receipt
        <where>
            del_flag = 0
            AND (contract_money IS NOT NULL AND contract_money != 0 AND contract_money != 0.0 AND contract_money != 0.00)
            AND contract_code IS NOT NULL
            <if test="dto.projectNameOrNo!=null and dto.projectNameOrNo!=''">
                AND
                (project_name like CONCAT('%',#{dto.projectNameOrNo},'%')
                OR
                project_no like CONCAT('%',#{dto.projectNameOrNo},'%'))
            </if>
            <if test="dto.contractNameOrNo!=null and dto.contractNameOrNo!=''">
                AND
                (contract_name like CONCAT('%',#{dto.contractNameOrNo},'%')
                OR
                contract_code like CONCAT('%',#{dto.contractNameOrNo},'%'))
            </if>
            <if test="dto.projectStatus!=null and dto.projectStatus!=''">
                AND
                project_status=#{dto.projectStatus}
            </if>
            <if test="dto.contractMoneyLow!=null">
                AND
                contract_money >= #{dto.contractMoneyLow}
            </if>
            <if test="dto.contractMoneyUp!=null">
                AND
                #{dto.contractMoneyUp} >= contract_money
            </if>
            <if test="dto.currentPaymentName!=null and dto.currentPaymentName!=''">
                AND
                current_payment_name=#{dto.currentPaymentName}
            </if>
            <if test="dto.warningLevel!=null">
                AND
                warning_level like #{dto.warningLevel}
            </if>
            <if test="dto.salesmanOrManageUserName!=null and dto.salesmanOrManageUserName!=''">
                AND
                CONCAT(salesman_user_name,manager_user_name) like CONCAT('%',#{dto.salesmanOrManageUserName},'%')
            </if>
            <if test="dto.authority">
                AND (salesman_user_id IN
                <foreach collection="dto.userId" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                OR manager_user_id IN
                <foreach collection="dto.userId" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                OR pre_sale_user_id IN
                <foreach collection="dto.userId" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                OR salesman_leader_user_id IN
                <foreach collection="dto.userId" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                OR manager_leader_user_id IN
                <foreach collection="dto.userId" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                OR pre_sale_leader_user_id IN
                <foreach collection="dto.userId" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                <if test="dto.authDeptIdList != null and dto.authDeptIdList.size() > 0">
                    OR first_department_id IN
                    <foreach collection="dto.authDeptIdList" item="item" index="index" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                    OR second_department_id IN
                    <foreach collection="dto.authDeptIdList" separator="," item="item" open="(" close=")">
                        #{item}
                    </foreach>
                </if>
                )
            </if>
            <if test="dto.attributableSubject != null and dto.attributableSubject != ''">
                AND attributable_subject = #{dto.attributableSubject}
            </if>
            <if test="dto.deptIdList != null and dto.deptIdList.size() > 0">
                AND (first_department_id IN
                <foreach collection="dto.deptIdList" separator="," open="(" close=")" item="item">
                    #{item}
                </foreach>
                OR second_department_id IN
                <foreach collection="dto.deptIdList" separator="," item="item" open="(" close=")">
                    #{item}
                </foreach>)
            </if>
        </where>
    </select>

    <select id="selectVoById" resultType="com.gok.pboot.pms.entity.vo.SalesReceiptInDetailVO">
        SELECT
            project_no,
            project_name,
            project_status,
            customer_name,
            contract_code,
            signing_date,
            contract_money,
            attributable_subject,
            first_department,
            salesman_user_name,
            manager_user_name
        FROM
            sales_receipt
        WHERE
            id =#{id}
        AND del_flag = 0
    </select>

    <select id="queryPushVo" resultType="com.gok.pboot.pms.entity.vo.SalesReceiptPushVO">
        SELECT
            sr.id,
            sr.salesman_user_id,
            sr.salesman_user_name,
            sr.contract_id,
            sr.contract_code,
            srd.payment_name,
            srd.expected_date,
            sr.first_department_id,
            sr.contract_money
        FROM
            sales_receipt AS sr
                RIGHT JOIN sales_receipt_details AS srd ON srd.main_id = sr.contract_id
        WHERE
                sr.del_flag = 0
          AND srd.del_flag = 0
          AND srd.payment_status =1
          AND srd.expected_date IS NOT NULL
          and srd.expected_date >= DATE_FORMAT('2024-01-01', '%Y-%m-%d')
    </select>
</mapper>
