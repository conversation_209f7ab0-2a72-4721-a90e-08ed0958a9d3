<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.cost.mapper.CostDeliverPurchasePlanMapper">
  <resultMap id="BaseResultMap" type="com.gok.pboot.pms.cost.entity.domain.CostDeliverPurchasePlan">
    <!--@mbg.generated-->
    <!--@Table cost_deliver_purchase_plan-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="purchase_item" jdbcType="VARCHAR" property="purchaseItem" />
    <result column="sale_contract_id" jdbcType="BIGINT" property="saleContractId" />
    <result column="sale_contract_name" jdbcType="VARCHAR" property="saleContractName" />
    <result column="purchase_contract_id" jdbcType="BIGINT" property="purchaseContractId" />
    <result column="purchase_contract_name" jdbcType="VARCHAR" property="purchaseContractName" />
    <result column="account_id" jdbcType="BIGINT" property="accountId" />
    <result column="account_oa_id" jdbcType="BIGINT" property="accountOaId" />
    <result column="account_name" jdbcType="VARCHAR" property="accountName" />
    <result column="budget_amount_included_tax" jdbcType="DECIMAL" property="budgetAmountIncludedTax" />
    <result column="planned_delivery_date" jdbcType="DATE" property="plannedDeliveryDate" />
    <result column="plan_status" jdbcType="VARCHAR" property="planStatus" />
    <result column="actual_delivery_date" jdbcType="DATE" property="actualDeliveryDate" />
    <result column="acceptance_conditions_status_ids" jdbcType="VARCHAR" property="acceptanceConditionsStatusIds" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="creator_id" jdbcType="BIGINT" property="creatorId" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="modifier_id" jdbcType="BIGINT" property="modifierId" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, project_id, purchase_item, sale_contract_id, sale_contract_name, purchase_contract_id, 
    purchase_contract_name, account_id, account_name, budget_amount_included_tax, planned_delivery_date,
    plan_status, actual_delivery_date, acceptance_conditions_status_ids, creator, creator_id, 
    modifier, modifier_id, ctime, mtime, del_flag
  </sql>

    <delete id="delById">
        update cost_deliver_purchase_plan
        set del_flag = 1
        where del_flag = '0'
          and id = #{id}
    </delete>

</mapper>