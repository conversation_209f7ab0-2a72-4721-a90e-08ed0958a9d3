<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.mapper.ContractMilestoneMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.gok.pboot.pms.entity.domain.ContractMilestone">
        <id column="id" property="id"/>
        <result column="contract_id" property="contractId"/>
        <result column="milestone" property="milestone"/>
        <result column="payment_name_id" property="paymentNameId"/>
        <result column="payment_name" property="paymentName"/>
        <result column="expected_complete_date" property="expectedCompleteDate"/>
        <result column="actual_complete_date" property="actualCompleteDate"/>
        <result column="estimated_amount_include_tax" property="estimatedAmountIncludeTax"/>
        <result column="estimated_amount" property="estimatedAmount"/>
        <result column="estimated_tax_rate" property="estimatedTaxRate"/>
        <result column="planned_amount_include_tax" property="plannedAmountIncludeTax"/>
        <result column="planned_amount," property="plannedAmount"/>
        <result column="planned_tax_rate" property="plannedTaxRate"/>
        <result column="payment_period_date" property="paymentPeriodDate"/>
        <result column="actual_payment_amount" property="actualPaymentAmount"/>
        <result column="actual_payment_date" property="actualPaymentDate"/>
        <result column="milestone_condition" property="milestoneCondition"/>
        <result column="milestone_evidence" property="milestoneEvidence"/>
        <result column="settlement_attachment" property="settlementAttachment"/>
        <result column="amount_difference" property="amountDifference"/>
        <result column="milestone_status" property="milestoneStatus"/>
        <result column="creator" property="creator"/>
        <result column="creator_id" property="creatorId"/>
        <result column="modifier" property="modifier"/>
        <result column="modifier_id" property="modifierId"/>
        <result column="ctime" property="ctime"/>
        <result column="mtime" property="mtime"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, contract_id, milestone, payment_name_id, payment_name,expected_complete_date, milestone_status,
        actual_complete_date, estimated_amount_include_tax, estimated_tax_rate,
        estimated_amount, planned_amount_include_tax, planned_amount, planned_tax_rate,
        payment_period_date, actual_payment_amount, actual_payment_date, amount_difference,
        milestone_condition, milestone_evidence, creator, creator_id, modifier, modifier_id,
        settlement_attachment, ctime, mtime, del_flag
    </sql>

    <select id="findByContractIds" resultType="com.gok.pboot.pms.entity.vo.ContractMilestoneVO">
        SELECT
            id,
            contract_id,
            milestone,
            payment_name_id,
            payment_name,
            expected_complete_date,
            actual_complete_date,
            estimated_amount_include_tax,
            planned_amount_include_tax,
            planned_amount,
            planned_tax_rate,
            actual_payment_amount,
            actual_payment_date,
            payment_period_date,
            milestone_condition,
            milestone_evidence,
            settlement_attachment,
            estimated_tax_rate,
            estimated_amount,
            bad_debt_process_number,
            bad_debt_amount,
            bad_debt_filing_time,
            amount_difference,
            milestone_status
        FROM
            contract_milestone
        WHERE
            del_flag = ${@<EMAIL>()}
        <if test="contractIds != null and contractIds.size() > 0">
            AND contract_id IN
            <foreach collection="contractIds" item="contractId" open="(" separator="," close=")">
                #{contractId}
            </foreach>
        </if>
        ORDER BY
            id
    </select>

</mapper>