<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.mapper.PmsContractProcessInfoMapper">
  <resultMap id="BaseResultMap" type="com.gok.pboot.pms.entity.domain.PmsContractProcessInfo">
    <!--@mbg.generated-->
    <!--@Table pms_contract_process_info-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="request_id" jdbcType="BIGINT" property="requestId" />
    <result column="requestmark" jdbcType="VARCHAR" property="requestmark" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="contract_id" jdbcType="BIGINT" property="contractId" />
    <result column="applicat_id" jdbcType="BIGINT" property="applicatId" />
    <result column="applicat" jdbcType="VARCHAR" property="applicat" />
    <result column="applicant_dept_id" jdbcType="BIGINT" property="applicantDeptId" />
    <result column="applicant_dept" jdbcType="VARCHAR" property="applicantDept" />
    <result column="process_type" jdbcType="INTEGER" property="processType" />
    <result column="process_type_name" jdbcType="VARCHAR" property="processTypeName" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="filing_date_time" jdbcType="TIMESTAMP" property="filingDateTime" />
    <result column="srze" jdbcType="VARCHAR" property="srze" />
    <result column="htje" jdbcType="VARCHAR" property="htje" />
    <result column="kpje" jdbcType="VARCHAR" property="kpje" />
    <result column="zfje" jdbcType="VARCHAR" property="zfje" />
    <result column="srje" jdbcType="VARCHAR" property="srje" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="creator_id" jdbcType="BIGINT" property="creatorId" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="modifier_id" jdbcType="BIGINT" property="modifierId" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="del_flag" jdbcType="BOOLEAN" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, request_id, requestmark, project_id, contract_id, applicat_id, applicat, applicant_dept_id, 
    applicant_dept, process_type, process_type_name, `status`, `name`, filing_date_time, 
    srze, htje, kpje, zfje, srje, creator, creator_id, modifier, modifier_id, ctime, 
    mtime, del_flag
  </sql>

  <select id="selByContractIdAndProjectId" resultType="com.gok.pboot.pms.entity.vo.ContractProcessInfoVO">
      select
             request_id,
             requestmark,
             project_id,
             contract_id,
             applicat_id,
             applicat,
             applicant_dept_id,
             applicant_dept,
             process_type,
             process_type_name,
             status,
             name,
             filing_date_time,
             srze,
             htje,
             kpje,
             zfje,
             srje
      from pms_contract_process_info
              where del_flag = '0'
      <if test="contractId != null and projectId == null">
          AND contract_id = #{contractId}
      </if>
      <if test="contractId != null and projectId != null">
          AND (contract_id = #{contractId} OR project_id = #{projectId})
      </if>
      ORDER BY filing_date_time DESC
  </select>

</mapper>