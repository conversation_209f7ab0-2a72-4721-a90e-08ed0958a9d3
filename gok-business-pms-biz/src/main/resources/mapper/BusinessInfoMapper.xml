<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.gok.pboot.pms.mapper.BusinessInfoMapper">

    <sql id="baseColumnList">
        a.id,
        a.business_name,
        a.item_no,
        a.first_level_department_id,
        a.first_level_department,
        a.department_id,
        a.department,
        a.is_not_internal_project,
        a.project_location,
        a.end_customer_name,
        a.end_customer_grade,
        a.end_customer_industry,
        a.contract_customer_name,
        a.contract_customer_grade,
        a.is_not_need_bidding,
        a.bidding_method,
        a.contract_entity,
        a.business_module,
        a.income_type,
        a.technology_type,
        a.settlement_method,
        a.delivery_method,
        a.key_decision_chain,
        a.support_from_key_decision,
        a.project_requirement_clear,
        a.project_requirement,
        a.budget_situation,
        a.expected_order_amount,
        a.ygmll,
        a.expected_complete_time,
        a.competition_situation,
        a.business_stage,
        a.business_milestone,
        a.internal_project_type,
        a.is_external_procurement,
        a.purchasing_categories,
        a.business_status,
        a.pre_sale_hours,
        a.after_sale_hours,
        a.student_hours,
        a.pre_sale_invest_amount,
        a.after_sale_invest_amount,
        a.salesman_user_id,
        a.project_salesperson,
        a.pre_sale_user_id,
        a.pre_sale_user_name,
        a.manager_user_id,
        a.manager_user_name,
        a.business_ctime,
        a.business_mtime,
        a.transaction_date,
        a.creator,
        a.creator_id,
        a.modifier,
        a.modifier_id,
        a.ctime,
        a.mtime,
        a.del_flag
    </sql>
    <sql id="voColumnList">
        a.id,
        a.project_id,
        a.business_name,
        a.item_no,
        a.first_level_department_id AS 'firstDepartmentId',
        a.first_level_department AS 'firstDepartment',
        a.department_id,
        a.department,
        a.is_not_internal_project,
        a.project_location,
        a.end_customer_name,
        a.end_customer_grade,
        a.end_customer_industry,
        a.contract_customer_name,
        a.contract_customer_grade,
        a.is_not_need_bidding,
        a.bidding_method,
        a.contract_entity,
        a.business_module,
        a.income_type,
        a.technology_type,
        a.settlement_method,
        a.delivery_method,
        a.key_decision_chain,
        a.support_from_key_decision,
        a.project_requirement_clear,
        a.project_requirement,
        a.budget_situation,
        a.expected_order_amount,
        a.ygmll,
        a.expected_complete_time,
        a.competition_situation,
        a.business_stage,
        a.business_milestone,
        a.internal_project_type,
        a.is_external_procurement,
        a.purchasing_categories,
        a.business_status,
        a.pre_sale_hours,
        a.after_sale_hours,
        a.student_hours,
        a.pre_sale_invest_amount,
        a.after_sale_invest_amount,
        a.salesman_user_id,
        a.project_salesperson,
        a.pre_sale_user_id,
        a.pre_sale_user_name,
        a.manager_user_id,
        a.manager_user_name,
        a.business_ctime,
        IFNULL(a.business_mtime, a.business_ctime) AS 'business_mtime',
        a.transaction_date,
        a.business_id,
        a.unit_id
    </sql>
    <sql id="progressVOColumnList">
        a.id,
        a.business_id,
        a.business_name,
        a.customer_name,
        a.business_progress,
        a.next_step_forward_plan,
        a.next_step_plan_ftime,
        a.yddwtjsxdzc,
        a.support_from_key_decision,
        a.project_requirement_clear,
        a.budget_situation,
        a.expected_order_amount,
        a.expected_complete_time,
        a.business_stage,
        a.business_milestone,
        a.business_status,
        a.salesman_user_id,
        a.project_salesperson,
        a.submit_user_id,
        a.submit_user,
        a.submit_user_avatar,
        a.submit_user_dept_id,
        a.submit_user_dept,
        a.submit_time,
        a.requestid as requestId,
        a.nodeoperator,
        a.archive_time
    </sql>

    <select id="findBusinessInfoPage" resultType="com.gok.pboot.pms.entity.domain.BusinessInfo">
        SELECT
        <include refid="baseColumnList"/>
        FROM business_info a
        WHERE a.del_flag = 0
        <if test="filter.keyword != null and filter.keyword != ''">
            AND (a.business_name LIKE CONCAT('%', #{filter.keyword}, '%')
            OR a.end_customer_name LIKE CONCAT('%', #{filter.keyword}, '%')
            OR a.contract_customer_name LIKE CONCAT('%', #{filter.keyword}, '%'))
        </if>
        <if test="filter.departmentIds != null and filter.departmentIds.size() > 0">
            AND (
            a.department_id IN
            <foreach collection="filter.departmentIds" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
            OR
            a.first_level_department_id IN
            <foreach collection="filter.departmentIds" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
            )
        </if>
        <if test="filter.businessStage != null">
            AND a.business_stage = #{filter.businessStage}
        </if>
        <if test="filter.businessMilestone != null">
            AND a.business_milestone = #{filter.businessMilestone}
        </if>
        <if test="filter.internalProjectType != null">
            AND a.internal_project_type = #{filter.internalProjectType}
        </if>
        <if test="filter.isExternalProcurement != null">
            AND a.is_external_procurement = #{filter.isExternalProcurement}
        </if>
        <if test="filter.purchasingCategories != null">
            AND a.purchasing_categories = #{filter.purchasingCategories}
        </if>
        <if test="filter.customerGrade != null">
            AND (a.end_customer_grade = #{filter.customerGrade}
            OR a.contract_customer_grade = #{filter.customerGrade})
        </if>
        <if test="filter.isNotInternalProject != null">
            AND a.is_not_internal_project = #{filter.isNotInternalProject}
        </if>
        <if test="filter.memberId != null">
            AND (a.salesman_user_id = #{filter.memberId}
            OR a.pre_sale_user_id = #{filter.memberId}
            OR a.manager_user_id = #{filter.memberId})
        </if>
        <if test="filter.projectLocation != null and filter.projectLocation != ''">
            AND a.project_location = #{filter.projectLocation}
        </if>
        <if test="filter.customerIndustry != null and filter.customerIndustry != ''">
            AND a.end_customer_industry = #{filter.customerIndustry}
        </if>
        <if test="filter.businessModule != null">
            AND a.business_module = #{filter.businessModule}
        </if>
        <if test="filter.isNotNeedBidding != null">
            AND a.is_not_need_bidding = #{filter.isNotNeedBidding}
        </if>
        <if test="filter.contractEntity != null and filter.contractEntity != ''">
            AND a.contract_entity = #{filter.contractEntity}
        </if>
        <if test="filter.incomeType != null">
            AND a.income_type = #{filter.incomeType}
        </if>
        <if test="filter.technologyType != null">
            AND a.technology_type = #{filter.technologyType}
        </if>
        <if test="filter.settlementMethod != null">
            AND a.settlement_method = #{filter.settlementMethod}
        </if>
        <if test="filter.deliveryMethod != null">
            AND a.delivery_method = #{filter.deliveryMethod}
        </if>
        <if test="filter.supportFromKeyDecision != null">
            AND a.support_from_key_decision = #{filter.supportFromKeyDecision}
        </if>
        <if test="filter.projectRequirementClear != null">
            AND a.project_requirement_clear = #{filter.projectRequirementClear}
        </if>
        <if test="filter.budgetSituation != null">
            AND a.budget_situation = #{filter.budgetSituation}
        </if>
        <if test="filter.isBasedOnUpdateTime == 0">
            ORDER BY CAST(a.business_ctime AS DATETIME) DESC
        </if>
        <if test="filter.isBasedOnUpdateTime == 1">
            ORDER BY CAST(a.business_mtime AS DATETIME) DESC
        </if>
    </select>

    <select id="findBusinessInfoVOPage" resultType="com.gok.pboot.pms.entity.vo.BusinessInfoVO">
        SELECT
        <include refid="voColumnList"/>,
        IFNULL(a.business_mtime, a.business_ctime) AS 'business_mtime_for_sort',
        b.pre_sales_labor_input AS 'preSalesLaborInput',
        b.sqfytr AS 'sqfytr',
        c.name AS 'businessUnitName',
        d.unit_name AS 'unitName'
        FROM business_info a
        LEFT JOIN project_data b ON a.project_id = b.id
        LEFT JOIN customer_business c ON a.business_id =c.id
        LEFT JOIN customer_business_unit d ON a.unit_id =d.id
        WHERE a.del_flag = 0
        AND a.item_no IS NOT NULL
        AND a.is_not_internal_project != 1
        <if test="filter.scope == null and filter.businessIdsInDataScope != null and filter.businessIdsInDataScope.size() > 0">
            and a.id in
            <foreach collection="filter.businessIdsInDataScope" item="bId" open="(" separator="," close=")">
                #{bId}
            </foreach>
        </if>
        <if test="filter.keyword != null and filter.keyword != ''">
            AND (a.business_name LIKE CONCAT('%', #{filter.keyword}, '%')
            OR a.end_customer_name LIKE CONCAT('%', #{filter.keyword}, '%')
            OR a.contract_customer_name LIKE CONCAT('%', #{filter.keyword}, '%'))
        </if>
        <if test="filter.departmentIds != null and filter.departmentIds.size() > 0">
            AND (
            a.department_id IN
            <foreach collection="filter.departmentIds" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
            OR
            a.first_level_department_id IN
            <foreach collection="filter.departmentIds" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
            )
        </if>
        <if test="filter.businessStage != null and filter.businessStage.size() > 0">
            AND a.business_stage IN
            <foreach collection="filter.businessStage" item="businessStage" separator="," open="(" close=")">
                #{businessStage}
            </foreach>
        </if>
        <if test="filter.businessId != null">
            AND a.business_id = #{filter.businessId}
        </if>
        <if test="filter.unitId != null and filter.unitId.size() > 0">
            AND a.unit_id IN
            <foreach collection="filter.unitId" item="unitId" open="(" separator="," close=")">
                #{unitId}
            </foreach>
        </if>
        <if test="filter.businessMilestone != null">
            AND a.business_milestone = #{filter.businessMilestone}
        </if>
        <if test="filter.isExternalProcurement != null and filter.isExternalProcurement != ''">
            AND a.is_external_procurement = #{filter.isExternalProcurement}
        </if>
        <if test="filter.purchasingCategories != null and filter.purchasingCategories != ''">
            AND a.purchasing_categories = #{filter.purchasingCategories}
        </if>
        <if test="filter.customerGrade != null">
            AND (a.end_customer_grade = #{filter.customerGrade}
            OR a.contract_customer_grade = #{filter.customerGrade})
        </if>
        <if test="filter.memberId != null">
            AND (a.salesman_user_id = #{filter.memberId}
            OR a.pre_sale_user_id = #{filter.memberId}
            OR a.manager_user_id = #{filter.memberId})
        </if>
        <if test="filter.projectLocation != null and filter.projectLocation != ''">
            AND a.project_location LIKE CONCAT('%', #{filter.projectLocation}, '%')
        </if>
        <if test="filter.customerIndustry != null and filter.customerIndustry != ''">
            AND a.end_customer_industry = #{filter.customerIndustry}
        </if>
        <if test="filter.businessModule != null">
            AND a.business_module = #{filter.businessModule}
        </if>
        <if test="filter.isNotNeedBidding != null">
            AND a.is_not_need_bidding = #{filter.isNotNeedBidding}
        </if>
        <if test="filter.contractEntity != null and filter.contractEntity != ''">
            AND a.contract_entity = #{filter.contractEntity}
        </if>
        <if test="filter.incomeType != null">
            AND a.income_type = #{filter.incomeType}
        </if>
        <if test="filter.technologyType != null">
            AND a.technology_type = #{filter.technologyType}
        </if>
        <if test="filter.settlementMethod != null">
            AND a.settlement_method = #{filter.settlementMethod}
        </if>
        <if test="filter.deliveryMethod != null">
            AND a.delivery_method = #{filter.deliveryMethod}
        </if>
        <if test="filter.supportFromKeyDecision != null">
            AND a.support_from_key_decision = #{filter.supportFromKeyDecision}
        </if>
        <if test="filter.projectRequirementClear != null">
            AND a.project_requirement_clear = #{filter.projectRequirementClear}
        </if>
        <if test="filter.budgetSituation != null">
            AND a.budget_situation = #{filter.budgetSituation}
        </if>
        <if test="filter.businessStatus != null">
            <if test="filter.businessStatus == 50">
                AND a.business_status NOT IN ('0', '1')
            </if>
            <if test="filter.businessStatus != 50">
                AND a.business_status = #{filter.businessStatus}
            </if>
        </if>
        <if test="filter.businessCtimeSort != null and filter.businessMtimeSort == null">
            ORDER BY
            IF(ISNULL(a.business_ctime), 1, 0),
            a.business_ctime
            <if test="filter.businessCtimeSort == 1">
                DESC
            </if>
        </if>
        <if test="filter.businessCtimeSort == null and filter.businessMtimeSort != null">
            ORDER BY
            IF(ISNULL(business_mtime_for_sort), 1, 0),
            business_mtime_for_sort
            <if test="filter.businessMtimeSort == 1">
                DESC
            </if>
        </if>
        <if test="filter.businessCtimeSort != null and filter.businessMtimeSort != null">
            ORDER BY
            IF(ISNULL(a.business_ctime), 1, 0),
            a.business_ctime
            <if test="filter.businessCtimeSort == 1">
                DESC
            </if>,
            IF(ISNULL(business_mtime_for_sort), 1, 0),
            business_mtime_for_sort
            <if test="filter.businessMtimeSort == 1">
                DESC
            </if>
        </if>
        <if test="filter.businessCtimeSort == null and filter.businessMtimeSort == null">
            ORDER BY IF(ISNULL(a.business_ctime), 1, 0), a.business_ctime DESC
        </if>
    </select>

    <select id="findOne" resultType="com.gok.pboot.pms.entity.vo.BusinessInfoVO">
        SELECT
        <include refid="voColumnList"/>,
        b.pre_sales_labor_input AS 'preSalesLaborInput',
        b.sqfytr AS 'sqfytr',
        c.name AS 'businessUnitName',
        d.unit_name AS 'unitName'
        FROM business_info a
        LEFT JOIN project_data b ON a.project_id = b.id
        LEFT JOIN customer_business c ON a.business_id =c.id
        LEFT JOIN customer_business_unit d ON a.unit_id =d.id
        WHERE a.del_flag = 0
        AND a.id = #{id}
    </select>

    <select id="findContact" resultType="com.gok.pboot.pms.entity.domain.BusinessContact">
        SELECT business_id,
               contact_user_id,
               contact_user_name,
               contact_way,
               contact_user_type
        FROM business_contact
        WHERE del_flag = 0
          AND business_id = #{businessId};
    </select>

    <select id="getContactCount" resultType="com.gok.pboot.pms.entity.vo.BusinessOfContactCountVo">
        SELECT business_id, COUNT(*) AS contact_count
        FROM business_contact
        WHERE del_flag = 0
        <if test="businessIds != null and businessIds.size() > 0">
            AND business_id IN
            <foreach collection="businessIds" item="businessId" open="(" separator="," close=")">
                #{businessId}
            </foreach>
        </if>
        GROUP BY business_id
    </select>

    <select id="findAllProjectLocation" resultType="java.lang.String">
        SELECT project_location
        FROM (SELECT project_location, COUNT(*) AS location_count
              FROM business_info
              WHERE project_location IS NOT NULL
              GROUP BY project_location) AS location_counts
        ORDER BY location_count DESC;
    </select>

    <select id="findProgressGroupByBusinessId" resultType="com.gok.pboot.pms.entity.vo.BusinessProgressVO">
        SELECT
        <include refid="progressVOColumnList"/>
        FROM business_progress_info a
        WHERE del_flag = 0
        AND business_id = #{businessId}
        ORDER BY CAST(a.submit_time AS DATETIME) DESC
    </select>

    <select id="findDataLog" resultType="com.gok.pboot.pms.entity.vo.BusinessDataLogVO">
        SELECT id,
        business_id,
        data_change_record,
        data_change_user_id,
        data_change_user_name,
        data_change_user_dept_id,
        data_change_user_dept,
        data_change_time
        FROM business_data_log
        WHERE del_flag = 0
        AND (
        business_id = #{id}
        <if test="projectId != null">
            OR business_id = #{projectId}
        </if>
        )
        ORDER BY CAST(data_change_time AS DATETIME) DESC
    </select>

    <select id="findBusinessProgressPage" resultType="com.gok.pboot.pms.entity.vo.BusinessProgressVO">
        SELECT
        <include refid="progressVOColumnList"/>
        FROM business_progress_info a
        WHERE del_flag = 0 AND a.business_id IS NOT NULL AND a.business_name IS NOT NULL
        <if test="filter.keyword != null and filter.keyword != ''">
            AND (a.business_name LIKE CONCAT('%', #{filter.keyword}, '%')
            OR a.customer_name LIKE CONCAT('%', #{filter.keyword}, '%'))
        </if>
        <if test="filter.projectSalesperson != null and filter.projectSalesperson != ''">
            AND a.project_salesperson LIKE CONCAT('%', #{filter.projectSalesperson}, '%')
        </if>
        <if test="filter.startTime != null">
            AND CAST(a.submit_time AS DATETIME) &gt;= #{filter.startTime}
        </if>
        <if test="filter.endTime != null">
            AND CAST(a.submit_time AS DATETIME) &lt;= #{filter.endTime}
        </if>
        ORDER BY CAST(a.submit_time AS DATETIME) DESC,a.id DESC
    </select>

    <select id="findOneProgress" resultType="com.gok.pboot.pms.entity.vo.BusinessProgressDetailsVO">
        SELECT
        <include refid="progressVOColumnList"/>
        FROM business_progress_info a
        WHERE del_flag = 0 AND a.id = #{id}
    </select>

    <select id="findNewProgressByBusinessIds" resultType="com.gok.pboot.pms.entity.vo.BusinessProgressVO">
        SELECT
        <include refid="progressVOColumnList"/>
        FROM business_progress_info a
        INNER JOIN (
        SELECT business_id, MAX(CAST(submit_time AS DATETIME)) AS latest_submit_time
        FROM business_progress_info
        WHERE del_flag = 0
        GROUP BY business_id
        ) b ON a.business_id = b.business_id AND a.submit_time = b.latest_submit_time
        WHERE del_flag = 0
        <if test="businessIds != null and businessIds.size() > 0">
            AND a.business_id IN
            <foreach collection="businessIds" item="businessId" open="(" separator="," close=")">
                #{businessId}
            </foreach>
        </if>
    </select>

    <select id="findOneByProjectId" resultType="com.gok.pboot.pms.entity.vo.BusinessInfoVO">
        SELECT
        <include refid="voColumnList"/>
        FROM business_info a
        WHERE a.del_flag = 0
        AND a.project_id = #{projectId}
    </select>

    <select id="findIds" resultType="java.lang.Long">
        SELECT
        a.id
        FROM business_info a
        <where>
            <if test='filter.scope == "none"'>
                1 = 0
            </if>
            <if test='filter.scope != "all"'>
                <if test="filter.userIdList == null or filter.userIdList.size() == 0">
                    AND 1 = 0
                </if>
                <if test="filter.userIdList != null and filter.userIdList.size() > 0">
                    AND
                    (
                    a.salesman_user_id IN
                    <foreach collection="filter.userIdList" item="uid" open="(" separator="," close=")">
                        #{uid}
                    </foreach>
                    OR
                    a.pre_sale_user_id IN
                    <foreach collection="filter.userIdList" item="uid" open="(" separator="," close=")">
                        #{uid}
                    </foreach>
                    OR
                    a.manager_user_id IN
                    <foreach collection="filter.userIdList" item="uid" open="(" separator="," close=")">
                        #{uid}
                    </foreach>
                    )
                </if>
            </if>
        </where>
    </select>

    <select id="findBusinessProgressByRequestId" resultType="com.gok.pboot.pms.entity.vo.BusinessProgressVO">
        SELECT
        <include refid="progressVOColumnList"/>
        FROM business_progress_info a
        WHERE del_flag = 0 AND a.requestid = #{requestId} limit 1
    </select>

    <select id="findBusinessIdsByProgress" resultType="java.lang.Long">
        SELECT
        a.id
        FROM business_progress_info a
        <where>
            <if test='filter.scope == "none"'>
                1 = 0
            </if>
            <if test='filter.scope != "all"'>
                <if test="filter.userIdList == null or filter.userIdList.size() == 0">
                    AND 1 = 0
                </if>
                <if test="filter.userIdList != null and filter.userIdList.size() > 0">
                    AND
                    (
                    a.salesman_user_id IN
                    <foreach collection="filter.userIdList" item="uid" open="(" separator="," close=")">
                        #{uid}
                    </foreach>
                    OR
                    a.submit_user_id IN
                    <foreach collection="filter.userIdList" item="uid" open="(" separator="," close=")">
                        #{uid}
                    </foreach>
                    )
                </if>
            </if>
        </where>
    </select>

    <select id="findBusinessInfoVO" resultType="com.gok.pboot.pms.entity.vo.BusinessInfoVO">
        SELECT
        <include refid="voColumnList"/>,
        IFNULL(a.business_mtime, a.business_ctime) AS 'business_mtime_for_sort',
        b.pre_sales_labor_input AS 'preSalesLaborInput',
        b.sqfytr AS 'sqfytr',
        c.name AS 'businessUnitName',
        d.unit_name AS 'unitName'
        FROM business_info a
        LEFT JOIN project_data b ON a.project_id = b.id
        LEFT JOIN customer_business c ON a.business_id =c.id
        LEFT JOIN customer_business_unit d ON a.unit_id =d.id
        WHERE a.del_flag = 0 AND a.item_no IS NOT NULL
        AND a.is_not_internal_project != 1
        <if test="filter.keyword != null and filter.keyword != ''">
            AND (a.business_name LIKE CONCAT('%', #{filter.keyword}, '%')
            OR a.end_customer_name LIKE CONCAT('%', #{filter.keyword}, '%')
            OR a.contract_customer_name LIKE CONCAT('%', #{filter.keyword}, '%'))
        </if>
        <if test="filter.departmentIds != null and filter.departmentIds.size() > 0">
            AND (
            a.department_id IN
            <foreach collection="filter.departmentIds" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
            OR
            a.first_level_department_id IN
            <foreach collection="filter.departmentIds" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
            )
        </if>
        <if test="filter.businessStage != null and filter.businessStage.size() > 0">
            AND a.business_stage IN
            <foreach collection="filter.businessStage" item="businessStage" separator="," open="(" close=")">
                #{businessStage}
            </foreach>
        </if>
        <if test="filter.businessId != null">
            AND a.business_id = #{filter.businessId}
        </if>
        <if test="filter.unitId != null and filter.unitId.size() > 0">
            AND a.unit_id IN
            <foreach collection="filter.unitId" item="unitId" open="(" separator="," close=")">
                #{unitId}
            </foreach>
        </if>
        <if test="filter.businessMilestone != null">
            AND a.business_milestone = #{filter.businessMilestone}
        </if>
        <if test="filter.isExternalProcurement != null and filter.isExternalProcurement != ''">
            AND a.is_external_procurement = #{filter.isExternalProcurement}
        </if>
        <if test="filter.purchasingCategories != null and filter.purchasingCategories != ''">
            AND a.purchasing_categories = #{filter.purchasingCategories}
        </if>
        <if test="filter.customerGrade != null">
            AND (a.end_customer_grade = #{filter.customerGrade}
            OR a.contract_customer_grade = #{filter.customerGrade})
        </if>
        <if test="filter.memberId != null">
            AND (a.salesman_user_id = #{filter.memberId}
            OR a.pre_sale_user_id = #{filter.memberId}
            OR a.manager_user_id = #{filter.memberId})
        </if>
        <if test="filter.projectLocation != null and filter.projectLocation != ''">
            AND a.project_location LIKE CONCAT('%', #{filter.projectLocation}, '%')
        </if>
        <if test="filter.customerIndustry != null and filter.customerIndustry != ''">
            AND a.end_customer_industry = #{filter.customerIndustry}
        </if>
        <if test="filter.businessModule != null">
            AND a.business_module = #{filter.businessModule}
        </if>
        <if test="filter.isNotNeedBidding != null">
            AND a.is_not_need_bidding = #{filter.isNotNeedBidding}
        </if>
        <if test="filter.contractEntity != null and filter.contractEntity != ''">
            AND a.contract_entity = #{filter.contractEntity}
        </if>
        <if test="filter.incomeType != null">
            AND a.income_type = #{filter.incomeType}
        </if>
        <if test="filter.technologyType != null">
            AND a.technology_type = #{filter.technologyType}
        </if>
        <if test="filter.settlementMethod != null">
            AND a.settlement_method = #{filter.settlementMethod}
        </if>
        <if test="filter.deliveryMethod != null">
            AND a.delivery_method = #{filter.deliveryMethod}
        </if>
        <if test="filter.supportFromKeyDecision != null">
            AND a.support_from_key_decision = #{filter.supportFromKeyDecision}
        </if>
        <if test="filter.projectRequirementClear != null">
            AND a.project_requirement_clear = #{filter.projectRequirementClear}
        </if>
        <if test="filter.budgetSituation != null">
            AND a.budget_situation = #{filter.budgetSituation}
        </if>
        <if test="filter.businessStatus != null">
            <if test="filter.businessStatus == 50">
                AND a.business_status NOT IN ('0', '1')
            </if>
            <if test="filter.businessStatus != 50">
                AND a.business_status = #{filter.businessStatus}
            </if>
        </if>
        <if test="filter.businessCtimeSort != null and filter.businessMtimeSort == null">
            ORDER BY
            IF(ISNULL(a.business_ctime), 1, 0),
            a.business_ctime
            <if test="filter.businessCtimeSort == 1">
                DESC
            </if>
        </if>
        <if test="filter.businessCtimeSort == null and filter.businessMtimeSort != null">
            ORDER BY
            IF(ISNULL(business_mtime_for_sort), 1, 0),
            business_mtime_for_sort
            <if test="filter.businessMtimeSort == 1">
                DESC
            </if>
        </if>
        <if test="filter.businessCtimeSort != null and filter.businessMtimeSort != null">
            ORDER BY
            IF(ISNULL(a.business_ctime), 1, 0),
            a.business_ctime
            <if test="filter.businessCtimeSort == 1">
                DESC
            </if>,
            IF(ISNULL(business_mtime_for_sort), 1, 0),
            business_mtime_for_sort
            <if test="filter.businessMtimeSort == 1">
                DESC
            </if>
        </if>
        <if test="filter.businessCtimeSort == null and filter.businessMtimeSort == null">
            ORDER BY IF(ISNULL(a.business_ctime), 1, 0), a.business_ctime DESC
        </if>
    </select>

    <select id="findBusinessProgress" resultType="com.gok.pboot.pms.entity.vo.BusinessProgressVO">
        SELECT
        <include refid="progressVOColumnList"/>
        FROM business_progress_info a
        WHERE del_flag = 0 AND a.business_id IS NOT NULL AND a.business_name IS NOT NULL
        <if test="filter.keyword != null and filter.keyword != ''">
            AND (a.business_name LIKE CONCAT('%', #{filter.keyword}, '%')
            OR a.customer_name LIKE CONCAT('%', #{filter.keyword}, '%'))
        </if>
        <if test="filter.projectSalesperson != null and filter.projectSalesperson != ''">
            AND a.project_salesperson LIKE CONCAT('%', #{filter.projectSalesperson}, '%')
        </if>
        <if test="filter.startTime != null">
            AND CAST(a.submit_time AS DATETIME) &gt;= #{filter.startTime}
        </if>
        <if test="filter.endTime != null">
            AND CAST(a.submit_time AS DATETIME) &lt;= #{filter.endTime}
        </if>
        ORDER BY CAST(a.submit_time AS DATETIME) DESC,a.id DESC
    </select>
    <select id="findId" resultType="java.lang.Long">
        SELECT DISTINCT bi.id
        FROM business_info AS bi
        LEFT JOIN customer_business_person AS cbp ON bi.business_id = cbp.business_id
        LEFT JOIN customer_business_unit AS cbu ON bi.unit_id = cbu.id
        <where>
            bi.del_flag = 0
            <if test='filter.scope != "all"'>
                AND (
                <!-- 客户（销售）经理 -->
                bi.salesman_user_id IN
                <foreach collection="filter.userIdList" item="uId" open="(" separator="," close=")">
                    #{uId}
                </foreach>
                <!-- 售前经理 -->
                OR bi.pre_sale_user_id IN
                <foreach collection="filter.userIdList" item="uId" open="(" separator="," close=")">
                    #{uId}
                </foreach>
                <!-- 项目经理 -->
                OR bi.manager_user_id IN
                <foreach collection="filter.userIdList" item="uId" open="(" separator="," close=")">
                    #{uId}
                </foreach>
                <!--所属客户经理-->
                OR cbu.unit_manager_id IN
                <foreach collection="filter.userIdList" item="uId" open="(" separator="," close=")">
                    #{uId}
                </foreach>
                <!--经营单元角色-->
                OR cbp.manager_id IN
                <foreach collection="filter.userIdList" item="uId" open="(" separator="," close=")">
                    #{uId}
                </foreach>
                )
            </if>
        </where>
    </select>
    <select id="countByUnitId" resultType="java.lang.Integer">
        select count(*)
        from business_info
        where unit_id = #{unitId}
      <if test="businessStatus != null">
        and business_status = #{businessStatus}
      </if>
        and del_flag = 0
    </select>
</mapper>
