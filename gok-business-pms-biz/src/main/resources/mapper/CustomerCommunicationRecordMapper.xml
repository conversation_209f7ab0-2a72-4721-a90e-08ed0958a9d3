<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.mapper.CustomerCommunicationRecordMapper">

    <!--获取客户沟通记录-分页-->
    <select id="findPage" resultType="com.gok.pboot.pms.entity.vo.CustomerCommunicationRecordVO">
        SELECT
            ccr.id,
            ccr.customer_name,
            ccr.customer_grade,
            ccr.communication_method,
            ccr.communication_time,
            ccr.time_slot,
            ccr.theme_and_objectives,
            ccr.content_situation,
            ccr.feedback_and_requirements,
            ccr.key_progress_made,
            ccr.next_step_forward_plan,
            ccr.account_manager_id,
            ccr.account_manager,
            ccr.other_participants,
            ccr.our_participants,
            ccr.submitter_id,
            ccr.submitter,
            ccr.submission_time,
            ccr.requestid as requestId,
            ccr.nodeoperator
        FROM
            customer_communication_record ccr
        WHERE
            1 = 1
        <if test="filter.customerName != null and filter.customerName != ''">
            AND ccr.customer_name LIKE CONCAT('%', #{filter.customerName}, '%')
        </if>
        <if test="filter.accountManager != null and filter.accountManager != ''">
            AND ccr.account_manager LIKE CONCAT('%', #{filter.accountManager}, '%')
        </if>
        <if test="filter.startTime != null">
            AND ccr.submission_time &gt;= #{filter.startTime}
        </if>
        <if test="filter.endTime != null">
            AND ccr.submission_time &lt;= #{filter.endTime}
        </if>
        ORDER BY ccr.submission_time DESC,ccr.id DESC
    </select>
    <select id="findCustomerCommunicationRecordByRequestId"
            resultType="com.gok.pboot.pms.entity.vo.CustomerCommunicationRecordVO">
        SELECT
            ccr.id,
            ccr.customer_name,
            ccr.customer_grade,
            ccr.communication_method,
            ccr.communication_time,
            ccr.time_slot,
            ccr.theme_and_objectives,
            ccr.content_situation,
            ccr.feedback_and_requirements,
            ccr.key_progress_made,
            ccr.next_step_forward_plan,
            ccr.account_manager_id,
            ccr.account_manager,
            ccr.other_participants,
            ccr.our_participants,
            ccr.submitter_id,
            ccr.submitter,
            ccr.submission_time,
            ccr.requestid as requestId,
            ccr.nodeoperator
        FROM
            customer_communication_record ccr
        WHERE ccr.del_flag = 0 AND ccr.requestid = #{requestId}
    </select>

    <select id="findIds" resultType="java.lang.Long">
        SELECT
        a.id
        FROM customer_communication_record a
        <where>
            <if test='filter.scope == "none"'>
                1 = 0
            </if>
            <if test='filter.scope != "all"'>
                <if test="filter.userIdList == null or filter.userIdList.size() == 0">
                    AND 1 = 0
                </if>
                <if test="filter.userIdList != null and filter.userIdList.size() > 0">
                    AND
                    (
                    a.account_manager_id IN
                    <foreach collection="filter.userIdList" item="uid" open="(" separator="," close=")">
                        #{uid}
                    </foreach> OR
                    a.submitter_id IN
                    <foreach collection="filter.userIdList" item="uid" open="(" separator="," close=")">
                        #{uid}
                    </foreach>
                    )
                </if>
            </if>
        </where>
    </select>

</mapper>
