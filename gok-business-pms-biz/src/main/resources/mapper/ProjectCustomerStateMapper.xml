<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.gok.pboot.pms.mapper.ProjectCustomerStateMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.gok.pboot.pms.entity.domain.ProjectCustomerState" id="projectCustomerStateMap">
        <result property="id" column="id"/>
        <result property="projectId" column="project_id"/>
        <result property="contact" column="contact"/>
        <result property="contactPhone" column="contact_phone"/>
        <result property="role" column="role"/>
        <result property="status" column="status"/>
        <result property="judgeBasis" column="judge_basis"/>
        <result property="supportDegree" column="support_degree"/>
        <result property="ctime" column="ctime"/>
    </resultMap>


    <select id="findListPage" resultType="com.gok.pboot.pms.entity.vo.ProjectCustomerStateVO">
        SELECT
        b.id,
        b.project_id,
        b.contact,
        b.contact_phone,
        b.role,
        b.status,
        b.judge_basis,
        b.support_degree
        FROM project_customer_state b
        where 1=1
        <if test="filter.id != null">
            AND  b.id = #{filter.id}
        </if>
        <if test="filter.projectId != null">
            AND  b.project_id = #{filter.projectId}
        </if>
        order by b.ctime desc
    </select>


</mapper>