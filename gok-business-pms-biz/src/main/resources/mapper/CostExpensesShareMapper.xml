<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.cost.mapper.CostExpensesShareMapper">
  <resultMap id="BaseResultMap" type="com.gok.pboot.pms.cost.entity.domain.CostExpensesShare">
    <!--@mbg.generated-->
    <!--@Table cost_expenses_share-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="recipient_user_name" jdbcType="VARCHAR" property="recipientUserName" />
    <result column="work_code" jdbcType="VARCHAR" property="workCode" />
    <result column="department_id" jdbcType="BIGINT" property="departmentId" />
    <result column="department_name" jdbcType="VARCHAR" property="departmentName" />
    <result column="account_id" jdbcType="BIGINT" property="accountId" />
    <result column="account_name" jdbcType="VARCHAR" property="accountName" />
    <result column="account_category_id" jdbcType="BIGINT" property="accountCategoryId" />
    <result column="account_category_name" jdbcType="VARCHAR" property="accountCategoryName" />
    <result column="reimburse_money" jdbcType="DECIMAL" property="reimburseMoney" />
    <result column="request_id" jdbcType="BIGINT" property="requestId" />
    <result column="request_number" jdbcType="VARCHAR" property="requestNumber" />
    <result column="applicant_id" jdbcType="BIGINT" property="applicantId" />
    <result column="applicant_name" jdbcType="VARCHAR" property="applicantName" />
    <result column="applicant_time" jdbcType="VARCHAR" property="applicantTime" />
    <result column="belonging_month" jdbcType="VARCHAR" property="belongingMonth" />
    <result column="filing_time" jdbcType="VARCHAR" property="filingTime" />
    <result column="customer_undertakes" jdbcType="TINYINT" property="customerUndertakes" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="creator_id" jdbcType="BIGINT" property="creatorId" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="modifier_id" jdbcType="BIGINT" property="modifierId" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, project_id, project_name, recipient_user_name, work_code, department_id, department_name, 
    account_id, account_name, account_category_id, account_category_name, reimburse_money, 
    request_id, request_number, applicant_id, applicant_name, applicant_time, belonging_month,filing_time,
    customer_undertakes, creator, creator_id, modifier, modifier_id, ctime, mtime, del_flag
  </sql>

  <sql id="queryColumn">
    <if test="dto.projectId != null">
      and project_id = #{dto.projectId}
    </if>
    <if test="dto.belongingStartMonth != null and dto.belongingStartMonth != ''">
      and belonging_month &gt;= #{dto.belongingStartMonth}
    </if>
    <if test="dto.belongingEndMonth != null and param1.belongingEndMonth != ''">
      and belonging_month &lt;= #{dto.belongingEndMonth}
    </if>
    <if test="dto.applicantName != null and dto.applicantName != ''">
      and (recipient_user_name like concat('%', #{dto.applicantName}, '%')
      or work_code like concat('%', #{dto.applicantName}, '%')
      or applicant_name like concat('%', #{dto.applicantName}, '%'))
    </if>
    <if test="dto.accountName != null and dto.accountName != ''">
      and account_name like concat('%', #{dto.accountName}, '%')
    </if>
    <if test="dto.nameSet != null and dto.nameSet.size() != 0">
      and recipient_user_name in
      <foreach collection="dto.nameSet" item="name" open="(" separator="," close=")">
            #{name}
        </foreach>
    </if>
      <if test="dto.projectIds != null and dto.projectIds.size() > 0">
          and project_id in
          <foreach collection="dto.projectIds" item="projectId" open="(" separator="," close=")">
              #{projectId}
          </foreach>
      </if>
      <if test="dto.workCodes != null and dto.workCodes.size() > 0">
          and work_code in
          <foreach collection="dto.workCodes" item="workCode" open="(" separator="," close=")">
              #{workCode}
          </foreach>
      </if>
      <if test="dto.belongMonths != null and dto.belongMonths.size() > 0">
          and belonging_month in
          <foreach collection="dto.belongMonths" item="belongMonth" open="(" separator="," close=")">
              #{belongMonth}
          </foreach>
      </if>
      <if test="dto.accountIds != null and dto.accountIds.size() > 0">
          and account_id in
          <foreach collection="dto.accountIds" item="accountId" open="(" separator="," close=")">
              #{accountId}
          </foreach>
      </if>
      <if test="dto.purviewNames != null and dto.purviewNames.size() != 0">
          and recipient_user_name in
          <foreach collection="dto.purviewNames" item="name" open="(" separator="," close=")">
              #{name}
          </foreach>
      </if>
  </sql>

  <select id="selSummary" resultType="com.gok.pboot.pms.cost.entity.vo.CostExpensesShareSumDataVO">
    select IFNULL(sum(reimburse_money),0.00)                                           as expensesTotal,
           IFNULL(sum(case when customer_undertakes = '0' then reimburse_money else 0 end),0.00) as customerBearsCost,
           IFNULL(sum(case when customer_undertakes = '1' then reimburse_money else 0 end),0.00) as gokBearsCost
    from cost_expenses_share
            where
            del_flag = '0'
    <include refid="queryColumn"/>
  </select>

    <select id="selSummaryList" resultType="com.gok.pboot.pms.cost.entity.vo.CostExpensesShareSummaryListVO">
        select belonging_month,
               account_id,
               account_name,
               account_category_id,
               account_category_name,
               sum(reimburse_money) as reimburseMoney
        from cost_expenses_share
                where
                del_flag = '0'
        <if test="dto.customerUndertakes != null">
            and customer_undertakes = #{dto.customerUndertakes}
        </if>
        <include refid="queryColumn"/>
        group by belonging_month,account_id
    </select>

    <select id="selDetailsList" resultType="com.gok.pboot.pms.cost.entity.vo.CostExpensesShareDetailsVO">
        select recipient_user_name,
               work_code,
               department_id,
               department_name,
               account_id,
               account_name,
               account_category_id,
               account_category_name,
               reimburse_money,
               request_id,
               request_number,
               applicant_id,
               applicant_name,
               applicant_time,
               belonging_month,
               filing_time,
               project_id,
               customer_undertakes
        from cost_expenses_share
        where
            del_flag = ${@<EMAIL>()}
        <if test="dto.customerUndertakes != null">
            and customer_undertakes = #{dto.customerUndertakes}
        </if>
        <if test="dto.workCodes != null and dto.workCodes.size() > 0">
            and work_code in
            <foreach collection="dto.workCodes" item="workCode" open="(" separator="," close=")">
                #{workCode}
            </foreach>
        </if>
        <include refid="queryColumn"/>
    </select>

</mapper>