<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.cost.mapper.CostConfigTravelSubsidyMapper">



    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.gok.pboot.pms.cost.entity.domain.CostConfigTravelSubsidy">
        <id column="id" property="id" />
        <result column="version_id" property="versionId" />
        <result column="person_num" property="personNum" />
        <result column="away_day" property="awayDay" />
        <result column="stay_own" property="stayOwn" />
        <result column="subsidy_price" property="subsidyPrice" />
        <result column="creator" property="creator" />
        <result column="creator_id" property="creatorId" />
        <result column="modifier" property="modifier" />
        <result column="modifier_id" property="modifierId" />
        <result column="ctime" property="ctime" />
        <result column="mtime" property="mtime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, version_id, person_num, away_day, stay_own, subsidy_price, creator, creator_id, modifier, modifier_id, ctime, mtime, del_flag
    </sql>
    <select id="getTravelSubsidiesByVersionId"
            resultType="com.gok.pboot.pms.cost.entity.vo.CostConfigTravelSubsidyVO">
        SELECT ts.id,
               ts.person_num,
               ts.away_day,
               ts.stay_own,
               ts.subsidy_price,
               cv.version_name
        FROM cost_config_travel_subsidy ts
                 LEFT JOIN cost_config_version cv ON ts.version_id = cv.id AND cv.del_flag = 0
        WHERE ts.version_id = #{versionId}
          AND ts.del_flag = 0
        ORDER BY ts.id
    </select>

</mapper>
