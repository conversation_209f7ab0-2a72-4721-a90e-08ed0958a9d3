<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.cost.mapper.CostTaskCategoryManagementMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.gok.pboot.pms.cost.entity.domain.CostTaskCategoryManagement">
        <id column="id" property="id" />
        <result column="task_category" property="taskCategory" />
        <result column="task_category_name" property="taskCategoryName" />
        <result column="task_type" property="taskType" />
        <result column="can_self_create" property="canSelfCreate" />
        <result column="sort" property="sort" />
        <result column="creator" property="creator" />
        <result column="creator_id" property="creatorId" />
        <result column="modifier" property="modifier" />
        <result column="modifier_id" property="modifierId" />
        <result column="ctime" property="ctime" />
        <result column="mtime" property="mtime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>
    <select id="getMaxTaskCategory" resultType="java.lang.Integer">
        SELECT IFNULL(MAX(task_category), 0) 
        FROM cost_task_category_management 
    </select>
    <select id="getMaxSort" resultType="java.lang.Integer">
        SELECT IFNULL(MAX(sort), 0)
        FROM cost_task_category_management 
        WHERE del_flag = ${@<EMAIL>()}
        AND task_type = ${@com.gok.pboot.pms.enumeration.ProjectTaskKindEnum@PRE_SALES_SUPPORT.getValue()}
    </select>
    <select id="getAllCategoryList"
            resultType="com.gok.pboot.pms.cost.entity.domain.CostTaskCategoryManagement">
        SELECT id,
               task_category,
               task_category_name,
               task_type,
               can_self_create,
               sort
        FROM cost_task_category_management
        order by sort
    </select>

</mapper>