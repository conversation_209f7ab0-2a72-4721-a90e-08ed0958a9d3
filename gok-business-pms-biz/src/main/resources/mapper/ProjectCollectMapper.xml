<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.mapper.ProjectCollectMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        a.id AS 'id',
                    a.project_id AS 'projectId',
                    a.collect_type AS 'collectType',
                    a.creator_id AS 'creatorId',
                    a.modifier AS 'modifier',
                    a.creator AS 'creator',
                    a.modifier_id AS 'modifierId',
                    a.ctime AS 'ctime',
                    a.mtime AS 'mtime',
                    a.del_flag AS 'delFlag'
    </sql>


    <select id="findForDailyPaperEntry" resultType="com.gok.pboot.pms.entity.ProjectCollect">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mhour_project_collect a
        <where>
            AND a.collect_type = #{collectType}
            AND a.creator_id = #{userId}
            <if test="projectIds != null and projectIds.size() > 0">
                AND a.project_id in
                <foreach collection="projectIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            AND a.del_flag = 0
        </where>
    </select>

    <insert id="batchSave">
        INSERT INTO mhour_project_collect (
        id,
        project_id,
        collect_type,
        creator_id,
        modifier,
        creator,
        modifier_id,
        ctime,
        mtime,
        del_flag
        )VALUES
        <foreach collection="poList" item="item" separator=",">
            (
            #{item.id},
            #{item.projectId},
            #{item.collectType},
            #{item.creatorId},
            #{item.modifier},
            #{item.creator},
            #{item.modifierId},
            #{item.ctime},
            #{item.mtime},
            #{item.delFlag}
            )
        </foreach>
    </insert>

    <update id="batchDel">
        update mhour_project_collect set del_flag = 1 where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="batchUpdate" parameterType="arraylist">
        update mhour_project_collect
        <trim prefix="set" suffixOverrides=",">
            <trim prefix=" project_id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id}
                    then #{item.projectId}
                </foreach>
            </trim>
            <trim prefix=" collect_type =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id}
                    then #{item.collectType}
                </foreach>
            </trim>
            <trim prefix=" modifier =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id}
                    then #{item.modifier}
                </foreach>
            </trim>
            <trim prefix=" modifier_id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id}
                    then #{item.modifierId}
                </foreach>
            </trim>
            <trim prefix=" mtime =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id}
                    then #{item.mtime}
                </foreach>
            </trim>
            <trim prefix=" del_flag =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id}
                    then #{item.delFlag}
                </foreach>
            </trim>
        </trim>

        <where>
            id in
            <foreach collection="list" separator="," item="item" open="(" close=")">
                #{item.id}
            </foreach>
        </where>
    </update>

</mapper>
