<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.cost.mapper.CostConfigTravelStayMapper">



    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.gok.pboot.pms.cost.entity.domain.CostConfigTravelStay">
        <id column="id" property="id" />
        <result column="version_id" property="versionId" />
        <result column="city_standards" property="cityStandards" />
        <result column="city_ids" property="cityIds" />
        <result column="general_office_price" property="generalOfficePrice" />
        <result column="director_above_price" property="directorAbovePrice" />
        <result column="director_below_price" property="directorBelowPrice" />
        <result column="creator" property="creator" />
        <result column="creator_id" property="creatorId" />
        <result column="modifier" property="modifier" />
        <result column="modifier_id" property="modifierId" />
        <result column="ctime" property="ctime" />
        <result column="mtime" property="mtime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, version_id, city_standards, city_ids, general_office_price, director_above_price, director_below_price, creator, creator_id, modifier, modifier_id, ctime, mtime, del_flag
    </sql>
    <select id="getTravelStaysByVersionId"
            resultType="com.gok.pboot.pms.cost.entity.vo.CostConfigTravelStayVO">
        SELECT ts.id,
               ts.city_standards,
               ts.city_ids,
               ts.general_office_price,
               ts.director_above_price,
               ts.director_below_price,
               cv.version_name
        FROM cost_config_travel_stay ts
                 LEFT JOIN cost_config_version cv ON ts.version_id = cv.id AND cv.del_flag = 0
        WHERE ts.del_flag = 0
          AND ts.version_id = #{versionId}
        ORDER BY ts.id
    </select>

</mapper>
