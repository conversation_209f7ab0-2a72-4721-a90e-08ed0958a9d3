<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.cost.mapper.CostIncomeCalculationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.gok.pboot.pms.cost.entity.domain.CostIncomeCalculation">
        <id column="id" property="id"/>
        <result property="projectId" column="project_id"/>
        <result column="belong_month" property="belongMonth"/>
        <result column="settlement_hours" property="settlementHours"/>
        <result column="settlement_unit_price" property="settlementUnitPrice"/>
        <result column="customer_bearing_amount" property="customerBearingAmount"/>
        <result column="estimated_inclusive_amount_tax" property="estimatedInclusiveAmountTax"/>
        <result column="confirm_status" property="confirmStatus"/>
        <result column="confirm_date" property="confirmDate"/>
        <result column="settlement_status" property="settlementStatus"/>
        <result column="ctime" property="ctime"/>
        <result column="mtime" property="mtime"/>
        <result column="creator" property="creator"/>
        <result column="creator_id" property="creatorId"/>
        <result column="modifier" property="modifier"/>
        <result column="modifier_id" property="modifierId"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,project_id,belong_month,settlement_hours,settlement_unit_price,customer_bearing_amount,
        estimated_inclusive_amount_tax,confirm_status,confirm_date,settlement_status,creator,
        creator_id,modifier,modifier_id,ctime,mtime,del_flag
    </sql>

    <select id="findList" resultType="com.gok.pboot.pms.cost.entity.domain.CostIncomeCalculation">
        SELECT
        <include refid="Base_Column_List"/>
        FROM cost_income_calculation
        <where>
            del_flag = ${@<EMAIL>()}
            <if test="query.projectIds != null and query.projectIds.size() > 0">
                AND project_id IN
                <foreach collection="query.projectIds" item="pId" open="(" separator="," close=")">
                    #{pId}
                </foreach>
            </if>
            <if test="query.query != null and query.query != ''">
                <bind name="queryLike" value="'%'+query.query+'%'"/>
                <if test="query.ids != null and query.ids.size() > 0">
                    AND id IN
                    <foreach collection="query.ids" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
            </if>
            <if test="query.startBelongMonth != null and query.endBelongMonth != null">
                AND belong_month <![CDATA[ >= ]]>  #{query.startBelongMonth} AND belong_month <![CDATA[ <= ]]>
                #{query.endBelongMonth}
            </if>
            <if test="query.ids != null and query.ids.size() > 0">
                AND id IN
                <foreach collection="query.ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="query.confirmStatus != null">
                AND confirm_status = #{query.confirmStatus}
            </if>
        </where>
        ORDER BY
        belong_month DESC,
        confirm_date DESC
    </select>

    <update id="batchUpdateStatusByDetails">
        <foreach collection="calculationConfirmMap.entrySet()" item="value" index="key" separator=";">
            UPDATE cost_income_calculation
            SET
            modifier = #{modifier},
            modifier_id = #{modifierId},
            mtime = now(),
            <if test="statusType == 0">
                confirm_status = #{value},
                <choose>
                    <when test="value == 0">
                        confirm_date = null
                    </when>
                    <when test="value == 1">
                        confirm_date = now()
                    </when>
                    <otherwise>
                        confirm_date = IFNULL(confirm_date, now())
                    </otherwise>
                </choose>
            </if>
            <if test="statusType == 1">
                settlement_status = #{value}
            </if>
            WHERE id = #{key}
            <if test="statusType == 0">
                <if test="value == 0">
                    AND confirm_status IN (${@<EMAIL>()},
                    ${@com.gok.pboot.pms.cost.enums.ConfirmStatusEnum@PART_CONFIRM.getValue()})
                </if>
                <if test="value == 1">
                    AND confirm_status != ${@<EMAIL>()}
                </if>
                AND settlement_status IN(${@com.gok.pboot.pms.cost.enums.SettlementStatusEnum@AWAIT_SETTLEMENT.getValue()},
                ${@com.gok.pboot.pms.cost.enums.SettlementStatusEnum@PART_SETTLEMENT.getValue()}
                )
            </if>
            <if test="statusType == 1">
                <if test="value == 1">
                    AND confirm_status != ${@com.gok.pboot.pms.cost.enums.ConfirmStatusEnum@AWAIT_CONFIRM.getValue()}
                </if>
            </if>
        </foreach>
    </update>

    <update id="batchUpdate">
        <foreach collection="updateEntries" item="item" separator=";">
            UPDATE cost_income_calculation SET
            project_id = #{item.projectId},
            belong_month = #{item.belongMonth},
            settlement_hours = #{item.settlementHours},
            settlement_unit_price = #{item.settlementUnitPrice},
            customer_bearing_amount = #{item.customerBearingAmount},
            estimated_inclusive_amount_tax = #{item.estimatedInclusiveAmountTax},
            confirm_status = #{item.confirmStatus},
            confirm_date = #{item.confirmDate},
            settlement_status = #{item.settlementStatus},
            modifier = #{item.modifier},
            modifier_id = #{item.modifierId},
            mtime = #{item.mtime}
            where id = #{item.id}
        </foreach>
    </update>

    <select id="findUnconfirmedProject" resultType="com.gok.pboot.pms.cost.entity.vo.CostIncomeCalculationVO">
        SELECT
            cia.project_id,
            pi.item_name AS project_name,
            pi.manager_user_id,
            pi.manager_user_name,
            MAX( cia.confirm_status ) AS confirm_status
        FROM
            cost_income_calculation AS cia
            LEFT JOIN project_info AS pi ON cia.project_id = pi.id
        WHERE
          cia.del_flag = ${@<EMAIL>()}
          AND pi.secondary_business_type = ${@com.gok.pboot.pms.enumeration.SecondaryBusinessTypeEnum@DIGITAL_TALENT_SERVICE.getValue()}
          AND cia.confirm_status IN (${@com.gok.pboot.pms.cost.enums.ConfirmStatusEnum@AWAIT_CONFIRM.getValue()},
                                     ${@com.gok.pboot.pms.cost.enums.ConfirmStatusEnum@PART_CONFIRM.getValue()})
          AND  YEAR(cia.belong_month) = YEAR(CURDATE() - INTERVAL 1 MONTH)
          AND MONTH(cia.belong_month) = MONTH(CURDATE() - INTERVAL 1 MONTH)
        GROUP BY
            cia.project_id,
            pi.item_name
    </select>

</mapper>
