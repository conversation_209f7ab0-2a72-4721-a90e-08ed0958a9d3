<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.mapper.PayAttendanceDataMapper">


    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.gok.pboot.pms.entity.PayAttendanceData">
        <id column="id" property="id"/>
        <result column="oaId" property="oaId"/>
        <result column="payrollTime" property="payrollTime"/>
        <result column="cwDueAttendance" property="cwDueAttendance"/>
        <result column="cwActualAttendance" property="cwActualAttendance"/>
        <result column="salary" property="salary"/>
    </resultMap>


    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        a.id AS 'id',
        a.oa_id AS 'oaId',
        a.payroll_time AS 'payrollTime',
        a.cw_due_attendance AS 'cwDueAttendance',
        a.cw_actual_attendance AS 'cwActualAttendance',
        a.salary AS 'salary'
    </sql>

    <sql id="join"></sql>

    <select id="findList" resultType="com.gok.pboot.pms.entity.PayAttendanceData">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mhour_pay_attendance_data a
        <include refid="join"/>
        <where>
        </where>
        ORDER BY a.id desc
    </select>

    <insert id="batchSave">
        INSERT INTO mhour_pay_attendance_data (
        id,
        oa_id,
        payroll_time,
        cw_due_attendance,
        cw_actual_attendance
        )VALUES
        <foreach collection="poList" item="item" separator=",">
            (
            #{item.id},
            #{item.oaId},
            #{item.payrollTime},
            #{item.cwDueAttendance},
            #{item.cwActualAttendance}
            )
        </foreach>
    </insert>


    <update id="batchUpdate" parameterType="arraylist">
        update mhour_pay_attendance_data
        <trim prefix="set" suffixOverrides=",">
            <trim prefix=" oa_id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id}
                    then #{item.oaId}
                </foreach>
            </trim>
            <trim prefix=" payroll_time =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id}
                    then #{item.payrollTime}
                </foreach>
            </trim>
            <trim prefix=" cw_due_attendance =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id}
                    then #{item.cwDueAttendance}
                </foreach>
            </trim>
            <trim prefix=" cw_actual_attendance =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id}
                    then #{item.cwActualAttendance}
                </foreach>
            </trim>
        </trim>

        <where>
            id in
            <foreach collection="list" separator="," item="item" open="(" close=")">
                #{item.id}
            </foreach>
        </where>
    </update>

    <!--批量更新非空字段-->
    <update id="updateBatch" parameterType="arraylist">
        update mhour_pay_attendance_data
        <trim prefix="set" suffixOverrides=",">
            <trim prefix=" oa_id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.oaId!=null">
                        when id=#{item.id}
                        then #{item.oaId}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" payroll_time =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.payrollTime!=null">
                        when id=#{item.id}
                        then #{item.payrollTime}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" cw_due_attendance =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.cwDueAttendance!=null">
                        when id=#{item.id}
                        then #{item.cwDueAttendance}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" cw_actual_attendance =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.cwActualAttendance!=null">
                        when id=#{item.id}
                        then #{item.cwActualAttendance}
                    </if>
                </foreach>
            </trim>
        </trim>
        where
        id in
        <foreach collection="list" separator="," item="item" open="(" close=")">
            #{item.id}
        </foreach>
    </update>
    <select id="findByDateRangeAndUserIds" resultType="com.gok.pboot.pms.entity.PayAttendanceData">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mhour_pay_attendance_data a
        <where>
            payroll_time&gt;=#{startTime} and payroll_time &lt;#{endTime}
            <if test="userIds != null and userIds.size() > 0">
                AND oa_id IN
                <foreach collection="userIds" separator="," item="userId" open="(" close=")">
                    #{userId}
                </foreach>
            </if>
        </where>
        order by payroll_time desc
    </select>

    <select id="findByWorkCodeAndPayrollTime" resultType="com.gok.pboot.pms.entity.PayAttendanceData">
        SELECT
            a.id,
            a.oa_id,
            r.work_code,
            a.payroll_time,
            a.cw_due_attendance,
            a.salary
        FROM
            mhour_pay_attendance_data AS a
            LEFT JOIN mhour_roster AS r ON a.oa_id = r.id
        <where>
            <if test="workCodes != null and workCodes.size() > 0">
                AND r.work_code IN
                <foreach collection="workCodes" separator="," item="workCode" open="(" close=")">
                    #{workCode}
                </foreach>
            </if>
            <if test="payrollTimes != null and payrollTimes.size() > 0">
                AND a.payroll_time IN
                <foreach collection="payrollTimes" separator="," item="payrollTime" open="(" close=")">
                    #{payrollTime}
                </foreach>
            </if>
        </where>
    </select>

</mapper>
