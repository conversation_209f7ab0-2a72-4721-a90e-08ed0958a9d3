<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.gok.pboot.pms.mapper.ProjectWeeklyMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.gok.pboot.pms.entity.domain.ProjectWeekly" id="baseResultMap">
        <result property="id" column="id"/>
        <result property="projectId" column="project_id"/>
        <result property="projectName" column="project_name"/>
        <result property="projectDepartment" column="project_department"/>
        <result property="projectDepartmentId" column="project_department_id"/>
        <result property="reportStart" column="report_start"/>
        <result property="reportEnd" column="report_end"/>
        <result property="reportUserId" column="report_user_id"/>
        <result property="reportUser" column="report_user"/>
        <result property="currentWorkProgress" column="current_work_progress"/>
        <result property="currentProgress" column="current_progress"/>
        <result property="currentHours" column="current_hours"/>
        <result property="totalHours" column="total_hours"/>
        <result property="nextWorkPlan" column="next_work_plan"/>
        <result property="needSupportItem" column="need_support_item"/>
        <result property="weeklyRisk" column="weekly_risk"/>
        <result property="docIds" column="doc_ids"/>
        <result property="docNames" column="doc_names"/>
        <result property="creator" column="creator"/>
        <result property="creatorId" column="creator_id"/>
        <result property="modifier" column="modifier"/>
        <result property="modifierId" column="modifier_id"/>
        <result property="ctime" column="ctime"/>
        <result property="mtime" column="mtime"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>


    <insert id="batchSave" parameterType="java.util.List">
        INSERT INTO project_weekly(
        id,
        project_id,
        project_name,
        project_department,
        project_department_id,
        report_start,
        report_end,
        report_user_id,
        report_user,
        current_work_progress,
        current_progress,
        current_hours,
        total_hours,
        next_work_plan,
        need_support_item,
        weekly_risk,
        weekly_risk_ids,
        creator,
        creator_id,
        ctime
        )VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},
            #{item.projectId},
            #{item.projectName},
            #{item.projectDepartment},
            #{item.projectDepartmentId},
            #{item.reportStart},
            #{item.reportEnd},
            #{item.reportUserId},
            #{item.reportUser},
            #{item.currentWorkProgress},
            #{item.currentProgress},
            #{item.currentHours},
            #{item.totalHours},
            #{item.nextWorkPlan},
            #{item.needSupportItem},
            #{item.weeklyRisk},
            #{item.weeklyRiskIds},
            #{item.creator},
            #{item.creatorId},
            #{item.ctime}
            )
        </foreach>
    </insert>

    <insert id="addReadList">
        insert into project_weekly_reading
            (id,user_id,pw_id,creator,creator_id,modifier,modifier_id,ctime,mtime,del_flag)
        values
        <foreach collection="newReadList" item="item" separator=",">
            (#{item.id},#{item.userId},#{item.pwId},#{item.creator},#{item.creatorId},
             #{item.modifier},#{item.modifierId},#{item.ctime},#{item.mtime},#{item.delFlag})
        </foreach>
    </insert>


    <select id="findListPage" resultType="com.gok.pboot.pms.entity.vo.ProjectWeeklyVO">
        SELECT
            b.id,
            b.project_id,
            b.project_name,
            b.project_department,
            b.project_department_id,
            b.report_start,
            b.report_end,
            b.report_user_id,
            b.report_user,
            b.current_work_progress,
            b.current_progress,
            b.current_hours,
            b.total_hours,
            b.next_work_plan,
            b.need_support_item,
            b.weekly_risk,
            b.weekly_risk_ids,
            b.remark,
            b.doc_ids,
            b.doc_names,
            b.ctime,
            b.mtime
        FROM project_weekly b
        where TRUE
            <if test="filter.id != null">
                AND  b.id = #{filter.id}
            </if>
            <if test="filter.projectId != null">
                AND  b.project_id = #{filter.projectId}
            </if>
            AND del_flag =0
            order by b.report_start desc

         </select>


    <select id="findListAllPage" resultType="com.gok.pboot.pms.entity.vo.ProjectWeeklyVO">
        SELECT
        b.id,
        b.project_id,
        b.project_name,
        p.item_no,
        p.project_date,
        p.project_status,
        p.first_level_department_id,
        p.first_level_department,
        b.project_department,
        b.project_department_id,
        b.report_start,
        b.report_end,
        b.report_user_id,
        b.report_user,
        b.current_work_progress,
        b.current_progress,
        b.current_hours,
        b.total_hours,
        b.next_work_plan,
        b.need_support_item,
        b.weekly_risk,
        b.weekly_risk_ids,
        b.remark,
        b.doc_ids,
        b.doc_names,
        b.ctime,
        b.mtime,
        p.is_not_internal_project
        FROM project_weekly b
            left join project_info p on  b.project_id=p.id
            LEFT JOIN project_stakeholder_member psm ON b.project_id = psm.project_id
        where  TRUE
        <if test="filter.projectName != null and filter.projectName != ''">
            AND b.project_name like concat('%', #{filter.projectName}, '%')
        </if>

        <if test="filter.projectDepartment != null and filter.projectDepartment != ''">
            AND b.project_department like concat('%', #{filter.projectDepartment}, '%')
        </if>
        <if test="filter.deptIds != null and filter.deptIds != ''">
            AND b.project_department_id IN
            <include refid="com.gok.pboot.pms.mapper.CommonMapper.splitLongsStr">
                <property name="longsStr" value="filter.deptIds"/>
            </include>
        </if>
        <if test="filter.reportStart != null ">
            AND b.report_start &gt;=  #{filter.reportStart}
        </if>
        <if test="filter.reportEnd != null">
            AND b.report_end  &lt;=  #{filter.reportEnd}
        </if>
        AND b.del_flag =0
        <if test="filter.scope != 'all'">
            <if test="filter.projectIdsInDataScope == null or filter.projectIdsInDataScope.size() == 0">
                AND FALSE
            </if>
            <if test="filter.projectIdsInDataScope != null and filter.projectIdsInDataScope.size() > 0">
                AND b.project_id in
                <foreach collection="filter.projectIdsInDataScope" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test="filter.projectStatus != null and filter.projectStatus != ''">
            AND p.project_status IN
            <include refid="com.gok.pboot.pms.mapper.CommonMapper.splitLongsStr">
                <property name="longsStr" value="filter.projectStatus"/>
            </include>
        </if>
        <if test="filter.ifInner != null ">
            AND p.is_not_internal_project = #{filter.ifInner}
        </if>
        <if test="filter.reporterIds != null and filter.reporterIds != ''">
            AND b.report_user_id in
            <include refid="com.gok.pboot.pms.mapper.CommonMapper.splitLongsStr">
                <property name="longsStr" value="filter.reporterIds"/>
            </include>
        </if>
        <if test="filter.projectIds != null and filter.projectIds.size>0">
            AND p.id IN
            <foreach collection="filter.projectIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by b.id
        order by b.ctime desc
    </select>

    <select id="findAllList" resultType="com.gok.pboot.pms.entity.vo.ProjectWeeklyVO">
        SELECT
        b.id,
        b.project_id,
        b.project_name,
        b.project_department,
        b.project_department_id,
        b.report_start,
        b.report_end,
        b.report_user_id,
        b.report_user,
        b.current_work_progress,
        b.current_progress,
        b.current_hours,
        b.total_hours,
        b.next_work_plan,
        b.need_support_item,
        b.weekly_risk,
        b.remark,
        b.doc_ids,
        b.doc_names,
        b.ctime,
        b.mtime,
        p.project_status
        FROM project_weekly b
        left join project_info p on b.project_id=p.id
        LEFT JOIN project_stakeholder_member psm ON b.project_id = psm.project_id
        where TRUE
        <if test="filter.projectName != null and filter.projectName != ''">
            AND b.project_name like concat('%', #{filter.projectName}, '%')
        </if>

        <if test="filter.projectDepartment != null and filter.projectDepartment != ''">
            AND b.project_department like concat('%', #{filter.projectDepartment}, '%')
        </if>
        <if test="filter.deptIds != null">
            AND b.project_department_id IN
            <include refid="com.gok.pboot.pms.mapper.CommonMapper.splitLongsStr">
                <property name="longsStr" value="filter.deptIds"/>
            </include>
        </if>
        <if test="filter.reportStart != null ">
            AND b.report_start &gt;=  #{filter.reportStart}
        </if>
        <if test="filter.reportEnd != null">
            AND b.report_end  &lt;=  #{filter.reportEnd}
        </if>
        AND b.del_flag = 0
        <if test="filter.scope != 'all'">
            <if test="filter.projectIdsInDataScope == null or filter.projectIdsInDataScope.size() == 0">
                AND FALSE
            </if>
            <if test="filter.projectIdsInDataScope != null and filter.projectIdsInDataScope.size() > 0">
                AND b.project_id IN
                <foreach collection="filter.projectIdsInDataScope" item="projectId" open="(" separator="," close=")">
                    #{projectId}
                </foreach>
            </if>
        </if>
        <if test="filter.projectStatus != null ">
            AND p.project_status = #{filter.projectStatus}
        </if>
        <if test="filter.ifInner != null ">
            AND p.is_not_internal_project = #{filter.ifInner}
        </if>
        <if test="filter.reporterIds != null">
            AND b.report_user_id in
            <include refid="com.gok.pboot.pms.mapper.CommonMapper.splitLongsStr">
                <property name="longsStr" value="filter.reporterIds"/>
            </include>
        </if>
        <if test="filter.projectIds != null and filter.projectIds.size>0">
            AND p.id IN
            <foreach collection="filter.projectIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by b.id
        order by b.ctime desc
    </select>

    <select id="findReadList" resultType="com.gok.pboot.pms.entity.domain.ProjectWeeklyReading">
        select
               id,
               user_id,
               pw_id
        from project_weekly_reading
        where del_flag = 0 and user_id = #{userId}
    </select>

    <select id="findById" resultType="com.gok.pboot.pms.entity.domain.ProjectWeekly">
        select
            *
        from project_weekly
        where del_flag = 0 and id = #{id}
    </select>



</mapper>