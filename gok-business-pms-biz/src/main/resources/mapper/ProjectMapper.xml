<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.mapper.ProjectMapper">


    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.gok.pboot.pms.entity.Project">
        <id column="id" property="id"/>
        <result column="projectName" property="projectName"/>
        <result column="code" property="code"/>
        <result column="projectStatus" property="projectStatus"/>
        <result column="managerUserId" property="managerUserId"/>
        <result column="managerUserName" property="managerUserName"/>
        <result column="salesmanUserId" property="salesmanUserId"/>
        <result column="salesmanUserName" property="salesmanUserName"/>
        <result column="preSalesmanUserId" property="preSalesmanUserId"/>
        <result column="preSalesmanUserName" property="preSalesmanUserName"/>
        <result column="deptId" property="deptId"/>
        <result column="creator" property="creator"/>
        <result column="creatorId" property="creatorId"/>
        <result column="modifier" property="modifier"/>
        <result column="modifierId" property="modifierId"/>
        <result column="ctime" property="ctime"/>
        <result column="mtime" property="mtime"/>
        <result column="delFlag" property="delFlag"/>
    </resultMap>


    <resultMap id="projectInDailyPaperEntryMap" type="com.gok.pboot.pms.common.join.ProjectInDailyPaperEntry">
        <id property="id" column="id"/>
        <result property="projectName" column="projectName"/>
        <result property="projectStatus" column="projectStatus"/>
        <result property="ctime" column="ctime"/>
        <result property="managerUserName" column="managerUserName"/>
        <result property="salesmanUserName" column="salesmanUserName"/>
        <result property="preSalesmanUserName" column="preSalesmanUserName"/>
        <result property="isInsideProject" column="isInsideProject"/>
        <result property="collectId" column="collectId"/>
        <result property="collectUserId" column="collectUserId"/>
        <result property="collectTime" column="collectTime"/>
        <collection property="auditorNames" ofType="java.lang.String" javaType="java.util.List">
            <result column="auditorName"/>
        </collection>
    </resultMap>
    <resultMap id="projectInDailyPaperEntryMap2" type="com.gok.pboot.pms.common.join.ProjectInDailyPaperEntry">
        <id property="id" column="id"/>
        <result property="projectName" column="projectName"/>
        <result property="projectStatus" column="projectStatus"/>
        <result property="ctime" column="ctime"/>
        <result property="managerUserName" column="managerUserName"/>
        <result property="salesmanUserName" column="salesmanUserName"/>
        <result property="preSalesmanUserName" column="preSalesmanUserName"/>
        <result property="isInsideProject" column="isInsideProject"/>
        <result property="collectId" column="collectId"/>
        <result property="collectUserId" column="collectUserId"/>
        <result property="collectTime" column="collectTime"/>
    </resultMap>


    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        a.id AS 'id',
        a.project_name AS 'projectName',
        a.code AS 'code',
        a.project_status AS 'projectStatus',
        a.manager_user_id AS 'managerUserId',
        a.manager_user_name AS 'managerUserName',
        a.salesman_user_id AS 'salesmanUserId',
        a.salesman_user_name AS 'salesmanUserName',
        a.pre_salesman_user_id AS 'preSalesmanUserId',
        a.pre_salesman_user_name AS 'preSalesmanUserName',
        a.dept_id AS 'deptId',
        a.creator AS 'creator',
        a.creator_id AS 'creatorId',
        a.modifier AS 'modifier',
        a.modifier_id AS 'modifierId',
        a.ctime AS 'ctime',
        a.mtime AS 'mtime',
        a.del_flag AS 'delFlag'
    </sql>

    <sql id="join"></sql>

    <sql id="findForDailyPaperEntry">
        SELECT project.id AS id,
        project.project_status AS projectStatus,
        DATE_FORMAT(project.ctime, '%Y-%m-%d') AS ctime,
        project.project_name AS projectName,
        project.salesman_user_name AS salesmanUserName,
        project.manager_user_name AS managerUserName,
        project.pre_salesman_user_name AS preSalesmanUserName,
        project.is_inside_project AS isInsideProject,
        privilege.user_name AS auditorName
        FROM mhour_project project
        LEFT JOIN mhour_task task ON task.project_id = project.id
        LEFT JOIN mhour_task_user tu ON tu.task_id = task.id
        LEFT JOIN ( SELECT project_id, user_name FROM mhour_privilege WHERE del_flag = 0 AND privilege_type = 1)
        privilege ON privilege.project_id = project.id
    </sql>

    <!--根据用户id删除关注项目-->
    <update id="deleteAttentionProjectByUserId">
        DELETE FROM project_attention
        WHERE
            user_id = #{param.userId}
    </update>

    <select id="findList" resultType="com.gok.pboot.pms.entity.Project">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mhour_project a
        <include refid="join"/>
        <where>
            a.del_flag = 0
        </where>
        ORDER BY a.id desc
    </select>

    <select id="findListXmjl" resultType="com.gok.pboot.pms.entity.vo.ProjectVO">
        SELECT
        a.id AS 'id',
        a.project_name AS 'projectName',
        a.code AS 'code',
        a.project_status AS 'projectStatus',
        a.dept_id
        FROM mhour_project a
        <where>
            a.del_flag = 0
            <if test="filter.projectName != null and filter.projectName != '' ">
                AND a.project_name like concat('%',#{filter.projectName},'%')
            </if>
            <if test="filter.projectStatus != null">
                AND a.project_status = #{filter.projectStatus}
            </if>
            <if test="filter.type != null and filter.type == 'privilege' ">
                AND a.id NOT IN(
                SELECT a.project_id
                FROM mhour_privilege a
                where a.del_flag = 0 GROUP BY a.project_id
                )
            </if>
            <if test="filter.userId != null">
                AND (
                ( a.salesman_user_id = #{filter.userId} )
                OR ( a.manager_user_id = #{filter.userId} )
                OR ( a.id IN ( SELECT a.project_id FROM mhour_privilege a where a.del_flag = 0 and a.user_id =
                #{filter.userId} GROUP BY a.project_id ))
                )
            </if>
        </where>
        ORDER BY project_name
    </select>

    <select id="findListVoPage" resultType="com.gok.pboot.pms.entity.vo.ProjectVO">
        SELECT
        a.id AS 'id',
        a.project_name AS 'projectName',
        a.code AS 'code',
        a.project_status AS 'projectStatus',
        a.dept_id

        FROM mhour_project a
        <where>
            a.del_flag = 0
            <if test="filter.projectName != null and filter.projectName != '' ">
                AND a.project_name like concat('%',#{filter.projectName},'%')
            </if>
            <if test="filter.type != null and filter.type == 'privilege' ">
                AND a.id NOT IN(
                SELECT a.project_id
                FROM mhour_privilege a
                where a.del_flag = 0 GROUP BY a.project_id
                )
            </if>
            <if test="filter.type != null and filter.type == 'task' and filter.userId != null">
                AND (
                ( a.project_status IN(0,1) and a.salesman_user_id = #{filter.userId} )
                OR ( a.project_status IN(2,6,7) and a.manager_user_id = #{filter.userId} )
                OR ( a.id IN ( SELECT a.project_id FROM mhour_privilege a where a.del_flag = 0 and a.user_id =
                #{filter.userId} GROUP BY a.project_id ))
                )
            </if>
        </where>
        ORDER BY project_name
    </select>

    <select id="findListPageNoRole" resultType="com.gok.pboot.pms.entity.vo.ProjectVO">
        SELECT
        a.id AS 'id',
        a.item_name AS 'projectName',
        a.item_no AS 'code',
        a.project_status AS 'projectStatus',
        a.first_level_department_id AS 'deptId',
        b.id AS 'collectId',b.ctime AS 'collectTime',b.creator_id AS 'collectUserId'
        FROM project_info a LEFT JOIN mhour_project_collect b on a.id=b.project_id and b.del_flag = 0
        <if test="filter.collectType != null and filter.collectType != '' ">
            and b.collect_type=#{filter.collectType}
        </if>
        <if test="filter.userId != null and filter.userId != '' ">
            and b.creator_id=#{filter.userId}
        </if>
        <where>
            <if test="filter.projectName != null and filter.projectName != '' ">
                AND a.item_name like concat('%',#{filter.projectName},'%')
            </if>
            <if test="filter.projectIds != null and filter.projectIds.size() > 0 ">
                AND a.id IN
                <foreach collection="filter.projectIds" open="(" close=")" separator="," item="id">
                    #{id}
                </foreach>
            </if>
        </where>
        group by a.id
        ORDER BY b.ctime desc,FIELD(project_status,0,2,3,4,5,6,1,7) ,item_name
    </select>


    <select id="findListFront" resultType="com.gok.pboot.pms.entity.vo.ProjectVO">
        SELECT
        a.id AS 'id',
        a.item_name AS 'projectName',
        a.item_no AS 'code',
        a.project_status AS 'projectStatus',
        a.first_level_department_id AS 'deptId'
        FROM project_info a
        <where>
            <if test="filter.projectName != null and filter.projectName != '' ">
                a.item_name like concat('%',#{filter.projectName},'%')
            </if>
        </where>
        ORDER BY a.item_name
    </select>

    <select id="findListFrontByRole" resultType="com.gok.pboot.pms.entity.vo.ProjectVO">
        SELECT
        a.id AS 'id',
        a.project_name AS 'projectName',
        a.code AS 'code',
        a.project_status AS 'projectStatus',
        a.dept_id
        FROM mhour_project a
        <where>
            a.del_flag = 0
            <if test="filter.projectName != null and filter.projectName != '' ">
                AND a.project_name like concat('%',#{filter.projectName},'%')
            </if>

            <if test="roleFilter.privilegeUserId != null and roleFilter.privilegeUserId != '' and roleFilter.privilegeUserId != -1">
                and a.id IN (
                select project_id from mhour_privilege
                <where>
                    del_flag = 0 and user_id=#{roleFilter.privilegeUserId}
                </where>)
            </if>
            <if test="roleFilter.managerUserId != null and roleFilter.managerUserId != '' ">
                or a.manager_user_id=#{roleFilter.managerUserId}
                or a.salesman_user_id=#{roleFilter.managerUserId}
                or a.pre_salesman_user_id=#{roleFilter.managerUserId}
            </if>
            <if test="roleFilter.deptId != null and roleFilter.deptId != '' ">
                or a.dept_id=#{roleFilter.deptId}
            </if>
        </where>
        ORDER BY project_name
    </select>

    <select id="selectByIdVo" resultType="com.gok.pboot.pms.entity.vo.facade.ProjectInfoVO">
        SELECT
        a.id AS 'id',
        a.item_name AS 'projectName',
        a.item_no AS 'code',
        a.project_status AS 'projectStatus',
        a.first_level_department AS 'projectDepartment'
        FROM project_info a
        <where>
            a.id = #{id}
        </where>
        ORDER BY item_name
    </select>

    <select id="findByUserIdForDailyPaperEntry" resultMap="projectInDailyPaperEntryMap">
        SELECT project.id AS id,
        project.project_status AS projectStatus,
        DATE_FORMAT(project.ctime, '%Y-%m-%d') AS ctime,
        project.project_name AS projectName,
        project.salesman_user_name AS salesmanUserName,
        project.pre_salesman_user_name AS preSalesmanUserName,
        project.manager_user_name AS managerUserName,
        project.is_inside_project AS isInsideProject,
        privilege.user_name AS auditorName,
        b.id AS 'collectId',b.ctime AS 'collectTime',b.creator_id AS 'collectUserId'
        FROM mhour_project project
        LEFT JOIN mhour_task task ON task.project_id = project.id
        LEFT JOIN mhour_task_user tu ON tu.task_id = task.id
        LEFT JOIN ( SELECT project_id, user_name FROM mhour_privilege WHERE del_flag = 0 AND privilege_type = 1)
        privilege ON privilege.project_id = project.id
        LEFT JOIN mhour_project_collect b ON project.id = b.project_id and b.collect_type=#{collectType} and
        b.creator_id=#{userId} and b.del_flag = 0
        WHERE tu.user_id = #{userId}
        AND tu.del_flag = 0
        AND project.del_flag = 0
        AND task.del_flag = 0
        ORDER BY b.ctime desc
    </select>

<!--    <select id="findByUserIdForDailyPaperEntryNew" resultMap="projectInDailyPaperEntryMap">-->
<!--        SELECT DISTINCT project.id AS id,-->
<!--               project.project_status AS projectStatus,-->
<!--               DATE_FORMAT(project.ctime, '%Y-%m-%d') AS ctime,-->
<!--               project.project_name AS projectName,-->
<!--               project.salesman_user_name AS salesmanUserName,-->
<!--               project.pre_salesman_user_name AS preSalesmanUserName,-->
<!--               project.manager_user_name AS managerUserName,-->
<!--               project.is_inside_project AS isInsideProject,-->
<!--               privilege.user_name AS auditorName,-->
<!--               b.id AS 'collectId',b.ctime AS 'collectTime',b.creator_id AS 'collectUserId'-->
<!--        FROM mhour_project project-->
<!--                 LEFT JOIN project_task task ON task.project_id = project.id-->
<!--                 LEFT JOIN project_task_user tu ON (tu.task_id = task.id OR task.manager_user_id = #{userId})-->
<!--                 LEFT JOIN ( SELECT project_id, user_name FROM mhour_privilege WHERE del_flag = 0 AND privilege_type = 1)-->
<!--            privilege ON privilege.project_id = project.id-->
<!--                 LEFT JOIN mhour_project_collect b ON project.id = b.project_id and b.collect_type=#{collectType} and-->
<!--                                                      b.creator_id=#{userId} and b.del_flag = 0-->
<!--        WHERE (tu.user_id = #{userId} OR task.manager_user_id = #{userId})-->
<!--          AND tu.del_flag = 0-->
<!--          AND project.del_flag = 0-->
<!--          AND task.del_flag = 0-->
<!--        ORDER BY b.ctime desc-->
<!--    </select>-->
    <select id="findByUserIdForDailyPaperEntryNew" resultMap="projectInDailyPaperEntryMap2">
        SELECT DISTINCT project.id AS id,
                        project.project_status AS projectStatus,
                        DATE_FORMAT(project.ctime, '%Y-%m-%d') AS ctime,
                        project.project_name AS projectName,
                        project.salesman_user_name AS salesmanUserName,
                        project.pre_salesman_user_name AS preSalesmanUserName,
                        project.manager_user_name AS managerUserName,
                        project.is_inside_project AS isInsideProject
        FROM mhour_project project
                 LEFT JOIN project_task task ON task.project_id = project.id
                 LEFT JOIN project_task_user tu ON (tu.task_id = task.id OR task.manager_user_id = #{userId})
        WHERE (tu.user_id = #{userId} OR task.manager_user_id = #{userId})
          AND tu.del_flag = 0
          AND project.del_flag = 0
          AND task.del_flag = 0
    </select>
    <select id="findByUserIdForDailyPaperEntryNew2" resultMap="projectInDailyPaperEntryMap">
        SELECT privilege.project_id AS id,
                        privilege.user_name AS auditorName
        FROM mhour_privilege privilege
        WHERE 1 = 1
        <if test="list != null and list.size() > 0">
            AND privilege.project_id IN
            <foreach collection="list" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="list == null or list.size() == 0">
            AND privilege.project_id = null
        </if>
          AND privilege.del_flag = 0
          AND privilege.privilege_type = 1
    </select>
    <select id="findByUserIdForDailyPaperEntryNew3" resultMap="projectInDailyPaperEntryMap2">
        SELECT b.project_id AS id,
                        b.id AS 'collectId',b.ctime AS 'collectTime',b.creator_id AS 'collectUserId'
        FROM mhour_project_collect b
        WHERE 1 = 1
        <if test="list != null and list.size() > 0">
            AND b.project_id IN
            <foreach collection="list" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="list == null or list.size() == 0">
            AND b.project_id = null
        </if>
          AND b.collect_type = #{collectType}
          AND b.creator_id = #{userId}
          AND b.del_flag = 0
    </select>

<!--    <select id="findByIdsForDailyPaperEntry" resultMap="projectInDailyPaperEntryMap">-->
<!--        SELECT project.id AS id,-->
<!--        project.project_status AS projectStatus,-->
<!--        DATE_FORMAT(project.ctime, '%Y-%m-%d') AS ctime,-->
<!--        project.project_name AS projectName,-->
<!--        project.salesman_user_name AS salesmanUserName,-->
<!--        project.pre_salesman_user_name AS preSalesmanUserName,-->
<!--        project.manager_user_name AS managerUserName,-->
<!--        project.is_inside_project AS isInsideProject,-->
<!--        privilege.user_name AS auditorName-->
<!--        FROM mhour_project project-->
<!--        LEFT JOIN mhour_task task ON task.project_id = project.id-->
<!--        LEFT JOIN ( SELECT project_id, user_name FROM mhour_privilege WHERE del_flag = 0 AND privilege_type = 1)-->
<!--        privilege ON privilege.project_id = project.id-->
<!--        WHERE project.del_flag = 0-->
<!--        AND task.del_flag = 0-->
<!--        <if test="list != null and list.size() > 0">-->
<!--            AND project.id IN-->
<!--            <foreach collection="list" open="(" close=")" separator="," item="id">-->
<!--                #{id}-->
<!--            </foreach>-->
<!--        </if>-->

<!--    </select>-->

    <!--逻辑删除-->
    <update id="deleteByLogic">
        UPDATE mhour_project SET
        del_flag = 1
        WHERE id = #{id}
    </update>


    <insert id="batchSave">
        INSERT INTO mhour_project (
        id,
        project_name,
        code,
        project_status,
        manager_user_id,
        manager_user_name,
        salesman_user_id,
        salesman_user_name,
        pre_salesman_user_id,
        pre_salesman_user_name,
        dept_id,
        creator,
        creator_id,
        modifier,
        modifier_id,
        ctime,
        mtime,
        del_flag
        )VALUES
        <foreach collection="poList" item="item" separator=",">
            (
            #{item.id},
            #{item.projectName},
            #{item.code},
            #{item.projectStatus},
            #{item.managerUserId},
            #{item.managerUserName},
            #{item.salesmanUserId},
            #{item.salesmanUserName},
            #{item.preSalesmanUserId},
            #{item.preSalesmanUserName},
            #{item.deptId},
            #{item.creator},
            #{item.creatorId},
            #{item.modifier},
            #{item.modifierId},
            #{item.ctime},
            #{item.mtime},
            #{item.delFlag}
            )
        </foreach>
    </insert>

    <update id="batchDel">
        update mhour_project set del_flag = 1 where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <insert id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            UPDATE mhour_project SET
            id = #{item.id},
            project_name = #{item.projectName},
            code = #{item.code},
            project_status = #{item.projectStatus},
            manager_user_id = #{item.managerUserId},
            manager_user_name = #{item.managerUserName},
            salesman_user_id = #{item.salesmanUserId},
            salesman_user_name = #{item.salesmanUserName},
            pre_salesman_user_id = #{item.preSalesmanUserId},
            pre_salesman_user_name = #{item.preSalesmanUserName},
            dept_id = #{item.deptId},
            creator = #{item.creator},
            creator_id = #{item.creatorId},
            modifier = #{item.modifier},
            modifier_id = #{item.modifierId},
            ctime = #{item.ctime},
            mtime = #{item.mtime},
            del_flag = #{item.delFlag}
            where id = #{item.id}
        </foreach>
    </insert>

    <!--插入用户关注项目-->
    <insert id="addAttentionProject">
        insert into project_attention
        (
         id,
         user_id,
         project_id,
         ctime,
         mtime,
         creator,
         creator_id,
         modifier,
         modifier_id,
         del_flag
         )values
        <foreach collection="list" item="item" separator=",">
            (
             #{item.id},
             #{item.userId},
             #{item.projectId},
             #{item.ctime},
             #{item.mtime},
             #{item.creator},
             #{item.creatorId},
             #{item.modifier},
             #{item.modifierId},
             #{item.delFlag}
            )
        </foreach>
    </insert>

    <!--批量更新非空字段-->
    <update id="updateBatch" parameterType="arraylist">
        update mhour_project
        <trim prefix="set" suffixOverrides=",">
            <trim prefix=" project_name =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.projectName!=null">
                        when id=#{item.id} then #{item.projectName}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" code =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.code!=null">
                        when id=#{item.id} then #{item.code}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" project_status =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.projectStatus!=null">
                        when id=#{item.id} then #{item.projectStatus}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" manager_user_id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.managerUserId!=null">
                        when id=#{item.id} then #{item.managerUserId}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" manager_user_name =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.managerUserName!=null">
                        when id=#{item.id} then #{item.managerUserName}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" salesman_user_id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.salesmanUserId!=null">
                        when id=#{item.id} then #{item.salesmanUserId}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" salesman_user_name =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.salesmanUserName!=null">
                        when id=#{item.id} then #{item.salesmanUserName}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" pre_salesman_user_id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.preSalesmanUserId!=null">
                        when id=#{item.id} then #{item.preSalesmanUserId}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" pre_salesman_user_name =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.preSalesmanUserName!=null">
                        when id=#{item.id} then #{item.preSalesmanUserName}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" dept_id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.deptId!=null">
                        when id=#{item.id} then #{item.deptId}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" modifier =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.modifier!=null">
                        when id=#{item.id} then #{item.modifier}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" modifier_id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.modifierId!=null">
                        when id=#{item.id} then #{item.modifierId}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" mtime =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.mtime!=null">
                        when id=#{item.id} then #{item.mtime}
                    </if>
                </foreach>
            </trim>
        </trim>

        where
        id in
        <foreach collection="list" separator="," item="item" open="(" close=")">
            #{item.id}
        </foreach>
    </update>

    <update id="removeAttentionProject">
        DELETE FROM project_attention
        WHERE
        user_id = #{param.userId}
        AND project_id = #{param.projectId}
    </update>


    <select id="selectUpdateList" resultType="com.gok.pboot.pms.entity.vo.ProjectUpdataVO">
        SELECT
        a.id AS 'id',
        a.xmcy AS 'xmcy'
        FROM mhour_project a
        <where>
            a.del_flag = 0 AND a.xmcy IS NOT NULL AND a.xmcy NOT LIKE 'XM-17项目结项申请-%'
            <if test=" para !='1' ">
                AND (( ctime &lt; date_sub(sysdate(),interval 15 minute) and ctime IS NOT NULL )
                OR (mtime &lt; date_sub(sysdate(),interval 15 minute) and mtime IS NOT NULL ))
            </if>
        </where>
    </select>

    <select id="filterIdsByEntryApprovalStatusTag" resultType="java.lang.Long">
        SELECT project.id AS 'id'
        FROM mhour_project project
        WHERE
        project.del_flag = 0
        AND <if test="tag != 1">NOT</if> EXISTS (
        SELECT entry.project_id
        FROM mhour_daily_paper_entry entry
        WHERE
        entry.del_flag = 0
        AND
        entry.project_id = project.id
        AND
        <choose>
            <when test="tag == 1">
                entry.approval_status = 2
            </when>
            <when test="tag == 2">
                entry.approval_status != 4
            </when>
        </choose>
        )
        <if test="filter.projectStatus != null">
            AND project.project_status = #{filter.projectStatus}
        </if>

    </select>

    <select id="findIdByApprovalStatusTagForProjectHourSum" resultType="java.lang.Long"></select>

    <select id="pageProjectHourDetails" resultType="com.gok.pboot.pms.entity.vo.ProjectHourDetailsFindPageVO">
        select
        hp.id projectId,
        hdpe.task_id taskId,
        hdpe.daily_paper_id dailyPaperId,
        hp.item_name projectName,
        hdpe.task_name taskName,
        hp.project_status projectStatus,
        hdpe.user_real_name username,
        hdpe.submission_date submissionDate,
        hdpe.normal_hours normalHours,
        hdpe.added_hours addedHours,
        hdpe.description workContent,
        -- getYesterDayPlan是一个自定义函数，函数大概意思就是按用户、项目、任务去查询距离当前条数据最近的日报的明日计划
        getYesterDayPlan(hdpe.user_id, hdpe.project_id, hdpe.task_id, hdpe.submission_date) yesterdayPlan
        FROM project_info hp
        LEFT JOIN mhour_daily_paper_entry hdpe ON hp.id = hdpe.project_id
        WHERE hdpe.del_flag = 0
        AND hp.id = #{filter.projectId}
        <if test="filter.startTime != null and filter.endTime != null">
            AND hdpe.submission_date BETWEEN #{filter.startTime} AND #{filter.endTime}
        </if>
        <if test="filter.taskName != null and filter.taskName != ''">
            AND hdpe.task_name like concat('%', #{filter.taskName}, '%')
        </if>
        <if test="filter.username != null and filter.username != ''">
            AND hdpe.user_real_name like concat('%', #{filter.username}, '%')
        </if>
    </select>

    <select id="delivererFindPage" resultType="com.gok.pboot.pms.entity.vo.DelivererFindPageVO">
        SELECT
        hpdh.project_id projectId,
        hpdh.project_name projectName,
        hpdh.user_real_name personName,
        hpdh.attendance_days attendanceDays,
        hpdh.project_consumed projectConsumed,
        hpdh.remark remark,
        hpdh.executor_user_real_name executorUserRealName,
        hpdh.normal_work_days normalWorkDays,
        hpdh.rest_work_days restWorkDays,
        hpdh.holidays_work_days holidaysWorkDays,
        hpdh.ompensatory_days ompensatoryDays
        FROM mhour_personnel_delivery_hour hpdh
        WHERE hpdh.del_flag = 0
        <if test="filter.exportName != null and filter.exportName != ''">
            AND hpdh.executor_user_real_name like concat('%', #{filter.exportName}, '%')
        </if>
        <if test="filter.projectId != null">
            AND hpdh.project_id = #{filter.projectId}
        </if>
        <if test="filter.month != null">
            AND hpdh.reuse_date = concat(#{filter.month}, '-01')
        </if>
        <if test="approvalStatus != null">
            AND hpdh.approval_status = #{approvalStatus}
        </if>
        AND (
        hpdh.executor_user_id = #{filter.userId}
        <if test="filter.ids != null and filter.ids.size() > 0">
            OR hpdh.id in
            <foreach collection="filter.ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="filter.projectIds != null and filter.projectIds.size() > 0">
            OR hpdh.project_id in
            <foreach collection="filter.projectIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        )
        ORDER BY hpdh.project_name
    </select>


    <select id="delivererFindIdPage" resultType="java.lang.Long">
        SELECT
        hpdh.id,
        hpdh.revenue_dept_id as 'dept_id'
        FROM mhour_personnel_delivery_hour hpdh
        WHERE hpdh.del_flag = 0
        <if test="filter.flag == null and filter.userId != null">
            AND hpdh.executor_user_id = #{filter.userId}
        </if>
        <if test="filter.exportName != null and filter.exportName != ''">
            AND hpdh.executor_user_real_name like concat('%', #{filter.exportName}, '%')
        </if>
        <if test="filter.projectId != null">
            AND hpdh.project_id = #{filter.projectId}
        </if>
        <if test="filter.month != null">
            AND hpdh.reuse_date = concat(#{filter.month}, '-01')
        </if>
        <if test="approvalStatus != null">
            AND hpdh.approval_status = #{approvalStatus}
        </if>
    </select>

    <select id="delivererExport" resultType="com.gok.pboot.pms.entity.vo.DelivererFindPageVO">
        SELECT
        hpdh.project_id projectId,
        hpdh.project_name projectName,
        hpdh.user_real_name personName,
        hpdh.attendance_days attendanceDays,
        hpdh.project_consumed projectConsumed,
        hpdh.remark remark,
        hpdh.executor_user_real_name executorUserRealName,
        hpdh.normal_work_days normalWorkDays,
        hpdh.rest_work_days restWorkDays,
        hpdh.holidays_work_days holidaysWorkDays,
        hpdh.ompensatory_days ompensatoryDays
        FROM mhour_personnel_delivery_hour hpdh
        WHERE hpdh.del_flag = 0
        <if test="filter.flag == null and filter.userId != null">
            AND hpdh.executor_user_id = #{filter.userId}
        </if>
        <if test="filter.exportName != null and filter.exportName != ''">
            AND hpdh.executor_user_real_name like concat('%', #{filter.exportName}, '%')
        </if>
        <if test="filter.projectId != null">
            AND hpdh.project_id = #{filter.projectId}
        </if>
        <if test="filter.month != null">
            AND hpdh.reuse_date = concat(#{filter.month}, '-01')
        </if>
        <if test="approvalStatus != null">
            AND hpdh.approval_status = #{approvalStatus}
        </if>
        ORDER BY hpdh.project_name
    </select>

    <select id="pageCountProjectByIds" resultType="com.gok.pboot.pms.entity.vo.DailyReviewPageVO">
        <!--        SELECT-->
        <!--        *-->
        <!--        FROM-->
        <!--        (-->
        SELECT
        hp.id as projectId,
        hp.project_name as projectName,
        COUNT(hdpe.id) as approvalNum,
        SUM(hdpe.normal_hours) as normalHours,
        SUM(hdpe.added_hours) as addedHours,
        1 as isEdit
        FROM mhour_project hp
        LEFT JOIN mhour_daily_paper_entry hdpe ON hp.id = hdpe.project_id
        WHERE hp.del_flag = 0
        AND hdpe.del_flag = 0
        <if test="filter.auditStatus == 0">
            AND (hdpe.approval_status = 2 OR hdpe.approval_status IS NULL)
        </if>
        <if test="filter.auditStatus == 1">
            AND (hdpe.approval_status IN (1, 3, 4) OR hdpe.approval_status IS NULL)
        </if>
        <if test="projectIds != null and projectIds.size > 0">
            AND hp.id IN
            <foreach collection="projectIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="filter.projectName != null and filter.projectName != ''">
            AND hp.project_name LIKE CONCAT('%', #{filter.projectName}, '%')
        </if>
        <!--<if test="filter.userIds != null and filter.userIds.size() > 0">-->
        <!--    AND hdpe.user_id IN-->
        <!--    <foreach collection="filter.userIds" open="(" separator="," item="userId" close=")">-->
        <!--        #{userId}-->
        <!--    </foreach>-->
        <!--</if>-->
        <if test="filter.startTime != null">
            AND DATE_FORMAT(hdpe.submission_date, '%Y-%m-%d') &gt;= DATE_FORMAT(#{
                filter.startTime},'%Y-%m-%d')
        </if>
        <if test="filter.endTime != null">
            AND DATE_FORMAT(hdpe.submission_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{
                filter.endTime},'%Y-%m-%d')
        </if>
        GROUP BY hp.id
        <!--   如果存在下级，查询下级的项目     -->
        <!--        <if test="filter.userIds != null and filter.userIds.size > 0">-->
        <!--            UNION ALL-->
        <!--            SELECT-->
        <!--            hp.id as projectId,-->
        <!--            hp.project_name as projectName,-->
        <!--            COUNT(hdpe.id) as approvalNum,-->
        <!--            SUM(hdpe.normal_hours) as normalHours,-->
        <!--            SUM(hdpe.added_hours) as addedHours,-->
        <!--            0 as isEdit-->
        <!--            FROM mhour_project hp-->
        <!--            LEFT JOIN mhour_daily_paper_entry hdpe ON hp.id = hdpe.project_id and hdpe.user_id in-->
        <!--            <foreach collection="filter.userIds" item="id" open="(" separator="," close=")">-->
        <!--                #{id}-->
        <!--            </foreach>-->
        <!--            WHERE hp.del_flag = 0-->
        <!--            AND hdpe.del_flag = 0-->
        <!--            <if test="filter.auditStatus == 0">-->
        <!--                AND (hdpe.approval_status = 2 OR hdpe.approval_status IS NULL)-->
        <!--            </if>-->
        <!--            <if test="filter.auditStatus == 1">-->
        <!--                AND (hdpe.approval_status IN (1, 3, 4) OR hdpe.approval_status IS NULL)-->
        <!--            </if>-->
        <!--            <if test="projectIds != null and projectIds.size > 0">-->
        <!--                AND hp.id not IN-->
        <!--                <foreach collection="projectIds" item="id" open="(" separator="," close=")">-->
        <!--                    #{id}-->
        <!--                </foreach>-->
        <!--            </if>-->
        <!--            <if test="filter.projectName != null and filter.projectName != ''">-->
        <!--                AND hp.project_name LIKE CONCAT('%', #{filter.projectName}, '%')-->
        <!--            </if>-->
        <!--            GROUP BY hp.id-->
        <!--        </if>-->
        <!--        ) a-->
        ORDER BY
        projectName
    </select>

    <select id="selectProjectById" resultType="com.gok.pboot.pms.entity.vo.DailyReviewPageVO">
        SELECT
        hp.id as projectId,
        hp.project_name as projectName,
        COUNT(hdpe.id) as approvalNum,
        SUM(IFNULL(hdpe.normal_hours, 0)) as normalHours,
        SUM(IFNULL(hdpe.added_hours, 0)) as addedHours
        FROM mhour_project hp
        LEFT JOIN mhour_daily_paper_entry hdpe ON hp.id = hdpe.project_id
        WHERE hp.del_flag = 0
        AND (hdpe.del_flag = 0 OR hdpe.del_flag IS NULL)
        <if test="viewTotalDto.projectId != null">
            AND hp.id = #{viewTotalDto.projectId}
        </if>
        <if test="viewTotalDto.auditStatus == 0">
            AND (hdpe.approval_status = 2 OR hdpe.approval_status IS NULL)
        </if>
        <if test="viewTotalDto.auditStatus == 1">
            AND (hdpe.approval_status IN (1, 3, 4) OR hdpe.approval_status IS NULL)
        </if>
        <if test="viewTotalDto.startTime != null">
            AND DATE_FORMAT(hdpe.submission_date, '%Y-%m-%d') &gt;= DATE_FORMAT(#{viewTotalDto.startTime}, '%Y-%m-%d')
        </if>
        <if test="viewTotalDto.endTime != null">
            AND DATE_FORMAT(hdpe.submission_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{viewTotalDto.endTime}, '%Y-%m-%d')
        </if>
        <if test="userIdList != null and userIdList.size() > 0">
            AND hdpe.user_id IN
            <foreach collection="userIdList" open="(" separator="," item="userId" close=")">
                #{userId}
            </foreach>
        </if>
        <if test="viewTotalDto.taskName != null and viewTotalDto.taskName != ''">
            AND hdpe.task_name like concat('%', #{viewTotalDto.taskName}, '%')
        </if>
        <if test="viewTotalDto.username != null and viewTotalDto.username != ''">
            AND hdpe.user_real_name like concat('%', #{viewTotalDto.username}, '%')
        </if>
        GROUP BY hp.id
    </select>

    <select id="selectPageReuseAndDelivery" resultType="com.gok.pboot.pms.entity.vo.DailyReviewPageVO">
        SELECT
        r.`projectId` projectId,
        r.`projectName` projectName,
        COUNT(r.`personnelId`) approvalNum,
        SUM(r.`aggregatedDays`) aggregatedDays
        FROM (
        SELECT
        hp.id projectId,
        hp.project_name projectName,
        hpr.id personnelId,
        hpr.aggregated_days aggregatedDays
        FROM mhour_project hp
        LEFT JOIN mhour_personnel_reuse hpr ON hp.id = hpr.project_id
        WHERE hp.del_flag = 0
        AND hpr.del_flag = 0
        <if test="projectIds != null and projectIds.size > 0">
            AND hp.id IN
            <foreach collection="projectIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="filter.auditStatus == 0">
            AND hpr.approval_status = 2
        </if>
        <if test="filter.auditStatus == 1">
            AND hpr.approval_status IN (1, 3, 4)
        </if>
        <if test="filter.projectName != null and filter.projectName != ''">
            AND hp.project_name LIKE CONCAT('%', #{filter.projectName}, '%')
        </if>
        UNION ALL
        SELECT
        hp.id projectId,
        hp.project_name projectName,
        hpdh.id personnelId,
        hpdh.attendance_days aggregatedDays
        FROM mhour_project hp
        LEFT JOIN mhour_personnel_delivery_hour hpdh ON hp.id = hpdh.project_id
        WHERE hp.del_flag = 0
        AND hpdh.del_flag = 0
        <if test="projectIds != null and projectIds.size > 0">
            AND hp.id IN
            <foreach collection="projectIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="filter.auditStatus == 0">
            AND hpdh.approval_status = 2
        </if>
        <if test="filter.auditStatus == 1">
            AND hpdh.approval_status IN (1, 3, 4)
        </if>
        <if test="filter.projectName != null and filter.projectName != ''">
            AND hpdh.project_name LIKE CONCAT('%', #{filter.projectName}, '%')
        </if>
        ) r
        GROUP BY r.`projectId`
    </select>

    <select id="reuseAndDeliveryViewTotal" resultType="com.gok.pboot.pms.entity.vo.DailyReviewPageVO">
        SELECT
        r.`projectId` projectId,
        r.`projectName` projectName,
        COUNT(r.`personnelId`) approvalNum,
        SUM(r.`aggregatedDays`) aggregatedDays
        FROM (
        SELECT
        hp.id projectId,
        hp.project_name projectName,
        hpr.id personnelId,
        IFNULL(hpr.aggregated_days, 0) aggregatedDays
        FROM mhour_project hp
        LEFT JOIN mhour_personnel_reuse hpr ON hp.id = hpr.project_id
        WHERE hp.del_flag = 0
        AND hp.id = #{projectId}
        AND (hpr.del_flag = 0 OR hpr.del_flag IS NULL)
        <if test="auditStatus == 0">
            AND (hpr.approval_status = 2 OR hpr.approval_status IS NULL)
        </if>
        <if test="auditStatus == 1">
            AND (hpr.approval_status IN (1, 3, 4) OR hpr.approval_status IS NULL)
        </if>
        <if test="startTime != null and startTime != '' ">
            AND hpr.reuse_date &gt;= concat(#{startTime}, '-01')
        </if>
        <if test="endTime != null and endTime != ''">
            AND hpr.reuse_date &lt;= concat(#{endTime}, '-01')
        </if>
        UNION ALL
        SELECT
        hp.id projectId,
        hp.project_name projectName,
        hpdh.id personnelId,
        IFNULL(hpdh.attendance_days, 0) aggregatedDays
        FROM mhour_project hp
        LEFT JOIN mhour_personnel_delivery_hour hpdh ON hp.id = hpdh.project_id
        WHERE hp.del_flag = 0
        AND hp.id = #{projectId}
        AND (hpdh.del_flag = 0 OR hpdh.del_flag IS NULL)
        <if test="auditStatus == 0">
            AND (hpdh.approval_status = 2 OR hpdh.approval_status IS NULL)
        </if>
        <if test="auditStatus == 1">
            AND (hpdh.approval_status IN (1, 3, 4) OR hpdh.approval_status IS NULL)
        </if>
        <if test="startTime != null and startTime != '' ">
            AND hpdh.reuse_date &gt;= concat(#{startTime}, '-01')
        </if>
        <if test="endTime != null and endTime != ''">
            AND hpdh.reuse_date &lt;= concat(#{endTime}, '-01')
        </if>
        ) r
        GROUP BY r.`projectId`
    </select>

    <select id="findByPermissions" resultType="com.gok.pboot.pms.entity.vo.ProjectVO">
        SELECT * from(
        SELECT
        a.id AS 'id',
        a.item_name AS 'projectName',
        a.item_no AS 'code',
        a.project_status AS 'projectStatus',
        a.first_level_department_id AS projectDepartmentId,
        a.first_level_department AS projectDepartment,
        b.id AS 'collectId',
        b.ctime AS 'collectTime',
        b.creator_id AS 'collectUserId'
        FROM project_info a LEFT JOIN mhour_project_collect b on a.id=b.project_id and b.del_flag = 0
        and b.creator_id=#{filter.userId}
        <if test="filter.collectType != null">
            and b.collect_type=#{filter.collectType}
        </if>
        <where>
            a.item_name IS NOT NULL
            AND a.item_no IS NOT NULL
            <if test="filter.deptId != null and filter.deptId != '' ">
                AND a.first_level_department_id=#{filter.deptId}
            </if>
            <if test="filter.projectIds != null and filter.projectIds.size() != 0">
                AND a.id IN
                <foreach collection="filter.projectIds" item="pId" open="(" separator="," close=")">
                    #{pId}
                </foreach>
            </if>
            <if test="filter.projectName != null and filter.projectName != '' ">
                AND a.item_name like concat('%',#{filter.projectName},'%')
            </if>
            <if test="filter.projectStatus != null and filter.projectStatus.size() != 0">
                AND a.project_status IN
                <foreach collection="filter.projectStatus" item="projectStatus" open="(" separator="," close=")">
                    #{projectStatus}
                </foreach>
            </if>
        </where>
        ORDER BY b.ctime desc,FIELD(project_status,0,2,3,4,5,6,1,7) ,item_name) b
    </select>

    <select id="reuseAndDeliveryViewPage" resultType="com.gok.pboot.pms.entity.vo.DailyReviewReuseAndDeliveryPageVO">
        SELECT
        *
        FROM (
        SELECT
        hpr.id id,
        hpr.user_real_name userRealName,
        DATE_FORMAT( hpr.reuse_date, '%Y-%m' ) month,
        hpr.aggregated_days aggregatedDays,
        '人才复用' type,
        hpr.approval_status approvalStatus,
        hpr.modifier approvalName,
        (case when hpr.approval_status = 1 then '已退回'
        when hpr.approval_status = 2 then '待审核'
        when hpr.approval_status = 3 then '不通过'
        when hpr.approval_status = 4 then '已通过'
        end) approvalStatusName,
        hf.filed filed
        FROM mhour_personnel_reuse hpr
        LEFT JOIN mhour_filing hf ON hf.`year` = DATE_FORMAT(hpr.reuse_date, '%Y') AND hf.`month` =
        DATE_FORMAT(hpr.reuse_date, '%m')
        WHERE hpr.del_flag = 0
        AND hpr.project_id = #{filter.projectId}
        <if test="filter.auditStatus == 0">
            AND (hpr.approval_status = 2 OR hpr.approval_status IS NULL)
        </if>
        <if test="filter.auditStatus == 1">
            AND (hpr.approval_status IN (1, 3, 4) OR hpr.approval_status IS NULL)
        </if>
        <if test="filter.startTime != null and filter.startTime != '' ">
            AND hpr.reuse_date &gt;= concat(#{filter.startTime}, '-01')
        </if>
        <if test="filter.endTime != null and filter.endTime != ''">
            AND hpr.reuse_date &lt;= concat(#{filter.endTime}, '-01')
        </if>
        <if test="filter.username != null and filter.username != ''">
            AND hpr.user_real_name LIKE CONCAT('%', #{filter.username}, '%')
        </if>
        UNION ALL
        SELECT
        hpdh.id id,
        hpdh.user_real_name userRealName,
        DATE_FORMAT( hpdh.reuse_date, '%Y-%m' ) month,
        hpdh.attendance_days aggregatedDays,
        '交付人员' type,
        hpdh.approval_status approvalStatus,
        hpdh.modifier approvalName,
        (case when hpdh.approval_status = 1 then '已退回'
        when hpdh.approval_status = 2 then '待审核'
        when hpdh.approval_status = 3 then '不通过'
        when hpdh.approval_status = 4 then '已通过'
        end) approvalStatusName,
        hf.filed filed
        FROM mhour_personnel_delivery_hour hpdh
        LEFT JOIN mhour_filing hf ON hf.`year` = DATE_FORMAT(hpdh.reuse_date, '%Y') AND hf.`month` =
        DATE_FORMAT(hpdh.reuse_date, '%m')
        WHERE hpdh.del_flag = 0
        AND hpdh.project_id = #{filter.projectId}
        <if test="filter.auditStatus == 0">
            AND (hpdh.approval_status = 2 OR hpdh.approval_status IS NULL)
        </if>
        <if test="filter.auditStatus == 1">
            AND (hpdh.approval_status IN (1, 3, 4) OR hpdh.approval_status IS NULL)
        </if>
        <if test="filter.startTime != null and filter.startTime != '' ">
            AND hpdh.reuse_date &gt;= concat(#{filter.startTime}, '-01')
        </if>
        <if test="filter.endTime != null and filter.endTime != ''">
            AND hpdh.reuse_date &lt;= concat(#{filter.endTime}, '-01')
        </if>
        <if test="filter.username != null and filter.username != ''">
            AND hpdh.user_real_name LIKE CONCAT('%', #{filter.username}, '%')
        </if>
        ) r
    </select>
    <select id="selectProjectsByIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mhour_project a
        WHERE a.del_flag = 0
        AND a.id IN
        <foreach collection="projectIds" item="projectId" separator="," open="(" close=")">
            #{projectId}
        </foreach>
    </select>

    <select id="countUnauditedNumber" resultType="java.lang.Integer">
        SELECT count(id)
        FROM (
        SELECT id
        FROM mhour_personnel_delivery_hour
        WHERE del_flag = 0
        AND approval_status = 2
        <if test="projectIds != null and projectIds.size > 0">
            AND project_id IN
            <foreach collection="projectIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        UNION ALL
        SELECT id
        FROM mhour_personnel_reuse
        WHERE del_flag = 0
        AND approval_status = 2
        <if test="projectIds != null and projectIds.size > 0">
            AND project_id IN
            <foreach collection="projectIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        ) r
    </select>

    <select id="findByOaSalesTask" resultType="com.gok.pboot.pms.entity.Project">
        SELECT
        a.id AS 'id',
        a.salesman_user_id AS 'salesmanUserId',
        a.manager_user_id AS 'managerUserId',
        a.pre_salesman_user_id AS 'preSalesmanUserId'
        FROM mhour_project a
        <where>
            a.del_flag = 0
            <if test="para !=1 ">
                AND (( ctime &gt; date_sub(sysdate(),interval 30 minute) and ctime IS NOT NULL )
                OR (mtime &gt; date_sub(sysdate(),interval 30 minute) and mtime IS NOT NULL ))
            </if>
        </where>
    </select>

    <select id="findAttentionProjectByUserId" resultType="com.gok.pboot.pms.entity.domain.ProjectAttention">
        select
            id,
            user_id,
            project_id
        from project_attention
        where del_flag = 0 and user_id = #{userId}
    </select>

    <select id="findAttentionProjectPage" resultType="com.gok.pboot.pms.entity.vo.ProjectAttentionVO">
        select
            a.id attentionId,
            a.project_id id,
            p.item_name projectName,
            p.project_status projectStatus
        from project_attention a
         left join project_info p on  a.project_id=p.id
        where a.del_flag = 0
          and a.user_id = #{filter.userId}
        ORDER BY p.id DESC
    </select>

    <select id="getAttentionProjectList" resultType="com.gok.pboot.pms.entity.vo.ProjectVO">
        select
            a.project_id id
        from project_attention a
        where a.del_flag = 0
          and a.user_id = #{userId}
    </select>


</mapper>
