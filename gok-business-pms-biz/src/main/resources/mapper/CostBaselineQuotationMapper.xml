<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.cost.mapper.CostBaselineQuotationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.gok.pboot.pms.cost.entity.domain.CostBaselineQuotation">
        <id column="id" property="id"/>
        <result column="project_id" property="projectId"/>
        <result column="version_id" property="versionId"/>
        <result column="audit_status" property="auditStatus"/>
        <result column="income_amount_included_tax" property="incomeAmountIncludedTax"/>
        <result column="income_amount_excluding_tax" property="incomeAmountExcludingTax"/>
        <result column="expected_early_investment_cost" property="expectedEarlyInvestmentCost"/>
        <result column="creator" property="creator"/>
        <result column="creator_id" property="creatorId"/>
        <result column="modifier" property="modifier"/>
        <result column="modifier_id" property="modifierId"/>
        <result column="ctime" property="ctime"/>
        <result column="mtime" property="mtime"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, project_id, version_id, audit_status, income_amount_included_tax, income_amount_excluding_tax, expected_early_investment_cost, creator, creator_id, modifier, modifier_id, ctime, mtime, del_flag
    </sql>
    <select id="getGrossProfitMeasurementVersionInfo"
            resultType="com.gok.pboot.pms.cost.entity.vo.CostBaselineQuotationVO">
        select cbq.project_id,
        cbq.version_id,
        cmv.version_name,
        cmv.version_type,
        cbq.audit_status,
        cbq.income_amount_included_tax,
        cbq.income_amount_excluding_tax,
        cbq.expected_early_investment_cost,
        cbq.ctime,
        cbq.creator,
        cmv.operator_name,
        cmv.mtime,
        cmv.refuse_reason
        from cost_baseline_quotation as cbq
        left join cost_manage_version as cmv on cbq.version_id = cmv.id
        where cmv.version_type = ${@<EMAIL>}
        and cbq.project_id = #{projectId}
        <if test="versionId != null">
            and cbq.version_id = #{versionId}
        </if>
        <if test="versionId == null">
            and cmv.version_status = 0
        </if>
        <if test="auditStatus != null">
            and cbq.audit_status = #{auditStatus}
        </if>
    </select>
    <select id="getGrossProfitMeasurementHistoryVersionInfo"
            resultType="com.gok.pboot.pms.cost.entity.vo.CostBaselineQuotationHistoryVersionVO">
        select cmv.project_id,
               cmv.id as versionId,
               cmv.version_name,
               cbq.audit_status,
               cbq.mtime,
               cbq.ctime,
               cmv.creator,
               cmv.operator_role
        from cost_manage_version as cmv
                 left join cost_baseline_quotation as cbq on cmv.id = cbq.version_id
        where cmv.del_flag = 0
          and cmv.version_type = ${@<EMAIL>}
          and cmv.project_id = #{projectId}
    </select>

    <update id="updateByVersionId">
        update cost_baseline_quotation
        set audit_status = #{auditStatus}
        where version_id = #{versionId}
    </update>

    <select id="getCostBaselineQuotationLast"
            resultType="com.gok.pboot.pms.cost.entity.vo.CostBaselineQuotationVO">
        select cbq.project_id,
        cbq.version_id,
        cmv.version_name,
        cmv.version_type,
        cbq.audit_status,
        cbq.income_amount_included_tax,
        cbq.income_amount_excluding_tax,
        cbq.expected_early_investment_cost,
        cbq.ctime,
        cbq.creator,
        cmv.operator_name,
        cmv.mtime,
        cmv.refuse_reason
        from cost_baseline_quotation as cbq
        left join cost_manage_version as cmv on cbq.version_id = cmv.id
        where cmv.version_type = ${@<EMAIL>}
        and cbq.project_id = #{projectId}
        and cbq.audit_status in (${@<EMAIL>},
                                 ${@<EMAIL>})
        ORDER BY cbq.ctime desc
        limit 1
    </select>
</mapper>
