<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.mapper.ProjectTaskMapper">

    <resultMap id="ProjectTaskResultMap" type="com.gok.pboot.pms.entity.domain.ProjectTask">
        <result property="parentId" column="parent_id"/>
        <result property="projectId" column="project_id"/>
        <result property="treeLevel" column="tree_level"/>
        <result property="treePathIds" column="tree_path_ids"/>
        <result property="title" column="title"/>
        <result property="managerUserId" column="manager_user_id"/>
        <result property="managerUserName" column="manager_user_name"/>
        <result property="state" column="state"/>
        <result property="expectedStartTime" column="expected_start_time"/>
        <result property="expectedEndTime" column="expected_end_time"/>
        <result property="actualStartTime" column="actual_start_time"/>
        <result property="actualEndTime" column="actual_end_time"/>
        <result property="milestone" column="milestone"/>
        <result property="content" column="content"/>
        <result property="groupId" column="group_id"/>
    </resultMap>

    <sql id="baseColumnList">
        a.id,
        a.parent_id,
        a.project_id,
        a.tree_level,
        a.tree_path_ids,
        a.title,
        a.manager_user_id,
        a.manager_user_name,
        a.state,
        a.expected_start_time,
        a.expected_end_time,
        a.actual_start_time,
        a.actual_end_time,
        a.milestone,
        a.content,
        a.group_id
    </sql>

    <insert id="save" parameterType="com.gok.pboot.pms.entity.domain.ProjectTask">
        INSERT INTO project_task
        (id, parent_id, project_id, tree_level, tree_path_ids,
         title, manager_user_id, manager_user_name, state,
         expected_start_time, expected_end_time, actual_start_time, actual_end_time,
         milestone, content, group_id, creator, creator_id,
         modifier, modifier_id, ctime, mtime)
        VALUES (#{id}, #{parentId}, #{projectId}, #{treeLevel}, #{treePathIds},
                #{title}, #{managerUserId}, #{managerUserName}, #{state},
                #{expectedStartTime}, #{expectedEndTime}, #{actualStartTime}, #{actualEndTime},
                #{milestone}, #{content}, #{groupId}, #{creator}, #{creatorId},
                #{modifier}, #{modifierId}, #{ctime}, #{mtime})
    </insert>

    <update id="update">
        update project_task
        <set>
            <if test="title != null">title = #{title},</if>
            <if test="managerUserId != null">manager_user_id = #{managerUserId},manager_user_name =
                #{managerUserName},</if>
            <if test="expectedStartTime != null">expected_start_time = #{expectedStartTime},</if>
            <if test="expectedEndTime != null">expected_end_time = #{expectedEndTime},</if>
            actual_start_time = #{actualStartTime},
            actual_end_time = #{actualEndTime},
            <if test="milestone != null">milestone = #{milestone},</if>
            content = #{content},
            <if test="modifier != null">modifier = #{modifier},</if>
            <if test="modifierId != null">modifier_id = #{modifierId},</if>
            <if test="mtime != null">mtime = #{mtime},</if>
        </set>
        where id = #{id}
    </update>

    <update id="delete">
        update project_task
        set del_flag = 1
        where id = #{id}
    </update>

    <update id="groupIdInit">
        update project_task
        set group_id = 0
        where del_flag = 0 and group_id = #{groupId}
    </update>
    <update id="groupIdUpdate">
        update project_task
        set group_id = #{groupId}
        where del_flag = 0 and id = #{taskId}
    </update>

    <update id="rollbackEndTime">
        update project_task
        set actual_end_time = null
        where del_flag = 0 and id = #{id}
    </update>

    <!--<select id="findList" resultMap="ProjectTaskResultMap">-->
    <!--    SELECT-->
    <!--    <include refid="baseColumnList"/>-->
    <!--    FROM project_task a-->
    <!--    <where>-->
    <!--        del_flag = 0-->
    <!--        <if test="filter.state != null">-->
    <!--            AND state = #{filter.state}-->
    <!--        </if>-->
    <!--    </where>-->
    <!--</select>-->

    <select id="findList" resultMap="ProjectTaskResultMap">
        SELECT
        <include refid="baseColumnList"/>
        FROM project_task a
        <where>
            del_flag = 0
            <if test="filter.projectId != null">
                AND project_id = #{filter.projectId}
            </if>
            <if test="filter.title != null and filter.title !=''">
                AND title LIKE CONCAT('%', #{filter.title},'%')
            </if>
            <if test="filter.managerUserName != null and filter.managerUserName !=''">
                AND manager_user_name LIKE CONCAT('%', #{filter.managerUserName},'%')
            </if>
            <if test="filter.treeLevel != null and filter.treeLevel !=''">
                AND tree_level <![CDATA[ <= ]]> #{filter.treeLevel}
            </if>
            <if test="filter.chargeUserId != null and filter.chargeUserId !=''">
                AND manager_user_id = #{filter.chargeUserId}
            </if>

        </where>
        ORDER BY tree_path_ids,expected_start_time ASC
    </select>

    <select id="findOneById" resultType="com.gok.pboot.pms.entity.domain.ProjectTask">
        SELECT
        <include refid="baseColumnList"/>
        FROM project_task a
        WHERE
        del_flag = 0
        AND id = #{id}
    </select>

    <select id="getChildCount" resultType="java.lang.Integer">
        SELECT
        COUNT(*)
        FROM
        (
        SELECT
        id,tree_path_ids
        FROM
        project_task
        WHERE
        del_flag = 0
        AND tree_path_ids LIKE CONCAT('%', #{id}, '%')
        )a
    </select>

    <select id="findByUserIdAndProjectIdForEntry" resultType="com.gok.pboot.pms.common.join.TaskInDailyPaperEntry">
        SELECT task.id    AS id,
               task.title AS taskName
        FROM project_task task
                 LEFT JOIN project_task_user tu
                           ON
                               tu.task_id = task.id
                 LEFT JOIN mhour_project project
                           ON
                               project.id = task.project_id
        WHERE tu.user_id = #{userId}
          AND project.id = #{projectId}
          AND tu.del_flag = 0
          AND project.del_flag = 0
          AND task.del_flag = 0
        UNION DISTINCT
        SELECT project.id AS projectId,
        task.id    AS id,
        task.title AS taskName
        FROM project_task task
        LEFT JOIN mhour_project project
        ON
        project.id = task.project_id
        WHERE task.manager_user_id = #{userId}
          AND project.id = #{projectId}
          AND project.del_flag = 0
          AND task.del_flag = 0
    </select>

    <select id="findTitleById" resultType="java.lang.String">
        SELECT title
        FROM project_task
        WHERE
            del_flag = 0
        AND
            id = #{id}
    </select>

    <select id="getCountByGroupId" resultType="java.lang.Integer">
        SELECT
            count(*)
        FROM
            project_task
        WHERE
            group_id = #{groupId}
          AND del_flag = 0;
    </select>

    <select id="findRootPage" resultType="com.gok.pboot.pms.entity.domain.ProjectTask">
        SELECT <include refid="baseColumnList"/>
        FROM project_task a
        WHERE del_flag = 0
          AND tree_level = 1
        <if test="filter.projectId != null">
            AND project_id = #{filter.projectId}
        </if>
    </select>

    <select id="findChargeTaskNum" resultType="java.lang.Integer">
        select count(*) as taskNum
        from project_task
        where del_flag = 0 and manager_user_id = #{managerUserId}
    </select>

    <select id="findChargeList" resultType="com.gok.pboot.pms.entity.domain.ProjectTask">
        SELECT
        <include refid="baseColumnList"/>
        FROM project_task a left join project_info b on a.project_id = b.id
        <where>
            del_flag = 0
            <if test="filter.chargeUserId != null and filter.chargeUserId !=''">
                AND a.manager_user_id = #{filter.chargeUserId}
            </if>
            <if test="filter.projectName != null and filter.projectName !=''">
                AND b.item_name LIKE CONCAT('%',#{filter.projectName},'%')
            </if>

        </where>
        ORDER BY tree_path_ids
    </select>

<!--    <select id="findByUserIdAndProjectIdForEntryVos"-->
<!--            resultType="com.gok.pboot.pms.entity.vo.TaskInDailyPaperEntryListVo">-->
<!--        SELECT task.id    AS id,-->
<!--               task.title AS taskName-->
<!--        FROM project_task task-->
<!--                 LEFT JOIN project_task_user tu-->
<!--                           ON-->
<!--                               tu.task_id = task.id-->
<!--                 LEFT JOIN mhour_project project-->
<!--                           ON-->
<!--                               project.id = task.project_id-->
<!--        WHERE 1=1-->
<!--          AND tu.user_id = #{userId}-->
<!--        <if test="list != null and list.size() > 0">-->
<!--            AND project.id IN-->
<!--            <foreach collection="list" item="item" open="(" separator="," close=")">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--        </if>-->
<!--        AND-->
<!--           tu.del_flag = 0-->
<!--          AND project.del_flag = 0-->
<!--          AND task.del_flag = 0-->
<!--        UNION ALL-->
<!--        SELECT task.id    AS id,-->
<!--               task.title AS taskName-->
<!--        FROM project_task task-->
<!--                 LEFT JOIN mhour_project project-->
<!--                           ON-->
<!--                               project.id = task.project_id-->
<!--        WHERE 1=1-->
<!--          AND task.manager_user_id = #{userId}-->
<!--        <if test="list != null and list.size() > 0">-->
<!--            AND project.id IN-->
<!--            <foreach collection="list" item="item" open="(" separator="," close=")">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--        </if>-->
<!--        AND-->
<!--           project.del_flag = 0-->
<!--          AND task.del_flag = 0-->
<!--    </select>-->
    <select id="findByUserIdAndProjectIdForEntryVos"
            resultType="com.gok.pboot.pms.entity.vo.TaskInDailyPaperEntryListVo">
        SELECT project.id AS projectId,
               task.id    AS id,
               task.title AS taskName
        FROM project_task task
        LEFT JOIN project_task_user tu
        ON
        tu.task_id = task.id
        LEFT JOIN mhour_project project
        ON
        project.id = task.project_id
        WHERE 1=1
        AND tu.user_id = #{userId}
        <if test="list != null and list.size() > 0">
            AND project.id IN
            <foreach collection="list" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="list == null or list.size() == 0">
            AND project.id = null
        </if>
        AND
        tu.del_flag = 0
        AND project.del_flag = 0
        AND task.del_flag = 0
    </select>
    <select id="findByUserIdAndProjectIdForEntryVos2"
            resultType="com.gok.pboot.pms.entity.vo.TaskInDailyPaperEntryListVo">
        SELECT project.id AS projectId,
        task.id    AS id,
        task.title AS taskName
        FROM project_task task
        LEFT JOIN mhour_project project
        ON
        project.id = task.project_id
        WHERE 1=1
        AND task.manager_user_id = #{userId}
        <if test="list != null and list.size() > 0">
            AND project.id IN
            <foreach collection="list" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
          <if test="list == null or list.size() == 0">
              AND project.id = null
          </if>
        AND
        project.del_flag = 0
        AND task.del_flag = 0
    </select>

    <select id="getChildIdList" resultType="java.lang.String">
        select id from project_task
        where parent_id = #{parentId}
    </select>

    <select id="getAllNotStartTask" resultType="com.gok.pboot.pms.entity.domain.ProjectTask">
        SELECT
        <include refid="baseColumnList"/>
        FROM
            project_task a
        WHERE
            del_flag = 0
          AND a.actual_start_time IS NULL
          AND a.actual_end_time IS NULL
    </select>

    <select id="findAll" resultType="com.gok.pboot.pms.entity.domain.ProjectTask">
        SELECT
        <include refid="baseColumnList"/>
        FROM
        project_task a
        WHERE
        a.del_flag = 0
    </select>

    <select id="findToSyncMember" resultType="com.gok.pboot.pms.entity.domain.ProjectStakeholderMember">
        SELECT
        tu.id as id,
        t.project_id as projectId,
        tu.user_id as memberId,
        tu.user_name as memberName,
        NULL as deptName,
        NULL as position,
        4 as roleType,
        NULL as duty,
        NULL as remark,
        0 as syncOaType,
        tu.creator as creator,
        tu.creator_id as creatorId,
        tu.modifier as modifier,
        tu.modifier_id as modifierId,
        tu.ctime as ctime,
        tu.mtime as mtime,
        0 as delFlag
        FROM
        project_task t
        INNER JOIN project_task_user tu ON t.id = tu.task_id
        WHERE
        t.del_flag = 0
        AND tu.del_flag = 0
        AND tu.user_id NOT IN ( SELECT m.member_id FROM project_stakeholder_member m WHERE m.project_id = t.project_id AND m.del_flag = 0 )
        GROUP BY
        t.project_id,
        tu.user_id;
    </select>


</mapper>
