<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.mapper.ProjectMeetingMapper">

    <!--分页查询项目会议纪要-->
    <select id="findListPage" resultMap="BaseResultMapFindPageVo">
        SELECT
        id,
        `name`,
        convener_id,
        convener,
        meeting_date,
        place,
        creator,
        modifier,
        mtime,
        ctime
        FROM project_meeting
        <where>
            del_flag = 0
            <if test="filter.projectId != null ">
                AND project_id = #{filter.projectId}
            </if>
        </where>
        ORDER BY ctime DESC
    </select>

    <!--分页查询项目会议纪要导出所需数据-->
    <select id="findExportPage" resultMap="BaseResultMapExportVo">
        SELECT
        id,
        `name`,
        convener,
        meeting_date,
        CONCAT(start_time, '-', end_time) AS time,
        recorder,
        place,
        member,
        objective,
        process,
        resolution,
        backlog
        FROM project_meeting
        <where>
            del_flag = 0
            <if test="filter.projectId != null ">
                AND project_id = #{filter.projectId}
            </if>
        </where>
        ORDER BY ctime DESC
    </select>

    <!--根据id查询项目会议纪要详情-->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT *
        FROM project_meeting
        <where>
            id = #{id} AND del_flag = 0
        </where>
    </select>

    <!--  批量逻辑删除  -->
    <update id="batchDel">
        UPDATE project_meeting SET del_flag = 1 WHERE id IN
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.gok.pboot.pms.entity.domain.ProjectMeeting">
        <id column="id" property="id"/>
        <result column="project_id" property="projectId"/>
        <result column="name" property="name"/>
        <result column="convener_id" property="convenerId"/>
        <result column="convener" property="convener"/>
        <result column="meeting_date" property="meetingDate"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="recorder_id" property="recorderId"/>
        <result column="recorder" property="recorder"/>
        <result column="place" property="place"/>
        <result column="member" property="member"/>
        <result column="objective" property="objective"/>
        <result column="process" property="process"/>
        <result column="resolution" property="resolution"/>
        <result column="backlog" property="backlog"/>
        <result column="doc_ids" property="docIds"/>
        <result column="doc_names" property="docNames"/>
        <result column="creator" property="creator"/>
        <result column="creator_id" property="creatorId"/>
        <result column="modifier" property="modifier"/>
        <result column="modifier_id" property="modifierId"/>
        <result column="ctime" property="ctime"/>
        <result column="mtime" property="mtime"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

    <!-- 分页查询映射结果 -->
    <resultMap id="BaseResultMapFindPageVo" type="com.gok.pboot.pms.entity.vo.ProjectMeetingFindPageVO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="convener_id" property="convenerId"/>
        <result column="convener" property="convener"/>
        <result column="meeting_date" property="meetingDate"/>
        <result column="place" property="place"/>
        <result column="creator" property="creator"/>
        <result column="modifier" property="modifier"/>
        <result column="mtime" property="mtime"/>
        <result column="ctime" property="ctime"/>
    </resultMap>

    <!-- 导出数据查询映射结果 -->
    <resultMap id="BaseResultMapExportVo" type="com.gok.pboot.pms.entity.vo.ProjectMeetingExportExcelVO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" property="name"/>
        <result column="convener" property="convener"/>
        <result column="meeting_date" property="meetingDate"/>
        <result column="time" property="time"/>
        <result column="recorder" property="recorder"/>
        <result column="place" property="place"/>
        <result column="member" property="member"/>
        <result column="objective" property="objective"/>
        <result column="process" property="process"/>
        <result column="resolution" property="resolution"/>
        <result column="backlog" property="backlog"/>
    </resultMap>

</mapper>