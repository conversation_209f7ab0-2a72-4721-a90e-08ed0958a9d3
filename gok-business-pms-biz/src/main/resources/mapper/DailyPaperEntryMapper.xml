<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.gok.pboot.pms.mapper.DailyPaperEntryMapper">
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        pe.id AS 'id',
        pe.project_id AS 'projectId',
        pe.project_name AS 'projectName',
        pe.task_id AS 'taskId',
        pe.task_name AS 'taskName',
        pe.daily_paper_id AS 'dailyPaperId',
        pe.approval_status AS 'approvalStatus',
        pe.submission_date AS 'submissionDate',
        pe.normal_hours AS 'normalHours',
        pe.added_hours AS 'addedHours',
        pe.work_overtime_hours AS 'workOvertimeHours',
        pe.rest_overtime_hours AS 'restOvertimeHours',
        pe.holiday_overtime_hours AS 'holidayOvertimeHours',
        pe.description AS 'description',
        pe.approval_reason AS 'approvalReason',
        pe.user_id AS 'userId',
        pe.user_real_name AS 'userRealName',
        pe.user_dept_id AS 'userDeptId',
        pe.creator AS 'creator',
        pe.creator_id AS 'creatorId',
        pe.modifier AS 'modifier',
        pe.modifier_id AS 'modifierId',
        pe.ctime AS 'ctime',
        pe.mtime AS 'mtime',
        pe.del_flag AS 'delFlag',
        pe.work_type AS 'workType',
        pe.old_task_flag AS 'oldTaskFlag'
    </sql>

    <insert id="batchSave">
        INSERT INTO mhour_daily_paper_entry
        (
        id,
        project_id,
        project_name,
        task_id,
        task_name,
        daily_paper_id,
        approval_status,
        submission_date,
        normal_hours,
        added_hours,
        work_overtime_hours,
        rest_overtime_hours,
        holiday_overtime_hours,
        description,
        approval_reason,
        user_id,
        user_real_name,
        user_dept_id,
        creator,
        creator_id,
        modifier,
        modifier_id,
        ctime,
        mtime,
        del_flag,
        work_type,
        old_task_flag
        )
        VALUES
        <foreach collection="poList" item="po" separator=",">
            (
            #{po.id},
            #{po.projectId},
            #{po.projectName},
            #{po.taskId},
            #{po.taskName},
            #{po.dailyPaperId},
            #{po.approvalStatus},
            #{po.submissionDate},
            #{po.normalHours},
            #{po.addedHours},
            #{po.workOvertimeHours},
            #{po.restOvertimeHours},
            #{po.holidayOvertimeHours},
            #{po.description},
            #{po.approvalReason},
            #{po.userId},
            #{po.userRealName},
            #{po.userDeptId},
            #{po.creator},
            #{po.creatorId},
            #{po.modifier},
            #{po.modifierId},
            #{po.ctime},
            #{po.mtime},
            #{po.delFlag},
            #{po.workType},
            #{po.oldTaskFlag}
            )
        </foreach>
    </insert>

    <update id="deleteByDailyPaperId">
        UPDATE mhour_daily_paper_entry
        SET del_flag = 1
        WHERE
        del_flag = 0
        AND
        daily_paper_id = #{dailyPaperId}
    </update>

    <insert id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            UPDATE mhour_daily_paper_entry
            SET
            id = #{item.id},
            project_id = #{item.projectId},
            project_name = #{item.projectName},
            task_id = #{item.taskId},
            task_name = #{item.taskName},
            daily_paper_id = #{item.dailyPaperId},
            approval_status = #{item.approvalStatus},
            submission_date = #{item.submissionDate},
            normal_hours = #{item.normalHours},
            added_hours = #{item.addedHours},
            work_overtime_hours = #{item.workOvertimeHours},
            rest_overtime_hours = #{item.restOvertimeHours},
            holiday_overtime_hours = #{item.holidayOvertimeHours},
            description = #{item.description},
            approval_reason = #{item.approvalReason},
            user_id = #{item.userId},
            user_real_name = #{item.userRealName},
            user_dept_id = #{item.userDeptId},
            modifier = #{item.modifier},
            modifier_id = #{item.modifierId},
            mtime = #{item.mtime},
            del_flag = #{item.delFlag},
            work_type = #{item.workType},
            old_task_flag = #{item.oldTaskFlag}
            WHERE
            id = #{item.id}
        </foreach>
    </insert>

    <select id="findList" resultType="com.gok.pboot.pms.entity.DailyPaperEntry">
        SELECT
            <include refid="Base_Column_List"/>
        FROM mhour_daily_paper_entry pe
        LEFT JOIN mhour_roster r ON pe.user_id = r.id
        <where>
            pe.del_flag = 0
            <if test="filter.onlyActiveFlag == true">
                AND pe.approval_status NOT IN (
                    ${@<EMAIL>},
                    ${@<EMAIL>}
                )
            </if>
            <if test="filter.approvalStatus != null">
                AND pe.approval_status = #{filter.approvalStatus}
            </if>
            <if test="filter.approvalStatusList != null">
                AND pe.approval_status IN
                <foreach collection="filter.approvalStatusList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="filter.projectId != null">
                AND pe.project_id = #{filter.projectId}
            </if>
            <if test="filter.projectIds != null and filter.projectIds.size() > 0">
                AND pe.project_id IN
                <foreach collection="filter.projectIds" item="pId" open="(" separator="," close=")">
                    #{pId}
                </foreach>
            </if>
            <if test="filter.projectName != null">
                <bind name="projectNameLike" value="'%' + filter.projectName + '%'"/>
                AND pe.project_name LIKE #{projectNameLike}
            </if>
            <if test="filter.taskName != null">
                <bind name="taskNameLike" value="'%' + filter.taskName + '%'"/>
                AND pe.task_name LIKE #{taskNameLike}
            </if>
            <if test="filter.submissionDateStart != null and filter.submissionDateEnd != null">
                AND pe.submission_date BETWEEN #{filter.submissionDateStart} AND #{filter.submissionDateEnd}
            </if>
            <if test="filter.startDate != null">
                AND pe.submission_date &gt;= #{filter.startDate}
            </if>
            <if test="filter.endDate != null">
                AND pe.submission_date &lt;= #{filter.endDate}
            </if>
            <if test="filter.filingDateRanges != null">
                <foreach collection="filter.filingDateRanges" item="range">
                    AND NOT (pe.submission_date BETWEEN #{range.first} AND #{range.second})
                </foreach>
            </if>
            <if test="filter.deptIds != null">
                AND r.dept_id IN
                <foreach collection="filter.deptIds" item="dId" open="(" separator="," close=")">
                    #{dId}
                </foreach>
            </if>
            <if test="filter.taskIds != null">
                AND pe.task_id IN
                <foreach collection="filter.taskIds" item="tId" open="(" separator="," close=")">
                    #{tId}
                </foreach>
            </if>
            <if test="filter.userIds != null">
                AND user_id IN
                <foreach collection="filter.userIds" item="uId" open="(" separator="," close=")">
                    #{uId}
                </foreach>
            </if>
            <if test="filter.userId != null">
                AND pe.user_id = #{filter.userId}
            </if>
            <if test="filter.userRealName != null">
                <bind name="userRealNameLike" value="'%' + filter.userRealName + '%'"/>
                AND pe.user_real_name LIKE #{userRealNameLike}
            </if>
            <if test="filter.username != null">
                <bind name="userRealNameLike" value="'%' + filter.username + '%'"/>
                AND pe.user_real_name LIKE #{userRealNameLike}
            </if>
            <if test="filter.reviewTriple != null">
                AND (
                    pe.task_id IN
                    <foreach collection="filter.reviewTriple.left" item="tId" separator="," open="(" close=")">
                        #{tId}
                    </foreach>
                    OR pe.user_id IN
                    <foreach collection="filter.reviewTriple.middle" item="uId" separator="," open="(" close=")">
                        #{uId}
                    </foreach>
                    OR pe.project_id IN
                    <foreach collection="filter.reviewTriple.right" item="pId" separator="," open="(" close=")">
                        #{pId}
                    </foreach>
                )
            </if>
            <if test="filter.dailyPaperIds != null">
                AND pe.daily_paper_id IN
                <foreach collection="filter.dailyPaperIds" item="dId" open="(" separator="," close=")">
                    #{dId}
                </foreach>
            </if>
        </where>
        ORDER BY
            <if test="filter.orderBy == null">
                pe.id DESC
            </if>
            <if test="filter.orderBy != null">
                #{filter.orderBy}
            </if>
            <if test="filter.orderBy != null and filter.orderDesc == true">
                DESC
            </if>
    </select>

    <select id="findByDailyPaperId" resultType="com.gok.pboot.pms.entity.DailyPaperEntry">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mhour_daily_paper_entry pe
        WHERE
        pe.del_flag = 0
        AND
        pe.daily_paper_id = #{dailyPaperId}
        ORDER BY pe.ctime DESC
    </select>


    <select id="allocationFindPage" resultType="com.gok.pboot.pms.entity.vo.AllocationFindPageVO">
        SELECT
        t1.id AS 'id',
        t1.item_no AS 'code',
        t1.item_name AS 'projectName',
        t1.project_status AS 'projectStatus',
        (CASE WHEN t1.is_not_internal_project = 1 and t1.project_type = 0 THEN '公司信息化'
        WHEN t1.is_not_internal_project = 1 and t1.project_type = 1 THEN '通用课程开发'
        WHEN t1.is_not_internal_project = 1 and t1.project_type = 2 THEN '自研产品研发'
        WHEN t1.is_not_internal_project = 1 and t1.project_type = 3 THEN '标准化解决方案打造'
        WHEN t1.is_not_internal_project = 1 and t1.project_type = 4 THEN '专项人才供应链构建'
        WHEN t1.is_not_internal_project = 1 and t1.project_type = 99 THEN '部门工作'
        WHEN t1.is_not_internal_project = 2 THEN '/'
        END) AS 'projectTypeName',
        t1.is_not_internal_project,
        (CASE WHEN t1.is_not_internal_project = 1 THEN '是'
        WHEN t1.is_not_internal_project = 2 THEN '否' END ) AS 'isNotInternalProjectStr',
        t1.first_level_department_id AS 'projectDeptId',
        t1.user_dept_id AS 'dept_id',
        t1.user_dept_id AS 'userDeptId',
        t1.user_id AS 'userId',
        t1.user_real_name AS 'name',
        IFNULL(t1.id_card_no, '') AS 'idCardNo',
        IFNULL(t1.work_code, '') AS 'workCode',
        t1.personnelStatusName AS 'personnelStatusName',
        SUM(t1.projectHours) AS 'projectShareHours',
        SUM(t1.projectNormalHours) AS 'projectNormalHours',
        SUM(t1.projectAddedHours) AS 'projectAddedHours',
        SUM(t1.workOvertimeHours) AS 'workOvertimeHours',
        SUM(t1.restOvertimeHours) AS 'restOvertimeHours',
        SUM(t1.holidayOvertimeHours) AS 'holidayOvertimeHours',
        SUM(t1.preSaleHours) AS 'preSaleHours',
        SUM(t1.afterSaleHours) AS 'afterSaleHours',
        SUM(t1.leaveHours) AS 'leaveHours',
        t1.cwActualAttendance ,
        t1.privilegeUserName ,
        t1.date,
        t1.managerUserName,
        t1.salesmanUserName
        FROM (
        SELECT pr.id,
        pr.item_no ,
        pr.item_name ,
        pr.project_status ,
        pr.project_type,
        pr.is_not_internal_project,
        pr.first_level_department_id ,
        mdp.user_dept_id,
        pe.user_id ,
        r.id_card_no,
        r.work_code,
        pe.user_real_name as 'user_real_name',
        (CASE WHEN mdp.user_status = 0 THEN '正式'
        WHEN mdp.user_status = 1 THEN '实习'
        END) AS 'personnelStatusName',
        SUM(pe.normal_hours) AS 'projectNormalHours',
        SUM(pe.added_hours) AS 'projectAddedHours',
        SUM(pe.work_overtime_hours) AS 'workOvertimeHours',
        SUM(pe.rest_overtime_hours) AS 'restOvertimeHours',
        SUM(pe.holiday_overtime_hours) AS 'holidayOvertimeHours',
        SUM(pe.normal_hours) AS 'projectHours',
        SUM(CASE pe.work_type WHEN 0 THEN pe.normal_hours  ELSE 0 END) AS 'preSaleHours',
        SUM(CASE pe.work_type WHEN 1 THEN pe.normal_hours  ELSE 0 END) AS 'afterSaleHours',
        0 AS 'leaveHours',
        pe.submission_date AS 'date',
        0 AS 'cwActualAttendance',
        null AS 'privilegeUserName',
        pr.manager_user_name AS 'managerUserName',
        pr.project_salesperson AS 'salesmanUserName'
        FROM mhour_daily_paper_entry pe
        LEFT JOIN mhour_daily_paper mdp ON pe.daily_paper_id = mdp.id
        LEFT JOIN project_info pr ON pr.id = pe.project_id
        LEFT JOIN mhour_roster r ON r.id = pe.user_id
        <where>
            pe.approval_status = 4 AND pe.del_flag = 0
            <if test="allocationFindPageDTO.isNotInternalProject != null and allocationFindPageDTO.isNotInternalProject > 0">
                AND pr.is_not_internal_project = #{allocationFindPageDTO.isNotInternalProject}
            </if>
            <if test="allocationFindPageDTO.projectId != null">
                AND pe.project_id = #{allocationFindPageDTO.projectId}
            </if>
            <if test="allocationFindPageDTO.userIds != null and allocationFindPageDTO.userIds.size() > 0">
                AND pe.user_id IN
                <foreach collection="allocationFindPageDTO.userIds" separator="," item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="allocationFindPageDTO.userName != null and allocationFindPageDTO.userName != '' ">
                AND (r.alias_name like concat('%',#{allocationFindPageDTO.userName},'%')
                OR r.work_code like concat('%',#{allocationFindPageDTO.userName},'%'))
            </if>
            <if test="allocationFindPageDTO.personnelStatus != null ">
                AND mdp.user_status = #{allocationFindPageDTO.personnelStatus}
            </if>
            <if test="allocationFindPageDTO.selectMonth != null">
                AND DATE_FORMAT(pe.submission_date, '%Y%m') =
                DATE_FORMAT('${allocationFindPageDTO.selectMonth}-01','%Y%m')
            </if>
            <if test="allocationFindPageDTO.projectTypeList != null and allocationFindPageDTO.projectTypeList.size()>0">
                AND pr.project_type IN
                <foreach collection="allocationFindPageDTO.projectTypeList" item="i" open="(" separator="," close=")">
                      #{i}
                </foreach>
            </if>
            <if test="allocationFindPageDTO.deptIds != null and allocationFindPageDTO.deptIds.size() > 0">
                AND r.dept_id IN
                <foreach collection="allocationFindPageDTO.deptIds" separator="," item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            GROUP BY pe.user_id, pe.project_id
        </where>
        <if test="allocationFindPageDTO.personnelStatus != 2 and allocationFindPageDTO.personnelStatus != 3">
            UNION ALL
            SELECT pr.id,
                   pr.item_no,
                   pr.item_name,
                   pr.project_status,
                   pr.project_type,
                   pr.is_not_internal_project,
                   pr.first_level_department_id,
                   r.dept_id,
                   mc.oa_id,
                   r.id_card_no,
                   r.work_code,
                   r.alias_name as 'user_real_name',
                   (CASE
                           WHEN r.employee_status = 0 THEN '实习'
                           ELSE '正式'
                           END) AS 'personnelStatusName',
                   0 AS 'projectNormalHours',
                   0 AS 'projectAddedHours',
                   0 AS 'workOvertimeHours',
                   0 AS 'restOvertimeHours',
                   0 AS 'holidayOvertimeHours',
                   0 AS 'projectHours',
            SUM(CASE WHEN mc.work_type = 0 THEN mc.hour_data ELSE 0 END) AS 'preSaleHours',
            SUM(CASE WHEN mc.work_type = 1 THEN mc.hour_data ELSE 0 END) AS 'afterSaleHours',
                   0 AS 'leaveHours',
                   mc.belong_date AS 'date',
                   0 AS 'cwActualAttendance',
                   null AS 'privilegeUserName',
                   pr.manager_user_name AS 'managerUserName',
                   pr.project_salesperson AS 'salesmanUserName'
            FROM mhour_compensatory_leave_data mc
                         LEFT JOIN project_info pr ON pr.id = mc.xmmc
                         LEFT JOIN mhour_roster r ON r.id = mc.oa_id
            WHERE
            mc.belong_date not in(select day_date from  mhour_holiday)
            <if test="allocationFindPageDTO.userIds != null and allocationFindPageDTO.userIds.size() > 0">
                AND mc.oa_id IN
                <foreach collection="allocationFindPageDTO.userIds" separator="," item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="allocationFindPageDTO.userName != null and allocationFindPageDTO.userName != '' ">
                AND (r.alias_name like concat('%',#{allocationFindPageDTO.userName},'%')
                OR r.work_code like concat('%',#{allocationFindPageDTO.userName},'%'))
            </if>
            <if test="allocationFindPageDTO.projectId != null">
                AND mc.xmmc = #{allocationFindPageDTO.projectId}
            </if>
            <if test="allocationFindPageDTO.selectMonth != null">
                AND DATE_FORMAT(mc.belong_date, '%Y%m') =
                DATE_FORMAT('${allocationFindPageDTO.selectMonth}-01','%Y%m')
            </if>
            <if test="allocationFindPageDTO.projectTypeList != null and allocationFindPageDTO.projectTypeList.size()>0">
                AND pr.project_type IN
                <foreach collection="allocationFindPageDTO.projectTypeList" item="i" open="(" separator="," close=")">
                      #{i}
                </foreach>
            </if>
            <if test="allocationFindPageDTO.isNotInternalProject != null and allocationFindPageDTO.isNotInternalProject > 0" >
                AND pr.is_not_internal_project = #{allocationFindPageDTO.isNotInternalProject}
            </if>
            <if test="allocationFindPageDTO.personnelStatus != null and allocationFindPageDTO.personnelStatus == 0">
                AND r.employee_status != 0
            </if>
            <if test="allocationFindPageDTO.personnelStatus != null and allocationFindPageDTO.personnelStatus == 1">
                AND r.employee_status = 0
            </if>
            <if test="allocationFindPageDTO.deptIds != null and allocationFindPageDTO.deptIds.size() > 0">
                AND r.dept_id IN
                <foreach collection="allocationFindPageDTO.deptIds" separator="," item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            GROUP BY mc.oa_id, mc.xmmc
        </if>
        <if test="allocationFindPageDTO.personnelStatus == 2 or allocationFindPageDTO.personnelStatus == null">
            UNION ALL
            SELECT
            mpr.project_id AS 'id',
            mpr.project_code AS 'code',
            mpr.project_name ,
            pr.project_status,
            pr.project_type,
            pr.is_not_internal_project,
            pr.first_level_department_id AS 'dept_id',
            pr.first_level_department_id AS 'user_dept_id',
            '' AS 'userId',
            '' AS 'id_card_no',
            '' AS 'work_code',
            mpr.user_real_name AS 'user_real_name',
            '项目实习' AS 'personnelStatusName',
            IFNULL(mpr.aggregated_days,0)*7 AS 'projectNormalHours',
            0 AS 'projectAddedHours',
            0 AS 'workOvertimeHours',
            0 AS 'restOvertimeHours',
            0 AS 'holidayOvertimeHours',
            IFNULL(mpr.aggregated_days,0)*7 AS 'projectHours',
            0 AS 'preSaleHours',
            0 AS 'afterSaleHours',
            0 AS 'leaveHours',
            mpr.reuse_date AS 'date',
            null AS 'cwActualAttendance',
            mpr.executor_user_real_name AS 'privilegeUserName',
            null AS 'managerUserName',
            null AS 'salesmanUserName'
            FROM mhour_personnel_reuse mpr
            LEFT JOIN project_info pr ON pr.id = mpr.project_id
            <where>
                mpr.del_flag = 0 AND mpr.approval_status = 4
                <if test="allocationFindPageDTO.isNotInternalProject != null and allocationFindPageDTO.isNotInternalProject > 0">
                    AND pr.is_not_internal_project = #{allocationFindPageDTO.isNotInternalProject}
                </if>
                <if test="allocationFindPageDTO.userName != null and allocationFindPageDTO.userName != '' ">
                    AND mpr.user_real_name like concat('%',#{allocationFindPageDTO.userName},'%')
                </if>
                <if test="allocationFindPageDTO.projectId != null">
                    AND mpr.project_id = #{allocationFindPageDTO.projectId}
                </if>
                <if test="allocationFindPageDTO.selectMonth != null">
                    AND DATE_FORMAT(mpr.reuse_date, '%Y%m') =
                    DATE_FORMAT('${allocationFindPageDTO.selectMonth}-01','%Y%m')
                </if>
                <if test="allocationFindPageDTO.userIds != null and allocationFindPageDTO.userIds.size() > 0">
                    AND mpr.executor_user_id IN
                    <foreach collection="allocationFindPageDTO.userIds" separator="," item="item" open="(" close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="allocationFindPageDTO.deptIds != null and allocationFindPageDTO.deptIds.size() > 0">
                    AND pr.first_level_department_id IN
                    <foreach collection="allocationFindPageDTO.deptIds" separator="," item="item" open="(" close=")">
                    #{item}
                    </foreach>
                </if>
                <if test="allocationFindPageDTO.projectTypeList != null and allocationFindPageDTO.projectTypeList.size()>0">
                    AND pr.project_type IN
                    <foreach collection="allocationFindPageDTO.projectTypeList" item="i" open="(" separator="," close=")">
                        #{i}
                    </foreach>
                </if>
                GROUP BY mpr.user_real_name,mpr.mobile,mpr.project_id
            </where>
        </if>
        <!--   2023.3.3 新连一张交付人员表     -->
        <if test="allocationFindPageDTO.personnelStatus == 3 or allocationFindPageDTO.personnelStatus == null">
            UNION ALL
            SELECT
            mpr.project_id AS 'id',
            mpr.project_code AS 'code',
            pr.item_name ,
            pr.project_status,
            pr.project_type,
            pr.is_not_internal_project,
            mpr.revenue_dept_id AS 'dept_id',
            mpr.dept_id AS 'user_dept_id',
            '' AS 'userId',
            '' AS 'id_card_no',
            mpr.work_code AS 'work_code',
            mpr.user_real_name AS 'user_real_name',
            '外包' AS 'personnelStatusName',
            IFNULL(mpr.normal_work_days,0)*7 AS 'projectNormalHours',
            IFNULL(mpr.rest_work_days,0)*7 + IFNULL(mpr.holidays_work_days,0)*7 AS 'projectAddedHours',
            0 AS 'workOvertimeHours',
            IFNULL(mpr.rest_work_days,0)*7 AS 'restOvertimeHours',
            IFNULL(mpr.holidays_work_days,0)*7 AS 'holidayOvertimeHours',
            IFNULL(mpr.normal_work_days,0)*7 AS 'projectHours',
            0 AS 'preSaleHours',
            0 AS 'afterSaleHours',
            IFNULL(mpr.ompensatory_days,0) *7 AS 'leaveHours',
            mpr.reuse_date AS 'date',
            mpr.attendance_days AS 'cwActualAttendance',
            mpr.executor_user_real_name AS 'privilegeUserName',
            null AS 'managerUserName',
            null AS 'salesmanUserName'
            FROM mhour_personnel_delivery_hour mpr
            LEFT JOIN project_info pr ON pr.id = mpr.project_id
            <where>
                mpr.del_flag = 0 AND mpr.approval_status = 4
                <if test="allocationFindPageDTO.isNotInternalProject != null and allocationFindPageDTO.isNotInternalProject > 0">
                    AND pr.is_not_internal_project = #{allocationFindPageDTO.isNotInternalProject}
                </if>
                <if test="allocationFindPageDTO.userName != null and allocationFindPageDTO.userName != '' ">
                    AND (mpr.user_real_name like concat('%',#{allocationFindPageDTO.userName},'%')
                    OR mpr.work_code like concat('%',#{allocationFindPageDTO.userName},'%'))
                </if>
                <if test="allocationFindPageDTO.projectId != null">
                    AND mpr.project_id = #{allocationFindPageDTO.projectId}
                </if>
                <if test="allocationFindPageDTO.selectMonth != null">
                    AND DATE_FORMAT(mpr.reuse_date, '%Y%m') =
                    DATE_FORMAT('${allocationFindPageDTO.selectMonth}-01','%Y%m')
                </if>
                <if test="allocationFindPageDTO.userIds != null and allocationFindPageDTO.userIds.size() > 0">
                    AND mpr.executor_user_id IN
                    <foreach collection="allocationFindPageDTO.userIds" separator="," item="item" open="(" close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="allocationFindPageDTO.deptIds != null and allocationFindPageDTO.deptIds.size() > 0">
                AND mpr.dept_id IN
                    <foreach collection="allocationFindPageDTO.deptIds" separator="," item="item" open="(" close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="allocationFindPageDTO.projectTypeList != null and allocationFindPageDTO.projectTypeList.size()>0">
                    AND pr.project_type IN
                    <foreach collection="allocationFindPageDTO.projectTypeList" item="i" open="(" separator="," close=")">
                        #{i}
                    </foreach>
                </if>
                GROUP BY mpr.work_code,mpr.project_id
            </where>
        </if>
        ) t1
        GROUP BY t1.id,t1.user_id,t1.work_code,t1.user_real_name
        ORDER BY t1.date DESC,t1.item_name
    </select>

    <select id="allocationFindAll" resultType="com.gok.pboot.pms.entity.vo.AllocationFindPageVO">
        SELECT
        t1.id AS 'id',
        t1.item_no AS 'code',
        t1.item_name AS 'projectName',
        t1.project_status AS 'projectStatus',
        t1.is_not_internal_project,
        t1.first_level_department_id AS 'projectDeptId',
        t1.user_dept_id AS 'dept_id',
        t1.user_dept_id AS 'userDeptId',
        t1.user_id AS 'userId',
        t1.user_real_name AS 'name',
        IFNULL(t1.id_card_no, '') AS 'idCardNo',
        t1.personnelStatusName AS 'personnelStatusName',
        t1.projectHours AS 'projectHours',
        t1.projectNormalHours AS 'projectNormalHours',
        t1.projectAddedHours AS 'projectAddedHours',
        t1.workOvertimeHours AS 'workOvertimeHours',
        t1.restOvertimeHours AS 'restOvertimeHours',
        t1.holidayOvertimeHours AS 'holidayOvertimeHours',
        t1.preSaleHours AS 'preSaleHours',
        t1.afterSaleHours AS 'afterSaleHours',
        t1.leaveHours AS 'leaveHours',
        t1.cwActualAttendance ,
        t1.privilegeUserName ,
        t1.date,
        t1.managerUserName,
        t1.salesmanUserName
        FROM (
        SELECT pr.id,
        pr.item_no ,
        pr.item_name ,
        pr.project_status ,
        pr.is_not_internal_project,
        pr.first_level_department_id ,
        mdp.user_dept_id,
        pe.user_id ,
        r.id_card_no,
        pe.user_real_name,
        (CASE WHEN mdp.user_status = 0 THEN '正式'
        WHEN mdp.user_status = 1 THEN '实习'
        END) AS 'personnelStatusName',
        SUM(pe.normal_hours) AS 'projectNormalHours',
        SUM(pe.added_hours) AS 'projectAddedHours',
        SUM(pe.work_overtime_hours) AS 'workOvertimeHours',
        SUM(pe.rest_overtime_hours) AS 'restOvertimeHours',
        SUM(pe.holiday_overtime_hours) AS 'holidayOvertimeHours',
        SUM(pe.added_hours)+SUM(pe.normal_hours) AS 'projectHours',
        SUM(CASE pe.work_type WHEN 0 THEN pe.normal_hours + pe.added_hours ELSE 0 END) AS 'preSaleHours',
        SUM(CASE pe.work_type WHEN 1 THEN pe.normal_hours + pe.added_hours ELSE 0 END) AS 'afterSaleHours',
        0 AS 'leaveHours',
        pe.submission_date AS 'date',
        0 AS 'cwActualAttendance',
        null AS 'privilegeUserName',
        pr.manager_user_name AS 'managerUserName',
        pr.project_salesperson AS 'salesmanUserName'
        FROM mhour_daily_paper_entry pe
        LEFT JOIN mhour_daily_paper mdp ON pe.daily_paper_id = mdp.id
        LEFT JOIN project_info pr ON pr.id = pe.project_id
        LEFT JOIN mhour_roster r ON r.id = pe.user_id
        <where>
            pe.approval_status = 4 AND pe.del_flag = 0
            <if test="allocationFindPageDTO.isNotInternalProject != null and allocationFindPageDTO.isNotInternalProject > 0">
                AND pr.is_not_internal_project = #{allocationFindPageDTO.isNotInternalProject}
            </if>
            <if test="allocationFindPageDTO.projectId != null">
                AND pe.project_id = #{allocationFindPageDTO.projectId}
            </if>
            <if test="allocationFindPageDTO.userIds != null and allocationFindPageDTO.userIds.size() > 0">
                AND pe.user_id IN
                <foreach collection="allocationFindPageDTO.userIds" separator="," item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="allocationFindPageDTO.userName != null and allocationFindPageDTO.userName != '' ">
                AND (r.alias_name like concat('%',#{allocationFindPageDTO.userName},'%')
                OR r.work_code like concat('%',#{allocationFindPageDTO.userName},'%'))
            </if>
            <if test="allocationFindPageDTO.deptIds != null and allocationFindPageDTO.deptIds.size() > 0">
                AND r.dept_id IN
                <foreach collection="allocationFindPageDTO.deptIds" separator="," item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="allocationFindPageDTO.personnelStatus != null ">
                AND mdp.user_status = #{allocationFindPageDTO.personnelStatus}
            </if>
            <if test="allocationFindPageDTO.selectMonth != null">
                AND DATE_FORMAT(pe.submission_date, '%Y%m') =
                DATE_FORMAT('${allocationFindPageDTO.selectMonth}-01','%Y%m')
            </if>
            <if test="allocationFindPageDTO.projectTypeList != null and allocationFindPageDTO.projectTypeList.size()>0">
                AND pr.project_type IN
                <foreach collection="allocationFindPageDTO.projectTypeList" item="i" open="(" separator="," close=")">
                      #{i}
                </foreach>
            </if>
            GROUP BY pe.user_id, pe.project_id
        </where>
        <if test="allocationFindPageDTO.personnelStatus != 2 and allocationFindPageDTO.personnelStatus != 3">
            UNION ALL
            SELECT pr.id,
			pr.item_no,
			pr.item_name,
			pr.project_status,
			pr.is_not_internal_project,
			pr.first_level_department_id,
			r.dept_id,
			mc.oa_id,
			r.id_card_no,
			r.alias_name,
			( CASE WHEN r.employee_status = 0 THEN '实习' ELSE '正式' END ) AS 'personnelStatusName',
			0 AS 'projectNormalHours',
			0 AS 'projectAddedHours',
			0 AS 'workOvertimeHours',
			0 AS 'restOvertimeHours',
			0 AS 'holidayOvertimeHours',
			0 AS 'projectHours',
			0 AS 'preSaleHours',
			0 AS 'afterSaleHours',
            0 AS 'leaveHours',
			mc.belong_date AS 'date',
			0 AS 'cwActualAttendance',
			NULL AS 'privilegeUserName',
			pr.manager_user_name AS 'managerUserName',
			pr.project_salesperson AS 'salesmanUserName'
            FROM mhour_compensatory_leave_data mc
                         LEFT JOIN project_info pr ON pr.id = mc.xmmc
                         LEFT JOIN mhour_roster r ON r.id = mc.oa_id
            WHERE
            mc.belong_date not in(select day_date from  mhour_holiday)
            <if test="allocationFindPageDTO.userIds != null and allocationFindPageDTO.userIds.size() > 0">
                AND mc.oa_id IN
                <foreach collection="allocationFindPageDTO.userIds" separator="," item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="allocationFindPageDTO.userName != null and allocationFindPageDTO.userName != '' ">
                AND (r.alias_name like concat('%',#{allocationFindPageDTO.userName},'%')
                OR r.work_code like concat('%',#{allocationFindPageDTO.userName},'%'))
            </if>
            <if test="allocationFindPageDTO.projectId != null">
                AND mc.xmmc = #{allocationFindPageDTO.projectId}
            </if>
            <if test="allocationFindPageDTO.selectMonth != null">
                AND DATE_FORMAT(mc.belong_date, '%Y%m') =
                DATE_FORMAT('${allocationFindPageDTO.selectMonth}-01','%Y%m')
            </if>
            <if test="allocationFindPageDTO.projectTypeList != null and allocationFindPageDTO.projectTypeList.size()>0">
                AND pr.project_type IN
                <foreach collection="allocationFindPageDTO.projectTypeList" item="i" open="(" separator="," close=")">
                      #{i}
                </foreach>
            </if>
            <if test="allocationFindPageDTO.isNotInternalProject != null and allocationFindPageDTO.isNotInternalProject > 0">
                AND pr.is_not_internal_project = #{allocationFindPageDTO.isNotInternalProject}
            </if>
            <if test="allocationFindPageDTO.personnelStatus != null and allocationFindPageDTO.personnelStatus == 0">
                AND r.employee_status != 0
            </if>
            <if test="allocationFindPageDTO.personnelStatus != null and allocationFindPageDTO.personnelStatus == 1">
                AND r.employee_status = 0
            </if>
            <if test="allocationFindPageDTO.deptIds != null and allocationFindPageDTO.deptIds.size() > 0">
                AND r.dept_id IN
                <foreach collection="allocationFindPageDTO.deptIds" separator="," item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            GROUP BY mc.oa_id, mc.xmmc
        </if>
        <if test="allocationFindPageDTO.personnelStatus == 2 or allocationFindPageDTO.personnelStatus == null">
            UNION ALL
            SELECT
            mpr.project_id AS 'id',
            mpr.project_code AS 'code',
            mpr.project_name ,
            pr.project_status,
            pr.is_not_internal_project,
            pr.first_level_department_id AS 'dept_id',
            pr.first_level_department_id AS 'user_dept_id',
            mpr.executor_user_id AS 'userId',
            '' AS 'id_card_no',
            mpr.executor_user_real_name AS 'user_name',
            '项目实习' AS 'personnelStatusName',
            IFNULL(mpr.aggregated_days,0)*7 AS 'projectNormalHours',
            0 AS 'projectAddedHours',
            0 AS 'workOvertimeHours',
			0 AS 'restOvertimeHours',
			0 AS 'holidayOvertimeHours',
            IFNULL(mpr.aggregated_days,0)*7 AS 'projectHours',
            0 AS 'preSaleHours',
            0 AS 'afterSaleHours',
            0 AS 'leaveHours',
            mpr.reuse_date AS 'date',
            null AS 'cwActualAttendance',
            mpr.executor_user_real_name AS 'privilegeUserName',
            null AS 'managerUserName',
            null AS 'salesmanUserName'
            FROM mhour_personnel_reuse mpr
            LEFT JOIN project_info pr ON pr.id = mpr.project_id
            <where>
                mpr.del_flag = 0 AND mpr.approval_status = 4
                <if test="allocationFindPageDTO.isNotInternalProject != null and allocationFindPageDTO.isNotInternalProject > 0">
                    AND pr.is_not_internal_project = #{allocationFindPageDTO.isNotInternalProject}
                </if>
                <if test="allocationFindPageDTO.userName != null and allocationFindPageDTO.userName != '' ">
                    AND mpr.user_real_name like concat('%',#{allocationFindPageDTO.userName},'%')
                </if>
                <if test="allocationFindPageDTO.projectId != null">
                    AND mpr.project_id = #{allocationFindPageDTO.projectId}
                </if>
                <if test="allocationFindPageDTO.selectMonth != null">
                    AND DATE_FORMAT(mpr.reuse_date, '%Y%m') =
                    DATE_FORMAT('${allocationFindPageDTO.selectMonth}-01','%Y%m')
                </if>
                <if test="allocationFindPageDTO.userIds != null and allocationFindPageDTO.userIds.size() > 0">
                    AND mpr.executor_user_id IN
                    <foreach collection="allocationFindPageDTO.userIds" separator="," item="item" open="(" close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="allocationFindPageDTO.deptIds != null and allocationFindPageDTO.deptIds.size() > 0">
                    AND pr.first_level_department_id IN
                    <foreach collection="allocationFindPageDTO.deptIds" separator="," item="item" open="(" close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="allocationFindPageDTO.projectTypeList != null and allocationFindPageDTO.projectTypeList.size()>0">
                    AND pr.project_type IN
                    <foreach collection="allocationFindPageDTO.projectTypeList" item="i" open="(" separator="," close=")">
                        #{i}
                    </foreach>
                </if>
                GROUP BY mpr.user_real_name,mpr.mobile,mpr.project_id
            </where>
        </if>
        <!--   2023.3.3 新连一张交付人员表     -->
        <if test="allocationFindPageDTO.personnelStatus == 3 or allocationFindPageDTO.personnelStatus == null">
            UNION ALL
            SELECT
            mpr.project_id AS 'id',
            mpr.project_code AS 'code',
            pr.item_name ,
            pr.project_status,
            pr.is_not_internal_project,
            mpr.revenue_dept_id AS 'dept_id',
            mpr.dept_id AS 'user_dept_id',
            null AS 'userId',
            '' AS 'id_card_no',
            mpr.executor_user_real_name AS 'user_name',
            '外包' AS 'personnelStatusName',
            IFNULL(mpr.normal_work_days,0)*7 AS 'projectNormalHours',
            IFNULL(mpr.rest_work_days,0)*7 + IFNULL(mpr.holidays_work_days,0)*7  AS 'projectAddedHours',
            0 AS 'workOvertimeHours',
            IFNULL(mpr.rest_work_days,0)*7 AS 'restOvertimeHours',
            IFNULL(mpr.holidays_work_days,0)*7 AS 'holidayOvertimeHours',
            IFNULL(mpr.normal_work_days,0)*7 AS 'projectHours',
            0 AS 'preSaleHours',
            0 AS 'afterSaleHours',
            IFNULL(mpr.ompensatory_days,0) *7 AS 'leaveHours',
            mpr.reuse_date AS 'date',
            mpr.attendance_days AS 'cwActualAttendance',
            mpr.executor_user_real_name AS 'privilegeUserName',
            null AS 'managerUserName',
            null AS 'salesmanUserName'
            FROM mhour_personnel_delivery_hour mpr
            LEFT JOIN project_info pr ON pr.id = mpr.project_id
            <where>
                mpr.del_flag = 0 AND mpr.approval_status = 4
                <if test="allocationFindPageDTO.isNotInternalProject != null and allocationFindPageDTO.isNotInternalProject > 0">
                    AND pr.is_not_internal_project = #{allocationFindPageDTO.isNotInternalProject}
                </if>
                <if test="allocationFindPageDTO.userName != null and allocationFindPageDTO.userName != '' ">
                    AND (mpr.user_real_name like concat('%',#{allocationFindPageDTO.userName},'%')
                    OR mpr.work_code like concat('%',#{allocationFindPageDTO.userName},'%'))
                </if>
                <if test="allocationFindPageDTO.projectId != null">
                    AND mpr.project_id = #{allocationFindPageDTO.projectId}
                </if>
                <if test="allocationFindPageDTO.selectMonth != null">
                    AND DATE_FORMAT(mpr.reuse_date, '%Y%m') =
                    DATE_FORMAT('${allocationFindPageDTO.selectMonth}-01','%Y%m')
                </if>
                <if test="allocationFindPageDTO.userIds != null and allocationFindPageDTO.userIds.size() > 0">
                    AND mpr.executor_user_id IN
                    <foreach collection="allocationFindPageDTO.userIds" separator="," item="item" open="(" close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="allocationFindPageDTO.deptIds != null and allocationFindPageDTO.deptIds.size() > 0">
                    AND mpr.dept_id IN
                    <foreach collection="allocationFindPageDTO.deptIds" separator="," item="item" open="(" close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="allocationFindPageDTO.projectTypeList != null and allocationFindPageDTO.projectTypeList.size()>0">
                    AND pr.project_type IN
                    <foreach collection="allocationFindPageDTO.projectTypeList" item="i" open="(" separator="," close=")">
                        #{i}
                    </foreach>
                </if>
                GROUP BY mpr.work_code,mpr.project_id
            </where>
        </if>
        ) t1
        ORDER BY t1.date DESC,t1.item_name
    </select>

    <select id="projectHourSumFindPage" resultType="com.gok.pboot.pms.entity.vo.ProjectHourSumFindPageVO">
        SELECT
        pr.item_no AS 'code',
        pr.id AS 'id',
        pr.item_name AS 'projectName',
        pr.project_status AS 'projectStatus',
        pr.first_level_department_id AS 'projectDeptId',
        pr.first_level_department_id AS 'dept_id',
        SUM(pe.normal_hours) AS 'normalHours',
        SUM(pe.added_hours) AS 'addedHours',
        SUM(pe.added_hours)+SUM(pe.normal_hours) AS 'projectHours',
        h.hourData AS 'hourData'
        FROM mhour_daily_paper_entry pe
        LEFT JOIN project_info pr ON pr.id = pe.project_id
        LEFT JOIN mhour_daily_paper mdp ON pe.daily_paper_id = mdp.id
        LEFT JOIN (
        SELECT
        ol.xmmc,SUM(ol.hour_data) AS 'hourData'
        FROM mhour_overtime_leave_data ol
        WHERE ol.type IN (99)
        AND ol.xmmc IS NOT NULL
        <if test="projectHourSumFindPageDTO.startTime != null">
            AND DATE_FORMAT(ol.belongdate, '%Y-%m-%d') &gt;=
            DATE_FORMAT(#{projectHourSumFindPageDTO.startTime},'%Y-%m-%d')
        </if>
        <if test="projectHourSumFindPageDTO.endTime != null">
            AND DATE_FORMAT(ol.belongdate, '%Y-%m-%d') &lt;=
            DATE_FORMAT(#{projectHourSumFindPageDTO.endTime},'%Y-%m-%d')
        </if>
        GROUP BY ol.xmmc
        ) h ON h.xmmc = pe.project_id
        <where>
            pe.del_flag = 0
            <if test="filter.showUnreviewed == false">
                AND pe.approval_status = 4
            </if>
            <if test="filter.showUnreviewed == null or filter.showUnreviewed == true">
                AND (pe.approval_status = 2 OR pe.approval_status = 4)
            </if>
            <if test="projectHourSumFindPageDTO.projectId != null">
                AND pr.id = #{projectHourSumFindPageDTO.projectId}
            </if>
            <if test="filter.projectIds != null and filter.projectIds.size() > 0 ">
                AND pr.id IN
                <foreach collection="filter.projectIds" open="(" close=")" separator="," item="id">
                    #{id}
                </foreach>
            </if>
            <if test="projectHourSumFindPageDTO.startTime != null">
                AND DATE_FORMAT(pe.submission_date, '%Y-%m-%d') &gt;=
                DATE_FORMAT(#{projectHourSumFindPageDTO.startTime},'%Y-%m-%d')
            </if>
            <if test="projectHourSumFindPageDTO.endTime != null">
                AND DATE_FORMAT(pe.submission_date, '%Y-%m-%d') &lt;=
                DATE_FORMAT(#{projectHourSumFindPageDTO.endTime},'%Y-%m-%d')
            </if>
            <if test="filter.projectStatus != null">
                AND pr.project_status = #{filter.projectStatus}
            </if>
            <if test="projectHourSumFindPageDTO.personnelStatus != null">
                AND mdp.user_status = #{projectHourSumFindPageDTO.personnelStatus}
            </if>
        </where>
        GROUP BY pr.id
    </select>

    <select id="confirmHourSumFindPageVO2" resultType="com.gok.pboot.pms.entity.vo.ConfirmHourSumFindPageVO">
        SELECT
        t1.id AS 'id',
        t1.code AS 'code',
        t1.project_name AS 'projectName',
        t1.project_status AS 'projectStatus',
        t1.dept_id AS 'projectDeptId',
        t1.user_dept_id AS 'dept_id',
        t1.user_id AS 'userId',
        t1.user_real_name AS 'name',
        t1.personnelStatusName AS 'personnelStatusName',
        t1.projectHours AS 'projectHours',
        t1.cwActualAttendance ,
        t1.privilegeUserName ,
        t1.date,
        t1.managerUserName,
        t1.salesmanUserName,
        t1.addedHours,
        t1.normalHours,
        t1.hourData
        FROM (
        SELECT pr.id,
        pr.code ,
        pr.project_name ,
        pr.project_status ,
        pr.dept_id ,
        mdp.user_dept_id,
        pe.user_id ,
        pe.user_real_name,
        (CASE WHEN mdp.user_status = 0 THEN '正式'
        WHEN mdp.user_status = 1 THEN '实习'
        END) AS 'personnelStatusName',
        SUM(pe.normal_hours) AS 'normalHours',
        SUM(pe.added_hours) AS 'addedHours',
        SUM(pe.added_hours)+SUM(pe.normal_hours) AS 'projectHours',
        pe.submission_date AS 'date',
        null AS 'cwActualAttendance',
        null AS 'privilegeUserName',
        pr.manager_user_name AS 'managerUserName',
        pr.salesman_user_name AS 'salesmanUserName',
        h.hourData AS 'hourData'
        FROM mhour_daily_paper_entry pe
        LEFT JOIN mhour_daily_paper mdp ON pe.daily_paper_id = mdp.id
        LEFT JOIN mhour_project pr ON pr.id = pe.project_id
        LEFT JOIN mhour_roster roster ON roster.id = pe.user_id
        LEFT JOIN (
        SELECT
        ol.xmmc,ol.oa_id,SUM(ol.hour_data) AS 'hourData'
        FROM mhour_overtime_leave_data ol
        WHERE ol.type IN (99) AND ol.xmmc IS NOT NULL
        <if test="confirmHourSumFindPageDTO.selectMonth != null">
            AND DATE_FORMAT(ol.belongdate, '%Y%m') = DATE_FORMAT('${confirmHourSumFindPageDTO.selectMonth}-01','%Y%m')
        </if>
        GROUP BY ol.oa_id,ol.xmmc
        ) h ON h.xmmc = pr.id AND h.oa_id = pe.user_id
        <where>
            pe.approval_status = 4 AND pe.del_flag = 0
            <if test="confirmHourSumFindPageDTO.projectId != null">
                AND pe.project_id = #{confirmHourSumFindPageDTO.projectId}
            </if>
            <if test="confirmHourSumFindPageDTO.userIds != null and confirmHourSumFindPageDTO.userIds.size() > 0">
                AND pe.user_id IN
                <foreach collection="confirmHourSumFindPageDTO.userIds" separator="," item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="confirmHourSumFindPageDTO.personnelStatus != null ">
                AND roster.employee_status = #{confirmHourSumFindPageDTO.personnelStatus}
            </if>
            <if test="confirmHourSumFindPageDTO.selectMonth != null">
                AND DATE_FORMAT(pe.submission_date, '%Y%m') =
                DATE_FORMAT('${confirmHourSumFindPageDTO.selectMonth}-01','%Y%m')
            </if>
            GROUP BY pe.user_id, pe.project_id
        </where>

        <if test="confirmHourSumFindPageDTO.personnelStatus == 2 or confirmHourSumFindPageDTO.personnelStatus == null">
            UNION ALL
            SELECT
            mpr.project_id AS 'id',
            mpr.project_code AS 'code',
            mpr.project_name ,
            pr.project_status,
            pr.dept_id,
            pr.dept_id AS 'user_dept_id',
            null AS 'user_id',
            mpr.user_real_name AS 'user_name',
            '项目实习' AS 'personnelStatusName',
            null AS 'normalHours',
            null AS 'addedHours',
            IFNULL(mpr.aggregated_days,0)*7 AS 'projectHours',
            mpr.reuse_date AS 'date',
            null AS 'cwActualAttendance',
            mpr.executor_user_real_name AS 'privilegeUserName',
            null AS 'managerUserName',
            null AS 'salesmanUserName',
            null AS 'hourData'
            FROM mhour_personnel_reuse mpr
            LEFT JOIN mhour_project pr ON pr.id = mpr.project_id
            <where>
                mpr.del_flag = 0 AND mpr.approval_status = 4
                <if test="confirmHourSumFindPageDTO.projectId != null">
                    AND mpr.project_id = #{confirmHourSumFindPageDTO.projectId}
                </if>
                <if test="confirmHourSumFindPageDTO.userNames != null and confirmHourSumFindPageDTO.userNames.size() > 0">
                    AND mpr.user_real_name IN
                    <foreach collection="confirmHourSumFindPageDTO.userNames" separator="," item="item" open="("
                             close=")">#{item}
                    </foreach>
                </if>
                <if test="confirmHourSumFindPageDTO.selectMonth != null">
                    AND DATE_FORMAT(mpr.reuse_date, '%Y%m') =
                    DATE_FORMAT('${confirmHourSumFindPageDTO.selectMonth}-01','%Y%m')
                </if>
                GROUP BY mpr.user_real_name,mpr.mobile,mpr.project_id
            </where>
        </if>
        <!--   2023.3.3 新连一张交付人员表     -->
        <if test="confirmHourSumFindPageDTO.personnelStatus == 3 or confirmHourSumFindPageDTO.personnelStatus == null">
            UNION ALL
            SELECT
            mpr.project_id AS 'id',
            mpr.project_code AS 'code',
            mpr.project_name ,
            pr.project_status,
            pr.dept_id,
            mpr.dept_id AS 'user_dept_id',
            null AS 'user_id',
            mpr.user_real_name AS 'user_name',
            '外包' AS 'personnelStatusName',
            null AS 'normalHours',
            null AS 'addedHours',
            IFNULL(mpr.project_consumed,0)*7 AS 'projectHours',
            mpr.reuse_date AS 'date',
            null AS 'cwActualAttendance',
            mpr.executor_user_real_name AS 'privilegeUserName',
            null AS 'managerUserName',
            null AS 'salesmanUserName',
            null AS 'hourData'
            FROM mhour_personnel_delivery_hour mpr
            LEFT JOIN mhour_project pr ON pr.id = mpr.project_id
            <where>
                mpr.del_flag = 0 AND mpr.approval_status = 4
                <if test="confirmHourSumFindPageDTO.projectId != null">
                    AND mpr.project_id = #{confirmHourSumFindPageDTO.projectId}
                </if>
                <if test="confirmHourSumFindPageDTO.userNames != null and confirmHourSumFindPageDTO.userNames.size() > 0">
                    AND mpr.user_real_name IN
                    <foreach collection="confirmHourSumFindPageDTO.userNames" separator="," item="item" open="("
                             close=")">#{item}
                    </foreach>
                </if>
                <if test="confirmHourSumFindPageDTO.selectMonth != null">
                    AND DATE_FORMAT(mpr.reuse_date, '%Y%m') =
                    DATE_FORMAT('${confirmHourSumFindPageDTO.selectMonth}-01','%Y%m')
                </if>
                GROUP BY mpr.work_code,mpr.project_id
            </where>
        </if>

        ) t1
        ORDER BY t1.date DESC,t1.project_name
    </select>
    <select id="dailyReviewFindPageVO" resultType="com.gok.pboot.pms.entity.vo.DailyReviewFindPageVO">
        SELECT
        hdpe.submission_date AS 'submissionDate',
        hdpe.project_id AS 'projectId',
        hdpe.project_name AS 'projectName',
        count(hdpe.id) as number,
        sum(hdpe.normal_hours) normalHours,
        sum(hdpe.added_hours) as addedHours,
        count(hdpe.id) as numberTotal,
        <choose>
            <when test="dailyReviewFindPageDTO.notProjectIds != null and dailyReviewFindPageDTO.notProjectIds.size() > 0">
                0 AS isEdit,
                GROUP_CONCAT( hdpe.user_id ) AS userStr
            </when>
            <otherwise>
                1 AS isEdit,
                NULL AS userStr
            </otherwise>
        </choose>
        FROM mhour_daily_paper_entry hdpe
        WHERE hdpe.del_flag = 0
        <if test="dailyReviewFindPageDTO.status != null and dailyReviewFindPageDTO.status == 0 ">
            AND hdpe.approval_status IN (2)
        </if>
        <if test="dailyReviewFindPageDTO.status != null and dailyReviewFindPageDTO.status == 1 ">
            AND hdpe.approval_status IN (3,4)
        </if>
        <if test="dailyReviewFindPageDTO.projectName != null and dailyReviewFindPageDTO.projectName != '' ">
            AND hdpe.project_name like concat('%',#{dailyReviewFindPageDTO.projectName},'%')
        </if>
        <if test="dailyReviewFindPageDTO.projectIds != null and dailyReviewFindPageDTO.projectIds.size() > 0">
            AND hdpe.project_id IN
            <foreach collection="dailyReviewFindPageDTO.projectIds" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="dailyReviewFindPageDTO.userIds != null and dailyReviewFindPageDTO.userIds.size() > 0">
            AND hdpe.user_id IN
            <foreach collection="dailyReviewFindPageDTO.userIds" separator="," item="userId" open="(" close=")">
                #{userId}
            </foreach>
        </if>
        <if test="dailyReviewFindPageDTO.startTime != null">
            AND DATE_FORMAT(hdpe.submission_date, '%Y-%m-%d') &gt;= DATE_FORMAT(#{
                dailyReviewFindPageDTO.startTime},'%Y-%m-%d')
        </if>
        <if test="dailyReviewFindPageDTO.endTime != null">
            AND DATE_FORMAT(hdpe.submission_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{
                dailyReviewFindPageDTO.endTime},'%Y-%m-%d')
        </if>
        <if test="dailyReviewFindPageDTO.notProjectIds != null and dailyReviewFindPageDTO.notProjectIds.size() > 0">
            AND hdpe.project_id not IN
            <foreach collection="dailyReviewFindPageDTO.notProjectIds" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY hdpe.project_id, hdpe.submission_date
        ORDER BY hdpe.submission_date
    </select>

    <select id="dailyProjectDetailsFindPageVO" resultType="com.gok.pboot.pms.entity.vo.DailyProjectDetailsFindPageVO">
        SELECT
        pe.id AS 'id',
        pe.user_real_name AS 'userRealName',
        pe.task_name AS 'taskName',
        pe.submission_date AS 'submissionDate',
        (CASE WHEN mdp.filling_state = 0 THEN '正常'
        WHEN mdp.filling_state = 1 THEN '滞后'
        END) AS 'fillingStateName',
        pe.normal_hours AS 'normalHours',
        pe.added_hours AS 'addedHours',
        pe.description AS 'description',
        pe.approval_reason AS 'approvalReason',
        pe.approval_status AS 'approvalStatus',
        (CASE WHEN pe.approval_status = 0 THEN '未提交'
        WHEN pe.approval_status = 1 THEN '已退回'
        WHEN pe.approval_status = 2 THEN '待审核'
        WHEN pe.approval_status = 3 THEN '不通过'
        WHEN pe.approval_status = 4 THEN '已通过'
        END) AS 'approvalStatusName',
        pe.modifier AS 'approvalName',
        pe.daily_paper_id AS 'dailyPaperId',
        pe.user_id AS 'userId',
        pe.project_id AS 'projectId'
        FROM mhour_daily_paper_entry pe
        LEFT JOIN mhour_daily_paper mdp ON pe.daily_paper_id = mdp.id
        <where>
            pe.del_flag = 0
            <if test="dailyProjectDetailsFindPageDTO.taskName != null and dailyProjectDetailsFindPageDTO.taskName != '' ">
                AND pe.task_name like concat('%',#{dailyProjectDetailsFindPageDTO.taskName},'%')
            </if>
            <if test="dailyProjectDetailsFindPageDTO.userRealName != null and dailyProjectDetailsFindPageDTO.userRealName != '' ">
                AND pe.user_real_name like concat('%',#{dailyProjectDetailsFindPageDTO.userRealName},'%')
            </if>
            <if test="dailyProjectDetailsFindPageDTO.status != null and dailyProjectDetailsFindPageDTO.status == 0 ">
                AND pe.approval_status IN (2)
            </if>
            <if test="dailyProjectDetailsFindPageDTO.status != null and dailyProjectDetailsFindPageDTO.status == 1 ">
                AND pe.approval_status IN (3,4)
            </if>
            <if test="dailyProjectDetailsFindPageDTO.projectId != null and dailyProjectDetailsFindPageDTO.projectId != '' ">
                AND pe.project_id = #{dailyProjectDetailsFindPageDTO.projectId}
            </if>
            <if test="dailyProjectDetailsFindPageDTO.submissionDate != null">
                AND DATE_FORMAT(pe.submission_date, '%Y-%m-%d') =
                DATE_FORMAT(#{dailyProjectDetailsFindPageDTO.submissionDate},'%Y-%m-%d')
            </if>
            GROUP BY pe.id
        </where>
    </select>

    <select id="dailyProjectDetailsFindPage2VO" resultType="com.gok.pboot.pms.entity.vo.DailyProjectDetailsFindPageVO">
        SELECT
        pe.id AS 'id',
        pe.user_real_name AS 'userRealName',
        pe.task_name AS 'taskName',
        pe.submission_date AS 'submissionDate',
        (CASE WHEN mdp.filling_state = 0 THEN '正常'
        WHEN mdp.filling_state = 1 THEN '滞后' END) AS 'fillingStateName',
        pe.normal_hours AS 'normalHours',
        pe.added_hours AS 'addedHours',
        pe.description AS 'description',
        pe.approval_reason AS 'approvalReason',
        pe.approval_status AS 'approvalStatus',
        pe.work_type AS 'workType',
        (CASE WHEN pe.approval_status = 0 THEN '未提交'
        WHEN pe.approval_status = 1 THEN '已退回'
        WHEN pe.approval_status = 2 THEN '待审核'
        WHEN pe.approval_status = 3 THEN '不通过'
        WHEN pe.approval_status = 4 THEN '已通过' END) AS 'approvalStatusName',
        pe.modifier AS 'approvalName',
        pe.daily_paper_id AS 'dailyPaperId',
        pe.user_id AS 'userId',
        pe.project_id AS 'projectId',
        t1.description AS 'yesterdayPlan',
        <choose>
            <when test="dailyProjectDetailsFindPageDTO.userIds != null and dailyProjectDetailsFindPageDTO.userIds.size() > 0">
                0 AS isEdit
            </when>
            <otherwise>
                1 AS isEdit
            </otherwise>
        </choose>
        FROM mhour_daily_paper_entry pe
        LEFT JOIN mhour_daily_paper mdp ON pe.daily_paper_id = mdp.id
        LEFT JOIN (
        SELECT
        mtppe.user_real_name AS userRealName,
        mtppe.project_id AS projectId,
        mtppe.task_name AS taskname,
        mtppe.description AS description
        FROM mhour_tomorrow_plan_paper_entry mtppe
        <where>
            mtppe.del_flag = 0
            <if test="dailyProjectDetailsFindPageDTO.projectId != null and dailyProjectDetailsFindPageDTO.projectId != '' ">
                AND mtppe.project_id = #{dailyProjectDetailsFindPageDTO.projectId}
            </if>
            <if test="dailyProjectDetailsFindPageDTO.taskName != null and dailyProjectDetailsFindPageDTO.taskName != '' ">
                AND mtppe.task_name like concat('%',#{dailyProjectDetailsFindPageDTO.taskName},'%')
            </if>
            <if test="dailyProjectDetailsFindPageDTO.userRealName != null and dailyProjectDetailsFindPageDTO.userRealName != '' ">
                AND mtppe.user_real_name like concat('%',#{dailyProjectDetailsFindPageDTO.userRealName},'%')
            </if>
            <if test="dailyProjectDetailsFindPageDTO.submissionDate != null">
                AND DATE_FORMAT(mtppe.submission_date, '%Y-%m-%d') <![CDATA[ < ]]>
                DATE_FORMAT(#{dailyProjectDetailsFindPageDTO.submissionDate},'%Y-%m-%d')
            </if>
            ORDER BY mtppe.submission_date DESC LIMIT 1
        </where>
        ) t1 ON t1.userRealName = pe.user_real_name AND t1.projectId = pe.project_id AND t1.taskName = pe.task_name
        <where>
            pe.del_flag = 0
            <if test="dailyProjectDetailsFindPageDTO.taskName != null and dailyProjectDetailsFindPageDTO.taskName != '' ">
                AND pe.task_name like concat('%',#{dailyProjectDetailsFindPageDTO.taskName},'%')
            </if>
            <if test="dailyProjectDetailsFindPageDTO.userRealName != null and dailyProjectDetailsFindPageDTO.userRealName != '' ">
                AND pe.user_real_name like concat('%',#{dailyProjectDetailsFindPageDTO.userRealName},'%')
            </if>
            <if test="dailyProjectDetailsFindPageDTO.status != null and dailyProjectDetailsFindPageDTO.status == 0 ">
                AND pe.approval_status IN (2)
            </if>
            <if test="dailyProjectDetailsFindPageDTO.userIds != null and dailyProjectDetailsFindPageDTO.userIds.size() > 0">
                AND pe.user_id IN
                <foreach collection="dailyProjectDetailsFindPageDTO.userIds" item="userId" open="(" separator=","
                         close=")">
                    #{userId}
                </foreach>
            </if>
            <if test="dailyProjectDetailsFindPageDTO.status != null and dailyProjectDetailsFindPageDTO.status == 1 ">
                AND pe.approval_status IN (3,4)
            </if>
            <if test="dailyProjectDetailsFindPageDTO.projectId != null and dailyProjectDetailsFindPageDTO.projectId != '' ">
                AND pe.project_id = #{dailyProjectDetailsFindPageDTO.projectId}
            </if>
            <if test="dailyProjectDetailsFindPageDTO.submissionDate != null">
                AND DATE_FORMAT(pe.submission_date, '%Y-%m-%d') =
                DATE_FORMAT(#{dailyProjectDetailsFindPageDTO.submissionDate},'%Y-%m-%d')
            </if>
            GROUP BY pe.id
        </where>
    </select>


    <select id="countAuditsByAuditorUserId" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM mhour_daily_paper_entry entry,
        mhour_privilege priv
        WHERE
        entry.del_flag = 0
        AND
        priv.del_flag = 0
        AND
        priv.user_id = #{userId}
        AND
        (
        entry.project_id = priv.project_id
        OR
        entry.user_dept_id = priv.dept_id
        )
        AND
        entry.approval_status = 2
        <if test="excludeProjectIds != null and excludeProjectIds.size() > 0">
            AND entry.project_id NOT IN
            <foreach collection="excludeProjectIds" item="exProjectId" open="(" separator="," close=")">
                #{exProjectId}
            </foreach>
        </if>
    </select>

    <select id="findByProjectIds" resultType="com.gok.pboot.pms.entity.DailyPaperEntry">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mhour_daily_paper_entry pe
        WHERE
        del_flag = 0
        AND
        pe.project_id IN
        <foreach collection="list" open="(" separator="," close=")" item="projectId">
            #{projectId}
        </foreach>
    </select>

    <update id="updateApprovalStatusById">
        UPDATE mhour_daily_paper_entry
        SET
        approval_status = #{changeApprovalStatusDTO.approvalStatus},
        modifier = #{changeApprovalStatusDTO.approvalName},
        <if test="changeApprovalStatusDTO.approvalReason!= null and changeApprovalStatusDTO.approvalReason != '' ">
            approval_reason = #{changeApprovalStatusDTO.approvalReason},
        </if>
        modifier_id = #{changeApprovalStatusDTO.approvalId},
        mtime = #{changeApprovalStatusDTO.mtime}
        WHERE
        id = #{changeApprovalStatusDTO.id}
    </update>


    <update id="batchApproval">
        UPDATE mhour_daily_paper_entry
        SET
        approval_status = 4,
        modifier = #{changeApprovalStatusDTO.approvalName},
        modifier_id = #{changeApprovalStatusDTO.approvalId},
        mtime = #{changeApprovalStatusDTO.mtime}
        WHERE
        id IN
        <foreach collection="ids" separator="," item="item" open="(" close=")">#{item}</foreach>
    </update>

    <update id="updateTaskNameByTaskId">
        UPDATE mhour_daily_paper_entry
        SET
            task_name = #{taskName}
        WHERE
            task_id = #{taskId}
    </update>

    <select id="findByUserIdsAndProjectIdsAndDateRange" resultType="com.gok.pboot.pms.entity.vo.DailyPaperEntryVO">
        SELECT
        paper.user_status AS 'userStatus',
        <include refid="Base_Column_List"/>,
        pr.project_status AS 'projectStatus',
        (CASE WHEN pr.is_not_internal_project = 1 and pr.project_type = 0 THEN '公司信息化'
        WHEN pr.is_not_internal_project = 1 and pr.project_type = 1 THEN '通用课程开发'
        WHEN pr.is_not_internal_project = 1 and pr.project_type = 2 THEN '自研产品研发'
        WHEN pr.is_not_internal_project = 1 and pr.project_type = 3 THEN '标准化解决方案打造'
        WHEN pr.is_not_internal_project = 1 and pr.project_type = 4 THEN '专项人才供应链构建'
        WHEN pr.is_not_internal_project = 1 and pr.project_type = 99 THEN '部门工作'
        END) AS 'projectTypeName'
        FROM
        mhour_daily_paper_entry pe
        left join mhour_daily_paper paper on pe.daily_paper_id = paper.id
        LEFT JOIN project_info pr ON pr.id = pe.project_id
        <where>
            pe.del_flag = 0
            AND
            pe.approval_status = 4
            <if test="userIds != null and userIds.size() > 0">
                AND
                pe.user_id IN
                <foreach collection="userIds" open="(" separator="," close=")" item="userId">
                    #{userId}
                </foreach>
            </if>
            <if test="projectIds != null and projectIds.size() > 0">
                AND
                pe.project_id IN
                <foreach collection="projectIds" open="(" separator="," close=")" item="projectId">
                    #{projectId}
                </foreach>
            </if>
            <if test="startDate != null and endDate == null">
                AND pe.submission_date &gt;= #{startDate}
            </if>
            <if test="endDate != null and startDate == null">
                AND pe.submission_date &lt;= #{endDate}
            </if>
            <if test="startDate != null and endDate != null">
                AND pe.submission_date BETWEEN #{startDate} AND #{endDate}
            </if>
            <if test="taskId != null and taskId.size() > 0">
                AND pe.task_id IN
                <foreach collection="taskId" open="(" separator="," close=")" item="taskId">
                    #{taskId}
                </foreach>
            </if>
            <if test="deptIds != null and deptIds.size() > 0">
                AND pe.user_dept_id IN
                <foreach collection="deptIds" separator="," item="deptId" open="(" close=")">
                    #{deptId}
                </foreach>
            </if>
            <if test="personnelStatus != null ">
                AND paper.user_status = #{personnelStatus}
            </if>
            <if test="dto.projectTypeList != null and dto.projectTypeList.size()>0">
                AND pr.project_type IN
                <foreach collection="dto.projectTypeList" item="i" open="(" separator="," close=")">
                      #{i}
                </foreach>
            </if>
            <if test="dto.projectStatusList != null and dto.projectStatusList.size()>0">
                AND pr.project_status IN
                <foreach collection="dto.projectStatusList" item="i" open="(" separator="," close=")">
                      #{i}
                </foreach>
            </if>
            <if test="dto.isNotInternalProject != null ">
                AND pr.is_not_internal_project = #{dto.isNotInternalProject}
            </if>
        </where>
        ORDER BY paper.submission_date DESC,paper.submission_time DESC

    </select>

    <select id="sumHoursByUserIdsAndProjectIdsAndDateRange" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(pe.normal_hours + pe.added_hours), 0)
        FROM mhour_daily_paper_entry pe
        LEFT JOIN project_info pr ON pr.id = pe.project_id
        left join mhour_daily_paper paper on pe.daily_paper_id = paper.id
        <where>
            pe.del_flag = 0
            AND
            pe.approval_status = 4
            <if test="userIds != null and userIds.size() > 0">
                AND
                pe.user_id IN
                <foreach collection="userIds" open="(" separator="," close=")" item="userId">
                    #{userId}
                </foreach>
            </if>
            <if test="projectIds != null and projectIds.size() > 0">
                AND
                pe.project_id IN
                <foreach collection="projectIds" open="(" separator="," close=")" item="projectId">
                    #{projectId}
                </foreach>
            </if>
            <if test="startDate != null and endDate == null">
                AND pe.submission_date &gt;= #{startDate}
            </if>
            <if test="endDate != null and startDate == null">
                AND pe.submission_date &lt;= #{endDate}
            </if>
            <if test="startDate != null and endDate != null">
                AND pe.submission_date BETWEEN #{startDate} AND #{endDate}
            </if>
            <if test="taskId != null and taskId.size() > 0">
                AND pe.task_id IN
                <foreach collection="taskId" open="(" separator="," close=")" item="taskId">
                    #{taskId}
                </foreach>
            </if>
            <if test="deptIds != null and deptIds.size() > 0">
                AND pe.user_dept_id IN
                <foreach collection="deptIds" separator="," item="deptId" open="(" close=")">
                    #{deptId}
                </foreach>
            </if>
            <if test="personnelStatus != null ">
                AND paper.user_status = #{personnelStatus}
            </if>
            <if test="dto.projectTypeList != null and dto.projectTypeList.size()>0">
                AND pr.project_type IN
                <foreach collection="dto.projectTypeList" item="i" open="(" separator="," close=")">
                      #{i}
                </foreach>
            </if>
            <if test="dto.projectStatusList != null and dto.projectStatusList.size()>0">
                AND pr.project_status IN
                <foreach collection="dto.projectStatusList" item="i" open="(" separator="," close=")">
                      #{i}
                </foreach>
            </if>
            <if test="dto.isNotInternalProject != null ">
                AND pr.is_not_internal_project = #{dto.isNotInternalProject}
            </if>
        </where>
    </select>

    <select id="findByDailyPaperIdsAndProjectNameLike" resultType="com.gok.pboot.pms.entity.DailyPaperEntry">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        mhour_daily_paper_entry pe
        <where>
            del_flag = 0
            AND daily_paper_id IN
            <foreach collection="dailyPaperIds" open="(" separator="," close=")" item="dpId">
                #{dpId}
            </foreach>
            <if test="projectNameLike != null">
                <bind value="'%' + projectNameLike + '%'" name="pNLike"/>
                AND project_name LIKE #{pNLike}
            </if>
            <if test="approvalStatus != null">
                AND approval_status = #{approvalStatus}
            </if>
        </where>
    </select>

    <select id="findByProjectIdsAndSubmissionDateBetween"
            resultType="com.gok.pboot.pms.entity.vo.DailyPaperEntryVO">
        SELECT
        <include refid="Base_Column_List"/>,
        mp.is_not_internal_project AS "isInsideProject"
        FROM mhour_daily_paper_entry pe
        LEFT JOIN project_info mp ON mp.id = pe.project_id
        WHERE
        pe.del_flag = 0
        AND
        pe.user_dept_id IN
        <foreach collection="deptIds" open="(" separator="," close=")" item="deptId">
            #{deptId}
        </foreach>
        AND pe.user_id IN
        <foreach collection="userIds" item="uId" open="(" separator="," close=")">
            #{uId}
        </foreach>
        AND pe.approval_status = #{approvalStatus}
        AND pe.submission_date BETWEEN #{startDate} AND #{endDate}
        <if test="projectIds != null">
            AND mp.id IN
            <foreach collection="projectIds" item="pId" open="(" separator="," close=")">
                #{pId}
            </foreach>
        </if>
    </select>

    <select id="findForExport" resultType="com.gok.pboot.pms.entity.vo.UnReviewedDailyPaperEntryExcelVO">
        SELECT
        t1.id AS 'id',
        t1.projectCode as 'projectCode',
        t1.projectName as 'projectName',
        t1.submissionDate as 'submissionDate',
        t1.userRealName as 'userRealName',
        t1.projectDeptId as 'projectDeptId',
        t1.auditPerson as 'auditPerson',
        t1.salesmanUserName as 'salesmanUserName',
        t1.preSaleUserName as 'preSaleUserName',
        t1.managerUserName as 'managerUserName',
        SUM(t1.normalHours) AS 'normalHours',
        SUM(t1.addedHours) AS 'addedHours',
        SUM(t1.workOvertimeHours) AS 'workOvertimeHours',
        SUM(t1.restOvertimeHours) AS 'restOvertimeHours',
        SUM(t1.holidayOvertimeHours) AS 'holidayOvertimeHours',
        SUM(t1.projectHours) AS 'projectHours',
        sum(t1.preSaleHours) AS 'preSaleHours',
        sum(t1.afterSaleHours) AS 'afterSaleHours',
        t1.projectStatus,
        t1.projectTypeName,
        (CASE WHEN t1.is_not_internal_project = 1 THEN '是'
        WHEN t1.is_not_internal_project = 2 THEN '否' END ) AS 'isNotInternalProject'
        from (
        SELECT
        mpj.id AS 'id',
        mpj.`item_no` as 'projectCode',
        mpj.item_name as 'projectName',
        pe.submission_date as 'submissionDate',
        pe.user_real_name as 'userRealName',
        mpj.first_level_department_id as 'projectDeptId',
        mp.auditPerson as 'auditPerson',
        mpj.project_salesperson as 'salesmanUserName',
        mpj.pre_sale_user_name as 'preSaleUserName',
        mpj.manager_user_name as 'managerUserName',
        SUM(pe.normal_hours) AS 'normalHours',
        SUM(pe.added_hours) AS 'addedHours',
        SUM(work_overtime_hours) AS 'workOvertimeHours',
        SUM(rest_overtime_hours) AS 'restOvertimeHours',
        SUM(holiday_overtime_hours) AS 'holidayOvertimeHours',
        SUM(pe.normal_hours) AS 'projectHours',
        SUM(CASE pe.work_type WHEN 0 THEN pe.normal_hours  ELSE 0 END) AS 'preSaleHours',
        SUM(CASE pe.work_type WHEN 1 THEN pe.normal_hours  ELSE 0 END) AS 'afterSaleHours',
        mpj.project_status AS 'projectStatus',
        (CASE
        WHEN mpj.is_not_internal_project = 1 and mpj.project_type = 0 THEN '公司信息化'
        WHEN mpj.is_not_internal_project = 1 and mpj.project_type = 1 THEN '通用课程开发'
        WHEN mpj.is_not_internal_project = 1 and mpj.project_type = 2 THEN '自研产品研发'
        WHEN mpj.is_not_internal_project = 1 and mpj.project_type = 3 THEN '标准化解决方案打造'
        WHEN mpj.is_not_internal_project = 1 and mpj.project_type = 4 THEN '专项人才供应链构建'
        WHEN mpj.is_not_internal_project = 1 and mpj.project_type = 99 THEN '部门工作'
        WHEN mpj.is_not_internal_project = 2 THEN '/'
        END) AS 'projectTypeName',
        mpj.is_not_internal_project
        FROM
        mhour_daily_paper_entry pe
        LEFT JOIN
        ( SELECT mp.project_id, group_concat( mp.user_name ) as 'auditPerson' FROM mhour_privilege mp WHERE mp.del_flag
        = 0 GROUP BY mp.project_id ) mp ON pe.project_id = mp.project_id
        LEFT JOIN project_info mpj on pe.project_id = mpj.id
        WHERE
        pe.del_flag = 0
        AND pe.approval_status IN (2, 4)
        AND pe.submission_date BETWEEN #{dto.startTime} AND #{dto.endTime}
        <if test="projectIds != null and projectIds.size() > 0">
            AND pe.project_id IN
            <foreach collection="projectIds" item="pId" open="(" separator="," close=")">
                #{pId}
            </foreach>
        </if>
        <if test="dto.getProjectId != null">
            AND pe.project_id = #{dto.projectId}
        </if>
        <if test="dto.projectStatus != null">
            AND mpj.project_status = #{dto.projectStatus}
        </if>
        <if test="approvalStatus != null">
            AND pe.approval_status = #{approvalStatus}
        </if>
        <if test="dto.isNotInternalProject != null and dto.isNotInternalProject > 0">
            AND mpj.is_not_internal_project = #{dto.isNotInternalProject}
        </if>
        <if test="dto.projectTypeList != null and dto.projectTypeList.size()>0">
                AND mpj.project_type IN
                <foreach collection="dto.projectTypeList" item="i" open="(" separator="," close=")">
                      #{i}
                </foreach>
        </if>
        <if test="dto.projectStatusList != null and dto.projectStatusList.size()>0">
                AND mpj.project_status IN
                <foreach collection="dto.projectStatusList" item="i" open="(" separator="," close=")">
                      #{i}
                </foreach>
        </if>
        GROUP BY mpj.id
        <if test="approvalStatus != 2">
            UNION ALL
                    SELECT mpj.id AS 'id',
                    mpj.`item_no` as 'projectCode',
                    mpj.item_name as 'projectName',
                    a.belong_date as 'submissionDate',
                    r.alias_name as 'userRealName',
                    mpj.first_level_department_id as 'projectDeptId',
                    null as 'auditPerson',
                    mpj.project_salesperson as 'salesmanUserName',
                    mpj.pre_sale_user_name as 'preSaleUserName',
                    mpj.manager_user_name as 'managerUserName',
                    0 AS 'normalHours', 0 AS 'addedHours',
                    0 AS 'workOvertimeHours',
                    0 AS 'restOvertimeHours',
                    0 AS 'holidayOvertimeHours',
                    0 AS 'projectHours',
                    SUM( CASE WHEN a.work_type = 0 THEN a.hour_data ELSE 0 END )  AS "preSaleHours",
                    SUM( CASE WHEN a.work_type = 1 THEN a.hour_data ELSE 0 END )  AS "afterSaleHours",
                    mpj.project_status AS 'projectStatus',
                    (CASE
                            WHEN mpj.is_not_internal_project = 1 and mpj.project_type = 0 THEN '公司信息化'
                            WHEN mpj.is_not_internal_project = 1 and mpj.project_type = 1 THEN '通用课程开发'
                            WHEN mpj.is_not_internal_project = 1 and mpj.project_type = 2 THEN '自研产品研发'
                            WHEN mpj.is_not_internal_project = 1 and mpj.project_type = 3 THEN '标准化解决方案打造'
                            WHEN mpj.is_not_internal_project = 1 and mpj.project_type = 4 THEN '专项人才供应链构建'
                            WHEN mpj.is_not_internal_project = 1 and mpj.project_type = 99 THEN '部门工作'
                            WHEN mpj.is_not_internal_project = 2 THEN '/'
                            END) AS 'projectTypeName',
                    mpj.is_not_internal_project
                    FROM mhour_compensatory_leave_data a
                                 LEFT JOIN project_info mpj ON mpj.id = a.xmmc
                                 LEFT JOIN mhour_roster r ON r.id = a.oa_id
                    WHERE
                    a.belong_date NOT IN (SELECT day_date FROM mhour_holiday)
                      AND a.type = '3'
           AND  a.belong_date BETWEEN #{dto.startTime} AND #{dto.endTime}
            <if test="projectIds != null and projectIds.size() > 0">
                AND a.xmmc IN
                <foreach collection="projectIds" item="pId" open="(" separator="," close=")">
                    #{pId}
                </foreach>
            </if>
            <if test="dto.getProjectId != null">
                AND a.xmmc = #{dto.projectId}
            </if>
            <if test="dto.projectStatus != null">
                AND mpj.project_status = #{dto.projectStatus}
            </if>
            <if test="dto.isNotInternalProject != null and dto.isNotInternalProject > 0">
                AND mpj.is_not_internal_project = #{dto.isNotInternalProject}
            </if>
            <if test="dto.projectTypeList != null and dto.projectTypeList.size() > 0">
                AND mpj.project_type IN
                <foreach collection="dto.projectTypeList" item="i" open="(" separator="," close=")">
                    #{i}
                </foreach>
            </if>
            <if test="dto.projectStatusList != null and dto.projectStatusList.size() > 0">
                AND mpj.project_status IN
                <foreach collection="dto.projectStatusList" item="i" open="(" separator="," close=")">
                    #{i}
                </foreach>
            </if>
            GROUP BY a.xmmc
        </if>
         ORDER BY id, submissionDate
         )t1 GROUP BY t1.id
    </select>

    <select id="findProjectIdsByApprovalStatusTagForProjectHourSum" resultType="java.lang.Long">
        SELECT DISTINCT project_id
        FROM mhour_daily_paper_entry
        WHERE
        del_flag = 0
        AND approval_status
        <if test="tag == 1">
            = 2
        </if>
        <if test="tag == 2">
            != 4
        </if>
        <if test="dto.startTime != null and dto.endTime != null">
            AND submission_date BETWEEN #{dto.startTime} AND #{dto.endTime}
        </if>
    </select>

    <select id="findProjectIdAndApprovalStatusByProjectIdsAndSubmissionDateBetween"
            resultType="com.gok.pboot.pms.entity.DailyPaperEntry">
        SELECT
        project_id,
        approval_status
        FROM mhour_daily_paper_entry
        WHERE
        del_flag = 0
        AND
        project_id IN
        <foreach collection="projectIds" open="(" separator="," close=")" item="projectId">
            #{projectId}
        </foreach>
        AND
        submission_date BETWEEN #{startDate} AND #{endDate}
    </select>

    <select id="queryAllAndWorkCode" resultType="com.gok.pboot.pms.entity.vo.DailyPaperEntryExcelVO">
        SELECT
        pe.project_id AS 'projectId',
        pe.project_name AS 'projectName',
        pe.task_name AS 'taskName',
        pe.user_real_name AS 'userRealName',
        mr.work_code AS 'workCode',
        pe.submission_date AS 'submissionDate',
        pe.description AS 'description',
        pe.normal_hours AS 'normalHours',
        pe.added_hours AS 'addedHours',
        pe.normal_hours + pe.added_hours as 'totalHours',
        pe.user_id AS 'userId',
        pe.user_dept_id AS 'userDeptId',
        paper.user_status AS 'userStatus',
        pr.project_status AS 'projectStatus',
        (CASE WHEN pr.is_not_internal_project = 1 and pr.project_type = 0 THEN '公司信息化'
        WHEN pr.is_not_internal_project = 1 and pr.project_type = 1 THEN '通用课程开发'
        WHEN pr.is_not_internal_project = 1 and pr.project_type = 2 THEN '自研产品研发'
        WHEN pr.is_not_internal_project = 1 and pr.project_type = 3 THEN '标准化解决方案打造'
        WHEN pr.is_not_internal_project = 1 and pr.project_type = 4 THEN '专项人才供应链构建'
        WHEN pr.is_not_internal_project = 1 and pr.project_type = 99 THEN '部门工作'
        END) AS 'projectTypeName'
        FROM mhour_daily_paper_entry pe
        left join mhour_roster mr on pe.user_id = mr.id
        left join mhour_daily_paper paper on pe.daily_paper_id = paper.id
        LEFT JOIN project_info pr ON pr.id = pe.project_id
        WHERE
        pe.del_flag = 0
        AND
        pe.approval_status = 4
        <if test="filter.dailyPaperEntryIds != null and filter.dailyPaperEntryIds.size() > 0">
            AND pe.id IN
            <foreach collection="filter.dailyPaperEntryIds" item="dailyPaperEntryId" open="(" separator="," close=")">
                #{dailyPaperEntryId}
            </foreach>
        </if>
        <if test="filter.userIds != null and filter.userIds.size() > 0">
            AND pe.user_id IN
            <foreach collection="filter.userIds" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
        <if test="filter.taskIds != null and filter.taskIds.size() > 0">
            AND pe.task_id IN
            <foreach collection="filter.taskIds" item="taskId" open="(" separator="," close=")">
                #{taskId}
            </foreach>
        </if>
        <if test="filter.projectIds != null and filter.projectIds.size() > 0">
            AND pe.project_id IN
            <foreach collection="filter.projectIds" item="projectId" open="(" separator="," close=")">
                #{projectId}
            </foreach>
        </if>
        <if test="filter.startDate != null and filter.endDate != null">
            AND pe.submission_date BETWEEN #{filter.startDate} AND #{filter.endDate}
        </if>
        <if test="filter.deptIds != null and filter.deptIds != ''">
            AND pe.user_dept_id IN
            <include refid="com.gok.pboot.pms.mapper.CommonMapper.splitLongsStr">
                <property name="longsStr" value="filter.deptIds"/>
            </include>
        </if>
        <if test="filter.personnelStatus != null ">
            AND paper.user_status = #{filter.personnelStatus}
        </if>
        <if test="filter.isNotInternalProject != null ">
            AND pr.is_not_internal_project = #{filter.isNotInternalProject}
        </if>
        <if test="filter.projectTypeList != null and filter.projectTypeList.size()>0">
                AND pr.project_type IN
                <foreach collection="filter.projectTypeList" item="i" open="(" separator="," close=")">
                      #{i}
                </foreach>
        </if>
        <if test="filter.projectStatusList != null and filter.projectStatusList.size()>0">
                AND pr.project_status IN
                <foreach collection="filter.projectStatusList" item="i" open="(" separator="," close=")">
                      #{i}
                </foreach>
        </if>
    </select>

    <select id="countAuditsByUserIdsAndProjectIds" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM mhour_daily_paper_entry
        WHERE
        del_flag = 0
        AND
        approval_status = 2
        AND project_id IN
        <foreach collection="projectIds" item="projectId" open="(" separator="," close=")">
            #{projectId}
        </foreach>
        AND user_id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </select>

    <select id="findProjectIdSetByApprovalStatusForProjectHourSum" resultType="java.lang.Long">
        SELECT DISTINCT project_id
        FROM mhour_daily_paper_entry
        WHERE
        del_flag = 0
        AND approval_status = #{approvalStatus}
        <if test="dto.startTime != null and dto.endTime != null">
            AND submission_date BETWEEN #{dto.startTime} AND #{dto.endTime}
        </if>
        <if test="dto.projectIds != null and dto.projectIds != null">
            AND project_id in
            <foreach collection="dto.projectIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="selectPaperView" resultType="com.gok.pboot.pms.entity.vo.DailyReviewProjectAuditPageVO">
        SELECT
        hdpe.id id,
        hdpe.user_id userId,
        hdpe.user_real_name userRealName,
        hdpe.task_id taskId,
        hdpe.task_name taskName,
        hdpe.work_type workType,
        hdpe.submission_date submissionDate,
        hdpe.ctime commitDate,
        <if test="filter.auditStatus == 0">
            (CASE WHEN hdp.filling_state = 0 THEN '正常'
            WHEN hdp.filling_state = 1 THEN '滞后'
            END) AS 'fillingStateName',
        </if>
        hdpe.normal_hours normalHours,
        hdpe.added_hours addedHours,
        hdpe.description description,
        <if test="filter.auditStatus == 1">
            hdpe.modifier approvalName,
        </if>
        hdpe.approval_status approvalStatus,
        (case when hdpe.approval_status = 1 then '已退回'
        when hdpe.approval_status = 2 then '待审核'
        when hdpe.approval_status = 3 then '不通过'
        when hdpe.approval_status = 4 then '已通过'
        end) approvalStatusName,
        -- getYesterDayPlan是一个自定义函数，函数大概意思就是按用户、项目、任务去查询距离当前条数据最近的日报的明日计划
        -- getYesterDayPlan(hdpe.user_id, hdpe.project_id, hdpe.task_id, hdpe.submission_date) yesterdayPlan,
        <choose>
            <when test="filter.userIds != null and filter.userIds.size() > 0">
                0 AS isEdit
            </when>
            <otherwise>
                1 AS isEdit
            </otherwise>
        </choose>
        FROM mhour_project hp
        LEFT JOIN mhour_daily_paper_entry hdpe ON hp.id = hdpe.project_id
        LEFT JOIN mhour_daily_paper hdp ON hdpe.daily_paper_id = hdp.id
        WHERE hp.del_flag = 0
        AND hdpe.del_flag = 0
        AND hdp.del_flag = 0
        AND hp.id = #{filter.projectId}
        <if test="filter.auditStatus == 0">
            AND (hdpe.approval_status = 2 OR hdpe.approval_status IS NULL)
        </if>
        <if test="filter.auditStatus == 1">
            AND (hdpe.approval_status IN (1, 3, 4) OR hdpe.approval_status IS NULL)
        </if>
        <if test="filter.startTime != null and filter.startTime != ''">
            AND hdpe.submission_date &gt;= #{filter.startTime}
        </if>
        <if test="filter.endTime != null and filter.endTime != ''">
            AND hdpe.submission_date &lt;= #{filter.endTime}
        </if>
        <if test="filter.taskName != null and filter.taskName != ''">
            AND hdpe.task_name like concat('%', #{filter.taskName}, '%')
        </if>
        <if test="filter.username != null and filter.username != ''">
            AND hdpe.user_real_name like concat('%', #{filter.username}, '%')
        </if>
        <if test="childUserIds != null and childUserIds.size() > 0">
            AND hdpe.user_id IN
            <foreach collection="childUserIds" open="(" close=")" item="id" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="filter.userIds != null and filter.userIds.size() > 0">
            AND hdpe.user_id IN
            <foreach collection="filter.userIds" open="(" close=")" item="id" separator=",">
                #{id}
            </foreach>
        </if>
        group by hdpe.id ORDER BY hdp.submission_date DESC , hdp.user_id
    </select>

    <select id="selectDailyPaperById" resultType="com.gok.pboot.pms.entity.dto.DailyPaperCommonDTO">
        select daily_paper_id,project_id, project_name, submission_date, user_id
        from mhour_daily_paper_entry
        where del_flag = 0
        and id = #{id}
    </select>
    <select id="findByDailyPaperIds" resultType="com.gok.pboot.pms.entity.DailyPaperEntry">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mhour_daily_paper_entry pe
        WHERE
        pe.del_flag = 0 and pe.approval_status = ${@<EMAIL>()}
        <if test="list != null and list.size != ''">
            AND pe.daily_paper_id in
            <foreach collection="list" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        AND submission_date BETWEEN #{startDate} AND #{endDate}
        ORDER BY pe.ctime DESC
    </select>
    <select id="findUnAuditDailyByTime" resultType="com.gok.pboot.pms.entity.vo.DailyReviewFindPageVO">
        SELECT
        t1.submission_date AS 'submissionDate',
        t1.project_id AS 'projectId',
        t1.project_name AS 'projectName',
        t1.user_id AS 'userId',
        t1.number,
        t1.normalHours,
        t1.addedHours,
        t2.numberTotal
        FROM
        (SELECT
        pe.submission_date,
        pe.project_id,
        pe.project_name ,
        pe.user_id ,
        COUNT(1) AS 'number' ,
        SUM(pe.normal_hours) AS 'normalHours',
        SUM(pe.added_hours) AS 'addedHours'
        FROM mhour_daily_paper_entry pe
        <where>
            pe.del_flag = 0 and pe.approval_status = 2
            AND pe.submission_date BETWEEN #{startDate} AND #{endDate}
            GROUP BY pe.submission_date,pe.project_id
            ORDER BY pe.submission_date DESC,pe.project_name
        </where>
        ) t1
        LEFT JOIN (
        SELECT
        pe.submission_date,
        pe.project_id,
        COUNT(1) AS 'numberTotal'
        FROM mhour_daily_paper_entry pe
        <where>
            pe.del_flag = 0 AND pe.approval_status IN (2,3,4)
            AND pe.submission_date BETWEEN #{startDate} AND #{endDate}
            GROUP BY pe.submission_date,pe.project_id
        </where>
        ) t2 ON t1.submission_date = t2.submission_date AND t1.project_id = t2.project_id
    </select>

    <select id="selectPageByUserId" resultType="com.gok.pboot.pms.entity.vo.PanelProjectSituationVO">
        SELECT project_id projectId,
        project_name projectName,
        MIN(submission_date) startTime,
        MAX(submission_date) endTime,
        SUM(normal_hours) normalHours,
        SUM(work_overtime_hours) workOvertimeHours,
        SUM(rest_overtime_hours) restOvertimeHours,
        SUM(holiday_overtime_hours) holidayOvertimeHours,
        SUM(added_hours) addedHours
        FROM mhour_daily_paper_entry
        WHERE del_flag = 0
        AND approval_status = 4
        AND user_id = #{filter.userId}
        <if test="filter.startTime != null and filter.endTime != null">
            AND submission_date &gt;= #{filter.startTime}
            AND submission_date &lt;= #{filter.endTime}
        </if>
        <if test="filter.projectName != null and filter.projectName != ''">
            AND project_name like concat('%', #{filter.projectName},'%')
        </if>
        GROUP BY project_id
        ORDER BY project_name
    </select>

    <select id="selectPanelProjectSituationDetailsPage"
            resultType="com.gok.pboot.pms.entity.vo.PanelProjectSituationDetailsVO">
        SELECT main.project_id projectId,
        main.project_name projectName,
        main.task_id taskId,
        main.task_name taskName,
        main.user_real_name userRealName,
        main.submission_date submissionDate,
        main.normal_hours normalHours,
        main.added_hours addedHours,
        main.description workContent,
        getYesterDayPlan(main.user_id, main.project_id, main.task_id, main.submission_date) yesterdayPlan
        FROM mhour_daily_paper_entry main
        WHERE main.del_flag = 0
        AND main.approval_status = 4
        AND main.user_id = #{filter.userId}
        AND main.project_id = #{filter.projectId}
        <if test="filter.startTime != null and filter.endTime != null">
            AND main.submission_date between #{filter.startTime} and #{filter.endTime}
        </if>
        <if test="filter.taskName != null and filter.taskName != ''">
            AND main.task_name like concat('%', #{filter.taskName}, '%')
        </if>
    </select>
    <select id="selectPageProjectSituationAnalysis"
            resultType="com.gok.pboot.pms.entity.vo.PanelProjectSituationAnalysisVO">
        SELECT
        hdpe.project_id projectId,
        hdpe.project_name projectName,
        MIN( hdpe.submission_date ) startTime,
        MAX( hdpe.submission_date ) endTime,
        SUM( hdpe.normal_hours ) normalHours,
        SUM(hdpe.work_overtime_hours) workOvertimeHours,
        SUM(hdpe.rest_overtime_hours) restOvertimeHours,
        SUM(hdpe.holiday_overtime_hours) holidayOvertimeHours,
        SUM( hdpe.added_hours ) addedHours
        FROM
        mhour_daily_paper_entry hdpe
        WHERE
        del_flag = 0
        AND approval_status = 4
        AND hdpe.user_id = #{filter.userId}
        <if test="filter.startTime != null and filter.endTime != null">
            and hdpe.submission_date &gt;= #{filter.startTime}
            and hdpe.submission_date &lt;= #{filter.endTime}
        </if>
        <if test="filter.projectName != null and filter.projectName != ''">
            and hdpe.project_name like concat('%', #{filter.projectName}, '%')
        </if>
        GROUP BY
        hdpe.project_id
        ORDER BY 	hdpe.project_name
    </select>

    <select id="selectListSituationAnalysis"
            resultType="com.gok.pboot.pms.entity.vo.SituationAnalysisDeptVO">
        SELECT
            x.projectId,
            x.projectName,
            x.userName,
            x.userId,
            x.deptId,
            x.isInsideProject,
            x.startTime,
            x.endTime,
            sum(x.normalHours) AS normalHours,
            sum(x.addedHours) AS addedHours ,
            sum(x.workOvertimeHours) AS workOvertimeHours ,
            sum(x.restOvertimeHours) AS restOvertimeHours ,
            sum(x.holidayOvertimeHours) AS holidayOvertimeHours,
            sum(x.leaveHours) AS leaveHours,
            sum(x.managementHours) AS managementHours
        FROM
        (
        SELECT
            hdpe.project_id projectId,
            hdpe.project_name projectName,
            hdpe.user_real_name userName,
            hdpe.user_id userId,
            hdpe.user_dept_id deptId,
            mp.is_not_internal_project isInsideProject,
            MIN( hdpe.submission_date ) startTime,
            MAX( hdpe.submission_date ) endTime,
            sum(hdpe.normal_hours) AS normalHours,
            sum(hdpe.added_hours) AS addedHours ,
            sum(hdpe.work_overtime_hours) AS workOvertimeHours ,
            sum(hdpe.rest_overtime_hours) AS restOvertimeHours ,
            sum(hdpe.holiday_overtime_hours) AS holidayOvertimeHours ,
            0.0 AS leaveHours,
            sum(hdpe_manage.normal_hours + hdpe_manage.added_hours) AS managementHours
        FROM
        mhour_daily_paper_entry hdpe
        LEFT JOIN (
        <!-- 查询特殊项目内部管理工时 -->
        SELECT
        pe.id AS id,
        pe.normal_hours AS normal_hours,
        pe.added_hours AS added_hours
        FROM mhour_daily_paper_entry pe
        LEFT JOIN project_info pi ON pi.id = pe.project_id
        LEFT JOIN mhour_entity_option eo ON pi.id = eo.entity_id AND eo.sign = 'special_project'
        WHERE
        pe.del_flag = 0
        AND
        eo.del_flag = 0
        ) hdpe_manage ON hdpe.id = hdpe_manage.id
        LEFT JOIN project_info mp ON mp.id = hdpe.project_id
        WHERE
        hdpe.del_flag = 0
        AND hdpe.user_dept_id IN
        <foreach collection="filter.deptIds" item="dId" open="(" separator="," close=")">
            #{dId}
        </foreach>
        AND hdpe.user_id IN
        <foreach collection="userIds" item="uId" open="(" separator="," close=")">
            #{uId}
        </foreach>
        <if test="projectIds != null">
            AND mp.id IN
            <foreach collection="projectIds" item="pId" open="(" separator="," close=")">
                #{pId}
            </foreach>
        </if>
        <if test="filter.approvalStatus != null">
            AND hdpe.approval_status = #{filter.approvalStatus}
        </if>
        <if test="filter.startTime != null and filter.endTime != null">
            and hdpe.submission_date &gt;= #{filter.startTime}
            and hdpe.submission_date &lt;= #{filter.endTime}
        </if>
        <if test="filter.flag == 1">
            GROUP BY
            hdpe.user_dept_id
        </if>
        <if test="filter.flag == 0">
            GROUP BY
            hdpe.user_id
        </if>
        union all
        SELECT
            a.xmmc projectId,
            b.item_name projectName,
            c.alias_name userName,
            a.oa_id userId,
            c.dept_id deptId,
            b.is_not_internal_project isInsideProject,
            MIN( a.belong_date ) startTime,
            MAX( a.belong_date ) endTime,
            0.0 AS normalHours,
            0.0 AS addedHours ,
            0.0 AS workOvertimeHours ,
            0.0 AS restOvertimeHours ,
            0.0 AS holidayOvertimeHours ,
            sum(a.hour_data) as leaveHours,
            0.0 AS managementHours
        FROM
        mhour_compensatory_leave_data a
        left JOIN project_info b on a.xmmc=b.id
        left JOIN mhour_roster c on a.oa_id = c.oa_id
        where
        c.dept_id IN
        <foreach collection="filter.deptIds" item="dId" open="(" separator="," close=")">
            #{dId}
        </foreach>
        AND a.oa_id IN
        <foreach collection="userIds" item="uId" open="(" separator="," close=")">
            #{uId}
        </foreach>
        <if test="projectIds != null">
            AND a.xmmc IN
            <foreach collection="projectIds" item="pId" open="(" separator="," close=")">
                #{pId}
            </foreach>
        </if>
        <if test="filter.startTime != null and filter.endTime != null">
            and a.belong_date &gt;= #{filter.startTime}
            and a.belong_date &lt;= #{filter.endTime}
        </if>
        <if test="filter.flag == 1">
            GROUP BY
            c.dept_id
        </if>
        <if test="filter.flag == 0">
            GROUP BY
            a.oa_id
        </if>
        ) x
        <if test="filter.flag == 1">
            GROUP BY
            x.deptId
        </if>
        <if test="filter.flag == 0">
            GROUP BY
            x.userId
        </if>
    </select>

    <select id="situationInSide"
            resultType="com.gok.pboot.pms.entity.vo.SituationAnalysisDeptVO">
        SELECT
        hdpe.project_id projectId,
        hdpe.project_name projectName,
        hdpe.user_real_name userName,
        hdpe.user_id userId,
        hdpe.user_dept_id deptId,
        mp.is_not_internal_project isInsideProject,
        MIN( hdpe.submission_date ) startTime,
        MAX( hdpe.submission_date ) endTime,
        IFNULL( sum( hdpe.normal_hours), 0) AS normalHours,
        IFNULL( sum( hdpe.added_hours), 0) AS addedHours
        FROM
        mhour_daily_paper_entry hdpe
        LEFT JOIN project_info mp ON mp.id = hdpe.project_id
        WHERE
        hdpe.del_flag = 0
        <if test="filter.insideProject != null">
            AND mp.is_not_internal_project = #{filter.insideProject}
        </if>
        <if test="filter.deptIds != null and filter.deptIds.size() != 0">
            AND hdpe.user_dept_id IN
            <foreach collection="filter.deptIds" item="deptId" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        AND hdpe.user_dept_id IN
        <foreach collection="filter.deptIds" item="dId" open="(" separator="," close=")">
            #{dId}
        </foreach>
        AND hdpe.user_id IN
        <foreach collection="userIds" item="uId" open="(" separator="," close=")">
            #{uId}
        </foreach>
        <if test="projectIds != null">
            AND mp.id IN
            <foreach collection="projectIds" item="pId" open="(" separator="," close=")">
                #{pId}
            </foreach>
        </if>
        <if test="filter.approvalStatus != null">
            AND hdpe.approval_status = #{filter.approvalStatus}
        </if>
        <if test="filter.startTime != null and filter.endTime != null">
            and hdpe.submission_date &gt;= #{filter.startTime}
            and hdpe.submission_date &lt;= #{filter.endTime}
        </if>
        <if test="filter.flag == 1">
            GROUP BY
            hdpe.user_dept_id
        </if>
        <if test="filter.flag == 0">
            GROUP BY
            hdpe.user_id
        </if>
    </select>
    <select id="selectPageSituationAnalysis"
            resultType="com.gok.pboot.pms.entity.vo.SituationAnalysisVO">

        SELECT
            x.id,
            x.projectId,
            x.projectName,
            x.userName,
            x.userId,
            x.deptId,
            x.isInsideProject,
            MIN( x.startTime ) startTime,
            MAX( x.endTime ) endTime,
            SUM( x.normalHours ) normalHours,
            SUM( x.addedHours ) addedHours,
            SUM( x.workOvertimeHours ) workOvertimeHours,
            SUM( x.restOvertimeHours ) restOvertimeHours,
            SUM( x.holidayOvertimeHours ) holidayOvertimeHours,
            SUM( x.leaveHours ) leaveHours
        from
        (
        SELECT
        CONCAT(hdpe.user_id, hdpe.user_dept_id) AS id,
        hdpe.project_id projectId,
        hdpe.project_name projectName,
        hdpe.user_real_name userName,
        hdpe.user_id userId,
        hdpe.user_dept_id deptId,
        mp.is_not_internal_project isInsideProject,
        MIN( hdpe.submission_date ) startTime,
        MAX( hdpe.submission_date ) endTime,
        SUM( hdpe.normal_hours ) normalHours,
        SUM( hdpe.added_hours ) addedHours,
        SUM( hdpe.work_overtime_hours ) workOvertimeHours,
        SUM( hdpe.rest_overtime_hours ) restOvertimeHours,
        SUM( hdpe.holiday_overtime_hours ) holidayOvertimeHours,
        0.0 as leaveHours
        FROM
        mhour_daily_paper_entry hdpe
        LEFT JOIN project_info mp ON mp.id = hdpe.project_id
        WHERE
        hdpe.del_flag = 0
        AND hdpe.approval_status = 4
        AND hdpe.user_id IN
        <foreach collection="userIds" item="uId" open="(" separator="," close=")">
            #{uId}
        </foreach>
        <if test="filter.deptIds != null and filter.deptIds.size() != 0">
            AND hdpe.user_dept_id IN
            <foreach collection="filter.deptIds" item="deptId" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="filter.startTime != null and filter.endTime != null">
            and hdpe.submission_date &gt;= #{filter.startTime}
            and hdpe.submission_date &lt;= #{filter.endTime}
        </if>
        <if test="filter.approvalStatus != null">
            AND hdpe.approval_status = #{filter.approvalStatus}
        </if>
        <if test="filter.userName != null and filter.userName != ''">
            and hdpe.user_real_name like concat('%', #{filter.userName} ,'%')
        </if>
        <if test="filter.userId != null and filter.userId != ''">
            and hdpe.user_id =#{filter.userId}
        </if>
        <if test="projectIds != null">
            AND hdpe.project_id IN
            <foreach collection="projectIds" item="pId" open="(" separator="," close=")">
                #{pId}
            </foreach>
        </if>
        <choose>
            <when test="filter.flag == 1">
                GROUP BY
                hdpe.user_dept_id
            </when>
            <when test="filter.flag == 0">
                GROUP BY
                hdpe.project_id,hdpe.user_real_name
            </when>
            <otherwise>
                GROUP BY hdpe.user_id
            </otherwise>
        </choose>
        union all
        SELECT
        CONCAT(a.oa_id, c.dept_id) AS id,
        a.xmmc projectId,
        b.item_name projectName,
        c.alias_name userName,
        a.oa_id userId,
        c.dept_id deptId,
        b.is_not_internal_project isInsideProject,
        MIN( a.belong_date ) startTime,
        MAX( a.belong_date ) endTime,
        0.0 as normalHours,
        0.0 as addedHours,
        0.0 as workOvertimeHours,
        0.0 as restOvertimeHours,
        0.0 as holidayOvertimeHours,
        sum(a.hour_data) as leaveHours
        from         mhour_compensatory_leave_data a
        left JOIN project_info b on a.xmmc=b.id
        left JOIN mhour_roster c on a.oa_id = c.oa_id
        where a.oa_id IN
        <foreach collection="userIds" item="uId" open="(" separator="," close=")">
            #{uId}
        </foreach>
        <if test="filter.deptIds != null and filter.deptIds.size() != 0">
            AND c.dept_id IN
            <foreach collection="filter.deptIds" item="deptId" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="filter.startTime != null and filter.endTime != null">
            and a.belong_date &gt;= #{filter.startTime}
            and a.belong_date &lt;= #{filter.endTime}
        </if>
        <if test="filter.userName != null and filter.userName != ''">
            and c.alias_name like concat('%', #{filter.userName} ,'%')
        </if>
        <if test="filter.userId != null and filter.userId != ''">
            and a.oa_id =#{filter.userId}
        </if>
        <if test="projectIds != null">
            AND a.xmmc IN
            <foreach collection="projectIds" item="pId" open="(" separator="," close=")">
                #{pId}
            </foreach>
        </if>
        <choose>
            <when test="filter.flag == 1">
                GROUP BY
                c.dept_id
            </when>
            <when test="filter.flag == 0">
                GROUP BY
                a.xmmc,c.alias_name
            </when>
            <otherwise>
                GROUP BY a.oa_id
            </otherwise>
        </choose>
        ) x
        <choose>
            <when test="filter.flag == 1">
                GROUP BY
                x.deptId
            </when>
            <when test="filter.flag == 0">
                GROUP BY
                x.projectId,x.userName
            </when>
            <otherwise>
                GROUP BY x.userId
            </otherwise>
        </choose>
        order by CONVERT ( x.userName USING gbk )
    </select>

    <select id="selectFilingByDailyPaperIds" resultType="com.gok.pboot.pms.entity.dto.DailyPaperEntryFilingDTO">
        SELECT
        hdpe.id as dailyPaperEntryId,
        hdpe.daily_paper_id dailyPaperId,
        hdpe.submission_date submissionDate, -- 日报的填报日期
        IFNULL(hf.filed, 0) filed,
        normal_hours,
        added_hours
        FROM `mhour_daily_paper_entry` hdpe
        LEFT JOIN mhour_filing hf ON hf.`year` = DATE_FORMAT(hdpe.submission_date, '%Y') AND hf.`month` =
        DATE_FORMAT(hdpe.submission_date, '%m')
        WHERE hdpe.del_flag = 0
        AND hdpe.id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="countUnauditedNumber" resultType="java.lang.Integer">
        SELECT count(*)
        FROM mhour_daily_paper_entry hdpe
        WHERE hdpe.del_flag = 0
        AND hdpe.approval_status = 2
        <if test="projectIds != null and projectIds.size() > 0">
            AND hdpe.project_id IN
            <foreach collection="projectIds" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="projectUserIds != null and projectUserIds.size() > 0">
            AND hdpe.user_id IN
            <foreach collection="projectUserIds" separator="," item="userId" open="(" close=")">
                #{userId}
            </foreach>
        </if>
    </select>

    <select id="selectProjectSituationAnalysis" resultType="com.gok.pboot.pms.entity.vo.PanelProjectSituationAnalysisVO">
        SELECT SUM(hdpe.normal_hours) normalHours,
        SUM(work_overtime_hours) workOvertimeHours,
        SUM(rest_overtime_hours) restOvertimeHours,
        SUM(holiday_overtime_hours) holidayOvertimeHours,
        SUM(hdpe.added_hours) addedHours
        FROM mhour_daily_paper_entry hdpe
        WHERE del_flag = 0
        AND approval_status = 4
        AND hdpe.user_id = #{filter.userId}
        <if test="filter.startTime != null and filter.endTime != null">
            and hdpe.submission_date &gt;= #{filter.startTime}
            and hdpe.submission_date &lt;= #{filter.endTime}
        </if>
        <if test="filter.projectName != null and filter.projectName != ''">
            and hdpe.project_name like concat('%', #{filter.projectName} ,'%')
        </if>
    </select>

    <select id="projectHourSumFindPageV2" resultType="com.gok.pboot.pms.entity.vo.ProjectHourSumFindPageVO">
        SELECT
t1.code,
t1.id,
t1.projectName,
t1.projectStatus,
t1.projectDeptId,
t1.isNotInternalProject,
t1.dept_id,
	sum(t1.normalHours) AS "normalHours",
	sum(t1.addedHours) AS "addedHours",
	sum(t1.workOvertimeHours) AS "workOvertimeHours",
	sum(t1.restOvertimeHours) AS "restOvertimeHours",
	sum(t1.holidayOvertimeHours) AS "holidayOvertimeHours",
	sum(t1.projectHours) AS "projectHours",
	sum(t1.preSaleHours) AS "preSaleHours",
	sum(t1.afterSaleHours) AS "afterSaleHours",
	t1.hourData AS "hourData",
t1.projectTypeName
FROM
(SELECT
        pr.item_no AS 'code',
        pr.id AS 'id',
        pr.item_name AS 'projectName',
        pr.project_status AS 'projectStatus',
        pr.first_level_department_id AS 'projectDeptId',
        pr.first_level_department_id AS 'dept_id',
        pr.is_not_internal_project as 'isNotInternalProject',
        SUM(pe.normal_hours) AS 'normalHours',
        SUM(pe.added_hours) AS 'addedHours',
        SUM(work_overtime_hours) AS 'workOvertimeHours',
        SUM(rest_overtime_hours) AS 'restOvertimeHours',
        SUM(holiday_overtime_hours) AS 'holidayOvertimeHours',
        SUM(pe.normal_hours) AS 'projectHours',
        SUM(CASE pe.work_type WHEN 0 THEN pe.normal_hours ELSE 0 END) AS 'preSaleHours',
        SUM(CASE pe.work_type WHEN 1 THEN pe.normal_hours ELSE 0 END) AS 'afterSaleHours',
        h.hourData AS 'hourData',
        (CASE WHEN pr.is_not_internal_project = 1 and pr.project_type = 0 THEN '公司信息化'
        WHEN pr.is_not_internal_project = 1 and pr.project_type = 1 THEN '通用课程开发'
        WHEN pr.is_not_internal_project = 1 and pr.project_type = 2 THEN '自研产品研发'
        WHEN pr.is_not_internal_project = 1 and pr.project_type = 3 THEN '标准化解决方案打造'
        WHEN pr.is_not_internal_project = 1 and pr.project_type = 4 THEN '专项人才供应链构建'
        WHEN pr.is_not_internal_project = 1 and pr.project_type = 99 THEN '部门工作'
        END) AS 'projectTypeName'
        FROM mhour_daily_paper_entry pe
        LEFT JOIN project_info pr ON pr.id = pe.project_id
        LEFT JOIN mhour_daily_paper mdp ON pe.daily_paper_id = mdp.id
        LEFT JOIN (
        SELECT
        ol.xmmc,SUM(ol.hour_data) AS 'hourData'
        FROM mhour_overtime_leave_data ol
        WHERE ol.type IN (99)
        AND ol.xmmc IS NOT NULL
        <if test="projectHourSumFindPageDTO.startTime != null">
            AND DATE_FORMAT(ol.belongdate, '%Y-%m-%d') &gt;=
            DATE_FORMAT(#{projectHourSumFindPageDTO.startTime},'%Y-%m-%d')
        </if>
        <if test="projectHourSumFindPageDTO.endTime != null">
            AND DATE_FORMAT(ol.belongdate, '%Y-%m-%d') &lt;=
            DATE_FORMAT(#{projectHourSumFindPageDTO.endTime},'%Y-%m-%d')
        </if>
        GROUP BY ol.xmmc
        ) h ON h.xmmc = pe.project_id
        where
            pe.del_flag = 0
            <if test="projectHourSumFindPageDTO.projectId != null">
                AND pr.id = #{projectHourSumFindPageDTO.projectId}
            </if>
            <if test="projectHourSumFindPageDTO.projectIds != null and projectHourSumFindPageDTO.projectIds.size() > 0 ">
                AND pr.id IN
                <foreach collection="projectHourSumFindPageDTO.projectIds" open="(" close=")" separator="," item="id">
                    #{id}
                </foreach>
            </if>
            <if test="projectHourSumFindPageDTO.startTime != null">
                AND DATE_FORMAT(pe.submission_date, '%Y-%m-%d') &gt;=
                DATE_FORMAT(#{projectHourSumFindPageDTO.startTime},'%Y-%m-%d')
            </if>
            <if test="projectHourSumFindPageDTO.endTime != null">
                AND DATE_FORMAT(pe.submission_date, '%Y-%m-%d') &lt;=
                DATE_FORMAT(#{projectHourSumFindPageDTO.endTime},'%Y-%m-%d')
            </if>
            <if test="projectHourSumFindPageDTO.projectStatus != null">
                AND pr.project_status = #{projectHourSumFindPageDTO.projectStatus}
            </if>
            <if test="projectHourSumFindPageDTO.personnelStatus != null">
                AND mdp.user_status = #{projectHourSumFindPageDTO.personnelStatus}
            </if>
            <if test="projectHourSumFindPageDTO.isNotInternalProject != null and projectHourSumFindPageDTO.isNotInternalProject > 0">
                AND pr.is_not_internal_project = #{projectHourSumFindPageDTO.isNotInternalProject}
            </if>
            <if test="projectHourSumFindPageDTO.projectTypeList != null and projectHourSumFindPageDTO.projectTypeList.size()>0">
                AND pr.project_type IN
                <foreach collection="projectHourSumFindPageDTO.projectTypeList" item="i" open="(" separator="," close=")">
                      #{i}
                </foreach>
            </if>
            <if test="projectHourSumFindPageDTO.projectStatusList != null and projectHourSumFindPageDTO.projectStatusList.size()>0">
                AND pr.project_status IN
                <foreach collection="projectHourSumFindPageDTO.projectStatusList" item="i" open="(" separator="," close=")">
                      #{i}
                </foreach>
            </if>
            <choose>
                <when test="showUnreviewed==true">
                    AND pe.approval_status = 2
                </when>
                <when test="showUnreviewed==false">
                    AND pe.approval_status = 4
                </when>
                <when test="showUnreviewed == null">
                    AND pe.approval_status IN (2, 4)
                </when>
            </choose>
        GROUP BY pr.id

        <if test="showUnreviewed != true">
        UNION ALL
        SELECT pr.item_no                   AS "code",
               pr.id                        AS "id",
               pr.item_name                 AS "projectName",
               pr.project_status            AS "projectStatus",
               pr.first_level_department_id AS "projectDeptId",
               pr.first_level_department_id AS "dept_id",
               pr.is_not_internal_project AS "isNotInternalProject",
               0                            AS "normalHours",
               0                            AS "addedHours",
               0                            AS "workOvertimeHours",
               0                            AS "restOvertimeHours",
               0                            AS "holidayOvertimeHours",
               0                            AS "projectHours",
                ( CASE WHEN a.work_type = 0 THEN a.hour_data ELSE 0 END )  AS "preSaleHours",
                ( CASE WHEN a.work_type = 1 THEN a.hour_data ELSE 0 END )  AS "afterSaleHours",
               0                            AS "hourData",
               CASE
                       WHEN pr.is_not_internal_project = 1
                               AND pr.project_type = 0 THEN
                               '公司信息化'
                       WHEN pr.is_not_internal_project = 1
                               AND pr.project_type = 1 THEN
                               '通用课程开发'
                       WHEN pr.is_not_internal_project = 1
                               AND pr.project_type = 2 THEN
                               '自研产品研发'
                       WHEN pr.is_not_internal_project = 1
                               AND pr.project_type = 3 THEN
                               '标准化解决方案打造'
                       WHEN pr.is_not_internal_project = 1
                               AND pr.project_type = 4 THEN
                               '专项人才供应链构建'
                       WHEN pr.is_not_internal_project = 1
                               AND pr.project_type = 99 THEN
                               '部门工作'
                       END                  AS "projectTypeName"
        FROM mhour_compensatory_leave_data a
                     LEFT JOIN project_info pr ON pr.id = a.xmmc
                     LEFT JOIN mhour_roster r ON r.id = a.oa_id
        WHERE a.belong_date NOT IN (SELECT day_date FROM mhour_holiday)
          AND a.type = '3'
          <if test="projectHourSumFindPageDTO.projectId != null">
                AND pr.id = #{projectHourSumFindPageDTO.projectId}
            </if>
            <if test="projectHourSumFindPageDTO.projectIds != null and projectHourSumFindPageDTO.projectIds.size() > 0 ">
                AND pr.id IN
                <foreach collection="projectHourSumFindPageDTO.projectIds" open="(" close=")" separator="," item="id">
                    #{id}
                </foreach>
            </if>
            <if test="projectHourSumFindPageDTO.startTime != null">
                AND DATE_FORMAT(a.belong_date, '%Y-%m-%d') &gt;=
                DATE_FORMAT(#{projectHourSumFindPageDTO.startTime},'%Y-%m-%d')
            </if>
            <if test="projectHourSumFindPageDTO.endTime != null">
                AND DATE_FORMAT(a.belong_date, '%Y-%m-%d') &lt;=
                DATE_FORMAT(#{projectHourSumFindPageDTO.endTime},'%Y-%m-%d')
            </if>
            <if test="projectHourSumFindPageDTO.projectStatus != null">
                AND pr.project_status = #{projectHourSumFindPageDTO.projectStatus}
            </if>
            <if test="projectHourSumFindPageDTO.isNotInternalProject != null and projectHourSumFindPageDTO.isNotInternalProject > 0">
                AND pr.is_not_internal_project = #{projectHourSumFindPageDTO.isNotInternalProject}
            </if>
            <if test="projectHourSumFindPageDTO.projectTypeList != null and projectHourSumFindPageDTO.projectTypeList.size()>0">
                AND pr.project_type IN
                <foreach collection="projectHourSumFindPageDTO.projectTypeList" item="i" open="(" separator="," close=")">
                      #{i}
                </foreach>
            </if>
            <if test="projectHourSumFindPageDTO.projectStatusList != null and projectHourSumFindPageDTO.projectStatusList.size()>0">
                AND pr.project_status IN
                <foreach collection="projectHourSumFindPageDTO.projectStatusList" item="i" open="(" separator="," close=")">
                      #{i}
                </foreach>
            </if>
        GROUP BY a.xmmc
        </if>
        ) t1
												GROUP BY t1.id
    </select>
    <select id="specialProjectsContainsUserId" resultType="com.gok.pboot.pms.entity.vo.DailyReviewPageVO">
        SELECT
        hdpe.project_id as projectId,
        hdpe.project_name as projectName,
        COUNT(hdpe.id) as approvalNum,
        SUM(hdpe.normal_hours) as normalHours,
        SUM(hdpe.added_hours) as addedHours
        FROM mhour_daily_paper_entry hdpe
        WHERE hdpe.del_flag = 0
        <if test="filter.auditStatus == 0">
            AND (hdpe.approval_status = 2 OR hdpe.approval_status IS NULL)
        </if>
        <if test="filter.auditStatus == 1">
            AND (hdpe.approval_status IN (1, 3, 4) OR hdpe.approval_status IS NULL)
        </if>
        <if test="specialProjectIds != null and specialProjectIds.size > 0">
            AND hdpe.project_id IN
            <foreach collection="specialProjectIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="userIds != null and userIds.size() > 0">
            AND hdpe.user_id IN
            <foreach collection="userIds" separator="," item="userId" open="(" close=")">
                #{userId}
            </foreach>
        </if>
        <if test="filter.startTime != null">
            AND DATE_FORMAT(hdpe.submission_date, '%Y-%m-%d') &gt;= DATE_FORMAT(#{
                filter.startTime},'%Y-%m-%d')
        </if>
        <if test="filter.endTime != null">
            AND DATE_FORMAT(hdpe.submission_date, '%Y-%m-%d') &lt;= DATE_FORMAT(#{
                filter.endTime},'%Y-%m-%d')
        </if>
        GROUP BY hdpe.project_id
    </select>
    <select id="notEditProjectUserId" resultType="com.gok.pboot.pms.entity.vo.DailyReviewPageVO">
        SELECT
        DISTINCT hdpe.user_id userId,
        hdpe.project_id projectId
        FROM mhour_daily_paper_entry hdpe
        WHERE hdpe.del_flag = 0
        <if test="filter.auditStatus == 0">
            AND (hdpe.approval_status = 2 OR hdpe.approval_status IS NULL)
        </if>
        <if test="filter.auditStatus == 1">
            AND (hdpe.approval_status IN (1, 3, 4) OR hdpe.approval_status IS NULL)
        </if>
        <if test="childProjectIds != null and childProjectIds.size > 0">
            AND hdpe.project_id IN
            <foreach collection="childProjectIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="filter.userIds != null and filter.userIds.size() > 0">
            AND hdpe.user_id IN
            <foreach collection="filter.userIds" separator="," item="userId" open="(" close=")">
                #{userId}
            </foreach>
        </if>
    </select>
    <select id="findBySubordinate" resultType="com.gok.pboot.pms.entity.vo.DailyReviewProjectAuditPageVO">
        SELECT
        pe.id AS 'id',
        pe.user_real_name AS 'userRealName',
        pe.project_name AS 'projectName',
        pe.task_name AS 'taskName',
        pe.task_id AS 'taskId',
        pe.submission_date AS 'submissionDate',
        pe.normal_hours AS 'normalHours',
        pe.added_hours AS 'addedHours',
        pe.description AS 'description',
        pe.approval_reason AS 'approvalReason',
        pe.approval_status AS 'approvalStatus',
        pe.work_type AS 'workType',
        pe.modifier AS 'approvalName',
        pe.daily_paper_id AS 'dailyPaperId',
        pe.user_id AS 'userId',
        pe.project_id AS 'projectId',
        pe.ctime commitDate,
        mdp.filling_state fillingState
        FROM
        mhour_daily_paper_entry pe
        LEFT JOIN mhour_daily_paper mdp ON pe.daily_paper_id = mdp.id
        <where>
            pe.approval_status !=0
            and pe.del_flag=0
            <if test="filter.userIds != null and filter.userIds.size() > 0">
                AND pe.user_id IN
                <foreach collection="filter.userIds" separator="," item="userId" open="(" close=")">
                    #{userId}
                </foreach>
            </if>
            <if test="filter.startTime !=null and filter.startTime!=''">
                and pe.submission_date &gt;=#{filter.startTime}
            </if>
            <if test="filter.endTime !=null and filter.endTime!=''">
                and pe.submission_date &lt;= #{filter.endTime}
            </if>
            <if test="filter.projectName != null">
                <bind value="'%' + filter.projectName + '%'" name="pNLike"/>
                AND pe.project_name LIKE #{pNLike}
            </if>
            <if test="filter.userName != null">
                <bind value="'%' + filter.userName + '%'" name="userName"/>
                AND pe.user_real_name LIKE #{userName}
            </if>
        </where>
        order by pe.submission_date desc, pe.mtime desc

    </select>
    <select id="findBySubordinateStatic" resultType="com.gok.pboot.pms.entity.vo.SubordinatePaperEntryStaticVO">
        SELECT
        pe.user_id userId,
        pe.submission_date submissionDate,
        pe.added_hours addedHours,
        pe.normal_hours normalHours,
        pe.work_overtime_hours workOvertimeHours,
        pe.rest_overtime_hours restOvertimeHours,
        pe.holiday_overtime_hours holidayOvertimeHours
        FROM
        mhour_daily_paper_entry pe
        <where>
            pe.approval_status !=0
            and pe.del_flag=0
            <if test="filter.userIds != null and filter.userIds.size() > 0">
                AND pe.user_id IN
                <foreach collection="filter.userIds" separator="," item="userId" open="(" close=")">
                    #{userId}
                </foreach>
            </if>
            <if test="filter.startTime !=null and filter.startTime!=''">
                and pe.submission_date &gt;=#{filter.startTime}
            </if>
            <if test="filter.endTime !=null and filter.endTime!=''">
                and pe.submission_date &lt;= #{filter.endTime}
            </if>
            <if test="filter.projectName != null and filter.projectName!=''">
                <bind value="'%' + filter.projectName + '%'" name="pNLike"/>
                AND pe.project_name LIKE #{pNLike}
            </if>
            <if test="filter.userName != null and filter.userName!=''">
                <bind value="'%' + filter.userName + '%'" name="userName"/>
                AND pe.user_real_name LIKE #{userName}
            </if>
        </where>
    </select>
    <select id="findByPanel" resultType="com.gok.pboot.pms.entity.vo.DailPanelPaperPageVO">
        SELECT
        pe.id AS id,
        pe.user_real_name AS 'userRealName',
        pe.project_name AS 'projectName',
        pe.project_id AS 'projectId',
        pe.task_name AS 'taskName',
        pe.task_id AS 'taskId',
        pe.submission_date AS 'submissionDate',
        pe.normal_hours AS 'normalHours',
        pe.added_hours AS 'addedHours',
        pe.description AS 'description',
        pe.approval_status AS 'approvalStatus',
        pe.user_id AS 'userId',
        pro.manager_user_name AS 'managerUserName',
        pro.salesman_user_name AS 'salesmanUserName',
        pro.code AS 'code'
        FROM
        mhour_daily_paper_entry pe
        LEFT JOIN mhour_project pro ON pe.project_id = pro.id and pro.del_flag = 0
        <where>
            pe.del_flag = 0
            <if test="filter.userId !=null and filter.userId!=''">
                and pe.user_id = #{filter.userId}
            </if>
            <if test="filter.startTime !=null and filter.startTime!=''">
                and pe.submission_date &gt;=#{filter.startTime}
            </if>
            <if test="filter.endTime !=null and filter.endTime!=''">
                and pe.submission_date &lt;= #{filter.endTime}
            </if>
            <if test="filter.projectName != null">
                <bind value="'%' + filter.projectName + '%'" name="pNLike"/>
                AND pe.project_name LIKE #{pNLike}
            </if>
            <if test="filter.taskName != null">
                <bind value="'%' + filter.taskName + '%'" name="tNLike"/>
                AND pe.task_name LIKE #{tNLike}
            </if>
        </where>
        ORDER BY
        pe.submission_date desc, CONVERT ( pe.project_name USING gbk )
    </select>
    <select id="panelSituationAnalysis" resultType="com.gok.pboot.pms.entity.vo.PanelProjectSituationAnalysisVO">
        SELECT SUM(hdpe.normal_hours) normalHours,
        SUM(hdpe.added_hours) addedHours,
        SUM(hdpe.work_overtime_hours) workOvertimeHours,
        SUM(hdpe.rest_overtime_hours) restOvertimeHours,
        SUM(hdpe.holiday_overtime_hours) holidayOvertimeHours
        FROM mhour_daily_paper_entry hdpe
        WHERE del_flag = 0
        AND hdpe.user_id = #{filter.userId}
        <if test="filter.startTime != null and filter.endTime != null">
            and hdpe.submission_date &gt;= #{filter.startTime}
            and hdpe.submission_date &lt;= #{filter.endTime}
        </if>
        <if test="filter.projectName != null and filter.projectName != ''">
            and hdpe.project_name like concat('%', #{filter.projectName} ,'%')
        </if>
        <if test="filter.taskName != null">
            <bind value="'%' + filter.taskName + '%'" name="tNLike"/>
            AND hdpe.task_name LIKE #{tNLike}
        </if>
    </select>

    <select id="saturationAnalysisBySubmissionDateRangeAndDeptIdsAndUserIdsAndProjectIds" resultType="java.math.BigDecimal">
        SELECT
        COALESCE(SUM(hdpe.normal_hours+hdpe.added_hours),0) auditTime
        FROM mhour_daily_paper_entry hdpe
        LEFT JOIN project_info mp ON mp.id = hdpe.project_id
        WHERE hdpe.del_flag = 0
        AND hdpe.approval_status = ${@<EMAIL>}
        AND hdpe.submission_date BETWEEN #{startDate} AND #{endDate}
        AND hdpe.user_dept_id IN
        <foreach collection="deptIds" item="deptId" open="(" close=")" separator=",">
            #{deptId}
        </foreach>
        AND hdpe.user_id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        <if test="projectIds != null">
            AND mp.id IN
            <foreach collection="projectIds" item="pId" open="(" separator="," close=")">
                #{pId}
            </foreach>
        </if>
    </select>
    <select id="findBySaturation" resultType="com.gok.pboot.pms.entity.vo.DailyReviewProjectAuditPageVO">
        SELECT
        pe.id AS 'id',
        pe.user_real_name AS 'userRealName',
        pe.project_name AS 'projectName',
        pe.task_name AS 'taskName',
        pe.task_id AS 'taskId',
        pe.submission_date AS 'submissionDate',
        pe.normal_hours AS 'normalHours',
        pe.added_hours AS 'addedHours',
        pe.description AS 'description',
        pe.daily_paper_id AS 'dailyPaperId',
        pe.user_id AS 'userId',
        pe.project_id AS 'projectId',
        pe.ctime commitDate
        FROM
        mhour_daily_paper_entry pe
        LEFT JOIN mhour_project mp ON mp.id = pe.project_id and mp.del_flag = 0
        <if test="filter.fillingState != null">
            INNER JOIN mhour_daily_paper mdp ON pe.daily_paper_id = mdp.id AND mdp.filling_state =
            #{filter.fillingState}
        </if>
        <where>
            <if test="filter.deptIds != null and filter.deptIds.size() > 0">
                AND pe.user_dept_id IN
                <foreach collection="filter.deptIds" separator="," item="id" open="(" close=")">
                    #{id}
                </foreach>
            </if>
            <if test="filter.startTime !=null and filter.startTime!=''">
                and pe.submission_date &gt;=#{filter.startTime}
            </if>
            <if test="filter.endTime !=null and filter.endTime!=''">
                and pe.submission_date &lt;= #{filter.endTime}
            </if>
            <if test="filter.projectName != null">
                <bind value="'%' + filter.projectName + '%'" name="projectName"/>
                AND pe.project_name LIKE #{projectName}
            </if>
            <if test="filter.userName != null">
                <bind value="'%' + filter.userName + '%'" name="userName"/>
                AND pe.user_real_name LIKE #{userName}
            </if>
            <if test="filter.taskName != null">
                <bind value="'%' + filter.taskName + '%'" name="taskName"/>
                AND pe.task_name LIKE #{taskName}
            </if>
            <if test="filter.approvalStatus != null">
                AND pe.approval_status = #{filter.approvalStatus}
            </if>
            <if test="projectIds != null">
                AND mp.id IN
                <foreach collection="projectIds" item="pId" open="(" separator="," close=")">
                    #{pId}
                </foreach>
            </if>
        </where>
        order by CONVERT ( user_real_name USING gbk )
    </select>
    <select id="exportBySaturation" resultType="com.gok.pboot.pms.entity.vo.SaturationExportVO">
        SELECT
        pe.id AS 'id',
        pe.user_real_name AS 'userRealName',
        pe.project_name AS 'projectName',
        pe.task_name AS 'taskName',
        pe.task_id AS 'taskId',
        pe.submission_date AS 'submissionDate',
        pe.normal_hours AS 'normalHours',
        pe.added_hours AS 'addedHours',
        pe.description AS 'description',
        pe.daily_paper_id AS 'dailyPaperId',
        pe.user_id AS 'userId',
        pe.project_id AS 'projectId',
        pe.ctime commitDate
        FROM
        mhour_daily_paper_entry pe
        LEFT JOIN mhour_project mp ON mp.id = pe.project_id and mp.del_flag = 0
        <if test="filter.fillingState != null">
            LEFT JOIN mhour_daily_paper mdp ON pe.daily_paper_id = mdp.id AND mdp.filling_state = #{filter.fillingState}
        </if>
        <where>
            <if test="filter.deptIds != null and filter.deptIds.size() > 0">
                AND pe.user_dept_id IN
                <foreach collection="filter.deptIds" separator="," item="id" open="(" close=")">
                    #{id}
                </foreach>
            </if>
            <if test="filter.startTime !=null and filter.startTime!=''">
                and pe.submission_date &gt;=#{filter.startTime}
            </if>
            <if test="filter.endTime !=null and filter.endTime!=''">
                and pe.submission_date &lt;= #{filter.endTime}
            </if>
            <if test="filter.projectName != null">
                <bind value="'%' + filter.projectName + '%'" name="pNLike"/>
                AND pe.project_name LIKE #{pNLike}
            </if>
            <if test="filter.taskName != null">
                <bind value="'%' + filter.taskName + '%'" name="taskName"/>
                AND pe.task_name LIKE #{taskName}
            </if>
            <if test="filter.userName != null">
                <bind value="'%' + filter.userName + '%'" name="uNLike"/>
                AND pe.user_real_name LIKE #{uNLike}
            </if>
            <if test="filter.approvalStatus != null">
                AND pe.approval_status = #{filter.approvalStatus}
            </if>
            <if test="projectIds != null">
                AND mp.id IN
                <foreach collection="projectIds" item="pId" open="(" separator="," close=")">
                    #{pId}
                </foreach>
            </if>
        </where>
        order by CONVERT ( user_real_name USING gbk )
    </select>
    <select id="findReturnEntry" resultType="com.gok.pboot.pms.entity.DailyPaperEntry">
        SELECT
        pe.normal_hours AS 'normalHours',
        pe.approval_status AS 'approvalStatus',
        pe.daily_paper_id AS 'dailyPaperId'
        FROM
        mhour_daily_paper_entry pe
        <where>
            pe.del_flag=0 and pe.approval_status=1
            AND pe.daily_paper_id IN
            <foreach collection="paperIds" separator="," item="id" open="(" close=")">
                #{id}
            </foreach>
        </where>

    </select>
    <select id="findByTaskIds" resultType="com.gok.pboot.pms.entity.DailyPaperEntry">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mhour_daily_paper_entry pe
        WHERE
        del_flag = 0
        AND
        pe.task_id IN
        <foreach collection="taskIds" open="(" separator="," close=")" item="taskId">
            #{taskId}
        </foreach>
    </select>

    <select id="existsByTaskIds" resultType="java.lang.Boolean">
        select
        COUNT(*)
        FROM
        (
        SELECT
        id,task_id
        FROM mhour_daily_paper_entry
        WHERE
        del_flag = 0
        AND task_id = #{id}
        )a
    </select>

    <select id="findUserIds" resultType="java.lang.Long">
        SELECT
        DISTINCT x.userId userId
        FROM
        (
        SELECT
            DISTINCT hdpe.user_id userId
        FROM
        mhour_daily_paper_entry hdpe
        LEFT JOIN project_info mp ON mp.id = hdpe.project_id
        WHERE
        hdpe.del_flag = 0
        <if test="projectIds != null">
            AND hdpe.project_id IN
            <foreach collection="projectIds" item="projectId" open="(" close=")" separator=",">
                #{projectId}
            </foreach>
        </if>
        <if test="deptIds != null and deptIds.size() != 0">
            AND hdpe.user_dept_id IN
            <foreach collection="deptIds" item="deptId" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="approvalStatus != null">
            AND hdpe.approval_status = #{approvalStatus}
        </if>
        <if test="startDate != null and endDate != null">
            and hdpe.submission_date &gt;= #{startDate}
            and hdpe.submission_date &lt;= #{endDate}
        </if>

        union all

        SELECT
            DISTINCT a.oa_id userId
        from  mhour_compensatory_leave_data a
        left JOIN project_info b on a.xmmc=b.id
        left JOIN mhour_roster c on a.oa_id = c.oa_id
        <where>
            <if test="projectIds != null">
                AND a.xmmc IN
                <foreach collection="projectIds" item="projectId" open="(" close=")" separator=",">
                    #{projectId}
                </foreach>
            </if>
            <if test="deptIds != null and deptIds.size() != 0">
                AND c.dept_id IN
                <foreach collection="deptIds" item="deptId" open="(" close=")" separator=",">
                    #{deptId}
                </foreach>
            </if>
            <if test="startDate != null and endDate != null">
                and a.belong_date &gt;= #{startDate}
                and a.belong_date &lt;= #{endDate}
            </if>
        </where>
        ) x
    </select>

    <select id="isExistsUnauditedByTaskIdForDel" resultType="java.lang.Boolean">
        select exists
           (
               select
                   1
               from
                   mhour_daily_paper_entry a
               where
                   a.del_flag = 0
                 and a.task_id = #{taskId}
                 and a.approval_status = 2
           )
    </select>

    <select id="isExistsAttachedHours" resultType="java.lang.Boolean">
        select exists
           (
               select
                   1
               from
                   mhour_daily_paper_entry a
               where
                   a.del_flag = 0
                 and a.task_id = #{taskId}
                 and a.approval_status = 4
           )
    </select>

    <select id="findCantDelTaskIds" resultType="java.lang.Long">
        select distinct
        a.task_id
        from mhour_daily_paper_entry a
        left join mhour_filing b on (year(a.submission_date) = b.year and month(a.submission_date) = b.month)
        where
        a.del_flag = 0
        and b.del_flag = 0
        and ((a.approval_status = 4) or (a.approval_status = 1 and b.filed = 1) or (a.approval_status = 3 and b.filed = 1) or (a.approval_status = 2))
        and a.task_id in
        <foreach collection="taskIds" open="(" separator="," close=")" item="taskId">
            #{taskId}
        </foreach>
    </select>

    <select id="isExistsUnauditedByTaskId" resultType="java.lang.Boolean">
        select exists
        (
        select
        1
        from
        mhour_daily_paper_entry a
        left join mhour_filing b on (year(a.submission_date) = b.year and month(a.submission_date) = b.month)
        where
        a.del_flag = 0
        and b.del_flag = 0
        and a.task_id = #{taskId}
        and ((a.approval_status = 2 and b.filed != 1))
        )
    </select>

    <select id="findFinalDailyDateByTaskId" resultType="java.time.LocalDate">
        select submission_date
        from
        mhour_daily_paper_entry
        where
        del_flag = 0
        and task_id = #{taskId}
        order by submission_date desc
        limit 1
    </select>

    <select id="findFinalPassedDailyDateByTaskId" resultType="java.time.LocalDate">
        select submission_date
        from
            mhour_daily_paper_entry
        where
        del_flag = 0
        and task_id = #{taskId}
        and approval_status = 4
        order by submission_date desc
        limit 1
    </select>

    <select id="findFirstDailyDateByTaskId" resultType="java.time.LocalDate">
        select submission_date
        from
            mhour_daily_paper_entry
        where
            del_flag = 0
          and task_id = #{taskId}
        order by submission_date asc
            limit 1
    </select>

    <select id="findTaskIdByUserIdsAndDateRangeAndApprovalStatus" resultType="java.lang.Long">
        SELECT DISTINCT task_id
        FROM mhour_daily_paper_entry
        WHERE
            del_flag = 0
        AND user_id IN
        <foreach collection="userIds" separator="," item="userId" open="(" close=")">
            #{userId}
        </foreach>
        <if test="startDate != null">
            AND submission_date &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND submission_date &lt;= #{endDate}
        </if>
        <if test="approvalStatusList != null">
            AND approval_status IN
            <foreach collection="approvalStatusList" separator="," item="approvalStatus" open="(" close=")">
                #{approvalStatus}
            </foreach>
        </if>
    </select>

    <select id="findByTaskIdsAndDateRangeAndApprovalStatus" resultType="com.gok.pboot.pms.entity.DailyPaperEntry">
        SELECT
            <include refid="Base_Column_List"/>
        FROM mhour_daily_paper_entry pe
        WHERE
            del_flag = 0
        AND task_id IN
        <foreach collection="taskIds" separator="," item="taskId" open="(" close=")">
            #{taskId}
        </foreach>
        <if test="startDate != null">
            AND submission_date &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND submission_date &lt;= #{endDate}
        </if>
        <if test="approvalStatus != null">
            AND approval_status = #{approvalStatus}
        </if>
    </select>

    <select id="findPassedByTaskIds" resultType="com.gok.pboot.pms.entity.DailyPaperEntry">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mhour_daily_paper_entry pe
        WHERE
        del_flag = 0
        AND pe.approval_status = 4
        AND
        pe.task_id IN
        <foreach collection="taskIds" open="(" separator="," close=")" item="taskId">
            #{taskId}
        </foreach>
    </select>

    <select id="findDSHByTaskIds" resultType="com.gok.pboot.pms.entity.DailyPaperEntry">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mhour_daily_paper_entry pe
        LEFT JOIN mhour_filing hf on (year(pe.submission_date) = hf.year and month(pe.submission_date) = hf.month)
        WHERE
        pe.del_flag = 0
        AND pe.approval_status = 2
        AND
        pe.task_id IN
        <foreach collection="taskIds" open="(" separator="," close=")" item="taskId">
            #{taskId}
        </foreach>
        AND hf.del_flag = 0
        AND hf.filed = 0
    </select>

    <select id="findDSHPageByTaskIds" resultType="com.gok.pboot.pms.entity.DailyPaperEntry">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mhour_daily_paper_entry pe
        LEFT JOIN mhour_filing hf on (year(pe.submission_date) = hf.year and month(pe.submission_date) = hf.month)
        WHERE
        pe.del_flag = 0
        AND pe.approval_status = 2
        AND
        pe.task_id IN
        <foreach collection="taskIds" open="(" separator="," close=")" item="taskId">
            #{taskId}
        </foreach>
        AND hf.del_flag = 0
        AND hf.filed = 0
    </select>

    <select id="findInvalidByTaskIds" resultType="com.gok.pboot.pms.entity.DailyPaperEntry">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mhour_daily_paper_entry pe
        LEFT JOIN mhour_filing hf on (year(pe.submission_date) = hf.year and month(pe.submission_date) = hf.month)
        WHERE
        pe.del_flag = 0
        AND ((pe.approval_status = 2 AND hf.filed = 1) OR (pe.approval_status = 3 AND hf.filed = 1) OR (pe.approval_status = 1 AND hf.filed = 1))
        AND
        pe.task_id IN
        <foreach collection="taskIds" open="(" separator="," close=")" item="taskId">
            #{taskId}
        </foreach>
        AND hf.del_flag = 0
    </select>

    <select id="findInvalidPageByTaskIds" resultType="com.gok.pboot.pms.entity.DailyPaperEntry">
        SELECT
        <include refid="Base_Column_List"/>
        FROM mhour_daily_paper_entry pe
        LEFT JOIN mhour_filing hf on (year(pe.submission_date) = hf.year and month(pe.submission_date) = hf.month)
        WHERE
        pe.del_flag = 0
        AND ((pe.approval_status = 2 AND hf.filed = 1) OR (pe.approval_status = 3 AND hf.filed = 1) OR (pe.approval_status = 1 AND hf.filed = 1))
        AND
        pe.task_id IN
        <foreach collection="taskIds" open="(" separator="," close=")" item="taskId">
            #{taskId}
        </foreach>
        AND hf.del_flag = 0
    </select>

    <select id="findFirstSqByTaskId" resultType="java.time.LocalDate">
        select submission_date
        from
            mhour_daily_paper_entry
        where
            del_flag = 0
          and task_id = #{taskId}
          and work_type = 0
        order by submission_date asc
            limit 1
    </select>

    <select id="findFirstShByTaskId" resultType="java.time.LocalDate">
        select submission_date
        from
            mhour_daily_paper_entry
        where
            del_flag = 0
          and task_id = #{taskId}
          and work_type = 1
        order by submission_date asc
            limit 1
    </select>

    <select id="findDailyPaperIdAndUserIdAndTaskIdTripleByDailyPaperIdsAndApprovalStatus" resultType="org.apache.commons.lang3.tuple.MutableTriple">
        SELECT
            daily_paper_id AS 'left',
            user_id AS 'middle',
            task_id AS 'right'
        FROM mhour_daily_paper_entry pe
        WHERE
            del_flag = 0
        AND pe.daily_paper_id IN
        <foreach collection="dailyPaperIds" item="pId" separator="," open="(" close=")">
            #{pId}
        </foreach>
        AND pe.approval_status = #{approvalStatus}
        GROUP BY daily_paper_id, user_id, task_id
    </select>

    <select id="selectSituationAnalysisMultiDept" resultType="com.gok.pboot.pms.entity.vo.SituationAnalysisVO">
        SELECT
            x.id,
            x.projectId,
            x.projectName,
            x.userName,
            x.userId,
            x.deptId,
            x.isInsideProject,
            MIN( x.startTime ) startTime,
            MAX( x.endTime ) endTime,
            SUM( x.normalHours ) normalHours,
            SUM( x.addedHours ) addedHours
        FROM
        (
        SELECT
                CONCAT(hdpe.user_id, hdpe.user_dept_id) AS id,
                hdpe.project_id projectId,
                hdpe.project_name projectName,
                hdpe.user_real_name userName,
                hdpe.user_id userId,
                hdpe.user_dept_id deptId,
                mp.is_not_internal_project isInsideProject,
                hdpe.submission_date  startTime,
                hdpe.submission_date  endTime,
                hdpe.normal_hours  normalHours,
                hdpe.added_hours  addedHours
            FROM
            mhour_daily_paper_entry hdpe
            LEFT JOIN project_info mp ON mp.id = hdpe.project_id
            WHERE
            hdpe.del_flag = 0

            <if test="filter.deptIds != null and filter.deptIds.size() != 0">
                AND hdpe.user_dept_id IN
                <foreach collection="filter.deptIds" item="deptId" open="(" close=")" separator=",">
                    #{deptId}
                </foreach>
            </if>
            <if test="filter.startTime != null and filter.endTime != null">
                and hdpe.submission_date &gt;= #{filter.startTime}
                and hdpe.submission_date &lt;= #{filter.endTime}
            </if>
            <if test="filter.approvalStatus != null">
                AND hdpe.approval_status = #{filter.approvalStatus}
            </if>
            <if test="filter.userName != null and filter.userName != ''">
                and hdpe.user_real_name like concat('%', #{filter.userName} ,'%')
            </if>
            <if test="filter.userId != null and filter.userId != ''">
                and hdpe.user_id =#{filter.userId}
            </if>
            <if test="projectIds != null">
                AND hdpe.project_id IN
                <foreach collection="projectIds" item="pId" open="(" separator="," close=")">
                    #{pId}
                </foreach>
            </if>
            AND hdpe.user_id IN
            <foreach collection="userIds" item="uId" open="(" separator="," close=")">
                #{uId}
            </foreach>
        union all
            SELECT
                CONCAT(a.oa_id, c.dept_id) AS id,
                a.xmmc projectId,
                b.item_name projectName,
                c.alias_name userName,
                a.oa_id userId,
                c.dept_id deptId,
                b.is_not_internal_project isInsideProject,
                a.belong_date startTime,
                a.belong_date endTime,
                0.0 as normalHours,
                0.0 as addedHours
            from  mhour_compensatory_leave_data a
            left JOIN project_info b on a.xmmc=b.id
            left JOIN mhour_roster c on a.oa_id = c.oa_id
            where a.oa_id IN
            <foreach collection="userIds" item="uId" open="(" separator="," close=")">
                #{uId}
            </foreach>
            <if test="filter.deptIds != null and filter.deptIds.size() != 0">
                AND c.dept_id IN
                <foreach collection="filter.deptIds" item="deptId" open="(" close=")" separator=",">
                    #{deptId}
                </foreach>
            </if>
            <if test="filter.startTime != null and filter.endTime != null">
                and a.belong_date &gt;= #{filter.startTime}
                and a.belong_date &lt;= #{filter.endTime}
            </if>
            <if test="filter.userName != null and filter.userName != ''">
                and c.alias_name like concat('%', #{filter.userName} ,'%')
            </if>
            <if test="filter.userId != null and filter.userId != ''">
                and a.oa_id =#{filter.userId}
            </if>
            <if test="projectIds != null">
                AND a.xmmc IN
                <foreach collection="projectIds" item="pId" open="(" separator="," close=")">
                    #{pId}
                </foreach>
            </if>
        ) x
        GROUP BY x.userId, x.deptId
    </select>

    <select id="findDeptIdsBySubmissionDateRangeAndApprovalStatusAndUserIdsAndProjectIds" resultType="org.apache.commons.lang3.tuple.MutablePair">
        SELECT
        x.user_id as 'left',
        x.dept_id as 'right'
        FROM
        (
        SELECT
            user_id ,
            user_dept_id as dept_id
        FROM mhour_daily_paper_entry
        WHERE
            del_flag = 0
        AND submission_date BETWEEN #{startDate} AND #{endDate}
        AND approval_status = #{approvalStatus}
        AND user_id IN
        <foreach collection="userIds" item="uId" open="(" separator="," close=")">
            #{uId}
        </foreach>
        <if test="projectIds != null">
            AND project_id IN
            <foreach collection="projectIds" item="pId" open="(" separator="," close=")">
                #{pId}
            </foreach>
        </if>
        union all
        select
            a.oa_id as user_id,
            b.dept_id as dept_id
        from mhour_compensatory_leave_data a
        left join mhour_roster b on a.oa_id=b.oa_id
        where a.oa_id in
        <foreach collection="userIds" item="uId" open="(" separator="," close=")">
            #{uId}
        </foreach>
        <if test="projectIds != null">
            AND a.xmmc IN
            <foreach collection="projectIds" item="pId" open="(" separator="," close=")">
                #{pId}
            </foreach>
        </if>
        ) x
        GROUP BY x.user_id, x.dept_id
    </select>

    <select id="allocationForCalculation" resultType="com.gok.pboot.pms.entity.vo.AllocationFindPageVO">
        SELECT
            t1.id AS "id",
            t1.`date`,
            t1.work_code AS 'workCode',
            t1.user_real_name AS 'name',
            SUM( t1.projectNormalHours ) + SUM( t1.restOvertimeHours )+ SUM( t1.holidayOvertimeHours )+ SUM( t1.leaveHours ) AS 'projectShareHours'
        FROM
            (
                SELECT
                    pe.project_id AS id,
                    r.work_code,
                    pe.user_real_name AS 'user_real_name',
                    sum( pe.normal_hours ) AS "projectNormalHours",
                    sum( pe.rest_overtime_hours ) AS "restOvertimeHours",
                    sum( pe.holiday_overtime_hours ) AS "holidayOvertimeHours",
                    0 AS "leaveHours",
                    CONCAT( DATE_FORMAT( pe.submission_date, '%Y-%m' ), '-01' ) AS "date"
                FROM
                    mhour_daily_paper_entry pe
                    LEFT JOIN mhour_roster r ON r.id = pe.user_id
                WHERE
                    pe.approval_status = 4
                  AND pe.del_flag = 0
                  AND pe.project_id IN
                  <foreach collection="projectIds" item="item" open="(" separator="," close=")">
                    #{item}
                  </foreach>
                  AND r.work_code IN
                  <foreach collection="workCodes" item="item" open="(" separator="," close=")">
                    #{item}
                  </foreach>
                GROUP BY
                    pe.project_id,
                    r.work_code,
                    pe.user_real_name,
                    DATE_FORMAT( pe.submission_date, '%Y-%m' ) UNION ALL
                SELECT
                    mc.xmmc,
                    r.work_code,
                    r.alias_name AS 'user_real_name',
                    0 AS "projectNormalHours",
                    0 AS "restOvertimeHours",
                    0 AS "holidayOvertimeHours",
                    0 AS "leaveHours",
                    CONCAT( DATE_FORMAT( mc.belong_date, '%Y-%m' ), '-01' ) AS "date"
                FROM
                    mhour_compensatory_leave_data AS mc
                    LEFT JOIN mhour_roster r ON r.id = mc.oa_id
                WHERE
                    mc.belong_date NOT IN ( SELECT day_date FROM mhour_holiday )
                  AND mc.xmmc IN
                    <foreach collection="projectIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                  AND r.work_code IN
                    <foreach collection="workCodes" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                GROUP BY
                    mc.xmmc,
                    r.work_code,
                    r.alias_name,
                    DATE_FORMAT( mc.belong_date, '%Y-%m' ) UNION ALL
                SELECT
                    mpr.project_id AS "id",
                    NULL AS work_code,
                    mpr.user_real_name AS 'user_real_name',
                    IFNULL( mpr.aggregated_days, 0 ) * 7 AS "projectNormalHours",
                    0 AS "restOvertimeHours",
                    0 AS "holidayOvertimeHours",
                    0 AS "leaveHours",
                    CONCAT( DATE_FORMAT( mpr.reuse_date, '%Y-%m' ), '-01' ) AS "date"
                FROM
                    mhour_personnel_reuse mpr
                WHERE
                    mpr.del_flag = 0
                  AND mpr.approval_status = 4
                  AND mpr.project_id IN
                    <foreach collection="projectIds" item="item" open="(" separator="," close=")">
                    #{item}
                    </foreach>
                  AND mpr.user_real_name IN
                    <foreach collection="memberNames" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                GROUP BY
                    mpr.project_id,
                    mpr.user_real_name,
                    DATE_FORMAT( mpr.reuse_date, '%Y-%m' ) UNION ALL
                SELECT
                    mpr.project_id AS "id",
                    mpr.work_code AS work_code,
                    mpr.user_real_name AS 'user_real_name',
                    IFNULL( mpr.normal_work_days, 0 ) * 7 AS "projectNormalHours",
                    IFNULL( mpr.rest_work_days, 0 ) * 7 AS "restOvertimeHours",
                    IFNULL( mpr.holidays_work_days, 0 ) * 7 AS "holidayOvertimeHours",
                    IFNULL( mpr.ompensatory_days, 0 ) * 7 AS "leaveHours",
                    CONCAT( DATE_FORMAT( mpr.reuse_date, '%Y-%m' ), '-01' ) AS "date"
                FROM
                    mhour_personnel_delivery_hour mpr
                WHERE
                  mpr.del_flag = 0
                  AND mpr.approval_status = 4
                  AND mpr.project_id IN
                    <foreach collection="projectIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                  AND mpr.work_code IN
                    <foreach collection="workCodes" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                GROUP BY
                    mpr.project_id,
                    mpr.work_code,
                    mpr.user_real_name,
                    DATE_FORMAT( mpr.reuse_date, '%Y-%m' ) UNION ALL
                SELECT
                    a.xmmc AS "id",
                    r.work_code AS 'workCode',
                    a.`name` AS 'name',
                    0 AS "projectNormalHours",
                    0 AS "restOvertimeHours",
                    0 AS "holidayOvertimeHours",
                    SUM(a.hour_data) AS "leaveHours",
                    CONCAT(DATE_FORMAT( a.belong_date, '%Y-%m' ), '-01' ) AS 'date'
                FROM
                    mhour_compensatory_leave_data AS a
                    LEFT JOIN mhour_roster r ON r.id = a.oa_id
                WHERE
                    a.belong_date NOT IN ( SELECT day_date FROM mhour_holiday )
                    AND a.type = '3'
                    AND a.xmmc IN
                    <foreach collection="projectIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    AND r.work_code IN
                    <foreach collection="workCodes" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                GROUP BY
                    a.xmmc,
                    r.work_code,
                    a.`name`,
                    DATE_FORMAT( a.belong_date, '%Y-%m' )
            ) t1
        GROUP BY
            t1.id,
            t1.`date`,
            t1.work_code,
            t1.user_real_name
    </select>

</mapper>
