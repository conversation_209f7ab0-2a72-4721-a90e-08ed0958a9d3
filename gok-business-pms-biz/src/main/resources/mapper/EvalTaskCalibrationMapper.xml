<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.eval.mapper.EvalTaskCalibrationMapper">
    <resultMap id="BaseResultMap" type="com.gok.pboot.pms.eval.entity.domain.EvalTaskCalibration">
        <id column="id" property="id"/>
        <result column="project_id" property="projectId"/>
        <result column="employee_id" property="employeeId"/>
        <result column="employee_name" property="employeeName"/>
        <result column="current_score" property="currentScore"/>
        <result column="current_level" property="currentLevel"/>
        <result column="adjusted_score" property="adjustedScore"/>
        <result column="adjusted_level" property="adjustedLevel"/>
        <result column="adjustment_reason" property="adjustmentReason"/>
        <result column="calibration_status" property="calibrationStatus"/>
        <result column="return_reason" property="returnReason"/>
        <result property="rank" column="rank"/>
    </resultMap>

    <select id="getProjectEvalRankList" resultType="com.gok.pboot.pms.eval.entity.vo.ProjectEvalRankVO">
        select t.project_id,
               t.employee_id,
               IFNULL( t.adjusted_score, t.current_score ) as comprehensiveScore,
               IFNULL(t2.incomeSum,0)                              as income
        from eval_task_calibration t
                     LEFT JOIN (select project_id, manager_id, sum(income) as incomeSum
                                from cost_deliver_task
                                where del_flag = 1
                                  and project_id = #{projectId}
                                group by project_id, manager_id
                ) t2 on t.project_id = t2.project_id and t.employee_id = t2.manager_id
        where t.del_flag = ${@<EMAIL>()}
         AND t.project_id = #{projectId}
         AND t.task_type = #{taskType}
        order by comprehensiveScore desc ,income desc
    </select>

    <update id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            UPDATE eval_task_calibration
            SET project_id         = #{item.projectId},
                employee_id        = #{item.employeeId},
                employee_name      = #{item.employeeName},
                current_score      = #{item.currentScore},
                current_level      = #{item.currentLevel},
                adjusted_score     = #{item.adjustedScore},
                adjusted_level     = #{item.adjustedLevel},
                adjustment_reason  = #{item.adjustmentReason},
                rank               = #{item.rank},
                return_reason      = #{item.returnReason},
                calibration_status = #{item.calibrationStatus},
                creator=#{item.creator},
                creator_id=#{item.creatorId},
                modifier=#{item.modifier},
                modifier_id=#{item.modifierId},
                ctime=#{item.ctime},
                mtime=#{item.mtime},
                del_flag           = #{item.delFlag}
            where id = #{item.id}
        </foreach>
    </update>


    <!--逻辑删除-->
    <update id="delById">
        UPDATE eval_task_calibration
        SET del_flag = 1
        WHERE id = #{id}
    </update>
</mapper>