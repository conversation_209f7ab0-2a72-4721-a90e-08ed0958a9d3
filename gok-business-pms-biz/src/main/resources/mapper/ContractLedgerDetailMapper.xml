<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.gok.pboot.pms.mapper.ContractLedgerDetailMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.gok.pboot.pms.entity.domain.ContractLedgerDetail" id="contractLedgerDetailMap">
        <result property="id" column="id"/>
        <result property="mainid" column="mainid"/>
        <result property="yjskrq" column="yjskrq"/>
        <result property="skje" column="skje"/>
        <result property="sktjxmjd" column="sktjxmjd"/>
        <result property="skzt" column="skzt"/>
        <result property="skbz" column="skbz"/>
        <result property="djbkx" column="djbkx"/>
        <result property="sjskrq" column="sjskrq"/>
        <result property="fpkjzt" column="fpkjzt"/>
        <result property="sjkprq" column="sjkprq"/>
        <result property="sfkjebhs" column="sfkjebhs"/>
        <result property="sl" column="sl"/>
        <result property="kxbl" column="kxbl"/>
        <result property="ddksrq" column="ddksrq"/>
        <result property="ddjsrq" column="ddjsrq"/>
        <result property="ddfj" column="ddfj"/>
        <result property="ddfjImagefileid" column="ddfj_imagefileid"/>
        <result property="ddfjImagefilename" column="ddfj_imagefilename"/>
        <result property="jsdfj" column="jsdfj"/>
        <result property="jsdfjImagefileid" column="jsdfj_imagefileid"/>
        <result property="jsdfjImagefilename" column="jsdfj_imagefilename"/>
        <result property="kxmc" column="kxmc"/>
        <result property="sfkzt" column="sfkzt"/>
        <result property="sjsfkjehs" column="sjsfkjehs"/>
        <result property="sjsfkrq" column="sjsfkrq"/>
        <result property="dsfkje" column="dsfkje"/>
        <result property="ddsmj" column="ddsmj"/>
        <result property="ddsmjImagefileid" column="ddsmj_imagefileid"/>
        <result property="ddsmjImagefilename" column="ddsmj_imagefilename"/>
        <result property="creator" column="creator"/>
        <result property="creatorId" column="creator_id"/>
        <result property="modifier" column="modifier"/>
        <result property="modifierId" column="modifier_id"/>
        <result property="ctime" column="ctime"/>
        <result property="mtime" column="mtime"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <select id="selSkjeSum" resultType="com.gok.pboot.pms.entity.vo.ContractDetailSumVo">
        SELECT mainid    as id,
               sum(sjsfkjehs) as skje
        FROM contract_ledger_detail
        WHERE sjsfkjehs is not NULL
        GROUP BY mainid
    </select>

    <select id="selZbjSkjeSum" resultType="com.gok.pboot.pms.entity.vo.ContractDetailSumVo">
        SELECT mainid    as id,
               sum(skje) as skje
        FROM contract_ledger_detail
        WHERE djbkx ='质保金' and skzt =1
        GROUP BY mainid
    </select>
</mapper>