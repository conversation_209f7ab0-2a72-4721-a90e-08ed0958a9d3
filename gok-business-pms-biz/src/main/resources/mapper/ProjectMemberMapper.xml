<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.gok.pboot.pms.mapper.ProjectMemberMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.gok.pboot.pms.entity.domain.ProjectMember" id="projectMemberMap">
        <result property="id" column="id"/>
        <result property="projectId" column="project_id"/>
        <result property="memberRole" column="member_role"/>
        <result property="memberLevel" column="member_level"/>
        <result property="memberNumber" column="member_number"/>
        <result property="expectedCycle" column="expected_cycle"/>
        <result property="expectedEngagement" column="expected_engagement"/>
        <result property="assignorId" column="assignor_id"/>
        <result property="assignor" column="assignor"/>
    </resultMap>


</mapper>