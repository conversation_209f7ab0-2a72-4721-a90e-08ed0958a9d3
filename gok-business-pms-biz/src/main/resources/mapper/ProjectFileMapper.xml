<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.mapper.ProjectFileMapper">

    <!--通过参数过滤获取附件列表-->
    <select id="selectListByParams" resultType="com.gok.pboot.pms.entity.domain.ProjectFile">
        SELECT
            pf.id,
            pf.project_id,
            pf.doc_name,
            pf.doc_url,
            pf.association_type,
            pf.ctime
        FROM project_file pf
        WHERE
            1 = 1
        <if test="filter.projectId!= null">
            AND pf.project_id = #{filter.projectId}
        </if>
        <if test="filter.docName!= null">
            AND pf.doc_name LIKE CONCAT('%', #{filter.docName}, '%')
        </if>
        <if test="filter.associationType!= null">
            AND pf.association_type = #{filter.associationType}
        </if>
    </select>

</mapper>
