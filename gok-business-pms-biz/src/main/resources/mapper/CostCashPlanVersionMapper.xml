<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.cost.mapper.CostCashPlanVersionMapper">

    <select id="getCurrentVersion" resultType="com.gok.pboot.pms.cost.entity.domain.CostCashPlanVersion">
        SELECT *
        FROM cost_cash_plan_version
        WHERE project_id = #{projectId}
        AND version_status = ${@<EMAIL>()}
        AND del_flag = ${@<EMAIL>()}
        ORDER BY ctime DESC
        LIMIT 1
    </select>

    <update id="updateHistoryVersion">
        UPDATE cost_cash_plan_version
        SET version_status = ${@<EMAIL>()}
        WHERE project_id = #{projectId}
        AND version_status = ${@<EMAIL>()}
        AND del_flag = ${@<EMAIL>()}
    </update>

    <select id="getVersionList" resultType="com.gok.pboot.pms.cost.entity.domain.CostCashPlanVersion">
        SELECT 
            id,
            project_id,
            version_no,
            version_status,
            cost_version_id,
            cost_version,
            creator,
            creator_role,
            ctime
        FROM cost_cash_plan_version
        WHERE project_id = #{projectId}
        AND del_flag = ${@<EMAIL>()}
        ORDER BY ctime DESC
    </select>

    <select id="findPage" resultType="com.gok.pboot.pms.cost.entity.vo.CostCashPlanVersionVO">
        SELECT
            cpv.id,
            cpv.project_id,
            cpv.version_no,
            cpv.version_status,
            cpv.cost_version_id,
            cpv.cost_version,
            cpv.creator_role,
            cpv.creator_id,
            cpv.creator,
            cpv.ctime,
            cpv.request_id,
            cpv.request_name
        FROM cost_cash_plan_version cpv
        WHERE cpv.project_id = #{projectId}
          AND cpv.del_flag = ${@<EMAIL>()}
        ORDER BY ctime DESC
    </select>

    <update id="updateCostVersion">
        UPDATE cost_cash_plan_version
        SET cost_version_id = #{costVersionId},
            cost_version = #{costVersion}
        WHERE
          id = #{id}
          AND  cost_version_id IS NULL
          AND cost_version IS NULL
          AND del_flag = ${@<EMAIL>()}
    </update>

</mapper>