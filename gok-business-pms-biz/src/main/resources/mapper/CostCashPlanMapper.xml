<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.cost.mapper.CostCashPlanMapper">

    <select id="getCostCashPlanList" resultType="com.gok.pboot.pms.cost.entity.vo.CostCashPlanVO">
        SELECT
            ccp.version_id,
            ccpv.version_no,
            ccp.time_month,
            ccp.plan_month,
            ccp.month_income,
            ccp.labor_cost,
            ccp.expense_cost,
            ccp.outsourcing_cost
        FROM cost_cash_plan AS ccp
        LEFT JOIN cost_cash_plan_version AS ccpv ON ccp.version_id = ccpv.id
        WHERE ccp.version_id IN
        <foreach collection="versionIds" item="versionId" index="index" open="(" close=")" separator=",">
            #{versionId}
        </foreach>
        AND ccp.del_flag = ${@<EMAIL>()}
        ORDER BY ccp.plan_month ASC
    </select>
    
</mapper>