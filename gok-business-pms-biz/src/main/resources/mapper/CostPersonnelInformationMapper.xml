<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.cost.mapper.CostPersonnelInformationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.gok.pboot.pms.cost.entity.domain.CostPersonnelInformation">
        <id column="id" property="id"/>
        <result column="project_id" property="projectId"/>
        <result column="user_id" property="userId"/>
        <result column="request_id" property="requestId"/>
        <result column="name" property="name"/>
        <result column="work_code" property="workCode"/>
        <result column="personnel_attribute" property="personnelAttribute"/>
        <result column="available_status" property="availableStatus"/>
        <result column="dept_id" property="deptId"/>
        <result column="dept_name" property="deptName"/>
        <result column="job_id" property="jobId"/>
        <result column="job_name" property="jobName"/>
        <result column="position_id" property="positionId"/>
        <result column="position_name" property="positionName"/>
        <result column="entry_time" property="entryTime"/>
        <result column="leave_time" property="leaveTime"/>
        <result column="status" property="status"/>
        <result column="duration_days" property="durationDays"/>
        <result column="domicile" property="domicile"/>
        <result column="quotation_type" property="quotationType"/>
        <result column="quotation_include_tax" property="quotationIncludeTax"/>
        <result column="quoted_rate_id" property="quotedRateId"/>
        <result column="quotation_exclude_tax" property="quotationExcludeTax"/>
        <result column="flat_rate" property="flatRate"/>
        <result column="foreign_purchase_unit_price_include_tax" property="foreignPurchaseUnitPriceIncludeTax"/>
        <result column="foreign_purchase_tax_rate_id" property="foreignPurchaseTaxRateId"/>
        <result column="foreign_purchase_unit_price_exclude_tax" property="foreignPurchaseUnitPriceExcludeTax"/>
        <result column="human_day_price" property="humanDayPrice"/>
        <result column="human_month_price" property="humanMonthPrice"/>
        <result column="belong_month" property="belongMonth"/>
        <result column="creator" property="creator"/>
        <result column="creator_id" property="creatorId"/>
        <result column="modifier" property="modifier"/>
        <result column="modifier_id" property="modifierId"/>
        <result column="ctime" property="ctime"/>
        <result column="mtime" property="mtime"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, project_id, user_id, request_id, `name`, work_code, personnel_attribute, available_status, dept_id, dept_name, job_id, job_name, position_id, position_name, entry_time, leave_time, `status`, duration_days, domicile, quotation_type, quotation_include_tax, quoted_rate_id, quoted_rate, quotation_exclude_tax, flat_rate, foreign_purchase_unit_price_include_tax, foreign_purchase_tax_rate_id, foreign_purchase_tax_rate, foreign_purchase_unit_price_exclude_tax, human_day_price, human_month_price, belong_month, creator, creator_id, modifier, modifier_id, ctime, mtime, del_flag
    </sql>
    
    <!-- 根据工号列表查询人员信息,确保工号不为空且结果按工号去重 -->
    <select id="findByWorkCodes" resultMap="BaseResultMap">
        SELECT
        *
        FROM
        cost_personnel_information
        WHERE
        del_flag = 0
        AND work_code IS NOT NULL
        AND work_code != ''
        <if test="workCodes != null and workCodes.size() > 0">
            AND work_code IN
            <foreach collection="workCodes" item="workCode" open="(" separator="," close=")">
                #{workCode}
            </foreach>
        </if>
        GROUP BY
        work_code
    </select>
    
    <update id="updateBatch">
        UPDATE cost_personnel_information
        SET
        project_id = CASE
        <foreach collection="list" item="item">
            WHEN id = #{item.id} THEN #{item.projectId}
        </foreach>
        END,
        user_id = CASE
        <foreach collection="list" item="item">
            WHEN id = #{item.id} THEN
            <choose>
                <when test="item.userId == null">NULL</when>
                <otherwise>#{item.userId}</otherwise>
            </choose>
        </foreach>
        END,
        request_id = CASE
        <foreach collection="list" item="item">
            WHEN id = #{item.id} THEN
            <choose>
                <when test="item.requestId == null">NULL</when>
                <otherwise>#{item.requestId}</otherwise>
            </choose>
        </foreach>
        END,
        name = CASE
        <foreach collection="list" item="item">
            WHEN id = #{item.id} THEN
            <choose>
                <when test="item.name == null">NULL</when>
                <otherwise>#{item.name}</otherwise>
            </choose>
        </foreach>
        END,
        work_code = CASE
        <foreach collection="list" item="item">
            WHEN id = #{item.id} THEN #{item.workCode}
        </foreach>
        END,
        personnel_attribute = CASE
        <foreach collection="list" item="item">
            WHEN id = #{item.id} THEN #{item.personnelAttribute}
        </foreach>
        END,
        available_status = CASE
        <foreach collection="list" item="item">
            WHEN id = #{item.id} THEN #{item.availableStatus}
        </foreach>
        END,
        dept_id = CASE
        <foreach collection="list" item="item">
            WHEN id = #{item.id} THEN
            <choose>
                <when test="item.deptId == null">NULL</when>
                <otherwise>#{item.deptId}</otherwise>
            </choose>
        </foreach>
        END,
        dept_name = CASE
        <foreach collection="list" item="item">
            WHEN id = #{item.id} THEN
            <choose>
                <when test="item.deptName == null">NULL</when>
                <otherwise>#{item.deptName}</otherwise>
            </choose>
        </foreach>
        END,
        job_id = CASE
        <foreach collection="list" item="item">
            WHEN id = #{item.id} THEN
            <choose>
                <when test="item.jobId == null">NULL</when>
                <otherwise>#{item.jobId}</otherwise>
            </choose>
        </foreach>
        END,
        job_name = CASE
        <foreach collection="list" item="item">
            WHEN id = #{item.id} THEN
            <choose>
                <when test="item.jobName == null">NULL</when>
                <otherwise>#{item.jobName}</otherwise>
            </choose>
        </foreach>
        END,
        position_id = CASE
        <foreach collection="list" item="item">
            WHEN id = #{item.id} THEN
            <choose>
                <when test="item.positionId == null">NULL</when>
                <otherwise>#{item.positionId}</otherwise>
            </choose>
        </foreach>
        END,
        position_name = CASE
        <foreach collection="list" item="item">
            WHEN id = #{item.id} THEN
            <choose>
                <when test="item.positionName == null">NULL</when>
                <otherwise>#{item.positionName}</otherwise>
            </choose>
        </foreach>
        END,
        entry_time = CASE
        <foreach collection="list" item="item">
            WHEN id = #{item.id} THEN
            <choose>
                <when test="item.entryTime == null">NULL</when>
                <otherwise>#{item.entryTime}</otherwise>
            </choose>
        </foreach>
        END,
        leave_time = CASE
        <foreach collection="list" item="item">
            WHEN id = #{item.id} THEN
            <choose>
                <when test="item.leaveTime == null">NULL</when>
                <otherwise>#{item.leaveTime}</otherwise>
            </choose>
        </foreach>
        END,
        status = CASE
        <foreach collection="list" item="item">
            WHEN id = #{item.id} THEN #{item.status}
        </foreach>
        END,
        duration_days = CASE
        <foreach collection="list" item="item">
            WHEN id = #{item.id} THEN
            <choose>
                <when test="item.durationDays == null">NULL</when>
                <otherwise>#{item.durationDays}</otherwise>
            </choose>
        </foreach>
        END,
        domicile = CASE
        <foreach collection="list" item="item">
            WHEN id = #{item.id} THEN
            <choose>
                <when test="item.domicile == null">NULL</when>
                <otherwise>#{item.domicile}</otherwise>
            </choose>
        </foreach>
        END,
        quotation_type = CASE
        <foreach collection="list" item="item">
            WHEN id = #{item.id} THEN
            <choose>
                <when test="item.quotationType == null">NULL</when>
                <otherwise>#{item.quotationType}</otherwise>
            </choose>
        </foreach>
        END,
        quotation_include_tax = CASE
        <foreach collection="list" item="item">
            WHEN id = #{item.id} THEN
            <choose>
                <when test="item.quotationIncludeTax == null">NULL</when>
                <otherwise>#{item.quotationIncludeTax}</otherwise>
            </choose>
        </foreach>
        END,
        quoted_rate_id = CASE
        <foreach collection="list" item="item">
            WHEN id = #{item.id} THEN
            <choose>
                <when test="item.quotedRateId == null">NULL</when>
                <otherwise>#{item.quotedRateId}</otherwise>
            </choose>
        </foreach>
        END,
        quotation_exclude_tax = CASE
        <foreach collection="list" item="item">
            WHEN id = #{item.id} THEN
            <choose>
                <when test="item.quotationExcludeTax == null">NULL</when>
                <otherwise>#{item.quotationExcludeTax}</otherwise>
            </choose>
        </foreach>
        END,
        flat_rate = CASE
        <foreach collection="list" item="item">
            WHEN id = #{item.id} THEN
            <choose>
                <when test="item.flatRate == null">NULL</when>
                <otherwise>#{item.flatRate}</otherwise>
            </choose>
        </foreach>
        END,
        foreign_purchase_unit_price_include_tax = CASE
        <foreach collection="list" item="item">
            WHEN id = #{item.id} THEN
            <choose>
                <when test="item.foreignPurchaseUnitPriceIncludeTax == null">NULL</when>
                <otherwise>#{item.foreignPurchaseUnitPriceIncludeTax}</otherwise>
            </choose>
        </foreach>
        END,
        foreign_purchase_tax_rate_id = CASE
        <foreach collection="list" item="item">
            WHEN id = #{item.id} THEN
            <choose>
                <when test="item.foreignPurchaseTaxRateId == null">NULL</when>
                <otherwise>#{item.foreignPurchaseTaxRateId}</otherwise>
            </choose>
        </foreach>
        END,
        foreign_purchase_unit_price_exclude_tax = CASE
        <foreach collection="list" item="item">
            WHEN id = #{item.id} THEN
            <choose>
                <when test="item.foreignPurchaseUnitPriceExcludeTax == null">NULL</when>
                <otherwise>#{item.foreignPurchaseUnitPriceExcludeTax}</otherwise>
            </choose>
        </foreach>
        END,
        belong_month = CASE
        <foreach collection="list" item="item">
            WHEN id = #{item.id} THEN
            <choose>
                <when test="item.belongMonth == null">NULL</when>
                <otherwise>#{item.belongMonth}</otherwise>
            </choose>
        </foreach>
        END,
        modifier = CASE
        <foreach collection="list" item="item">
            WHEN id = #{item.id} THEN #{item.modifier}
        </foreach>
        END,
        modifier_id = CASE
        <foreach collection="list" item="item">
            WHEN id = #{item.id} THEN #{item.modifierId}
        </foreach>
        END,
        mtime = CASE
        <foreach collection="list" item="item">
            WHEN id = #{item.id} THEN NOW()
        </foreach>
        END
        WHERE id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>
    <select id="getMaxWorkCode" resultType="java.lang.String">
        select max(work_code)
        from cost_personnel_information
        where personnel_attribute = ${@com.gok.pboot.pms.cost.enums.PersonnelAttributeEnum@THIRD_PARTY.getValue}
    </select>

    <select id="getPersonInfoList" resultType="com.gok.pboot.pms.cost.entity.vo.CostPersonnelInformationVO">
        SELECT
        a.id,
        a.request_id,
        a.project_id,
        a.user_id,
        IFNULL( a.`name`, b.alias_name ) AS name,
        IFNULL( a.work_code, b.work_code ) AS work_code,
        a.personnel_attribute,
        a.available_status,
        a.dept_id,
        a.dept_name,
        a.job_id,
        a.job_name,
        a.position_id,
        a.position_name,
        a.entry_time,
        a.leave_time,
        a.`status`,
        a.duration_days,
        a.domicile,
        a.quotation_type,
        a.quotation_include_tax,
        a.quoted_rate_id,
        a.quotation_exclude_tax,
        a.flat_rate,
        a.foreign_purchase_unit_price_include_tax,
        a.foreign_purchase_tax_rate_id,
        a.foreign_purchase_unit_price_exclude_tax,
        a.belong_month,
        a.creator,
        a.mtime
        FROM
        cost_personnel_information a
        LEFT JOIN mhour_roster b ON a.user_id = b.id
        WHERE
        a.del_flag = 0
        <if test="dto.projectIds != null and dto.projectIds.size() > 0">
            and a.project_id in
            <foreach collection="dto.projectids" item="projectId" open="(" separator="," close=")">
                #{projectId}
            </foreach>
        </if>
        <if test="dto.ids != null and dto.ids.size() > 0">
            and a.id in
            <foreach collection="dto.ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="dto.status != null">
            and a.status = #{dto.status}
        </if>
        <if test="dto.personnelAttribute != null">
            and a.personnel_attribute = #{dto.personnelAttribute}
        </if>
        <if test="dto.availableStatus != null">
            and a.available_status = #{dto.availableStatus}
        </if>
        <if test="dto.belongMonth != null and dto.belongMonth.size() > 0">
            and a.belong_month in
            <foreach collection="dto.belongMonth" item="belongMonth" open="(" separator="," close=")">
                #{belongMonth}
            </foreach>
        </if>
        <if test="dto.nameOrWorkCode != null and dto.nameOrWorkCode != ''">
            <bind name="nameOrWorkCode" value="'%' + dto.nameOrWorkCode + '%'"/>
            and (a.name like #{nameOrWorkCode} or a.work_code like #{nameOrWorkCode})
        </if>
        <if test="dto.userIds != null and dto.userIds.size() > 0">
            and (a.user_id in
            <foreach collection="dto.userIds" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
            or a.creator_id in
            <foreach collection="dto.userIds" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
            )
        </if>
        GROUP BY
        a.project_id,
        a.user_id,
        a.`name`,
        a.work_code,
        a.belong_month
        order by a.ctime desc
    </select>
    <select id="getQuotationIncludeTax" resultType="java.math.BigDecimal">
        <choose>
            <when test="dto.quotationType != null and dto.quotationType == @<EMAIL>()">
                select distinct human_day_price
            </when>
            <when test="dto.quotationType != null and dto.quotationType == @<EMAIL>()">
                select distinct human_month_price
            </when>
        </choose>
        from cost_personnel_information
        where del_flag = 0
        <if test="dto.projectId != null">
            and project_id = #{dto.projectId}
        </if>
        <if test="dto.name != null and dto.name != ''">
            and name = #{dto.name}
        </if>
        <if test="dto.personnelAttribute != null">
            and personnel_attribute = #{dto.personnelAttribute}
        </if>
    </select>

    <sql id="findByWorkCodes">
          
    </sql>

    <select id="getPersonAutoBringInfo" resultType="com.gok.pboot.pms.cost.entity.vo.CostPersonnelInformationVO"
            parameterType="com.gok.pboot.pms.cost.entity.dto.AutoBringConditionDTO">
        SELECT DISTINCT
        a.id,
        a.project_id,
        a.user_id,
        IFNULL( a.`name`, b.alias_name ) AS name,
        IFNULL( a.work_code, b.work_code ) AS work_code,
        a.personnel_attribute,
        a.available_status,
        a.dept_id,
        a.dept_name,
        a.job_id,
        a.job_name,
        a.position_id,
        a.position_name,
        a.entry_time,
        a.leave_time,
        a.`status`,
        a.duration_days,
        a.domicile,
        a.quotation_type,
        a.quotation_include_tax,
        a.quoted_rate_id,
        a.quotation_exclude_tax,
        a.flat_rate,
        a.foreign_purchase_unit_price_include_tax,
        a.foreign_purchase_tax_rate_id,
        a.foreign_purchase_unit_price_exclude_tax,
        a.belong_month,
        a.mtime
        FROM
        cost_personnel_information a
        LEFT JOIN mhour_roster b ON a.user_id = b.id
        WHERE
        a.del_flag = 0
        <if test="dto.projectId != null">
            and a.project_id = #{dto.projectId}
        </if>
        <if test="dto.name != null and dto.name != ''">
            <bind name="name" value="'%' + dto.name + '%'"/>
            and a.`name` like #{name}
        </if>
        <if test="dto.personnelAttribute != null">
            and a.personnel_attribute = #{dto.personnelAttribute}
        </if>
        order by a.mtime desc
    </select>

    <select id="getByIncomeCalculationDetail"
            resultType="com.gok.pboot.pms.cost.entity.domain.CostPersonnelInformation">

    </select>

	

</mapper>
