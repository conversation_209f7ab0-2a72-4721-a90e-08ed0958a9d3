<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.eval.mapper.EvalProjectOverviewMapper">

    <resultMap id="baseResultMap" type="com.gok.pboot.pms.eval.entity.domain.EvalProjectOverview">
        <id column="id" property="id"/>
        <result column="project_id" property="projectId"/>
        <result column="project_name" property="projectName"/>
        <result column="project_no" property="projectNo"/>
        <result column="deliver_type" property="deliverType"/>
        <result column="budget_cost" property="budgetCost"/>
        <result column="actual_cost" property="actualCost"/>
        <result column="cost_deviation_rate" property="costDeviationRate"/>
        <result column="plan_completion_cycle" property="planCompletionCycle"/>
        <result column="actual_completion_cycle" property="actualCompletionCycle"/>
        <result column="plan_deviation_rate" property="planDeviationRate"/>
        <result column="customer_eval_score" property="customerEvalScore"/>
        <result column="eval_status" property="evalStatus"/>
        <result column="manager_eval_status" property="managerEvalStatus"/>
        <result column="creator" property="creator"/>
        <result column="creator_id" property="creatorId"/>
        <result column="modifier" property="modifier"/>
        <result column="modifier_id" property="modifierId"/>
        <result column="ctime" property="ctime"/>
        <result column="mtime" property="mtime"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

    <select id="getUnSaveProjectInfoList" resultType="com.gok.pboot.pms.eval.entity.vo.EvalProjectOverviewVO">
        SELECT
            pi.id AS projectId,
            pi.item_no AS projectNo,
            pi.item_name AS projectName,
            pi.deliver_type AS deliverType,
            IFNULL(cbp.manager_id, mr.leader_id) AS supportManagerId,
            mr2.alias_name AS supportManagerName
        FROM
            project_info AS pi
            LEFT JOIN customer_business_person AS cbp ON pi.business_id = cbp.business_id AND cbp.manager_role = 3
            LEFT JOIN mhour_roster AS mr ON pi.manager_user_id = mr.id
            LEFT JOIN mhour_roster AS mr2 ON mr2.id = IFNULL(cbp.manager_id, mr.leader_id)
        WHERE
          pi.project_status = 6
          AND pi.deliver_type = 1
          AND pi.id NOT IN ( SELECT project_id FROM eval_end_project )
          AND pi.id NOT IN (
            SELECT DISTINCT epo.project_id AS project_id
            FROM eval_project_overview AS epo
            WHERE epo.del_flag = ${@<EMAIL>()}
        );
    </select>

    <select id="findList" resultType="com.gok.pboot.pms.eval.entity.vo.EvalProjectOverviewVO">
        SELECT
            epo.id,
            epo.project_id,
            epo.project_name,
            epo.project_no,
            epo.deliver_type,
            epo.plan_completion_cycle,
            epo.actual_completion_cycle,
            epo.plan_deviation_rate,
            epo.customer_eval_score,
            ecl.commendation_letter,
            epo.eval_status,
            epo.manager_eval_status,
            pi.business_id,
            cb.`name` AS businessName,
            pi.unit_id,
            cbu.unit_name AS unitName,
            pi.manager_user_id,
            pi.manager_user_name,
            pi.salesman_user_id,
            pi.project_status,
            pi.pre_sale_user_name AS salesmanUserName,
            pi.project_type,
            pd.zyywcb AS actualCost,
            IFNULL(cbp.manager_id, mr.leader_id) AS supportManagerId,
            mr2.alias_name AS supportManagerName,
            IF(epo.eval_status != 3, pd.zyywcb, epo.actual_cost) AS actualCost,
            IF(epo.eval_status != 3, NULL, epo.budget_cost) AS budgetCost,
            IF(epo.eval_status != 3, NULL, epo.cost_deviation_rate) AS costDeviationRate,
            IF(epo.eval_status != 3, NULL, epo.change_count) AS changeCount,
            epo.ctime
        FROM
            eval_project_overview AS epo
            LEFT JOIN project_info AS pi ON epo.project_id = pi.id
            LEFT JOIN customer_business AS cb ON pi.business_id = cb.id
            LEFT JOIN customer_business_unit AS cbu ON pi.unit_id = cbu.id
            LEFT JOIN project_data AS pd ON epo.project_id = pd.id
            LEFT JOIN customer_business_person AS cbp ON pi.business_id = cbp.business_id AND cbp.manager_role = 3
            LEFT JOIN mhour_roster AS mr ON pi.manager_user_id = mr.id
            LEFT JOIN eval_commendation_letter AS ecl ON epo.project_id = ecl.project_id
            LEFT JOIN mhour_roster AS mr2 ON mr2.id = IFNULL(cbp.manager_id, mr.leader_id)
        WHERE
        epo.del_flag = ${@<EMAIL>()}
        <if test="query.projectIds != null and query.projectIds.size() > 0">
            AND epo.project_id IN
            <foreach collection="query.projectIds" item="projectId" open="(" separator="," close=")">
                #{projectId}
            </foreach>
        </if>
        <if test="query.projectNo != null and query.projectNo != ''">
            AND epo.project_no LIKE CONCAT('%', #{query.projectNo}, '%')
        </if>
        <if test="query.projectName != null and query.projectName != ''">
            AND epo.project_name LIKE CONCAT('%', #{query.projectName}, '%')
        </if>
        <if test="query.evalStatus != null">
            AND epo.eval_status = #{query.evalStatus}
        </if>
        ORDER BY
        eval_status, epo.ctime DESC, id
    </select>


    <select id="getById" resultType="com.gok.pboot.pms.eval.entity.vo.EvalProjectOverviewVO">
        SELECT
        epo.id,
        epo.project_id,
        epo.project_name,
        epo.project_no,
        epo.deliver_type,
        epo.plan_completion_cycle,
        epo.actual_completion_cycle,
        epo.plan_deviation_rate,
        epo.customer_eval_score,
        ecl.commendation_letter,
        epo.eval_status,
        epo.manager_eval_status,
        pi.business_id,
        pi.project_status,
        cb.`name` AS businessName,
        pi.unit_id,
        cbu.unit_name AS unitName,
        pi.manager_user_id,
        pi.manager_user_name,
        pi.salesman_user_id,
        pi.pre_sale_user_name AS salesmanUserName,
        pi.project_type,
        pd.zyywcb AS actualCost,
        t1.changeCount AS changeCount,
        IFNULL(cbp.manager_id, mr.leader_id) AS supportManagerId
        FROM
        eval_project_overview AS epo
        LEFT JOIN project_info AS pi ON epo.project_id = pi.id
        LEFT JOIN customer_business AS cb ON pi.business_id = cb.id
        LEFT JOIN customer_business_unit AS cbu ON pi.unit_id = cbu.id
        LEFT JOIN project_data AS pd ON epo.project_id = pd.id
        LEFT JOIN customer_business_person AS cbp ON pi.business_id = cbp.business_id AND cbp.manager_role = 3
        LEFT JOIN mhour_roster AS mr ON pi.manager_user_id = mr.id
        LEFT JOIN eval_commendation_letter AS ecl ON epo.project_id = ecl.project_id
        LEFT JOIN (
        SELECT
        project_id,
        COUNT(*) AS changeCount
        FROM
        cost_baseline_version_record
        GROUP BY
        project_id
        )AS t1 ON t1.project_id = epo.project_id
        WHERE
        epo.del_flag = ${@<EMAIL>()}
        AND epo.id = #{id}
    </select>

    <select id="getByProjectId" resultType="com.gok.pboot.pms.eval.entity.domain.EvalProjectOverview">
        SELECT
            epo.*,
            pi.manager_user_id AS managerUserId,
            pi.manager_user_name AS managerUserName
        FROM
            eval_project_overview AS epo
            LEFT JOIN project_info AS pi ON epo.project_id = pi.id
        WHERE
            epo.del_flag = ${@<EMAIL>()}
            AND epo.project_id = #{projectId}
    </select>

</mapper>