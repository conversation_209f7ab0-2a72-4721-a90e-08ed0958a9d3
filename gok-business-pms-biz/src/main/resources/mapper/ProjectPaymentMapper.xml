<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.mapper.ProjectPaymentMapper">
    <resultMap id="BaseResultMap" type="com.gok.pboot.pms.entity.domain.ProjectPayment">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="document_number" jdbcType="VARCHAR" property="documentNumber"/>
        <result column="payment_company" jdbcType="BOOLEAN" property="paymentCompany"/>
        <result column="customer_id" jdbcType="BIGINT" property="customerId"/>
        <result column="customer_name" jdbcType="VARCHAR" property="customerName"/>
        <result column="enterprise_name" jdbcType="VARCHAR" property="enterpriseName"/>
        <result column="payment_date" jdbcType="VARCHAR" property="paymentDate"/>
        <result column="payment_amount" jdbcType="DECIMAL" property="paymentAmount"/>
        <result column="payment_platform" jdbcType="VARCHAR" property="paymentPlatform"/>
        <result column="voucher_number" jdbcType="VARCHAR" property="voucherNumber"/>
        <result column="budget_collection_amount" jdbcType="DECIMAL" property="budgetCollectionAmount"/>
        <result column="payment_note" jdbcType="VARCHAR" property="paymentNote"/>
        <result column="bank_account" jdbcType="VARCHAR" property="bankAccount"/>
        <result column="claim_status" jdbcType="INTEGER" property="claimStatus"/>
        <result column="lock_status" jdbcType="INTEGER" property="lockStatus"/>
        <result column="auto_lock" jdbcType="INTEGER" property="autoLock"/>
        <result column="push_status" jdbcType="INTEGER" property="pushStatus"/>
        <result column="record_man_id" jdbcType="BIGINT" property="recordManId"/>
        <result column="creator_id" jdbcType="BIGINT" property="creatorId"/>
        <result column="creator_name" jdbcType="VARCHAR" property="creatorName"/>
        <result column="education_ids" jdbcType="VARCHAR" property="educationIds"/>
        <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="del_flag" jdbcType="CHAR" property="delFlag"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <association property="projectPaymentClaim">
            <id column="id" jdbcType="BIGINT" property="id"/>
            <result column="project_payment_id" jdbcType="BIGINT" property="projectPaymentId"/>
            <result column="salesman_user_id" jdbcType="BIGINT" property="salesmanUserId"/>
            <result column="salesman_user_name" jdbcType="VARCHAR" property="salesmanUserName"/>
            <result column="business_line" jdbcType="INTEGER" property="businessLine"/>
            <result column="project_collection" jdbcType="INTEGER" property="projectCollection"/>
            <result column="collection_within_budget" jdbcType="INTEGER" property="collectionWithinBudget"/>
            <result column="contract_payment_id" jdbcType="BIGINT" property="contractPaymentId"/>
            <result column="contract_payment" jdbcType="VARCHAR" property="contractPayment"/>
            <result column="contract_name" jdbcType="VARCHAR" property="contractName"/>
            <result column="project_id" jdbcType="BIGINT" property="projectId"/>
            <result column="project_name" jdbcType="VARCHAR" property="projectName"/>
            <result column="payment_dept_id" jdbcType="BIGINT" property="paymentDeptId"/>
            <result column="payment_dept" jdbcType="VARCHAR" property="paymentDept"/>
            <result column="payment_secondary_dept_id" jdbcType="BIGINT" property="paymentSecondaryDeptId"/>
            <result column="payment_secondary_dept" jdbcType="VARCHAR" property="paymentSecondaryDept"/>
            <result column="belonging_area_id" jdbcType="VARCHAR" property="belongingAreaId"/>
            <result column="belonging_area" jdbcType="VARCHAR" property="belongingArea"/>
            <result column="claimant_id" jdbcType="BIGINT" property="claimantId"/>
            <result column="claimant_name" jdbcType="VARCHAR" property="claimantName"/>
            <result column="claimant_date" jdbcType="TIMESTAMP" property="claimantDate"/>
            <result column="del_flag" jdbcType="CHAR" property="delFlag"/>
            <result column="business_block" jdbcType="INTEGER" property="businessBlock" />
            <result column="skill_type" jdbcType="INTEGER" property="skillType" />
        </association>
    </resultMap>
    <select id="findPage" resultMap="BaseResultMap">
        SELECT
        p1.id,
        p1.document_number,
        p1.payment_company,
        p1.customer_id,
        p1.customer_name,
        p1.enterprise_name,
        p1.payment_date,
        p1.payment_amount,
        p1.payment_platform,
        p1.voucher_number,
        p1.budget_collection_amount,
        p1.payment_note,
        p1.bank_account,
        p1.claim_status,
        p1.lock_status,
        p1.auto_lock,
        p1.push_status,
        p1.record_man_id,
        p1.creator_id,
        p1.creator_name,
        p1.education_ids,
        p2.salesman_user_id,
        p2.salesman_user_name,
        p2.business_line,
        p2.project_collection,
        p2.collection_within_budget,
        p2.contract_payment_id,
        p2.contract_payment,
        p2.contract_name,
        p2.project_id,
        p2.project_name,
        p2.payment_dept_id,
        p2.payment_dept,
        p2.payment_secondary_dept_id,
        p2.payment_secondary_dept,
        p2.belonging_area_id,
        p2.belonging_area,
        p2.claimant_id,
        p2.claimant_name,
        DATE_FORMAT(p2.claimant_date, '%Y-%m-%d') AS claimant_date,
        p2.business_block,
        p2.skill_type
        FROM
        project_payment AS p1
        LEFT JOIN project_payment_claim AS p2 ON p1.id = p2.project_payment_id AND p2.del_flag = 0
        <where>
            p1.del_flag = 0
            <!-- 数据权限 -->
            <if test="auth.isAll">
                AND ( p1.creator_id IN
                <foreach collection="auth.userIdList" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                OR p1.record_man_id IN
                <foreach collection="auth.userIdList" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                OR p2.claimant_id IN
                <foreach collection="auth.userIdList" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                OR p2.payment_dept_id IN
                <foreach collection="auth.deptIdList" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                OR p2.payment_secondary_dept_id IN
                <foreach collection="auth.deptIdList" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="query.ids != null and query.ids.size() > 0">
                AND p1.id IN
                <foreach collection="query.ids" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="query.documentNumber != null and query.documentNumber != ''">
                AND p1.document_number = #{query.documentNumber}
            </if>
            <if test="query.paymentCompany != null">
                AND p1.payment_company = #{query.paymentCompany}
            </if>
            <if test="query.customerOrEnterprise != null and query.customerOrEnterprise != ''">
                AND (p1.customer_name LIKE CONCAT('%', #{query.customerOrEnterprise} , '%')
                OR p1.enterprise_name LIKE CONCAT('%', #{query.customerOrEnterprise} , '%'))
            </if>
            <if test="query.paymentBegin != null and query.paymentBegin != ''">
                AND p1.payment_date BETWEEN #{query.paymentBegin} AND #{query.paymentEnd}
            </if>
            <if test="query.belongingArea != null and query.belongingArea != ''">
                AND p2.belonging_area LIKE CONCAT('%', #{query.belongingArea} , '%')
            </if>
            <if test="query.businessLine != null">
                AND p2.business_line = #{query.businessLine}
            </if>
            <if test="query.projectName != null and query.projectName != ''">
                AND p2.project_name LIKE CONCAT('%', #{query.projectName} , '%')
            </if>
            <if test="query.contractName != null and query.contractName != ''">
                AND p2.contract_name LIKE CONCAT('%', #{query.contractName} , '%')
            </if>
            <if test="query.paymentDeptId != null and query.paymentDeptId.size() > 0">
                AND (p2.payment_dept_id IN
                <foreach collection="query.paymentDeptId" separator="," open="(" close=")" index="index" item="item">
                    #{item}
                </foreach>
                OR p2.payment_secondary_dept_id IN
                <foreach collection="query.paymentDeptId" separator="," open="(" close=")" index="index" item="item">
                    #{item}
                </foreach>
                )
            </if>
            <if test="query.salesmanUserName != null and query.salesmanUserName != ''">
                AND p2.salesman_user_name LIKE CONCAT ('%', #{query.salesmanUserName} , '%')
            </if>
            <if test="query.documentStatus != null and query.documentStatus.size() > 0">
                AND(
                <if test="query.claimStatusByDocument != null and query.claimStatusByDocument.size() > 0">
                    p1.claim_status IN
                    <foreach collection="query.claimStatusByDocument" separator="," open="(" close=")" index="index" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="query.claimStatusByDocument != null and query.claimStatusByDocument.size() > 0
                          and query.lockStatusByDocument != null and query.lockStatusByDocument.size() > 0
                          or query.claimStatusByDocument != null and query.claimStatusByDocument.size() > 0
                          and query.pushStatusByDocument != null and query.pushStatusByDocument.size() > 0">
                    OR
                </if>
                <if test="query.lockStatusByDocument != null and query.lockStatusByDocument.size() > 0">
                    p1.lock_status IN
                    <foreach collection="query.lockStatusByDocument" separator="," open="(" close=")" index="index" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="query.lockStatusByDocument != null and query.lockStatusByDocument.size() > 0
                         and query.pushStatusByDocument != null and query.pushStatusByDocument.size() > 0">
                    OR
                </if>
                <if test="query.pushStatusByDocument != null and query.pushStatusByDocument.size() > 0">
                    p1.push_status IN
                    <foreach collection="query.pushStatusByDocument" separator="," open="(" close=")" index="index" item="item">
                        #{item}
                    </foreach>
                </if>)
            </if>
            <if test="query.collectionWithinBudget != null">
                AND p2.collection_within_budget = #{query.collectionWithinBudget}
            </if>
            <if test="query.claimStatus != null">
                AND p1.claim_status = #{query.claimStatus}
                AND p1.lock_status != 0
            </if>
            <if test="query.pushStatus != null">
                AND p1.push_status = #{query.pushStatus}
            </if>
            <if test="query.businessBlock != null">
                AND p2.business_block LIKE #{query.businessBlock}
            </if>
        </where>
        ORDER BY p1.document_number DESC
    </select>

    <select id="listByAuth" resultMap="BaseResultMap">
        SELECT
        p1.id,
        p1.document_number,
        p1.payment_company,
        p1.customer_id,
        p1.customer_name,
        p1.enterprise_name,
        p1.payment_date,
        p1.payment_amount,
        p1.payment_platform,
        p1.voucher_number,
        p1.budget_collection_amount,
        p1.payment_note,
        p1.bank_account,
        p1.claim_status,
        p1.lock_status,
        p1.auto_lock,
        p1.push_status,
        p1.record_man_id,
        p1.creator_id,
        p1.creator_name,
        p1.education_ids,
        p2.salesman_user_id,
        p2.salesman_user_name,
        p2.business_line,
        p2.project_collection,
        p2.collection_within_budget,
        p2.contract_payment_id,
        p2.contract_payment,
        p2.contract_name,
        p2.project_id,
        p2.project_name,
        p2.payment_dept_id,
        p2.payment_dept,
        p2.payment_secondary_dept_id,
        p2.payment_secondary_dept,
        p2.belonging_area_id,
        p2.belonging_area,
        p2.claimant_id,
        p2.claimant_name,
        DATE_FORMAT(p2.claimant_date, '%Y-%m-%d') AS claimant_date,
        p2.business_block,
        p2.skill_type
        FROM
        project_payment AS p1
        LEFT JOIN project_payment_claim AS p2 ON p1.id = p2.project_payment_id AND p2.del_flag = 0
        <where>
            p1.del_flag = 0
            AND p1.customer_name IS NULL
            AND p1.claim_status = 1
            <if test="query.ids != null and query.ids.size() > 0">
                AND p1.id IN
                <foreach collection="query.ids" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="query.documentNumber != null and query.documentNumber != ''">
                AND p1.document_number = #{query.documentNumber}
            </if>
            <if test="query.paymentCompany != null">
                AND p1.payment_company = #{query.paymentCompany}
            </if>
            <if test="query.customerOrEnterprise != null and query.customerOrEnterprise != ''">
                AND (p1.customer_name LIKE CONCAT('%', #{query.customerOrEnterprise} , '%')
                OR p1.enterprise_name LIKE CONCAT('%', #{query.customerOrEnterprise} , '%'))
            </if>
            <if test="query.paymentBegin != null and query.paymentBegin != ''">
                AND p1.payment_date BETWEEN #{query.paymentBegin} AND #{query.paymentEnd}
            </if>
            <if test="query.belongingArea != null and query.belongingArea != ''">
                AND p2.belonging_area LIKE CONCAT('%', #{query.belongingArea} , '%')
            </if>
            <if test="query.businessLine != null">
                AND p2.business_line = #{query.businessLine}
            </if>
            <if test="query.projectName != null and query.projectName != ''">
                AND p2.project_name LIKE CONCAT('%', #{query.projectName} , '%')
            </if>
            <if test="query.contractName != null and query.contractName != ''">
                AND p2.contract_name LIKE CONCAT('%', #{query.contractName} , '%')
            </if>
            <if test="query.paymentDeptId != null and query.paymentDeptId.size() > 0">
                AND (p2.payment_dept_id IN
                <foreach collection="query.paymentDeptId" separator="," open="(" close=")" index="index" item="item">
                    #{item}
                </foreach>
                OR p2.payment_secondary_dept_id IN
                <foreach collection="query.paymentDeptId" separator="," open="(" close=")" index="index" item="item">
                    #{item}
                </foreach>
                )
            </if>
            <if test="query.salesmanUserName != null and query.salesmanUserName != ''">
                AND p2.salesman_user_name LIKE CONCAT ('%', #{query.salesmanUserName} , '%')
            </if>
            <if test="query.documentStatus != null and query.documentStatus.size() > 0">
                AND(
                <if test="query.claimStatusByDocument != null and query.claimStatusByDocument.size() > 0">
                    p1.claim_status IN
                    <foreach collection="query.claimStatusByDocument" separator="," open="(" close=")" index="index" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="query.claimStatusByDocument != null and query.claimStatusByDocument.size() > 0
                          and query.lockStatusByDocument != null and query.lockStatusByDocument.size() > 0
                          or query.claimStatusByDocument != null and query.claimStatusByDocument.size() > 0
                          and query.pushStatusByDocument != null and query.pushStatusByDocument.size() > 0">
                    OR
                </if>
                <if test="query.lockStatusByDocument != null and query.lockStatusByDocument.size() > 0">
                    p1.lock_status IN
                    <foreach collection="query.lockStatusByDocument" separator="," open="(" close=")" index="index" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="query.lockStatusByDocument != null and query.lockStatusByDocument.size() > 0
                         and query.pushStatusByDocument != null and query.pushStatusByDocument.size() > 0">
                    OR
                </if>
                <if test="query.pushStatusByDocument != null and query.pushStatusByDocument.size() > 0">
                    p1.push_status IN
                    <foreach collection="query.pushStatusByDocument" separator="," open="(" close=")" index="index" item="item">
                        #{item}
                    </foreach>
                </if>)
            </if>
            <if test="query.collectionWithinBudget != null">
                AND p2.collection_within_budget = #{query.collectionWithinBudget}
            </if>
            <if test="query.claimStatus != null">
                AND p1.claim_status = #{query.claimStatus}
                AND p1.lock_status != 0
            </if>
            <if test="query.pushStatus != null">
                AND p1.push_status = #{query.pushStatus}
            </if>
            <if test="query.businessBlock != null">
                AND p2.business_block LIKE #{query.businessBlock}
            </if>
        </where>
    </select>

    <select id="listByPushOrLock" resultMap="BaseResultMap">
        SELECT
            p1.id,
            p1.document_number,
            p1.payment_company,
            p1.customer_id,
            p1.customer_name,
            p1.enterprise_name,
            p1.payment_date,
            IFNULL(p1.payment_amount, 0.00) AS payment_amount,
            p1.payment_platform,
            p1.voucher_number,
            p1.budget_collection_amount,
            p1.payment_note,
            p1.bank_account,
            p1.claim_status,
            p1.lock_status,
            p1.auto_lock,
            p1.push_status,
            p1.record_man_id,
            p1.creator_id,
            p1.creator_name,
            p1.education_ids,
            p2.salesman_user_id,
            p2.salesman_user_name,
            p2.business_line,
            p2.project_collection,
            p2.collection_within_budget,
            p2.contract_payment_id,
            p2.contract_payment,
            p2.contract_name,
            p2.project_id,
            p2.project_name,
            p2.payment_dept_id,
            p2.payment_dept,
            p2.payment_secondary_dept_id,
            p2.payment_secondary_dept,
            p2.belonging_area_id,
            p2.belonging_area,
            p2.claimant_id,
            p2.claimant_name,
            DATE_FORMAT(p2.claimant_date, '%Y-%m-%d') AS claimant_date,
            p2.business_block,
            p2.skill_type
        FROM
            project_payment AS p1
        LEFT JOIN project_payment_claim AS p2 ON p1.id = p2.project_payment_id AND p2.del_flag = 0
        WHERE
            p1.del_flag = 0
            AND (p1.push_status = 1
            OR p1.lock_status = 0
    </select>

</mapper>