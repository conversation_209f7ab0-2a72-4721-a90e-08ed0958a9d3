<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.mapper.ProjectRiskMapper">
    
    <sql id="baseColumnList">
        a.id,
        a.project_id,
        a.description,
        a.probability,
        a.influence_degree,
        a.`level`,
        a.response_plan,
        a.charge_user,
        a.charge_user_id,
        a.status
    </sql>

    <select id="findListPage" resultMap="BaseResultMapProjectRiskFindPageVo">
        SELECT
        id,
        description,
        probability,
        influence_degree,
        `level`,
        response_plan,
        charge_user,
        charge_user_id,
        status
        FROM project_risk
        <where>
            del_flag = 0
            <if test="filter.projectId != null ">
                AND project_id = #{filter.projectId}
            </if>
        </where>
        ORDER BY ctime DESC
    </select>

    <!--根据id查询项目风险详情-->
    <select id="selectById" resultType="com.gok.pboot.pms.entity.domain.ProjectRisk">
        SELECT *
        FROM project_risk
        <where>
            id = #{id} AND del_flag = 0
        </where>
    </select>

    <!--根据id查询风险项目数-->
    <select id="findChargeRiskNum" resultType="java.lang.Integer">
        select count(*) as riskNumber
        from project_risk
        where
            del_flag = 0
            AND status = 0
            AND charge_user_id = #{id}
    </select>

    <!--根据负责人id分页查询风险表-->
    <select id="findPage" resultType="com.gok.pboot.pms.entity.domain.ProjectRisk">
        SELECT
            <include refid="baseColumnList" />
        FROM
            project_risk a
        WHERE
            del_flag = 0
            AND status = 0
        <if test="filter.chargeUserId != null">
            AND charge_user_id = #{filter.chargeUserId}
        </if>
        <if test="filter.projectIds != null">
            AND project_id IN
            <foreach collection="filter.projectIds" item="pId" open="(" separator="," close=")">
                #{pId}
            </foreach>
        </if>
        ORDER BY id DESC
    </select>

    <!--根据id列表批量查询风险信息，需支持查询已删除项目-->
    <select id="selectByIds" resultType="com.gok.pboot.pms.entity.domain.ProjectRisk">
        SELECT *
        FROM project_risk
        <where>
            id IN
            <foreach collection="ids" open="(" close=")" separator="," item="id">
                #{id}
            </foreach>
        </where>
    </select>

    <!--项目信息详情查询，需支持查询已删除项目-->
    <select id="findRiskSpecialById" resultType="com.gok.pboot.pms.entity.domain.ProjectRisk">
        SELECT *
        FROM project_risk
        <where>
            id = #{id}
        </where>
    </select>

    <select id="selectByProjectId" resultType="com.gok.pboot.pms.entity.domain.ProjectRisk">
        SELECT
        id,
        description,
        probability,
        influence_degree,
        `level`,
        response_plan,
        charge_user,
        charge_user_id,
        status
        FROM project_risk
        <where>
            del_flag = 0
            AND project_id = #{projectId}
            AND status = 0
        </where>
        ORDER BY ctime DESC
    </select>

    <!--  批量逻辑删除  -->
    <update id="batchDel">
        UPDATE project_risk SET del_flag = 1 WHERE id IN
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.gok.pboot.pms.entity.domain.ProjectRisk">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="project_id" property="projectId"/>
        <result column="description" property="description"/>
        <result column="probability" property="probability"/>
        <result column="influence_degree" property="influenceDegree"/>
        <result column="level" property="level"/>
        <result column="response_plan" property="responsePlan"/>
        <result column="charge_user_id" property="chargeUserId"/>
        <result column="charge_user" property="chargeUser"/>
        <result column="status" property="status"/>
        <result column="creator" property="creator"/>
        <result column="creator_id" property="creatorId"/>
        <result column="modifier" property="modifier"/>
        <result column="modifier_id" property="modifierId"/>
        <result column="ctime" property="ctime"/>
        <result column="mtime" property="mtime"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

    <!-- 分页查询映射结果 -->
    <resultMap id="BaseResultMapProjectRiskFindPageVo" type="com.gok.pboot.pms.entity.vo.ProjectRiskFindPageVO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="project_id" property="projectId"/>
        <result column="description" property="description"/>
        <result column="probability" property="probability"/>
        <result column="influence_degree" property="influenceDegree"/>
        <result column="level" property="level"/>
        <result column="response_plan" property="responsePlan"/>
        <result column="charge_user_id" property="chargeUserId"/>
        <result column="charge_user" property="chargeUser"/>
        <result column="status" property="status"/>
    </resultMap>

</mapper>