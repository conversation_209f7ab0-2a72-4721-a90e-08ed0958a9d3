<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.cost.mapper.CostSalaryDetailsMapper">
    <select id="getList" resultType="com.gok.pboot.pms.cost.entity.domain.CostSalaryDetails">
        select * from cost_salary_details
        <where>
            del_flag = 0
            and
            <foreach collection="list" item="item" separator="or" open="(" close=")">
                (relate_id = #{item.relateId} and relate_type = #{item.relateType})
            </foreach>
        </where>
    </select>
</mapper>