<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.mapper.CustomerInfoMapper">

    <!--获取客户沟通记录-分页-->
    <select id="findList" resultType="com.gok.pboot.pms.entity.vo.CustomerListVO">
        SELECT
        c.id,
        c.khmc,
        c.khfj,
        c.khszd,
        t.cityname As khszdTxt,
        c.khxyyj,
        c.gsywbmejbm,
        c.khjl,
        c.khjlxm,
        c.ctime,
        u.manager_ids
        FROM
        customer_info  c
        left join  oa_hrmcity t  on  c.khszd=t.id
        LEFT JOIN (
            SELECT
                cbu.business_id,
                cbu.id,
                cbp.manager_ids
                FROM
                customer_business_unit cbu
                LEFT JOIN (
                   SELECT business_id, GROUP_CONCAT( manager_id SEPARATOR ',' ) AS manager_ids
                    FROM customer_business_person where del_flag=0 GROUP BY business_id
                    )  cbp
                ON cbu.business_id = cbp.business_id
                where cbu.del_flag=0
              ) u ON u.id = c.id
        WHERE 1 = 1
        <if test="filter.khmc != null and filter.khmc != ''">
            AND c.khmc LIKE CONCAT('%', #{filter.khmc}, '%')
        </if>
        <if test="filter.khjlxm != null and filter.khjlxm != ''">
            AND c.khjlxm LIKE CONCAT('%',  #{filter.khjlxm}, '%')
        </if>
        <if test="filter.khfjList != null and filter.khfjList.size() > 0">
            AND c.khfj IN
            <foreach collection="filter.khfjList" item="itemId" open="(" separator="," close=")">
                #{itemId}
            </foreach>
        </if>
        <if test="filter.khszdList != null and filter.khszdList.size() > 0">
            AND c.khszd IN
            <foreach collection="filter.khszdList" item="itemId" open="(" separator="," close=")">
                #{itemId}
            </foreach>
        </if>
        <if test="filter.khxyyjList != null and filter.khxyyjList.size() > 0">
            AND c.khxyyj IN
            <foreach collection="filter.khxyyjList" item="itemId" open="(" separator="," close=")">
                #{itemId}
            </foreach>
        </if>
        <if test="filter.gsywbmejbmList != null and filter.gsywbmejbmList.size() > 0">
            AND c.gsywbmejbm IN
            <foreach collection="filter.gsywbmejbmList" item="itemId" open="(" separator="," close=")">
                #{itemId}
            </foreach>
        </if>
         order by c.ctime desc
    </select>

    <!--获取客户沟通记录-分页-->
    <select id="findAttentionList" resultType="com.gok.pboot.pms.entity.vo.CustomerListVO">
        SELECT
        c.id,
        c.khmc,
        c.khfj,
        c.khszd,
        t.cityname As khszdTxt,
        c.khxyyj,
        c.gsywbmejbm,
        c.khjl,
        c.khjlxm,
        c.ctime,
        u.manager_ids
        FROM
        customer_attention ca
        left join   customer_info  c on  ca.customer_id=c.id
        left join  oa_hrmcity t  on  c.khszd=t.id
        LEFT JOIN (
            SELECT
            cbu.business_id,
            cbu.id,
            cbp.manager_ids
            FROM
            customer_business_unit cbu
            LEFT JOIN (
            SELECT business_id, GROUP_CONCAT( manager_id SEPARATOR ',' ) AS manager_ids
            FROM customer_business_person where del_flag=0 GROUP BY business_id
            )  cbp
            ON cbu.business_id = cbp.business_id
             where cbu.del_flag=0
        ) u ON u.id = c.id
        WHERE ca.userid = #{filter.userId}
        <if test="filter.khmc != null and filter.khmc != ''">
            AND c.khmc LIKE CONCAT('%', #{filter.khmc}, '%')
        </if>
        <if test="filter.khjlxm != null and filter.khjlxm != ''">
            AND c.khjlxm LIKE CONCAT('%',  #{filter.khjlxm}, '%')
        </if>
        <if test="filter.khfjList != null and filter.khfjList.size() > 0">
            AND c.khfj IN
            <foreach collection="filter.khfjList" item="itemId" open="(" separator="," close=")">
                #{itemId}
            </foreach>
        </if>
        <if test="filter.khszdList != null and filter.khszdList.size() > 0">
            AND c.khszd IN
            <foreach collection="filter.khszdList" item="itemId" open="(" separator="," close=")">
                #{itemId}
            </foreach>
        </if>
        <if test="filter.khxyyjList != null and filter.khxyyjList.size() > 0">
            AND c.khxyyj IN
            <foreach collection="filter.khxyyjList" item="itemId" open="(" separator="," close=")">
                #{itemId}
            </foreach>
        </if>
        <if test="filter.gsywbmejbmList != null and filter.gsywbmejbmList.size() > 0">
            AND c.gsywbmejbm IN
            <foreach collection="filter.gsywbmejbmList" item="itemId" open="(" separator="," close=")">
                #{itemId}
            </foreach>
        </if>
        order by c.ctime desc
    </select>
    <select id="findCommonPage" resultType="com.gok.pboot.pms.entity.vo.CustomerCommonVO">
        SELECT
        c.id,
        c.khmc,
        c.khfj,
        c.khszd,
        t.cityname As khszdTxt,
        c.khxyyj,
        c.gsywbmejbm,
        c.khjl,
        c.khjlxm,
        c.ctime
        FROM
        customer_info  c
        left join  oa_hrmcity t  on  c.khszd=t.id
        WHERE 1 = 1
        <if test="filter.khmc != null and filter.khmc != ''">
            AND c.khmc LIKE CONCAT('%', #{filter.khmc}, '%')
        </if>
        <if test="filter.khjlxm != null and filter.khjlxm != ''">
            AND c.khjlxm LIKE CONCAT('%',  #{filter.khjlxm}, '%')
        </if>
        <if test="filter.khfjList != null and filter.khfjList.size() > 0">
            AND c.khfj IN
            <foreach collection="filter.khfjList" item="itemId" open="(" separator="," close=")">
                #{itemId}
            </foreach>
        </if>
        order by c.ctime desc
    </select>
    <select id="getCityListByName" resultType="com.gok.pboot.pms.entity.vo.OaHrmcityVO">
        SELECT t.id, t.cityname
        from (SELECT DISTINCT c.khszd
              FROM customer_info c
              where c.khszd is not null
              ) ct
       left join oa_hrmcity t on ct.khszd = t.id
        WHERE 1 = 1
        <if test="name != null and name != ''">
            AND t.cityname LIKE CONCAT('%', #{name}, '%')
        </if>
        ORDER BY t.id
        limit 50
    </select>
</mapper>
