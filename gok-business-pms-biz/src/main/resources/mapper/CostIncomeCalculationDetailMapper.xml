<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.cost.mapper.CostIncomeCalculationDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.gok.pboot.pms.cost.entity.domain.CostIncomeCalculationDetail">
        <id column="id" property="id"/>
        <result column="project_id" property="projectId"/>
        <result column="calculation_id" property="calculationId"/>
        <result column="member_id" property="memberId"/>
        <result column="member_name" property="memberName"/>
        <result column="work_code" property="workCode"/>
        <result column="personnel_attribute" property="personnelAttribute"/>
        <result column="job_id" property="jobId"/>
        <result column="job_name" property="jobName"/>
        <result column="belong_month" property="belongMonth"/>
        <result column="settlement_hours" property="settlementHours"/>
        <result column="settlement_unit_price" property="settlementUnitPrice"/>
        <result column="customer_bearing_amount" property="customerBearingAmount"/>
        <result column="estimated_inclusive_amount_tax" property="estimatedInclusiveAmountTax"/>
        <result column="confirm_status" property="confirmStatus"/>
        <result column="confirm_date" property="confirmDate"/>
        <result column="settlement_status" property="settlementStatus"/>
        <result column="quoted_rate_id" property="quotedRateId"/>
        <result column="ctime" property="ctime"/>
        <result column="mtime" property="mtime"/>
        <result column="creator" property="creator"/>
        <result column="creator_id" property="creatorId"/>
        <result column="modifier" property="modifier"/>
        <result column="modifier_id" property="modifierId"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

    <select id="findList" resultMap="BaseResultMap">
        SELECT
        id,
        project_id,
        calculation_id,
        member_id,
        member_name,
        work_code,
        personnel_attribute,
        job_id,
        job_name,
        belong_month,
        settlement_hours,
        settlement_unit_price,
        quoted_rate_id,
        customer_bearing_amount,
        estimated_inclusive_amount_tax,
        confirm_status,
        confirm_date,
        settlement_status
        FROM
        cost_income_calculation_detail
        WHERE
         del_flag = ${@<EMAIL>()}
        <if test="query.projectIds != null and query.projectIds.size() > 0">
            AND project_id IN
            <foreach collection="query.projectIds" item="pId" open="(" separator="," close=")">
                #{pId}
            </foreach>
        </if>
        <if test="query.query != null and query.query != ''">
            <bind name="queryLike" value="'%'+query.query+'%'"/>
            AND ( member_name LIKE #{queryLike}
            OR work_code LIKE #{queryLike}
                <if test="query.ids != null and query.ids.size() > 0">
                    OR id IN
                    <foreach collection="query.ids" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
            )
        </if>
        <if test="query.startBelongMonth != null and query.endBelongMonth != null">
            AND belong_month <![CDATA[ >= ]]>  #{query.startBelongMonth} AND belong_month <![CDATA[ <= ]]>
            #{query.endBelongMonth}
        </if>
        <if test="query.confirmStatus != null">
            AND confirm_status = #{query.confirmStatus}
        </if>
        <if test="query.calculationIds != null and query.calculationIds.size() > 0">
            AND calculation_id IN
            <foreach collection="query.calculationIds" item="cId" open="(" separator="," close=")">
                #{cId}
            </foreach>
        </if>
        <if test="query.ids != null and query.ids.size() > 0">
            AND id IN
            <foreach collection="query.ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="query.purviewWorkCodes != null and query.purviewWorkCodes.size() > 0">
            AND work_code IN
            <foreach collection="query.purviewWorkCodes" item="purviewWorkCode" open="(" separator="," close=")">
                #{purviewWorkCode}
            </foreach>
        </if>
        ORDER BY
            belong_month DESC,
            confirm_date DESC
    </select>

    <insert id="batchSave">
        INSERT INTO cost_income_calculation_detail (
        id,
        project_id,
        calculation_id,
        member_id,
        member_name,
        work_code,
        personnel_attribute,
        job_id,
        job_name,
        belong_month,
        settlement_hours,
        quoted_rate_id,
        settlement_unit_price,
        customer_bearing_amount,
        estimated_inclusive_amount_tax,
        confirm_status,
        confirm_date,
        settlement_status,
        creator,
        creator_id,
        modifier,
        modifier_id,
        ctime,
        mtime
        ) VALUES
        <foreach collection="saveEntries" item="item" separator=",">
            (
            #{item.id},
            #{item.projectId},
            #{item.calculationId},
            #{item.memberId},
            #{item.memberName},
            #{item.workCode},
            #{item.personnelAttribute},
            #{item.jobId},
            #{item.jobName},
            #{item.belongMonth},
            #{item.settlementHours},
            #{item.quotedRateId},
            #{item.settlementUnitPrice},
            #{item.customerBearingAmount},
            #{item.estimatedInclusiveAmountTax},
            #{item.confirmStatus},
            #{item.confirmDate},
            #{item.settlementStatus},
            #{item.creator},
            #{item.creatorId},
            #{item.modifier},
            #{item.modifierId},
            #{item.ctime},
            #{item.mtime}
            )
        </foreach>
    </insert>

    <insert id="batchUpdate">
        <foreach collection="updateEntries" item="item" separator=";">
            UPDATE cost_income_calculation_detail SET
            project_id = #{item.projectId},
            calculation_id = #{item.calculationId},
            member_id = #{item.memberId},
            member_name = #{item.memberName},
            work_code = #{item.workCode},
            personnel_attribute = #{item.personnelAttribute},
            job_id = #{item.jobId},
            job_name = #{item.jobName},
            belong_month = #{item.belongMonth},
            settlement_hours = #{item.settlementHours},
            quoted_rate_id = #{item.quotedRateId},
            settlement_unit_price = #{item.settlementUnitPrice},
            customer_bearing_amount = #{item.customerBearingAmount},
            estimated_inclusive_amount_tax = #{item.estimatedInclusiveAmountTax},
            confirm_status = #{item.confirmStatus},
            confirm_date = #{item.confirmDate},
            settlement_status = #{item.settlementStatus},
            modifier = #{item.modifier},
            modifier_id = #{item.modifierId},
            mtime = #{item.mtime}
            where id = #{item.id}
        </foreach>
    </insert>

    <update id="batchConfirmByCalculation">
        UPDATE cost_income_calculation_detail
        SET confirm_status = #{confirmStatus},
        confirm_date = #{confirmDate},
        modifier = #{modifier},
        modifier_id = #{modifierId},
        mtime = now()
        WHERE calculation_id IN
        <foreach collection="calculationIds" item="calculationId" open="(" separator="," close=")">
            #{calculationId}
        </foreach>
        <if test="confirmDate != null and confirmDate != ''">
            AND confirm_status != ${@<EMAIL>()}
        </if>
        <if test="confirmDate == null or confirmDate == ''">
            AND confirm_status = ${@<EMAIL>()}
        </if>
        AND settlement_status = ${@com.gok.pboot.pms.cost.enums.SettlementStatusEnum@AWAIT_SETTLEMENT.getValue()}
    </update>

</mapper>
