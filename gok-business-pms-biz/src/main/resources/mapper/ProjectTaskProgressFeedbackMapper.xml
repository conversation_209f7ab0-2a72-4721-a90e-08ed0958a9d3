<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.mapper.ProjectTaskProgressFeedbackMapper">

    <sql id="baseColumnList">
        a.id,
        a.progress_id,
        a.user_id,
        a.user_avatar_url,
        a.user_name,
        a.content,
        a.ctime,
        a.mtime,
        a.creator,
        a.creator_id,
        a.modifier,
        a.modifier_id,
        a.del_flag
    </sql>

    <select id="findByProgressIds" resultType="com.gok.pboot.pms.entity.domain.ProjectTaskProgressFeedback">
        SELECT
            <include refid="baseColumnList"/>
        FROM project_task_progress_feedback a
        WHERE
            del_flag = 0
        AND progress_id IN
        <foreach collection="progressIds" item="pId" open="(" separator="," close=")">
            #{pId}
        </foreach>
    </select>

    <select id="existsByProgressId" resultType="java.lang.Boolean">
        SELECT EXISTS(
            SELECT id
            FROM project_task_progress_feedback
            WHERE
                del_flag = 0
            AND
                progress_id = #{progressId}
        )
    </select>

    <select id="countByProgressId" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM project_task_progress_feedback a
        WHERE
            del_flag = 0
        AND
            progress_id = #{progressId}
        FOR UPDATE
    </select>
</mapper>