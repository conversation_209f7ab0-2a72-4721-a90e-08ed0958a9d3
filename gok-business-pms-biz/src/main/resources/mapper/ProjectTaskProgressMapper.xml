<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.mapper.ProjectTaskProgressMapper">

    <resultMap id="projectTaskMap" type="com.gok.pboot.pms.entity.domain.ProjectTaskProgress">
        <id property="id" column="id"/>
        <result property="taskId" column="task_id"/>
        <result property="projectId" column="project_id"/>
        <result property="userId" column="user_id"/>
        <result property="userAvatarUrl" column="user_avatar_url"/>
        <result property="userName" column="user_name"/>
        <result property="progress" column="progress"/>
        <result property="content" column="content"/>
        <result property="creator" column="creator"/>
        <result property="creatorId" column="creator_id"/>
        <result property="modifier" column="modifier"/>
        <result property="modifierId" column="modifier_id"/>
        <result property="ctime" column="ctime"/>
        <result property="mtime" column="mtime"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <sql id="BaseColumns">
        a.id,
        a.task_id,
        a.project_id,
        a.user_id,
        a.user_avatar_url,
        a.user_name,
        a.progress,
        a.content,
        a.creator,
        a.creator_id,
        a.modifier,
        a.modifier_id,
        a.ctime,
        a.mtime,
        a.del_flag
    </sql>

    <select id="findList" resultMap="projectTaskMap">
        select <include refid="BaseColumns"/>
        from project_task_progress a
        <where>
            del_flag = 0
        </where>
    </select>

    <select id="getAllProgress" resultMap="projectTaskMap">
        select
        a.id,
        a.project_id,
        a.progress,
        a.task_id,
        a.ctime
        from project_task_progress a
        <where>
            del_flag = 0
            <if test="filter.projectId != null">
                AND project_id = #{filter.projectId}
            </if>
        </where>
    </select>

    <select id="findByTaskId" resultType="com.gok.pboot.pms.entity.domain.ProjectTaskProgress">
        SELECT
            <include refid="BaseColumns"/>
        FROM project_task_progress a
        WHERE
            del_flag = 0
        AND
            task_id = #{taskId}
    </select>

    <select id="existsById" resultType="java.lang.Boolean">
        SELECT EXISTS(
            SELECT id
            FROM project_task_progress
            WHERE
                del_flag = 0
            AND
                id = #{id}
        )
    </select>
</mapper>