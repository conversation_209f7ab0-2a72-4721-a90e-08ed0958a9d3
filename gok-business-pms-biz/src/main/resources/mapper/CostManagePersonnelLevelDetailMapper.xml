<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.cost.mapper.CostManagePersonnelLevelDetailMapper">



    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.gok.pboot.pms.cost.entity.domain.CostManagePersonnelLevelDetail">
        <id column="id" property="id" />
        <result column="version_id" property="versionId" />
        <result column="estimation_results_id" property="estimationResultsId" />
        <result column="account_id" property="accountId" />
        <result column="account_name" property="accountName" />
        <result column="level_config_id" property="levelConfigId" />
        <result column="personnel_type" property="personnelType" />
        <result column="job_activity_id" property="jobActivityId" />
        <result column="region" property="region" />
        <result column="personnel_level" property="personnelLevel" />
        <result column="personnel_price" property="personnelPrice" />
        <result column="personnel_num" property="personnelNum" />
        <result column="expected_person_day" property="expectedPersonDay" />
        <result column="business_trip_flag" property="businessTripFlag" />
        <result column="business_trip_day" property="businessTripDay" />
        <result column="business_trip_city_id" property="businessTripCityId" />
        <result column="single_person_flag" property="singlePersonFlag" />
        <result column="self_stay_flag" property="selfStayFlag" />
        <result column="travel_stay_config_id" property="travelStayConfigId" />
        <result column="travel_stay_type" property="travelStayType" />
        <result column="travel_subsidy_config_id" property="travelSubsidyConfigId" />
        <result column="level_price_cost" property="levelPriceCost" />
        <result column="estimated_stay_cost" property="estimatedStayCost" />
        <result column="travel_subsidy_cost" property="travelSubsidyCost" />
        <result column="custom_subsidy_cost" property="customSubsidyCost" />
        <result column="remark" property="remark" />
        <result column="creator" property="creator" />
        <result column="creator_id" property="creatorId" />
        <result column="modifier" property="modifier" />
        <result column="modifier_id" property="modifierId" />
        <result column="ctime" property="ctime" />
        <result column="mtime" property="mtime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, version_id, estimation_results_id, account_id, account_name, level_config_id, personnel_type, job_activity_id,
        region, personnel_level, personnel_price, personnel_num, expected_person_day, business_trip_flag, business_trip_day,
        business_trip_city_id, single_person_flag, self_stay_flag, travel_stay_config_id,travel_stay_type,
        travel_subsidy_config_id, level_price_cost, estimated_stay_cost, travel_subsidy_cost, custom_subsidy_cost,
        remark, creator, creator_id, modifier, modifier_id, ctime, mtime, del_flag
    </sql>

    <insert id="batchSave">
        INSERT INTO cost_manage_personnel_level_detail (
        id,
        version_id,
        estimation_results_id,
        account_id,
        account_name,
        level_config_id,
        personnel_type,
        job_activity_id,
        region,
        personnel_level,
        personnel_price,
        personnel_num,
        expected_person_day,
        business_trip_flag,
        business_trip_day,
        business_trip_city_id,
        single_person_flag,
        self_stay_flag,
        travel_stay_config_id,
        travel_stay_type,
        travel_subsidy_config_id,
        level_price_cost,
        estimated_stay_cost,
        travel_subsidy_cost,
        custom_subsidy_cost,
        remark,
        creator,
        creator_id,
        modifier,
        modifier_id,
        ctime,
        mtime,
        del_flag
        ) VALUES
        <foreach collection="saveEntries" item="item" separator=",">
            (
            #{item.id},
            #{item.versionId},
            #{item.estimationResultsId},
            #{item.accountId},
            #{item.accountName},
            #{item.levelConfigId},
            #{item.personnelType},
            #{item.jobActivityId},
            #{item.region},
            #{item.personnelLevel},
            #{item.personnelPrice},
            #{item.personnelNum},
            #{item.expectedPersonDay},
            #{item.businessTripFlag},
            #{item.businessTripDay},
            #{item.businessTripCityId},
            #{item.singlePersonFlag},
            #{item.selfStayFlag},
            #{item.travelStayConfigId},
            #{item.travelStayType},
            #{item.travelSubsidyConfigId},
            #{item.levelPriceCost},
            #{item.estimatedStayCost},
            #{item.travelSubsidyCost},
            #{item.customSubsidyCost},
            #{item.remark},
            #{item.creator},
            #{item.creatorId},
            #{item.modifier},
            #{item.modifierId},
            #{item.ctime},
            #{item.mtime},
            #{item.delFlag}
            )
        </foreach>
    </insert>

    <select id="findByEstimateResultIdList"
            resultType="com.gok.pboot.pms.cost.entity.vo.CostManagePersonnelLevelDetailVO">
        SELECT
            *
        FROM
            cost_manage_personnel_level_detail
        WHERE
            estimation_results_id IN
            <foreach collection="estimateResultIdList" item="estimateResultId" index="index" open="(" close=")" separator=",">
                #{estimateResultId}
            </foreach>
            AND del_flag = ${@<EMAIL>()}
    </select>

</mapper>
