<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.mapper.SpecialSettingMapper">
    <sql id="baseColumn">
        a.id,
        a.user_id,
        a.top_dept_id,
        a.creator,
        a.creator_id,
        a.modifier,
        a.modifier_id,
        a.ctime,
        a.mtime,
        a.del_flag
    </sql>

    <insert id="batchSave">
        INSERT INTO mhour_special_setting(
            id,
            user_id,
            top_dept_id,
            creator,
            creator_id,
            modifier,
            modifier_id,
            ctime,
            mtime,
            del_flag
        ) VALUES
        <foreach collection="poList" item="po" separator=",">
            (
             #{po.id},
             #{po.userId},
             #{po.topDeptId},
             #{po.creator},
             #{po.creatorId},
             #{po.modifier},
             #{po.modifierId},
             #{po.ctime},
             #{po.mtime},
             #{po.delFlag}
            )
        </foreach>
    </insert>

    <update id="batchDelete">
        UPDATE mhour_special_setting SET del_flag = 1
        WHERE id IN
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND del_flag = 0
    </update>

    <select id="findPage" resultType="com.gok.pboot.pms.entity.SpecialSetting">
        SELECT
            <include refid="baseColumn"/>
        FROM mhour_special_setting a
        LEFT JOIN mhour_roster mr ON a.user_id = mr.id
        WHERE a.del_flag = 0
        <if test="filter.topDeptId != null">
            AND top_dept_id = #{filter.topDeptId}
        </if>
        <if test="filter.deptList != null and filter.deptList.size>0">
            AND top_dept_id IN
            <foreach collection="filter.deptList" item="pId" open="(" separator="," close=")">
                #{pId}
            </foreach>
        </if>
        <if test="filter.userRealName != null and filter.userRealName != ''">
            AND mr.alias_name like concat('%', #{filter.userRealName}, '%')
        </if>
        order by a.ctime desc
    </select>

    <select id="countByUserIds" resultType="java.lang.Integer">
        SELECT COUNT(id) > 0
        FROM mhour_special_setting
        WHERE
            del_flag = 0
        AND user_id IN
        <foreach collection="list" item="uId" open="(" separator="," close=")">
            #{uId}
        </foreach>
        LIMIT 1
    </select>

    <select id="findUserList" resultType="java.lang.Long">
        SELECT
            a.user_id
        FROM mhour_special_setting a
        WHERE
            del_flag = 0
        <if test="filter.topDeptId != null">
            AND top_dept_id = #{filter.topDeptId}
        </if>
        <if test="filter.deptList != null and filter.deptList.size>0">
            AND top_dept_id IN
            <foreach collection="filter.deptList" item="pId" open="(" separator="," close=")">
                #{pId}
            </foreach>
        </if>
        order by a.ctime desc
    </select>

    <select id="findUserIds" resultType="java.lang.Long">
        SELECT user_id
        FROM mhour_special_setting
        WHERE
            del_flag = 0
    </select>

</mapper>