<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.eval.mapper.EvalProjectOverallDistributionMapper">
  <resultMap id="BaseResultMap" type="com.gok.pboot.pms.eval.entity.domain.EvalProjectOverallDistribution">
    <!--@mbg.generated-->
    <!--@Table eval_project_overall_distribution-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_overall_grade" jdbcType="VARCHAR" property="projectOverallGrade" />
    <result column="overall_starting_score" jdbcType="DECIMAL" property="overallStartingScore" />
    <result column="overall_end_score" jdbcType="DECIMAL" property="overallEndScore" />
    <result column="eval_grade" jdbcType="VARCHAR" property="evalGrade" />
    <result column="starting_range" jdbcType="DECIMAL" property="startingRange" />
    <result column="end_range" jdbcType="DECIMAL" property="endRange" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="creator_id" jdbcType="BIGINT" property="creatorId" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="modifier_id" jdbcType="BIGINT" property="modifierId" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, project_overall_grade, overall_starting_score, overall_end_score, eval_grade, 
    starting_range, end_range, creator, creator_id, modifier, modifier_id, ctime, mtime, 
    del_flag
  </sql>
</mapper>