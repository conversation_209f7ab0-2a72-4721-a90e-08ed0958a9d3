<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.mapper.SalesReceiptDetailsMapper">

    <resultMap id="BaseResultMap" type="com.gok.pboot.pms.entity.domain.SalesReceiptDetails">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="mainId" column="main_id" jdbcType="BIGINT"/>
            <result property="paymentName" column="payment_name" jdbcType="VARCHAR"/>
            <result property="proportionFunds" column="proportion_funds" jdbcType="VARCHAR"/>
            <result property="fundsAmount" column="funds_amount" jdbcType="DECIMAL"/>
            <result property="expectedDate" column="expected_date" jdbcType="VARCHAR"/>
            <result property="actualDate" column="actual_date" jdbcType="VARCHAR"/>
            <result property="paymentAmountIncludingTax" column="payment_amount_including_tax" jdbcType="DECIMAL"/>
            <result property="taxRate" column="tax_rate" jdbcType="INTEGER"/>
            <result property="paymentAmount" column="payment_amount" jdbcType="DECIMAL"/>
            <result property="projectSchedule" column="project_schedule" jdbcType="VARCHAR"/>
            <result property="paymentStatus" column="payment_status" jdbcType="INTEGER"/>
            <result property="paymentRemark" column="payment_remark" jdbcType="VARCHAR"/>
            <result property="invoiceStatus" column="Invoice_status" jdbcType="INTEGER"/>
            <result property="actualInvoicingDate" column="actual_invoicing_date" jdbcType="VARCHAR"/>
            <result property="remarks" column="remarks" jdbcType="VARCHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
    </resultMap>
    <select id="selectByMainId" resultType="com.gok.pboot.pms.entity.domain.SalesReceiptDetails">
            SELECT *
            FROM sales_receipt_details
            WHERE main_id = #{mainId}
            AND del_flag = 0
    </select>

</mapper>
