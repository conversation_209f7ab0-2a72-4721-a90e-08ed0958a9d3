<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.gok.pboot.pms.mapper.ContractLedgerMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.gok.pboot.pms.entity.domain.ContractLedger" id="contractLedgerMap">
        <result property="id" column="id"/>
        <result property="requestid" column="REQUESTID"/>
        <result property="htlb" column="HTLB"/>
        <result property="htxl" column="HTXL"/>
        <result property="relateprocess" column="RELATEPROCESS"/>
        <result property="remark" column="REMARK"/>
        <result property="htmc" column="HTMC"/>
        <result property="sjhtbh" column="SJHTBH"/>
        <result property="htssgs" column="HTSSGS"/>
        <result property="htssbm" column="HTSSBM"/>
        <result property="htssbmName" column="HTSSBM_NAME"/>
        <result property="htssfb" column="HTSSFB"/>
        <result property="htssfbName" column="HTSSFB_NAME"/>
        <result property="htdj" column="HTDJ"/>
        <result property="khmc" column="KHMC"/>
        <result property="khmcName" column="KHMC_NAME"/>
        <result property="khbh" column="KHBH"/>
        <result property="lxr" column="LXR"/>
        <result property="lxfs" column="LXFS"/>
        <result property="lxdz" column="LXDZ"/>
        <result property="khfkhyx" column="KHFKHYX"/>
        <result property="khfzh" column="KHFZH"/>
        <result property="khfnsrdjh" column="KHFNSRDJH"/>
        <result property="relatedoc" column="RELATEDOC"/>
        <result property="relateattachment" column="RELATEATTACHMENT"/>
        <result property="relateattachmentImagefileid" column="RELATEATTACHMENT_IMAGEFILEID"/>
        <result property="relateattachmentImagefilename" column="RELATEATTACHMENT_IMAGEFILENAME"/>
        <result property="ywfx" column="YWFX"/>
        <result property="xmmc" column="XMMC"/>
        <result property="xmmcName" column="XMMC_NAME"/>
        <result property="ywlx" column="YWLX"/>
        <result property="htje" column="HTJE"/>
        <result property="fkfs" column="FKFS"/>
        <result property="xmxsry" column="XMXSRY"/>
        <result property="xmxsryName" column="XMXSRY_NAME"/>
        <result property="htqsrq" column="HTQSRQ"/>
        <result property="htjzrq" column="HTJZRQ"/>
        <result property="htfj" column="HTFJ"/>
        <result property="htfjImagefileid" column="HTFJ_IMAGEFILEID"/>
        <result property="htfjImagefilename" column="HTFJ_IMAGEFILENAME"/>
        <result property="htfjygz" column="HTFJYGZ"/>
        <result property="htfjygzImagefileid" column="HTFJYGZ_IMAGEFILEID"/>
        <result property="htfjygzImagefilename" column="HTFJYGZ_IMAGEFILENAME"/>
        <result property="htbh" column="HTBH"/>
        <result property="hthqlc" column="HTHQLC"/>
        <result property="hthqlcNodeoperator" column="HTHQLC_NODEOPERATOR"/>
        <result property="htzt" column="HTZT"/>
        <result property="ysbg" column="YSBG"/>
        <result property="ysbgImagefileid" column="YSBG_IMAGEFILEID"/>
        <result property="ysbgImagefilename" column="YSBG_IMAGEFILENAME"/>
        <result property="gljxlc" column="GLJXLC"/>
        <result property="gljxlcNodeoperator" column="GLJXLC_NODEOPERATOR"/>
        <result property="jxzje" column="JXZJE"/>
        <result property="jxzcje" column="JXZCJE"/>
        <result property="yskje" column="YSKJE"/>
        <result property="ykpje" column="YKPJE"/>
        <result property="qygs" column="QYGS"/>
        <result property="gsqy" column="GSQY"/>
        <result property="lxrdh" column="LXRDH"/>
        <result property="xmxsry2" column="XMXSRY2"/>
        <result property="xmxsry2Name" column="XMXSRY2_NAME"/>
        <result property="zbtzs" column="ZBTZS"/>
        <result property="zbtzsfj" column="ZBTZSFJ"/>
        <result property="zbtzsfjImagefileid" column="ZBTZSFJ_IMAGEFILEID"/>
        <result property="zbtzsfjImagefilename" column="ZBTZSFJ_IMAGEFILENAME"/>
        <result property="ysbga" column="YSBGA"/>
        <result property="ysrq" column="YSRQ"/>
        <result property="zbjshqk" column="ZBJSHQK"/>
        <result property="zbjshrq" column="ZBJSHRQ"/>
        <result property="zbdqk" column="ZBDQK"/>
        <result property="zbjje" column="ZBJJE"/>
        <result property="zbjdqsj" column="ZBJDQSJ"/>
        <result property="htlx" column="HTLX"/>
        <result property="xmmlcslc" column="XMMLCSLC"/>
        <result property="xmmlcslcNodeoperator" column="XMMLCSLC_NODEOPERATOR"/>
        <result property="xmfzr" column="XMFZR"/>
        <result property="xmfzrName" column="XMFZR_NAME"/>
        <result property="xmjl" column="XMJL"/>
        <result property="xmjlName" column="XMJL_NAME"/>
        <result property="sjhtqdrq" column="SJHTQDRQ"/>
        <result property="yjshul" column="YJSHUL"/>
        <result property="xmjxsqlc" column="XMJXSQLC"/>
        <result property="xmjxsqlcNodeoperator" column="XMJXSQLC_NODEOPERATOR"/>
        <result property="xmjxlc" column="XMJXLC"/>
        <result property="xmsbqsdfj" column="XMSBQSDFJ"/>
        <result property="xmsbqsdfjImagefileid" column="XMSBQSDFJ_IMAGEFILEID"/>
        <result property="xmsbqsdfjImagefilename" column="XMSBQSDFJ_IMAGEFILENAME"/>
        <result property="sfsjyjcg" column="SFSJYJCG"/>
        <result property="bcjczzxyfj" column="BCJCZZXYFJ"/>
        <result property="bcjczzxyfjImagefileid" column="BCJCZZXYFJ_IMAGEFILEID"/>
        <result property="bcjczzxyfjImagefilename" column="BCJCZZXYFJ_IMAGEFILENAME"/>
        <result property="bgxyqdrq" column="BGXYQDRQ"/>
        <result property="bgxyyjsl" column="BGXYYJSL"/>
        <result property="htjehs" column="HTJEHS"/>
        <result property="htjebhs" column="HTJEBHS"/>
        <result property="xmyjml" column="XMYJML"/>
        <result property="xmyjmll" column="XMYJMLL"/>
        <result property="xmbh" column="XMBH"/>
        <result property="htssejbm" column="HTSSEJBM"/>
        <result property="htssejbmName" column="HTSSEJBM_NAME"/>
        <result property="zbtzssflq" column="ZBTZSSFLQ"/>
        <result property="zbtzssmj" column="ZBTZSSMJ"/>
        <result property="zbtzssmjImagefileid" column="ZBTZSSMJ_IMAGEFILEID"/>
        <result property="zbtzssmjImagefilename" column="ZBTZSSMJ_IMAGEFILENAME"/>
        <result property="jsfs" column="JSFS"/>
        <result property="khmcnew" column="KHMCNEW"/>
        <result property="khmcnewName" column="KHMCNEW_NAME"/>
        <result property="gysmc" column="GYSMC"/>
        <result property="gysmcName" column="GYSMC_NAME"/>
        <result property="gysbh" column="GYSBH"/>
        <result property="sfdfxy" column="SFDFXY"/>
        <result property="srlx" column="SRLX"/>
        <result property="jslx" column="JSLX"/>
        <result property="jfxs" column="JFXS"/>
        <result property="ywbk" column="YWBK"/>
        <result property="xmlx" column="XMLX"/>
        <result property="dskje" column="DSKJE"/>
        <result property="ykpbl" column="YKPBL"/>
        <result property="dkpje" column="DKPJE"/>
        <result property="yskbl" column="YSKBL"/>
        <result property="hksqdlc" column="HKSQDLC"/>
        <result property="hksqdlcNodeoperator" column="HKSQDLC_NODEOPERATOR"/>
        <result property="mxid" column="MXID"/>
        <result property="dwcglx" column="DWCGLX"/>
        <result property="xzxmcgsqlc" column="XZXMCGSQLC"/>
        <result property="xzxmcgsqlcNodeoperator" column="XZXMCGSQLC_NODEOPERATOR"/>
        <result property="xmszd" column="XMSZD"/>
        <result property="zzkh" column="ZZKH"/>
        <result property="htbdjfw" column="HTBDJFW"/>
        <result property="jfdd" column="JFDD"/>
        <result property="bzysfs" column="BZYSFS"/>
        <result property="jffwqx" column="JFFWQX"/>
        <result property="ystj" column="YSTJ"/>
        <result property="htbdmxfj" column="HTBDMXFJ"/>
        <result property="htbdmxfjImagefileid" column="HTBDMXFJ_IMAGEFILEID"/>
        <result property="htbdmxfjImagefilename" column="HTBDMXFJ_IMAGEFILENAME"/>
        <result property="zbq" column="ZBQ"/>
        <result property="zbqqksm" column="ZBQQKSM"/>
        <result property="gysmcs" column="GYSMCS"/>
        <result property="gysmcsName" column="GYSMCS_NAME"/>
        <result property="creator" column="creator"/>
        <result property="creatorId" column="creator_id"/>
        <result property="modifier" column="modifier"/>
        <result property="modifierId" column="modifier_id"/>
        <result property="ctime" column="ctime"/>
        <result property="mtime" column="mtime"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <select id="findPage" resultType="com.gok.pboot.pms.entity.vo.ContractListVo">
        SELECT a.id,
               a.htmc,
               a.htbh,
               a.htxl,
               a.htje as htjehs,
               a.htssgs,
               case
               when a.htxl='0' then a.gysmcs
               else  a.khmcnew
               end as khmc,
               case
               when a.htxl='0' then a.gysmcs_name
               else  a.khmcnew_name
               end as khmc_name,
               a.htjebhs,
               a.jsfs,
               a.ywbk,
               a.htlb,
               a.srlx,
               a.jslx,
               a.sjhtqdrq,
               a.htqsrq,
               a.htjzrq,
               a.xmbh,
               a.xmmc_name,
               a.htssbm,
               a.htssbm_name,
               a.htssejbm,
               a.htssejbm_name,
               a.xmxsry,
               a.xmxsry_name,
               a.xmjl,
               a.xmjl_name,
               a.jxqdtj,
               a.ysrq,
               c.bglx
        FROM contract_ledger a
        left join contract_amendment c on a.id = c.llhtmc
        where a.del_flag = '0'
        <if test="dto.htmc != null and dto.htmc != ''">
            AND (htmc LIKE CONCAT('%', #{dto.htmc}, '%')
                    OR a.htbh LIKE CONCAT('%', #{dto.htmc}, '%'))
        </if>
        <if test="dto.xmmc != null and dto.xmmc != ''">
            AND (xmmc_name LIKE CONCAT('%', #{dto.xmmc}, '%')
                    OR a.xmbh LIKE CONCAT('%', #{dto.xmmc}, '%'))
        </if>
        <if test="dto.khjl != null and dto.khjl != ''">
            AND (a.xmxsry_name LIKE CONCAT('%', #{dto.khjl}, '%')
                    OR a.xmjl_name LIKE CONCAT('%', #{dto.khjl}, '%'))
        </if>
        <if test="dto.khmc != null and dto.khmc != ''">
            AND a.khmc_name LIKE CONCAT('%', #{dto.khmc}, '%')
        </if>
        <if test="dto.htxl != null">
            AND a.htxl = #{dto.htxl}
        </if>
        <if test="dto.htssgs != null">
            AND a.htssgs = #{dto.htssgs}
        </if>
        <if test="dto.ywbk != null">
            AND a.ywbk = #{dto.ywbk}
        </if>
        <if test="dto.jslx != null">
            AND a.jslx = #{dto.jslx}
        </if>
        <if test="dto.srlx != null">
            AND a.srlx = #{dto.srlx}
        </if>
        <if test="dto.htqsrq != null">
            AND CAST(a.sjhtqdrq AS DATETIME) &gt;= #{dto.htqsrq}
        </if>
        <if test="dto.htjzrq != null ">
            AND  CAST(a.sjhtqdrq AS DATETIME) &lt;= #{dto.htjzrq}
        </if>
        ORDER BY htqsrq DESC
    </select>



    <select id="selHeadInfoById" resultType="com.gok.pboot.pms.entity.vo.ContractBaseHeadVo">
        SELECT a.id,
               a.htmc,
               a.htbh,
               a.htxl,
               a.htzt,
               a.htssgs,
                case
                when a.htxl='0' then a.gysmcs
                when a.htxl !='0' then a.khmcnew
                end khmc,
                case
                when a.htxl='0' then a.gysmcs_name
                when a.htxl !='0' then a.khmcnew_name
                end khmcName,
               a.htlb,
               a.jslx,
               a.xmbh,
               a.xmmc,
               a.xmmc_name,
               a.xmxsry,
               a.xmxsry_name,
               a.htje,
               a.sjhtqdrq,
               c.bglx,
               a.yskje as ljskje,
               a.yskbl,
               a.xmfzr,
               a.xmjl,
               a.xmxsry2
        FROM contract_ledger a
        left join contract_ledger_detail b on a.id = b.mainid
        left join contract_amendment c on a.id = c.llhtmc
                where a.del_flag = '0'
        <if test="id != null">
            AND a.id = #{id}
        </if>
        GROUP BY b.mainid
</select>

    <select id="selOverviewInfoById" resultType="com.gok.pboot.pms.entity.vo.ContractOverviewInfoVo">
        SELECT a.id,
               a.htmc,
               a.htje,
               a.htqsrq,
               a.htjzrq,
               a.sjhtqdrq,
               a.xmjl,
               a.xmjl_name,
               a.xmlxrq,
               a.yskje,
               replace(a.xmczjd,'%','') as xmczjd
        FROM contract_ledger a
        where a.del_flag = '0'
        <if test="id != null">
            AND a.id = #{id}
        </if>
    </select>


    <sql id="queryParams">
    <if test="dto != null ">
        <if test="dto.htmc != null and dto.htmc != ''">
            AND (htmc LIKE CONCAT('%', #{dto.htmc}, '%')
            OR a.htbh LIKE CONCAT('%', #{dto.htmc}, '%'))
        </if>
        <if test="dto.xmmc != null and dto.xmmc != ''">
            AND (xmmc_name LIKE CONCAT('%', #{dto.xmmc}, '%')
            OR a.xmbh LIKE CONCAT('%', #{dto.xmmc}, '%'))
        </if>
        <if test="dto.khjl != null and dto.khjl != ''">
            AND (a.xmxsry_name LIKE CONCAT('%', #{dto.khjl}, '%')
            OR a.xmjl_name LIKE CONCAT('%', #{dto.khjl}, '%'))
        </if>
        <if test="dto.khmc != null and dto.khmc != ''">
            AND (a.khmcnew_name LIKE CONCAT('%', #{dto.khmc}, '%')
            OR a.gysmcs_name LIKE CONCAT('%', #{dto.khmc}, '%'))
        </if>
        <if test="dto.htzt != null">
            AND a.htzt = #{dto.htzt}
        </if>
        <if test="dto.htssgs != null">
            AND a.htssgs = #{dto.htssgs}
        </if>
        <if test="dto.ywbk != null">
            AND a.ywbk = #{dto.ywbk}
        </if>
        <if test="dto.jslx != null">
            AND a.jslx = #{dto.jslx}
        </if>
        <if test="dto.srlx != null">
            AND a.srlx = #{dto.srlx}
        </if>
        <if test="dto.htqsrq != null">
            AND CAST(a.sjhtqdrq AS DATETIME) &gt;= #{dto.htqsrq}
        </if>
        <if test="dto.htjzrq != null ">
            AND  CAST(a.sjhtqdrq AS DATETIME) &lt;= #{dto.htjzrq}
        </if>
        <if test="dto.deptIds != null and dto.deptIds.size() > 0">
            AND (
            a.htssbm IN
            <foreach collection="dto.deptIds" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
            OR
            a.htssejbm IN
            <foreach collection="dto.deptIds" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
            )
        </if>
        <if test="dto.ids != null and dto.ids.size() > 0">
            AND
            a.id IN
            <foreach collection="dto.ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="dto.htxlIds != null and dto.htxlIds.size() > 0">
            AND
            a.htxl IN
            <foreach collection="dto.htxlIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        group by a.id
      </if>
    </sql>


    <select id="selSummary" resultType="com.gok.pboot.pms.entity.vo.ContractLedgerSummaryVo">
        SELECT
        a.id,
        a.htxl,
        a.htje as htje,
        a.sjhtqdrq,
        a.salesman_user_id,
        a.pre_sale_user_id,
        a.manager_user_id,
        a.xmfzr,
        a.xmjl,
        a.xmxsry,
        a.xmxsry2,
        c.bglx,
        a.htzt,
        a.yskje
        FROM contract_ledger a
        left join contract_amendment c on a.id = c.llhtmc
        where a.del_flag = '0'
        <include refid="queryParams"></include>
    </select>

    <select id="selListCount" resultType="com.gok.pboot.pms.entity.vo.ContractListVo">
        SELECT
        a.xmfzr,
        a.xmjl,
        a.xmxsry,
        a.xmxsry2
        FROM contract_ledger a
        left join contract_amendment c on a.id = c.llhtmc
        where a.del_flag = '0'
        <include refid="queryParams"></include>
    </select>

    <select id="selList" resultType="com.gok.pboot.pms.entity.vo.ContractListVo">
        SELECT a.id,
        a.htmc,
        a.htbh,
        a.htxl,
        a.htje as htjehs,
        a.htssgs,
        case
        when a.htxl=0 then a.gysmcs
        end
        as  gysmcId,
        case
        when a.htxl !=0 then a.khmcnew
        end
        as khmcnewId,
        case
        when a.htxl=0 then a.gysmcs_name
        end
        as gysmcName,
        case
        when a.htxl !=0 then a.khmcnew_name
        end khmcnewName,
        a.htjebhs,
        a.jsfs,
        a.ywbk,
        a.htlb,
        a.jslx,
        a.srlx,
        a.sjhtqdrq,
        a.htqsrq,
        a.htjzrq,
        a.xmbh,
        a.xmmc,
        a.xmmc_name,
        a.htssbm,
        a.htssbm_name,
        a.htssejbm,
        a.htssejbm_name,
        a.xmxsry,
        a.xmxsry_name,
        a.xmjl,
        a.xmjl_name,
        a.jxqdtj,
        a.ysrq,
        a.salesman_user_id,
        a.pre_sale_user_id,
        a.manager_user_id,
        a.xmfzr,
        a.xmxsry2,
        c.bglx,
        a.yskje,
        a.htzt,
        a.yskbl,
        CASE
        WHEN a.htzt = 0 THEN 1
        WHEN a.htzt = 1 THEN 3
        WHEN a.htzt = 2 THEN 2
        WHEN a.htzt = 3 THEN 4
        END AS htzt_order
        FROM contract_ledger a
        left join contract_amendment c on a.id = c.llhtmc
        where a.del_flag = '0'
        <include refid="queryParams"></include>
        ORDER BY htzt_order ASC,
        a.htjzrq ASC,
        a.htqsrq ASC
    </select>

    <select id="selEvalProjectData" resultType="com.gok.pboot.pms.entity.domain.ContractLedger">
        SELECT
            xmmc,
            min( sjhtqdrq ) AS sjhtqdrq
        FROM
            contract_ledger
        WHERE
            del_flag = '0'
        <if test="projectIds != null and projectIds.size() > 0">
            AND XMMC IN
                <foreach collection="projectIds" separator="," item="item" open="(" close=")">
                    #{item}
                </foreach>
        </if>
        GROUP BY
            XMMC
    </select>


</mapper>