<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.mapper.ProjectPaymentClaimMapper">
    <resultMap id="BaseResultMap" type="com.gok.pboot.pms.entity.domain.ProjectPaymentClaim">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="project_payment_id" jdbcType="BIGINT" property="projectPaymentId" />
        <result column="salesman_user_id" jdbcType="BIGINT" property="salesmanUserId" />
        <result column="salesman_user_name" jdbcType="VARCHAR" property="salesmanUserName" />
        <result column="business_line" jdbcType="BOOLEAN" property="businessLine" />
        <result column="project_collection" jdbcType="BOOLEAN" property="projectCollection" />
        <result column="collection_within_budget" jdbcType="BOOLEAN" property="collectionWithinBudget" />
        <result column="contract_payment_id" jdbcType="BIGINT" property="contractPaymentId" />
        <result column="contract_payment" jdbcType="VARCHAR" property="contractPayment" />
        <result column="contract_name" jdbcType="VARCHAR" property="contractName" />
        <result column="project_id" jdbcType="BIGINT" property="projectId" />
        <result column="project_name" jdbcType="VARCHAR" property="projectName" />
        <result column="payment_dept_id" jdbcType="BIGINT" property="paymentDeptId" />
        <result column="payment_dept" jdbcType="VARCHAR" property="paymentDept" />
        <result column="payment_secondary_dept_id" jdbcType="BIGINT" property="paymentSecondaryDeptId" />
        <result column="payment_secondary_dept" jdbcType="VARCHAR" property="paymentSecondaryDept" />
        <result column="belonging_area" jdbcType="VARCHAR" property="belongingArea" />
        <result column="claimant_id" jdbcType="BIGINT" property="claimantId" />
        <result column="claimant_name" jdbcType="VARCHAR" property="claimantName" />
        <result column="claimant_date" jdbcType="TIMESTAMP" property="claimantDate" />
        <result column="business_block" jdbcType="INTEGER" property="businessBlock" />
        <result column="skill_type" jdbcType="INTEGER" property="skillType" />
        <result column="remarks" jdbcType="VARCHAR" property="remarks" />
        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="del_flag" jdbcType="CHAR" property="delFlag" />
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    </resultMap>
    <sql id="Base_Column_List">
        id, project_payment_id, salesman_user_id, salesman_user_name, business_line, project_collection,
        collection_within_budget, contract_payment_id, contract_payment, contract_name, project_id,
        project_name, payment_dept_id, payment_dept, payment_secondary_dept_id, payment_secondary_dept,
        belonging_area, claimant_id, claimant_name, claimant_date, business_block, skill_type
        remarks, create_by, update_by, create_time, update_time, del_flag, tenant_id
    </sql>

    <insert id="saveBatch">
        INSERT INTO project_payment_claim (id, project_payment_id, salesman_user_id, salesman_user_name, business_line,
        project_collection,
        collection_within_budget, contract_payment_id, contract_payment, contract_name, project_id,
        project_name, payment_dept_id, payment_dept, payment_secondary_dept_id, payment_secondary_dept,
        belonging_area, claimant_id, claimant_name, claimant_date, remarks, create_by, update_by,
        create_time, update_time, del_flag, tenant_id)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            ( #{item.id}, #{item.projectPaymentId}, #{item.salesmanUserId}, #{item.salesmanUserName},
            #{item.businessLine}, #{item.projectCollection},
            #{item.collectionWithinBudget}, #{item.contractPaymentId}, #{item.contractPayment}, #{item.contractName},
            #{item.projectId},
            #{item.projectName}, #{item.paymentDeptId}, #{item.paymentDept}, #{item.paymentSecondaryDeptId},
            #{item.paymentSecondaryDept},
            #{item.belongingArea}, #{item.claimantId}, #{item.claimantName}, #{item.claimantDate}, #{item.remarks},
            #{item.createBy},
            #{item.updateBy}, #{item.createTime}, #{item.updateTime}, #{item.delFlag}, #{item.tenantId}
            )
        </foreach>
    </insert>
</mapper>