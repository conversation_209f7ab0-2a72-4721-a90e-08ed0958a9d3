<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.pms.mapper.ProjectProcessInfoMapper">

    <select id="findListPage" resultMap="BaseResultMap">
        SELECT
        id,
        request_id,
        project_id,
        applicat_id,
        applicat,
        process_type,
        status,
        `name`,
        filing_date_time,
        current_node_name,
        ctime
        FROM project_process_info
        <where>1=1
            <if test="filter.projectId != null ">
                AND project_id = #{filter.projectId}
            </if>
        </where>
        ORDER BY filing_date_time DESC
    </select>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.gok.pboot.pms.entity.domain.ProjectProcessInfo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="request_id" property="requestId"/>
        <result column="project_id" property="projectId"/>
        <result column="applicat_id" property="applicatId"/>
        <result column="applicat" property="applicat"/>
        <result column="process_type" property="processType"/>
        <result column="status" property="status"/>
        <result column="name" property="name"/>
        <result column="ctime" property="ctime"/>
        <result column="filing_date_time" property="filingDateTime"/>
        <result column="current_node_name" property="currentNodeName"/>
    </resultMap>

</mapper>