<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.gok.pboot.pms.mapper.ProjectTaskGroupMapper">
    <update id="delete">
        update project_task_group
        set del_flag = 1
        where id = #{id}
    </update>
    <select id="getCountByProjectId" resultType="java.lang.Integer">
        SELECT count(*)
        FROM project_task_group
        WHERE project_id = #{projectId}
          AND del_flag = 0;
    </select>
    <select id="getListByProjectId" resultType="com.gok.pboot.pms.entity.domain.ProjectTaskGroup">
        select a.id, a.project_id, a.title, a.capacity
        from project_task_group a
        where del_flag = 0
          and a.project_id = #{projectId}
    </select>
</mapper>