DROP TABLE IF EXISTS `mhour_roster`;
CREATE TABLE `mhour_roster`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `oa_id` bigint(20) NULL DEFAULT NULL COMMENT 'OA表主键',
  `work_code` varchar(160) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'OA工号',
  `dept_id` bigint(20) NULL DEFAULT NULL COMMENT 'oa部门id',
  `alias_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '姓名',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '花名册（包含OA工号、姓名）' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
