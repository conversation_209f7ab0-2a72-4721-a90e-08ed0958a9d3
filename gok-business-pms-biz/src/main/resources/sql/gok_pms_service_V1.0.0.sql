/*
 Navicat Premium Data Transfer

 Source Server         : Gok-线上
 Source Server Type    : MySQL
 Source Server Version : 50728
 Source Host           : *************:3306
 Source Schema         : gok_pms_service

 Target Server Type    : MySQL
 Target Server Version : 50728
 File Encoding         : 65001

 Date: 19/08/2022 09:14:03
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for mhour_attendance_data
-- ----------------------------
DROP TABLE IF EXISTS `mhour_attendance_data`;
CREATE TABLE `mhour_attendance_data`  (
  `id` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '同步主键',
  `oa_id` bigint(20) NULL DEFAULT NULL COMMENT 'OA表主键',
  `payroll_time` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '考勤月份',
  `cw_due_attendance` decimal(12, 2) NULL DEFAULT NULL COMMENT '应出勤天数',
  `cw_actual_attendance` decimal(12, 2) NULL DEFAULT NULL COMMENT '实际出勤天',
  `cw_absenteeism_leave` decimal(12, 2) NULL DEFAULT NULL COMMENT '旷工次数',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '考勤数据同步' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for mhour_daily_paper
-- ----------------------------
DROP TABLE IF EXISTS `mhour_daily_paper`;
CREATE TABLE `mhour_daily_paper`  (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '员工ID',
  `submission_date` date NULL DEFAULT NULL COMMENT '提交日期',
  `holiday` tinyint(2) NOT NULL COMMENT '是否节假日（0=否，1=是）',
  `filling_state` tinyint(2) NOT NULL COMMENT '填报状态（0=正常，1=滞后）',
  `approval_status` tinyint(2) NOT NULL COMMENT '审核状态（0=未提交，1=已退回，2=待审核，3=不通过，4=已审核）',
  `project_count` int(11) NOT NULL COMMENT '项目数量',
  `task_count` int(11) NOT NULL COMMENT '任务数量',
  `daily_hour_count` decimal(10, 1) NOT NULL COMMENT '日常工时数量',
  `added_hour_count` decimal(10, 1) NOT NULL COMMENT '加班工时数量',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `creator_id` bigint(20) NULL DEFAULT NULL COMMENT '创建人ID',
  `modifier` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `modifier_id` bigint(20) NULL DEFAULT NULL COMMENT '修改人ID',
  `ctime` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `mtime` datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标识',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '日报' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for mhour_daily_paper_entry
-- ----------------------------
DROP TABLE IF EXISTS `mhour_daily_paper_entry`;
CREATE TABLE `mhour_daily_paper_entry`  (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `project_id` bigint(20) NOT NULL COMMENT '项目ID',
  `project_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目名称',
  `task_id` bigint(20) NOT NULL COMMENT '任务ID',
  `task_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务名称',
  `daily_paper_id` bigint(20) NOT NULL COMMENT '日报ID',
  `approval_status` tinyint(2) NOT NULL COMMENT '审核状态（0=未提交，1=已退回，2=待审核，3=不通过，4=已通过）',
  `submission_date` date NOT NULL COMMENT '提交日期',
  `actual_hours` decimal(10, 1) NOT NULL COMMENT '实际工时',
  `added_hours` decimal(10, 1) NOT NULL COMMENT '加班工时',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '描述',
  `approval_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '审核不通过原因',
  `user_id` bigint(20) NOT NULL COMMENT '提交人ID',
  `user_real_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '提交人姓名',
  `user_dept_id` bigint(20) NOT NULL COMMENT '提交人所属部门ID',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `creator_id` bigint(20) NULL DEFAULT NULL COMMENT '创建人ID',
  `modifier` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `modifier_id` bigint(20) NULL DEFAULT NULL COMMENT '修改人ID',
  `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标识',
  `mtime` datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  `ctime` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '工时日报条目' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for mhour_filing
-- ----------------------------
DROP TABLE IF EXISTS `mhour_filing`;
CREATE TABLE `mhour_filing`  (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `year` int(11) NOT NULL COMMENT '年',
  `month` int(11) NOT NULL COMMENT '月',
  `filing_start_datetime` datetime(0) NOT NULL COMMENT '归档年月对应开始日期时间（当月1号0点）',
  `filing_end_datetime` datetime(0) NOT NULL COMMENT '归档年月对应结束日期时间（次月1号0点）',
  `filed` tinyint(2) NOT NULL COMMENT '对应状态（0=未归档，1=已归档）',
  `operator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作人',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `creator_id` bigint(20) NULL DEFAULT NULL COMMENT '创建人ID',
  `modifier` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `modifier_id` bigint(20) NULL DEFAULT NULL COMMENT '修改人ID',
  `ctime` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `mtime` datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标识',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '归档' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for mhour_holiday
-- ----------------------------
DROP TABLE IF EXISTS `mhour_holiday`;
CREATE TABLE `mhour_holiday`  (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `day_datetime` datetime(0) NOT NULL COMMENT '日期',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `creator_id` bigint(20) NULL DEFAULT NULL COMMENT '创建人ID',
  `modifier` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `modifier_id` bigint(20) NULL DEFAULT NULL COMMENT '修改人ID',
  `ctime` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `mtime` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标识',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '非工作日' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for mhour_operating_record
-- ----------------------------
DROP TABLE IF EXISTS `mhour_operating_record`;
CREATE TABLE `mhour_operating_record`  (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `operation_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作类型',
  `operation_info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作信息',
  `task_id` bigint(20) NOT NULL COMMENT '被操作任务ID',
  `operator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作人',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `creator_id` bigint(20) NULL DEFAULT NULL COMMENT '创建人ID',
  `modifier` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `modifier_id` bigint(20) NULL DEFAULT NULL COMMENT '修改人ID',
  `ctime` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `mtime` datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标识',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '操作记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for mhour_personnel_reuse
-- ----------------------------
DROP TABLE IF EXISTS `mhour_personnel_reuse`;
CREATE TABLE `mhour_personnel_reuse`  (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `project_code` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目编号',
  `project_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目名称',
  `user_real_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '人员名称',
  `aggregated_days` int(11) NOT NULL COMMENT '汇总工时（天）',
  `personnel_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '人才类型',
  `grade` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '年级',
  `school_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '院校名称',
  `major` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '专业',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '备注',
  `executor_user_id` bigint(20) NOT NULL COMMENT '导入人ID',
  `executor_user_real_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '导入人姓名',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `creator_id` bigint(20) NULL DEFAULT NULL COMMENT '创建人ID',
  `modifier` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `modifier_id` bigint(20) NULL DEFAULT NULL COMMENT '修改人ID',
  `ctime` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `mtime` datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  `del_flag` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '删除标识',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '人才复用' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for mhour_privilege
-- ----------------------------
DROP TABLE IF EXISTS `mhour_privilege`;
CREATE TABLE `mhour_privilege`  (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '人员ID',
  `privilege_type` tinyint(2) NOT NULL COMMENT '权限类型（0=业务部门管理员，1=审核员，2=管理员）',
  `project_id` bigint(20) NOT NULL COMMENT '可以审核的项目',
  `dept_id` bigint(20) NOT NULL COMMENT '可以审核的部门ID',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `creator_id` bigint(20) NULL DEFAULT NULL COMMENT '创建人ID',
  `modifier` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `modifier_id` bigint(20) NULL DEFAULT NULL COMMENT '修改人ID',
  `ctime` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `mtime` datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标识',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '部门审核人员及权限表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for mhour_project
-- ----------------------------
DROP TABLE IF EXISTS `mhour_project`;
CREATE TABLE `mhour_project`  (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `project_name` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目名称',
  `code` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目编号',
  `project_status` tinyint(2) NOT NULL COMMENT '项目状态',
  `manager_user_id` bigint(20) NULL DEFAULT NULL COMMENT '项目经理人员ID',
  `manager_user_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '项目经理姓名',
  `salesman_user_id` bigint(20) NULL DEFAULT NULL COMMENT '项目销售人员ID',
  `salesman_user_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '项目销售姓名',
  `dept_id` bigint(20) NOT NULL COMMENT '业务归属（一级）部门ID',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `creator_id` bigint(20) NULL DEFAULT NULL COMMENT '创建人ID',
  `modifier` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `modifier_id` bigint(20) NULL DEFAULT NULL COMMENT '修改人ID',
  `ctime` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `mtime` datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标识',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `code`(`code`) USING BTREE COMMENT '项目编号唯一索引'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '项目' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for mhour_task
-- ----------------------------
DROP TABLE IF EXISTS `mhour_task`;
CREATE TABLE `mhour_task`  (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `task_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务名称',
  `task_type` tinyint(2) NOT NULL DEFAULT 0 COMMENT '任务类型（0=默认任务，1=手动添加，2=后期维保）',
  `task_status` tinyint(2) NOT NULL DEFAULT 0 COMMENT '任务状态（0=正常，1=关闭）',
  `actual_hour` decimal(10, 1) NOT NULL COMMENT '实际工时',
  `normal_hour` decimal(10, 1) NOT NULL COMMENT '正常工时',
  `added_hour` decimal(10, 1) NOT NULL COMMENT '加班工时',
  `user_type` tinyint(2) NOT NULL COMMENT '人员类型（0=正常，1=实习，2=项目实习）',
  `project_id` bigint(20) NOT NULL COMMENT '所属项目ID',
  `task_user_id` bigint(20) NOT NULL COMMENT '任务-人员多对多关联ID',
  `hour_confirmed` tinyint(2) NOT NULL COMMENT '工时确认状态（0=待确认，1=已确认）',
  `hour_confirmer_user_id` bigint(20) NOT NULL COMMENT '工时确认人ID',
  `hour_confirmer_real_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '工时确认人姓名',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `creator_id` bigint(20) NULL DEFAULT NULL COMMENT '创建人ID',
  `modifier` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `modifier_id` bigint(20) NULL DEFAULT NULL COMMENT '修改人ID',
  `ctime` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `mtime` datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  `del_flag` int(11) NULL DEFAULT 0 COMMENT '删除标识',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '任务' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for mhour_task_user
-- ----------------------------
DROP TABLE IF EXISTS `mhour_task_user`;
CREATE TABLE `mhour_task_user`  (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `task_id` bigint(20) NOT NULL COMMENT '任务ID',
  `user_id` bigint(20) NOT NULL COMMENT '人员ID',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `creator_id` bigint(20) NULL DEFAULT NULL COMMENT '创建人ID',
  `modifier` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `modifier_id` bigint(20) NULL DEFAULT NULL COMMENT '修改人ID',
  `ctime` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `mtime` datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标识',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '任务-人员多对多关联' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
