/*
 Navicat Premium Data Transfer

 Source Server         : 国科-dev
 Source Server Type    : MySQL
 Source Server Version : 50728
 Source Host           : *************:10012
 Source Schema         : gok_pms_service

 Target Server Type    : MySQL
 Target Server Version : 50728
 File Encoding         : 65001

 Date: 12/10/2022 16:40:29
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for mhour_overtime_leave_data
-- ----------------------------
DROP TABLE IF EXISTS `mhour_overtime_leave_data`;
CREATE TABLE `mhour_overtime_leave_data` (
  `id` varchar(30) NOT NULL COMMENT '同步主键',
  `oa_id` bigint(20) NOT NULL COMMENT 'OA编号',
  `minute_data` int(10) NOT NULL COMMENT '分钟数',
  `hour_data` decimal(10,2) NOT NULL COMMENT '小时数',
  `belongdate` date NOT NULL COMMENT '流程归属日期',
  `xmmc` bigint(20) DEFAULT NULL COMMENT '项目编号',
  `type` int(2) NOT NULL COMMENT '数据类型(1:加班、2:请假、3:销假)',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='加班、请假、销假数据同步';

SET FOREIGN_KEY_CHECKS = 1;
