package com.gok.pboot.pms.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.entity.vo.ProjectProcessInfoFindPageVO;

import java.util.Map;

/**
 * <p>
 * 项目动态表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-14
 **/
public interface IProjectProcessInfoService {

    /**
     * 分页查询项目流程信息（OA同步）
     *
     * @param pageRequest
     * @param filter
     * @return
     */
    Page<ProjectProcessInfoFindPageVO> findPage(PageRequest pageRequest, Map<String, Object> filter);

}
