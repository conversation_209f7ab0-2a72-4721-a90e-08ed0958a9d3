package com.gok.pboot.pms.cost.entity.vo;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2025/03/04
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ColumnWidth(15)
public class CostIncomeCalculationDetailExportVO {

    /**
     * 姓名
     */
    @ExcelProperty("姓名")
    private String memberName;

    /**
     * 工号
     */
    @ExcelProperty("工号")
    private String workCode;

    /**
     * 人员属性（0=国科人员，1=第三方）
     */
    @ExcelProperty("人员属性")
    private String personnelAttributeTxt;

    /**
     * 岗位
     */
    @ExcelProperty("岗位")
    private String jobName;

    /**
     * 归属月份
     */
    @ExcelProperty("归属月份")
    private String belongMonth;

    /**
     * 结算人天
     */
    @ExcelProperty("结算人天")
    private String settlementHours;

    /**
     * 结算单价
     */
    @ExcelProperty("结算单价")
    private String settlementUnitPrice;

    /**
     * 报价税率
     */
    @ExcelProperty("报价税率")
    private String quotedRateIdTxt;

    /**
     * 客户承担费用
     */
    @ColumnWidth(30)
    @ExcelProperty("客户承担费用")
    private String customerBearingAmount;

    /**
     * 测算含税金额
     */
    @ColumnWidth(30)
    @ExcelProperty("测算含税金额")
    private String estimatedInclusiveAmountTax;

    /**
     * 确认状态
     */
    @ExcelProperty("确认状态")
    private String confirmStatusTxt;

    /**
     * 确认日期
     */
    @ExcelProperty("确认日期")
    private String confirmDate;

    /**
     * 结算状态
     */
    @ExcelProperty("结算状态")
    private String settlementStatusTxt;

    /**
     * 操作结算日期
     */
    @ColumnWidth(30)
    @ExcelProperty("操作结算日期")
    private String operationSettlementDate;

    /**
     * 结算明细编号
     */
    @ColumnWidth(30)
    @ExcelProperty("结算明细编号")
    private String settlementDetailsNumber;

    /**
     * 结算单编号
     */
    @ColumnWidth(30)
    @ExcelProperty("结算单编号")
    private String settlementNumber;

    public static List<CostIncomeCalculationDetailExportVO> fromVoList(List<CostIncomeCalculationDetailVO> voList) {
        if (CollUtil.isEmpty(voList)) {
            return ListUtil.empty();
        }

        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM");
        List<CostIncomeCalculationDetailExportVO> resultList = new ArrayList<>();
        voList.forEach(vo -> {
            List<CostIncomeSettlementDetailVO> settlementList = vo.getSettlementList();
            if (CollUtil.isEmpty(settlementList)) {
                CostIncomeCalculationDetailExportVO item =
                        BeanUtil.copyProperties(vo, CostIncomeCalculationDetailExportVO.class);
                item.setBelongMonth(vo.getBelongMonth().format(dateTimeFormatter));
                resultList.add(item);
            } else {
                settlementList.forEach(settlement -> {
                    CostIncomeCalculationDetailExportVO item =
                            BeanUtil.copyProperties(vo, CostIncomeCalculationDetailExportVO.class);
                    item.setBelongMonth(vo.getBelongMonth().format(dateTimeFormatter));
                    item.setSettlementNumber(settlement.getSettlementNumber());
                    item.setSettlementDetailsNumber(settlement.getSettlementDetailsNumber());
                    item.setOperationSettlementDate(settlement.getOperationSettlementDate());
                    resultList.add(item);
                });
            }
        });

        return resultList;
    }

}
