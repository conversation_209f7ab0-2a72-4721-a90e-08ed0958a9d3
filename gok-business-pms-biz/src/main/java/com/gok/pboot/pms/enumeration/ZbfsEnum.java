package com.gok.pboot.pms.enumeration;

/**
 * 项目-招标方式枚举
 *
 * <AUTHOR>
 */
public enum ZbfsEnum implements ValueEnum<Integer> {
    /**
     * 公开招标-最低价中标
     */
    bottom_price(0, "公开招标-最低价中标"),
    /**
     * 公开招标-综合评分
     */
    Comprehensive_score(1, "公开招标-综合评分"),
    /**
     * 邀请招标
     */
    invite(2, "邀请招标"),
    /**
     * 竞争性谈判
     */
    negotiations(3, "竞争性谈判"),
    /**
     * 竞争性磋商
     */
    consult(4, "竞争性磋商"),
    /**
     * 询价采购
     */
    procure(5, "询价采购"),
    /**
     * 待定
     */
    pending(6, "待定");

    //值
    private Integer  value;
    //名称
    private String name;

    ZbfsEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
    /**
     * 获取值
     *
     * @return Integer
     */
    @Override
    public Integer getValue() {
        return value;
    }

    /**
     * 获取名称
     *
     * @return String
     */
    @Override
    public String getName() {
        return name;
    }

    public static String getNameByVal(Integer value) {
        for (ZbfsEnum zbfsEnum : ZbfsEnum.values()) {
            if (zbfsEnum.value.equals(value)) {
                return zbfsEnum.name;
            }
        }
        return "";
    }
    public static Integer getValByName(String name) {
        for (ZbfsEnum zbfsEnum : ZbfsEnum.values()) {
            if (zbfsEnum.getName().equals(name)) {
                return zbfsEnum.getValue();
            }
        }
        return null;
    }

    public static ZbfsEnum getZbfsEnum(Integer value) {
        for (ZbfsEnum zbfsEnum : ZbfsEnum.values()) {
            if (zbfsEnum.value.equals(value)) {
                return zbfsEnum;
            }
        }
        return null;
    }
}
