package com.gok.pboot.pms.enumeration;

import lombok.Getter;

/**
 * 推送状态
 *
 * <AUTHOR>
 * @since 2023-08-30
 */
@Getter
public enum PushStatusEnum implements ValueEnum<Integer> {

    /**
     * 待推送
     */
    WAIT_PUSH(0, "待推送"),

    /**
     * 已推送
     */
    SUCCESS_PUSH(1, "已推送"),

    /**
     * 推送失败
     */
    FAIL_PUSH(2, "推送失败");

    private final Integer value;

    private final String name;

    PushStatusEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
}
