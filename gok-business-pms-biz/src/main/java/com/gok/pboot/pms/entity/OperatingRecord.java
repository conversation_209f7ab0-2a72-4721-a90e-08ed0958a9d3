package com.gok.pboot.pms.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 操作记录
 *
 * <AUTHOR>
 * @since 2022-08-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(OperatingRecord.ALIAS)
public class OperatingRecord extends BeanEntity<Long> {

    public static final String ALIAS = "mhour_operating_record";
    /**
    * 操作类型
     * @see com.gok.pboot.pms.enumeration.OperationTypeEnum
    */
    private Integer operationType;
    /**
    * 操作信息
    */
    private String operationInfo;
    /**
    * 被操作任务ID
    */
    private Long taskId;
    /**
    * 操作人
    */
    private String operator;


}
