package com.gok.pboot.pms.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.common.base.PropertyFilters;
import com.gok.pboot.pms.entity.dto.ProjectResponsePlanDTO;
import com.gok.pboot.pms.entity.dto.ProjectRiskDTO;
import com.gok.pboot.pms.entity.vo.ProjectRiskFindPageVO;
import com.gok.pboot.pms.entity.vo.ProjectRiskSpecialVo;
import com.gok.pboot.pms.service.IProjectRiskService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @description 项目风险 前端控制器
 * @menu 项目风险
 * @since 2023-07-12
 **/
@Slf4j
@RestController
@RequestMapping("/projectRisk")
@RequiredArgsConstructor
@Api(tags = "项目风险表")
public class ProjectRiskController {

    private final IProjectRiskService service;

    /**
     * 分页查询 项目风险
     *
     * @param pageRequest 分页请求实体
     * @param request     {@link HttpServletRequest}
     * @return {@link ApiResult}<{@link Page}<{@link ProjectRiskFindPageVO}>>
     */
    @GetMapping("/findPage")
    public ApiResult<Page<ProjectRiskFindPageVO>> findPage(PageRequest pageRequest, HttpServletRequest request) {
        return ApiResult.success(service.findPage(pageRequest, PropertyFilters.get(request)));
    }

    /**
     * 新增 项目风险
     *
     * @param request 新增请求实体
     * @return {@link ApiResult}
     */
    @PostMapping
    public ApiResult<Long> save(@Valid @RequestBody ProjectRiskDTO request) {
        return ApiResult.success(service.save(request));
    }

    /**
     * 编辑 项目风险
     *
     * @param request 编辑请求实体
     * @return {@link ApiResult}
     */
    @PutMapping
    public ApiResult<Long> update(@Valid @RequestBody ProjectRiskDTO request) {
        return ApiResult.success(service.update(request));
    }

    /**
     * 批量逻辑删除
     *
     * @param list 要删除的项目风险id集合[]
     * @return {@link ApiResult}
     */
    @DeleteMapping("/batchDel")
    public ApiResult<String> batchDel(@RequestBody List<Long> list) {
        return service.batchDel(list);
    }

    /**
     * 查询责任人的风险数量
     * @return {@link ApiResult<Integer>}
     */
    @GetMapping("/findChargeRiskNum")
    public ApiResult<Integer> findChargeRiskNum(){
        return service.findChargeRiskNum();
    }

    /**
     * 分页查询负责人的风险表
     * @param pageRequest 分页请求实体
     * @customParam pageNumber 页码
     * @customParam pageSize 页长
     * @param request     {@link HttpServletRequest}
     * @customParam filter_S_projectName 项目名称（模糊查询）
     * @return {@link ApiResult<Page<ProjectRiskFindPageVO>>}
     */
    @GetMapping("/findPageOfCharge")
    public ApiResult<Page<ProjectRiskFindPageVO>> findPageOfCharge(PageRequest pageRequest, HttpServletRequest request){
        return ApiResult.success(service.findPageOfCharge(pageRequest,PropertyFilters.get(request,true)));
    }

    ///** 可用方法
    // * 根据项目名称模糊查询负责人的风险项目
    // * @param pageRequest 分页请求实体
    // * @customParam pageNumber 页码
    // * @customParam pageSize 页长
    // * @param request     {@link HttpServletRequest}
    // * @customParam filter_L_chargeUserId 负责人id
    // * @customParam filter_S_projectName 项目名称
    // * @return {@link ApiResult<Page<ProjectRiskFindPageVO>>}
    // */
    //@GetMapping("/findByItemName")
    //public ApiResult<Page<ProjectRiskFindPageVO>> findByItemName(PageRequest pageRequest, HttpServletRequest request){
    //    return ApiResult.success(service.findByItemName(pageRequest,PropertyFilters.get(request,true)));
    //}

    /**
     * 编辑风险应对计划字段
     * @param request {@link HttpServletRequest}
     * @return {@link ApiResult<Long>}
     */
    @PutMapping("/updateResponsePlan")
    public ApiResult<Long> updateResponsePlan(@Valid @RequestBody ProjectResponsePlanDTO request,
                                              BindingResult result) {
        if (result.hasErrors()) {
            return ApiResult.failure(result.getAllErrors().get(0).getDefaultMessage());
        }
        return ApiResult.success(service.updateResponsePlan(request));
    }

    /**
     * 根据风险项id查询基本信息，可查出已删除风险项
     * @param id 风险id
     * @return {@link ApiResult}<{@link ProjectRiskSpecialVo}>}
     */
    @GetMapping("/{id}")
    public ApiResult<ProjectRiskSpecialVo> findRiskSpecialById(@PathVariable("id") Long id){
        return ApiResult.success(service.findRiskSpecialById(id));
    }


}
