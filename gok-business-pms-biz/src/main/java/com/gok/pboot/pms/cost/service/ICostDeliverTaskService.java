package com.gok.pboot.pms.cost.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.cost.entity.domain.CostDeliverTask;
import com.gok.pboot.pms.cost.entity.domain.CostManageVersion;
import com.gok.pboot.pms.cost.entity.domain.CostTaskCategoryManagement;
import com.gok.pboot.pms.cost.entity.dto.*;
import com.gok.pboot.pms.cost.entity.vo.*;
import com.gok.pboot.pms.cost.enums.TaskAbnormalTypeEnum;
import com.gok.pboot.pms.enumeration.ProjectTaskKindEnum;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 交付管理工单服务接口
 *
 * <AUTHOR>
 * @date 2025/01/15
 */
public interface ICostDeliverTaskService extends IService<CostDeliverTask> {

    /**
     * 按 ID 获取子项
     *
     * @param pageRequest   页面请求
     * @param taskId        任务 ID
     * @param requestObject requestObject
     * @return {@link Page }<{@link CostDeliverTaskVO }>
     */
    Page<CostDeliverTaskVO> getChildrenPage(PageRequest pageRequest, Long taskId, JSONObject requestObject);

    /**
     * 查询所有项目工单
     *
     * @param projectId 项目 id
     * @param taskType  任务类型
     * @return 项目工单集合[]
     */
    List<CostDeliverTaskVO> findAll(Long projectId, Integer taskType);

    /**
     * 批量创建一级工单
     *
     * @param projectId                     项目 id
     * @param costDeliverTaskAddEditDTOList 成本 交付任务 添加 编辑 dto list
     */
    void batchCreateFirstDeliverTask(Long projectId, List<CostDeliverTaskAddEditDTO> costDeliverTaskAddEditDTOList);

    /**
     * 工单头部统计信息
     *
     * @param taskType 任务类型
     * @return {@link CostTaskTopCountMsgVO }
     */
    CostTaskTopCountMsgVO taskTopCountMsg(Integer taskType);

    /**
     * 通过工单
     *
     * @param id id
     */
    void passTask(Long id);

    /**
     * 退回工单
     *
     * @param id  id
     * @param dto DTO
     */
    void returnTask(Long id, CostTaskReturnReasonDTO dto);

    /**
     * 提交完成佐证
     *
     * @param id  id
     * @param dto DTO
     */
    void taskFinishCorroboration(Long id, CostFinishCorroborationDTO dto);

    /**
     * 查看佐证
     *
     * @param id id
     * @return {@link CostViewCorroborationVO }
     */
    CostViewCorroborationVO taskViewCorroboration(Long id);


    /**
     * 工单编辑
     *
     * @param costDeliverTaskAddEditDTO 成本 交付任务 添加编辑 DTO
     */
    void editFirstDeliverTask(CostDeliverTaskAddEditDTO costDeliverTaskAddEditDTO);

    /**
     * 售后交付工单拆解
     *
     * @param parentId                      父工单 ID
     * @param costDeliverTaskAddEditDTOList 成本 交付任务 添加 编辑 dto list
     */
    void decomposition(Long parentId, List<CostDeliverTaskAddEditDTO> costDeliverTaskAddEditDTOList);


    /**
     * 批量编辑售前支撑工单
     *
     * @param costSupportTaskAddEditDTO 售前支撑工单添加 编辑 dto
     * @param add                       加
     */
    void batchAddEditSupportTask(CostSupportTaskAddEditDTO costSupportTaskAddEditDTO, boolean add);

    /**
     * 通过项目id查找工单列表
     *
     * @param pageRequest 分页请求
     * @param filter      查询请求
     * @return {@link Page}<{@link CostDeliverTaskVO}>
     */
    Page<CostDeliverTaskVO> findPage(PageRequest pageRequest, Map<String, Object> filter);

    /**
     * 查看退回信息
     *
     * @param id id
     * @return {@link CostTaskReturnInfoVO }
     */
    CostTaskReturnInfoVO returnInfo(Long id);

    /**
     * 查询最新已确认的人工成本预算
     *
     * @param projectId 项目id
     * @return {@link List }<{@link DeliverCostBudgetListVO }>
     */
    List<DeliverCostBudgetListVO> getConfirmedCost(Long projectId);

    /**
     * 获取支持成本预算
     *
     * @param projectId 项目 ID
     * @return {@link DeliverCostBudgetListVO }
     */
    DeliverCostBudgetListVO getSupportCostBudget(Long projectId);

    /**
     * 通过工单查询人工成本预算明细对应已用预算、剩余预算、已确认成本
     *
     * @param projectId          项目id
     * @param costBudgetListList 人工成本预算明细
     */
    void getUsedCostByTask(Long projectId, List<DeliverCostBudgetListVO> costBudgetListList);

    /**
     * 查询所有（人工成本、费用报销、外采费用）成本预算明细对应已用预算、剩余预算、已确认成本
     *
     * @param costManageVersion 成本管理版本信息
     * @return {@link Map }<{@link String }, {@link DeliverCostBudgetListVO }> key-accountOaId+taxRate value-已用预算、剩余预算、已确认成本
     */
    Map<String, DeliverCostBudgetListVO> getUsedAllCostBudget(CostManageVersion costManageVersion);

    /**
     * 批量通过工单
     *
     * @param ids IDS
     */
    void batchPassTask(List<Long> ids);

    /**
     * 删除工单（只针对一级任务）
     *
     * @param id id
     */
    void removeTask(Long id);

    /**
     * 通过工单id查找工单
     *
     * @param id 身份证
     * @return {@link CostDeliverTaskVO }
     */
    CostDeliverTaskVO findById(Long id);

    /**
     * 查询个人工单
     *
     * @param pageRequest 页面请求
     * @param request     查询请求
     * @return {@link Page }<{@link CostDeliverTaskVO }>
     */
    Page<CostDeliverTaskVO> findPersonalTask(PageRequest pageRequest, CostDeliverTaskDTO request);


//    /**
//     * 工单审核分页查询
//     *
//     * @param pageRequest 页面请求
//     * @param request     请求
//     * @return {@link Page }<{@link CostDeliverApprovalVO }>
//     */
//    Page<CostDeliverApprovalVO> findTaskApprovalPage(PageRequest pageRequest, CostDeliverTaskDTO request);


    Page<CostDeliverTaskVO> findProjectApprovalV1(PageRequest pageRequest, CostDeliverTaskDTO request);

//    /**
//     * 查看项目工单审核情况
//     *
//     * @param pageRequest 页面请求
//     * @param request     请求
//     * @return {@link Page }<{@link CostDeliverTaskVO }>
//     */
//    Page<CostDeliverTaskVO> findProjectApproval(PageRequest pageRequest, CostDeliverTaskDTO request);

//    /**
//     * 查询我待审核/已审核工单
//     *
//     * @param pageRequest 页面请求
//     * @param request     查询请求
//     * @return {@link Page }<{@link CostDeliverTaskVO }>
//     */
//    Page<CostDeliverTaskVO> findPersonalApproval(PageRequest pageRequest, CostDeliverTaskDTO request);

    /**
     * 计算人员时薪
     *
     * @param userId 用户ID
     * @return 人员成本
     */
    List<Map<String, Object>> calculateUserHourlyWages(Set<Long> userId);

    /**
     * 计算工单人工成本
     *
     * @param dto 工单
     * @return 实际人工成本
     */
    CostSalaryDTO calculateEstimateLaborCost(CostCalculateLaborCostDTO dto);


    /**
     * 获得 工单产值
     *
     * @param projectId 项目 ID
     * @return {@link CostDeliverTaskIncomeVo }
     */
    CostDeliverTaskIncomeVo getDeliverTaskIncome(Long projectId);


    /**
     * 结束工单任务
     *
     * @param id 工单ID
     */
    void endTask(Long id);

    /**
     * 重启工单任务
     *
     * @param id 工单ID
     */
    void restartTask(Long id);


    /**
     * 工时填报查询所有当前用户可用的所有项目和对应的工单
     *
     * @param date 日期
     * @return {@link List }<{@link CostDeliverTaskInDailyPaperEntryVO }>
     */
    List<CostDeliverTaskInDailyPaperEntryVO> findCurrentForDailyPaperEntry(LocalDate date);


    /**
     * 查询异常工单列表
     *
     * @param pageRequest 分页请求
     * @param request     查询条件
     * @return 异常工单列表
     */
    Page<CostTaskAbnormalVO> findAbnormalPage(PageRequest pageRequest, CostTaskAbnormalDTO request);

    /**
     * 导出异常工单
     *
     * @param request 请求
     * @return {@link List }<{@link CostTaskAbnormalVO }>
     */
    List<CostTaskAbnormalVO> exportAbnormal(CostTaskAbnormalDTO request);


    /**
     * 发送未评价异常工单消息推送
     *
     * @param abnormalTypeEnum 异常类型枚举
     */
    void sendAbnormalMsg(TaskAbnormalTypeEnum abnormalTypeEnum);

    /**
     * 发送异常工单消息推送
     * 包含三种异常工单：
     * 1. 未提交：超过工单的起止日期的截止日期1天，工单尚未提交完成
     * 2. 未审核：超过工单提交日期1天后，工单尚未进行审核
     * 3. 未拆解：工单为总成工单，起止日期含近60天内的日期，工单尚未进行拆解
     *
     * @param request 查询条件
     */
    void sendAbnormalMsg(CostTaskAbnormalDTO request);

    /**
     * 获取异常工单头部统计信息
     *
     * @param request 请求
     * @return {@link CostTaskAbnormalCountVO }
     */
    CostTaskAbnormalCountVO getAbnormalCount(CostTaskAbnormalDTO request);

    /**
     * 结束售前支撑工单
     */
    void endSupportTask();

    /**
     * 按日期获取最大序列号
     *
     * @param dateStr 日期 str
     * @param prefix  前缀
     * @return {@link Integer }
     */
    Integer getMaxSequenceNumberByDate(String dateStr, String prefix);

    /**
     * 生成任务号
     *
     * @param taskTypeEnum    任务类型
     * @param taskCategoryMap 任务类别图
     * @param taskCategory    任务类别
     * @param managerName     经理姓名
     * @return {@link String }
     */
    String generateTaskNo(ProjectTaskKindEnum taskTypeEnum, Map<Integer, CostTaskCategoryManagement> taskCategoryMap,
                          Integer taskCategory, String managerName);


    /**
     * 获取售后交付工单拆解草稿
     *
     * @param taskId 任务id
     * @return {@link JSONObject }
     */
    Object decompositionDraft(Long taskId);

    /**
     * 保存售后交付工单拆解草稿
     *
     * @param taskId 任务id
     * @param object 对象
     */
    void decompositionSaveDraft(Long taskId, Object object);


    /**
     * 查找任务审批页面 v1
     *
     * @param pageRequest 页面请求
     * @param request     请求
     * @return {@link Page }<{@link CostDeliverApprovalVO }>
     */
    Page<CostDeliverApprovalVO> findTaskApprovalPageV1(PageRequest pageRequest, CostDeliverTaskDTO request);

    /**
     * 批量添加售前支持工单
     *
     * @param costSupportTaskAddEditDTOList 成本支持任务添加编辑 dto list
     */
    void batchAddSupportTask(List<CostSupportTaskAddEditDTO> costSupportTaskAddEditDTOList);
}
