package com.gok.pboot.pms.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.pboot.pms.entity.domain.ProjectBusinessMilestonesSchedule;
import com.gok.pboot.pms.mapper.ProjectBusinessMilestonesScheduleMapper;
import com.gok.pboot.pms.service.IProjectBusinessMilestonesScheduleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class ProjectBusinessMilestonesScheduleImpl extends ServiceImpl<ProjectBusinessMilestonesScheduleMapper, ProjectBusinessMilestonesSchedule>
        implements IProjectBusinessMilestonesScheduleService{

}