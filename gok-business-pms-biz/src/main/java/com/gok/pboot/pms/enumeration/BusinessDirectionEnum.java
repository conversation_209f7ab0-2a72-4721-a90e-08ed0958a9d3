package com.gok.pboot.pms.enumeration;

/**
 * 项目-业务方向枚举
 *
 * <AUTHOR>
 */
public enum BusinessDirectionEnum implements ValueEnum<Integer> {
    /**
     * ICT
     */
    ict(0, "ICT"),
    /**
     * 信息安全
     */
    information_security(1, "信息安全"),
    /**
     * 软件开发
     */
    software_development(2, "软件开发"),
    /**
     * 综合
     */
    comprehensive(3, "综合"),
    /**
     * 数据治理
     */
    data_governance(4, "数据治理");

    //值
    private Integer  value;
    //名称
    private String name;

    BusinessDirectionEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
    /**
     * 获取值
     *
     * @return Integer
     */
    @Override
    public Integer getValue() {
        return value;
    }

    /**
     * 获取名称
     *
     * @return String
     */
    @Override
    public String getName() {
        return name;
    }

    public static String getNameByVal(Integer value) {
        for (BusinessDirectionEnum businessDirectionEnum : BusinessDirectionEnum.values()) {
            if (businessDirectionEnum.value.equals(value)) {
                return businessDirectionEnum.name;
            }
        }
        return "";
    }
    public static Integer getValByName(String name) {
        for (BusinessDirectionEnum businessDirectionEnum : BusinessDirectionEnum.values()) {
            if (businessDirectionEnum.getName().equals(name)) {
                return businessDirectionEnum.getValue();
            }
        }
        return null;
    }

    public static BusinessDirectionEnum getBusinessDirectionEnum(Integer value) {
        for (BusinessDirectionEnum businessDirectionEnum : BusinessDirectionEnum.values()) {
            if (businessDirectionEnum.value.equals(value)) {
                return businessDirectionEnum;
            }
        }
        return null;
    }
}
