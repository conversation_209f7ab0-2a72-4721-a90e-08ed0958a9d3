package com.gok.pboot.pms.entity.dto;


import com.gok.pboot.pms.common.base.PageRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/2/22
 * 工时饱和度查询实体
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SaturationStatisticsDTO extends PageRequest implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 所选日期左边界  年月日
     */
    private String startTime;

    /**
     * 所选日期右边界  年月日
     */
    private String endTime;

    /**
     * 所选部门Id
     */
    private List<Long> deptIds;
    /**
     * 默认展示数量
     */
    private Integer defaultShowNum;

    /**
     * 标记  1以部门groupby  0以用户+项目groupby，2以用户groupby
     */
    private Integer flag;

    /**
     * 审核状态
     */
    private Integer approvalStatus;

    /**
     * 是否是内部项目
     */
    private Integer insideProject;

    /**
     * 项目收入类型
     * @see com.gok.pboot.pms.enumeration.ProjectIncomeTypeEnum
     */
    private List<Integer> projectIncomeTypes;

    /**
     * 用户名称
     */
    private String userName;
    /**
     * 用户id
     */
    private Long userId;

    /**
     * 只看筛选条件对应的部门数据
     */
    private Boolean onlySelectedDeptFlag;


    /**
     * 是否是查询用户详情
     */
    private Boolean checkUserDetailsFlag;

}
