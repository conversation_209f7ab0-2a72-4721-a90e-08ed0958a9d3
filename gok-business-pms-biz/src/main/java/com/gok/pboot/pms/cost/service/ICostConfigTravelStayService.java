package com.gok.pboot.pms.cost.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.cost.entity.domain.CostConfigTravelStay;
import com.gok.pboot.pms.cost.entity.dto.CostConfigTravelStayDTO;
import com.gok.pboot.pms.cost.entity.vo.CostConfigTravelStayVO;

import java.util.List;

/**
 * <p>
 * 差旅住宿标准配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
public interface ICostConfigTravelStayService extends IService<CostConfigTravelStay> {

    /**
     * 获取 差旅住宿标准配置
     *
     * @return {@link List }<{@link CostConfigTravelStayVO }>
     */
    List<CostConfigTravelStayVO> getCostConfigTravelStayList();

    /**
     * 编辑 差旅住宿标准配置
     *
     * @param dtoList DTO
     */
    void editCostConfigTravelStayList(List<CostConfigTravelStayDTO> dtoList);

    /**
     * 根据版本id获取差旅住宿标准配置
     *
     * @param versionId 版本 ID
     * @return {@link List }<{@link CostConfigTravelStayVO }>
     */
    List<CostConfigTravelStayVO> getCostConfigTravelStaysByVersionId(Long versionId);
}
