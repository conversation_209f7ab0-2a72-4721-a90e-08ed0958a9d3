package com.gok.pboot.pms.cost.entity.vo;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 收入结算明细列表VO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class CostIncomeSettlementDetailExportVO {

    /**
     * 结算开始日期
     */
    @ExcelProperty("结算开始日期")
    private String startDate;

    /**
     * 结算截止日期
     */
    @ExcelProperty("结算截止日期")
    private String endDate;

    /**
     * 姓名
     */
    @ExcelProperty("姓名")
    private String userName;

    /**
     * 工号
     */
    @ExcelProperty("工号")
    private String workCode;

    /**
     * 测算含税金额
     */
    @ExcelProperty("测算含税金额")
    private BigDecimal estimatedInclusiveAmountTax;

    /**
     * 结算含税金额
     */
    @ExcelProperty("结算含税金额")
    private BigDecimal budgetAmountIncludedTax;

    /**
     * 税率OA字典Txt
     */
    @ExcelProperty("税率")
    private String taxRateTxt;

    /**
     * 结算不含税金额
     */
    @ExcelProperty("结算不含税金额")
    private BigDecimal budgetAmountExcludingTax;

    /**
     * 备注说明
     */
    @ExcelProperty("备注说明")
    private String remarksDesc;

    /**
     * 归档日期
     */
    @ExcelProperty("归档日期")
    private String filingTime;

    /**
     * OA结算单编号
     */
    @ExcelProperty("OA结算单编号")
    private String requestNumber;

    /**
     * 结算明细编号
     */
    @ExcelProperty("结算明细编号")
    private String settlementDetailsNumber;

    /**
     * 结算单编号
     */
    @ExcelProperty("结算单编号")
    private String settlementNumber;

    /**
     * 数据来源Txt
     */
    @ExcelProperty("数据来源")
    private String dataSourcesTxt;

    /**
     * 审批状态Txt
     */
    @ExcelProperty("审批状态")
    private String approvalStatusTxt;

    public static CostIncomeSettlementDetailExportVO from(CostIncomeSettlementDetailVO detailVO){
        CostIncomeSettlementDetailExportVO vo = new CostIncomeSettlementDetailExportVO();
        BeanUtil.copyProperties(detailVO,vo);
        vo.setStartDate(detailVO.getStartDate().toString());
        vo.setEndDate(detailVO.getEndDate().toString());
        return vo;
    }
}
