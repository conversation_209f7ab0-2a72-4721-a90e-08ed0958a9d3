package com.gok.pboot.pms.service;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.R;
import com.gok.pboot.pms.entity.domain.ProjectBusinessMilestones;
import com.gok.pboot.pms.entity.vo.OaFileInfoVo;
import com.gok.pboot.pms.entity.vo.ProjectBusinessMilestonesVo;
import com.gok.pboot.pms.entity.vo.ProjectOverViewInnerVO;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
 * 项目商务里程碑（OA同步）
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-02-23 10:46:40
 */
public interface IProjectBusinessMilestonesService extends IService<ProjectBusinessMilestones> {

    /**
     * 通过项目Id查询项目商务里程碑
     *
     * @param projectId 项目ID
     * @return {@link R}<{@link ProjectOverViewInnerVO}>>
     */
    @ApiOperation(value = "通过项目Id查询项目商务里程碑", notes = "通过项目Id查询项目商务里程碑")
    R<List<ProjectBusinessMilestonesVo>> getListByProjectId(Long projectId);

    /**
     * 通过Id获取佐证材料
     *
     * @param id 商务里程碑ID
     * @return {@link R}<{@link List}<{@link OaFileInfoVo}>>
     */
    R<List<OaFileInfoVo>> getSupportMaterialsListById(Long id);

    /**
     * 根据类型发起流程
     *
     * @param requestMap 项目里程碑发起流程
     * @return String
     */
    R<String> doCreateRequest(JSONObject requestMap);

    /**
     * 定时器
     * 在里程碑预计完成日期前 7 天和当天均推送消息提醒
     *
     * @return {@link ApiResult}
     */
    ApiResult<String> remind();

}

