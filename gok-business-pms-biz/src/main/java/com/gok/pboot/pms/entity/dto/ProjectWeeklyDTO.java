package com.gok.pboot.pms.entity.dto;


import com.gok.pboot.pms.common.base.PageRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;


/**
 * 项目周报表dto
 * 
 * <AUTHOR>
 * @LocalDateTime 2023-07-11 17:05:26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectWeeklyDTO extends PageRequest {

	/**
	 * ID id
	 */
	private Long id;
	/**
	 * 项目id
	 */
	@NotNull(message = "项目id不能为空")
	private Long projectId;
	/**
	 * '项目名称'
	 */
	@NotBlank(message = "项目名称不能为空")
	private String projectName;
	/**
	 * '业务归属部门'
	 */
	//@NotBlank(message = "业务归属部门不能为空")
	private String projectDepartment;

	/**
	 * '业务归属部门id'
	 */
	private Long projectDepartmentId;

	/**
	 * 汇报周期-开始
	 */
	@NotNull(message = "汇报周期开始时间不能为空")
	private LocalDate reportStart;
	/**
	 * 汇报周期-结束
	 */
	@NotNull(message = "汇报周期结束时间不能为空")
	private LocalDate reportEnd;

	/**
	 * 汇报周期
	 */
	private String reportStartEnd;
	/**
	 * 汇报人id
	 */
	@NotNull(message = "汇报人id不能为空")
	private Long reportUserId;
	/**
	 * 汇报人
	 */
	@NotNull(message = "汇报人姓名不能为空")
	private String reportUser;
	/**
	 * 本周进展情况
	 */
	@NotNull(message = "本周进展情况不能为空")
	@Length(max = 500, message = "本周进展情况长度不能超过500")
	private String currentWorkProgress;

	/**
	 * 当前项目进度
	 */
	private String currentProgress;
	/**
	 * 当期新增工时（人天）
	 */
	private BigDecimal currentHours;
	/**
	 * 累计工时（人天）
	 */
	private BigDecimal totalHours;
	/**
	 * 下周工作计划
	 */
	@NotNull(message = "下周工作计划不能为空")
	@Length(max = 500, message = "下周工作计划长度不能超过500")
	private String nextWorkPlan;
	/**
	 * 需配合支撑事项
	 */
	@Length(max = 500, message = "需配合支撑事项长度不能超过500")
	private String needSupportItem;
	/**
	 * 文件id集合，用逗号分割
	 */
	@Length(max = 255, message = "文件id集合长度不能超过255")
	private String docIds;

	/**
	 * 文件名集合
	 */
	@Length(max = 512, message = "文件名集合长度不能超过512")
	private String docNames;

	/**
	 * 周报风险
	 */
	@Length(max = 500, message = "周报风险长度不能超过500")
	private String weeklyRisk;

	/**
	 * 周报风险id集合
	 */
	private String weeklyRiskIds;


	/**
	 * 备注
	 */
	@Length(max = 500, message = "周报风险长度不能超过500")
	private String remark;


}
