package com.gok.pboot.pms.enumeration;

/**
 * 操作类型状态枚举
 **/
public enum OperationTypeEnum implements ValueEnum<Integer> {
    /**
     * 移除参与人
     */
    YC(0, "移除参与人"),
    /**
     * 添加参与人
     */
    TJ(1, "添加参与人"),
    /**
     * 状态变更
     */
    ZTBG(2, "状态变更"),
    /**
     * 移除负责人
     */
    YCFZR(3, "移除负责人"),
    /**
     * 添加负责人
     */
    TJFZR(4, "添加负责人");

    //值
    private Integer  value;
    //名称
    private String name;

    OperationTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    /**
     * 获取值
     *
     * @return Integer
     */
    @Override
    public Integer getValue() {
        return value;
    }

    /**
     * 获取名称
     *
     * @return String
     */
    @Override
    public String getName() {
        return name;
    }

    public static String getNameByVal(Integer value) {
        for (OperationTypeEnum statusEnum : OperationTypeEnum.values()) {
            if (statusEnum.value.equals(value)) {
                return statusEnum.name;
            }
        }
        return "";
    }
    public static Integer getValByName(String name) {
        for (OperationTypeEnum statusEnum : OperationTypeEnum.values()) {
            if (statusEnum.getName().equals(name)) {
                return statusEnum.getValue();
            }
        }
        return null;
    }
}
