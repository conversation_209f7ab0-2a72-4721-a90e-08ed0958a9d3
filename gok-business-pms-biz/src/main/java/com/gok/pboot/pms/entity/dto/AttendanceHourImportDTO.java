package com.gok.pboot.pms.entity.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AttendanceHourImportDTO {

    /**
     * 人员名称
     */
    @ExcelProperty(value = "姓名", index = 0)
    private String userRealName;
    
    /**
     * 应出勤天数
     */
    @ExcelProperty(value = "应出勤天数", index = 1)
    private String cwDueAttendance;
    
    /**
     * 出勤天数
     */
    @ExcelProperty(value = "出勤天数", index = 2)
    private String attendanceDays;
    /**
     * 工作日正常工时（天）
     */
    @ExcelProperty(value = "工作日正常工时（天）", index = 3)
    private String normalWorkDays;
    /**
     * 工作日调休工时（天）
     */
    @ExcelProperty(value = "工作日调休工时（天）", index = 4)
    private String ompensatoryDays;
    /**
     * 休息日加班工时（天）
     */
    @ExcelProperty(value = "休息日加班工时（天）", index = 5)
    private String restWorkDays;
    /**
     * 节假日加班工时（天）
     */
    @ExcelProperty(value = "节假日加班工时（天）", index = 6)
    private String holidaysWorkDays;
    /**
     * 人员归属部门
     */
    @ExcelProperty(value = "人员归属部门", index = 7)
    private String deptName;
    /**
     * 备注
     */
    @ExcelProperty(value = "备注", index = 8)
    private String remark;

    /**
     * 主键
     */
    private Long id;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 项目编号
     */
    private String projectCode;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目状态
     */
    private String projectStatus;

    /**
     * 收入归属（一级）部门ID
     */
    private String revenueDeptName;

    /**
     * 收入归属（一级）部门ID
     */
    private Long revenueDeptId;

    /**
     * 项目分摊工时11
     */
    private String projectConsumed;

    /**
     * 税前工资
     */
    private String grossPay;

    /**
     * 社保
     */
    private String socialSecurity;

    /**
     * 公积金
     */
    private String accumulationFund;

    /**
     * 工资成本
     */
    private String wageCost;

    /**
     * 社保成本
     */
    private String socialSecurityCost;

    /**
     * 公积金成本
     */
    private String accumulationFundCost;

    /**
     * 合计
     */
    private String total;

    /**
     * 项目成本合计
     */
    private String totalProjectCost;

    /**
     * 工资发放地
     */
    private String payingPlace;

    /**
     * 人员归属部门Id
     */
    private Long deptId;

}