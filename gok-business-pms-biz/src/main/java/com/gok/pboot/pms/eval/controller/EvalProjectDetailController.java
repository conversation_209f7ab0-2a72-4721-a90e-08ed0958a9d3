package com.gok.pboot.pms.eval.controller;

import com.gok.module.file.entity.SysFile;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.eval.entity.dto.EvalProjectOverviewDTO;
import com.gok.pboot.pms.eval.service.IEvalCommendationLetterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 项目评价前端控制器
 *
 * <AUTHOR>
 * @menu 项目评价
 * @date 2025/05/16
 */
@Slf4j
@Api(tags = "项目评价")
@RestController
@RequestMapping("/evalProjectDetail")
@RequiredArgsConstructor
public class EvalProjectDetailController {

    private final IEvalCommendationLetterService echoCommendationLetterService;

    /**
     * 表扬信上传
     *
     * @param request
     * @return 操作结果
     */
    @ApiOperation("表扬信上传")
    @PutMapping("/upload/commendationLetters")
    public ApiResult<Long> uploadCommendationLetters(@RequestBody EvalProjectOverviewDTO request) {
        return ApiResult.success(echoCommendationLetterService.uploadCommendationLetters(request.getProjectId(), request.getCommendationLetter()));
    }


    /**
     * 获取表扬信
     *
     * @param projectId 项目ID
     * @return 操作结果
     */
    @ApiOperation("获取表扬信")
    @GetMapping("/commendationLetters/{projectId}")
    public ApiResult<List<SysFile>> getCommendationLetters(@PathVariable("projectId") Long projectId) {
        return ApiResult.success(echoCommendationLetterService.getCommendationLetters(projectId));
    }

}