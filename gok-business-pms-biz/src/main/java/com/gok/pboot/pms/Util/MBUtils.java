package com.gok.pboot.pms.Util;

import cn.hutool.core.text.StrPool;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.stream.Stream;

/**
 * MyBatis相关工具类
 *
 * <AUTHOR>
 */
@UtilityClass
public class MBUtils {

    /**
     * int字符串转数组
     * @param intsStr int字符串
     * @return int数组
     */
    public static int[] splitToIntArray(String intsStr) {
        return splitToIntArray(intsStr, StrPool.COMMA);
    }

    /**
     * int字符串转数组
     * @param intStr int字符串
     * @param delimiter 分割符
     * @return int数组
     */
    public static int[] splitToIntArray(String intStr, String delimiter) {
        if (StringUtils.isBlank(intStr)) {
            return new int[]{ Integer.MAX_VALUE };
        }

        return Stream.of(intStr.split(delimiter)).mapToInt(s -> NumberUtils.toInt(s, Integer.MAX_VALUE)).toArray();
    }

    /**
     * long字符串转数组
     * @param longsStr long字符串
     * @return long数组
     */
    public static long[] splitToLongArray(String longsStr) {
        return splitToLongArray(longsStr, StrPool.COMMA);
    }

    /**
     * long字符串转数组
     * @param longsStr long字符串
     * @param delimiter 分割符
     * @return long数组
     */
    public static long[] splitToLongArray(String longsStr, String delimiter) {
        if (StringUtils.isBlank(longsStr)) {
            return new long[]{ Long.MAX_VALUE };
        }

        return Stream.of(longsStr.split(delimiter)).mapToLong(s -> NumberUtils.toLong(s, Long.MAX_VALUE)).toArray();
    }
}
