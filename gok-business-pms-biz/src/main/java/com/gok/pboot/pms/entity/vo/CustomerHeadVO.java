package com.gok.pboot.pms.entity.vo;


import com.gok.bcp.upms.vo.SysMenuVo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerHeadVO {

    private Long id;

    /**
     * 客户名称
     */
    private String khmc;
    /**
     * 客户分级
     */
    private Integer khfj;
    /**
     * 客户分级Txt
     */
    private String khfjTxt;

    /**
     * 客户所在地
     */
    private Integer khszd;
    /**
     * 客户所在地Txt
     */
    private String khszdTxt;

    /**
     * 行业"
     */
    private Integer khxyyj;

    /**
     * 行业Txt
     */
    private String khxyyjTxt;
    /**
     * 归属业务部门
     */
    private Long gsywbmejbm;

    /**
     * 归属业务部门Txt
     */
    private String gsywbmejbmTxt;

    /**
     * 客户经理id
     */
    private String khjl;
    /**
     * 客户经理名字
     */
    private String khjlxm;

    /**
     * true=已关注  false未关注
     */
    private Boolean isAttention=false;

    /**
     * 详情页签权限
     *
     */
    private List<SysMenuVo> sysMenuVoList;
}