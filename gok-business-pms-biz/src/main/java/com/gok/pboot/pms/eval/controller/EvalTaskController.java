package com.gok.pboot.pms.eval.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.module.excel.api.annotation.ResponseExcel;
import com.gok.pboot.common.security.annotation.Inner;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.eval.entity.dto.EvalTaskCalibrationDTO;
import com.gok.pboot.pms.eval.entity.dto.EvalTaskDistributionDTO;
import com.gok.pboot.pms.eval.entity.dto.EvalTaskListDTO;
import com.gok.pboot.pms.eval.entity.dto.EvalTaskSubmitDTO;
import com.gok.pboot.pms.eval.entity.vo.*;
import com.gok.pboot.pms.eval.service.IEvalTaskService;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 工单评价
 * <AUTHOR>
 * @create 2025/5/8
 * @menu 工单评价
 **/
@Validated
@RestController
@AllArgsConstructor
@RequestMapping("/task/evaluation")
public class EvalTaskController {

    private final IEvalTaskService costDeliverTaskEvaluationService;

    /**
     * 工单评价提交
     * @param dto
     * @return
     */
    @PostMapping("/submit")
    public ApiResult<String> submitEvaluation(@RequestBody @Valid EvalTaskSubmitDTO dto) {
        costDeliverTaskEvaluationService.submitEvaluation(dto);
        return ApiResult.successMsg("评价提交成功");
    }

    /**
     * 工单评价详情
     * @param taskId
     * @return
     */
    @GetMapping("/detail/{taskId}")
    public ApiResult<EvalTaskDetailVO> evaluationDetail(@PathVariable Long taskId) {
        return ApiResult.success(costDeliverTaskEvaluationService.evaluationDetail(taskId));
    }

    /**
     * 工单评价列表
     * @param pageRequest
     * @param dto
     * @return
     */
    @GetMapping("/findPage")
    public ApiResult<Page<EvalTaskListVO>> findPage(PageRequest pageRequest,EvalTaskListDTO dto) {
        return ApiResult.success(costDeliverTaskEvaluationService.findPage(pageRequest,dto));
    }

    /**
     * 导出售后工单评价列表
     * @param dto     查询条件
     * @return {@link ApiResult}<{@link Page}<{@link EvalTaskListVO}>>
     */
    @ResponseExcel(name = "售后工单评价")
    @GetMapping("/export")
    public List<EvalTaskListVO> export(EvalTaskListDTO dto) {
        return costDeliverTaskEvaluationService.export(dto);
    }

    /**
     * 导出售前工单评价列表
     * @param dto     查询条件
     * @return {@link ApiResult}<{@link Page}<{@link EvalTaskListVO}>>
     */
    @ResponseExcel(name = "售前工单评价")
    @GetMapping("/pre/export")
    public List<EvalPreTaskExportVO> preExport(EvalTaskListDTO dto) {
        return costDeliverTaskEvaluationService.preExport(dto);
    }

    /**
     * 查询项目人员售后工单评价分布
     * @param pageRequest
     * @param dto
     * @return
     */
    @GetMapping("/project/after/eval/distribution")
    public ApiResult<Page<EvalTaskDistributionAfterVO>> getAfterEvalDistribution(PageRequest pageRequest, EvalTaskDistributionDTO dto) {
        return ApiResult.success(costDeliverTaskEvaluationService.getAfterEvalDistribution(pageRequest,dto));
    }

    /**
     * 导出项目人员售后工单评价分布
     * @param dto     查询条件
     * @return {@link ApiResult}<{@link Page}<{@link EvalTaskDistributionAfterVO}>>
     */
    @ResponseExcel(name = "项目人员评价分布-售后交付工单")
    @GetMapping("/export/after/eval/distribution")
    public List<EvalTaskDistributionAfterVO> exportAfterEvalDistribution(EvalTaskDistributionDTO dto) {
        return costDeliverTaskEvaluationService.exportAfterEvalDistribution(dto);
    }

    /**
     * 查询项目人员售后工单评价分布明细
     * @param dto
     * @param pageRequest
     * @return
     */
    @GetMapping("/project/after/eval/distribution/detail")
    public ApiResult<Page<EvalTaskDistributionAfterDetailVO>> getAfterEvalDistributionDetail(PageRequest pageRequest,EvalTaskDistributionDTO dto) {
        return ApiResult.success(costDeliverTaskEvaluationService.getAfterEvalDistributionDetail(pageRequest,dto));
    }

    /**
     * 查询项目人员售前工单评价分布
     * @param pageRequest
     * @param dto
     * @return
     */
    @GetMapping("/project/pre/eval/distribution")
    public ApiResult<Page<EvalTaskDistributionPreVO>> getPreEvalDistribution(PageRequest pageRequest, EvalTaskDistributionDTO dto) {
        return ApiResult.success(costDeliverTaskEvaluationService.getPreEvalDistribution(pageRequest,dto));
    }

    /**
     * 导出项目人员售前工单评价分布
     * @param dto     查询条件
     * @return {@link ApiResult}<{@link Page}<{@link EvalTaskDistributionPreVO}>>
     */
    @ResponseExcel(name = "项目人员评价分布-售前交付工单")
    @GetMapping("/export/pre/eval/distribution")
    public List<EvalTaskDistributionPreVO> exportPreEvalDistribution(EvalTaskDistributionDTO dto) {
        return costDeliverTaskEvaluationService.exportPreEvalDistribution(dto);
    }

    /**
     * 查询项目人员售前工单评价分布明细
     * @param pageRequest
     * @param dto
     * @return
     */
    @GetMapping("/project/pre/eval/distribution/detail")
    public ApiResult<Page<EvalTaskDistributionPreDetailVO>> getPreEvalDistributionDetail(PageRequest pageRequest,EvalTaskDistributionDTO dto) {
        return ApiResult.success(costDeliverTaskEvaluationService.getPreEvalDistributionDetail(pageRequest,dto));
    }

    /**
     * 获取工单评价校准详情
     * @param dto
     * @return
     */
    @GetMapping("/calibration/detail")
    public ApiResult<EvalTaskCalibrationDetailVO> getCalibrationDetail(EvalTaskDistributionDTO dto) {
        return ApiResult.success(costDeliverTaskEvaluationService.getCalibrationDetail(dto));
    }

    /**
     * 工单评价调整
     * @param dto
     * @return
     */
    @PostMapping("/calibration")
    public ApiResult<String> calibration(@RequestBody @Valid EvalTaskCalibrationDTO dto) {
        costDeliverTaskEvaluationService.calibration(dto);
        return ApiResult.successMsg("调整成功");
    }

    /**
     * 工单评价调整更改校准状态（校准后提交、通过、退回）
     * @param dto
     * @return
     */
    @PostMapping("/changeCalibrationStatus")
    public ApiResult<String> changeCalibrationStatus(@RequestBody @Valid EvalTaskCalibrationDTO dto) {
        costDeliverTaskEvaluationService.changeCalibrationStatus(dto);
        return ApiResult.successMsg("调整成功");
    }

    /**
     * 获取项目人员整体评价分布
     * @param projectId
     * @return
     */
    @GetMapping("/project/overall/distribution/{projectId}")
    public ApiResult<EvalProjectOverallStaffVO> getProjectOverallDistribution(@PathVariable("projectId") Long projectId) {
        return ApiResult.success(costDeliverTaskEvaluationService.getProjectOverallDistribution(projectId));
    }

    /**
     * 发送未评价的工单提醒
     ** @return 操作结果
     */
    @GetMapping("/sendAbnormalMsg")
    @Inner(value = false)
    public ApiResult<String> taskSendAbnormalMsg() {
        costDeliverTaskEvaluationService.taskSendAbnormalMsg();
        return ApiResult.success("发送成功");
    }
}
