package com.gok.pboot.pms.cost.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 人员级别单价配置 VO
 *
 * <AUTHOR>
 * @date 2025/01/08
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class CostConfigLevelPriceVO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 职务id
     */
    private Long jobActivityId;

    /**
     * 岗位类型名称
     */
    private String personnelType;

    /**
     * 地域
     */
    private String region;

    /**
     * 人员级别
     */
    private Long personnelLevel;

    private String personnelLevelStr;

    /**
     * 人员固定薪资单价（元/天）
     */
    private BigDecimal levelPrice;

    /**
     * 人员基本薪资单价（元/天）
     */
    private BigDecimal baseSalaryPrice;

    /**
     * 工资（元/天）
     */
    private BigDecimal salaryPerDay;

    /**
     * 社保（元/天）
     */
    private BigDecimal socialSecurityPerDay;

    /**
     * 公积金（元/天）
     */
    private BigDecimal housingFundPerDay;

    /**
     * 残保金（元/天）
     */
    private BigDecimal disabilityLevyPerDay;

    /**
     * 版本名称
     */
    private String versionName;


    public static void initVo(CostConfigLevelPriceVO vo) {
        if (vo.getLevelPrice() == null) {
            vo.setLevelPrice(BigDecimal.ZERO);
        }
        if (vo.getBaseSalaryPrice() == null) {
            vo.setBaseSalaryPrice(BigDecimal.ZERO);
        }
        if (vo.getSalaryPerDay() == null) {
            vo.setSalaryPerDay(BigDecimal.ZERO);
        }
        if (vo.getSocialSecurityPerDay() == null) {
            vo.setSocialSecurityPerDay(BigDecimal.ZERO);
        }
        if (vo.getHousingFundPerDay() == null) {
            vo.setHousingFundPerDay(BigDecimal.ZERO);
        }
        if (vo.getDisabilityLevyPerDay() == null) {
            vo.setDisabilityLevyPerDay(BigDecimal.ZERO);
        }
    }
}

