package com.gok.pboot.pms.service;

import com.gok.pboot.pms.entity.dto.BaseSendMsgDTO;
import com.gok.pboot.pms.entity.dto.BaseSendMsgOutDTO;

import javax.validation.Valid;
import java.util.List;

/**
 * BCP 消息服务
 *
 * <AUTHOR>
 * @date 2025/01/16
 */
public interface BcpMessageService {
    /**
     * 发送消息
     *
     * @param sendMsgDTO 发送消息 DTO
     */
    void sendMsg(@Valid BaseSendMsgDTO sendMsgDTO);

    /**
     * 批量发送消息
     *
     * @param baseSendMsgDTOList base send msg dto list
     */
    void batchSendMsg(List<BaseSendMsgDTO> baseSendMsgDTOList);

    /**
     * 发送短信消息
     *
     * @param dto DTO
     */
    void sendSmsMsg(@Valid BaseSendMsgOutDTO dto);
}
