package com.gok.pboot.pms.enumeration;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum TechnologyTypeEnum {

    SOFTWARE_DEVELOPMENT(2, "软件开发"),
    ERP_DELIVERY(3, "ERP交付"),
    ICT_INTEGRATION(0, "ICT集成"),
    DATA_GOVERNANCE(4, "数据治理"),
    SECURITY_SERVICES(1, "安全服务"),
    Other(5, "其他"),
    ;

    /**
     * 技术类型编码
     */
    private final Integer code;

    /**
     * 技术类型名称
     */
    private final String name;

    public static String getNameByVal(Integer value) {
        for (TechnologyTypeEnum technologyTypeEnum : TechnologyTypeEnum.values()) {
            if (technologyTypeEnum.code.equals(value)) {
                return technologyTypeEnum.name;
            }
        }
        return StrUtil.EMPTY;
    }

}