package com.gok.pboot.pms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.common.user.PigxUser;
import com.gok.components.data.handler.PermCodeHolder;
import com.gok.pboot.common.core.util.WebUtils;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.Util.BaseEntityUtils;
import com.gok.pboot.pms.Util.CollectionUtils;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.common.exception.ServiceException;
import com.gok.pboot.pms.entity.SpecialSetting;
import com.gok.pboot.pms.entity.SysDept;
import com.gok.pboot.pms.entity.domain.Roster;
import com.gok.pboot.pms.entity.dto.SpecialSettingAddDTO;
import com.gok.pboot.pms.entity.dto.UserPmsDTO;
import com.gok.pboot.pms.entity.vo.SpecialSettingUserVO;
import com.gok.pboot.pms.entity.vo.SpecialSettingVO;
import com.gok.pboot.pms.entity.vo.SysDeptOutVO;
import com.gok.pboot.pms.entity.vo.SysUserDataScopeVO;
import com.gok.pboot.pms.handler.ProjectScopeHandle;
import com.gok.pboot.pms.mapper.RosterMapper;
import com.gok.pboot.pms.mapper.SpecialSettingMapper;
import com.gok.pboot.pms.service.ISpecialSettingService;
import com.gok.pboot.pms.service.fegin.CenterDataScopeService;
import com.gok.pboot.pms.service.fegin.CenterDeptService;
import com.gok.pboot.pms.service.fegin.CenterUserService;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import java.sql.Timestamp;
import java.text.Collator;
import java.util.*;
import java.util.stream.Collectors;

/**
 * - 特殊配置服务 -
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
@AllArgsConstructor
@Slf4j
public class SpecialSettingServiceImpl implements ISpecialSettingService {

    private final SpecialSettingMapper mapper;

    private final CenterUserService centerUserService;
    private final CenterDeptService centerDeptService;
    private final CenterDataScopeService centerDataScopeService;
    private final ProjectScopeHandle projectScopeHandle;

    private final RosterMapper rosterMapper;

    @Override
    public Page<SpecialSettingVO> queryPage(PageRequest pageRequest, Map<String, Object> filter) {
        List<SysDept> topDept = getTopDeptIfCurrUserIsNotAdmin();
        log.info("topDept list: {}",topDept);
        if (ObjectUtil.isEmpty(filter.get("topDeptId"))&& CollUtil.isNotEmpty(topDept)){
            filter.put("deptList",topDept.stream().map(SysDept::getDeptId).collect(Collectors.toList()));
        }
        Page<SpecialSetting> poPage =
                mapper.findPage(new Page<>(pageRequest.getPageNumber(), pageRequest.getPageSize()), filter);
        List<SpecialSetting> poList;
        List<Long> userIds;
        Map<Long, String> userIdAndNameMap, deptIdAndNameMap;
        List<SpecialSettingVO> resultList;
        Page<SpecialSettingVO> result = new Page<>();

        if (poPage.getTotal() == 0){
            return result;
        }
        poList = poPage.getRecords();
        userIds = poList.stream().map(SpecialSetting::getUserId).collect(Collectors.toList());
        UserPmsDTO userPmsDTO = new UserPmsDTO();
        userPmsDTO.setUserIds(userIds);
//        List<SysUserOutVO> userOutVOList = centerUserService.getUserListByMultiParameterPms(userPmsDTO).unpack().orElse(new ArrayList<>())
//                .stream().distinct().filter(u -> u.getName() != null).collect(Collectors.toList());
//        userIdAndNameMap = BaseEntityUtils.mapCollectionToMap(
//                userOutVOList,
//                SysUserOutVO::getUserId,
//                SysUserOutVO::getName,
//                (a, b) -> a
//        );
        userIdAndNameMap = rosterMapper.selectBatchIds(userIds)
                .stream().collect(Collectors.toMap(Roster::getId, Roster::getAliasName));

        List<SysDeptOutVO> deptOutVOList = centerDeptService.getOrgStructList().unpack().orElse(new ArrayList<>())
                .stream().filter(d -> d.getName() != null).collect(Collectors.toList());
        deptIdAndNameMap = CollectionUtils.isEmpty(topDept) ? BaseEntityUtils.mapCollectionToMap(
                deptOutVOList,
                SysDeptOutVO::getDeptId,
                SysDeptOutVO::getName
        ) : BaseEntityUtils.mapCollectionToMap(
                topDept,
                SysDept::getDeptId,
                SysDept::getName
        );

        resultList = BaseEntityUtils.mapCollectionToList(
                poList, po -> SpecialSettingVO.from(
                        po,
                        userIdAndNameMap.get(po.getUserId()),
                        deptIdAndNameMap.get(po.getTopDeptId())
                )
        );
        BeanUtils.copyProperties(poPage, result);
        List<SpecialSettingVO> collect = resultList.stream()
                .peek(setting -> {
                    if (setting.getTopDeptName() == null){
                        setting.setTopDeptName("");
                    }
                    if (setting.getUserRealName() == null){
                        setting.setUserRealName("");
                    }
                    if (setting.getCtime() == null){
                        setting.setCtime(new Timestamp(System.currentTimeMillis()));
                    }
                })
                .sorted(Comparator.comparing(SpecialSettingVO::getCtime).reversed()
                        .thenComparing(SpecialSettingVO::getTopDeptName,Collator.getInstance(Locale.CHINA))
                        .thenComparing(SpecialSettingVO::getUserRealName,Collator.getInstance(Locale.CHINA))
                )
                .collect(Collectors.toList());
        result.setRecords(collect);

        return result;
    }

    @Override
    public void add(SpecialSettingAddDTO dto) {
        List<Long> userIds = dto.getUserIds();
        log.info("userIds result:{}",userIds);
//        Integer userIdsCount;
//        Set<Long> userIdsAvailable;
//        SysDept topDept = getTopDeptIfCurrUserIsNotAdmin();
//        Long topDeptId;
        List<SpecialSetting> addList;

//        if (topDept != null){
//            topDeptId = topDept.getDeptId();
//            // 用户是单一部门的管理员，因此过滤掉除这个部门外的人员
//            userIdsAvailable = BaseEntityUtils.mapCollectionToSet(remoteUserService.findByDeptIds(
//                    ImmutableList.of(topDeptId),
//                    SecurityConstants.FROM_IN,
//                    2L
//            ).getData(), SysUserVO::getUserId);
//            userIds = userIds.stream()
//                    .filter(userIdsAvailable::contains)
//                    .collect(Collectors.toList());
//        }
//        if (userIds.isEmpty()){
//            return;
//        }
//        userIdsCount = mapper.countByUserIds(userIds);
//        if (userIdsCount == userIds.size()){
//            throw new ServiceException("所选的用户均已存在，无法添加");
//        }
        List<SpecialSetting> specialSettings = mapper.selectList(Wrappers.<SpecialSetting>lambdaQuery().eq(SpecialSetting::getDelFlag, 0));
        List<Long> collect = specialSettings.stream().map(SpecialSetting::getUserId).collect(Collectors.toList());
        List<Long> userIdList = userIds.stream().filter(x -> !collect.contains(x)).collect(Collectors.toList());
        log.info("userIdList1 result:{}",userIdList);
        if (CollUtil.isEmpty(userIdList)) {return;}
        // 远程调用 此处仅用到部门信息的部门id、部门父id
        Map<Long, SysDept> deptMap = centerDeptService.getOrgStructList().unpack().orElse(new ArrayList<>())
                .stream().collect(Collectors.toMap(SysDeptOutVO::getDeptId, v -> BeanUtil.copyProperties(v, SysDept.class)));
        List<Long> deptIds = deptMap.values().stream().map(SysDept::getDeptId).collect(Collectors.toList());
        userIdList = userIdList.stream().filter(u -> !deptIds.contains(u)).collect(Collectors.toList());
        if (CollUtil.isEmpty(userIdList)) {return;}
        log.info("userIdList2 result:{}",userIdList);

//        // 远程调用 此处仅用到用户信息的用户id、部门id
//        UserPmsDTO userPmsDTO = new UserPmsDTO();
//        userPmsDTO.setUserIds(userIdList);
//        List<SysUser> users = centerUserService.getUserListByMultiParameterPms(userPmsDTO).unpack().orElse(new ArrayList<>())
//                .stream().map(SysUser::from).collect(Collectors.toList());
//
//        addList = Lists.newArrayListWithCapacity(userIdList.size());
//        log.info("users result:{}",users);
//        users.forEach(user->{
//            Long userToDeptId = user.getDeptId();
//            if (userToDeptId != null) {
//                // 尝试消除空指针问题
//                Long deptId;
//                Long deptIdNow = userToDeptId;
//                SysDept dept;
//                dept = deptMap.get(deptIdNow);
//                deptIdNow = dept.getParentId();
//                while(deptIdNow != -1){
//                    dept = deptMap.get(deptIdNow);
//                    deptIdNow = dept.getParentId();
//                }
//                deptId = dept.getDeptId();
//
//                if (ObjectUtil.isEmpty(deptId)) return;
//                addList.add(BaseBuildEntityUtil.buildSave(new SpecialSetting(user.getUserId(), deptId)));
//            }
//        });

        // 获取用户id-部门id map
        Map<Long, Long> userIdDeptIdMap = rosterMapper.selectBatchIds(userIdList)
                .stream().collect(Collectors.toMap(Roster::getId, Roster::getDeptId));

        addList = Lists.newArrayListWithCapacity(userIdList.size());
        for (Map.Entry<Long, Long> entry : userIdDeptIdMap.entrySet()) {
            Long userToDeptId = entry.getValue();
            if (userToDeptId != null) {
                // 尝试消除空指针问题
                Long deptId;
                Long deptIdNow = userToDeptId;
                SysDept dept;
                dept = deptMap.get(deptIdNow);
                deptIdNow = dept.getParentId();
                while(deptIdNow != -1){
                    dept = deptMap.get(deptIdNow);
                    deptIdNow = dept.getParentId();
                }
                deptId = dept.getDeptId();

                if (ObjectUtil.isEmpty(deptId)) {
                    return;
                }
                addList.add(BaseBuildEntityUtil.buildSave(new SpecialSetting(entry.getKey(), deptId)));
            }
        }
        mapper.batchSave(addList);
    }

    @Override
    public void delete(Long id) {
        mapper.deleteById(id);
    }

    @Override
    public List<SpecialSettingUserVO> findUserList() {
        Map<String, Object> filter = new HashMap<>();
        List<SysDept> topDept = getTopDeptIfCurrUserIsNotAdmin();
        if (ObjectUtil.isEmpty(filter.get("topDeptId"))&& CollUtil.isNotEmpty(topDept)){
            filter.put("deptList",topDept.stream().map(SysDept::getDeptId).collect(Collectors.toList()));
        }

        List<SpecialSettingUserVO> result = new ArrayList<>();
        List<Long> userIds;
//        Map<Long, String> userIdAndNameMap;
//        UserPmsDTO userPmsDTO = new UserPmsDTO();

        userIds = mapper.findUserList(filter);
        if(CollectionUtil.isEmpty(userIds)){
            //是空的
            SpecialSettingUserVO userVO = new SpecialSettingUserVO();
            result.add(userVO);
        }else{
            result = userIds.stream().map(e->{
                SpecialSettingUserVO userVO = new SpecialSettingUserVO();
                userVO.setUserId(e.toString());
                return userVO;
            }).collect(Collectors.toList());
        }

       /* userPmsDTO.setUserIds(userIds);
        centerUserService.get
        List<SysUserOutVO> userOutVOList = centerUserService.getUserListByMultiParameterPms(userPmsDTO).unpack().orElse(new ArrayList<>())
                .stream().distinct().filter(u -> u.getName() != null).collect(Collectors.toList());
        userIdAndNameMap = BaseEntityUtils.mapCollectionToMap(
                userOutVOList,
                SysUserOutVO::getUserId,
                SysUserOutVO::getName,
                (a, b) -> a
        );
        for (Long userId : userIds) {
            SpecialSettingUserVO user = SpecialSettingUserVO.from(userId,userIdAndNameMap);
            result.add(user);
        }

        if (result.isEmpty()) {
            SpecialSettingUserVO userVO = new SpecialSettingUserVO();
            result.add(userVO);
        }
*/
        return result;
    }

    /**
     * ~ 如果当前用户是管理员，返回null，否则返回当前用户的部门 ~
     * <AUTHOR>
     */
    @Nullable
    private List<SysDept> getTopDeptIfCurrUserIsNotAdmin() {
        PigxUser user = SecurityUtils.getUser();
        Long userId = user.getId();

        // 涉及角色权限，已修改
        //boolean result = SecurityUtils.hasAnyRole(PigXRole.ADMIN_PMS);
        //if (result) {
        //    return null;
        //}
        SysUserDataScopeVO dataScope = projectScopeHandle.findDataScope();
        if (dataScope.getIsAll()) {
            return new ArrayList<>();
        }

        SysUserDataScopeVO data;
        List<String> permissions = PermCodeHolder.get();
        data = centerDataScopeService.getByClientIdAndRoleId(
                WebUtils.getRequest().getHeader("Application"),
                SecurityUtils.getUser().getId(),
                permissions.get(0)
        ).getData();
        if(data == null || data.getIsAll()){
            return new ArrayList<>();
        }
        List<Long> deptIdList = data.getDeptIdList();

        Map<Long,SysDeptOutVO> deptOutVOMap =
                centerDeptService.getTopDeptByIds(deptIdList).unpack().<ServiceException>orElseThrow(()->{
            throw new ServiceException("获取用户一级部门失败，部门ID：" + deptIdList);}
        );

        // 放开所有权限
        //return deptOutVOMap.values().stream().distinct().map(SysDeptOutVO::toSysDept).collect(Collectors.toList());
        return new ArrayList<>();
    }
}
