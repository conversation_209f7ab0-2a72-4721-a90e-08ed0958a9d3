package com.gok.pboot.pms.entity.dto;

import com.gok.pboot.pms.common.base.PageRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

/**
 * 商机进展dto
 *
 * <AUTHOR>
 * @date 2023/11/24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BusinessProgressDTO extends PageRequest {

    /**
     * 商机/客户关键词 (模糊搜索: 商机名称、客户名称)
     */
    private String keyword;

    /**
     * 客户经理姓名 (模糊查询)
     */
    private String projectSalesperson;

    /**
     * 开始时间
     */
    private LocalDate startTime;

    /**
     * 结束时间
     */
    private LocalDate endTime;

    /**
     * 登录用户的用户id
     */
    private Long userId;

    /**
     * 商机id集合
     */
    private List<Long> businessIds;
}
