package com.gok.pboot.pms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.base.admin.dto.SalesReceiptCollectionRecordsDTO;
import com.gok.base.admin.feign.RemotePaymentService;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.Util.MoneyUtil;
import com.gok.pboot.pms.common.exception.ServiceException;
import com.gok.pboot.pms.entity.domain.SalesReceiptCollectionRecords;
import com.gok.pboot.pms.entity.vo.SalesReceiptCollectionRecordsVO;
import com.gok.pboot.pms.enumeration.CollectionMethodEnum;
import com.gok.pboot.pms.mapper.SalesReceiptCollectionRecordsMapper;
import com.gok.pboot.pms.service.ISalesReceiptCollectionRecordsService;
import com.gok.pboot.pms.service.ISalesReceiptService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 销售收款计划催款记录ServiceImpl
 *
 * <AUTHOR>
 * @since 2024-02-20
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SalesReceiptCollectionRecordsServiceImpl
        extends ServiceImpl<SalesReceiptCollectionRecordsMapper, SalesReceiptCollectionRecords>
        implements ISalesReceiptCollectionRecordsService {

    private final ISalesReceiptService salesReceiptService;

    private final RemotePaymentService remotePaymentService;

    /**
     * 催款记录
     *
     * @param salesReceiptId 销售收款id
     * @return {@link List}<{@link SalesReceiptCollectionRecordsVO}>
     */
    @Override
    public List<SalesReceiptCollectionRecordsVO> recordsList(Long salesReceiptId) {
        List<SalesReceiptCollectionRecordsVO> voList = new ArrayList<>();
        baseMapper.selectList(Wrappers.<SalesReceiptCollectionRecords>lambdaQuery()
                .eq(SalesReceiptCollectionRecords::getSalesReceiptId, salesReceiptId)
                .orderByDesc(SalesReceiptCollectionRecords::getCreateTime)
        ).forEach(s -> {
            SalesReceiptCollectionRecordsVO vo = BeanUtil.copyProperties(s, SalesReceiptCollectionRecordsVO.class);
            vo.setCollectionMethodTxt(EnumUtils.getNameByValue(CollectionMethodEnum.class, s.getCollectionMethod()));
            vo.setCollectionAmount(MoneyUtil.getInstance().transType(s.getCollectionAmount()));
            voList.add(vo);
        });
        return voList;
    }


    /**
     * 新增记录
     *
     * @param dto {@link SalesReceiptCollectionRecordsDTO}
     * @return {@code true or false}
     */
    @Override
    public boolean saveRecords(SalesReceiptCollectionRecordsDTO dto) {
        // 数字财务
        if (SecurityUtils.getUser() != null) {
            if (Boolean.FALSE.equals(remotePaymentService.saveRecords(dto).getData())) {
                throw new ServiceException("数字财务新增催收记录操作异常");
            }
        }
        return baseMapper.insert(BeanUtil.copyProperties(dto, SalesReceiptCollectionRecords.class)) > NumberUtils.INTEGER_ZERO;
    }

    @Override
    public List<SalesReceiptCollectionRecordsVO> getContractCollectionVoList(Long id) {
        List<SalesReceiptCollectionRecordsVO> salesReceiptCollectionRecordsVOS = new ArrayList<>();
        List<com.gok.base.admin.vo.SalesReceiptCollectionRecordsVO> data =
                remotePaymentService.salesReceiptRecordsList(id).getData();
        if(CollUtil.isNotEmpty(data)){
            salesReceiptCollectionRecordsVOS= BeanUtil.copyToList(data, SalesReceiptCollectionRecordsVO.class);
        }
        return salesReceiptCollectionRecordsVOS ;
    }
}
