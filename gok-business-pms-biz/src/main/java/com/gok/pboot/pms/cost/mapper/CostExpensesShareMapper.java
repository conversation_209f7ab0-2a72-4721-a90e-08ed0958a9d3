package com.gok.pboot.pms.cost.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.cost.entity.domain.CostExpensesShare;
import com.gok.pboot.pms.cost.entity.dto.CostExpensesShareListDto;
import com.gok.pboot.pms.cost.entity.vo.CostExpensesShareDetailsVO;
import com.gok.pboot.pms.cost.entity.vo.CostExpensesShareSumDataVO;
import com.gok.pboot.pms.cost.entity.vo.CostExpensesShareSummaryListVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 人力外包-费用分摊映射器
 * <AUTHOR>
 */
@Mapper
public interface CostExpensesShareMapper extends BaseMapper<CostExpensesShare> {

    /**
     * 查询费用分摊汇总数据
     * @param dto
     * @return
     */
    CostExpensesShareSumDataVO selSummary(@Param("dto") CostExpensesShareListDto dto);

    /**
     * 查询费用分摊汇总列表数据
     * @param dto
     * @return
     */
    List<CostExpensesShareSummaryListVO> selSummaryList(@Param("dto") CostExpensesShareListDto dto);

    /**
     * 查询费用分摊明细列表数据
     * @param dto
     * @return
     */
    List<CostExpensesShareDetailsVO> selDetailsList(@Param("dto") CostExpensesShareListDto dto);

}