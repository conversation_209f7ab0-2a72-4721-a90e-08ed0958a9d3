package com.gok.pboot.pms.eval.enums;

import com.gok.pboot.pms.enumeration.ValueEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * eval send status 枚举
 *
 * <AUTHOR>
 * @date 2025/05/12
 */
@Getter
@AllArgsConstructor
public enum EvalSendStatusEnum implements ValueEnum<Integer> {

    /**
     * 未发送
     */
    UNSENT(0, "未发送"),
    /**
     * 已发送
     */
    SENT(1, "已发送"),
    /**
     * 发送失败
     */
    SEND_FAILED(2, "发送失败");

    private final Integer value;
    private final String name;
}
