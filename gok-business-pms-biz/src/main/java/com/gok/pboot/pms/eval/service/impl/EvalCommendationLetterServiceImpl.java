package com.gok.pboot.pms.eval.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.module.file.entity.SysFile;
import com.gok.module.file.service.SysFileService;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.common.exception.ServiceException;
import com.gok.pboot.pms.eval.entity.domain.EvalCommendationLetter;
import com.gok.pboot.pms.eval.entity.vo.EvalCustomerSatisfactionSurveyVO;
import com.gok.pboot.pms.eval.mapper.EvalCommendationLetterMapper;
import com.gok.pboot.pms.eval.service.IEvalCommendationLetterService;
import com.gok.pboot.pms.eval.service.IEvalCustomerSatisfactionSurveyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2025/05/16
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class EvalCommendationLetterServiceImpl extends ServiceImpl<EvalCommendationLetterMapper, EvalCommendationLetter>
        implements IEvalCommendationLetterService {

    private final SysFileService sysFileService;

    private final IEvalCustomerSatisfactionSurveyService evalCustomerSatisfactionSurveyService;

    @Override
    @Transactional
    public Long uploadCommendationLetters(Long projectId, String commendationLetter) {
        EvalCommendationLetter evalCommendationLetter = baseMapper.getByProjectId(projectId);

        EvalCustomerSatisfactionSurveyVO customerSatisfactionSurvey =
                evalCustomerSatisfactionSurveyService.getSatisfactionSurveyByProjectId(projectId);
        if (null != customerSatisfactionSurvey.getTotalScore()) {
            throw new ServiceException("项目整体得分已生成，表扬信上传失效。");
        }

        if (null == evalCommendationLetter) {
            evalCommendationLetter = new EvalCommendationLetter();
            evalCommendationLetter.setProjectId(projectId);
            evalCommendationLetter.setCommendationLetter(commendationLetter);
            BaseBuildEntityUtil.buildInsert(evalCommendationLetter);
            baseMapper.insert(evalCommendationLetter);
        } else {
            evalCommendationLetter.setCommendationLetter(commendationLetter);
            BaseBuildEntityUtil.buildUpdate(evalCommendationLetter);
            baseMapper.updateById(evalCommendationLetter);
        }

        return evalCommendationLetter.getId();
    }

    @Override
    public List<SysFile> getCommendationLetters(Long projectId) {
        EvalCommendationLetter evalCommendationLetter = baseMapper.getByProjectId(projectId);
        if (null == evalCommendationLetter || StrUtil.isBlank(evalCommendationLetter.getCommendationLetter())) {
            return ListUtil.empty();
        }

        List<String> fileIds = Arrays.asList(evalCommendationLetter.getCommendationLetter().split(","));
        return sysFileService.listByIds(fileIds);
    }

}
