package com.gok.pboot.pms.entity.vo;

import com.gok.pboot.pms.Util.DailyPaperDateUtils;
import com.gok.pboot.pms.entity.DailyPaperEntry;
import com.google.common.collect.Lists;
import lombok.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;

/**
 * 日报审核日期维度
 *
 * <AUTHOR>
 * @version 1.3.2
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class DailyReviewDateVO {

    /**
     * 提交日期
     */
    private LocalDate submissionDate;

    /**
     * 格式化的提交日期 eg: XX月XX日（周X）
     */
    private String submissionDateName;

    /**
     * 填报日期带周
     */
    private String submissionDateFormatted;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 审核人数
     */
    private Integer approvalNum;

    /**
     * 正常工时
     */
    private BigDecimal normalHours;

    /**
     * 加班工时
     */
    private BigDecimal addedHours;

    /**
     * 提交人ID列表
     */
    private List<String> userIds;

    /**
     * 假期类型：1-法定节假日 0-普通休息日
     */
    private Integer holidayType;

    public static DailyReviewDateVO of(Collection<DailyPaperEntry> entries, HashMap<LocalDate, Integer> holidayMap) {
        DailyReviewDateVO result = new DailyReviewDateVO();
        DailyPaperEntry entry = entries.iterator().next();
        LocalDate submissionDate = entry.getSubmissionDate();
        BigDecimal normalHours = BigDecimal.ZERO;
        BigDecimal addedHours = BigDecimal.ZERO;
        List<String> userIds = Lists.newArrayListWithCapacity(entries.size());

        result.setSubmissionDate(submissionDate);
        result.setSubmissionDateName(DailyPaperDateUtils.asDateString(submissionDate));
        result.setProjectId(String.valueOf(entry.getProjectId()));
        result.setProjectName(entry.getProjectName());
        result.setApprovalNum(entries.size());
        for (DailyPaperEntry e : entries) {
            normalHours = normalHours.add(e.getNormalHours());
            addedHours = addedHours.add(e.getAddedHours());
            userIds.add(String.valueOf(e.getUserId()));
        }
        result.setNormalHours(normalHours.setScale(1, RoundingMode.HALF_UP));
        result.setAddedHours(addedHours.setScale(1, RoundingMode.HALF_UP));
        result.setUserIds(userIds);
        result.setSubmissionDateFormatted(DailyPaperDateUtils.asDateString(result.getSubmissionDate()));
        result.setHolidayType(holidayMap.get(result.getSubmissionDate()));
        return result;
    }
}
