package com.gok.pboot.pms.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.gok.bcp.upms.feign.RemoteUserService;
import com.gok.bcp.upms.vo.SysUserVo;
import com.gok.components.common.user.PigxUser;
import com.gok.components.data.handler.PermCodeHolder;
import com.gok.pboot.common.core.util.WebUtils;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.Util.CollectionUtils;
import com.gok.pboot.pms.common.base.R;
import com.gok.pboot.pms.common.exception.ServiceException;
import com.gok.pboot.pms.entity.domain.ProjectInfo;
import com.gok.pboot.pms.entity.vo.ProjectStakeholderMemberVO;
import com.gok.pboot.pms.entity.vo.SysUserDataScopeVO;
import com.gok.pboot.pms.enumeration.RoleTypeEnum;
import com.gok.pboot.pms.mapper.ProjectInfoMapper;
import com.gok.pboot.pms.mapper.ProjectStakeholderMemberMapper;
import com.gok.pboot.pms.service.fegin.CenterDataScopeService;
import com.google.common.collect.ImmutableList;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019-09-07
 * <p>
 * 默认data scope 判断处理器
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ProjectScopeHandleImpl implements ProjectScopeHandle {

    private final ProjectInfoMapper projectInfoMapper;
    private final RemoteUserService remoteUserService;
    private final CenterDataScopeService centerDataScopeService;
    private final ProjectStakeholderMemberMapper projectionStakeholderMemberMapper;

    /**
     * 计算用户数据权限，向查询SQL Map中写入相关查询条件
     *
     * @param filter SQL查询参数Map
     */
    @Override
    public void calcScope(Map<String, Object> filter) {
        PigxUser user = SecurityUtils.getUser();
        List<String> permissions = PermCodeHolder.get();
        SysUserDataScopeVO dataScope;

        if (CollectionUtil.isEmpty(permissions) && filter.containsKey("permCode")) {
            permissions = Collections.singletonList(String.valueOf(filter.get("permCode")));
        }

        if (CollectionUtil.isEmpty(permissions)) {
            scope(filter, null);
            return;
        }

        dataScope = centerDataScopeService.getByClientIdAndRoleId(
                WebUtils.getRequest().getHeader("Application"),
                user.getId(),
                permissions.get(0)
        ).getData();
        scope(filter, dataScope);
    }

    private void scope(Map<String, Object> filter, @Nullable SysUserDataScopeVO dataScope) {
        List<Long> userIdList;

        // 查询全部
        if (dataScope == null) {
            filter.put("scope", "none");
            filter.put("userIdList", ImmutableList.of(SecurityUtils.getUser().getId()));
        } else if (BooleanUtils.toBoolean(dataScope.getIsAll())) {
            filter.put("scope", "all");
        } else {
            userIdList = dataScope.getUserIdList();
            if (CollectionUtils.isEmpty(userIdList)) {
                userIdList = ImmutableList.of(SecurityUtils.getUser().getId());
            } else {
                userIdList.add(SecurityUtils.getUser().getId());
            }
            filter.put("deptList", dataScope.getDeptIdList());
            filter.put("userIdList", userIdList);

        }
    }

    @Override
    public SysUserDataScopeVO findDataScope() {
        List<String> permissions = PermCodeHolder.get();
        SysUserDataScopeVO dataScope;
        R<SysUserDataScopeVO> dataScopeR;

        if (CollectionUtils.isEmpty(permissions)) {
            dataScope = new SysUserDataScopeVO();
            dataScope.setIsAll(Boolean.FALSE);
            dataScope.setDeptIdList(ImmutableList.of());
            dataScope.setUserIdList(ImmutableList.of());

            return dataScope;
        }
        dataScopeR = centerDataScopeService.getByClientIdAndRoleId(
                WebUtils.getRequest().getHeader("Application"),
                SecurityUtils.getUser().getId(),
                permissions.get(0)
        );
        dataScope = dataScopeR.getData();
        if (dataScope == null) {
            log.error("获取数据权限异常, code={}, msg={}", dataScopeR.getCode(), dataScopeR.getMsg());
            throw new ServiceException("获取数据权限异常");
        }

        return dataScope;
    }

    /**
     * 获取数据权限
     *
     * @param menuCode  菜单权限编码
     * @param projectId 项目ID
     * @param isAll     是否查询全部权限的标志
     * @return 数据权限结果
     */
    @Override
    public SysUserDataScopeVO getDeliverManagementDataPermission(String menuCode, Long projectId, Boolean isAll) {
        // 获取当前用户
        PigxUser user = SecurityUtils.getUser();
        Long currentUserId = user.getId();

        // 初始化结果
        SysUserDataScopeVO result = new SysUserDataScopeVO();
        List<Long> userIds = new ArrayList<>();

        // 添加当前用户
        userIds.add(currentUserId);

        // 判断是否指定查询全部
        if (Boolean.TRUE.equals(isAll)) {
            result.setIsAll(true);
            return result;
        }

        // 判断是否是项目经理或客户经理或者项目干系人
        if (projectId != null) {
            ProjectInfo projectInfo = Optional.ofNullable(projectInfoMapper.selectById(projectId)).orElse(new ProjectInfo());
            if (currentUserId.equals(projectInfo.getManagerUserId())
                    || currentUserId.equals(projectInfo.getSalesmanUserId())) {
                result.setIsAll(true);
                return result;
            }
            List<Long> projectStakeholderMemberIds = projectionStakeholderMemberMapper.getMemberByProjectId(projectId, RoleTypeEnum.PROJECT_OPERATIONS_ASSISTANT.getValue())
                    .stream().map(ProjectStakeholderMemberVO::getMemberId)
                    .collect(Collectors.toList());
            if (projectStakeholderMemberIds.contains(currentUserId)) {
                result.setIsAll(true);
                return result;
            }
        }

        // 使用 calcScope 计算权限
        Map<String, Object> filter = new HashMap<>();
        filter.put("permCode", menuCode);
        calcScope(filter);

        // 判断是否有全部权限
        if ("all".equals(filter.get("scope"))) {
            result.setIsAll(true);
            return result;
        }

        // 获取有权限的用户ID列表
        List<Long> permissionUserIds = (List<Long>) filter.get("userIdList");
        if (CollUtil.isNotEmpty(permissionUserIds)) {
            userIds.addAll(permissionUserIds);
        }

        // 去重
        userIds = userIds.stream().distinct().collect(Collectors.toList());

        // 获取用户名
        List<SysUserVo> userList = Optional.ofNullable(remoteUserService.getAllUser().getData()).orElse(new ArrayList<>());
        List<Long> finalUserIds = userIds;
        Map<Long, SysUserVo> userMap = Optional.ofNullable(userList.stream()
                .filter(e -> finalUserIds.contains(e.getUserId()))
                .collect(Collectors.toMap(SysUserVo::getUserId, e -> e, (v1, v2) -> v1))).orElse(new HashMap<>());

        List<String> userNames = Optional.ofNullable(userMap.values()).orElse(new ArrayList<>()).stream()
                .map(SysUserVo::getName)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toList());
        userNames.add(user.getName());

        result.setIsAll(false);
        result.setUserIdList(userIds);
        result.setUserNameList(userNames);

        return result;
    }

}
