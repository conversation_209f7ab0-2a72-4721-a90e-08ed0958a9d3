package com.gok.pboot.pms.Util;

import com.gok.pboot.pms.entity.domain.PmsDictItem;
import com.gok.pboot.pms.entity.domain.PmsDictItemMultiLevel;
import com.gok.pboot.pms.mapper.PmsDictItemMapper;
import com.google.common.collect.HashMultimap;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Multimaps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * pms字典工具类
 *
 * <AUTHOR>
 * @date 2023/12/14
 */
@Component
@Slf4j
public class PmsDictUtil {

    @Resource
    private PmsDictItemMapper pmsDictItemMapper;

    /**
     * 根据字典类型查找字典项列表
     * @param dictType 字典类型
     * @return 字典项List
     */
    public List<PmsDictItem> findDictItemByDictType(String dictType) {
        return pmsDictItemMapper.findDictItemByDictType(dictType);
    }

    /**
     * 根据类型查找具有二级字典项的字典项列表
     * @param dictType 字典类型
     * @return 多层级字典项List
     */
    public List<PmsDictItemMultiLevel> findDictItemMultiLevelByDictType(String dictType) {
        Map<Long, PmsDictItemMultiLevel> dictItemIdMap;
        List<PmsDictItemMultiLevel> result;
        List<PmsDictItemMultiLevel> pmsDictItemMultiLevels = pmsDictItemMapper.findDictItemMultiLevelByDictType(dictType);

        // 对集合进行二级列表重组（目前只存在二级字典）
        // 父级列表
        dictItemIdMap = pmsDictItemMultiLevels.stream()
                .filter(p -> p.getParentId() == null || p.getParentId().equals(-1L))
                .collect(Collectors.toMap(PmsDictItemMultiLevel::getId, entity -> entity));
        // 插入子集列表
        pmsDictItemMultiLevels.forEach(p -> {
            Long parentId = p.getParentId();
            if (parentId != null && parentId != -1L) {
                PmsDictItemMultiLevel parent = dictItemIdMap.get(parentId);
                if(parent != null) {
                    List<PmsDictItemMultiLevel> childrenList = parent.getChildren();
                    if (childrenList == null) {
                        childrenList = new ArrayList<>();
                        parent.setChildren(childrenList);
                    }
                    childrenList.add(p);
                }
            }
        });
        result = new ArrayList<>(dictItemIdMap.values());

        return result;
    }

    /**
     * 通过字典类型列表查找字典类型-字典项Map
     * @param dictTypes 字典类型列表，用","拼接
     * @return 字典项Map
     */
    public Map<String, Collection<PmsDictItem>> findDictMapByTypeList(String dictTypes) {
        String[] typeArr = dictTypes.split(",");

        if (typeArr.length == 0) {
            return ImmutableMap.of();
        }

        return Multimaps.index(
                pmsDictItemMapper.findDictMapByTypeList(Arrays.asList(typeArr)),
                PmsDictItem::getDictType
        ).asMap();
    }

    /**
     * 获取全部数据字典项Map
     * @return 字典项Map
     */
    public HashMultimap<String, PmsDictItem> getPmsDictItemMap() {
        List<PmsDictItem> pmsDictItemList = pmsDictItemMapper.findAll();

        HashMultimap<String, PmsDictItem> dictItemMap = HashMultimap.create();
        pmsDictItemList.forEach(p -> dictItemMap.put(p.getDictType(), p));

        return dictItemMap;
    }

    /**
     * 获取全部子集字典项Map
     * @return 字典项Map
     */
    public HashMultimap<String, PmsDictItem> getPmsSubItemMap() {
        List<PmsDictItem> pmsDictItemList = pmsDictItemMapper.findSubItem();

        HashMultimap<String, PmsDictItem> dictItemMap = HashMultimap.create();
        pmsDictItemList.forEach(p -> dictItemMap.put(p.getDictType(), p));

        return dictItemMap;
    }

    /**
     * 传入字典项key和对应字典项集合，获取对应的字典项value
     * @param key 字典项key
     * @param pmsDictItems  字典项集合
     * @return 字典项value
     */
    public static String getLabelFromPmsDictItems(@Nullable String key, @Nullable Set<PmsDictItem> pmsDictItems) {
        if (key == null || pmsDictItems == null) {
            return "";
        }

        Optional<String> labelOptional = pmsDictItems.stream()
                .filter(item -> key.equals(item.getItemValue()))
                .map(PmsDictItem::getLabel)
                .findFirst();

        return labelOptional.orElse("");
    }
}
