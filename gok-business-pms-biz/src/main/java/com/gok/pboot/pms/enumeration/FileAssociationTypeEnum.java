package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;

/**
 * 文件关联附件枚举类
 *
 *  <AUTHOR>
 */
@AllArgsConstructor
public enum FileAssociationTypeEnum implements ValueEnum<Integer> {

    /**
     * 项目附件
     */
    PROJECT_ATTACHMENTS(0,"项目附件"),
    /**
     * 商机进展记录
     */
    BUSINESS_PROGRESS(1,"商机进展记录"),
    /**
     * 客户沟通记录
     */
    CUSTOMER_COMMUNICATION_RECORD(2,"客户沟通记录");

    private final Integer value;

    private final String name;

    @Override
    public Integer getValue() {
        return value;
    }

    @Override
    public String getName() {
        return name;
    }

}
