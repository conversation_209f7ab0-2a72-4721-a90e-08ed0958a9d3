package com.gok.pboot.pms.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.pboot.pms.entity.domain.PmsDocImageFile;
import com.gok.pboot.pms.entity.vo.OaFileInfoVo;
import com.gok.pboot.pms.entity.vo.OaFileVo;
import com.gok.pboot.pms.mapper.PmsDocImageFileMapper;
import com.gok.pboot.pms.oa.OaUtil;
import com.gok.pboot.pms.service.IPmsDocImageFileService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


/**
 * OA文件映射
 * Service
 *
 * <AUTHOR>
 * @date 2023/11/21
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PmsDocImageFileServiceImpl
        extends ServiceImpl<PmsDocImageFileMapper, PmsDocImageFile>
        implements IPmsDocImageFileService {

    private final static String FILE_SEPARATOR = ",";

    private final OaUtil oaUtil;


    @Override
    public List<OaFileInfoVo> getOaOaFileInfoList(OaFileInfoVo file, List<OaFileVo> resourcesData) {
        List<OaFileInfoVo> oaFileInfoVoList = new ArrayList<>();
        List<PmsDocImageFile> pmsDocImageFileList;
        if (CollUtil.isNotEmpty(resourcesData)) {
            //文件映射表
            QueryWrapper<PmsDocImageFile> pmsDocImageFileQueryWrapper = new QueryWrapper<>();
            List<String> list1 = Arrays.asList(file.getFileId().split(FILE_SEPARATOR));
            pmsDocImageFileQueryWrapper.lambda().in(PmsDocImageFile::getDocId, list1);
            pmsDocImageFileList = this.list(pmsDocImageFileQueryWrapper);
            log.info("pmsDocImageFileList:" + pmsDocImageFileList);
            if (CollUtil.isNotEmpty(pmsDocImageFileList)) {
                List<Long> collect = resourcesData.stream()
                        .map(r -> Long.parseLong(r.getId().toString())).collect(Collectors.toList());
                List<PmsDocImageFile> fileList =
                        pmsDocImageFileList.stream()
                                .filter(f -> !collect.contains(f.getImageFileId())).collect(Collectors.toList());
                //补充资源
                for (PmsDocImageFile p :
                        fileList) {
                    if (Optional.ofNullable(p.getOperateUserId()).isPresent()) {
                        List<OaFileVo> resourcesData1 = oaUtil.getResourcesData(String.valueOf(file.getRequestId()),
                                p.getOperateUserId().toString());
                        resourcesData.addAll(resourcesData1);
                    }
                }
                resourcesData = resourcesData.stream()
                        .collect(Collectors.collectingAndThen(
                                Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(OaFileVo::getId))),
                                ArrayList::new
                        ));
            }

            //多附件转换
            if (StrUtil.isNotBlank(file.getFileId()) && StrUtil.isBlank(file.getImageFileId())
                    && CollUtil.isNotEmpty(pmsDocImageFileList)) {
                log.info("多附件转换");
                String imageFileIds = StrUtil.EMPTY;
                for (PmsDocImageFile f :
                        pmsDocImageFileList) {
                    imageFileIds += f.getImageFileId() + FILE_SEPARATOR;
                }
                int index = imageFileIds.lastIndexOf(FILE_SEPARATOR);
                imageFileIds = imageFileIds.substring(0, index);
                log.info("imageFileIds:" + imageFileIds);
                file.setImageFileId(imageFileIds);
            }
            if (StrUtil.isNotBlank(file.getImageFileId())) {
                List<OaFileVo> oaFileVoList = resourcesData.stream()
                        .filter(r -> file.getImageFileId().contains(r.getId().toString()))
                        .collect(Collectors.toList());
                Map<Long, Long> pmsDocImageFileMap =
                        pmsDocImageFileList.stream()
                                .collect(Collectors.toMap(PmsDocImageFile::getImageFileId, PmsDocImageFile::getDocId));
                //有效附件
                if (CollectionUtil.isNotEmpty(oaFileVoList)) {
                    oaFileVoList.stream().forEach(f -> {
                        OaFileInfoVo oaFileInfoVo = new OaFileInfoVo();
                        oaFileInfoVo.setFileId(pmsDocImageFileMap.get(Long.parseLong(f.getId().toString())).toString());
                        oaFileInfoVo.setImageFileId(f.getId().toString());
                        oaFileInfoVo.setDownloadUrl(f.getDownloadUrl());
                        oaFileInfoVo.setFilename(f.getName());
                        oaFileInfoVoList.add(oaFileInfoVo);
                    });

                }
                //无效附件
                if (CollUtil.isNotEmpty(pmsDocImageFileList)) {
                    List<Long> fileIds = oaFileVoList.stream()
                            .map(f -> Long.parseLong(String.valueOf(f.getId()))).collect(Collectors.toList());
                    pmsDocImageFileList = pmsDocImageFileList.stream()
                            .filter(p -> !fileIds.contains(p.getImageFileId()))
                            .collect(Collectors.toList());
                    pmsDocImageFileList.stream().forEach(p -> {
                        OaFileInfoVo oaFileInfoVo = new OaFileInfoVo();
                        oaFileInfoVo.setFileId(p.getDocId().toString());
                        oaFileInfoVo.setImageFileId(p.getImageFileId().toString());
                        oaFileInfoVo.setFilename(p.getImageFileName());
                        oaFileInfoVoList.add(oaFileInfoVo);
                    });
                }
            }
        }


        return oaFileInfoVoList;
    }

    @Override
    public List<PmsDocImageFile> getByDocIds(List<Long> docIds) {
        QueryWrapper<PmsDocImageFile> pmsDocImageFileQueryWrapper = new QueryWrapper<>();
        pmsDocImageFileQueryWrapper.lambda().in(PmsDocImageFile::getDocId, docIds);
        return this.list(pmsDocImageFileQueryWrapper);
    }

    @Override
    public List<OaFileInfoVo> getOaFileVoList(String docId) {
        List<OaFileInfoVo> oaFileInfoVoList = new ArrayList<>();
        if (StringUtils.isEmpty(docId)) {
            return oaFileInfoVoList;
        }

        //文件映射表
        QueryWrapper<PmsDocImageFile> pmsDocImageFileQueryWrapper = new QueryWrapper<>();
        List<String> list1 = Arrays.asList(docId.split(FILE_SEPARATOR));
        pmsDocImageFileQueryWrapper.lambda().in(PmsDocImageFile::getDocId, list1);
        List<PmsDocImageFile> pmsDocImageFileList = this.list(pmsDocImageFileQueryWrapper);
        if (CollUtil.isEmpty(pmsDocImageFileList)) {
            return oaFileInfoVoList;
        }
        pmsDocImageFileList.stream().forEach(f -> {
            if(f.getRequestId() == null || f.getOperateUserId() == null){
                return;
            }
            List<OaFileVo> resourcesData = oaUtil.getResourcesData(String.valueOf(f.getRequestId()),
                    String.valueOf(f.getOperateUserId()));
            resourcesData.forEach(r -> {
                if(Long.valueOf(r.getId()).equals(f.getImageFileId())){
                    OaFileInfoVo oaFileInfoVo = new OaFileInfoVo();
                    oaFileInfoVo.setFileId(String.valueOf(f.getDocId()));
                    oaFileInfoVo.setImageFileId(r.getId().toString());
                    oaFileInfoVo.setDownloadUrl(r.getDownloadUrl());
                    oaFileInfoVo.setFilename(r.getName());
                    oaFileInfoVoList.add(oaFileInfoVo);
                }
            });
        });
        return oaFileInfoVoList;
    }
}
