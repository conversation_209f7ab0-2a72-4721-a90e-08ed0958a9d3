package com.gok.pboot.pms.service.fegin.document;


import com.gok.pboot.common.core.constant.SecurityConstants;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.ServiceNameConstants;
import com.gok.pboot.pms.common.doc.entity.Doc;
import com.gok.pboot.pms.common.doc.entity.InnerDoc;
import com.gok.pboot.pms.common.doc.vo.UploadFileVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.List;

@FeignClient(contextId = "iRemoteDocService", value = ServiceNameConstants.DOCUMENT)
public interface IRemoteDocService {

    @PostMapping(value = "/doc/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ApiResult upload(@RequestPart("file") MultipartFile file);

    @PostMapping(value = "/doc/expose/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ApiResult<Doc> uploadNoAuth(@RequestPart("file") MultipartFile file,@RequestParam(value = "tenantId",required = false)Long tenantId);

    @PostMapping("/doc/uploadFile")
    ApiResult uploadFile(@RequestParam("file") File file);

    @GetMapping("/doc/download/{id}")
    public ResponseEntity<byte[]> download(@PathVariable("id") Long id);

    @GetMapping("/doc/downloadWithAuth/{id}")
    public ResponseEntity<byte[]> downloadWithAuth(@PathVariable("id") Long id);

    /**
     * 根据id返回doc
     *
     * @param id
     * @return
     */
    @GetMapping("/doc/getDoc/{id}")
    public ApiResult<Doc> getDoc(@PathVariable("id") Long id);

    /**
     * 返回body
     *
     * @param id
     * @return
     * @throws Exception
     */
    @GetMapping("/doc/getPhoto/{id}")
    public byte[] getPhoto(@PathVariable("id") Long id);

    /**
     * 跨服务内部下载接口
     *
     * @param id 文件id
     * @return
     */
    @GetMapping(value = "/doc/innerDownload/{id}", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ApiResult<InnerDoc> innerDownload(@PathVariable("id") Long id);

    /**
     * 跨服务内部下载接口
     *
     * @param id 文件id
     * @return
     */
    @GetMapping(value = "/doc/innerDownloadNoAuth", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ApiResult<InnerDoc> innerDownloadNoAuth(@RequestParam("id") String id);

    /**
     * 通过url上传文件
     *
     * @param uploadFileVO
     * @return
     */
    @PostMapping("/doc/uploadByUrl")
    public ApiResult<Doc> uploadByUrl(@RequestBody UploadFileVO uploadFileVO);

    /**
     * 通过url上传文件
     *
     * @param uploadFileVO
     * @return
     */
    @PostMapping("/doc/uploadByUrlNoAuth")
    public ApiResult<Doc> uploadByUrlNoAuth(@RequestBody UploadFileVO uploadFileVO);

    /**
     * 二维码生成上传
     *
     * @param jsonStr
     * @return
     */
    @PostMapping("/doc/qrCodeUpload")
    public ApiResult<Doc> qrCodeUpload(@RequestBody String jsonStr);


    /**
     * 根据id集合获取对象集合
     */
    @PostMapping("/doc/findDocList")
    public ApiResult<List<Doc>> findDocList(@RequestBody List<Long> idList);

    /**
     * 根据id集合获取对象集合
     */
    @PostMapping("/doc/findDocListInner")
    public ApiResult<List<Doc>> findDocListInner(
            @RequestBody List<Long> idList,
            @RequestHeader(SecurityConstants.FROM) String from
    );


    /**
     * 删除非在编花名册Excel
     */
    @DeleteMapping("/doc/deleteEhrOutsideHrmExcel")
    ApiResult<Void> deleteEhrOutsideHrmExcel();

}
