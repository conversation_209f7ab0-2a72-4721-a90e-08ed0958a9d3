package com.gok.pboot.pms.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.common.base.R;
import com.gok.pboot.pms.entity.domain.ProjectInfo;
import com.gok.pboot.pms.entity.vo.ProjectInfoOverViewVO;
import com.gok.pboot.pms.entity.vo.ProjectInfoVO;
import com.gok.pboot.pms.entity.vo.ProjectOverViewInnerVO;
import com.gok.pboot.pms.eval.entity.vo.EvalSatisfactionSurveyProjectVo;

import java.util.List;
import java.util.Map;

/**
 * 项目信息服务
 *
 * <AUTHOR>
 * @since 2023-07-14
 */
public interface IProjectInfoService extends IService<ProjectInfo> {

    /**
     * 分页查询
     *
     * @param pageRequest 分页参数
     * @param filter      查询参数
     * @return page
     */
    Page<ProjectInfoOverViewVO> findPage(PageRequest pageRequest, Map<String, Object> filter);

    /**
     * 通过Id查询项目基本信息
     *
     * @param id 项目ID
     * @return {@link R}<{@link ProjectInfoVO}>>
     */
    ProjectInfoVO getProjectInfoById(Long id);

    /**
     * 同步项目干系人（废弃）
     */
    @Deprecated
    void syncMembers();

    /**
     * 同步项目干系人
     */
    void syncProjectMembers();

    /**
     * 通过Id查询内部项目概况
     *
     * @param id 项目ID
     * @return {@link R}<{@link ProjectInfoVO}>>
     */
    ProjectOverViewInnerVO getProjectOverviewInnerById(Long id);


    Integer countByUnitId(Long unitId, String projectStatus);

    /**
     * 获取结项项目且未进行满意度调查的项目列表
     *
     * @return {@link List }<{@link EvalSatisfactionSurveyProjectVo }>
     */
    List<EvalSatisfactionSurveyProjectVo> getCompletedProjectsWithoutSatisfactionSurvey();

}
