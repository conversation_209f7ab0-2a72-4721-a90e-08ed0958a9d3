package com.gok.pboot.pms.cost.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 差旅住宿标准配置
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("cost_config_travel_stay")
public class CostConfigTravelStay extends BeanEntity<Long> {

    private static final long serialVersionUID = 1L;

    /**
     * 版本ID
     */
    private Long versionId;

    /**
     * 城市标准（一线城市、二线城市、三线及以下城市）
     */
    private String cityStandards;

    /**
     * 城市ids（逗号隔开，-1=除以上外）
     */
    private String cityIds;

    /**
     * 总经办金额
     */
    private BigDecimal generalOfficePrice;

    /**
     * 总监级以上金额
     */
    private BigDecimal directorAbovePrice;

    /**
     * 总监级以下金额
     */
    private BigDecimal directorBelowPrice;
}
