package com.gok.pboot.pms.cost.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.cost.entity.domain.CostConfigSubsidyCustom;
import com.gok.pboot.pms.cost.entity.dto.CostConfigSubsidyCustomDTO;
import com.gok.pboot.pms.cost.entity.vo.CostConfigSubsidyCustomVO;

import java.util.List;

/**
 * <p>
 * 自定义补贴配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
public interface ICostConfigSubsidyCustomService extends IService<CostConfigSubsidyCustom> {

    /**
     * 获取成本配置补贴自定义列表
     *
     * @return {@link List }<{@link CostConfigSubsidyCustomVO }>
     */
    List<CostConfigSubsidyCustomVO> getCostConfigSubsidyCustomList();

    /**
     * 编辑成本配置补贴自定义列表
     *
     * @param dtoList DTO 列表
     */
    void editCostConfigSubsidyCustomList(List<CostConfigSubsidyCustomDTO> dtoList);

    /**
     * 根据版本ID获取自定义补贴配置列表
     *
     * @param versionId 版本 ID
     * @return {@link List }<{@link CostConfigSubsidyCustomVO }>
     */
    List<CostConfigSubsidyCustomVO> getCostConfigSubsidyCustomListByVersionId(Long versionId);
}
