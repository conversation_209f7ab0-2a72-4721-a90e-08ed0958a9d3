package com.gok.pboot.pms.entity.vo;

import com.gok.bcp.upms.vo.SysUserVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 个人面板基本信息vo
 *
 * <AUTHOR>
 * @date 2023/8/27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MyBaseInfoVO {

    /**
     * 我的项目数量
     */
    private Integer userTaskNum;

    /**
     * 我的风险数量
     */
    private Integer userRiskNum;

    /**
     * 用户头像链接
     */
    private String userAvatarUrl;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 用户上次登录时间
     */
    private LocalDateTime userLastLoginTime;

    public static MyBaseInfoVO of(SysUserVo data, Integer myRiskNum, Integer myTaskNum) {
        MyBaseInfoVO result = new MyBaseInfoVO();
        LocalDateTime lastLoginTime = null;
        String avatar = null;
        String userName = null;
        if (data != null) {
            lastLoginTime = data.getLastLoginTime();
            avatar = data.getAvatar();
            userName = data.getUsername();
        }
        result.setUserRiskNum(myRiskNum);
        result.setUserTaskNum(myTaskNum);
        result.setUserName(userName);
        result.setUserAvatarUrl(avatar);
        if (lastLoginTime != null) {
            result.setUserLastLoginTime(lastLoginTime);
        }
        return result;
    }
}
