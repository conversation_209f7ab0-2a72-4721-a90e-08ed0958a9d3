package com.gok.pboot.pms.cost.service;

import java.time.LocalDate;

/**
 * 工单工时归档服务接口
 *
 * <AUTHOR>
 * @date 2024/04/16
 */
public interface ICostTaskFilingService {

    /**
     * 根据日期判断是否已归档
     */
    boolean isFiled(LocalDate date);

    /**
     * 判断日期是否存在
     */
    boolean exists(LocalDate date);

    /**
     * 归档指定年月
     */
    void filing(Integer year, Integer month, String operator);

    /**
     * 取消归档指定年月
     */
    void unfiling(Integer year, Integer month, String operator);
} 