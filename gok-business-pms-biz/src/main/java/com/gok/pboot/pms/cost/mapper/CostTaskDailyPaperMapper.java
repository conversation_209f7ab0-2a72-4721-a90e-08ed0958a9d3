package com.gok.pboot.pms.cost.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.cost.entity.domain.CostTaskDailyPaper;
import com.gok.pboot.pms.cost.entity.dto.CostSupportTaskApprovalDTO;
import com.gok.pboot.pms.cost.entity.dto.CostTaskDailyPaperAbnormalDTO;
import com.gok.pboot.pms.cost.entity.dto.TaskAbnormalCountDTO;
import com.gok.pboot.pms.cost.entity.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 工单工时数据库操作接口
 *
 * <AUTHOR>
 * @date 2024/04/16
 */
@Mapper
public interface CostTaskDailyPaperMapper extends BaseMapper<CostTaskDailyPaper> {

    Page<CostTaskDailyPaper> findList(Page<CostTaskDailyPaper> page, @Param("filter") Map<String, Object> filter);

    List<CostTaskDailyPaper> findBySubmissionDateAndUserIds(@Param("submissionDate") LocalDate submissionDate, @Param("userIds") List<Long> userIds);

    List<CostTaskDailyPaper> findBySubmissionDateAndUserId(@Param("submissionDate") LocalDate submissionDate, @Param("userId") Long userId);

    List<CostTaskDailyPaperVO> findBySubmissionDateRange(
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate,
            @Param("userId") Long userId,
            @Param("projectNameLike") String projectNameLike,
            @Param("approvalTab") Integer approvalTab,
            @Param("approvalList") List<Integer> approvalList
    );

    Set<LocalDate> findSubmissionDateByUserIdAndSubmissionDateRangeAndApprovalStatusNot(
            @Param("userId") Long userId,
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate,
            @Param("approvalStatus") Integer approvalStatus
    );

    void batchSave(@Param("papers") List<CostTaskDailyPaper> papers);

    void updateApprovalStatus(@Param("papers") List<CostTaskDailyPaper> papers);

    void cleanUpUseless();

    Long findIdBySubmissionDateAndUserId(@Param("submissionDate") LocalDate submissionDate, @Param("userId") Long userId);

    CostTaskDailyPaper findOneBeforeBySubmissionDateAndUserId(@Param("submissionDate") LocalDate submissionDate, @Param("userId") Long userId);

    /**
     * 查询异常工时列表
     * @param page 分页参数
     * @param request 查询参数
     * @return 异常工时列表
     */
    Page<CostTaskDailyPaperAbnormalVO> findAbnormalList(Page<CostTaskDailyPaperAbnormalVO> page, @Param("request") CostTaskDailyPaperAbnormalDTO request);

    /**
     * 查询异常工时列表
     * @param request 查询参数
     * @return 异常工时列表
     */
    List<CostTaskDailyPaperAbnormalVO> findAbnormalList(@Param("request") CostTaskDailyPaperAbnormalDTO request);

    /**
     * 查询异常工时消息推送数据
     *
     * @param request 查询条件
     * @return 异常工时消息推送数据列表
     */
    List<CostTaskDailyPaperAbnormalMsgVO> findAbnormalMsgList(@Param("request") CostTaskDailyPaperAbnormalDTO request);


    /**
     * 查询售前工单工时审核分页列表
     * @param dto 查询条件
     * @return 分页结果
     */
    List<CostSupportTaskApprovalDetailVO> findSupportTaskApprovalList(@Param("dto") CostSupportTaskApprovalDTO dto);

    /**
     * 查询工单工时审核列表
     * @param dto 查询条件
     * @return 工单工时审核列表
     */
    List<CostTaskDailyPaperApprovalDetailVO> findApprovalDetailList(@Param("dto") CostSupportTaskApprovalDTO dto);

    /**
     * 查找异常计数
     *
     * @param request 请求
     * @return {@link Map }<{@link Integer }, {@link Integer }>
     */
    List<TaskAbnormalCountDTO> findAbnormalCount(@Param("request") CostTaskDailyPaperAbnormalDTO request);

}