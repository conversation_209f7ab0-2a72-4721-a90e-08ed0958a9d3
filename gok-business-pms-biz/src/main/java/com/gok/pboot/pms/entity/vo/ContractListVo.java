package com.gok.pboot.pms.entity.vo;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 合同台账列表vo
 *
 * <AUTHOR>
 * @date 2024/2/22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class ContractListVo {

    /**
     * 合同id
     */
    private Long id;

    /**
     * 合同名称
     */
    private String htmc;

    /**
     * 合同编号
     */
    private String htbh;

    /**
     * 合同细类
     */
    private Integer htxl;

    /**
     * 合同金额（含税）
     */
    private BigDecimal htjehs;

    /**
     * 合同所属公司
     */
    private Integer htssgs;

    /**
     * 对方名称id
     */
    private String khmc;
    /**
     * 对方名称
     */
    private String khmcName;

    /**
     * 客户名称
     */
    private String khmcnewName;

    /**
     * 客户名称
     */
    private String khmcnewId;

    /**
     * 供应商名称
     */
    private String gysmcId;

    /**
     * 供应商名称
     */
    private String gysmcName;

    /**
     * 合同金额(不含税)
     */
    private BigDecimal htjebhs;

    /**
     * 结算方式
     */
    private Integer jsfs;

    /**
     * 业务板块
     */
    private Integer ywbk;

    /**
     * 合同类别
     */
    private Integer htlb;

    /**
     * 收入类型
     */
    private Integer srlx;

    /**
     * 技术类型
     */
    private Integer jslx;

    /**
     * 实际合同签订日期
     */
    private String sjhtqdrq;

    /**
     * 合同起始日期
     */
    private String htqsrq;

    /**
     * 合同截止日期
     */
    private String htjzrq;

    /**
     * 项目编号
     */
    private String xmbh;

    /**
     * 合同所属项目名称
     */
    private Long xmmc;
    /**
     * 合同所属项目名称
     */
    private String xmmcName;

    /**
     * 合同所属一级部门id
     */
    private Long htssbm;
    /**
     * 合同所属一级部门
     */
    private String htssbmName;

    /**
     * 合同所属二级部门id
     */
    private Long htssejbm;
    /**
     * 合同所属二级部门
     */
    private String htssejbmName;

    /**
     * 客户经理id
     */
    private Long xmxsry;
    /**
     * 客户经理
     */
    private String xmxsryName;

    /**
     * 项目经理id
     */
    private Long xmjl;
    /**
     * 项目经理
     */
    private String xmjlName;

    /**
     * 结项启动条件
     */
    private Integer jxqdtj;

    /**
     * 验收日期
     */
    private String ysrq;

    /**
     * 变更类型
     */
    private Integer bglx;

    /**
     * 合同状态排序
     */
    private Integer htztOrder;

    /**
     * 合同状态
     */
    private Integer htzt;
    /**
     * 已收款金额
     */
    private String  yskje;
    /**
     * 已回款比例
     *
     */
    private String yskbl;

}
