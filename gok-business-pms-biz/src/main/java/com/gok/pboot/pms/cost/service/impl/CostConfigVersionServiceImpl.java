package com.gok.pboot.pms.cost.service.impl;

import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.components.common.user.PigxUser;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.Util.DateUtil;
import com.gok.pboot.pms.Util.SysDeptUtils;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.common.exception.ServiceException;
import com.gok.pboot.pms.cost.entity.domain.CostConfigVersion;
import com.gok.pboot.pms.cost.entity.vo.VersionHistoryVO;
import com.gok.pboot.pms.cost.enums.CostConfigVersionTypeEnum;
import com.gok.pboot.pms.cost.enums.VersionStatusEnum;
import com.gok.pboot.pms.cost.mapper.CostConfigVersionMapper;
import com.gok.pboot.pms.cost.service.ICostConfigVersionService;
import com.gok.pboot.pms.entity.SysDept;
import com.gok.pboot.pms.handler.BcpDataHandler;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 * 成本配置版本记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Service
@AllArgsConstructor
public class CostConfigVersionServiceImpl extends ServiceImpl<CostConfigVersionMapper, CostConfigVersion> implements ICostConfigVersionService {

    private final BcpDataHandler bcpDataHandler;

    public static final String V = "V";



    /**
     * 生成最新版本名称
     * 版本的命名格式为 VXX-yyyyMMdd，其中xx按版本数量累计
     *
     * @param versionTypeEnum version 类型 enum
     * @return {@link String }
     */
    @Override
    public Long generateVersionName(CostConfigVersionTypeEnum versionTypeEnum) {
        if (Objects.isNull(versionTypeEnum)){
            throw new ServiceException("版本类型不能为空");
        }
        // 获取当前版本
        CostConfigVersion crrVersion = getCrrCostConfigVersion(versionTypeEnum);
        String versionName = Objects.nonNull(crrVersion) ? crrVersion.getVersionName() : StrUtil.EMPTY;
        // 更新为历史版本
        if (StrUtil.isNotBlank(versionName)){
            BaseBuildEntityUtil.buildUpdate(crrVersion.setVersionStatus(VersionStatusEnum.HISTORY.getValue()));
            baseMapper.updateById(crrVersion);
        }
        // 返回新的版本名称
        String latestVersionName = getVersionName(versionName);
        // 获取部门map
        Map<Long, SysDept> deptIdMap = bcpDataHandler.getAllSysDeptMap();
        // 创建新的版本记录
        CostConfigVersion costConfigVersion = BaseBuildEntityUtil.buildInsert(new CostConfigVersion()
                .setVersionStatus(VersionStatusEnum.CURRENT.getValue())
                .setVersionType(versionTypeEnum.getValue())
                .setVersionName(latestVersionName));
        // 获取当前用户信息
        PigxUser user = SecurityUtils.getUser();
        if (Objects.nonNull(user)){
            String deptName = SysDeptUtils.collectFullName(deptIdMap, user.getDeptId());
            costConfigVersion.setOperatorId(user.getId())
                    .setOperatorName(user.getName())
                    .setOperatorDeptName(deptName)
                    .setOperatorDeptId(user.getDeptId());
        }
        baseMapper.insert(costConfigVersion);
        return costConfigVersion.getId();
    }

    /**
     * 获取版本名称
     *
     * @param versionName 版本名称
     * @return {@link String }
     */
    public static String getVersionName(String versionName) {
        int versionNum = NumberUtils.INTEGER_ONE;
        if (StrUtil.isNotBlank(versionName)){
            // 将版本名称拆分为数字和日期
            String[] parts = versionName.split(StrPool.DASHED);
            // 去掉"V",将数字部分转换为整数
            versionNum += Integer.parseInt(parts[NumberUtils.INTEGER_ZERO].substring(NumberUtils.INTEGER_ONE));
        }
        return V + versionNum + StrPool.DASHED + getCurrentDate();
    }

    /**
     * 获取 当前成本配置版本
     *
     * @param versionTypeEnum version 类型 enum
     * @return {@link CostConfigVersion }
     */
    @Override
    public CostConfigVersion getCrrCostConfigVersion(CostConfigVersionTypeEnum versionTypeEnum) {
        return lambdaQuery().eq(CostConfigVersion::getVersionType, versionTypeEnum.getValue())
                .eq(CostConfigVersion::getVersionStatus, VersionStatusEnum.CURRENT.getValue()).one();
    }


    /**
     * 获取最大版本号
     *
     * @param versionTypeEnum version 类型 enum
     * @return {@link String }
     */
    @Override
    public String getMaxVersionNum(CostConfigVersionTypeEnum versionTypeEnum) {
        return baseMapper.getMaxVersionNum(versionTypeEnum);
    }

    /**
     * 获取当前日期
     *
     * @return {@link String }
     */
    private static String getCurrentDate() {
        SimpleDateFormat format = new SimpleDateFormat(DateUtil.YEAR_MONTH_DAY);
        return format.format(new Date());
    }

    /**
     * 获取历史记录版本
     *
     * @param pageRequest 页面请求
     * @return {@link Page }<{@link VersionHistoryVO }>
     */
    @Override
    public Page<VersionHistoryVO> getHistoryVersions(PageRequest pageRequest, CostConfigVersionTypeEnum versionTypeEnum) {
        return baseMapper.findPage(Page.of(pageRequest.getPageNumber(), pageRequest.getPageSize()), versionTypeEnum);
    }
}
