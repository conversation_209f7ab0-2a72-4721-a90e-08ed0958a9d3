package com.gok.pboot.pms.entity.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.sql.Timestamp;

/**
 * 操作记录
 *
 * <AUTHOR>
 * @since 2022-08-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class OperatingRecordPageVO {

    /**
     * 操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp ctime;
    /**
     * 操作类型
     */
    private Integer operationType;
    /**
     * 操作类型名称
     */
    private String operationTypeName;
    /**
     * 被操作任务ID
     */
    private Long taskId;
    /**
    * 操作信息
    */
    private String operationInfo;

    /**
    * 操作人
    */
    private String operator;


}
