package com.gok.pboot.pms.cost.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.cost.entity.domain.CostDeliverTask;
import com.gok.pboot.pms.cost.entity.dto.CostDeliverTaskDTO;
import com.gok.pboot.pms.cost.entity.dto.CostTaskAbnormalDTO;
import com.gok.pboot.pms.cost.entity.vo.CostDeliverApprovalVO;
import com.gok.pboot.pms.cost.entity.vo.CostDeliverTaskVO;
import com.gok.pboot.pms.cost.entity.vo.CostTaskAbnormalVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 交付管理工单映射器
 *
 * <AUTHOR>
 * @date 2025/01/15
 */
@Mapper
public interface CostDeliverTaskMapper extends BaseMapper<CostDeliverTask> {

    /**
     * 根据交付管理工单DTO查询
     *
     * @param query 查询条件
     * @return 交付管理工单VO列表
     */
    List<CostDeliverTaskVO> findByCostDeliverTaskDTO(@Param("query") CostDeliverTaskDTO query);

    /**
     * 根据ID集合查询
     *
     * @param idList       ID集合
     * @param parentIdList 父级ID集合
     * @param taskType     任务类型
     * @param createId     创建 ID
     * @return {@link List }<{@link CostDeliverTaskVO }>
     */
    List<CostDeliverTaskVO> findByIdList(@Param("idList") Collection<Long> idList,
                                         @Param("parentIdList") Collection<Long> parentIdList,
                                         @Param("taskType") Integer taskType,
                                         @Param("createId") Long createId);

    /**
     * 查询审核工单
     *
     * @param query
     * @return
     */
    Page<CostDeliverApprovalVO> findTaskApprovalList(@Param("page") Page<CostDeliverApprovalVO> page, @Param("query") CostDeliverTaskDTO query);


    /**
     * 查询项目经理审核的工单
     *
     * @param query
     * @return
     */
    List<CostDeliverTask> findProjectManagerTask(@Param("query") CostDeliverTaskDTO query);

    /**
     * 查询本级关联父级数据
     *
     * @param query
     * @return
     */
    List<CostDeliverTask> findTaskAndParent(@Param("query") CostDeliverTaskDTO query);

    /**
     * 查询当前用户可用的工单列表
     *
     * @param userId 用户ID
     * @param date   日期
     * @return 工单列表
     */
    List<CostDeliverTaskVO> findByUserIdForDailyPaperEntry(@Param("userId") Long userId, @Param("date") LocalDate date);

    /**
     * 查询异常工单
     *
     * @param page
     * @param request
     * @return
     */
    Page<CostTaskAbnormalVO> findAbnormalPage(Page page, @Param("request") CostTaskAbnormalDTO request);

    /**
     * 查询异常工单
     *
     * @param request
     * @return
     */
    List<CostTaskAbnormalVO> findAbnormalPage(@Param("request") CostTaskAbnormalDTO request);

    /**
     * 查询售前经理需要审核的工单
     *
     * @param query
     * @return
     */
    List<CostDeliverTask> findPreManagerTask(@Param("query") CostDeliverTaskDTO query);

    /**
     * 按日期获取最大序列号
     *
     * @param dateStr 日期 str
     * @param prefix  前缀
     * @return {@link Integer }
     */
    Integer getMaxSequenceNumberByDate(@Param("dateStr") String dateStr, @Param("prefix") String prefix);

    /**
     * 按类型计数异常分组
     *
     * @param request 请求
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     */
    List<JSONObject> countAbnormalGroupByType(@Param("request")CostTaskAbnormalDTO request);

    /**
     * 查找审批项目列表
     *
     * @param page    页
     * @param request 请求
     * @return {@link Page }<{@link CostDeliverApprovalVO }>
     */
    Page<CostDeliverApprovalVO> findTaskApprovalProjectPage(Page<CostDeliverApprovalVO> page, @Param("query") CostDeliverTaskDTO request);

    /**
     * 查找审批任务
     *
     * @param request 请求
     * @return {@link List }<{@link CostDeliverTask }>
     */
    List<CostDeliverTask> findTaskApprovalTasks(@Param("query") CostDeliverTaskDTO request);

    /**
     * 查找项目审批页面
     *
     * @param page    页
     * @param request 请求
     * @return {@link Page }<{@link CostDeliverTaskVO }>
     */
    Page<CostDeliverTaskVO> findProjectApprovalPage(Page<CostDeliverTaskVO> page,@Param("query") CostDeliverTaskDTO request);
}

