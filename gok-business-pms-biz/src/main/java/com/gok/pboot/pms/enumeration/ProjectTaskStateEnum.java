package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;

/**
 * 任务状态
 *
 * <AUTHOR>
 * @date 2024/01/19
 */
@AllArgsConstructor
public enum ProjectTaskStateEnum implements ValueEnum<Integer> {

    NORMAL(0, "正常"),
    FINISHED(1, "结束")
    ;

    private final Integer value;

    private final String name;

    @Override
    public Integer getValue() {
        return value;
    }

    @Override
    public String getName() {
        return name;
    }

}
