package com.gok.pboot.pms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.common.join.ProjectInDailyPaperEntry;
import com.gok.pboot.pms.entity.bo.DailyReviewReuseAndDeliveryProjectBO;
import com.gok.pboot.pms.entity.domain.ProjectInfo;
import com.gok.pboot.pms.entity.domain.ProjectStakeholderMember;
import com.gok.pboot.pms.entity.dto.PrivilegeFindPageDTO;
import com.gok.pboot.pms.entity.vo.AdminConfigFindPageVo;
import com.gok.pboot.pms.eval.entity.vo.EvalSatisfactionSurveyProjectVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import javax.annotation.Nullable;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 项目信息Mapper
 *
 * <AUTHOR>
 * @since 2023-07-14
 */
@Mapper
public interface ProjectInfoMapper extends BaseMapper<ProjectInfo> {

    /**
     * 分页查询
     *
     * @param page 分页参数
     * @param map  查询参数
     * @return page
     */
    Page<ProjectInfo> findList(Page<ProjectInfo> page, @Param("filter") Map<String, Object> map);

    /**
     * 查询
     *
     * @param filter 查询参数
     * @return 数据集合
     */
    List<ProjectInfo> findListByProjectIds(@Param("filter") Map<String, Object> filter);

    /**
     * 查询ID列表
     *
     * @param filter 参数
     * @return 项目ID列表
     */
    List<Long> findId(@Param("filter") Map<String, Object> filter);

    List<Long> findIdByInsideProjects(@Nullable @Param("insideProjects") Collection<Integer> insideProjects);

    /**
     * 根据铁三角用户ID查询可审核的项目ID列表
     *
     * @return 项目ID列表
     */
    List<Long> findIdAuditableByTriangleUserId(Long userId);

    /**
     * 根据ID查询项目是否为内部项目
     *
     * @param id 项目ID
     * @return bool
     */
    boolean isInternalProjectById(Long id);

    /**
     * 根据ID查询项目是否存在
     *
     * @param id 项目ID
     * @return bool
     */
    boolean isExist(Long id);

    /**
     * 根据项目名模糊查询项目信息
     *
     * @param filter
     * @return: {@link List<ProjectInfo>}
     */
    List<ProjectInfo> findByProjectNameLike(@Param("filter") Map<String, Object> filter);

    /**
     * 根据项目ID集合查询项目信息
     *
     * @param idList     项目ID集合[]
     * @param statusList 项目状态集合[]
     * @return 项目信息实体类集合[]
     */
    List<ProjectInfo> findByIdListAndStatus(@Param("idList") Collection<Long> idList, @Param("statusList") List<Integer> statusList);

    /**
     * ~ 根据ID列表查询可用于日报填写的项目 ~
     *
     * @param ids ID列表
     * @return java.util.List<com.gok.pboot.pms.common.join.ProjectInDailyPaperEntry>
     */
    List<ProjectInDailyPaperEntry> findByIdsForDailyPaperEntry(List<Long> ids);

    /**
     * 根据项目名称获取项目信息
     *
     * @param projectNames
     * @return {@link List< ProjectInfo>}
     */
    List<ProjectInfo> selectByProjectName(@Param("projectNames") List<String> projectNames);

    /**
     * 根据权限的部门限制获取用户可用项目id列表
     */
    List<Long> findAllListByDept(@Param("filter") Map<String, Object> filter);

    /**
     * 查询项目表，支持模糊查询权限配置相关人员的信息
     *
     * @param
     * @param privilegeFindPageDTO
     * @return
     */
    Page<ProjectInfo> findPageAboutPrivilege(
            Page<AdminConfigFindPageVo> page,
            @Param("privilegeFindPageDTO") PrivilegeFindPageDTO privilegeFindPageDTO
    );

    /**
     * 根据项目ID列表查询，按项目名称排序
     *
     * @param ids 项目ID列表
     * @return 数据列表
     */
    Page<ProjectInfo> findByIdsOrderByItemName(
            Page<ProjectInfo> page,
            @Param("ids") Collection<Long> ids
    );

    /**
     * 根据用户id查询日报条目可用项目信息
     *
     * @param userId 用户id
     * @return {@link List}<{@link ProjectInfo}>
     */
    List<ProjectInfo> findByUserIdForDailyPaperEntry(@Param("userId") Long userId, @Param("date") LocalDate date);

    /**
     * 工时审核 复用+交付 项目查询
     *
     * @param page   分页对象
     * @param filter 查询参数
     * @return 分页数据
     */
    Page<DailyReviewReuseAndDeliveryProjectBO> findReuseAndDeliveryReview(
            Page<DailyReviewReuseAndDeliveryProjectBO> page,
            @Param("filter") Map<String, Object> filter
    );

    /**
     * 更新初始化任务标志
     *
     * @param ids  项目id列表
     * @param flag 标志
     * @return int
     */
    int updateInitTaskFlag(@Param("ids") List<Long> ids, @Param("flag") Integer flag);

    /**
     * 更新自动结束任务标志
     *
     * @param ids  项目id列表
     * @param flag 标志
     * @return int
     */
    int updateAutoFinishFag(@Param("ids") List<Long> ids, @Param("flag") Integer flag);

    /**
     * 通过id列表获取项目
     *
     * @param ids id列表
     * @return {@link List}<{@link ProjectInfo}>
     */
    List<ProjectInfo> findListByIds(@Param("ids") List<Long> ids);

    /**
     * 获取全部
     *
     * @return {@link List}<{@link ProjectInfo}>
     */
    List<ProjectInfo> findAll();

    List<ProjectStakeholderMember> findToSyncMember();

    List<ProjectStakeholderMember> findToSyncMember2();

    List<ProjectStakeholderMember> findToSyncMember3();


    int countByUnitId(@Param("unitId") Long unitId, @Param("projectStatus") String projectStatus);

    /**
     * 获取结项项目且未进行满意度调查的项目列表
     *
     * @return {@link List }<{@link EvalSatisfactionSurveyProjectVo }>
     */
    List<EvalSatisfactionSurveyProjectVo> getCompletedProjectsWithoutSatisfactionSurvey();

    /**
     * 获取项目 ID 范围
     *
     * @param userId 用户 ID
     * @return {@link List }<{@link Long }>
     */
    List<Long> getProjectIdScope(@Param("userId") Long userId);

}
