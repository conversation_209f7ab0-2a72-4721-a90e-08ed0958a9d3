package com.gok.pboot.pms.eval.entity.vo;

import cn.hutool.core.bean.BeanUtil;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.eval.entity.domain.EvalUserRole;
import com.gok.pboot.pms.eval.enums.EvalUserRoleEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 项目评价用户角色VO类
 *
 * <AUTHOR>
 * @date 2025/05/12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class EvalUserRoleVO {

    /**
     * ID
     */
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 角色
     * {@link com.gok.pboot.pms.eval.enums.EvalUserRoleEnum}
     */
    private Integer role;

    /**
     * 角色名称
     */
    private String roleName;

    public static EvalUserRoleVO convertToVO(EvalUserRole entity) {
        if (null == entity) {
            return null;
        }
        EvalUserRoleVO vo = BeanUtil.copyProperties(entity, EvalUserRoleVO.class);
        vo.setRoleName(EnumUtils.getNameByValue(EvalUserRoleEnum.class, entity.getRole()));
        return vo;
    }
} 