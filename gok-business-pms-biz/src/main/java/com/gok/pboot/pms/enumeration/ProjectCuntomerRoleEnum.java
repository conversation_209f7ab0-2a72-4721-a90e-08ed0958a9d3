package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;

/**
 * 项目客情角色枚举
 *
 * <AUTHOR>
 * @date 2023-07-20
 */
@AllArgsConstructor
public enum ProjectCuntomerRoleEnum implements ValueEnum<String> {
    HANDLER("0", "经办人"),
    DIVISION_MANAGER("1", "分管领导"),
    MAIN_MANAGER("2", "主管领导")
    ;

    private final String value;

    private final String name;

    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getName() {
        return name;
    }
}
