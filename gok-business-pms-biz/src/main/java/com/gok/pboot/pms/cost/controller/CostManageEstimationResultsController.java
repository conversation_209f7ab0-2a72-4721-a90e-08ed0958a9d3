package com.gok.pboot.pms.cost.controller;


import cn.hutool.core.collection.ListUtil;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PropertyFilters;
import com.gok.pboot.pms.cost.entity.dto.CostManageCalculateDTO;
import com.gok.pboot.pms.cost.entity.dto.CostManageEstimationResultsSaveDTO;
import com.gok.pboot.pms.cost.entity.dto.CostManageSelectDTO;
import com.gok.pboot.pms.cost.entity.dto.CostManageVersionDTO;
import com.gok.pboot.pms.cost.entity.vo.CostManageConfigVersionVO;
import com.gok.pboot.pms.cost.entity.vo.CostManageEstimationResultsVO;
import com.gok.pboot.pms.cost.entity.vo.CostManageListVO;
import com.gok.pboot.pms.cost.entity.vo.CostManageSelectVO;
import com.gok.pboot.pms.cost.service.ICostManageEstimationResultsService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;


/**
 * 成本管理估算结果 前端控制器
 *
 * <AUTHOR>
 * @menu 成本管理估算结果
 * @since 2025-01-07
 */
@RestController
@AllArgsConstructor
@RequestMapping("/costManageEstimationResults")
public class CostManageEstimationResultsController {

    private final ICostManageEstimationResultsService service;

    private static final String PARAM_PROJECT_ID = "projectId";

    /**
     * 查询最新版本的成本估算结果
     *
     * @param request 请求体
     * @return {@link ApiResult}<{@link List}<{@link CostManageEstimationResultsVO}>>
     */
    @GetMapping("/findLatest")
    public ApiResult<List<CostManageEstimationResultsVO>> findLastEstimationResults(CostManageVersionDTO request) {
        return ApiResult.success(service.findLatestEstimationResults(request));
    }

    /**
     * 生成成本估算结果
     *
     * @param request 生成成本估算结果请求
     */
    @PostMapping
    public ApiResult<Void> saveEstimationResults(@Valid @RequestBody CostManageEstimationResultsSaveDTO request) {
        service.saveEstimationResults(request);
        return ApiResult.success(null);
    }

    /**
     * 成本管理列表
     *
     * @param projectId          项目ID
     * @param costBudgetTypeList 成本预算类型枚举值集合[] {@link com.gok.pboot.pms.cost.enums.CostBudgetTypeEnum}
     * @return {@link ApiResult}<{@link List}<{@link com.gok.pboot.pms.cost.entity.vo.CostManageListVO}>>
     */
    @GetMapping("/findCostList/{projectId}")
    public ApiResult<CostManageListVO> findLastEstimationResults(@PathVariable Long projectId,
                                                                 @RequestParam(required = false) List<Integer> costBudgetTypeList) {
        return ApiResult.success(service.getCostManageListVO(projectId, costBudgetTypeList), "获取成功");
    }


    /**
     * 成本管理历史版本详情
     *
     * @param versionId 版本versionId
     * @return
     */
    @GetMapping("/historyInfo/{versionId}")
    public ApiResult<CostManageListVO> getCostManageById(@PathVariable Long versionId) {
        return ApiResult.success(service.getCostManageById(versionId), "查询成功");
    }

    /**
     * 查询成本估算使用版本
     *
     * @param request 请求体
     * @return {@link ApiResult}<{@link List}<{@link CostManageEstimationResultsVO}>>
     * customParam filter_L_projectId 项目ID
     * customParam filter_I_costBudgetType 成本预算类型枚举值
     */
    @GetMapping("/configVersion")
    public ApiResult<List<CostManageConfigVersionVO>> findConfigVersion(HttpServletRequest request) {
        Map<String, Object> filter = PropertyFilters.get(request);
        Object projectId = filter.get(PARAM_PROJECT_ID);
        if (projectId == null) {
            return ApiResult.success(ListUtil.empty());
        }
        return ApiResult.success(service.findConfigVersion(Long.parseLong(projectId.toString())));
    }

    /**
     * 人工成本测算
     *
     * @param request 成本管理人员级别测算明细
     * @return 成本估算结果集合
     */
    @PostMapping("/calculate")
    public ApiResult<List<CostManageEstimationResultsVO>> calculateEstimationResults(@Valid @RequestBody CostManageCalculateDTO request) {
        return ApiResult.success(service.calculateEstimationResults(request));
    }

    /**
     * 同步OA项目预算台账最新成本管理信息内容
     *
     * @return {@link ApiResult}<{@link String}>
     */
    @Deprecated
    @PostMapping("/getProjectBudgetInfo")
    public ApiResult<String> getProjectBudgetInfo() {
        service.getProjectBudgetInfo();
        return ApiResult.successMsg("同步OA项目预算台账信息成功");
    }

    /**
     * 获取项目成本科目预算
     *
     * @param dto
     * @return {@link ApiResult}<{@link List}<{@link CostManageSelectVO}>>
     */
    @PostMapping("/findCostSelect")
    public ApiResult<CostManageSelectVO> findCostSelect(@RequestBody CostManageSelectDTO dto) {
        return ApiResult.success(service.findCostSelect(dto), "获取成功");
    }

}
