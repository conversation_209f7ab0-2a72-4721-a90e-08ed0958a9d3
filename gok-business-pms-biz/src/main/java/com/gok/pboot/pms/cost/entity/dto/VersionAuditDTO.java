package com.gok.pboot.pms.cost.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VersionAuditDTO {

    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空")
    private Long projectId;

    /**
     * 版本ID
     */
    @NotNull(message = "版本ID不能为空")
    private Long versionId;

    /**
     * 审核状态(2=未通过，3=已通过)
     */
    @NotNull(message = "审核状态不能为空")
    private Integer auditStatus;

    /**
     * 拒绝理由
     */
    private String refuseReason;

}