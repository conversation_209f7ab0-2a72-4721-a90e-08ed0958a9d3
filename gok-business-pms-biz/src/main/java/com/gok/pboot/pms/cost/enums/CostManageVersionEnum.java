package com.gok.pboot.pms.cost.enums;

import com.gok.pboot.pms.enumeration.ValueEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 成本管理版本记录版本类型枚举类
 *
 * <AUTHOR>
 * @create 2025/01/08
 **/
@Getter
@AllArgsConstructor
public enum CostManageVersionEnum implements ValueEnum<Integer> {

    /**
     * 目标管理
     */
    MBGL(0, "目标管理"),

    /**
     * 成本管理
     */
    CBGL(1, "成本管理"),

    /**
     * 报价与毛利测算
     */
    BJYMLCS(2, "报价与毛利测算");

    private final Integer value;

    private final String name;

}
