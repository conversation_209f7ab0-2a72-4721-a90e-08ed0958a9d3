package com.gok.pboot.pms.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 项目费用项科目对照代表详情
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class SubjectDetailsVO {

    /**
     * ID
     */
    private Integer id;

    /**
     * 科目代码
     */
    private String kmdm;

    /**
     * 科目名称
     */
    private String kmmc;



}
