package com.gok.pboot.pms.didi.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 滴滴项目信息同步请求DTO
 * 根据滴滴企业版API文档构建：/river/BudgetCenter/add
 *
 * <AUTHOR>
 * @date 2025/01/27
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class DidiProjectSyncReq {


    /**
     * 类型
     * 必填
     * 枚举值数字 1 部门 2 项目
     */
    private Integer type;

    /**
     * 部门/项目名称
     * 必填
     * 不大于 200 字符
     */
    private String name;

    /**
     * 预算周期
     * 必填
     * 枚举值数字 0：不限额；1：自然月 2：自然季度 3：自然年
     * （其中23只对部门生效，需要设置白名单，须联系客户经理）
     */
    private Integer budget_cycle;

    /**
     * 总金额
     * 必填
     * 单位元 0表示不限额度 金额管控只支持正整数
     */
    private String total_quota;

    /**
     * 编号
     * 非必填
     * type = 1 时必填；type = 2 非必填
     * 长度限制：≤ 64 字符
     */
    private String out_budget_id;

    /**
     * 主管ID
     * 非必填
     * 人员同步时返回的 memberid
     * leader_id和leader_employee_id 时，优先处理leader_id
     * 多个用英文逗号分开，第一个是主要主管，后续是其他主管，最多30个
     */
    private String leader_id;

    /**
     * 主管员工编号
     * 非必填
     * 主管工号，json字符串，第一个是主要主管，后续是其他主管，最多30个
     * leader_id存在时不生效
     * 举例：[111,2222,44444]，0开头需要使用：["0012","1234"]
     */
    private String leader_employee_id;

    /**
     * 上级部门/项目 ID
     * 非必填
     * 新建部门时返回的部门ID
     * type = 1 非必填，传0为顶级部门ID
     * type=2 默认为空
     * parent_id优先级大于 out_parent_id和 out_parent_name
     */
    private String parent_id;

    /**
     * 上级部门/项目外部CODE
     * 非必填
     * type = 1 非必填，不传默认为顶级部门code
     * type=2 默认为空，项目需要out_parent_id和out_parent_name一起传递，作为唯一值校验
     */
    private String out_parent_id;

    /**
     * 上级部门/项目外部名称
     * 非必填
     * type = 1 非必填，不传默认为顶级部门code
     * type=2 默认为空，项目需要out_parent_id和out_parent_name一起传递，作为唯一值校验
     */
    private String out_parent_name;

    /**
     * 使用范围
     * 非必填
     * 枚举值数字 type=2时生效
     * 0：全员可见
     * 1：项目成员内可见
     * 2：公司主体内可见
     * 不传默认为0 (枚举 2需要设置白名单，须联系客户经理。报错误码10001)
     */
    private Integer member_used;

    /**
     * 项目开始日期
     * 非必填
     * 默认为空 格式：yyyy-MM-dd type=2时生效
     */
    private String start_date;

    /**
     * 项目结束日期
     * 非必填
     * 默认为空 格式：yyyy-MM-dd type=2时生效
     */
    private String expiry_date;

    /**
     * 公司主体ID
     * 非必填
     * 多个用英文逗号分开
     * 字段不传不生效
     * 如果对应的公司主体id已经停用或者不存在 返回错误码10001 type=2时生效
     */
    private String legal_entity_id;

    /**
     * 项目扩展信息的自定义字段
     * 非必填
     * 项目扩展信息的自定义字段；最长不大于 500 字符
     * (必须为json字符串，json解析后不能为空)
     * 仅对项目（type=2）生效，部门传了不生效
     */
    private String budget_extra_info;
}