package com.gok.pboot.pms.enumeration;


import lombok.AllArgsConstructor;

/**
 * 项目收入类型
 *
 * <AUTHOR>
 * @version 1.3.4
 */
@AllArgsConstructor
public enum ProjectIncomeTypeEnum implements ValueEnum<Integer> {

    OUTSIDE(0, "外部交付项目（收入型）"),
    INSIDE(1, "内部交付项目（非收入型）"),
    INSIDE_SPECIAL(2, "管理运营项目（非收入型）")
    ;

    private final Integer value;

    private final String name;

    @Override
    public Integer getValue() {
        return value;
    }

    @Override
    public String getName() {
        return name;
    }
}
