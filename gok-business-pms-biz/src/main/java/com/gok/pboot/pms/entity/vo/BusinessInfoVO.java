package com.gok.pboot.pms.entity.vo;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.StrPool;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gok.pboot.pms.Util.*;
import com.gok.pboot.pms.entity.domain.PmsDictItem;
import com.gok.pboot.pms.enumeration.BusinessStatusEnum;
import com.gok.pboot.pms.enumeration.PmsDictEnum;
import com.google.common.base.Splitter;
import com.google.common.collect.HashMultimap;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.util.Map;
import java.util.StringJoiner;

/**
 * 项目台账vo
 *
 * <AUTHOR>
 * @date 2023/11/21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BusinessInfoVO {

    /**
     * 商机ID(项目ID)
     */
    @ExcelIgnore
    private Long id;

    /**
     * 项目ID
     */
    @ExcelIgnore
    private Long projectId;

    /**
     * 商机名称(项目名称)
     */
    @ExcelProperty({"商机名称"})
    private String businessName;

    /**
     * 项目编号
     */
    @ExcelProperty({"项目编号"})
    private String itemNo;

    /**
     * 业务归属部门（一级）
     */
    @ExcelProperty({"业务归属部门（一级）"})
    private String firstDepartment;

    /**
     * 业务归属部门（二级）
     */
    @ExcelProperty({"业务归属部门（二级）"})
    private String department;

    /**
     * 项目所在地
     */
    @ExcelProperty({"项目所在地"})
    private String projectLocation;

    /**
     * 经营单元ID
     */
    @ExcelIgnore
    private Long businessId;

    /**
     * 经营单元名称
     */
    @ExcelProperty({"经营单元名称"})
    private String businessUnitName;

    /**
     * 所属客户ID
     */
    @ExcelIgnore
    private Long unitId;

    /**
     * 所属客户名称
     */
    @ExcelProperty({"所属客户名称"})
    private String unitName;

    /**
     * 最终客户名称
     */
    @ExcelProperty({"最终客户名称"})
    private String endCustomerName;

    /**
     * 最终客户分级
     */
    @ExcelProperty({"最终客户分级"})
    private String endCustomerGrade;

    /**
     * 最终客户行业
     */
    @ExcelProperty({"最终客户行业"})
    private String endCustomerIndustry;

    /**
     * 签约客户名称
     */
    @ExcelProperty({"签约客户名称"})
    private String contractCustomerName;

    /**
     * 签约客户分级
     */
    @ExcelProperty({"签约客户分级"})
    private String contractCustomerGrade;

    /**
     * 是否需要招投标 (是、否)
     */
    @ExcelProperty({"是否需要招投标"})
    private String isNotNeedBidding;

    /**
     * 招标方式
     */
    @ExcelIgnore
    private String biddingMethod;

    /**
     * 项目签约主体
     */
    @ExcelProperty({"项目签约主体"})
    private String contractEntity;

    /**
     * 业务板块
     */
    @ExcelProperty({"业务板块"})
    private String businessModule;

    /**
     * 收入类型
     */
    @ExcelProperty({"收入类型"})
    private String incomeType;

    /**
     * 技术类型
     */
    @ExcelProperty({"技术类型"})
    private String technologyType;

    /**
     * 结算方式
     */
    @ExcelProperty({"结算方式"})
    private String settlementMethod;

    /**
     * 交付形式
     */
    @ExcelProperty({"交付形式"})
    private String deliveryMethod;

    /**
     * 关键决策链
     */
    @ExcelProperty({"关键决策链"})
    private String keyDecisionChain;

    /**
     * 关键决策人支持情况
     */
    @ExcelProperty({"关键决策人支持情况"})
    private String supportFromKeyDecision;

    /**
     * 联系人个数 （从联系人表获取）
     */
    @ExcelProperty({"联系人"})
    private Integer contactNumber;

    /**
     * 项目需求是否明确
     */
    @ExcelProperty({"项目需求是否明确"})
    private String projectRequirementClear;

    /**
     * 项目需求
     */
    @ExcelProperty({"项目需求"})
    private String projectRequirement;

    /**
     * 预算情况
     */
    @ExcelProperty({"预算情况"})
    private String budgetSituation;

    /**
     * 预计签单金额
     */
    @ExcelProperty({"预计签单金额"})
    private String expectedOrderAmount;

    /**
     * 预估毛利率
     */
    @ExcelProperty({"预估毛利率"})
    private String ygmll;

    /**
     * 预计签单时间
     */
    @ExcelProperty({"预计签单时间"})
    private String expectedCompleteTime;

    /**
     * 竞争情况
     */
    @ExcelProperty({"竞争情况"})
    private String competitionSituation;

    /**
     * 商机阶段 (0、1、2、3、4)
     */
    @ExcelProperty({"商机阶段"})
    private String businessStage;

    /**
     * 项目里程碑
     */
    @ExcelProperty({"项目里程碑"})
    private String businessMilestone;

    /**
     * 是否涉及外采
     */
    @ExcelProperty({"是否涉及外采"})
    private String isExternalProcurement;

    /**
     * 采购类别
     */
    @ExcelProperty({"采购类别"})
    private String purchasingCategories;

    /**
     * 商机状态
     */
    @ExcelProperty({"商机状态"})
    private String businessStatus;

    /**
     * 售前工时
     */
    @ExcelProperty({"售前人天"})
    private String preSaleHours;

    /**
     * 售后工时
     */
    @ExcelProperty({"售后人天"})
    private String afterSaleHours;

    /**
     * 学员工时
     */
    @ExcelProperty({"学员人天"})
    private String studentHours;

    /**
     * 售前人工投入
     */
    @ExcelProperty({"商机阶段人工投入"})
    private String preSalesLaborInput;

    /**
     * 售前费用投入
     */
    @ExcelProperty({"售前费用投入"})
    private String sqfytr;

    /**
     * 售前人天 （从数仓获取）
     */
    @ExcelIgnore
    private BigDecimal beforeSalesManDays;

    /**
     * 售后人天 （从数仓获取）
     */
    @ExcelIgnore
    private BigDecimal afterSalesManDays;

    /**
     * 学员人天 （从数仓获取）
     */
    @ExcelIgnore
    private BigDecimal studentManDay;

    /**
     * 商机进展 （从进展表取）
     */
    @ExcelProperty({"商机进展"})
    private String businessProgress;

    /**
     * 下一步工作计划（从进展表取）
     */
    @ExcelProperty({"下一步工作计划"})
    private String nextStepForwardPlan;

    /**
     * 下一步计划完成时间 (从进展表取)
     */
    @ExcelProperty({"下一步计划完成时间"})
    private String nextStepPlanFtime;

    /**
     * 遇到的问题及所需支持 - 存在问题及需要协同 (从进展表取)
     */
    @ExcelProperty({"存在问题及需要协同"})
    private String yddwtjsxdzc;

    /**
     * 商机更新时限
     */
    @ExcelProperty({"商机更新时限"})
    private String businessMtime;

    /**
     * 商机更新时间
     */
    @ExcelProperty({"商机更新时间"})
    private String businessUpdateDate;

    /**
     * 项目销售人员ID(客户经理ID)
     */
    @ExcelIgnore
    private Long salesmanUserId;

    /**
     * 项目销售人员(客户经理姓名)
     */
    @ExcelProperty({"客户经理"})
    private String projectSalesperson;

    /**
     * 售前人员ID(售前经理ID)
     */
    @ExcelIgnore
    private Long preSaleUserId;

    /**
     * 售前人员姓名(售前经理姓名)
     */
    @ExcelProperty({"售前经理"})
    private String preSaleUserName;

    /**
     * 项目经理人员ID
     */
    @ExcelIgnore
    private Long managerUserId;

    /**
     * 项目经理人员姓名
     */
    @ExcelProperty({"项目经理"})
    private String managerUserName;

    /**
     * 商机创建时间
     */
    @ExcelProperty({"商机创建时间"})
    private String businessCtime;

    /**
     * 成交时间/终止时间
     */
    @ExcelProperty({"成交/终止日期"})
    private LocalDate transactionDate;

    public static void updateResultParam(
            HashMultimap<String, PmsDictItem> dictItemMap,
            HashMultimap<String, PmsDictItem> subItemMap,
            Map<Long, Integer> contactCountMap,
            BusinessInfoVO r
    ) {
        // 1.1 更新字典字段
        String endCustomerIndustry = r.getEndCustomerIndustry();
        String[] customerIndustries;
        String purchasingCategories = r.getPurchasingCategories();
        StringJoiner purchasingCategoriesJoiner = new StringJoiner("、");
        r.setIsExternalProcurement(PmsDictUtil.getLabelFromPmsDictItems(r.getIsExternalProcurement(), dictItemMap.get(PmsDictEnum.IS_EXTERNAL_PROCUREMENT.getValue())));
        if (StringUtils.isNotBlank(purchasingCategories)) {
            Splitter.on(StrPool.COMMA).omitEmptyStrings().trimResults().split(purchasingCategories)
                    .forEach(
                            pc -> purchasingCategoriesJoiner.add(PmsDictUtil.getLabelFromPmsDictItems(
                                    pc, dictItemMap.get(PmsDictEnum.PURCHASING_CATEGORIES.getValue())
                            ))
                    );
            r.setPurchasingCategories(purchasingCategoriesJoiner.toString());
        }
        r.setBusinessStage(PmsDictUtil.getLabelFromPmsDictItems(r.getBusinessStage(), dictItemMap.get(PmsDictEnum.BUSINESS_STAGE.getValue())));
        r.setBusinessMilestone(PmsDictUtil.getLabelFromPmsDictItems(r.getBusinessMilestone(), dictItemMap.get(PmsDictEnum.BUSINESS_MILESTONE.getValue())));
        r.setEndCustomerGrade(PmsDictUtil.getLabelFromPmsDictItems(r.getEndCustomerGrade(), dictItemMap.get(PmsDictEnum.CUSTOMER_GRADE.getValue())));
        r.setContractCustomerGrade(PmsDictUtil.getLabelFromPmsDictItems(r.getContractCustomerGrade(), dictItemMap.get(PmsDictEnum.CUSTOMER_GRADE.getValue())));
        r.setIsNotNeedBidding(PmsDictUtil.getLabelFromPmsDictItems(r.getIsNotNeedBidding(), dictItemMap.get(PmsDictEnum.IS_NOT_NEED_BIDDING.getValue())));
        r.setContractEntity(PmsDictUtil.getLabelFromPmsDictItems(r.getContractEntity(), dictItemMap.get(PmsDictEnum.CONTRACT_ENTITY.getValue())));
        r.setBusinessModule(PmsDictUtil.getLabelFromPmsDictItems(r.getBusinessModule(), dictItemMap.get(PmsDictEnum.BUSINESS_MODULE.getValue())));
        r.setIncomeType(PmsDictUtil.getLabelFromPmsDictItems(r.getIncomeType(), dictItemMap.get(PmsDictEnum.INCOME_TYPE.getValue())));
        r.setTechnologyType(PmsDictUtil.getLabelFromPmsDictItems(r.getTechnologyType(), dictItemMap.get(PmsDictEnum.TECHNOLOGY_TYPE.getValue())));
        r.setSettlementMethod(PmsDictUtil.getLabelFromPmsDictItems(r.getSettlementMethod(), dictItemMap.get(PmsDictEnum.SETTLEMENT_METHOD.getValue())));
        r.setDeliveryMethod(PmsDictUtil.getLabelFromPmsDictItems(r.getDeliveryMethod(), dictItemMap.get(PmsDictEnum.DELIVERY_METHOD.getValue())));
        r.setSupportFromKeyDecision(PmsDictUtil.getLabelFromPmsDictItems(r.getSupportFromKeyDecision(), dictItemMap.get(PmsDictEnum.SUPPORT_FROM_KEY_DECISION.getValue())));
        r.setProjectRequirementClear(PmsDictUtil.getLabelFromPmsDictItems(r.getProjectRequirementClear(), dictItemMap.get(PmsDictEnum.PROJECT_REQUIREMENT_CLEAR.getValue())));
        r.setBudgetSituation(PmsDictUtil.getLabelFromPmsDictItems(r.getBudgetSituation(), dictItemMap.get(PmsDictEnum.BUDGET_SITUATION.getValue())));
        if (StringUtils.isNotBlank(endCustomerIndustry)) {
            customerIndustries = endCustomerIndustry.split(StrPool.COMMA);
            if (customerIndustries.length == 2) {
                r.setEndCustomerIndustry(
                        PmsDictUtil.getLabelFromPmsDictItems(
                                customerIndustries[0], dictItemMap.get(PmsDictEnum.CUSTOMER_INDUSTRY.getValue())
                        ) + StrPool.SLASH +
                                PmsDictUtil.getLabelFromPmsDictItems(
                                        customerIndustries[1], subItemMap.get(PmsDictEnum.CUSTOMER_INDUSTRY.getValue())
                                )
                );
            }
        }
        // 将除商机、商机终止外的其他状态全部重写为已成交，并对商机更新时限进行调整
        String businessStatus = r.getBusinessStatus();
        Integer businessStatusVal;
        String businessMtime;

        if (StringUtils.isNotBlank(businessStatus)) {
            businessStatusVal = Integer.valueOf(businessStatus);
            businessStatus = EnumUtils.valueEquals(businessStatusVal, BusinessStatusEnum.BUSINESS) ||
                    EnumUtils.valueEquals(businessStatusVal, BusinessStatusEnum.ABORTED)
                    ? EnumUtils.getNameByValue(BusinessStatusEnum.class, businessStatusVal)
                    : BusinessStatusEnum.FINISHED.getName();
            businessMtime = r.getBusinessMtime();
            if (
                    EnumUtils.valueEquals(businessStatusVal, BusinessStatusEnum.BUSINESS) &&
                            StringUtils.isNotBlank(businessMtime)
            ) {
                r.setBusinessMtime(DateCalculationUtil.calculateDateDiff(DateUtil.parse(businessMtime)));
            }
            r.setBusinessStatus(businessStatus);
        }

        // 1.2 更新联系人数字
        Long id = r.getId();
        if (MapUtils.isNotEmpty(contactCountMap) && contactCountMap.containsKey(id)) {
            r.setContactNumber(contactCountMap.get(id));
        } else {
            r.setContactNumber(0);
        }

        // 1.3 对金额相关数值进行调整
        String ygmll = r.getYgmll();
        if (ObjectUtils.isNotEmpty(ygmll)) {
            r.setYgmll(new BigDecimal(ygmll).multiply(BigDecimal.valueOf(100)).stripTrailingZeros().toPlainString() + "%");
        }
        DecimalFormat decimalFormat = new DecimalFormat("#,##0.00");
        r.setExpectedOrderAmount(DecimalFormatUtil.setAndValidate(r.getExpectedOrderAmount(), 2, RoundingMode.DOWN, decimalFormat));
        r.setPreSalesLaborInput(DecimalFormatUtil.setAndValidate(r.getPreSalesLaborInput(), 2, RoundingMode.DOWN, decimalFormat));
        r.setSqfytr(DecimalFormatUtil.setAndValidate(r.getSqfytr(), 2, RoundingMode.DOWN, decimalFormat));

        // 1.4 工时人天相关
        String preSaleHours = r.getPreSaleHours();
        String afterSaleHours = r.getAfterSaleHours();
        String studentHours = r.getStudentHours();
        if (StringUtils.isNotBlank(preSaleHours)) {
            r.setBeforeSalesManDays(CommonUtils.unitConversion(new BigDecimal(preSaleHours)));
        }
        if (StringUtils.isNotBlank(afterSaleHours)) {
            r.setAfterSalesManDays(CommonUtils.unitConversion(new BigDecimal(afterSaleHours)));
        }
        if (StringUtils.isNotBlank(studentHours)) {
            r.setStudentManDay(CommonUtils.unitConversion(new BigDecimal(studentHours)));
        }
    }
}
