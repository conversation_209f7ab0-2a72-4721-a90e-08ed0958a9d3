package com.gok.pboot.pms.cost.enums;

import com.gok.pboot.pms.enumeration.ValueEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 工单类别枚举
 * (只包含售后交付类型便于编码，若需完整的工单类别得从工单类别管理获取)
 *
 * <AUTHOR>
 * @date 2025/03/26
 */
@Getter
@AllArgsConstructor
public enum TaskCategoryEnum implements ValueEnum<Integer> {

    /**
     * 项目管理
     */
    PROJECT_MANAGEMENT(1, "项目管理", 31),
    /**
     * 项目交付
     */
    PROJECT_DELIVERY(2, "项目交付", 7);

    private final Integer value;

    private final String name;

    private final int limitTime;
}