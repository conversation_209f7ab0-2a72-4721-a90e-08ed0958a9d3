package com.gok.pboot.pms.service.impl;

import com.gok.pboot.pms.mapper.EntityOptionMapper;
import com.gok.pboot.pms.service.IEntityOptionService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * - 实体选项服务实现 -
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
@AllArgsConstructor
public class EntityOptionServiceImpl implements IEntityOptionService {

    private final EntityOptionMapper mapper;

    @Override
    public boolean entityHasSign(Long entityId, String sign) {
        return mapper.existsByEntityIdAndSign(entityId, sign);
    }
}
