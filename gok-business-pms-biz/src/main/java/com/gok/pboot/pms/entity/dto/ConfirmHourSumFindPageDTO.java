package com.gok.pboot.pms.entity.dto;

import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.common.validate.constraint.SelectMonth;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * @Auther chenhc
 * @Date 2022-08-30 14:19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ConfirmHourSumFindPageDTO extends PageRequest {
    /**
     * 所属项目ID
     */
    private Long projectId;

    /**
     * 选择月份
     */
    @NotBlank(message = "选择月份不能为空")
    @SelectMonth(message = "选择月份格式:【yyyy-MM】")
    private String selectMonth;

    /**
     * 人员状态
     */
    private Integer personnelStatus;

    /**
     * 用户ID
     */
    private List<Long> userIds;

    /**
     * 用户名称
     */
    private List<String> userNames;

    /**
     * 用户ID集合实习
     */
    private List<Long> userIdsSX;
    /**
     * 用户ID集合正式
     */
    private List<Long> userIdsZS;

}
