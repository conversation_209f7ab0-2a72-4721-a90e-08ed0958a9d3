package com.gok.pboot.pms.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 项目台帐
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectAccountVO {

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目编码
     */
    private String projectNumber;

    /**
     * 是否内部项目
     * {@link com.gok.pboot.pms.enumeration.IsNoInternalProjectEnum}
     */
    private Integer internalProject;

    /**
     * 项目状态
     */
    private String xmzt;

    /**
     * 技术类型
     */
    private String jslxbb;

    /**
     * 客户经理
     */
    private String xmxsry;

    /**
     * 项目经理
     */
    private String xmjl;

    /**
     * 售前经理
     */
    private String sqjl;

    /**
     * 项目负责人
     */
    private String xmfzr;

    /**
     * 业务归属部门(一级部门)
     */
    private String ywgsbm;

    /**
     * 业务归属部门(二级部门)
     */
    private String ywgsbmejbm;

    /**
     * 项目归属部门(二级部门)
     */
    private String xmgsbmejbm;

    /**
     * 签约客户
     */
    private String qykh;

    /**
     * 最终客户
     */
    private String zzkh;


}
