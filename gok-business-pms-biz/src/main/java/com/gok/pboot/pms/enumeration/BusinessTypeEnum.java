package com.gok.pboot.pms.enumeration;

import cn.hutool.core.util.StrUtil;

/**
 * 项目-业务类型枚举
 *
 * <AUTHOR>
 */
public enum BusinessTypeEnum implements ValueEnum<Integer> {
    /**
     * 教学相关
     */
    teaching(0, "教学相关"),
    /**
     * 非教学相关
     */
    non_teaching(1, "非教学相关");

    //值
    private Integer value;
    //名称
    private String name;

    BusinessTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    /**
     * 获取值
     *
     * @return Integer
     */
    @Override
    public Integer getValue() {
        return value;
    }

    /**
     * 获取名称
     *
     * @return String
     */
    @Override
    public String getName() {
        return name;
    }

    public static String getNameByVal(Integer value) {
        for (BusinessTypeEnum businessTypeEnum : BusinessTypeEnum.values()) {
            if (businessTypeEnum.value.equals(value)) {
                return businessTypeEnum.name;
            }
        }
        return StrUtil.EMPTY;
    }

    public static Integer getValByName(String name) {
        for (BusinessTypeEnum businessTypeEnum : BusinessTypeEnum.values()) {
            if (businessTypeEnum.getName().equals(name)) {
                return businessTypeEnum.getValue();
            }
        }
        return null;
    }

    public static BusinessTypeEnum getBusinessTypeEnum(Integer value) {
        for (BusinessTypeEnum businessTypeEnum : BusinessTypeEnum.values()) {
            if (businessTypeEnum.value.equals(value)) {
                return businessTypeEnum;
            }
        }
        return null;
    }
}
