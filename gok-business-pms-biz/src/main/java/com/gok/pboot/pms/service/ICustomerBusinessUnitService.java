package com.gok.pboot.pms.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.entity.CustomerBusinessUnit;
import com.gok.pboot.pms.entity.dto.CustomerBusinessSearchDTO;
import com.gok.pboot.pms.entity.dto.CustomerBusinessUnitPageDTO;
import com.gok.pboot.pms.entity.vo.CustomerBusinessListVO;
import com.gok.pboot.pms.entity.vo.CustomerBusinessUnitPageVO;
import com.gok.pboot.pms.entity.vo.CustomerBusinessUnitProjectVO;
import com.gok.pboot.pms.entity.vo.CustomerBusinessUnitVO;

import java.util.List;

/**
 * <p>
 * 客户经营单元表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-12
 **/
public interface ICustomerBusinessUnitService extends IService<CustomerBusinessUnit> {

    /**
     * 批量逻辑删除
     *
     * @param list 要删除的id集合[]
     */
    void batchDel(List<Long> list);

    /**
     * 批量保存
     *
     * @param list
     */
    void saveList(List<CustomerBusinessUnit> list,Integer source);

    /**
     * 查询列表
     *
     * @param qo 查询参数
     * @return list
     */
    List<CustomerBusinessUnit> findList(CustomerBusinessUnit qo);

    /**
     * 分页查询客户
     *
     * @param dto
     * @return
     */
    Page<CustomerBusinessUnitPageVO> findPageList(CustomerBusinessUnitPageDTO dto);

    /**
     * 查询简单列表
     *
     * @param businessId 经营单元id
     * @return list
     */
    List<CustomerBusinessUnitPageVO> selectSimplyList(Long businessId);

    /**
     * 删除客户关联的经营单元
     *
     * @param ids
     */
    void batchDelete(List<Long> ids);

    /**
     * 新增所属客户同步至OA
     *
     * @param customerBusinessUnit
     */
    void addCustomerBusinessUnitSyncOA(CustomerBusinessUnit customerBusinessUnit);

    /**
     * 修改所属客户同步至OA
     *
     * @param customerBusinessUnit
     */
    void updateCustomerBusinessUnitSyncOA(CustomerBusinessUnit customerBusinessUnit);

    /**
     * 删除所属客户同步至OA
     *
     * @param id
     */
    void deleteCustomerBusinessUnitSyncOA(String id);

    /**
     * 同步OA公共字典缓存
     */
    void saveSelectItemSyncOA();


    /**
     * 获取名称列表
     *
     * @param customerBusinessSearchDTO
     * @return
     */
    List<CustomerBusinessListVO> findNameList(CustomerBusinessSearchDTO customerBusinessSearchDTO);

    /**
     * 获取详情
     *
     * @param id 主键id
     * @return
     */
   CustomerBusinessUnitVO getById(Long id);


    /**
     * 校验客户是否关联商机及项目
     * @param id
     * @return
     */
   CustomerBusinessUnitProjectVO checkUnit(Long id);
}
