package com.gok.pboot.pms.cost.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.bcp.admin.vo.DictKvVo;
import com.gok.components.common.constant.SecurityConstants;
import com.gok.module.excel.api.handler.SelectedSheetWriteHandler;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.Util.DateUtil;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.exception.ServiceException;
import com.gok.pboot.pms.cost.entity.domain.CostPersonnelInformation;
import com.gok.pboot.pms.cost.entity.dto.*;
import com.gok.pboot.pms.cost.entity.vo.CostPersonnelInformationVO;
import com.gok.pboot.pms.cost.entity.vo.ExitPersonInfoVO;
import com.gok.pboot.pms.cost.entity.vo.PersonPartInfoVO;
import com.gok.pboot.pms.cost.entity.vo.SupplierInfoVO;
import com.gok.pboot.pms.cost.enums.*;
import com.gok.pboot.pms.cost.mapper.CostIncomeCalculationDetailMapper;
import com.gok.pboot.pms.cost.mapper.CostPersonnelInformationMapper;
import com.gok.pboot.pms.cost.service.ICostPersonnelInformationService;
import com.gok.pboot.pms.cost.service.ISupplierService;
import com.gok.pboot.pms.entity.vo.SysUserDataScopeVO;
import com.gok.pboot.pms.handler.ProjectScopeHandle;
import com.gok.pboot.pms.service.IDictService;
import com.gok.pboot.service.entity.hrm.vo.HrmStaffRosterVo;
import com.gok.pboot.service.feign.RemoteEhrService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.text.NumberFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 人员信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Service
@RequiredArgsConstructor
public class CostPersonnelInformationServiceImpl extends ServiceImpl<CostPersonnelInformationMapper, CostPersonnelInformation>
        implements ICostPersonnelInformationService {

    private final ISupplierService supplierService;
    private final RemoteEhrService remoteEhrService;
    private final IDictService dictService;

    private final CostIncomeCalculationDetailMapper costCalculationDetailMapper;
    private final CostPersonnelInformationMapper costPersonnelInformationMapper;
    private final ProjectScopeHandle projectScopeHandle;

    private static final String CONTENT_TYPE_SHEET = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
    private static final String PARAM_CONTENT_DISPOSITION = "Content-disposition";
    private static final String CONTENT_DISPOSITION = "attachment;filename*=utf-8''";
    private static final String XLSX_SUFFIX = ".xlsx";

    private static final String FILE_NAME = "人员信息表";
    private static final String TAX_RATE = "税率";
    private static final String USER_ID = "userIds";
    private static final String WORK_CODE_LIST = "workCodeList";
    private static final String IDENTIFICATION_CODE = "Dsf";
    private static final String GOK_PREFIX = "Gok";
    private static final String PERCENTAGE_SIX = "6%";

    /**
     * 保存或更新人员信息
     *
     * @param dto dto实体
     * @return {@link List}<{@link String}>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> saveOrUpdatePersonInfo(CostPersonnelInformationDTO dto) {
        // 存储失败信息
        List<String> errorMsg = new ArrayList<>();
        // 存储添加人员信息
        List<CostPersonnelInformation> insertCostPersonnelInformationList = new ArrayList<>();
        // 存储更新人员信息
        List<CostPersonnelInformation> updateCostPersonnelInformationList = new ArrayList<>();
        // 暂存添加人员信息
        List<PersonnelInformationDTO> insertPersonnelInformationList = new ArrayList<>();
        // 暂存更新人员信息
        List<PersonnelInformationDTO> updatePersonnelInformationList = new ArrayList<>();
        // 获取当前归属月份第三方人员在场、所有归属月份已离场信息MAP
        Map<String, Integer> presencePersonListMap = costPersonnelInformationMapper
                .getPersonInfoList(new PersonInfoConditionDTO().setPersonnelAttribute(PersonnelAttributeEnum.THIRD_PARTY.getValue())
                        .setStatus(PersonStatusTypeEnum.ZC.getValue())
                        .setProjectIds(Collections.singletonList(dto.getProjectId()))
                        .setBelongMonth(dto.getPersonnelInformationList().stream().distinct()
                                .map(PersonnelInformationDTO::getBelongMonth).collect(Collectors.toList())))
                .stream().collect(Collectors.toMap(p -> p.getName() + StrPool.DASHED + p.getWorkCode(), CostPersonnelInformationVO::getStatus));
        Map<String, Integer> exitPersonListMap = costPersonnelInformationMapper.getPersonInfoList(new PersonInfoConditionDTO()
                        .setPersonnelAttribute(PersonnelAttributeEnum.THIRD_PARTY.getValue())
                        .setProjectIds(Collections.singletonList(dto.getProjectId())).setStatus(PersonStatusTypeEnum.YLC.getValue()))
                .stream().collect(Collectors.toMap(p -> p.getName() + StrPool.DASHED + p.getWorkCode(), CostPersonnelInformationVO::getStatus));
        // 数据校验
        dto.getPersonnelInformationList().forEach(personInfo -> {
            if (PersonnelAttributeEnum.THIRD_PARTY.getValue().equals(personInfo.getPersonnelAttribute())) {
                String key = personInfo.getName() + StrPool.DASHED + (StrUtil.isNotBlank(personInfo.getWorkCode()) ? personInfo.getWorkCode() : StrUtil.EMPTY);
                if (StrUtil.isNotBlank(personInfo.getWorkCode()) && personInfo.getId() == null
                        && (presencePersonListMap.containsKey(key) || exitPersonListMap.containsKey(key))) {
                    errorMsg.add(personInfo.getName());
                } else {
                    if (ObjectUtil.isNotEmpty(personInfo.getId())) {
                        updatePersonnelInformationList.add(personInfo);
                    } else {
                        insertPersonnelInformationList.add(personInfo);
                    }
                }
            } else if (PersonnelAttributeEnum.GOK_STAFF.getValue().equals(personInfo.getPersonnelAttribute())) {
                if (ObjectUtil.isNotEmpty(personInfo.getId())) {
                    updatePersonnelInformationList.add(personInfo);
                } else {
                    insertPersonnelInformationList.add(personInfo);
                }
            }
        });
        // 获取人员信息ID集合
        List<Long> userIds = dto.getPersonnelInformationList().stream().map(PersonnelInformationDTO::getUserId).collect(Collectors.toList());
        // 获取人员信息MAP
        Map<Long, HrmStaffRosterVo> userInfoMap = remoteEhrService
                .getStaffRosterPageVoList(Collections.singletonMap(USER_ID, userIds), SecurityConstants.FROM_IN).getData()
                .stream().collect(Collectors.toMap(HrmStaffRosterVo::getUserId, u -> u, (a, b) -> b));
        // 获取字典税率MAP
        Map<String, String> dictMap = dictService.getDictKvList(TAX_RATE).getData()
                .stream().collect(Collectors.toMap(DictKvVo::getValue, DictKvVo::getName));

        // 获取最大工号对应的序列号
        String maxWorkCode = costPersonnelInformationMapper.getMaxWorkCode();
        Integer serialNum = 1;
        if (StrUtil.isNotEmpty(maxWorkCode)) {
            serialNum = Integer.parseInt(maxWorkCode.substring(3)) + 1;
        }

        // 数据处理
        final Integer[] currentSerialNum = {serialNum};
        insertPersonnelInformationList.forEach(personInfo -> {
            // 如果是第三方人员且没有工号，则分配新工号
            if (PersonnelAttributeEnum.THIRD_PARTY.getValue().equals(personInfo.getPersonnelAttribute())
                    && StrUtil.isEmpty(personInfo.getWorkCode())) {
                personInfo.setWorkCode(IDENTIFICATION_CODE + String.format("%05d", currentSerialNum[0]++));
            }
            insertCostPersonnelInformationList.add(this.convert(dto.getProjectId(), personInfo, userInfoMap, dictMap));
        });

        updatePersonnelInformationList.forEach(personInfo -> updateCostPersonnelInformationList
                .add(this.convert(dto.getProjectId(), personInfo, userInfoMap, dictMap)));
        // 保存或更新
        if (CollUtil.isNotEmpty(insertCostPersonnelInformationList)) {
            this.saveBatch(insertCostPersonnelInformationList);
        }
        if (CollUtil.isNotEmpty(updateCostPersonnelInformationList)) {
            costPersonnelInformationMapper.updateBatch(updateCostPersonnelInformationList);
        }
        return errorMsg;
    }

    /**
     * 根据条件获取在场/已离场人员信息
     *
     * @param dto dto实体
     * @return {@link List}<{@link CostPersonnelInformationVO}>
     */
    @Override
    public List<CostPersonnelInformationVO> getPersonInfoList(PersonInfoConditionDTO dto) {
        // 数据权限
        SysUserDataScopeVO dataPermission =
                projectScopeHandle.getDeliverManagementDataPermission("DELIVERY_HUMAN_RESOURCE_RYXX", dto.getProjectIds().get(0), dto.getIsAll());
        if (!Boolean.TRUE.equals(dataPermission.getIsAll())) {
            dto.setUserIds(dataPermission.getUserIdList());
        }

        // 获取人员信息列表
        List<CostPersonnelInformationVO> costPersonnelInformationList = costPersonnelInformationMapper
                .getPersonInfoList(dto);
        if (CollUtil.isEmpty(costPersonnelInformationList)) {
            return costPersonnelInformationList;
        }
        // 获取EHR人员信息
        List<Long> userIds = costPersonnelInformationList
                .stream().map(CostPersonnelInformationVO::getUserId).filter(Objects::nonNull).collect(Collectors.toList());
        Map<Long, HrmStaffRosterVo> userInfoMap = remoteEhrService
                .getStaffRosterPageVoList(Collections.singletonMap(USER_ID, userIds), SecurityConstants.FROM_IN).getData()
                .stream().collect(Collectors.toMap(HrmStaffRosterVo::getUserId, u -> u, (a, b) -> b));
        // 处理OA初始化数据
        costPersonnelInformationList.forEach(person -> {
            if (PersonnelAttributeEnum.GOK_STAFF.getValue().equals(person.getPersonnelAttribute()) && person.getRequestId() != null
                    && userInfoMap.containsKey(person.getUserId())) {
                HrmStaffRosterVo userInfo = userInfoMap.get(person.getUserId());
                String[] positionSplit = userInfo.getGradeName().split(StrPool.SLASH);
                person.setName(userInfo.getAliasName()).setWorkCode(userInfo.getWorkCode())
                        .setDeptId(userInfo.getDeptId()).setDeptName(userInfo.getDepartment())
                        .setJobId(userInfo.getJobTitleId()).setJobName(userInfo.getJobTitleMark())
                        .setPositionId(userInfo.getGradeId())
                        .setPositionName(StrUtil.isNotBlank(positionSplit[positionSplit.length - NumberUtils.INTEGER_ONE])
                                ? positionSplit[positionSplit.length - NumberUtils.INTEGER_ONE] : StrUtil.EMPTY);
            }
        });
        return this.likeCode(dto.getProjectIds(), costPersonnelInformationList);
    }

    /**
     * 根据条件获取含税报价信息
     *
     * @param dto dto实体
     * @return {@link BigDecimal}
     */
    @Override
    public BigDecimal getQuotationIncludeTax(AutoBringConditionDTO dto) {
        return costPersonnelInformationMapper.getQuotationIncludeTax(dto);
    }

    /**
     * 根据条件自动带出人员信息列表
     *
     * @param dto dto实体
     * @return {@link List}<{@link CostPersonnelInformationVO}>
     */
    @Override
    public List<CostPersonnelInformationVO> getPersonAutoBringInfo(AutoBringConditionDTO dto) {
        dto.setPersonnelAttribute(NumberUtils.INTEGER_ONE);
        // 获取自动带出的人员信息
        List<CostPersonnelInformationVO> costPersonnelInformationList = costPersonnelInformationMapper.getPersonAutoBringInfo(dto);
        if (CollUtil.isEmpty(costPersonnelInformationList)) {
            return costPersonnelInformationList;
        }
        return this.likeCode(Collections.emptyList(), Collections.singletonList(costPersonnelInformationList.get(NumberUtils.INTEGER_ZERO)));
    }

    /**
     * 根据条件获取新增国科人员信息
     *
     * @param dto dto实体
     * @return {@link List}<{@link PersonPartInfoVO}>
     */
    @Override
    public List<PersonPartInfoVO> getPersonPartInfo(PersonPartInfoDTO dto) {
        // 存储人员信息
        List<PersonPartInfoVO> personPartInfoList = new ArrayList<>();
        // 获取人员信息MAP
        Map<Long, HrmStaffRosterVo> userInfoMap = remoteEhrService
                .getStaffRosterPageVoList(Collections.singletonMap(USER_ID, dto.getUserIds()), SecurityConstants.FROM_IN).getData()
                .stream().collect(Collectors.toMap(HrmStaffRosterVo::getUserId, u -> u, (a, b) -> b));
        // 获取当前归属月份在场、所有归属月份已离场人员信息
        Map<Long, Integer> presencePersonList = costPersonnelInformationMapper
                .getPersonInfoList(new PersonInfoConditionDTO().setStatus(PersonStatusTypeEnum.ZC.getValue())
                        .setProjectIds(Collections.singletonList(dto.getProjectId()))
                        .setPersonnelAttribute(PersonnelAttributeEnum.GOK_STAFF.getValue())
                        .setBelongMonth(Collections.singletonList(dto.getBelongMonth())))
                .stream().collect(Collectors.toMap(CostPersonnelInformationVO::getUserId, CostPersonnelInformationVO::getStatus));
        Map<Long, Integer> exitPersonList = costPersonnelInformationMapper.getPersonInfoList(new PersonInfoConditionDTO()
                        .setProjectIds(Collections.singletonList(dto.getProjectId()))
                        .setPersonnelAttribute(PersonnelAttributeEnum.GOK_STAFF.getValue())
                        .setStatus(PersonStatusTypeEnum.YLC.getValue()))
                .stream().collect(Collectors.toMap(CostPersonnelInformationVO::getUserId, CostPersonnelInformationVO::getStatus));
        // 数据处理
        dto.getUserIds().forEach(userId -> personPartInfoList.add(this.dataHandler(userId, presencePersonList, exitPersonList, userInfoMap)));
        return personPartInfoList;
    }

    /**
     * 离场/再次入场
     *
     * @param dto dto实体
     * @return {@link Boolean}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean exitOrEntry(ExitOrDeleteOrEntryPersonDTO dto) {
        // 存储已离场人员信息
        List<CostPersonnelInformation> leavePersonInfoList = new ArrayList<>();
        // 获取人员信息
        List<CostPersonnelInformation> costPersonnelInformationList = this.listByIds(dto.getIds());
        List<Long> projectIds = costPersonnelInformationList.stream().distinct().map(CostPersonnelInformation::getProjectId).collect(Collectors.toList());
        // 获取当前归属月份在场人员信息、所有归属月份人员信息MAP
        Map<Long, String> presencePersonListMap = costPersonnelInformationMapper
                .getPersonInfoList(new PersonInfoConditionDTO().setStatus(PersonStatusTypeEnum.ZC.getValue())
                        .setProjectIds(projectIds)
                        .setBelongMonth(Collections.singletonList(String.valueOf(LocalDate.now().withDayOfMonth(NumberUtils.INTEGER_ONE)))))
                .stream().collect(Collectors.toMap(CostPersonnelInformationVO::getId, CostPersonnelInformationVO::getName));
        Map<String, CostPersonnelInformation> exitPersonListMap = this.list(new LambdaQueryWrapper<CostPersonnelInformation>()
                        .in(CostPersonnelInformation::getProjectId, projectIds)
                        .eq(CostPersonnelInformation::getStatus, PersonStatusTypeEnum.YLC.getValue()))
                .stream().collect(Collectors.toMap(CostPersonnelInformation::getWorkCode, p -> p, (a, b) -> b));
        // 离场/再次入场
        costPersonnelInformationList.forEach(personInfo -> {
            if (PersonStatusTypeEnum.ZC.getValue().equals(dto.getStatus()) && presencePersonListMap.containsKey(personInfo.getId())) {
                throw new ServiceException(personInfo.getName() + "处于在场/离场状态！");
            }
            if (PersonStatusTypeEnum.YLC.getValue().equals(dto.getStatus())
                    && exitPersonListMap.containsKey(personInfo.getWorkCode())) {
                leavePersonInfoList.add(exitPersonListMap.get(personInfo.getWorkCode()));
            }
            personInfo.setEntryTime(dto.getStatus().equals(PersonStatusTypeEnum.ZC.getValue()) ?
                            LocalDate.now() : (ObjectUtil.isNotEmpty(personInfo.getEntryTime()) ? personInfo.getEntryTime() : null))
                    .setLeaveTime(dto.getStatus().equals(PersonStatusTypeEnum.YLC.getValue()) ?
                            LocalDate.now() : (ObjectUtil.isNotEmpty(personInfo.getLeaveTime()) ? personInfo.getLeaveTime() : null))
                    .setStatus(dto.getStatus().equals(PersonStatusTypeEnum.YLC.getValue()) ?
                            PersonStatusTypeEnum.YLC.getValue() : PersonStatusTypeEnum.ZC.getValue())
                    .setBelongMonth(LocalDate.now().withDayOfMonth(NumberUtils.INTEGER_ONE));
            BaseBuildEntityUtil.buildUpdate(personInfo);
        });
        if (CollUtil.isNotEmpty(leavePersonInfoList)) {
            this.removeBatchByIds(leavePersonInfoList);
        }
        this.saveOrUpdateBatch(costPersonnelInformationList);
        return true;
    }

    /**
     * 删除
     *
     * @param dto dto实体
     * @return {@link ApiResult}<{@link Boolean}>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deletePersonInfo(ExitOrDeleteOrEntryPersonDTO dto) {
        return this.removeBatchByIds(dto.getIds());
    }

    /**
     * 导出
     *
     * @param response HTTP
     * @param dto      dto实体
     */
    @Override
    public void export(HttpServletResponse response, PersonInfoConditionDTO dto) {
        try {
            List<CostPersonnelInformationVO> presencePersonInfoList = new ArrayList<>();
            List<CostPersonnelInformationVO> exitPersonInfoList;
            List<ExitPersonInfoVO> exitResultPersonInfoList = new ArrayList<>();
            WriteSheet writeSheet1 = EasyExcel.writerSheet(NumberUtils.INTEGER_ZERO, PersonStatusTypeEnum.ZC.getName())
                    .registerWriteHandler(new SelectedSheetWriteHandler(CostPersonnelInformationVO.class)).head(CostPersonnelInformationVO.class).build();
            WriteSheet writeSheet2 = EasyExcel.writerSheet(NumberUtils.INTEGER_ONE, PersonStatusTypeEnum.YLC.getName())
                    .registerWriteHandler(new SelectedSheetWriteHandler(ExitPersonInfoVO.class)).head(ExitPersonInfoVO.class).build();
            if (!dto.getExportStatus()) {
                // 数据权限
                SysUserDataScopeVO dataPermission =
                        projectScopeHandle.getDeliverManagementDataPermission("DELIVERY_HUMAN_RESOURCE_RYXX", dto.getProjectIds().get(0), dto.getIsAll());
                if (!Boolean.TRUE.equals(dataPermission.getIsAll())) {
                    dto.setUserIds(dataPermission.getUserIdList());
                }
                // 获取字典税率MAP
                Map<String, String> dictMap = dictService.getDictKvList(TAX_RATE).getData()
                        .stream().collect(Collectors.toMap(DictKvVo::getValue, DictKvVo::getName));
                // 获取数据
                presencePersonInfoList = costPersonnelInformationMapper.getPersonInfoList(dto);
                exitPersonInfoList = costPersonnelInformationMapper.getPersonInfoList(dto.setStatus(PersonStatusTypeEnum.YLC.getValue()));
                // 数据转换
                List<String> confirmedPersonalList = getConfirmedPersonalList(dto.getProjectIds());
                this.convert(presencePersonInfoList, dictMap, confirmedPersonalList);
                this.convert(exitPersonInfoList, dictMap, confirmedPersonalList);
                exitResultPersonInfoList = BeanUtil.copyToList(exitPersonInfoList, ExitPersonInfoVO.class);
            }
            // 响应体信息
            response.setContentType(CONTENT_TYPE_SHEET);
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            response.setHeader(PARAM_CONTENT_DISPOSITION, CONTENT_DISPOSITION + URLEncoder.encode(FILE_NAME, StandardCharsets.UTF_8.name()) + XLSX_SUFFIX);
            // 设置sheet名
            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).autoCloseStream(Boolean.FALSE).build();
            // 数据
            excelWriter.write(presencePersonInfoList, writeSheet1);
            if (!dto.getExportStatus()) {
                excelWriter.write(exitResultPersonInfoList, writeSheet2);
            }
            // 关闭
            excelWriter.finish();
            response.flushBuffer();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取收入测算已确认的人员集合
     *
     * @return
     */
    private List<String> getConfirmedPersonalList(List<Long> projectIds) {
        CostIncomeCalculationDTO calcIncomeCalculationDTO = CostIncomeCalculationDTO.builder()
                .projectIds(projectIds)
                .confirmStatus(ConfirmStatusEnum.CONFIRM.getValue())
                .build();
        List<String> confirmedPersonalList = CollUtil.emptyIfNull(costCalculationDetailMapper.findList(calcIncomeCalculationDTO)).stream()
                .map(e -> StrUtil.format("{}-{}-{}", e.getProjectId(), e.getWorkCode(), e.getBelongMonth()))
                .collect(Collectors.toList());
        return Optional.ofNullable(confirmedPersonalList).orElse(new ArrayList<>());
    }

    /**
     * 导入
     *
     * @param projectId 项目ID
     * @param dtoList   dto列表
     * @return {@link List}<{@link String}>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> importPersonData(Long projectId, List<PersonImportInfoDTO> dtoList) {
        // 行数
        int row = 3;
        // 存储不合法信息
        List<String> storageMsg = new ArrayList<>();
        if (CollUtil.isEmpty(dtoList)) {
            storageMsg.add("导入数据不能为空");
            return storageMsg;
        }
        // 存储校验人员信息MAP【工号、归属月份】
        Map<String, String> checkMap = new HashMap<>();
        // 获取人员信息MAP
        Map<String, HrmStaffRosterVo> userInfoMap = new HashMap<>();
        // 存储人员信息
        List<CostPersonnelInformation> costPersonnelInformationList = new ArrayList<>();
        // 暂存人员信息
        List<PersonImportInfoDTO> tempPersonInfoList = new ArrayList<>();
        // 获取所有归属月份在场、已离场、第三方人员信息MAP
        List<String> presencePersonList = costPersonnelInformationMapper.getPersonInfoList(new PersonInfoConditionDTO()
                        .setStatus(PersonStatusTypeEnum.ZC.getValue())
                        .setProjectIds(Collections.singletonList(projectId)))
                .stream().map(p -> p.getWorkCode() + StrPool.DASHED + p.getBelongMonth().substring(0, 7)).collect(Collectors.toList());
        List<String> exitPersonList = costPersonnelInformationMapper.getPersonInfoList(new PersonInfoConditionDTO()
                        .setStatus(PersonStatusTypeEnum.YLC.getValue())
                        .setProjectIds(Collections.singletonList(projectId)))
                .stream().map(p -> p.getWorkCode() + StrPool.DASHED + p.getBelongMonth().substring(0, 7)).collect(Collectors.toList());
        AutoBringConditionDTO autoBringConditionDTO = new AutoBringConditionDTO();
        autoBringConditionDTO.setPersonnelAttribute(PersonnelAttributeEnum.THIRD_PARTY.getValue());
        Map<String, CostPersonnelInformationVO> dsfInfoMap = costPersonnelInformationMapper.getPersonAutoBringInfo(autoBringConditionDTO)
                .stream().collect(Collectors.toMap(CostPersonnelInformationVO::getWorkCode, person -> person, (a, b) -> a));
        // 获取工号列表
        List<String> workCodeList = dtoList.stream().filter(p -> PersonnelAttributeEnum.GOK_STAFF.getName().equals(p.getPersonnelAttributeName()))
                .map(PersonImportInfoDTO::getWorkCode).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(workCodeList)) {
            userInfoMap = remoteEhrService
                    .getStaffRosterPageVoList(Collections.singletonMap(WORK_CODE_LIST, workCodeList), SecurityConstants.FROM_IN).getData()
                    .stream().collect(Collectors.toMap(HrmStaffRosterVo::getWorkCode, u -> u, (a, b) -> b));
        }
        // 数据校验【在场/已离场/不合法/重复】
        for (PersonImportInfoDTO person : dtoList) {
            String str = this.checkImportFormatAndData(person, presencePersonList, userInfoMap, exitPersonList, checkMap);
            if (StrUtil.isNotEmpty(str)) {
                storageMsg.add("第" + (row++) + "行: " + str);
            } else {
                tempPersonInfoList.add(person);
            }
        }
        // 获取供应商MAP
        Map<String, Long> supplierMap = supplierService.getAllSupplierInfo()
                .stream().collect(Collectors.toMap(SupplierInfoVO::getSupplierName, SupplierInfoVO::getId, (a, b) -> b));
        // 获取字典税率MAP
        Map<String, String> dictMap = dictService.getDictKvList(TAX_RATE).getData()
                .stream().collect(Collectors.toMap(DictKvVo::getName, DictKvVo::getValue));

        // 获取最大工号对应的序列号
        String maxWorkCode = costPersonnelInformationMapper.getMaxWorkCode();
        Integer serialNum = 1;
        if (StrUtil.isNotEmpty(maxWorkCode)) {
            serialNum = Integer.parseInt(maxWorkCode.substring(3)) + 1;
        }

        // 数据处理
        final Integer[] currentSerialNum = {serialNum};
        for (PersonImportInfoDTO person : tempPersonInfoList) {
            // 如果是第三方人员且没有工号，则分配新工号
            if (PersonnelAttributeEnum.THIRD_PARTY.getName().equals(person.getPersonnelAttributeName())
                    && StrUtil.isEmpty(person.getWorkCode())) {
                person.setWorkCode(IDENTIFICATION_CODE + String.format("%05d", currentSerialNum[0]++));
            }

            CostPersonnelInformation personnelInformation = this.convert(projectId, person, dictMap, userInfoMap, supplierMap, dsfInfoMap);
            if (ObjectUtil.isNotEmpty(personnelInformation)) {
                costPersonnelInformationList.add(personnelInformation);
            }
        }

        if (CollUtil.isNotEmpty(costPersonnelInformationList)) {
            this.saveOrUpdateBatch(costPersonnelInformationList);
        }
        return storageMsg;
    }

    /**
     * 归档人员信息
     *
     * @return {@link Boolean}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean filingPersonInfo() {
        // 获取上个月所有项目在场人员信息
        List<CostPersonnelInformation> presencePersonInfoList = this.list(new LambdaQueryWrapper<CostPersonnelInformation>()
                .eq(CostPersonnelInformation::getStatus, PersonStatusTypeEnum.ZC.getValue())
                .eq(CostPersonnelInformation::getBelongMonth, LocalDate.now().minusMonths(NumberUtils.INTEGER_ONE).withDayOfMonth(NumberUtils.INTEGER_ONE)));
        if (CollUtil.isNotEmpty(presencePersonInfoList)) {
            // 数据处理
            presencePersonInfoList.forEach(person -> {
                person.setLeaveTime(null)
                        .setBelongMonth(LocalDate.now().withDayOfMonth(NumberUtils.INTEGER_ONE))
                        .setMtime(new Timestamp(System.currentTimeMillis()));
                person.setId(IdWorker.getId());
            });
            this.saveBatch(presencePersonInfoList);
        }
        return Boolean.TRUE;
    }

    /**
     * 获取第三方最大工号
     *
     * @return {@link String}
     */
    public String getWorkCode() {
        // 流水号(5位)
        String serialNumber = "00001";
        // 获取第三方人员最大工号
        String maxWorkCode = costPersonnelInformationMapper.getMaxWorkCode();
        if (StrUtil.isNotEmpty(maxWorkCode)) {
            Integer serialNum = Integer.valueOf(maxWorkCode.substring(3));
            serialNum++;
            serialNumber = String.format("%05d", serialNum);
        }
        return IDENTIFICATION_CODE + serialNumber;
    }

    /**
     * 数据转换
     *
     * @param projectId   项目ID
     * @param dto         dto实体
     * @param userInfoMap 人员信息MAP
     * @param dictMap     字典税率MAP
     * @return {@link CostPersonnelInformation}
     */
    public CostPersonnelInformation convert(Long projectId, PersonnelInformationDTO dto, Map<Long, HrmStaffRosterVo> userInfoMap, Map<String, String> dictMap) {
        CostPersonnelInformation costPersonnelInformation = new CostPersonnelInformation();
        boolean isCurrentDate = LocalDate.now().withDayOfMonth(1).equals(LocalDate.parse(dto.getBelongMonth()));
        // 国科人员/第三方
        if (userInfoMap.containsKey(dto.getUserId())) {
            HrmStaffRosterVo userInfo = userInfoMap.get(dto.getUserId());
            String[] positionSplit = userInfo.getGradeName().split(StrPool.SLASH);
            costPersonnelInformation.setUserId(isCurrentDate ? userInfo.getUserId() : dto.getUserId())
                    .setName(isCurrentDate ? userInfo.getAliasName() : dto.getName())
                    .setWorkCode(isCurrentDate ? userInfo.getWorkCode() : dto.getWorkCode())
                    .setDeptId(isCurrentDate ? userInfo.getDeptId() : dto.getDeptId())
                    .setDeptName(StrUtil.isNotBlank(dto.getDeptName()) ? dto.getDeptName() : StrUtil.EMPTY)
                    .setJobId(isCurrentDate ? userInfo.getJobTitleId() : dto.getJobId())
                    .setJobName(isCurrentDate ? userInfo.getJobTitleMark() : dto.getJobName())
                    .setPositionId(isCurrentDate ? userInfo.getGradeId() : dto.getPositionId())
                    .setPositionName(isCurrentDate ? (StrUtil.isNotEmpty(positionSplit[positionSplit.length - NumberUtils.INTEGER_ONE]) ?
                            positionSplit[positionSplit.length - NumberUtils.INTEGER_ONE] : StrUtil.EMPTY) : dto.getPositionName());
        } else {
            costPersonnelInformation.setName(dto.getName())
                    .setWorkCode(StrUtil.isNotEmpty(dto.getWorkCode()) ? dto.getWorkCode() : getWorkCode())
                    .setDeptId(dto.getDeptId())
                    .setDeptName(StrUtil.isNotBlank(dto.getDeptName()) ? dto.getDeptName() : StrUtil.EMPTY)
                    .setJobName(dto.getJobName())
                    .setPositionName(dto.getPositionName());
        }
        // 通用
        costPersonnelInformation.setProjectId(projectId)
                .setPersonnelAttribute(dto.getPersonnelAttribute())
                .setAvailableStatus(AvailableStatusTypeEnum.KY.getValue())
                .setEntryTime(StrUtil.isNotEmpty(dto.getEntryTime()) ? LocalDate.parse(dto.getEntryTime()) : LocalDate.now())
                .setStatus(PersonStatusTypeEnum.ZC.getValue())
                .setDurationDays(dto.getDurationDays())
                .setDomicile(dto.getDomicile())
                .setQuotationType(dto.getQuotationType())
                .setQuotationIncludeTax(QuotationTypeEnum.GDFL.getValue().equals(dto.getQuotationType()) ? null :
                        ObjectUtil.isNotEmpty(dto.getQuotationIncludeTax()) ? dto.getQuotationIncludeTax() : null)
                .setQuotedRateId(StrUtil.isNotBlank(dto.getQuotedRateId()) ? Integer.valueOf(dto.getQuotedRateId()) : null)
                .setQuotationExcludeTax(QuotationTypeEnum.GDFL.getValue().equals(dto.getQuotationType()) ? null :
                        this.calculateExcludeTax(ObjectUtil.isNotEmpty(dto.getQuotationIncludeTax()) ?
                                dto.getQuotationIncludeTax() : null, StrUtil.isNotEmpty(dto.getQuotedRateId()) ?
                                dictMap.get(dto.getQuotedRateId()) : null))
                .setFlatRate(!QuotationTypeEnum.GDFL.getValue().equals(dto.getQuotationType()) ? null :
                        ObjectUtil.isNotEmpty(dto.getFlatRate()) ? dto.getFlatRate() : null)
                .setForeignPurchaseUnitPriceIncludeTax(dto.getForeignPurchaseUnitPriceIncludeTax())
                .setForeignPurchaseTaxRateId(StrUtil.isNotEmpty(dto.getForeignPurchaseTaxRateId()) ?
                        Integer.valueOf(dto.getForeignPurchaseTaxRateId()) : null)
                .setForeignPurchaseUnitPriceExcludeTax(this.calculateExcludeTax(ObjectUtil.isNotEmpty(dto.getForeignPurchaseUnitPriceIncludeTax()) ?
                        dto.getForeignPurchaseUnitPriceIncludeTax() : null, StrUtil.isNotEmpty(dto.getForeignPurchaseTaxRateId()) ?
                        dictMap.get(dto.getForeignPurchaseTaxRateId()) : null))
                .setBelongMonth(LocalDate.parse(dto.getBelongMonth()))
                .setId(ObjectUtil.isNotEmpty(dto.getId()) ? dto.getId() : IdWorker.getId());
        if (ObjectUtil.isEmpty(dto.getId())) {
            BaseBuildEntityUtil.buildInsertNoId(costPersonnelInformation);
        }
        BaseBuildEntityUtil.buildUpdate(costPersonnelInformation);
        return costPersonnelInformation;
    }

    /**
     * 数据转换
     *
     * @param costPersonnelInformationList 结果列表
     * @param dictMap                      字典税率MAP
     * @param confirmedPersonalList        归属月份MAP
     */
    public void convert(List<CostPersonnelInformationVO> costPersonnelInformationList,
                        Map<String, String> dictMap,
                        List<String> confirmedPersonalList) {
        if (CollUtil.isEmpty(costPersonnelInformationList)) {
            return;
        }
        // 日期格式
        DateTimeFormatter yearMonthFormatter = DateTimeFormatter.ofPattern("yyyy-MM");
        // %格式化
        NumberFormat percentInstance = NumberFormat.getPercentInstance();
        percentInstance.setMaximumFractionDigits(2);
        // 数据处理
        costPersonnelInformationList.forEach(personInfo ->
                personInfo.setEditStatus(
                                confirmedPersonalList.contains(StrUtil.format("{}-{}-{}", personInfo.getProjectId(), personInfo.getWorkCode(), personInfo.getBelongMonth()))
                                        ? AvailableStatusTypeEnum.BKY.getValue()
                                        : AvailableStatusTypeEnum.KY.getValue()
                        )
                        .setPersonnelAttributeName(EnumUtils.getNameByValue(PersonnelAttributeEnum.class, personInfo.getPersonnelAttribute()))
                        .setAvailableStatusName(EnumUtils.getNameByValue(AvailableStatusTypeEnum.class, personInfo.getAvailableStatus()))
                        .setStatusName(EnumUtils.getNameByValue(PersonStatusTypeEnum.class, personInfo.getStatus()))
                        .setQuotationTypeName(EnumUtils.getNameByValue(QuotationTypeEnum.class, personInfo.getQuotationType()))
                        .setQuotedRate(dictMap.get(personInfo.getQuotedRateId()))
                        .setForeignPurchaseTaxRate(dictMap.get(personInfo.getForeignPurchaseTaxRateId()))
                        .setFlatRateName(StrUtil.isNotEmpty(personInfo.getFlatRate()) ?
                                percentInstance.format(new BigDecimal(personInfo.getFlatRate())) : null)
                        .setBelongMonth(StrUtil.isNotEmpty(personInfo.getBelongMonth()) ?
                                LocalDate.parse(personInfo.getBelongMonth()).format(yearMonthFormatter) : StrUtil.EMPTY)
                        .setMtime(StrUtil.isNotEmpty(personInfo.getMtime()) ? personInfo.getMtime().split(StrUtil.SPACE)[NumberUtils.INTEGER_ZERO] : StrUtil.EMPTY));
    }

    /**
     * 数据处理
     *
     * @param userId             国科人员ID
     * @param presencePersonList 在场人员信息MAP
     * @param exitPersonList     已离场人员信息MAP
     * @param userInfoMap        国科人员信息MAP
     * @return {@link PersonPartInfoVO}
     */
    public PersonPartInfoVO dataHandler(Long userId, Map<Long, Integer> presencePersonList,
                                        Map<Long, Integer> exitPersonList, Map<Long, HrmStaffRosterVo> userInfoMap) {
        PersonPartInfoVO personPartInfo = new PersonPartInfoVO();
        if (presencePersonList.containsKey(userId)) {
            personPartInfo.setUserId(userId)
                    .setStatus(presencePersonList.get(userId))
                    .setStatusName(EnumUtils.getNameByValue(PersonStatusTypeEnum.class, presencePersonList.get(userId)));
            return personPartInfo;
        }
        if (exitPersonList.containsKey(userId)) {
            personPartInfo.setUserId(userId)
                    .setStatus(exitPersonList.get(userId))
                    .setStatusName(EnumUtils.getNameByValue(PersonStatusTypeEnum.class, exitPersonList.get(userId)));
            return personPartInfo;
        }
        if (userInfoMap.containsKey(userId)) {
            HrmStaffRosterVo userInfo = userInfoMap.get(userId);
            String[] positionSplit = userInfo.getGradeName().split(StrPool.SLASH);
            personPartInfo.setUserId(userInfo.getUserId())
                    .setName(userInfo.getAliasName())
                    .setWorkCode(userInfo.getWorkCode())
                    .setDeptId(userInfo.getDeptId())
                    .setDeptName(userInfo.getDepartment())
                    .setJobId(userInfo.getJobTitleId())
                    .setJobName(userInfo.getJobTitleMark())
                    .setPositionId(userInfo.getGradeId())
                    .setPositionName(StrUtil.isNotEmpty(positionSplit[positionSplit.length - 1]) ?
                            positionSplit[positionSplit.length - 1] : StrUtil.EMPTY);
        } else {
            personPartInfo.setUserId(userId);
        }
        return personPartInfo;
    }

    /**
     * 计算不含税
     *
     * @param includeTax 含税
     * @param quotedRate 税率
     * @return {@link BigDecimal}
     */
    public BigDecimal calculateExcludeTax(BigDecimal includeTax, String quotedRate) {
        if (includeTax == null || quotedRate == null) {
            return null;
        }
        BigDecimal rate = new BigDecimal(quotedRate.replace("%", StrUtil.EMPTY)).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
        return includeTax.divide(BigDecimal.ONE.add(rate), 2, RoundingMode.HALF_UP);
    }

    /**
     * 校验第三方工号是否合理
     *
     * @param workCode 第三方工号
     * @return {@link Boolean}
     */
    public Boolean checkWorkCode(String workCode) {
        if (!workCode.startsWith(IDENTIFICATION_CODE)) {
            return Boolean.FALSE;
        }
        String serialNumber = workCode.replace(IDENTIFICATION_CODE, StrUtil.EMPTY);
        if (serialNumber.length() != 5 && !serialNumber.matches("\\d{5}")) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    /**
     * 导入数据转换
     *
     * @param projectId   项目ID
     * @param dto         dto实体
     * @param dictMap     税率字典MAP
     * @param userInfoMap 人员信息MAP
     * @param supplierMap 供应商信息MAP
     * @return {@link CostPersonnelInformation}
     */
    public CostPersonnelInformation convert(Long projectId, PersonImportInfoDTO dto, Map<String, String> dictMap, Map<String,
            HrmStaffRosterVo> userInfoMap, Map<String, Long> supplierMap, Map<String, CostPersonnelInformationVO> dsfInfoMap) {
        Integer personnelAttribute = EnumUtils.getValueByName(PersonnelAttributeEnum.class, dto.getPersonnelAttributeName());
        Integer quotationType = EnumUtils.getValueByName(QuotationTypeEnum.class, dto.getQuotationTypeName());
        CostPersonnelInformation costPersonnelInformation = new CostPersonnelInformation();
        // 国科人员/第三方
        if (PersonnelAttributeEnum.GOK_STAFF.getValue().equals(personnelAttribute) && userInfoMap.containsKey(dto.getWorkCode())) {
            HrmStaffRosterVo userInfo = userInfoMap.get(dto.getWorkCode());
            costPersonnelInformation.setUserId(userInfo.getUserId()).setName(userInfo.getAliasName()).setWorkCode(userInfo.getWorkCode())
                    .setDeptId(userInfo.getDeptId()).setDeptName(userInfo.getDepartment()).setJobId(userInfo.getJobTitleId()).setJobName(userInfo.getJobTitleMark())
                    .setPositionId(userInfo.getGradeId()).setPositionName(userInfo.getGradeName());
        } else if (PersonnelAttributeEnum.THIRD_PARTY.getValue().equals(personnelAttribute)) {
            String workCode = StrUtil.isNotBlank(dto.getWorkCode()) ? dto.getWorkCode() : StrUtil.EMPTY;
            CostPersonnelInformationVO costPersonnelInformationVO = dsfInfoMap.getOrDefault(workCode, null);
            costPersonnelInformation
                    .setName(ObjectUtil.isNotNull(costPersonnelInformationVO) ? costPersonnelInformationVO.getName() : dto.getName())
                    .setWorkCode(ObjectUtil.isNotNull(costPersonnelInformationVO) ? costPersonnelInformationVO.getWorkCode() :
                            StrUtil.isNotBlank(dto.getWorkCode()) ? dto.getWorkCode() : this.getWorkCode())
                    .setDeptId(ObjectUtil.isNotNull(costPersonnelInformationVO) && ObjectUtil.isNotNull(costPersonnelInformationVO.getDeptId()) ?
                            costPersonnelInformationVO.getDeptId() : null)
                    .setDeptName(ObjectUtil.isNotNull(costPersonnelInformationVO) && StrUtil.isNotBlank(costPersonnelInformationVO.getDeptName()) ?
                            costPersonnelInformationVO.getDeptName() : null)
                    .setJobName(StrUtil.isNotBlank(dto.getJobName()) ? dto.getJobName() :
                            ObjectUtil.isNotNull(costPersonnelInformationVO) && StrUtil.isNotBlank(costPersonnelInformationVO.getJobName()) ?
                                    costPersonnelInformationVO.getJobName() : null)
                    .setPositionName(StrUtil.isNotBlank(dto.getPositionName()) ? dto.getPositionName() :
                            ObjectUtil.isNotNull(costPersonnelInformationVO) && StrUtil.isNotBlank(costPersonnelInformationVO.getPositionName()) ?
                                    costPersonnelInformationVO.getPositionName() : null);
        }
        if ((PersonnelAttributeEnum.GOK_STAFF.getValue().equals(personnelAttribute) && userInfoMap.containsKey(dto.getWorkCode())) ||
                (PersonnelAttributeEnum.THIRD_PARTY.getValue().equals(personnelAttribute))) {
            costPersonnelInformation
                    .setProjectId(projectId)
                    .setPersonnelAttribute(personnelAttribute)
                    .setDurationDays(StrUtil.isNotEmpty(dto.getDurationDays()) ? new BigDecimal(dto.getDurationDays()) : null)
                    .setDomicile(StrUtil.isNotEmpty(dto.getDomicile()) ? dto.getDomicile() : StrUtil.EMPTY)
                    .setQuotationType(quotationType)
                    .setQuotationIncludeTax(QuotationTypeEnum.GDFL.getValue().equals(quotationType) ? null :
                            StrUtil.isNotEmpty(dto.getQuotationIncludeTax()) ? new BigDecimal(dto.getQuotationIncludeTax()) : null)
                    .setQuotedRateId(QuotationTypeEnum.GDFL.getValue().equals(quotationType) ? Integer.valueOf(dictMap.get(PERCENTAGE_SIX)) :
                            dictMap.containsKey(dto.getQuotedRate()) ? Integer.valueOf(dictMap.get(dto.getQuotedRate())) : null)
                    .setQuotationExcludeTax(QuotationTypeEnum.GDFL.getValue().equals(quotationType) ? null :
                            this.calculateExcludeTax(StrUtil.isNotEmpty(dto.getQuotationIncludeTax()) ?
                                    new BigDecimal(dto.getQuotationIncludeTax()) : null, StrUtil.isNotEmpty(dto.getQuotedRate()) ?
                                    dto.getQuotedRate() : null))
                    .setFlatRate(!QuotationTypeEnum.GDFL.getValue().equals(quotationType) ? null :
                            StrUtil.isNotEmpty(dto.getFlatRate()) ? this.calculateFlatRate(dto.getFlatRate()) : null)
                    .setBelongMonth(StrUtil.isNotBlank(dto.getBelongMonth()) ?
                            LocalDate.parse(dto.getBelongMonth().substring(0, 7) + "-01") : LocalDate.now().withDayOfMonth(NumberUtils.INTEGER_ONE))
                    .setEntryTime(Optional.ofNullable(dto.getEntryTime())
                            .map(entryTime -> LocalDate.parse(entryTime.length() == 7 ? entryTime + "-01" : entryTime))
                            .orElse(LocalDate.now().withDayOfMonth(NumberUtils.INTEGER_ONE)));
            BaseBuildEntityUtil.buildInsert(costPersonnelInformation);
            BaseBuildEntityUtil.buildUpdate(costPersonnelInformation);
        }
        return costPersonnelInformation;
    }

    /**
     * 代码复用
     *
     * @param projectIds                   项目ID集合
     * @param costPersonnelInformationList 数据列表
     * @return {@link List}<{@link CostPersonnelInformationVO}>
     */
    public List<CostPersonnelInformationVO> likeCode(List<Long> projectIds, List<CostPersonnelInformationVO> costPersonnelInformationList) {
        // 获取字典税率MAP
        Map<String, String> dictMap = dictService.getDictKvList(TAX_RATE).getData()
                .stream().collect(Collectors.toMap(DictKvVo::getValue, DictKvVo::getName));
        // 数据转换
        this.convert(costPersonnelInformationList, dictMap, getConfirmedPersonalList(projectIds));
        return costPersonnelInformationList;
    }

    /**
     * 计算固定费率
     *
     * @param flatRate 固定费率
     * @return {@link BigDecimal}
     */
    public BigDecimal calculateFlatRate(String flatRate) {
        return new BigDecimal(flatRate.replace("%", StrUtil.EMPTY))
                .divide(BigDecimal.valueOf(100), 4, RoundingMode.DOWN);
    }

    /**
     * 导入内容校验
     *
     * @param person             人员信息
     * @param presencePersonList 在场人员信息MAP
     * @param exitPersonList     已离场人员信息MAP
     * @param userInfoMap        人员信息MAP
     * @param checkMap           校验信息MAP
     * @return {@link String}
     */
    public String checkImportFormatAndData(PersonImportInfoDTO person, List<String> presencePersonList,
                                           Map<String, HrmStaffRosterVo> userInfoMap,
                                           List<String> exitPersonList, Map<String, String> checkMap) {
        StringBuilder errorMsg = new StringBuilder();
        if (StrUtil.isBlank(person.getBelongMonth())) {
            errorMsg.append("归属月份不能为空");
            return String.valueOf(errorMsg);
        } else if (!DateUtil.isValidDateAll(person.getBelongMonth())) {
            errorMsg.append("归属月份格式有误,正确格式为xxxx-xx或xxxx/xx");
            return String.valueOf(errorMsg);
        }

        if (StrUtil.isBlank(person.getEntryTime())) {
            errorMsg.append("入场时间不能为空");
            return String.valueOf(errorMsg);
        } else if (!DateUtil.isValidDateAll(person.getEntryTime())) {
            errorMsg.append("入场时间格式有误,正确格式为xxxx-xx-xx或xxxx/xx/xx");
            return String.valueOf(errorMsg);
        }

        // 标准化日期格式
        if (StrUtil.isNotBlank(person.getBelongMonth())) {
            person.setBelongMonth(DateUtil.standardizeDateFormat(person.getBelongMonth()));
        }
        if (StrUtil.isNotBlank(person.getEntryTime())) {
            person.setEntryTime(DateUtil.standardizeDateFormat(person.getEntryTime()));
        }
        String key = StrUtil.isNotEmpty(person.getWorkCode()) ? person.getWorkCode() : StrUtil.EMPTY;
        String presenceKey = key + StrPool.DASHED + (StrUtil.isNotBlank(person.getBelongMonth()) ?
                person.getBelongMonth().substring(0, 7) : String.valueOf(LocalDate.now()).substring(0, 7));
        if (StrUtil.isBlank(person.getPersonnelAttributeName())) {
            errorMsg.append("人员属性不能为空");
            return String.valueOf(errorMsg);
        }
        if (StrUtil.isBlank(person.getQuotationTypeName())) {
            errorMsg.append("报价方式不能为空");
            return String.valueOf(errorMsg);
        }
        if (!EnumUtils.existsEnumValue(EnumUtils.getValueByName(PersonnelAttributeEnum.class,
                person.getPersonnelAttributeName()), PersonnelAttributeEnum.class)) {
            errorMsg.append("人员属性选择有误");
            return String.valueOf(errorMsg);
        }
        if (person.getPersonnelAttributeName().equals(PersonnelAttributeEnum.GOK_STAFF.getName()) && StrUtil.isBlank(person.getWorkCode())) {
            errorMsg.append("国科人员工号不能为空");
            return String.valueOf(errorMsg);
        }
        if ((PersonnelAttributeEnum.GOK_STAFF.getName().equals(person.getPersonnelAttributeName()) && !person.getWorkCode().contains(GOK_PREFIX))
                || (PersonnelAttributeEnum.THIRD_PARTY.getName().equals(person.getPersonnelAttributeName()) && StrUtil.isNotBlank(person.getWorkCode())
                && !person.getWorkCode().contains(IDENTIFICATION_CODE))) {
            errorMsg.append(person.getPersonnelAttributeName()).append("与").append(person.getWorkCode()).append("不匹配");
            return String.valueOf(errorMsg);
        }
        if (person.getPersonnelAttributeName().equals(PersonnelAttributeEnum.THIRD_PARTY.getName())) {
            if (StrUtil.isNotBlank(person.getWorkCode()) && !this.checkWorkCode(person.getWorkCode())) {
                errorMsg.append(key).append("工号格式不合理,正确格式: Dsf00001");
                return String.valueOf(errorMsg);
            }
            if (StrUtil.isBlank(person.getWorkCode()) && StrUtil.isBlank(person.getName())) {
                errorMsg.append("第三方姓名不能为空");
                return String.valueOf(errorMsg);
            }
        }
        if (StrUtil.isNotBlank(person.getWorkCode()) && !checkMap.containsKey(person.getWorkCode())) {
            checkMap.put(person.getWorkCode(), String.valueOf(person.getBelongMonth()));
        } else if (StrUtil.isNotBlank(person.getBelongMonth()) && person.getBelongMonth().equals(checkMap.get(person.getWorkCode()))) {
            errorMsg.append("已存在相同工号、相同归属月份的人员信息");
            return String.valueOf(errorMsg);
        }
        if (StrUtil.isBlank(person.getDurationDays())) {
            errorMsg.append("驻场时长(天)不能为空");
            return String.valueOf(errorMsg);
        }
        if (StrUtil.isBlank(person.getQuotedRate())) {
            errorMsg.append("报价税率不能为空");
            return String.valueOf(errorMsg);
        }
        if (QuotationTypeEnum.GDFL.getName().equals(person.getQuotationTypeName())) {
            if (StrUtil.isBlank(person.getFlatRate())) {
                errorMsg.append("固定费率模式下,固定费率不能为空");
                return String.valueOf(errorMsg);
            }
        } else if (QuotationTypeEnum.RT.getName().equals(person.getQuotationTypeName())
                || QuotationTypeEnum.RY.getName().equals(person.getQuotationTypeName())) {
            if (StrUtil.isBlank(person.getQuotationIncludeTax())) {
                errorMsg.append(person.getQuotationTypeName()).append("模式下,含税报价不能为空");
                return String.valueOf(errorMsg);
            }
        } else {
            errorMsg.append("不支持的报价方式:").append(person.getQuotationTypeName());
            return String.valueOf(errorMsg);
        }
        if (StrUtil.isNotBlank(person.getDurationDays()) && !this.checkDataFormat(person.getDurationDays())) {
            errorMsg.append("驻场时长(天)数据格式有误");
        }
        if (StrUtil.isNotBlank(person.getQuotationIncludeTax()) && !this.checkDataFormat(person.getQuotationIncludeTax())) {
            errorMsg.append("含税报价数据格式有误");
        }
        if (PersonnelAttributeEnum.GOK_STAFF.getName().equals(person.getPersonnelAttributeName())
                && !userInfoMap.containsKey(person.getWorkCode())) {
            errorMsg.append("该国科人员信息不存在");
            return String.valueOf(errorMsg);
        }
        if (presencePersonList.contains(presenceKey)) {
            errorMsg.append(presenceKey).append("在场");
            return String.valueOf(errorMsg);
        }
        if (exitPersonList.contains(key)) {
            errorMsg.append(key).append("离场");
            return String.valueOf(errorMsg);
        }
        return String.valueOf(errorMsg);
    }

    /**
     * 数据格式校验
     *
     * @param data 数据
     * @return {@link Boolean}
     */
    public Boolean checkDataFormat(String data) {
        return data.matches("^(0|[1-9]\\d*)(\\.\\d{1,2})?$");
    }

}