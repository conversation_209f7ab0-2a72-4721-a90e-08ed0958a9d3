package com.gok.pboot.pms.cost.entity.vo;

import lombok.Data;

/**
 * 成本估算配置变更DTO
 *
 * <AUTHOR>
 * @date 2025/01/07
 */
@Data
public class CostEstimationCfgAlterVO {

    /**
     * 配置 ID
     */
    private String configId;
    /**
     * 配置名称
     */
    private String configName;

    /**
     * 配置类型
     */
    private String configType;

    /**
     * 当前使用版本 ID
     */
    private Long usedVersionId;

    /**
     * 当前使用版本名称
     */
    private String usedVersionName;

    /**
     * 最新版本 ID
     */
    private Long latestVersionId;

    /**
     * 最新版本名称
     */
    private String latestVersionName;
}
