package com.gok.pboot.pms.controller;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.common.user.PigxUser;
import com.gok.pboot.common.security.annotation.Inner;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.StatisticsPage;
import com.gok.pboot.pms.entity.dto.DdOrderQueryDTO;
import com.gok.pboot.pms.entity.vo.DdFlightTicketOrderFindPageVO;
import com.gok.pboot.pms.entity.vo.DdHotelOrderFindPageVO;
import com.gok.pboot.pms.entity.vo.DdOrderImportResultVO;
import com.gok.pboot.pms.entity.vo.DdVehicleOrderFindPageVO;
import com.gok.pboot.pms.service.IDdOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @description 滴滴订单管理 前端控制器
 * @menu 滴滴订单管理
 * @since 2025-07-30
 **/
@Slf4j
@RestController
@RequestMapping("/ddOrder")
@RequiredArgsConstructor
@Api(tags = "滴滴订单管理")
public class DdOrderController {

    private final IDdOrderService ddOrderService;

    /**
     * 机票订单分页查询
     *
     * @param queryDTO    查询条件
     * @return {@link ApiResult}<{@link Page}<{@link DdFlightTicketOrderFindPageVO}>>
     */
    @PostMapping("/flightTicket/findPage")
    @PreAuthorize("@pms.hasPermission('TMC_ORDER_MANAGE')")
    public ApiResult<StatisticsPage<DdFlightTicketOrderFindPageVO>> findFlightTicketPage(@RequestBody DdOrderQueryDTO queryDTO) {
        StatisticsPage<DdFlightTicketOrderFindPageVO> page = new StatisticsPage<>(queryDTO.getPageNumber(), queryDTO.getPageSize());
        return ApiResult.success(ddOrderService.findFlightTicketPageList(page, BeanUtil.beanToMap(queryDTO, false, false)));
    }

    /**
     * 酒店订单分页查询
     *
     * @param queryDTO    查询条件
     * @return {@link ApiResult}<{@link Page}<{@link DdHotelOrderFindPageVO}>>
     */
    @PostMapping("/hotel/findPage")
    @PreAuthorize("@pms.hasPermission('TMC_ORDER_MANAGE')")
    public ApiResult<Page<DdHotelOrderFindPageVO>> findHotelPage(@RequestBody DdOrderQueryDTO queryDTO) {
        StatisticsPage<DdHotelOrderFindPageVO> page = new StatisticsPage<>(queryDTO.getPageNumber(), queryDTO.getPageSize());
        return ApiResult.success(ddOrderService.findHotelPageList(page, BeanUtil.beanToMap(queryDTO, false, false)));
    }

    /**
     * 用车订单分页查询
     *
     * @param queryDTO    查询条件
     * @return {@link ApiResult}<{@link Page}<{@link DdVehicleOrderFindPageVO}>>
     */
    @PostMapping("/vehicle/findPage")
    @PreAuthorize("@pms.hasPermission('TMC_ORDER_MANAGE')")
    public ApiResult<Page<DdVehicleOrderFindPageVO>> findVehiclePage(@RequestBody DdOrderQueryDTO queryDTO) {
        StatisticsPage<DdVehicleOrderFindPageVO> page = new StatisticsPage<>(queryDTO.getPageNumber(), queryDTO.getPageSize());
        return ApiResult.success(ddOrderService.findVehiclePageList(page, BeanUtil.beanToMap(queryDTO, false, false)));
    }

    /**
     * 导入订单数据
     *
     * @param file Excel文件
     * @return {@link ApiResult}<{@link DdOrderImportResultVO}>
     */
    @PostMapping("/import")
    @Inner(false)
    @ApiOperation(value = "导入订单数据", notes = "导入订单数据，支持机票订单、酒店订单、用车订单三个sheet")
    public ApiResult<DdOrderImportResultVO> importOrders(@RequestParam("file") MultipartFile file) {
        return ApiResult.success(ddOrderService.importOrders(file));
    }

    /**
     * 生成报销
     *
     * @param queryDTO 查询条件
     * @return {@link ApiResult}<{@link String}>
     */
    @PostMapping("/generateReimbursement")
    @PreAuthorize("@pms.hasPermission('TMC_ORDER_MANAGE')")
    @ApiOperation(value = "生成报销", notes = "根据查询条件生成报销单")
    public ApiResult<String> generateReimbursement(@Valid @RequestBody DdOrderQueryDTO queryDTO) {
        ddOrderService.generateReimbursement(BeanUtil.beanToMap(queryDTO, false, false));
        return ApiResult.success("正在生成报销单，请稍等几分钟后刷新查看结果");
    }

}
