package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 任务类型枚举
 *
 * <AUTHOR>
 **/
@Getter
@AllArgsConstructor
public enum ProjectCollectTypeEnum implements ValueEnum<Integer> {
    /**
     * 任务管理收藏
     */
    TASK_MANAGEMENT(0, "任务管理收藏"),
    /**
     * 工时填报收藏
     */
    WORKING_HOURS(1, "工时填报收藏"),
    /**
     * 项目工时汇总
     */
    PROJECT_HOURS(2, "项目工时汇总"),

    /**
     * 工单工时填报
     */
    TICKETS_WORKING_HOURS(3, "工单工时填报收藏")
    ;

    private final Integer value;
    private final String name;


}
