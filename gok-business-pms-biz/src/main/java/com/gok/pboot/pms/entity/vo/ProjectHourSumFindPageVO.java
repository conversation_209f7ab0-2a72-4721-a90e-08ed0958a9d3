package com.gok.pboot.pms.entity.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * @Auther chenhc
 * @Date 2022-08-24 14:52
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ProjectHourSumFindPageVO {

    @ExcelIgnore
    private Long id;

    /**
     * 项目编号
     */
    @ExcelProperty("项目编号")
    private String code;

    /**
     * 项目名称
     */
    @ExcelProperty("项目名称")
    private String projectName;

    /**
     * 项目状态值
     */
    @ExcelIgnore
    private Integer projectStatus;

    /**
     * 项目状态值
     */
    @ExcelProperty("项目状态")
    private String projectStatusName;



    @ExcelProperty("是否内部项目")
    private Integer isNotInternalProject;

    /**
     * 项目类型
     */
    @ExcelProperty("项目类型")
    private String projectTypeName;

    /**
     * 收入归属部门编号
     */
    @ExcelIgnore
    private Long projectDeptId;
    /**
     * 收入归属部门
     */
    @ExcelProperty("收入归属部门")
    private String projectDeptName;

    /**
     * 项目正常工时
     */
    @ExcelProperty("项目正常工时")
    private BigDecimal normalHours;

    /**
     * 项目加班工时
     */
    @ExcelProperty("项目加班工时")
    private BigDecimal addedHours;

    @ExcelProperty("工作日加班工时")
    private BigDecimal workOvertimeHours;

    @ExcelProperty("休息日加班工时")
    private BigDecimal restOvertimeHours;

    @ExcelProperty("节假日加班工时")
    private BigDecimal holidayOvertimeHours;

    /**
     * 调休工时
     */
    @ExcelProperty("调休工时")
    private BigDecimal leaveHours;

    /**
     * OA加班工时
     */
    @ExcelProperty("OA加班工时")
    private BigDecimal hourData;

    /**
     * 项目分摊工时
     */
    @ExcelProperty("项目分摊工时")
    private BigDecimal projectShareHours;

    /**
     * 项目耗用工时
     */
    @ExcelProperty("项目耗用工时")
    private BigDecimal projectHours;

    /**
     * 售前工时
     */
    @ExcelProperty("售前工时")
    private BigDecimal preSaleHours;

    /**
     * 售后工时
     */
    @ExcelProperty("售后工时")
    private BigDecimal afterSaleHours;

    /**
     * 是否已审核完成
     */
    @ExcelIgnore
    private boolean approvalFinished;

    /**
     * 项目参与人数
     */
    @ExcelProperty("项目参与人数")
    private Long userNum;

}
