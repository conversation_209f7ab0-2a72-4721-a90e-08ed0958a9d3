package com.gok.pboot.pms.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.BaseController;
import com.gok.pboot.pms.entity.Confirm;
import com.gok.pboot.pms.entity.dto.ConfirmHourSumFindPageDTO;
import com.gok.pboot.pms.entity.vo.ConfirmHourSumFindPageVO;
import com.gok.pboot.pms.service.IConfirmService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * 工时确认前端控制器
 *
 * <AUTHOR>
 * @description 工时确认 前端控制器
 * @since 2022-08-30
 */
@Slf4j
@RestController
@RequestMapping("/confirm")
public class ConfirmController extends BaseController {

    private IConfirmService service;

    /**
     * 公式确认控制器有参构造器
     *
     * @param service 工时确认服务类
     */
    @Autowired
    public ConfirmController(IConfirmService service) {
        this.service = service;
    }

    /**
     * 工时确认接口
     *
     * @param confirmHourSumFindPageDTO 工时确认汇总分页dto
     * @return {@link ApiResult}
     * @customParam selectMonth 选择月份
     */
    @PreAuthorize("@pms.hasPermission('WORKING_HOURS_VERIFY_CONFIRM')")
    @GetMapping("/mhoursConfirm")
    public ApiResult<String> add(@Valid ConfirmHourSumFindPageDTO confirmHourSumFindPageDTO) {
        return service.save(confirmHourSumFindPageDTO);
    }


    /**
     * 分页查询工时确认
     *
     * @param confirmHourSumFindPageDTO 工时确认汇总dto
     * @return {@link ApiResult<Page<Confirm>>}
     * @customParam projectId 所属项目ID
     * @customParam selectMonth 选择月份
     * @customParam personnelStatus 人员状态
     */
    @PreAuthorize("@pms.hasPermission('WORKING_HOURS_VERIFY_CONFIRM')")
    @PostMapping("/findPage")
    public ApiResult<Page<ConfirmHourSumFindPageVO>> confirmHourSumFindPageVO(@RequestBody @Valid ConfirmHourSumFindPageDTO confirmHourSumFindPageDTO) {
        return success(service.confirmHourSumFindPageVO(confirmHourSumFindPageDTO));
    }


    /**
     * 分页查询该月份工时是否确认
     * true:已确认，false:未确认
     *
     * @param confirmHourSumFindPageDTO 工时确认汇总dto
     * @return {@link ApiResult<Boolean>}
     * @customParam selectMonth 选择月份
     */
    @PreAuthorize("@pms.hasPermission('WORKING_HOURS_VERIFY_CONFIRM')")
    @GetMapping("/findConfirm")
    public ApiResult<Boolean> findConfirm(@Valid ConfirmHourSumFindPageDTO confirmHourSumFindPageDTO) {
        return success(service.findConfirm(confirmHourSumFindPageDTO));
    }

    /**
     * 分页查询工时确认 导出
     *
     * @param response httpservlet响应体
     * @param confirmHourSumFindPageDTO 工时确认汇总dto
     * @customParam projectId 所属项目ID
     * @customParam selectMonth 选择月份
     * @customParam personnelStatus 人员状态
     */
    @PreAuthorize("@pms.hasPermission('WORKING_HOURS_VERIFY_CONFIRM')")
    @PostMapping("/export")
    public void confirmHourSumExport(HttpServletResponse response, @RequestBody @Valid ConfirmHourSumFindPageDTO confirmHourSumFindPageDTO) {
        service.confirmHourSumExport(response, confirmHourSumFindPageDTO);
    }

}
