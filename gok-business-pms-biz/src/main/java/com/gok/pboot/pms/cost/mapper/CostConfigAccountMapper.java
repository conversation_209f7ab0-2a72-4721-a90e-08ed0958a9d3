package com.gok.pboot.pms.cost.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.cost.entity.domain.CostConfigAccount;
import com.gok.pboot.pms.cost.entity.vo.CostConfigAccountVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 成本科目配置 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Mapper
public interface CostConfigAccountMapper extends BaseMapper<CostConfigAccount> {

    List<CostConfigAccount> getCurrentVersionCostSubjectConfigInfoList(@Param("versionId") Long versionId);

    int getVersionCount();

    /**
     * 按版本 ID 获取成本主体配置
     *
     * @param versionId 版本 ID
     * @return {@link List }<{@link CostConfigAccountVO }>
     */
    List<CostConfigAccountVO> getCostSubjectConfigByVersionId(Long versionId);

}
