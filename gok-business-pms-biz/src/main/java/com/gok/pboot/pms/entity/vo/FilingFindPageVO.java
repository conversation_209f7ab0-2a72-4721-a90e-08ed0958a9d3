package com.gok.pboot.pms.entity.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.sql.Timestamp;

/**
 * 归档
 *
 * <AUTHOR>
 * @since 2022-08-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class FilingFindPageVO  {

    /**
     * ID
     */
    private Long id;

    /**
    * 年
    */
    private String year;
    /**
    * 月
    */
    private String month;

    /**
     * 对应状态（0=未归档，1=已归档）
     */
    private Integer filed;
    /**
     * 对应状态（0=未归档，1=已归档）
     */
    private String filedName;
    /**
    * 操作人
    */
    private String operator;
    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp mtime;

}
