package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;

/**
 * 商机状态
 *
 * <AUTHOR>
 * @version 1.3.1
 */
@AllArgsConstructor
public enum BusinessStatusEnum implements ValueEnum<Integer> {

    BUSINESS(0, "商机"),
    FINISHED(50, "已成交"),
    ABORTED(1, "商机终止");

    private final Integer value;

    private final String name;

    @Override
    public Integer getValue() {
        return value;
    }

    @Override
    public String getName() {
        return name;
    }
}
