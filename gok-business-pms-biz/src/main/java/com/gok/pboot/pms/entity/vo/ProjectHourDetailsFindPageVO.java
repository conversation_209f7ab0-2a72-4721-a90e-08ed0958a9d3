package com.gok.pboot.pms.entity.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class ProjectHourDetailsFindPageVO {
    /**
     * 项目id
     */
    @ExcelIgnore
    private Long projectId;
    /**
     * 任务id
     */
    @ExcelIgnore
    private Long taskId;
    /**
     * 日报id
     */
    @ExcelIgnore
    private Long dailyPaperId;
    /**
     * 项目名称
     */
    @ExcelProperty("项目名称")
    private String projectName;
    /**
     * 任务名称
     */
    @ExcelProperty("任务名称")
    private String taskName;
    /**
     * 项目状态
     */
    @ExcelIgnore
    private Integer projectStatus;
    /**
     * 项目状态
     */
    @ExcelProperty("项目状态")
    private String projectStatusName;

    /**
     * 姓名
     */
    @ExcelProperty("姓名")
    private String username;

    /**
     * 填报日期
     */
    @ExcelProperty("填报日期")
    private LocalDate submissionDate;
    /**
     * 正常工时
     */
    @ExcelProperty("正常工时（人天）")
    private BigDecimal normalHours;
    /**
     * 加班工时
     */
    @ExcelProperty("加班工时（人天）")
    private BigDecimal addedHours;
    /**
     * 工作内容
     */
    @ExcelProperty("工作内容")
    private String workContent;
    /**
     * 昨日计划
     */
    @ExcelProperty("昨日计划")
    private String yesterdayPlan;
}
