package com.gok.pboot.pms.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.gok.pboot.pms.Util.DbApiUtil;
import com.gok.pboot.pms.entity.domain.ContractLedger;
import com.gok.pboot.pms.entity.domain.ContractLedgerDetail;
import com.gok.pboot.pms.entity.domain.ProjectContractInvoice;
import com.gok.pboot.pms.entity.domain.ProjectInfo;
import com.gok.pboot.pms.entity.vo.ContractAcceptanceVo;
import com.gok.pboot.pms.entity.vo.OaFileDownloadVo;
import com.gok.pboot.pms.entity.vo.OaFileInfoVo;
import com.gok.pboot.pms.entity.vo.OaFileVo;
import com.gok.pboot.pms.enumeration.InvoicingStatusEnum;
import com.gok.pboot.pms.enumeration.OaFileDownLoadTypeEnum;
import com.gok.pboot.pms.mapper.ContractLedgerMapper;
import com.gok.pboot.pms.mapper.ProjectInfoMapper;
import com.gok.pboot.pms.oa.OaUtil;
import com.gok.pboot.pms.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * Oa Service
 *
 * <AUTHOR>
 * @date 2023/11/21
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OaServiceImpl implements IOaService {

    private final IContractLedgerService contractLedgerService;

    private final DbApiUtil dbApiUtil;

    private final IPmsDocImageFileService pmsDocImageFileService;

    private final OaUtil oaUtil;

    private final IContractLedgerDetailService contractLedgerDetailService;

    private  final IProjectContractInvoiceService projectContractInvoiceService;

    private  final ProjectInfoMapper projectInfoMapper;

    private final ContractLedgerMapper contractLedgerMapper;
    /**
     * oa的url地址
     */
    @Value("${oa.url.httpUrl}")
    private String httpUrl;

    @Value("${pms.tempPath:/var/tmp/}")
    private String tempPath;

    @Override
    public OaFileDownloadVo oaFileDownload(Long id, OaFileDownLoadTypeEnum fileType, String fileId) {
        OaFileDownloadVo oaFileDownloadVo = new OaFileDownloadVo();
        oaFileDownloadVo.setId(id);
        String downloadUrl = StrUtil.EMPTY;
        String oaFileIdStr = StrUtil.EMPTY;
        Long requestId = null;
        Long nodeOperator = null;
        switch (fileType) {
            //验收附件
            case YSBGFJ:
                List<ContractAcceptanceVo> contractChangeInfoList = dbApiUtil.getAcceptanceRecordsUrl(id);
                if (CollUtil.isNotEmpty(contractChangeInfoList)) {
                    contractChangeInfoList = contractChangeInfoList.stream()
                            .filter(c -> StrUtil.isNotBlank(c.getYsbgfj()) && c.getYsbgfj().contains(fileId))
                            .collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(contractChangeInfoList)) {
                        ContractAcceptanceVo c = contractChangeInfoList.get(0);
                        oaFileIdStr = c.getYsbgfj();
                        requestId = c.getRequestId();
                        nodeOperator = StrUtil.isNotBlank(c.getApplicantid()) ? Long.parseLong(c.getApplicantid()) : null;
                    }
                }
                break;
            case FPFJ:
                ContractLedger contractLedger = contractLedgerService.getById(id);
                QueryWrapper<ContractLedgerDetail> contractLedgerDetailQueryWrapper = new QueryWrapper<>();
                contractLedgerDetailQueryWrapper.lambda().eq(ContractLedgerDetail::getMainid, id)
                        .eq(ContractLedgerDetail::getFpkjzt, InvoicingStatusEnum.YES.getValue());
                List<ContractLedgerDetail> contractLedgerDetailList
                        = contractLedgerDetailService.list(contractLedgerDetailQueryWrapper);
                if (CollectionUtil.isNotEmpty(contractLedgerDetailList)) {
                    List<Long> ids = contractLedgerDetailList
                            .stream().map(d -> d.getId()).collect(Collectors.toList());
                    QueryWrapper<ProjectContractInvoice> queryWrapper = new QueryWrapper<>();
                    queryWrapper.lambda().in(ProjectContractInvoice::getLlhtmxskbh, ids)
                            .or()
                            .eq(ProjectContractInvoice::getHtbh, contractLedger.getHtbh())
                            .orderByDesc(ProjectContractInvoice::getFprq);
                    List<ProjectContractInvoice> projectContractInvoiceList =
                            projectContractInvoiceService.list(queryWrapper);
                    projectContractInvoiceList =
                            projectContractInvoiceList.stream()
                                    .filter(c -> StrUtil.isNotBlank(c.getFpsmj()) && c.getFpsmj().contains(fileId))
                                    .collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(projectContractInvoiceList)) {
                        ProjectContractInvoice c = projectContractInvoiceList.get(0);
                        oaFileIdStr = c.getFpsmj();
                        requestId = c.getRequestid();
                        nodeOperator = c.getNodeoperator();
                    }
                }
                break;
            case XMYWXQWD:
                ProjectInfo projectInfo1 = projectInfoMapper.selectById(id);
                oaFileIdStr = projectInfo1.getProjectBusinessRequirementsOafileid();
                requestId = projectInfo1.getProjectBusinessRequirementsOafileidRequestid();
                nodeOperator = projectInfo1.getProjectBusinessRequirementsOafileidNodeoperator();
                break;
        }
        if (StrUtil.isNotBlank(oaFileIdStr)
                && Optional.ofNullable(requestId).isPresent()
                && Optional.ofNullable(nodeOperator).isPresent()) {
            OaFileInfoVo oaFileInfoVo = new OaFileInfoVo();
            oaFileInfoVo.setFileId(oaFileIdStr);
            oaFileInfoVo.setRequestId(String.valueOf(requestId));
            List<OaFileVo> resourcesData = oaUtil.getResourcesData(String.valueOf(requestId),
                    String.valueOf(nodeOperator));
            List<OaFileInfoVo> fileList = pmsDocImageFileService.getOaOaFileInfoList(oaFileInfoVo
                    , resourcesData);
            fileList = fileList.stream().filter(f -> fileId.contains(f.getFileId())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(fileList)) {
                downloadUrl = fileList.get(0).getDownloadUrl();
            }
        }
        oaFileDownloadVo.setDownloadUrl(downloadUrl);

        return oaFileDownloadVo;
    }
    @Override
    public void getFileByUrl(HttpServletResponse response, String fileUrl, String fileName,boolean ifPreview){

            // 下载链接
            fileUrl=httpUrl+fileUrl;
            // 保存路径
            Path targetPath = Paths.get(tempPath,fileName);

            try {
                // 创建URL对象
                URL url = new URL(fileUrl);
                // 打开连接并获取输入流
                try (InputStream in = url.openStream();
                     OutputStream out = new FileOutputStream(targetPath.toFile())) {
                    // 传输数据
                    byte[] buffer = new byte[1024];
                    int bytesRead;
                    while ((bytesRead = in.read(buffer)) != -1) {
                        out.write(buffer, 0, bytesRead);
                    }
                }
                log.info("OA文件:"+fileName+",下载到本地成功！");
            } catch (IOException e) {
                log.error(fileName+",OA文件下载失败：" + e);
            }

        try {
            // 获取文件
            File file = targetPath.toFile();
            if (!file.exists()) {
                response.sendError(HttpServletResponse.SC_NOT_FOUND, "文件未找到:"+fileName);
                return;
            }

            // 设置响应头
            response.setContentType("application/octet-stream");
            response.setContentLength((int) file.length());

            fileName = URLEncoder.encode(file.getName(), "UTF-8").replaceAll("\\+", "%20");
            // 设置响应头
            String contentDisposition="attachment";
            if(ifPreview){
                 contentDisposition="inline";
            }
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, contentDisposition+"; filename=\"" + fileName + "\"");
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, contentDisposition+"; filename*=UTF-8''" + fileName);

            // 读取文件并写入响应输出流
            try (FileInputStream fis = new FileInputStream(file);
                 OutputStream os = response.getOutputStream()) {
                byte[] buffer = new byte[4096];
                int length;
                while ((length = fis.read(buffer)) > 0) {
                    os.write(buffer, 0, length);
                }
            }
            FileUtil.del(file);
            log.info("下载OA文件:"+fileName+"成功！");
        }catch (Exception e){
            log.error("下载OA文件:{},失败原因:{}",fileName,e);
        }
    }

    @Override
    public Integer getOaWorkFlowStatus(Long requestId) {
        return dbApiUtil.getOaRequestStatus(requestId);
    }

    /**
     * 根据项目id查询 OA合同台账是否存在归档的合同
     *
     * @param projectId 项目 ID
     * @return {@link Boolean }
     */
    @Override
    public Boolean existContractByOaHttz(Long projectId) {
        return contractLedgerMapper.exists(Wrappers.lambdaQuery(ContractLedger.class).eq(ContractLedger::getXmmc, projectId));
    }

}
