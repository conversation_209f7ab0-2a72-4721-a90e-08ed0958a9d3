package com.gok.pboot.pms.entity.vo;

import com.gok.pboot.pms.common.base.BeanEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 交付人员工时分页实体
 *
 * <AUTHOR>
 * @create 2023/2/13
 */
@Data
public class PersonnelDeliveryHourPageVO extends BeanEntity<Long> {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 项目编号
     */
    private String projectCode;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目状态
     */
    private Integer projectStatus;

    /**
     * 收入归属（一级）部门ID
     */
    private Long revenueDeptId;

    /**
     * 收入归属（一级）部门名称
     */
    private String revenueDeptName;

    /**
     * 人员名称
     */
    private String userRealName;

    /**
     * 工号
     */
    private String workCode;

    /**
     * 应出勤天数
     */
    private BigDecimal cwDueAttendance;

    /**
     * 出勤天数
     */
    private BigDecimal attendanceDays;

    /**
     * 项目分摊工时（天）
     */
    private BigDecimal projectConsumed;

    /**
     * 工作日正常工时（天）
     */
    private BigDecimal normalWorkDays;

    /**
     * 休息日加班工时（天）
     */
    private BigDecimal restWorkDays;

    /**
     * 节假日加班工时（天）
     */
    private BigDecimal holidaysWorkDays;

    /**
     * 工作日调休工时（天）
     */
    private BigDecimal ompensatoryDays;

    /**
     * 人员归属部门Id
     */
    private Long deptId;

    /**
     * 人员归属部门名称
     */
    private String deptName;

    /**
     * 审核状态（1=不通过，2=待审核，3=已通过）
     */
    private Integer approvalStatus;

    /**
     * 审核不通过原因
     */
    private String approvalReason;


    /**
     * 备注
     */
    private String remark;

    /**
     * 导入人姓名
     */
    private String executorUserRealName;

    /**
     * 日期（存储为x年x月1日）
     */
    private LocalDate reuseDate;

    /**
     * 项目经理
     */
    private String managerUserName;

    /**
     * 客户经理
     */
    private String projectSalesperson;

    /**
     * 项目ID
     */
    private Long projectId;

}
