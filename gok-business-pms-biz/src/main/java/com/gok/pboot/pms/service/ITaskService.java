package com.gok.pboot.pms.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.entity.domain.Task;
import com.gok.pboot.pms.entity.dto.TaskAddDTO;
import com.gok.pboot.pms.entity.dto.TaskAddUserDTO;
import com.gok.pboot.pms.entity.dto.TaskUpdateDTO;
import com.gok.pboot.pms.entity.vo.OperatingRecordPageVO;
import com.gok.pboot.pms.entity.vo.TaskInDailyPaperEntryVO;
import com.gok.pboot.pms.entity.vo.TaskInfoVO;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 任务 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-19
 */
public interface ITaskService {

    /**
     * 新增
     *
     * @param entity TaskAddVo实体
     * @return ApiResult
     */
    ApiResult<String> newAdd(TaskAddDTO entity);

    /**
     * 逻辑删除
     *
     * @param id 唯一标识
     * @return ApiResult
     */
    ApiResult deleteByLogic(Long id);

    /**
     * 分页查询项目下的任务
     *
     */
    Page<TaskInfoVO> findProjectIdPage(Long id, PageRequest pageRequest, Map<String, Object> taskInfoVoPage);

    /**
     * 任务添加人员
     *
     */
    ApiResult<String> addTaskUser(TaskAddUserDTO entity);

    /**
     * 任务编辑
     *
     */
    ApiResult<String> update(TaskUpdateDTO entity);

    Page<OperatingRecordPageVO> findOperatingRecordPage(Long id, PageRequest pageRequest);

    /**
     * ~ 根据项目ID查询当前用户可用的任务（已废弃，不使用该方法） ~
     * @param projectId 项目ID
     * @return java.util.List<com.gok.pboot.pms.entity.vo.TaskInDailyPaperEntryVO>
     * <AUTHOR>
     * @date 2022/8/26 9:32
     */
    @Deprecated
    List<TaskInDailyPaperEntryVO> findAvailableTasksForCurrentUserByProjectId(Long projectId);
    /**
     * @create by yzs at 2023/4/19
     * @description:
     * @param: id
     * @return: java.util.List<com.gok.pboot.pms.entity.vo.TaskInfoVO>
     *//**
     * @create by yzs at 2023/4/19
     * @description:查询项目下的任务
     * @param: id 项目id
     * @return: java.util.List<com.gok.pboot.pms.entity.vo.TaskInfoVO>
     */
    List<Task> findByprojectId(Long id);
}
