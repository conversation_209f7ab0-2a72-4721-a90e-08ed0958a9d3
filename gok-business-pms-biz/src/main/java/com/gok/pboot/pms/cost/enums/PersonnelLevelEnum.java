//package com.gok.pboot.pms.cost.enums;
//
//import com.gok.pboot.pms.enumeration.ValueEnum;
//import lombok.AllArgsConstructor;
//import lombok.Getter;
//
///**
// * 人员级别枚举
// *
// * <AUTHOR>
// * @date 2025/01/08
// */
//@AllArgsConstructor
//@Getter
//public enum PersonnelLevelEnum implements ValueEnum<Integer> {
//
//    /**
//     * T系列
//     */
//    T1(5, "T1"),
//    T2(6, "T2"),
//    T3(7, "T3"),
//    T4(8, "T4"),
//    T5(9, "T5"),
//    T6(10, "T6"),
//    T7(11, "T7"),
//    T8(12, "T8"),
//    T9(13, "T9"),
//
//    /**
//     * P系列
//     */
//    P1(14, "P1"),
//    P2(15, "P2"),
//    P3(16, "P3"),
//    P4(17, "P4"),
//    P5(18, "P5"),
//    P6(19, "P6"),
//    P7(20, "P7"),
//    P8(21, "P8"),
//    P9(22, "P9"),
//
//    /**
//     * M系列
//     */
//    M1(50, "M1"),
//    M2(51, "M2"),
//    M3(52, "M3"),
//    M4(53, "M4"),
//    M5(54, "M5"),
//    M6(55, "M6"),
//    M7(56, "M7"),
//    M8(57, "M8"),
//    M9(58, "M9");
//
//    private final Integer value;
//
//    private final String name;
//}
