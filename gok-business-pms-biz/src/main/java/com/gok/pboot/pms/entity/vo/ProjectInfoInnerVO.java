package com.gok.pboot.pms.entity.vo;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.gok.pboot.pms.Util.SysDeptUtils;
import com.gok.pboot.pms.entity.SysDept;
import com.gok.pboot.pms.entity.domain.ProjectInfo;
import com.gok.pboot.pms.enumeration.InternalProjectTypeEnum;
import com.gok.pboot.pms.enumeration.ProjectStatusEnum;
import com.google.common.base.Strings;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 项目信息VO
 *
 * <AUTHOR>
 * @since 2023-07-14
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class ProjectInfoInnerVO extends ProjectInfoOverViewVO {

    /**
     * 项目类型
     */
    private Integer projectType;

    /**
     * 项目类型（公司信息化、通用课程开发、自研产品研发、标准化解决方案打造、专项人才供应链构建、部门工作）
     *
     * @see InternalProjectTypeEnum
     */
    private String projectTypeName;

    /**
     * 预估成本
     */
    private BigDecimal estimatedCost;

    /**
     * 成本进度（实际成本/预算总成本） 显示百分比，保留至个位
     */
    private String projectCostProgress;


    /**
     * 项目归属部门id
     */
    private Long projectDepartmentId;

    /**
     * 项目归属部门
     */
    private String projectDepartment;

    /**
     * 业务经理id
     */
    private Long businessManagerId;

    /**
     * 业务经理
     */
    private String businessManager;

    /**
     * 立项日期
     */
    private String projectDate;

    /**
     * 结项日期（项目归档时间（结束日期））
     */
    private String projectFilingTime;

    public static ProjectInfoInnerVO from(ProjectInfo po, Map<Long, SysDept> deptIdMap) {
        ProjectInfoInnerVO result = new ProjectInfoInnerVO();
        BeanUtil.copyProperties(po, result);
        result.setId(String.valueOf(po.getId()));
        result.setItemNo(Strings.nullToEmpty(po.getItemNo()));
        result.setItemName(Strings.nullToEmpty(po.getItemName()));

        String projectStatus = Strings.nullToEmpty(po.getProjectStatus());
        result.setProjectStatus(projectStatus);
        result.setProjectStatusName(StringUtils.EMPTY.equals(projectStatus) ? StringUtils.EMPTY :
                ProjectStatusEnum.getNameByStrVal(projectStatus));

        result.setProjectTypeName(result.getProjectType() == null ? StringUtils.EMPTY :
                InternalProjectTypeEnum.getNameByVal(result.getProjectType()));
        result.setProjectDate(Strings.nullToEmpty(po.getProjectDate()));
        result.setProjectFilingTime(Strings.nullToEmpty(LocalDateTimeUtil.formatNormal(po.getProjectFilingTime())));
        result.setEstimatedCost(po.getEstimatedCost());

        // 填充部门信息
        result.setBusinessDepartment(SysDeptUtils.collectFullName(deptIdMap, po.getBusinessDepartmentId()));
        result.setProjectDepartment(SysDeptUtils.collectFullName(deptIdMap, po.getProjectDepartmentId()));

        return result;
    }

}
