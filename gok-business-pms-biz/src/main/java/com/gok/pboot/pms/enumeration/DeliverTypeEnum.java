package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 交付类型枚举
 *
 * <AUTHOR>
 * @create 2024/07/09
 **/
@Getter
@AllArgsConstructor
public enum DeliverTypeEnum implements ValueEnum<Integer> {

    /**
     * 人力外包
     */
    HUMAN_OUTSOURCING(0, "人力外包"),
    /**
     * 项目外包
     */
    PROJECT_OUTSOURCING(1, "项目外包");

    private final Integer value;

    private final String name;

}
