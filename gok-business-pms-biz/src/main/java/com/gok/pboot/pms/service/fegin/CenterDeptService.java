package com.gok.pboot.pms.service.fegin;

import com.gok.pboot.pms.common.base.R;
import com.gok.pboot.pms.entity.vo.SysDeptOutVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * 中台部门服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "centerDeptService", value = "gok-bcp-upms-biz")
public interface CenterDeptService {

    /**
     * 获取部门树结构
     * @return deptTree
     */
    @GetMapping("/out/getOrgStructTree")
    R<List<SysDeptOutVO>> getDeptTree();

    /**
     * 获取传入部门ids子部门列表
     * @param ids 部门列表
     * @return 子部门列表
     */
    @GetMapping("/outPms/getChildDeptByIds")
    R<Map<Long,List<SysDeptOutVO>>> getChildDeptMapByIds(@RequestParam(name = "ids") List<Long> ids);

    /** 
     * 获取传入部门ids的顶级部门
     * @param ids 部门列表
     * @return 顶级部门
     */
    @GetMapping("/outPms/getTopDeptByIds")
    R<Map<Long,SysDeptOutVO>> getTopDeptByIds(@RequestParam(name = "ids") List<Long> ids);

    /**
     * 查询组织架构列表
     * @return {@link R<List<SysDeptOutVO>>} 组织树
     */
    @GetMapping("/out/getOrgStructList")
    R<List<SysDeptOutVO>> getOrgStructList();


}
