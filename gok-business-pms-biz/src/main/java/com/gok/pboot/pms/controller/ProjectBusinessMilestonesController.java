package com.gok.pboot.pms.controller;

import com.alibaba.fastjson.JSONObject;
import com.gok.pboot.common.security.annotation.Inner;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.R;
import com.gok.pboot.pms.entity.vo.OaFileInfoVo;
import com.gok.pboot.pms.entity.vo.ProjectBusinessMilestonesVo;
import com.gok.pboot.pms.service.IProjectBusinessMilestonesService;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 项目商务里程碑
 *
 * <AUTHOR>
 * @menu 项目商务里程碑
 * @since 2023-07-14
 */
@RestController
@RequestMapping("/projectInfo/business-milestones")
@AllArgsConstructor
@Slf4j
public class ProjectBusinessMilestonesController {

    IProjectBusinessMilestonesService projectBusinessMilestonesService;

    /**
     * 通过项目Id查询项目商务里程碑
     *
     * @param projectId 项目ID
     * @return {@link R}<{@link List}<{@link ProjectBusinessMilestonesVo}>>
     */
    @ApiOperation(value = "通过项目Id查询项目商务里程碑", notes = "通过项目Id查询项目商务里程碑")
    @GetMapping("/getListByProjectId/{projectId}")
    public R<List<ProjectBusinessMilestonesVo>> getListByProjectId(@PathVariable Long projectId) {
        return projectBusinessMilestonesService.getListByProjectId(projectId);
    }

    /**
     * 通过里程碑Id获取佐证材料
     *
     * @param id 商务里程碑ID
     * @return {@link R}<{@link List}<{@link OaFileInfoVo}>>
     */
    @Inner(false)
    @GetMapping("/getSupportMaterialsListById/{id}")
    public R<List<OaFileInfoVo>> getSupportMaterialsListById(@PathVariable Long id) {
        return projectBusinessMilestonesService.getSupportMaterialsListById(id);
    }

    /**
     * 根据类型发起流程
     *
     * @param requestMap 项目里程碑发起流程参数
     * @return String
     */
    @PostMapping("/doCreateRequest")
    public R<String> doCreateRequest(@RequestBody JSONObject requestMap) {
        return projectBusinessMilestonesService.doCreateRequest(requestMap);
    }


    /**
     * 定时器
     * 在里程碑预计完成日期前 7 天和当天均推送消息提醒
     *
     * @return {@link ApiResult}
     */
    @Inner(value = false)
    @GetMapping("/remind")
    public ApiResult<String> remind() {
        log.info("定时任务触发,里程碑提醒:{},执行开始", LocalDateTime.now());
        ApiResult<String> result = projectBusinessMilestonesService.remind();
        log.info("定时任务触发,里程碑提醒:{}，执行结束", LocalDateTime.now());
        return result;
    }
}
