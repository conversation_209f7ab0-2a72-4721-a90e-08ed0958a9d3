package com.gok.pboot.pms.service.impl;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.gok.bcp.message.entity.enums.ChannelEnum;
import com.gok.bcp.message.entity.enums.MsgTypeEnum;
import com.gok.bcp.message.entity.enums.TargetTypeEnum;
import com.gok.components.common.util.SpringContextHolder;
import com.gok.pboot.common.core.exception.BusinessException;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.entity.domain.ProjectBusinessMilestones;
import com.gok.pboot.pms.entity.domain.ProjectInfo;
import com.gok.pboot.pms.entity.domain.ProjectOperationConfirmation;
import com.gok.pboot.pms.entity.domain.Roster;
import com.gok.pboot.pms.entity.dto.BaseSendMsgDTO;
import com.gok.pboot.pms.enumeration.*;
import com.gok.pboot.pms.eval.entity.domain.EvalUserRole;
import com.gok.pboot.pms.eval.enums.EvalUserRoleEnum;
import com.gok.pboot.pms.eval.mapper.EvalUserRoleMapper;
import com.gok.pboot.pms.mapper.ProjectBusinessMilestonesMapper;
import com.gok.pboot.pms.mapper.ProjectInfoMapper;
import com.gok.pboot.pms.mapper.ProjectOperationConfirmationMapper;
import com.gok.pboot.pms.service.BcpMessageService;
import com.gok.pboot.pms.service.IProjectOperationConfirmationService;
import com.gok.pboot.pms.service.IProjectStatusService;
import com.gok.pboot.pms.service.RosterService;
import lombok.AllArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 项目状态服务
 *
 * <AUTHOR>
 * @date 2025/07/02
 */
@Log4j2
@Service
@AllArgsConstructor
public class ProjectStatusServiceImpl implements IProjectStatusService {

    private final ProjectOperationConfirmationMapper projectOperationConfirmationMapper;

    private final ProjectBusinessMilestonesMapper projectBusinessMilestonesMapper;

    private final EvalUserRoleMapper evalUserRoleMapper;

    private final BcpMessageService bcpMessageService;

    private final ProjectInfoMapper projectInfoMapper;

    private final RosterService rosterService;


    private final static int REMINDER_CYCLES_DAY = 7;

    private final static String WARRANTY_FUND = "质保款";

    /**
     * 是否满足进入项目质保期的条件
     *
     * @return boolean
     */
    @Override
    public boolean isEnterProjectWarrantyPeriod(Long projectId) {
        if (projectId == null) {
            return false;
        }

        ProjectInfo projectInfo = projectInfoMapper.selectById(projectId);
        if (projectInfo == null) {
            return false;
        }

        return projectWarrantyPeriodCheck(projectInfo);
    }

    private boolean projectWarrantyPeriodCheck(ProjectInfo projectInfo) {
        if (!ProjectStatusEnum.valueEquals(projectInfo.getProjectStatus(), ProjectStatusEnum.ZJ)
                || !EnumUtils.valueEquals(projectInfo.getDeliverType(), DeliverTypeEnum.PROJECT_OUTSOURCING)) {
            return false;
        }
        // 当项目所有关联合同或订单 未达成的商务里程碑
        List<ProjectBusinessMilestones> businessMilestones = projectBusinessMilestonesMapper
                .selectList(Wrappers.lambdaQuery(ProjectBusinessMilestones.class)
                        .eq(ProjectBusinessMilestones::getProjectId, projectInfo.getId())
                        .eq(ProjectBusinessMilestones::getIfFinish, BusinessMilestonesFinishEnum.UNFINISHED.getValue())
                );
        // 只剩下【质保款】未达成时 则 进入质保期
        return CollUtil.isNotEmpty(businessMilestones) && businessMilestones.stream()
                .allMatch(item -> Objects.equals(item.getAccountType(), WARRANTY_FUND));
    }

    /**
     * 是否满足项目关闭的条件
     *
     * @param projectId 项目id
     * @return boolean
     */
    @Override
    public boolean isCloseProject(Long projectId) {
        if (projectId == null) {
            return false;
        }

        ProjectInfo projectInfo = projectInfoMapper.selectById(projectId);
        if (projectInfo == null) {
            return false;
        }

        return closeProjectCheck(projectInfo);
    }

    private boolean closeProjectCheck(ProjectInfo projectInfo) {
        DeliverTypeEnum deliverTypeEnum = EnumUtils.getEnumByValue(DeliverTypeEnum.class, projectInfo.getDeliverType());
        if (deliverTypeEnum == null) {
            throw new BusinessException("项目交付方式错误~");
        }
        if (DeliverTypeEnum.HUMAN_OUTSOURCING.equals(deliverTypeEnum)) {
            // 人力外包
            // 人力外包没有质保概念,为在建才能关闭
            if (!ProjectStatusEnum.valueEquals(projectInfo.getProjectStatus(), ProjectStatusEnum.ZJ)) {
                return false;
            }
            LocalDate serviceEndDate = projectInfo.getYdfwjsrq();
            // 当到了项目约定服务结束日期
            return serviceEndDate != null && serviceEndDate.isBefore(LocalDate.now());
        } else {
            // 项目外包
            // 查询商务里程碑
            List<ProjectBusinessMilestones> projectBusinessMilestones = projectBusinessMilestonesMapper
                    .selectList(Wrappers.lambdaQuery(ProjectBusinessMilestones.class)
                            .eq(ProjectBusinessMilestones::getProjectId, projectInfo.getId()));
            // 当项目有且所有关联合同或订单的商务里程碑(验收回款)均达成时
            return CollUtil.isNotEmpty(projectBusinessMilestones)
                    && projectBusinessMilestones.stream()
                    .allMatch(item -> Objects.equals(item.getIfFinish(), BusinessMilestonesFinishEnum.FINISHED.getValue()));
        }
    }

    /**
     * 项目操作提醒
     *
     * @param projectId 项目id
     * @return {@link Map }<{@link String }, {@link Object }>
     */
    @Override
    public Map<String, Object> projectOperateRemind(Long projectId) {
        ProjectInfo projectInfo = projectInfoMapper.selectById(projectId);
        if (projectInfo == null) {
            return Collections.emptyMap();
        }
        final String projectStatusStr = "projectStatus";
        Long userId = SecurityUtils.getUser().getId();
        // 获取当前用户 在当前项目中 需要操作确认的记录中 是否存在未确的数据
        List<ProjectOperationConfirmation> operationConfirmationList = projectOperationConfirmationMapper
                .selectList(Wrappers.lambdaQuery(ProjectOperationConfirmation.class)
                        .eq(ProjectOperationConfirmation::getProjectId, projectId)
                        .eq(ProjectOperationConfirmation::getUserId, userId)
                        .in(ProjectOperationConfirmation::getOperationType, ProjectOperationTypeEnum.NO.getValue(), ProjectOperationTypeEnum.DELAY.getValue()));
        if (CollUtil.isEmpty(operationConfirmationList)) {
            return Collections.emptyMap();
        }
        // 是否满足进入项目质保期的条件
        boolean isWarrantyPeriod = projectWarrantyPeriodCheck(projectInfo);
        if (isWarrantyPeriod) {
            //  项目质保过滤
            return operationConfirmationList.stream().anyMatch(i ->
                    Objects.equals(i.getModule(), ProjectOperationModuleEnum.WARRANTY.getValue())) ?
                    Collections.singletonMap(projectStatusStr, ProjectStatusEnum.ZB.getValue()) :
                    Collections.emptyMap();
        }

        // 是否满足项目关闭的条件
        boolean isClose = closeProjectCheck(projectInfo);
        if (isClose) {
            return operationConfirmationList.stream().anyMatch(i ->
                    Objects.equals(i.getModule(), ProjectOperationModuleEnum.CLOSE.getValue())) ?
                    Collections.singletonMap(projectStatusStr, ProjectStatusEnum.JX.getValue()) :
                    Collections.emptyMap();
        }
        return Collections.emptyMap();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized void statusTransferRemind() {
        log.info("定时发送项目状态流转提醒---");
        // 查询要处理的项目
        List<ProjectInfo> projectInfoList = projectInfoMapper.selectList(Wrappers.lambdaQuery(ProjectInfo.class)
                .in(ProjectInfo::getProjectStatus, ProjectStatusEnum.ZJ.getStrValue(),
                        ProjectStatusEnum.ZB.getValue() ,ProjectStatusEnum.GQ.getStrValue()));

        // 根据项目状态分组
        Map<String, List<ProjectInfo>> projectMapByStatus = CollStreamUtil.groupByKey(projectInfoList, ProjectInfo::getProjectStatus);

        // 构建消息推送列表
        List<BaseSendMsgDTO> sendMsgList = new ArrayList<>();

        // 项目重启判断
        // 到了预估重启日期或过了预估重启日期每7个自然日，给项目经理发送重启消息提醒
        projectMapByStatus.getOrDefault(ProjectStatusEnum.GQ.getStrValue(), Collections.emptyList())
                .stream().filter(p -> p.getXmcqrq() != null)
                .forEach(projectInfo -> processingRestartProject(projectInfo, sendMsgList));

        // 在建项目
        List<ProjectInfo> buildingProjectInfos = projectMapByStatus.getOrDefault(ProjectStatusEnum.ZJ.getStrValue(),new ArrayList<>());
        buildingProjectInfos.addAll(projectMapByStatus.getOrDefault(ProjectStatusEnum.ZB.getStrValue(), new ArrayList<>()));
        if (CollUtil.isEmpty(buildingProjectInfos)) {
            // 发送项目重启提醒消息
            bcpMessageService.batchSendMsg(sendMsgList);
            log.info("发送项目状态流转提醒消息完成,共发送${}条", sendMsgList.size());
            return;
        }

        // 新增的项目操作确认记录
        List<ProjectOperationConfirmation> saveList = new ArrayList<>();

        // 在建/质保项目的id
        List<Long> buildingProjectIds = CollStreamUtil.toList(buildingProjectInfos, ProjectInfo::getId);

        // 项目操作确认记录
        final Map<Long, Map<Integer, List<ProjectOperationConfirmation>>> projectOperateConfirmMap = projectOperationConfirmationMapper
                .selectList(Wrappers.lambdaQuery(ProjectOperationConfirmation.class)
                        .in(ProjectOperationConfirmation::getProjectId, buildingProjectIds))
                .stream().collect(Collectors.groupingBy(ProjectOperationConfirmation::getProjectId,
                        Collectors.groupingBy(ProjectOperationConfirmation::getModule)));

        // 商务里程碑
        final Map<Long, List<ProjectBusinessMilestones>> businessMilestonesMap = projectBusinessMilestonesMapper
                .selectList(Wrappers.lambdaQuery(ProjectBusinessMilestones.class)
                        .in(ProjectBusinessMilestones::getProjectId, buildingProjectIds))
                .stream().collect(Collectors.groupingBy(ProjectBusinessMilestones::getProjectId));

        // 获取pmo角色数据
        List<EvalUserRole> pmoUserList = evalUserRoleMapper.findAll().stream()
                .filter(i -> EnumUtils.valueEquals(i.getRole(), EvalUserRoleEnum.PMO))
                .collect(Collectors.toList());

        // 项目质保判断
        // 人力外包项目没有质保概念
        // 项目外包：当项目所有关联合同订单的商务里程碑(验收回款)均只剩下【质保款】未达成时
        // 对项目经理/客户经理没有确认操作的发送消息
        Set<Long> warrantyProjectIds = new HashSet<>();
        buildingProjectInfos.stream()
                .filter(p -> ProjectStatusEnum.valueEquals(p.getProjectStatus(), ProjectStatusEnum.ZJ))
                .filter(p -> EnumUtils.valueEquals(p.getDeliverType(), DeliverTypeEnum.PROJECT_OUTSOURCING))
                .forEach(projectInfo -> processingWarrantyProject(projectInfo, businessMilestonesMap,
                        projectOperateConfirmMap, warrantyProjectIds, sendMsgList, saveList, pmoUserList));

        // 获取上级信息
        Set<Long> userIds = new HashSet<>();
        buildingProjectInfos.stream()
                .filter(p -> !warrantyProjectIds.contains(p.getId()))
                .forEach(projectInfo -> {
                    userIds.add(projectInfo.getManagerUserId());
                    userIds.add(projectInfo.getSalesmanUserId());
                });
        Map<Long, Roster> userLeaderMap = rosterService.findUserLeaderMap(userIds);

        // 项目关闭判断
        // 项目外包：当项目有且所有关联合同订单的商务里程碑(验收回款)均达成时
        // 人力外包：当到了项目约定服务结束日期；
        // 项目经理/客户经理/项目经理直接上级/客户经理直接上级 没有确认操作的发送消息
        buildingProjectInfos.stream()
                .filter(p -> !warrantyProjectIds.contains(p.getId()))
                .forEach(projectInfo -> processingCloseProject(projectInfo, businessMilestonesMap,
                        projectOperateConfirmMap, pmoUserList, userLeaderMap, sendMsgList, saveList));

        // 保存操作记录
        saveList.forEach(BaseBuildEntityUtil::buildInsert);
        IProjectOperationConfirmationService projectOperationConfirmationService = SpringContextHolder.getBean(IProjectOperationConfirmationService.class);
        projectOperationConfirmationService.saveBatch(saveList, 300);

        // 发送项目提醒消息
        bcpMessageService.batchSendMsg(sendMsgList);
        log.info("发送项目状态流转提醒消息完成,共发送${}条", sendMsgList.size());
    }

    /**
     * 处理 重启项目
     *
     * @param projectInfo 项目信息
     * @param sendMsgList 发送 MSG 列表
     */
    private static void processingRestartProject(ProjectInfo projectInfo, List<BaseSendMsgDTO> sendMsgList) {
        // 获取重启时间
        LocalDate restartDate = projectInfo.getXmcqrq();
        long daysBetween = ChronoUnit.DAYS.between(restartDate, LocalDate.now());
        // 提醒周期为7天且项目经理
        if (daysBetween % REMINDER_CYCLES_DAY == 0 && projectInfo.getManagerUserId() != null) {
            sendMsgList.add(buildRestartReminderMsg(projectInfo, daysBetween > 0));
        }
    }

    /**
     * 处理 关闭 项目
     *
     * @param projectInfo              项目信息
     * @param businessMilestonesMap    业务里程碑Map
     * @param projectOperateConfirmMap 项目操作确认记录Map
     * @param pmoUserList              PMO 用户列表
     * @param userLeaderMap            用户上级Map
     * @param sendMsgList              发送消息列表
     * @param saveList                 保存列表
     */
    private static void processingCloseProject(ProjectInfo projectInfo,
                                               Map<Long, List<ProjectBusinessMilestones>> businessMilestonesMap,
                                               Map<Long, Map<Integer, List<ProjectOperationConfirmation>>> projectOperateConfirmMap,
                                               List<EvalUserRole> pmoUserList,
                                               Map<Long, Roster> userLeaderMap,
                                               List<BaseSendMsgDTO> sendMsgList,
                                               List<ProjectOperationConfirmation> saveList) {
        DeliverTypeEnum deliverTypeEnum = EnumUtils.getEnumByValue(DeliverTypeEnum.class, projectInfo.getDeliverType());
        if (deliverTypeEnum == null) {
            return;
        }
        // 判断项目关闭条件
        boolean isCloseConditionMet;
        if (DeliverTypeEnum.HUMAN_OUTSOURCING.equals(deliverTypeEnum)) {
            // 人力外包：项目状态为在建且已过服务结束日期
            isCloseConditionMet = ProjectStatusEnum.valueEquals(projectInfo.getProjectStatus(), ProjectStatusEnum.ZJ)
                    && projectInfo.getYdfwjsrq() != null
                    && LocalDate.now().isAfter(projectInfo.getYdfwjsrq());
        } else {
            // 项目外包：所有商务里程碑已完成
            List<ProjectBusinessMilestones> milestones = businessMilestonesMap.get(projectInfo.getId());
            isCloseConditionMet = CollUtil.isNotEmpty(milestones) && milestones.stream()
                    .allMatch(m -> Objects.equals(m.getIfFinish(), BusinessMilestonesFinishEnum.FINISHED.getValue()));
        }

        if (!isCloseConditionMet) {
            return;
        }

        // 获取该项目的所有关闭确认操作记录
        List<ProjectOperationConfirmation> closeConfirmations = projectOperateConfirmMap
                .getOrDefault(projectInfo.getId(), Collections.emptyMap())
                .getOrDefault(ProjectOperationModuleEnum.CLOSE.getValue(), Collections.emptyList());

        if (CollUtil.isEmpty(closeConfirmations)) {
            // 无操作记录则创建操作记录 并提醒 项目经理/客户经理/项目经理直接上级/客户经理直接上级/pmo
            List<ProjectOperationConfirmation> list = buildNewCloseOperateEntity(projectInfo, pmoUserList, userLeaderMap,sendMsgList);

            saveList.addAll(list);
        } else {
            // 存在操作记录 对未确认且（等于提醒日期或过了提醒日期的每7个自然日）进行提醒
            // 仅对项目经理/客户经理 发送
            closeConfirmations.stream()
                    .filter(item -> item.getConfirmTime() == null && item.getReminderDate() != null)
                    .filter(item -> ChronoUnit.DAYS.between(item.getReminderDate(), LocalDate.now()) % REMINDER_CYCLES_DAY == 0)
                    .filter(item -> EnumUtils.valueOrEquals(item.getRole(), ProjectOperationRoleEnum.MANAGER, ProjectOperationRoleEnum.SALESMAN))
                    .forEach(item -> sendMsgList.add(buildReminderMsg(item, projectInfo)));
        }
    }


    /**
     * 处理质保项目
     *
     * @param projectInfo              项目信息
     * @param businessMilestonesMap    业务里程碑Map
     * @param projectOperateConfirmMap 项目操作确认记录Map
     * @param pmoUserList              PMO 用户列表
     * @param sendMsgList              发送消息列表
     * @param saveList                 保存列表
     */
    private static void processingWarrantyProject(ProjectInfo projectInfo,
                                                  Map<Long, List<ProjectBusinessMilestones>> businessMilestonesMap,
                                                  Map<Long, Map<Integer, List<ProjectOperationConfirmation>>> projectOperateConfirmMap,
                                                  Set<Long> warrantyIds,
                                                  List<BaseSendMsgDTO> sendMsgList,
                                                  List<ProjectOperationConfirmation> saveList,
                                                  List<EvalUserRole> pmoUserList) {
        Long projectId = projectInfo.getId();
        // 获取项目里程碑
        List<ProjectBusinessMilestones> milestones = businessMilestonesMap.getOrDefault(projectId, Collections.emptyList());
        List<ProjectBusinessMilestones> toBeContinued = milestones.stream()
                .filter(item -> EnumUtils.valueEquals(item.getIfFinish(), BusinessMilestonesFinishEnum.UNFINISHED))
                .collect(Collectors.toList());

        boolean isWarrantyPeriod = CollUtil.isNotEmpty(toBeContinued) && toBeContinued.stream()
                .allMatch(item -> Objects.equals(item.getAccountType(), WARRANTY_FUND));

        if (!isWarrantyPeriod) {
            return;
        }

        // 获取项目确认操作记录
        List<ProjectOperationConfirmation> confirmationList = projectOperateConfirmMap
                .getOrDefault(projectId, Collections.emptyMap())
                .getOrDefault(ProjectOperationModuleEnum.WARRANTY.getValue(), Collections.emptyList());

        warrantyIds.add(projectId);
        if (CollUtil.isEmpty(confirmationList)) {
            // 无操作记录则创建操作记录 并提醒 项目经理/客户经理/pmo
            List<ProjectOperationConfirmation> list = buildNewWarrantyOperateEntity(projectInfo, pmoUserList,sendMsgList);

            saveList.addAll(list);
        } else {
            // 存在操作记录 对未确认且（等于提醒日期或过了提醒日期的每7个自然日）进行提醒
            // 仅对项目经理/客户经理 发送
            confirmationList.stream()
                    .filter(item -> item.getConfirmTime() == null && item.getReminderDate() != null)
                    .filter(item -> ChronoUnit.DAYS.between(item.getReminderDate(), LocalDate.now()) % REMINDER_CYCLES_DAY == 0)
                    .filter(item -> EnumUtils.valueOrEquals(item.getRole(), ProjectOperationRoleEnum.MANAGER, ProjectOperationRoleEnum.SALESMAN))
                    .forEach(item -> sendMsgList.add(buildReminderMsg(item, projectInfo)));
        }
    }

    private static List<ProjectOperationConfirmation> buildNewCloseOperateEntity(ProjectInfo projectInfo,
                                                                                 List<EvalUserRole> pmoUserList,
                                                                                 Map<Long, Roster> userLeaderMap,
                                                                                 List<BaseSendMsgDTO> sendMsgList) {
        Long projectId = projectInfo.getId();
        List<ProjectOperationConfirmation> saveList = new ArrayList<>();
        Set<Long> userIds = new HashSet<>();
        // 项目经理
        Long managerUserId = projectInfo.getManagerUserId();
        if (managerUserId != null) {
            ProjectOperationConfirmation managerItem = new ProjectOperationConfirmation()
                    .setProjectId(projectId)
                    .setUserId(managerUserId)
                    .setUserName(projectInfo.getManagerUserName())
                    .setRole(ProjectOperationRoleEnum.MANAGER.getValue())
                    .init(ProjectOperationModuleEnum.CLOSE);
            saveList.add(managerItem);
            userIds.add(managerUserId);
            sendMsgList.add(buildReminderMsg(managerItem, projectInfo));
        }

        // 客户经理
        Long salesmanUserId = projectInfo.getSalesmanUserId();
        if (salesmanUserId != null) {
            ProjectOperationConfirmation salesmanItem = new ProjectOperationConfirmation()
                    .setProjectId(projectId)
                    .setUserId(salesmanUserId)
                    .setUserName(projectInfo.getProjectSalesperson())
                    .setRole(ProjectOperationRoleEnum.SALESMAN.getValue())
                    .init(ProjectOperationModuleEnum.CLOSE);
            saveList.add(salesmanItem);
            if (userIds.add(salesmanUserId)) {
                sendMsgList.add(buildReminderMsg(salesmanItem, projectInfo));
            }
        }

        // 项目经理直接上级
        Roster managerLeader = userLeaderMap.get(managerUserId);
        if (managerLeader != null ) {
            ProjectOperationConfirmation managerLeaderItem = new ProjectOperationConfirmation()
                    .setProjectId(projectId)
                    .setUserId(managerLeader.getId())
                    .setUserName(managerLeader.getAliasName())
                    .setRole(ProjectOperationRoleEnum.MANAGER_LEADER.getValue())
                    .init(ProjectOperationModuleEnum.CLOSE);
            saveList.add(managerLeaderItem);
        }

        // 客户经理直接上级
        Roster salesmanLeader = userLeaderMap.get(salesmanUserId);
        if (salesmanLeader != null ) {
            ProjectOperationConfirmation salesmanLeaderItem = new ProjectOperationConfirmation()
                    .setProjectId(projectId)
                    .setUserId(salesmanLeader.getId())
                    .setUserName(salesmanLeader.getAliasName())
                    .setRole(ProjectOperationRoleEnum.SALES_LEADER.getValue())
                    .init(ProjectOperationModuleEnum.CLOSE);
            saveList.add(salesmanLeaderItem);
        }

        // PMO
        pmoUserList.forEach(pmo -> {
            ProjectOperationConfirmation pmoItem = new ProjectOperationConfirmation()
                    .setProjectId(projectId)
                    .setUserId(pmo.getUserId())
                    .setUserName(pmo.getUserName())
                    .setRole(ProjectOperationRoleEnum.PMO.getValue())
                    .init(ProjectOperationModuleEnum.CLOSE);
            saveList.add(pmoItem);
        });

        return saveList;
    }


    private static List<ProjectOperationConfirmation> buildNewWarrantyOperateEntity(ProjectInfo projectInfo,
                                                                                    List<EvalUserRole> pmoUserList,
                                                                                    List<BaseSendMsgDTO> sendMsgList) {
        Long projectId = projectInfo.getId();
        List<ProjectOperationConfirmation> saveList = new ArrayList<>();
        Set<Long> userIds = new HashSet<>();
        // 项目经理
        Long managerUserId = projectInfo.getManagerUserId();
        if (managerUserId != null) {
            ProjectOperationConfirmation managerItem = new ProjectOperationConfirmation()
                    .setProjectId(projectId)
                    .setUserId(managerUserId)
                    .setUserName(projectInfo.getManagerUserName())
                    .setRole(ProjectOperationRoleEnum.MANAGER.getValue())
                    .init(ProjectOperationModuleEnum.WARRANTY);
            saveList.add(managerItem);
            userIds.add(managerUserId);
            sendMsgList.add(buildReminderMsg(managerItem, projectInfo));
        }
        // 客户经理
        Long salesmanUserId = projectInfo.getSalesmanUserId();
        if (salesmanUserId != null) {
            ProjectOperationConfirmation salesmanItem = new ProjectOperationConfirmation()
                    .setProjectId(projectId)
                    .setUserId(salesmanUserId)
                    .setUserName(projectInfo.getProjectSalesperson())
                    .setRole(ProjectOperationRoleEnum.SALESMAN.getValue())
                    .init(ProjectOperationModuleEnum.WARRANTY);
            saveList.add(salesmanItem);
            if (userIds.add(salesmanUserId)) {
                sendMsgList.add(buildReminderMsg(salesmanItem, projectInfo));
            }
        }
        // pmo
        pmoUserList.forEach(pmo -> {
            ProjectOperationConfirmation pmoItem = new ProjectOperationConfirmation()
                    .setProjectId(projectId)
                    .setUserId(pmo.getUserId())
                    .setUserName(pmo.getUserName())
                    .setRole(ProjectOperationRoleEnum.PMO.getValue())
                    .init(ProjectOperationModuleEnum.WARRANTY);
            saveList.add(pmoItem);
        });
        return saveList;
    }

    /**
     * 构建重启提醒消息
     *
     * @param projectInfo 项目信息
     * @param overdue     逾期
     * @return {@link BaseSendMsgDTO }
     */
    private static BaseSendMsgDTO buildRestartReminderMsg(ProjectInfo projectInfo, boolean overdue) {
        String title;
        String content;
        String path = "/view-businesses/project-manage/project-manage-list?projectId=" + projectInfo.getId() + "&subTab=BUSINESS_OPPORTUNITY_INFORMATION";
        if (overdue) {
            title = "【挂起项目重启逾期提醒】";
            content = "您好，" + projectInfo.getItemName() + "项目的已逾期仍未重启，请前往重启项目或进行异常终止操作～";
        } else {
            title = "【挂起项目重启提醒】";
            content = "您好，" + projectInfo.getItemName() + "项目的重启日期已到，请前往重启项目，逾期3个月未启动项目将自动转为异常终止～";
        }
        return new BaseSendMsgDTO()
                .setTitle(title)
                .setContent(content)
                .setMsgTypeEnum(MsgTypeEnum.NOTICE_MSG)
                .setTargetTypeEnum(TargetTypeEnum.USERS)
                .setChannelEnums(CollUtil.newLinkedHashSet(ChannelEnum.WECOM, ChannelEnum.MAIL))
                .setPath(path)
                .populateSender()
                .toOneTarget(projectInfo.getManagerUserId(), projectInfo.getManagerUserName());
    }

    /**
     * 构建提醒消息
     *
     * @param item        项目
     * @param projectInfo 项目信息
     * @return {@link BaseSendMsgDTO }
     */
    private static BaseSendMsgDTO buildReminderMsg(ProjectOperationConfirmation item, ProjectInfo projectInfo) {
        String title;
        String content;
        String path;
        if (EnumUtils.valueEquals(item.getModule(), ProjectOperationModuleEnum.WARRANTY)) {
            title = "【项目质保期确认】";
            content = "您好，" + projectInfo.getItemName() + "项目已完成合同签署、交付、验收等全部交付工作，项目进入质保期，请前往确认～";
            path = "/view-businesses/project-manage/project-manage-list?projectId=" + projectInfo.getId() + "&subTab=PROJECT_OVERVIEW&todo=zbConfirm";
        } else {
            title = "【项目关闭确认】";
            content = "您好，" + projectInfo.getItemName() + "项目已完成合同签署、交付、验收、质保等全部合同规定工作，完成履约，项目关闭。请前往确认～";
            path = "/view-businesses/project-manage/project-manage-list?projectId=" + projectInfo.getId() + "&subTab=PROJECT_OVERVIEW&todo=closeConfirm";
        }

        return new BaseSendMsgDTO()
                .setTitle(title)
                .setContent(content)
                .setMsgTypeEnum(MsgTypeEnum.NOTICE_MSG)
                .setTargetTypeEnum(TargetTypeEnum.USERS)
                .setChannelEnums(CollUtil.newLinkedHashSet(ChannelEnum.WECOM, ChannelEnum.MAIL))
                .setPath(path)
                .populateSender()
                .toOneTarget(item.getUserId(), item.getUserName());
    }

    /**
     * 项目状态流转抄送
     */
    @Override
    public void statusTransferCarbonCopy(Long projectId,
                                         Collection<Long> userIds,
                                         ProjectOperationModuleEnum moduleEnum) {
        ProjectInfo projectInfo = projectInfoMapper.selectById(projectId);
        if (CollUtil.isEmpty(userIds) || projectInfo == null) {
            return;
        }
        List<Roster> rosterList = rosterService.lambdaQuery().in(Roster::getId, userIds).list();
        List<BaseSendMsgDTO> sendMsgDTOList = rosterList.stream()
                .map(roster -> buildCarbonCopyMsg(roster, projectInfo, moduleEnum))
                .collect(Collectors.toList());
        bcpMessageService.batchSendMsg(sendMsgDTOList);
    }

    private static BaseSendMsgDTO buildCarbonCopyMsg(Roster roster,
                                                     ProjectInfo projectInfo,
                                                     ProjectOperationModuleEnum moduleEnum) {
        String title;
        String content;
        String path;
        if (ProjectOperationModuleEnum.WARRANTY.equals(moduleEnum)) {
            title = "【项目质保期知悉】";
            content = "您好，" + projectInfo.getItemName() + "项目已完成合同签署、交付、验收等全部交付工作，项目进入质保期。";
            path = "/view-businesses/project-manage/project-manage-list?projectId=" + projectInfo.getId() + "&subTab=PROJECT_OVERVIEW&todo=zbConfirm";
        } else {
            title = "【项目关闭知悉】";
            content = "您好，" + projectInfo.getItemName() + "项目已完成合同签署、交付、验收、质保等全部合同规定工作，完成履约，项目关闭。";
            path = "/view-businesses/project-manage/project-manage-list?projectId=" + projectInfo.getId() + "&subTab=PROJECT_OVERVIEW&todo=closeConfirm";
        }
        return new BaseSendMsgDTO()
                .setTitle(title)
                .setContent(content)
                .setMsgTypeEnum(MsgTypeEnum.NOTICE_MSG)
                .setTargetTypeEnum(TargetTypeEnum.USERS)
                .setChannelEnums(CollUtil.newLinkedHashSet(ChannelEnum.WECOM, ChannelEnum.MAIL))
                .setPath(path)
                .populateSender()
                .toOneTarget(roster.getId(), roster.getAliasName());
    }

}
