package com.gok.pboot.pms.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 日报审核 分页展示VO
 * @createTime 2023/2/15 11:02
 */
@Deprecated
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@ApiModel("日报审核（项目维度）")
public class DailyReviewPageVO {
    /**
     * 项目id
     */
    @ApiModelProperty("项目id")
    private Long projectId;
    /**
     * 项目名称
     */
    @ApiModelProperty("项目名称")
    private String projectName;
    /**
     * 审核数量
     */
    @ApiModelProperty("审核数量")
    private Integer approvalNum;
    /**
     * 当前查询下审核数量
     */
    @ApiModelProperty("当前查询下审核数量")
    private Integer currentApprovalNum;
    /**
     * 当前查询下日常工时
     */
    @ApiModelProperty("当前查询下日常工时")
    private BigDecimal currentNormalHours;
    /**
     * 日常工时
     */
    @ApiModelProperty("日常工时")
    private BigDecimal normalHours;
    /**
     * 加班工时
     */
    @ApiModelProperty("加班工时")
    private BigDecimal addedHours;
    /**
     * 当前查询下
     */
    @ApiModelProperty("当前查询下加班工时")
    private BigDecimal currentAddedHours;
    /**
     * 汇总工时
     */
    @ApiModelProperty("汇总工时")
    private Double aggregatedDays;
    /**
     * 用户id集合
     */
    private Long userId;
    /**
     * 用户id集合
     */
    private List<Long> userIds;
    /**
     * 是否可编辑 ,1可0不可
     * {@link com.gok.pboot.enumeration.entity.YesOrNoEnum}
     */
    private Integer isEdit;
}
