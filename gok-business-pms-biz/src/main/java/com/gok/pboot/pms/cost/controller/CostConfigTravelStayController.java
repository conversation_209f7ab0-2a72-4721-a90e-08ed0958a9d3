package com.gok.pboot.pms.cost.controller;


import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.cost.entity.dto.CostConfigTravelStayDTO;
import com.gok.pboot.pms.cost.entity.vo.CostConfigTravelStayVO;
import com.gok.pboot.pms.cost.service.ICostConfigTravelStayService;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 差旅住宿标准配置 控制器
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Validated
@RestController
@AllArgsConstructor
@RequestMapping("/costConfigTravelStay")
public class CostConfigTravelStayController {

    private final ICostConfigTravelStayService service;

    /**
     * 获取最新版本差旅住宿标准配置
     *
     * @return {@link ApiResult }<{@link List }<{@link CostConfigTravelStayVO }>>
     */
    @GetMapping("/findList")
    public ApiResult<List<CostConfigTravelStayVO>> getCostConfigTravelStayList() {
    return ApiResult.success(service.getCostConfigTravelStayList(), "获取成功");
    }

    /**
     * 编辑差旅住宿标准配置
     *
     * @param dtoList DTO
     * @return {@link ApiResult }<{@link String }>
     */
    @PostMapping("/edit")
    public ApiResult<String> editCostConfigTravelStayList(@RequestBody @Valid List<CostConfigTravelStayDTO> dtoList) {
        service.editCostConfigTravelStayList(dtoList);
    return ApiResult.success("操作成功");
    }

    /**
     * 根据版本id获取差旅住宿标准配置
     *
     * @param versionId 版本 ID
     * @return {@link ApiResult }<{@link List }<{@link CostConfigTravelStayVO }>>
     */
    @GetMapping("/findByVersionId/{versionId}")
    public ApiResult<List<CostConfigTravelStayVO>> getCostConfigTravelStaysByVersionId(@PathVariable Long versionId) {
        return ApiResult.success(service.getCostConfigTravelStaysByVersionId(versionId), "获取成功");
    }

}
