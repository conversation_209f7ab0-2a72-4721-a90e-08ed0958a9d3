package com.gok.pboot.pms.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 项目组成员表（Oa项目项目台账-明细6）
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-17 11:11:29
 */
@Data
@TableName("project_member")
public class ProjectMember extends BaseEntity<Long> {

	/**
	 * 项目ID
	 */
	private Long projectId;
	/**
	 * 角色
	 */
	private String memberRole;
	/**
	 * 级别
	 */
	private Integer memberLevel;
	/**
	 * 数量
	 */
	private Integer memberNumber;
	/**
	 * 预计投入周期
	 */
	private String expectedCycle;
	/**
	 * 预计投入度（百分比）
	 */
	private BigDecimal expectedEngagement;
	/**
	 * 指派人ID
	 */
	private Long assignorId;
	/**
	 * 指派人
	 */
	private String assignor;

}
