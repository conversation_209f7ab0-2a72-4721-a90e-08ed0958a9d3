package com.gok.pboot.pms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.entity.domain.DdRelation;
import com.gok.pboot.pms.enumeration.DdRelationTypeEnum;

/**
 * 滴滴关联表 服务类
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
public interface IDdRelationService extends IService<DdRelation> {

    /**
     * 保存或更新关联关系
     *
     * @param relateId      关联ID
     * @param relateType    关联类型
     * @param didiId 滴滴ID
     * @return 是否成功
     */
    boolean saveOrUpdateRelation(Long relateId, DdRelationTypeEnum relateType, String didiId);


    /**
     * 根据滴滴项目ID和关联类型查询关联关系
     *
     * @param didiProjectId 滴滴项目ID
     * @param relateType    关联类型
     * @return 关联关系
     */
    DdRelation getByDidiIdAndType(String didiProjectId, DdRelationTypeEnum relateType);


    /**
     * 获取由迪迪项目id和类型
     *
     * @param relateId   与id
     * @param relateType 关联类型
     * @return {@link DdRelation }
     */
    DdRelation getByRelateIdAndType(Long relateId, DdRelationTypeEnum relateType);

    /**
     * 更新同步时间
     *
     * @param relateId   关联ID
     * @param relateType 关联类型
     */
    void updateSyncTime(Long relateId, DdRelationTypeEnum relateType);



}
