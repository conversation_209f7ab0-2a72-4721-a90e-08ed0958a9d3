package com.gok.pboot.pms.eval.entity.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 工单评价校准
 * <AUTHOR>
 */
@Data
public class EvalTaskCalibrationDTO {

    /**
     * id
     */
    private Long id;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 工单类型
     */
    private Integer taskType;

    /**
     * 员工id
     */
    private Long employeeId;

    /**
     * 调整后的评分
     */
    private BigDecimal adjustedScore;

    /**
     * 调整后等级
     */
    private Integer adjustedLevel;

    /**
     * 调整后等级Txt
     */
    private String adjustedLevelTxt;

    /**
     * 调整原因
     */
    private String adjustmentReason;

    /**
     * 审核状态（0待审核，1审核中,2已审核）
     */
    private Integer calibrationStatus;

    /**
     * 退回原因
     */
    private String returnReason;
}