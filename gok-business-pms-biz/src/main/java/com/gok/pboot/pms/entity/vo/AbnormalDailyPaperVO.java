package com.gok.pboot.pms.entity.vo;

import com.gok.pboot.pms.Util.DailyPaperDateUtils;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.Util.SysDeptUtils;
import com.gok.pboot.pms.entity.DailyPaper;
import com.gok.pboot.pms.entity.SysDept;
import com.gok.pboot.pms.enumeration.ApprovalStatusEnum;
import com.gok.pboot.pms.enumeration.DailyPaperAbnormalEnum;
import com.gok.pboot.pms.enumeration.PersonnelStatusEnum;
import com.google.common.base.Strings;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.data.util.Pair;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Map;
import java.util.Optional;

/**
 * - 异常日报 -
 *
 * <AUTHOR>
 * @version 1.3.4
 * @date 2022/11/10 15:31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
public class AbnormalDailyPaperVO {

    private Long id;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 姓名
     */
    private String userRealName;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户状态
     * @see com.gok.pboot.pms.enumeration.PersonnelStatusEnum
     */
    private Integer personnelStatus;

    /**
     * 用户状态名称
     */
    private String personnelStatusName;

    /**
     * 日报日期
     */
    private LocalDate submissionDate;

    /**
     * 日期（已格式化）
     */
    private String submissionDateFormatted;

    /**
     * 假期类型：1-法定节假日 0-普通休息日
     */
    private Integer holidayType;

    /**
     * 正常工时
     */
    private BigDecimal normalHours;

    /**
     * 加班工时
     */
    private BigDecimal addedHours;

    /**
     * 总工时
     */
    private BigDecimal totalHours;

    /**
     * 请休假小时
     */
    private BigDecimal leaveHourData;

    /**
     * 调休工时
     */
    private BigDecimal leaveHours;

    /**
     * 备注（异常情况）
     */
    private String remark;

    /**
     * 审核状态
     */
    private Integer approvalStatus;

    /**
     * 审核员
     */
    private String operator;

    /**
     * 异常类型
     * @see com.gok.pboot.pms.enumeration.DailyPaperAbnormalEnum
     */
    private Integer abnormalType;

    /**
     * 异常类型名称
     */
    private String abnormalTypeName;

    public static AbnormalDailyPaperVO of(
            DailyPaper paper,
            String userRealName,
            Map<Long, SysDept> deptMap,
            BigDecimal leaveHour,
            BigDecimal compensatoryLeaveHour,
            Pair<DailyPaperAbnormalEnum, String> abnormalTypeAndRemark,
            String operators,
            Map<LocalDate, Integer> holidayTypeMap
    ) {
        AbnormalDailyPaperVO result = new AbnormalDailyPaperVO();
        Integer approvalStatus = paper.getApprovalStatus();
        boolean unSubmitFlag = EnumUtils.valueEquals(approvalStatus, ApprovalStatusEnum.WTJ) ||
                EnumUtils.valueEquals(approvalStatus, ApprovalStatusEnum.WTB) ||
                EnumUtils.valueEquals(approvalStatus, ApprovalStatusEnum.YTH) ||
                EnumUtils.valueEquals(approvalStatus, ApprovalStatusEnum.BTG);

        result.setId(paper.getId());
        result.setApprovalStatus(approvalStatus);
        result.setDeptName(SysDeptUtils.collectFullName(deptMap, paper.getUserDeptId()));
        result.setUserRealName(userRealName);
        result.setUserId(paper.getUserId());
        result.setPersonnelStatus(paper.getUserStatus());
        result.setPersonnelStatusName(EnumUtils.getNameByValue(PersonnelStatusEnum.class, paper.getUserStatus()));
        result.setSubmissionDate(paper.getSubmissionDate());
        result.setSubmissionDateFormatted(DailyPaperDateUtils.asDateString(paper.getSubmissionDate()));
        result.setHolidayType(Optional.ofNullable(holidayTypeMap.getOrDefault(paper.getSubmissionDate(),null)).isPresent() ? holidayTypeMap.getOrDefault(paper.getSubmissionDate(),null) : null);
        result.setNormalHours(unSubmitFlag ? BigDecimal.ZERO : paper.getDailyHourCount());
        result.setAddedHours(unSubmitFlag ? BigDecimal.ZERO : paper.getAddedHourCount());
        result.setTotalHours(unSubmitFlag ? BigDecimal.ZERO : result.getNormalHours().add(paper.getAddedHourCount()));
        result.setLeaveHourData(leaveHour);
        result.setLeaveHours(compensatoryLeaveHour);
        result.setRemark(abnormalTypeAndRemark.getSecond());
        result.setApprovalStatus(approvalStatus);
        result.setAbnormalType(abnormalTypeAndRemark.getFirst().getValue());
        result.setAbnormalTypeName(abnormalTypeAndRemark.getFirst().getName());
        result.setOperator(Strings.nullToEmpty(operators));

        return result;
    }
}
