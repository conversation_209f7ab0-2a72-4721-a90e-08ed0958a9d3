package com.gok.pboot.pms.entity.vo;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.Util.SysDeptUtils;
import com.gok.pboot.pms.entity.SysDept;
import com.gok.pboot.pms.entity.domain.ProjectInfo;
import com.gok.pboot.pms.enumeration.*;
import com.google.common.base.Strings;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Map;
import java.util.Optional;

/**
 * 项目基本信息VO
 *
 * <AUTHOR>
 * @date 2023/10/23
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class ProjectBaseInfoVO {

    /**
     * ID
     */
    private String id;

    /**
     * 项目名称
     */
    private String itemName;
    private String projectStatus;
    /**
     * 项目状态名称
     */
    private String projectStatusName;

    /**
     * 业务归属一级部门
     */
    private String firstLevelDepartment;

    /**
     * 项目所在地
     */
    private String projectLocation;

    /**
     * 是否内部项目（1=是，2=否，""）
     *
     * @see com.gok.pboot.pms.enumeration.IsNoInternalProjectEnum
     */
    private String isNotInternalProject;

    /**
     * 是否内部项目名称
     */
    private String isNotInternalProjectName;


    /**
     * 预估毛利率
     */
    private String ygmll;

    /**
     * 项目整包预算
     */
    private BigDecimal proPackageBudget;

    /**
     * 业务方向名称
     */
    private String businessDirectionName;

    /**
     * 项目背景及建设内容（项目目标及建设范围）
     */
    private String proConstructionScope;

    /**
     * 我司建设内容
     */
    private String wsjsnr;

    /**
     * 项目交付部门id
     */
    private Long proDeliveryDepartmentId;

    /**
     * 项目交付部门
     */
    private String proDeliveryDepartment;

    /**
     * 业务归属部门id
     */
    private Long businessDepartmentId;

    /**
     * 业务归属部门
     */
    private String businessDepartment;

    /**
     * 签约客户
     */
    private String contractCustomer;

    /**
     * 最终客户
     */
    private String endCustomer;

    /**
     * 业务类型
     */
    private Integer secondaryBusinessType;

    private String secondaryBusinessTypeName;

    /**
     * 项目类型
     */
    private Integer projectType;

    private String projectTypeName;

    /**
     * 交付形式
     *
     * @see com.gok.pboot.pms.enumeration.DeliverTypeEnum
     */
    private Integer deliverType;

    private String deliverTypeName;


    /**
     * 销售经理 ID
     */
    private Long preSaleUserId;
    /**
     * 销售经理 姓名
     */
    private String preSaleUserName;


    /**
     * 销售经理 ID
     */
    private Long salesmanUserId;

    /**
     * 销售经理 姓名
     */
    private String projectSalesperson;

    /**
     * 项目经理 ID
     */
    private Long managerUserId;

    /**
     * 项目经理姓名
     */
    private String managerUserName;


    /**
     * 商务经理 ID
     */
    private Long businessManagerId;

    /**
     * 商务经理姓名
     */
    private String businessManagerName;


    /**
     * 项目重启日期
     */
    private LocalDate xmcqrq;
    public static ProjectBaseInfoVO of(ProjectInfo po, Map<Long, SysDept> deptIdMap) {
        ProjectBaseInfoVO result = BeanUtil.copyProperties(po, ProjectBaseInfoVO.class);

        result.setId(String.valueOf(po.getId()));
        result.setItemName(Strings.nullToEmpty(po.getItemName()));
        result.setFirstLevelDepartment(Strings.nullToEmpty(po.getFirstLevelDepartment()));
        result.setProjectLocation(Strings.nullToEmpty(po.getProjectLocation()));
        result.setYgmll(Strings.nullToEmpty(po.getYgmll()));
        result.setProPackageBudget(po.getProPackageBudget());
        result.setProConstructionScope(Strings.nullToEmpty(po.getProConstructionScope()));
        result.setWsjsnr(Strings.nullToEmpty(po.getWsjsnr()));
        result.setXmcqrq(po.getXmcqrq());
        // 项目状态
        String projectStatus = Strings.nullToEmpty(po.getProjectStatus());
        result.setProjectStatus(projectStatus);
        result.setProjectStatusName(StringUtils.EMPTY.equals(projectStatus) ? StringUtils.EMPTY : ProjectStatusEnum.getNameByStrVal(projectStatus));
        // 是否内部项目
        String notInternalProject = po.getIsNotInternalProject() == null ? StringUtils.EMPTY : String.valueOf(po.getIsNotInternalProject());
        result.setIsNotInternalProject(notInternalProject);
        result.setIsNotInternalProjectName(StringUtils.EMPTY.equals(notInternalProject) ? StringUtils.EMPTY : IsNoInternalProjectEnum.getNameByVal(Integer.parseInt(notInternalProject)));
        // 业务方向
        String businessDirection = (Strings.nullToEmpty(po.getBusinessDirection()));
        result.setBusinessDirectionName(StringUtils.isBlank(businessDirection) ? StringUtils.EMPTY : Strings.nullToEmpty(BusinessDirectionEnum.getNameByVal(Integer.parseInt(businessDirection))));
        // 部门处理
        Long proDeliveryDepartmentId = po.getProDeliveryDepartmentId();
        result.setProDeliveryDepartmentId(proDeliveryDepartmentId);
        result.setProDeliveryDepartment(proDeliveryDepartmentId == null ? StrUtil.EMPTY : SysDeptUtils.collectFullName(deptIdMap, proDeliveryDepartmentId));
        Long firstLevelDepartmentId = po.getFirstLevelDepartmentId();
        Long secondLevelDepartmentId = po.getSecondLevelDepartmentId();
        result.setBusinessDepartmentId(Optional.ofNullable(secondLevelDepartmentId).orElse(firstLevelDepartmentId));
        result.setBusinessDepartment(secondLevelDepartmentId == null ? firstLevelDepartmentId == null ? po.getFirstLevelDepartment() : SysDeptUtils.collectFullName(deptIdMap, firstLevelDepartmentId) : SysDeptUtils.collectFullName(deptIdMap, secondLevelDepartmentId));
        result.setProDeliveryDepartment(po.getProDeliveryDepartmentId() == null ? StrUtil.EMPTY : SysDeptUtils.collectFullName(deptIdMap, po.getProDeliveryDepartmentId()));
        // 业务类型
        result.setSecondaryBusinessType(po.getSecondaryBusinessType());
        result.setSecondaryBusinessTypeName(EnumUtils.getNameByValue(SecondaryBusinessTypeEnum.class, po.getSecondaryBusinessType()));
        //项目类型
        Integer projectType = po.getProjectType();
        if (null != projectType) {
            result.setProjectType(projectType);
            String projectTypeName = ObjectUtil.equal(1L, projectType) ? InternalProjectTypeEnum.getNameByVal(projectType) : EnumUtils.getNameByValue(ProjectTypeEnum.class, projectType);
            result.setProjectTypeName(projectTypeName);
        }
        //交付形式
        result.setDeliverType(po.getDeliverType());
        result.setDeliverTypeName(EnumUtils.getNameByValue(DeliverTypeEnum.class, po.getDeliverType()));


        // 销售经理
        result.setPreSaleUserId(po.getPreSaleUserId());
        result.setPreSaleUserName(Strings.nullToEmpty(po.getPreSaleUserName()));
        // 客户经理
        result.setSalesmanUserId(po.getSalesmanUserId());
        result.setProjectSalesperson(Strings.nullToEmpty(po.getProjectSalesperson()));
        //  项目经理
        result.setManagerUserId(po.getManagerUserId());
        result.setManagerUserName(Strings.nullToEmpty(po.getManagerUserName()));
        //  商务经理
        result.setBusinessManagerId(po.getBusinessManagerId());
        result.setBusinessManagerName(Strings.nullToEmpty(po.getBusinessManager()));

        return result;
    }

}
