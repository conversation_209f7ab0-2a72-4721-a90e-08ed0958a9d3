package com.gok.pboot.pms.cost.service.impl;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.common.exception.ServiceException;
import com.gok.pboot.pms.cost.entity.domain.CostDeliverTask;
import com.gok.pboot.pms.cost.entity.domain.CostPresalesTaskConfig;
import com.gok.pboot.pms.cost.entity.domain.CostTaskCategoryManagement;
import com.gok.pboot.pms.cost.entity.dto.CostPresalesTaskConfigDTO;
import com.gok.pboot.pms.cost.entity.vo.CostPresalesTaskConfigVO;
import com.gok.pboot.pms.cost.entity.vo.DeliverCostBudgetListVO;
import com.gok.pboot.pms.cost.enums.CostPresalesTaskManagerRoleEnum;
import com.gok.pboot.pms.cost.enums.CostPresalesTaskManagerTypeEnum;
import com.gok.pboot.pms.cost.enums.CostTaskStatusEnum;
import com.gok.pboot.pms.cost.mapper.CostPresalesTaskConfigMapper;
import com.gok.pboot.pms.cost.service.ICostDeliverTaskService;
import com.gok.pboot.pms.cost.service.ICostPresalesTaskConfigService;
import com.gok.pboot.pms.cost.service.ICostTaskCategoryManagementService;
import com.gok.pboot.pms.entity.domain.ProjectInfo;
import com.gok.pboot.pms.entity.dto.ProjectStakeholderMemberBatchDTO;
import com.gok.pboot.pms.entity.dto.ProjectStakeholderMemberDTO;
import com.gok.pboot.pms.enumeration.ProjectTaskKindEnum;
import com.gok.pboot.pms.enumeration.RoleTypeEnum;
import com.gok.pboot.pms.service.IProjectInfoService;
import com.gok.pboot.pms.service.IProjectStakeholderMemberService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.gok.pboot.pms.cost.entity.dto.CostPresalesTaskConfigDTO.buildDto;
import static com.gok.pboot.pms.cost.entity.vo.CostPresalesTaskConfigVO.buildVo;

/**
 * 售前报工工单配置表 服务实现类
 *
 * <AUTHOR>
 * @date 2025/04/09
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CostPresalesTaskConfigServiceImpl extends ServiceImpl<CostPresalesTaskConfigMapper, CostPresalesTaskConfig> implements ICostPresalesTaskConfigService {

    private final ICostTaskCategoryManagementService costTaskCategoryManagementService;

    private final IProjectStakeholderMemberService projectStakeholderMemberService;

    private final ICostDeliverTaskService costDeliverTaskService;

    private final IProjectInfoService projectInfoService;



    private static final int TASK_FIRST_LEVEL = 1;
    private static final int TASK_THIRD_LEVEL = 3;



    /**
     * 查找页面
     *
     * @param pageRequest 页面请求
     * @return {@link Page }<{@link CostPresalesTaskConfigVO }>
     */
    @Override
    public Page<CostPresalesTaskConfigVO> findPage(PageRequest pageRequest) {
        // 创建分页对象
        Page<CostPresalesTaskConfig> page = new Page<>(pageRequest.getPageNumber(), pageRequest.getPageSize());

        // 构建查询条件，只查询一级工单
        LambdaQueryWrapper<CostPresalesTaskConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CostPresalesTaskConfig::getTaskLevel, TASK_FIRST_LEVEL)
                .orderByAsc(CostPresalesTaskConfig::getCtime);

        // 执行分页查询
        Page<CostPresalesTaskConfig> resultPage = page(page, wrapper);

        // 转换为VO对象
        Page<CostPresalesTaskConfigVO> voPage = new Page<>(resultPage.getCurrent(), resultPage.getSize(), resultPage.getTotal());

        List<CostPresalesTaskConfig> firstLevelTaskList = resultPage.getRecords();
        if (CollUtil.isNotEmpty(firstLevelTaskList)) {
            // 获取所有一级工单的ID
            Set<Long> firstLevelIds = CollStreamUtil.toSet(firstLevelTaskList, CostPresalesTaskConfig::getId);

            // 查询所有二级工单
            List<CostPresalesTaskConfig> secondLevelTaskList = lambdaQuery()
                    .in(CostPresalesTaskConfig::getParentId, firstLevelIds)
                    .orderByAsc(CostPresalesTaskConfig::getId)
                    .list();

            // 如果存在二级工单，查询其下的三级工单
            List<CostPresalesTaskConfig> thirdLevelTaskList = new ArrayList<>();
            if (CollUtil.isNotEmpty(secondLevelTaskList)) {
                Set<Long> secondLevelIds = CollStreamUtil.toSet(secondLevelTaskList, CostPresalesTaskConfig::getId);
                thirdLevelTaskList = lambdaQuery()
                        .in(CostPresalesTaskConfig::getParentId, secondLevelIds)
                        .orderByAsc(CostPresalesTaskConfig::getId)
                        .list();
            }

            // 构建二级和三级工单的父子关系Map
            Map<Long, List<CostPresalesTaskConfig>> thirdLevelMap = thirdLevelTaskList.stream()
                    .collect(Collectors.groupingBy(CostPresalesTaskConfig::getParentId));

            // 构建一级和二级工单的父子关系Map
            Map<Long, List<CostPresalesTaskConfigVO>> secondLevelMap = secondLevelTaskList.stream()
                    .map(secondLevel -> {
                        CostPresalesTaskConfigVO secondLevelVO = convertToVO(secondLevel);
                        // 设置三级工单
                        List<CostPresalesTaskConfigVO> thirdLevelVoList = thirdLevelMap.getOrDefault(secondLevel.getId(), new ArrayList<>())
                                .stream()
                                .map(this::convertToVO)
                                .collect(Collectors.toList());
                        secondLevelVO.setChildren(thirdLevelVoList);
                        return secondLevelVO;
                    })
                    .collect(Collectors.groupingBy(CostPresalesTaskConfigVO::getParentId));

            // 构建完整的树形结构
            List<CostPresalesTaskConfigVO> voList = firstLevelTaskList.stream()
                    .map(firstLevel -> {
                        CostPresalesTaskConfigVO firstLevelVO = convertToVO(firstLevel);
                        firstLevelVO.setChildren(secondLevelMap.getOrDefault(firstLevel.getId(), new ArrayList<>()));
                        return firstLevelVO;
                    })
                    .collect(Collectors.toList());

            voPage.setRecords(voList);
        }

        return voPage;
    }


    /**
     * 将实体转换为VO
     *
     * @param entity 实体
     * @return VO
     */
    private CostPresalesTaskConfigVO convertToVO(CostPresalesTaskConfig entity) {
        if (entity == null) {
            return new CostPresalesTaskConfigVO();
        }

        // 查询工单类别
        Map<Integer, CostTaskCategoryManagement> costTaskCategoryMap = costTaskCategoryManagementService.getCostTaskCategoryMap();

        return buildVo(entity, costTaskCategoryMap);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePresalesTaskConfig(Long id) {
        if (id == null) {
            throw new ServiceException("工单ID不能为空");
        }

        List<CostPresalesTaskConfig> taskListToDelete = getAllChildTaskList(id);

        // 批量删除工单
        try {
            removeBatchByIds(taskListToDelete);
        } catch (Exception e) {
            throw new ServiceException("删除工单失败", e);
        }
    }

    /**
     * 批量编辑售前任务配置
     *
     * @param batchDto 批量 DTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchEditPresalesTaskConfig(CostPresalesTaskConfigDTO batchDto) {
        if (batchDto == null) {
            throw new ServiceException("工单列表不能为空");
        }

        List<CostPresalesTaskConfig> updateTaskConfigList = new ArrayList<>();
        List<CostPresalesTaskConfig> deleteTaskConfigList = new ArrayList<>();

        try {
            // 获取当前工单及其所有子工单
            List<CostPresalesTaskConfig> existingTasks = getAllChildTaskList(batchDto.getId());
            updateProcessTask(batchDto, updateTaskConfigList, deleteTaskConfigList, existingTasks);


            // 执行批量更新
            if (CollUtil.isNotEmpty(updateTaskConfigList)) {
                updateBatchById(updateTaskConfigList);
            }

            // 执行批量删除
            if (CollUtil.isNotEmpty(deleteTaskConfigList)) {
                removeBatchByIds(deleteTaskConfigList);
            }
        } catch (Exception e) {
            throw new ServiceException("更新工单配置时发生错误", e);
        }
    }

    /**
     * 获取所有子工单
     *
     * @param taskId 工单ID
     * @return 工单及其所有子工单列表
     */
    private List<CostPresalesTaskConfig> getAllChildTaskList(Long taskId) {
        if (taskId == null) {
            return Collections.emptyList();
        }

        List<CostPresalesTaskConfig> allTaskList = new ArrayList<>();
        
        // 查询当前工单
        CostPresalesTaskConfig currentTask = getById(taskId);
        if (currentTask == null) {
            throw new ServiceException("工单不存在");
        }

        allTaskList.add(currentTask);

        // 查询所有二级工单
        List<CostPresalesTaskConfig> secondLevelTaskList = lambdaQuery()
                .eq(CostPresalesTaskConfig::getParentId, taskId)
                .list();

        if (CollUtil.isNotEmpty(secondLevelTaskList)) {
            allTaskList.addAll(secondLevelTaskList);

            // 查询所有三级工单
            Set<Long> secondLevelIds = CollStreamUtil.toSet(secondLevelTaskList, CostPresalesTaskConfig::getId);

            List<CostPresalesTaskConfig> thirdLevelTaskList = lambdaQuery()
                    .in(CostPresalesTaskConfig::getParentId, secondLevelIds)
                    .list();

            if (CollUtil.isNotEmpty(thirdLevelTaskList)) {
                allTaskList.addAll(thirdLevelTaskList);
            }
        }

        
        return allTaskList;
    }

    /**
     * 更新进程任务
     *
     * @param dto               DTO
     * @param updateTaskList    需要更新的工单列表
     * @param deleteTaskList    需要删除的工单列表
     * @param existingTasks     现有的工单及其子工单列表
     */
    private void updateProcessTask(CostPresalesTaskConfigDTO dto, 
                                 List<CostPresalesTaskConfig> updateTaskList,
                                 List<CostPresalesTaskConfig> deleteTaskList,
                                 List<CostPresalesTaskConfig> existingTasks) {
        // 验证任务级别
        verifyLevel(dto);

        // 构建任务实体
        CostPresalesTaskConfig costPresalesTaskConfig = new CostPresalesTaskConfig();
        costPresalesTaskConfig.setId(dto.getId());
        CostPresalesTaskConfig task = buildDto(dto, costPresalesTaskConfig);
        updateTaskList.add(task);

        // 获取当前工单的所有子工单ID
        Set<Long> currentTaskIds = new HashSet<>();
        currentTaskIds.add(dto.getId());

        // 递归处理子任务
        if (CollUtil.isNotEmpty(dto.getChildren())) {
            for (CostPresalesTaskConfigDTO childDto : dto.getChildren()) {
                currentTaskIds.add(childDto.getId());
                updateProcessTask(childDto, updateTaskList, deleteTaskList, existingTasks);
            }
        }

        // 找出需要删除的工单
        if (CollUtil.isNotEmpty(existingTasks)) {
            List<CostPresalesTaskConfig> tasksToDelete = existingTasks.stream()
                    .filter(existingTask -> {
                        // 如果是当前工单的直接子工单，且不在当前传入的工单列表中，则需要删除
                        return existingTask.getParentId() != null
                                && existingTask.getParentId().equals(dto.getId())
                                && !currentTaskIds.contains(existingTask.getId());
                    })
                    .collect(Collectors.toList());
            
            if (CollUtil.isNotEmpty(tasksToDelete)) {
                deleteTaskList.addAll(tasksToDelete);
            }
        }
    }

    /**
     * 批量保存默认任务配置
     *
     * @param batchDto 批量 DTO
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchSaveDefaultTaskConfig(CostPresalesTaskConfigDTO batchDto) {
        if (batchDto == null) {
            throw new ServiceException("工单列表不能为空");
        }

        List<CostPresalesTaskConfig> allTaskConfigList = new ArrayList<>();
        try {
            // 处理工单
            saveProcessTask(batchDto, allTaskConfigList, null);

            // 批量保存所有工单
            if (CollUtil.isNotEmpty(allTaskConfigList)) {
                saveBatch(allTaskConfigList);
            }

        } catch (Exception e) {
            throw new ServiceException("保存工单配置时发生错误", e);
        }
    }

    /**
     * 保存进程任务
     *
     * @param dto               DTO
     * @param allTaskConfigList 所有任务配置列表
     * @param parentId          父 ID
     */
    private void saveProcessTask(CostPresalesTaskConfigDTO dto, List<CostPresalesTaskConfig> allTaskConfigList, Long parentId) {
        // 设置父ID
        if (parentId != null) {
            dto.setParentId(parentId);
        }

        // 验证任务级别
        verifyLevel(dto);

        // 构建任务实体
        CostPresalesTaskConfig task = buildDto(dto, new CostPresalesTaskConfig());
        allTaskConfigList.add(task);

        // 递归处理子任务
        if (CollUtil.isNotEmpty(dto.getChildren())) {
            for (CostPresalesTaskConfigDTO childDto : dto.getChildren()) {
                saveProcessTask(childDto, allTaskConfigList, task.getId());
            }
        }
    }

    /**
     * 保存项目干系人
     *
     * @param projectId     项目 ID
     * @param memberDTOList 成员 DTOLIST
     */
    private void addProjectStakeholderMemberBatchDTO(Long projectId, List<ProjectStakeholderMemberDTO> memberDTOList, List<ProjectStakeholderMemberBatchDTO> newMemberBatchList) {
        ProjectStakeholderMemberBatchDTO memberBatchDTO = new ProjectStakeholderMemberBatchDTO();
        memberBatchDTO.setProjectId(projectId);
        memberBatchDTO.setRoleType(RoleTypeEnum.PROJECT_MEMBER.getValue());
        memberBatchDTO.setMembers(memberDTOList);
        newMemberBatchList.add(memberBatchDTO);
    }

    /**
     * 添加项目干系人
     *
     * @param taskConfig    任务配置
     * @param newMemberList 新成员名单
     */
    private static void addProjectStakeholderMemberDTO(CostPresalesTaskConfig taskConfig, List<ProjectStakeholderMemberDTO> newMemberList) {
        ProjectStakeholderMemberDTO memberDTO = new ProjectStakeholderMemberDTO();
        memberDTO.setMemberId(taskConfig.getManagerId());
        memberDTO.setMemberName(taskConfig.getManagerName());
        newMemberList.add(memberDTO);
    }

    /**
     * 验证级别
     *
     * @param dto DTO
     */
    private static void verifyLevel(CostPresalesTaskConfigDTO dto) {
        // 验证任务级别
        int taskLevel = dto.getTaskLevel();
        if (taskLevel < TASK_FIRST_LEVEL || taskLevel > TASK_THIRD_LEVEL) {
            throw new ServiceException("工单级别错误");
        }
    }

    /**
     * 同步生成默认工单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized void syncGenerateDefaultTask() {
        // 获取默认工单配置
        List<CostPresalesTaskConfig> defaultTaskConfigList = list();
        if (CollUtil.isEmpty(defaultTaskConfigList)) {
            throw new ServiceException("未找到默认工单配置");
        }

        // 查询所有在建和商机状态的项目并没有售前支撑工单的工单列表
        List<ProjectInfo> projectList = baseMapper.getNotExistDeliverTaskProjectList();
        if (CollUtil.isEmpty(projectList)) {
            return;
        }

        // 处理工单负责人并生成工单
        List<ProjectStakeholderMemberBatchDTO> newMemberBatchList = new ArrayList<>();
        List<CostDeliverTask> deliverTaskList = new ArrayList<>();

        // 获取项目干系人map
        Map<Long, Set<Long>> memberIdsByProjectIdMap = projectStakeholderMemberService.getMemberIdsByProjectIdMap();

        // 获取工单类别信息map
        Map<Integer, CostTaskCategoryManagement> taskCategoryMap = costTaskCategoryManagementService.getCostTaskCategoryMap();

        // 为每个项目生成工单
        for (ProjectInfo project : projectList) {

            if (project == null || project.getId() == null) {
                continue;
            }

            // 获取项目干系人ID集合
            Set<Long> stakeholderIds = memberIdsByProjectIdMap.getOrDefault(project.getId(), new HashSet<>());

            // 处理工单负责人并生成工单
            processAndGenerateTasks(project, defaultTaskConfigList, taskCategoryMap, stakeholderIds, deliverTaskList, newMemberBatchList);
        }

        // 批量添加新的项目成员
        if (CollUtil.isNotEmpty(newMemberBatchList)) {
            // 批量添加新的项目干系人
            projectStakeholderMemberService.batchSave(newMemberBatchList);
        }

        // 批量保存工单
        if (CollUtil.isNotEmpty(deliverTaskList)) {
            int maxSize = 100;
            for (int i = 0; i < deliverTaskList.size(); i += maxSize){
                List<CostDeliverTask> subList = deliverTaskList.subList(i, Math.min(i + maxSize, deliverTaskList.size()));
                costDeliverTaskService.saveBatch(subList);
            }
        }
    }

    /**
     * 处理和生成任务
     *
     * @param project               项目
     * @param defaultTaskConfigList 默认任务配置列表
     */
    private void processAndGenerateTasks(ProjectInfo project,
                                         List<CostPresalesTaskConfig> defaultTaskConfigList,
                                         Map<Integer, CostTaskCategoryManagement> taskCategoryMap,
                                          Set<Long> stakeholderIds,
                                         List<CostDeliverTask> delivertaskList,
                                         List<ProjectStakeholderMemberBatchDTO> newMemberBatchList) {
        // 项目 ID
        Long projectId = project.getId();
        DeliverCostBudgetListVO supportCostBudget;
        //  获取支持成本预算
        try {
            supportCostBudget = costDeliverTaskService.getSupportCostBudget(projectId);
        } catch (Exception e) {
            log.error("{}:获取支持成本预算失败", project.getItemName(), e);
            return;
        }

        // 新的成员名单
        List<ProjectStakeholderMemberDTO> newMemberList = new ArrayList<>();


        // 存储配置ID到生成工单ID的映射关系
        Map<Long, Long> configIdToTaskIdMap = new HashMap<>(0);

        // 存储要同步的工单数据
        List<CostDeliverTask> taskList = new ArrayList<>();

        for (CostPresalesTaskConfig taskConfig : defaultTaskConfigList) {

            // 如果是指定人类型且不在干系人中，则添加到干系人列表
            if (CostPresalesTaskManagerTypeEnum.ZDR.getValue().equals(taskConfig.getManagerType())){

                // 获取指定人ID
                Long managerId = taskConfig.getManagerId();
                if (managerId == null){
                    throw new ServiceException("项目角色为指定人的人员ID不能为空");
                }

                if (CollUtil.isEmpty(stakeholderIds) || !stakeholderIds.contains(managerId)){
                    // 添加项目干系人
                    addProjectStakeholderMemberDTO(taskConfig, newMemberList);

                    // 添加到干系人ID集合
                    stakeholderIds.add(managerId);
                }
            }

            // 创建工单实体并设置属性
            CostDeliverTask task = getCostDeliverTask(taskConfig, project, supportCostBudget, configIdToTaskIdMap, taskCategoryMap);

            // 如果没有负责人，则整个工单都不保存
            if (task.getManagerId() == null){
                return;
            }


            // 添加到配置ID到工单ID的映射关系
            configIdToTaskIdMap.put(taskConfig.getId(), task.getId());

            taskList.add(task);
        }

        // 添加到工单列表
        delivertaskList.addAll(taskList);

        // 批量添加新的项目干系人
        addProjectStakeholderMemberBatchDTO(projectId, newMemberList, newMemberBatchList);

    }



    /**
     * 获取成本交付任务
     *
     * @param config  配置
     * @param project 项目
     * @return {@link CostDeliverTask }
     */
    @NotNull
    private CostDeliverTask getCostDeliverTask(CostPresalesTaskConfig config,
                                               ProjectInfo project,
                                               DeliverCostBudgetListVO supportCostBudget,
                                               Map<Long, Long> configIdToTaskIdMap,
                                               Map<Integer, CostTaskCategoryManagement> taskCategoryMap) {
        CostDeliverTask costDeliverTask = handleManagerData(config, project);
        if (costDeliverTask.getManagerId() == null) {
            return costDeliverTask;
        }

        // 设置父级工单ID
        if (config.getParentId() != null) {
            Long parentTaskId = configIdToTaskIdMap.get(config.getParentId());
            costDeliverTask.setParentId(parentTaskId);
        }

        if (StringUtils.isBlank(costDeliverTask.getManagerName())) {
            costDeliverTask.setManagerName("默认姓名");
        }

        // 生成工单编号
        String taskNo = costDeliverTaskService.generateTaskNo(ProjectTaskKindEnum.PRE_SALES_SUPPORT, taskCategoryMap, config.getTaskCategory(), costDeliverTask.getManagerName());

        return costDeliverTask
                .setProjectId(project.getId())
                .setProjectName(project.getItemName())
                .setTaskNo(taskNo)
                .setTaskName(config.getTaskName())
                .setTaskType(ProjectTaskKindEnum.PRE_SALES_SUPPORT.getValue())
                .setTaskLevel(config.getTaskLevel())
                .setTaskStatus(CostTaskStatusEnum.ZC.getValue())
                .setTaskDesc(config.getTaskDesc())
                .setDisassemblyType(config.getDisassemblyType())
                .setTaskCategory(config.getTaskCategory())
                .setAccountId(supportCostBudget.getAccountId())
                .setTaxRate(supportCostBudget.getTaxRate())
                .setAccountOaId(supportCostBudget.getAccountOaId())
                .setAccountName(supportCostBudget.getAccountName())
                .setDefaultConf(Boolean.TRUE);
    }

    /**
     * 处理项目负责人数据
     *
     * @param config  配置
     * @param project 项目
     * @return {@link CostDeliverTask }
     */
    public static CostDeliverTask handleManagerData(CostPresalesTaskConfig config, ProjectInfo project) {
        CostDeliverTask deliverTask = BaseBuildEntityUtil.buildInsert(new CostDeliverTask());
        Integer managerType = config.getManagerType();
        Integer managerRole = config.getManagerRole();
        // 项目角色
        if (CostPresalesTaskManagerTypeEnum.XMJZ.getValue().equals(managerType)){
            // 项目经理
            if (CostPresalesTaskManagerRoleEnum.XMJL.getValue().equals(managerRole)){
                return deliverTask.setManagerId(project.getManagerUserId())
                        .setManagerName(project.getManagerUserName());
            }
            // 售前经理
            if (CostPresalesTaskManagerRoleEnum.SQJL.getValue().equals(managerRole)){
                return deliverTask.setManagerId(project.getPreSaleUserId())
                        .setManagerName(project.getPreSaleUserName());
            }
            // 客户经理
            if (CostPresalesTaskManagerRoleEnum.KHJL.getValue().equals(managerRole)){
                return deliverTask.setManagerId(project.getSalesmanUserId())
                        .setManagerName(project.getProjectSalesperson());
            }
        }
        // 指定人
        return deliverTask.setManagerId(config.getManagerId())
                .setManagerName(config.getManagerName());
    }
}