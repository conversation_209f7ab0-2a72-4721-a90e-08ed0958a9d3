package com.gok.pboot.pms.entity.domain;


import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.common.base.BeanEntity;
import com.gok.pboot.pms.entity.dto.ProjectStakeholderCustomerDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 项目干系人-客户
 *
 * <AUTHOR>
 * @LocalDateTime 2023-07-11 17:05:26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("project_stakeholder_customer")
public class ProjectStakeholderCustomer extends BeanEntity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 联系人（手填）
     */
    private String contact;

    /**
     * 所在部门名（手填）
     */
    private String department;

    /**
     * 岗位/职位（手填）
     */
    private String position;

    /**
     * 项目影响度（0=高，1=中，2=低）
     *
     * @see com.gok.pboot.pms.enumeration.ProjectImpactDegreeEnum
     */
    private Integer impactDegree;

    /**
     * 职责
     */
    private String duty;

    /**
     * 联系方式
     */
    private String contactPhone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 备注
     */
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private String remark;


    /**
     * 是否满意度调查（0=否,1=是）
     */
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private Integer satisfactionSurvey;

    public static ProjectStakeholderCustomer saveOrUpdate(ProjectStakeholderCustomerDTO request) {
        ProjectStakeholderCustomer result = new ProjectStakeholderCustomer();
        if (request.getId() != null) {
            result.setId(request.getId());
            BaseBuildEntityUtil.buildUpdate(result);
        } else {
            BaseBuildEntityUtil.buildSave(result);
        }
        result.setProjectId(request.getProjectId());
        result.setContact(request.getContact());
        result.setDepartment(request.getDepartment());
        result.setPosition(request.getPosition());
        result.setContactPhone(request.getContactPhone());
        result.setEmail(request.getEmail());
        result.setImpactDegree(request.getImpactDegree());
        result.setDuty(request.getDuty());
        result.setRemark(request.getRemark());
        result.setSatisfactionSurvey(request.getSatisfactionSurvey());
        return result;
    }

}
