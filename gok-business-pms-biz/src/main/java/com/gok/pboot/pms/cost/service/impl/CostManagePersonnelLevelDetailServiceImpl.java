package com.gok.pboot.pms.cost.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.pboot.pms.cost.entity.domain.CostManagePersonnelLevelDetail;
import com.gok.pboot.pms.cost.entity.vo.CostManagePersonnelLevelDetailVO;
import com.gok.pboot.pms.cost.mapper.CostManagePersonnelLevelDetailMapper;
import com.gok.pboot.pms.cost.service.ICostManagePersonnelLevelDetailService;
import com.gok.pboot.pms.service.IEhrService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 成本管理人员级别测算明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Service
public class CostManagePersonnelLevelDetailServiceImpl extends ServiceImpl<CostManagePersonnelLevelDetailMapper, CostManagePersonnelLevelDetail> implements ICostManagePersonnelLevelDetailService {

    private final IEhrService IEhrService;

    public CostManagePersonnelLevelDetailServiceImpl(IEhrService IEhrService) {
        this.IEhrService = IEhrService;
    }

    @Override
    public List<CostManagePersonnelLevelDetailVO> getByEstimationResultsId(Long estimationResultsId) {
        List<CostManagePersonnelLevelDetailVO> detailVOList = new ArrayList<>();
        LambdaQueryWrapper<CostManagePersonnelLevelDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CostManagePersonnelLevelDetail::getEstimationResultsId, estimationResultsId);
        List<CostManagePersonnelLevelDetail> details = list(queryWrapper);
        Map<Long, String> positionGradeNameMap = IEhrService.getPositionGradeNameMap();
        Map<Long, String> jobActivityNameMap = IEhrService.getJobActivityNameMap();
        details.forEach(e -> {
            CostManagePersonnelLevelDetailVO costManagePersonnelLevelDetailVO = new CostManagePersonnelLevelDetailVO();
            BeanUtils.copyProperties(e, costManagePersonnelLevelDetailVO);
            costManagePersonnelLevelDetailVO.setId(e.getId());
            costManagePersonnelLevelDetailVO.setPersonnelLevelName(e.getPersonnelLevel() != null ? positionGradeNameMap.get(e.getPersonnelLevel()) : null);
            costManagePersonnelLevelDetailVO.setPersonnelType(e.getJobActivityId() != null ? jobActivityNameMap.get(e.getJobActivityId()) : null);
            detailVOList.add(costManagePersonnelLevelDetailVO);
        });
        return detailVOList;
    }

}
