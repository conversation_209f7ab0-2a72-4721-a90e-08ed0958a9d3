package com.gok.pboot.pms.didi.controller;

import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.didi.service.DidiSyncService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;

/**
 * 滴滴项目同步控制器
 *
 * <AUTHOR>
 * @date 2025/01/27
 */
@Slf4j
@RestController
@RequestMapping("/didi")
@Api(tags = "滴滴项目同步")
@AllArgsConstructor
public class DidiSyncController {

    private final DidiSyncService didiSyncService;


    /**
     * 批量同步项目
     *
     * @param dateParam   日期参数
     * @param withManager 与经理
     * @return {@link ApiResult }<{@link String }>
     */
    @PostMapping("/project/batch-sync")
    @ApiOperation("批量同步项目信息至滴滴")
    public ApiResult<String> batchSyncProjects(@RequestParam(required = false) LocalDate dateParam,
                                               @RequestParam(required = false,defaultValue = "true") Boolean withManager) {
        didiSyncService.batchSyncProjects(dateParam, withManager);
        return ApiResult.successMsg("批量同步项目信息成功");
    }

    /**
     * 批量同步用户
     *
     * @return {@link ApiResult }<{@link String }>
     */
    @PostMapping("/batch-sync")
    @ApiOperation("批量同步用户信息至滴滴")
    public ApiResult<String> batchSyncUsers(@RequestParam(required = false) LocalDate dateParam) {
        didiSyncService.batchSyncUsers(dateParam);
        return ApiResult.successMsg("批量同步用户信息成功");
    }

} 