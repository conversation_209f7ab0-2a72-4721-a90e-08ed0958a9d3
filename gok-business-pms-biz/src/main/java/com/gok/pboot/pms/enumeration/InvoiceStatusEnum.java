package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 发票状态枚举
 *
 * <AUTHOR>
 * @date 13/12/2023
 */
@Getter
@AllArgsConstructor
public enum InvoiceStatusEnum implements ValueEnum<Integer> {

    /**
     * 正常
     */
    NORMAL(0, "正常"),

    /**
     * 已作废
     */
    INVALIDATED(1, "已作废");

    private final Integer value;

    private final String name;
}
