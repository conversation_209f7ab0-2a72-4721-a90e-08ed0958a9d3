package com.gok.pboot.pms.service;

import com.gok.pboot.pms.common.base.ApiResult;

import java.time.LocalDate;
import java.util.Set;


/**
 * <AUTHOR>
 * @desc
 * @createTime 2023/2/22 10:09
 */
public interface IHolidayService {

    /**
     * 更新年度非工作日数据（普通休息日+法定节假日）
     *
     * @param year 年份
     * @return {@link ApiResult}
     */
    ApiResult<String> updateHoliday(Integer year);
    /**
     * 根据日期返回节假日类型
     *
     * @param date 日期
     * @return {@link ApiResult}
     */
    ApiResult<Integer> getHolidayTypeByDate(LocalDate date);

    /**
     * 获取日期范围内的工作日天数
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 工作日天数
     */
    Integer getRequiredAttendanceDays(LocalDate startDate, LocalDate endDate);
}
