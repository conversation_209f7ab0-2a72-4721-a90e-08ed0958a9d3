package com.gok.pboot.pms.enumeration;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;

/**
 * 日志内容枚举
 *
 * <AUTHOR>
 * @version 2.3.1
 */
@AllArgsConstructor
public enum LogContentEnum implements ValueEnum<String> {

    EXPORT_DATA("导出数据", "导出数据【{}】"),
    IMPORT_DATA("导入数据", "导入数据【{}】"),
    NEW_COLLECTION("新增回款", "新增回款【单据编号】"),
    EDIT_PAYMENT_COLLECTION("编辑回款", "编辑回款【单据编号】"),
    SUMMARY_OF_PAYMENTS_RECEIVED("汇总回款", "汇总回款【单据编号】N【单据编号】至项目回款【单据编号】"),
    CANCEL_SUMMARY_COLLECTION("取消汇总回款", "项目回款【单据编号】取消汇总回款【单据编号】N【单据编号】"),
    CLAIM_AND_COLLECTION("认领回款", "【单据编号】【认领人】认领回款成功"),
    CANCEL_CLAIM_AND_PAYMENT_COLLECTION("取消认领回款", "【单据编号］】取消认领回款"),
    LOCK_COLLECTION_TRACKING_EDU_TABLE("锁定回款跟踪表", "锁定教育回款跟踪表【单据编号】N【单据编号】"),
    YUXIAO_LOCK_COLLECTION_TRACKING_EDU_TABLE("驭消锁定回款跟踪表", "取消锁定教育回款跟踪表【单据编号】N【单据编号】"),
    DELETE_PAYMENT_EDU_COLLECTION("删除回款", "删除锁定教育回款跟踪表【单据编号】N【单据编号】"),
    SUBMIT_EXCEPTION_REMINDER("提交异常提醒", "触发异常提醒【{}】条"),
    PENDING_REVIEW_REMINDER("待审核提醒", "触发待审核提醒【{}】条"),
    EXPORT_ABNORMAL_DAILY_REPORT("导出异常日报", "导出【{}】异常日报"),
    DELETE_RISK("删除风险", "删除风险【{}】"),
    EDITING_RISKS("编辑风险", "编辑风险【{}】"),
    NEW_RISKS("新增风险", "新增风险【{}】"),
    DELETE_WEEKLY_REPORT("删除周报", "删除【{}】【{}】周报"),
    EDIT_WEEKLY_REPORT("编辑周报", "编辑【{}】【{}】周报"),
    EXPORT_MEETING_MINUTES("导出会议纪要", "导出【{}】条会议纪要"),
    IMPORT_MEETING_MINUTES("导入会议纪要", "导入【{}】条会议纪要"),
    NEW_MEETING_MINUTES("新建会议纪要", "新建【{}】【{}】会议纪要"),
    EDIT_MEETING_MINUTES("编辑会议纪要", "编辑【{}】【{}】会议纪要"),
    DELETE_CUSTOMER_STAKEHOLDERS("删除客户干系人", "删除【{}】"),
    EDIT_CUSTOMER_STAKEHOLDERS("编辑客户干系人", "编辑【{}】"),
    ADD_NEW_CUSTOMER_STAKEHOLDERS("新增客户干系人", "新增【{}】"),
    EDIT_NOTES("编辑备注", "编辑【{}】备注"),
    DELETE_STAKEHOLDERS("删除干系人", "删除【{}】"),
    MODIFY_ROLE("修改角色", "【{}】角色修改为【{}】"),
    SYNCHRONIZE_TO_OA("同步至OA", "同步成功【{}】，同步失败【{}】"),
    NEWLY_ADDED_WORK_ASSISTANT("新增搡作助理", "新增【{}】"),
    ADD_PROJECT_MEMBERS("新增项目成员", "新增【{}】"),
    DELETE_TASK("删除任务", "删除【{}】"),
    END_TASK("结束任务", "结束【{}】"),
    EDITING_TASKS("编辑任务", "{}负责人【{}】"),
    EDITING_PARTICIPANTS("编辑參与人", "{}參与人【{}】"),
    NEW_TASK("新建任务", "新建任务【{}】"),
    EXPORT_CUSTOMER_COMMUNICATION_RECORDS("导出", "导出【{}】条客户沟通记录"),
    EXPORT_BUSINESS_OPPORTUNITY_PROGRESS_RECORDS("导出", "导出【{}】条商机进展记录"),
    EXPORT_BUSINESS_OPPORTUNITIES("导出", "导出【{}】条商机"),
    EXPORT_WEEKLY_REPORT("导出周报", "导出【{}】条周报"),
    IMPORT_WEEKLY_REPORT("导入周报", "导入【{}】条周报"),
    NEW_WEEKLY_REPORT("新建周报", "新建【{}】【{}】周报"),
    REUSE_WORKING_HOURS_REVIEW("复用工时审核", "【{}】【{}】【{}】个人，汇总工时为【{}】人天"),
    BATCH_REVIEW("批量审核", "批量通过【{}】条工时，共【{}】"),
    //【通过/不通过】【任务名称】【提交人】【填报日期】
    WORK_HOUR_REVIEW("工时审核", "【{}】【{}】【{}】【{}】"),
    IMPORT("导入", "导入【{}】，数据【{}】"),
    //保存【填报对应日期】日报
    SAVE_DAILY_REPORT("保存日报", "保存【{}】日报"),
    SUBMIT_DAILY_REPORT("提交日报", "提交【{}】日报"),
    //下载【合同编号】附件
    DOWNLOAD("附件下载", "下载【{}】附件"),
    SYNCHRONIZE_DATA("同步数据", "同步数据"),
    LOCK_COLLECTION_TRACKING_TABLE("锁定回款跟踪表", "锁定回款跟踪表【单据编号】N【单据编号】"),
    UNLOCK_PAYMENT_TRACKING_TABLE("取消锁定回款跟踪表", "取消锁定回款跟踪表【单据编号】N【单据编号】"),
    PUSH_VOUCHER("推送凭证", "推送凭证【单据编号】【推送状态】N【单据编号】【推送状态】"),
    DELETE_PAYMENT_COLLECTION("删除回款", "删除回款【单据编号】N【单据编号】"),
    OPEN_THE_BUSINESS_OPPORTUNITY_REPORT("打开商机报备", "【{}】在【{}】点击商机报备"),
    OPEN_CUSTOMER_HANDOVER("打开客户移交", "【{}】在【{}】点击客户移交"),
    OPEN_CUSTOMER_COMMUNICATION_RECORDS("打开客户沟通记录", "【{}】在【{}】点击客户沟通记录"),
    OPEN_CUSTOMER_REPORT("打开客户报备", "【{}】点击客户报备"),
    CANCEL_FOLLOWING_CUSTOMERS("取消关注客户", "【{}】取消关注了客户【{}】"),
    PAY_ATTENTION_TO_CUSTOMERS("关注客户", "【{}】关注了客户【{}】"),
    //客户经营单元
    CREATE_CUSTOMER_BUSINESS("创建经营单元", "【{}】创建经营单元【{}】"),
    UPDATE_CUSTOMER_BUSINESS("编辑经营单元", "【{}】编辑经营单元【{}】"),
    UPDATE_CUSTOMER_BUSINESS_INFO("编辑经营单元基础信息", "【{}】编辑经营单元【{}】的基础信息"),
    UPDATE_CUSTOMER_BUSINESS_UNIT("编辑所属客户", "【{}】编辑所属客户【{}】"),
    CREATE_CUSTOMER_BUSINESS_UNIT("创建所属客户", "【{}】创建所属客户【{}】"),
    DELETE_CUSTOMER_BUSINESS_UNIT("删除所属客户", "【{}】删除所属客户【{}】");
    /**
     * 日志名称
     */
    private final String name;

    /**
     * 日志内容摘要
     */
    private final String value;

    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getName() {
        return name;
    }

    public String formatValue(Object... args) {
        if (args == null || args.length == 0) {
            return value;
        }
        return StrUtil.format(value, args);
    }
}
