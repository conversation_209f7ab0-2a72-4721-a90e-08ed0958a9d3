package com.gok.pboot.pms.entity.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;

/**
 * PMS日报审核
 *
 * @Auther chenhc
 * @Date 2022-08-24 11:24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class DailyReviewFindPageDTO {

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 日期开始时间
     */
    private LocalDate startTime;

    /**
     * 日期结束时间
     */
    private LocalDate endTime;

    /**
     * 审核状态（0：未审核、1：已审核）
     */
    @NotNull(message = "审核状态不能为空！")
    private Integer status;

    /**
     * 权限项目编号集合
     */
    private List<Long> projectIds;

    /**
     * 用户ID列表
     */
    private List<Long> userIds;
    /**
     * 排除普通项目+特殊项目
     */
    private Collection<Long> notProjectIds;
    /**
     * 是否查询归档数据（0：不查询、1：查询）
     */
    private Integer filing;
}
