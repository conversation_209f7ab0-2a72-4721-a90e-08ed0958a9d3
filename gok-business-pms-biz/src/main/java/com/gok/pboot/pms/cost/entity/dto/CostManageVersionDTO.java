package com.gok.pboot.pms.cost.entity.dto;

import com.gok.pboot.pms.enumeration.CostManageStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 成本管理版本记录DTO类
 *
 * <AUTHOR>
 * @create 2025/04/14
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CostManageVersionDTO {

    /**
     * 版本ID
     */
    private Long id;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 预算类型集合
     * {@link com.gok.pboot.pms.cost.enums.CostBudgetTypeEnum}
     */
    private List<Integer> costBudgetTypeList;

    /**
     * 版本类型
     * {@link com.gok.pboot.pms.cost.enums.CostManageVersionEnum}
     */
    private Integer versionType;

    /**
     * 版本状态
     * {@link CostManageStatusEnum}
     */
    private Integer status;

    /**
     * 发起OA表单类型
     * {@link com.gok.pboot.pms.enumeration.OAFormTypeEnum}
     */
    private Integer formType;

    /**
     * 同步创建项目总成本标记
     * 0-已同步 1-未同步
     */
    private Integer generateTotalCost;

}
