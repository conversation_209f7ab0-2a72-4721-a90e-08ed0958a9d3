package com.gok.pboot.pms.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.entity.dto.ConfirmHourSumFindPageDTO;
import com.gok.pboot.pms.entity.vo.ConfirmHourSumFindPageVO;

import javax.servlet.http.HttpServletResponse;

/**
 * <p>
 * 工时确认 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-30
 */
public interface IConfirmService {

    /**
     * 添加
     *
     * @param confirmHourSumFindPageDTO
     * @return ApiResult
     */
    ApiResult<String> save(ConfirmHourSumFindPageDTO confirmHourSumFindPageDTO);

    /**
     * 分页查询
     *
     * @param confirmHourSumFindPageDTO
     * @return 分页记录
     */
    Page<ConfirmHourSumFindPageVO> confirmHourSumFindPageVO(ConfirmHourSumFindPageDTO confirmHourSumFindPageDTO);


    Boolean findConfirm(ConfirmHourSumFindPageDTO confirmHourSumFindPageDTO);

    void confirmHourSumExport(HttpServletResponse response, ConfirmHourSumFindPageDTO confirmHourSumFindPageDTO);
}
