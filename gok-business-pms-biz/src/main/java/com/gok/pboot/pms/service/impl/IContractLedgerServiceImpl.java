package com.gok.pboot.pms.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.pboot.common.core.exception.BusinessException;
import com.gok.pboot.pms.Util.*;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.common.constant.DictConstants;
import com.gok.pboot.pms.common.constant.FunctionConstants;
import com.gok.pboot.pms.entity.domain.*;
import com.gok.pboot.pms.entity.dto.ContractLedgerListDTO;
import com.gok.pboot.pms.entity.dto.ContractLedgerSelectListDTO;
import com.gok.pboot.pms.entity.vo.*;
import com.gok.pboot.pms.enumeration.*;
import com.gok.pboot.pms.mapper.*;
import com.gok.pboot.pms.oa.OaUtil;
import com.gok.pboot.pms.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;


@Slf4j
@Service
@RequiredArgsConstructor
public class IContractLedgerServiceImpl extends ServiceImpl<ContractLedgerMapper, ContractLedger> implements IContractLedgerService {

    public final static String PROJECT_DICT_CACHE = "project:dict";

    private final DbApiUtil dbApiUtil;

    private final ContractLedgerDetailMapper detailMapper;

    private final ContractMilestoneMapper milestoneMapper;

    private final PmsContractProcessInfoMapper processInfoMapper;

    private final ProjectInfoMapper projectInfoMapper;

    private final ProjectContractInvoiceMapper projectContractInvoiceMapper;

    private final StringRedisTemplate stringRedisTemplate;

    private final IPmsDocImageFileService pmsDocImageFileService;

    private final IContractMilestoneService contractMilestoneService;

    private final IProjectContractAmendmentService projectContractAmendmentService;

    private final OaUtil oaUtil;

    private final BcpLoggerUtils bcpLoggerUtils;

    @Autowired
    @Lazy
    private IContractLedgerDetailService contractLedgerDetailService;
    @Autowired
    @Lazy
    private IProjectContractInvoiceService invoiceService;


    @Override
    public ApiResult<ContractBaseInfoVo> getContractBaseInfoVoList(Long id) {
        ContractLedger contractLedger = baseMapper.selectById(id);
        if (!Optional.ofNullable(contractLedger).isPresent()) {
            ApiResult<ContractBaseInfoVo> apiResult = new ApiResult<>();
            apiResult.setCode(4001);
            return apiResult;
        }
        ContractBaseInfoVo contractBaseInfoVo = new ContractBaseInfoVo();
        BeanUtil.copyProperties(contractLedger, contractBaseInfoVo);
        //字典转换
        contractBaseInfoVo.setJsfsTxt(EnumUtils.getNameByValue(ContractSettlementMethodEnum.class, contractBaseInfoVo.getJsfs()));
        contractBaseInfoVo.setYwbkTxt(EnumUtils.getNameByValue(ContractBusinessUnitEnum.class, contractBaseInfoVo.getYwbk()));
        contractBaseInfoVo.setSrlxTxt(EnumUtils.getNameByValue(ContractIncomeTypeEnum.class, contractBaseInfoVo.getSrlx()));
        contractBaseInfoVo.setJslxTxt(EnumUtils.getNameByValue(ContractTechnologyTypeEnum.class, contractBaseInfoVo.getJslx()));
        contractBaseInfoVo.setJfxsTxt(EnumUtils.getNameByValue(ContractDeliveryFormEnum.class, contractBaseInfoVo.getJfxs()));
        contractBaseInfoVo.setXmszd(contractLedger.getXmszdName());
        contractBaseInfoVo.setSfsjyjcgTxt(EnumUtils.getNameByValue(ContractYesOrNoEnum.class, contractBaseInfoVo.getSfsjyjcg()));
        //合同会签附件
        QueryWrapper<PmsContractProcessInfo> processInfoQueryWrapper = new QueryWrapper<>();
        processInfoQueryWrapper.lambda().eq(PmsContractProcessInfo::getContractId, contractLedger.getId())
                .eq(PmsContractProcessInfo::getProcessType, 3);
        List<PmsContractProcessInfo> pmsContractProcessInfos = processInfoMapper.selectList(processInfoQueryWrapper);
        if (CollUtil.isNotEmpty(pmsContractProcessInfos)) {
            PmsContractProcessInfo pmsContractProcessInfo = pmsContractProcessInfos.get(0);
            List<OaFileVo> resourcesData = oaUtil.getResourcesData(String.valueOf(pmsContractProcessInfo.getRequestId()),
                    String.valueOf(pmsContractProcessInfo.getCreater()));
            if (Optional.ofNullable(pmsContractProcessInfo.getHtfj()).isPresent()) {
                OaFileInfoVo htfjOaFileInfoVo = new OaFileInfoVo();
                htfjOaFileInfoVo.setRequestId(String.valueOf(pmsContractProcessInfo.getRequestId()));
                htfjOaFileInfoVo.setFileId(pmsContractProcessInfo.getHtfj());
                contractBaseInfoVo.setHtfjList(pmsDocImageFileService.getOaOaFileInfoList(htfjOaFileInfoVo
                        , resourcesData));
            }

            if (Optional.ofNullable(pmsContractProcessInfo.getHtfjygz()).isPresent()) {
                OaFileInfoVo htfjygzOaFileInfoVo = new OaFileInfoVo();
                htfjygzOaFileInfoVo.setRequestId(String.valueOf(pmsContractProcessInfo.getRequestId()));
                htfjygzOaFileInfoVo.setFileId(pmsContractProcessInfo.getHtfjygz());
                contractBaseInfoVo.setHtfjygzList(pmsDocImageFileService.getOaOaFileInfoList(htfjygzOaFileInfoVo
                        , resourcesData));
            }

        }
        //变更记录
        List<ContractChangeInfoVo> contractChangeInfoVoList = projectContractAmendmentService.getContractChangeInfoVoList(id);
        contractBaseInfoVo.setContractChangeInfoVoList(contractChangeInfoVoList);
        //是否采购合同
        if (Optional.ofNullable(contractLedger.getHtxl()).isPresent() && contractLedger.getHtxl() == 0) {
            if (Optional.ofNullable(contractLedger.getXmsbqsdfj()).isPresent()) {
                List<OaFileVo> resourcesData = oaUtil.getResourcesData(String.valueOf(contractLedger.getXzxmcgsqlc()),
                        String.valueOf(contractLedger.getXzxmcgsqlcNodeoperator()));

                OaFileInfoVo xmsbqsdfjOaFileInfoVo = new OaFileInfoVo();
                xmsbqsdfjOaFileInfoVo.setRequestId(String.valueOf(contractLedger.getXzxmcgsqlc()));
                xmsbqsdfjOaFileInfoVo.setFileId(contractLedger.getXmsbqsdfj());
                xmsbqsdfjOaFileInfoVo.setImageFileId(Optional.ofNullable(contractLedger.getXmsbqsdfjImagefileid()).isPresent() ?
                        contractLedger.getXmsbqsdfjImagefileid().toString() : null);
                contractBaseInfoVo.setXmsbqsdfjList(pmsDocImageFileService.getOaOaFileInfoList(xmsbqsdfjOaFileInfoVo
                        , resourcesData));
            }
        }
        //字段保留，不取值
        contractBaseInfoVo.setApprovalProcess(StrUtil.EMPTY);
        contractBaseInfoVo.setZbtzs(StrUtil.EMPTY);
        contractBaseInfoVo.setRelateprocess(StrUtil.EMPTY);
        contractBaseInfoVo.setZbtzsfjList(null);
        contractBaseInfoVo.setRelateattachmentList(null);
        contractBaseInfoVo.setXmmlcslc(contractBaseInfoVo.getXmmlcslcName());
        //对方名称-客户/供应商
        if (Optional.ofNullable(contractLedger.getHtxl()).isPresent() && contractLedger.getHtxl() == 0) {
            contractBaseInfoVo.setKhmcName(contractLedger.getGysmcsName());
            contractBaseInfoVo.setKhbh(contractLedger.getGysbh());
            contractBaseInfoVo.setLxr(contractLedger.getGyslxr1());
            contractBaseInfoVo.setLxfs(contractLedger.getGysdh1());
            contractBaseInfoVo.setLxdz(contractLedger.getGysbgdzkyjdz());
            contractBaseInfoVo.setKhfkhyx(contractLedger.getGyskpkhyx());
            contractBaseInfoVo.setKhfzh(contractLedger.getGyskpkhzh());
        } else {
            contractBaseInfoVo.setKhmcName(contractLedger.getKhmcnewName());
            contractBaseInfoVo.setKhbh(contractLedger.getKhbh());
            contractBaseInfoVo.setLxr(contractLedger.getKhlxr1());
            contractBaseInfoVo.setLxfs(contractLedger.getKhlxdh1());
            contractBaseInfoVo.setLxdz(contractLedger.getKhkpdz());
            contractBaseInfoVo.setKhfkhyx(contractLedger.getKhkpkhyx());
            contractBaseInfoVo.setKhfzh(contractLedger.getKhkpkhxzh());

        }
        contractBaseInfoVo.setHtjebhs(DecimalFormatUtil
                .setThousandthAndTwoDecimal(contractLedger.getHtjebhs(), DecimalFormatUtil.ZERO));
        contractBaseInfoVo.setXmyjml(DecimalFormatUtil
                .setThousandthAndTwoDecimal(contractLedger.getXmyjml(), DecimalFormatUtil.NULL));
        contractBaseInfoVo.setXmyjmll(DecimalFormatUtil
                .setThousandthAndTwoDecimal(contractLedger.getXmyjmll(), DecimalFormatUtil.NULL));
        return ApiResult.success(contractBaseInfoVo);

    }


    @Override
    public Page<ContractLedgerListVo> getContractLedgerVoList(ContractLedgerListDTO dto) {
        if (dto.getIfExport() && CollUtil.isNotEmpty(dto.getIds())) {
            ContractLedgerListDTO contractLedgerListDTO = new ContractLedgerListDTO();
            contractLedgerListDTO.setIds(dto.getIds());
            contractLedgerListDTO.setPageNumber(dto.getPageNumber());
            contractLedgerListDTO.setPageSize(dto.getPageSize());
            contractLedgerListDTO.setIfExport(true);
            dto = contractLedgerListDTO;
        }
        PageRequest pageRequest = new PageRequest(dto.getPageNumber(), dto.getPageSize());
        StopWatch stopWatch = StopWatch.createStarted();

        StopWatch stopWatch2 = StopWatch.createStarted();

        List<ContractListVo> contractList = baseMapper.selList(dto);
        contractList = CollUtil.page(dto.getPageNumber() - 1, dto.getPageSize(), contractList);
        List<ContractListVo> contractListCount = baseMapper.selListCount(dto);
        stopWatch2.stop();
        log.info("列表主要信息查询分页 took " + stopWatch2.getTime() + " milliseconds.");

        if (CollUtil.isEmpty(contractList)) {
            return PageUtils.page(new ArrayList<>(), pageRequest);
        }

        List<ContractLedgerListVo> collect = BeanUtil.copyToList(contractList, ContractLedgerListVo.class);
        Page<ContractLedgerListVo> page = PageUtils.page(new ArrayList<>(), pageRequest);
        page.setRecords(collect);
        page.setSize(dto.getPageSize());
        page.setCurrent(dto.getPageNumber());
        page.setTotal(contractListCount.size());
        //OA字典
        Set<Integer> fieldIds = CollUtil.newHashSet(
                Integer.valueOf(DictConstants.CONTRACT_DETAILS_ID),
                Integer.valueOf(DictConstants.CONTRACT_TYPE_ID),
                Integer.valueOf(DictConstants.INCOME_TYPE),
                Integer.valueOf(DictConstants.CONTRACT_COMPANY_ID),
                Integer.valueOf(DictConstants.BUSINESS_BLOCK_ID),
                Integer.valueOf(DictConstants.SETTLEMENT_METHOD_ID),
                Integer.valueOf(DictConstants.SKILL_TYPE_ID),
                Integer.valueOf(DictConstants.COMPLETION_STARTING_ID)
        );
        Map<Integer, Map<Integer, String>> projectDictMap = getProjectDictMap(fieldIds);
        if (CollUtil.isEmpty(projectDictMap)) {
            log.info("查询Oa字典失败");
            throw new BusinessException("查询Oa字典失败");
        }
        Map<Long, ContractListVo> contractListVoMap =
                contractList.stream().collect(Collectors.toMap(ContractListVo::getId, Function.identity()));

        Integer xh = 1;
        if (CollUtil.isNotEmpty(contractList)) {
            //显示页额id
            List<Long> contractIds = contractList.stream().map(c -> c.getId()).collect(Collectors.toList());

            //合同风险
            Map<Long, List<ContractRiskInfoVO>> contractRiskInfoVoMap = this.getContractRiskInfoVo(contractIds);

            //dbapi合同发票
            Map<Long, List<ContractInvoiceVo>> contractInvoiceVoListMap
                    = invoiceService.getContractInvoiceVoListMap(contractIds);

            //合同收款明细
            StopWatch stopWatch5 = StopWatch.createStarted();
            Map<Long, List<ContractAcceptanceVo>> contractAcceptanceVoListMap = this.getContractAcceptanceVoListMap(contractIds);
            List<Long> xmIds = contractList.stream().map(c -> c.getXmmc()).collect(Collectors.toList());
            List<ProjectInfo> projectInfos = projectInfoMapper.selectBatchIds(xmIds);
            Map<Long, ProjectInfo> projectInfoMap = projectInfos.stream()
                    .collect(Collectors.toMap(ProjectInfo::getId, Function.identity()));
            stopWatch5.stop();
            log.info("合同收款明细 dbapi took " + stopWatch5.getTime() + " milliseconds.");

            StopWatch stopWatch6 = StopWatch.createStarted();
            Map<Integer, String> htxlMap = projectDictMap.get(Integer.valueOf(DictConstants.CONTRACT_DETAILS_ID));
            Map<Integer, String> htlbMap = projectDictMap.get(Integer.valueOf(DictConstants.CONTRACT_TYPE_ID));
            Map<Integer, String> srlxMap = projectDictMap.get(Integer.valueOf(DictConstants.INCOME_TYPE));
            Map<Integer, String> htssgsMap = projectDictMap.get(Integer.valueOf(DictConstants.CONTRACT_COMPANY_ID));
            Map<Integer, String> ywbkMap = projectDictMap.get(Integer.valueOf(DictConstants.BUSINESS_BLOCK_ID));
            Map<Integer, String> jsfsMap = projectDictMap.get(Integer.valueOf(DictConstants.SETTLEMENT_METHOD_ID));
            Map<Integer, String> jslxMap = projectDictMap.get(Integer.valueOf(DictConstants.SKILL_TYPE_ID));
            Map<Integer, String> jxqdtjMap = projectDictMap.get(Integer.valueOf(DictConstants.COMPLETION_STARTING_ID));
            for (ContractLedgerListVo contract :
                    collect) {
                ContractListVo contractVo = contractListVoMap.get(contract.getId());
                contract.setXh(xh);
                xh++;
                contract.setHtztText(EnumUtils.getNameByValue(ContractStatusEnum.class, contract.getHtzt()));

                //合同细类
                contract.setHtxlText(htxlMap.getOrDefault(contractVo.getHtxl(), ""));

                //合同类别
                contract.setHtlbText(htlbMap.getOrDefault(contractVo.getHtlb(), ""));
                //收入类型

                contract.setSrlxText(srlxMap.getOrDefault(contractVo.getSrlx(), ""));
                //合同所属公司

                contract.setHtssgsText(htssgsMap.getOrDefault(contractVo.getHtssgs(), ""));
                //业务板块

                contract.setYwbkText(ywbkMap.getOrDefault(contractVo.getYwbk(), ""));
                //结算方式

                contract.setJsfsText(jsfsMap.getOrDefault(contractVo.getJsfs(), ""));
                //技术类型

                contract.setJslxText(jslxMap.getOrDefault(contractVo.getJslx(), ""));
                //结项启动条件

                contract.setJxqdtjText(jxqdtjMap.getOrDefault(contractVo.getJxqdtj(), ""));

                contract.setHtkxjd((StrUtil.isNotBlank(contractVo.getYskbl()) ? contractVo.getYskbl() : "0.00") + "%");
                contract.setHtjehs(MoneyUtil.getInstance().transType(Optional.ofNullable(contractVo.getHtjehs()).isPresent() ? contractVo.getHtjehs() : null));
                contract.setHtjebhs(MoneyUtil.getInstance().transType(Optional.ofNullable(contractVo.getHtjebhs()).isPresent() ? contractVo.getHtjebhs() : null));

                //项目状态
                ProjectInfo projectInfo = projectInfoMap.get(contract.getXmmc());
                if (Optional.ofNullable(projectInfo).isPresent()) {
                    contract.setProjectStatus(projectInfo.getProjectStatus());
                    contract.setProjectStatusName(StrUtil.isBlank(contract.getProjectStatus()) ? StringUtils.EMPTY :
                            ProjectStatusEnum.getNameByStrVal(contract.getProjectStatus()));
                }

                //已收款金额，代收款金额
                BigDecimal yskje = new BigDecimal(StrUtil.isNotBlank(contractVo.getYskje()) ? contractVo.getYskje() : "0");
                contractVo.setHtjehs(contractVo.getHtjehs() == null ? BigDecimal.ZERO : contractVo.getHtjehs());
                BigDecimal dskje = contractVo.getHtjehs().subtract(yskje);
                contract.setYskje(DecimalFormatUtil.setThousandthAndTwoDecimal(yskje, DecimalFormatUtil.ZERO));
                contract.setDskje(DecimalFormatUtil.setThousandthAndTwoDecimal(dskje, DecimalFormatUtil.ZERO));
                //合同风险数目
                Integer riskProjectNum = 0;
                List<ContractRiskInfoVO> contractRiskInfoVoList = contractRiskInfoVoMap.get(contractVo.getId());
                if (CollUtil.isNotEmpty(contractRiskInfoVoList)) {
                    for (ContractRiskInfoVO cr :
                            contractRiskInfoVoList) {
                        if (StrUtil.isNotBlank(cr.getRiskMark()) && !StrUtil.equals(cr.getRiskMark(), "2")) {
                            riskProjectNum++;
                        }
                    }
                }

                contract.setRiskProjectNum(riskProjectNum);
                // 累计已开票金额  累计已开票金额
                BigDecimal ykpje = BigDecimal.ZERO;
                List<ContractInvoiceVo> invoiceList = contractInvoiceVoListMap.get(contractVo.getId());
                ykpje = invoiceList.stream()
                        .filter(c -> NumberUtils.INTEGER_ZERO.equals(c.getFpzt())
                                && Optional.ofNullable(c.getFpje()).isPresent())
                        .map(e -> e.getFpje())
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                contract.setYkpje(DecimalFormatUtil.setThousandthAndTwoDecimal(ykpje, DecimalFormatUtil.ZERO));
                contract.setDkpje(DecimalFormatUtil.setThousandthAndTwoDecimal(contractVo.getHtjehs().subtract(ykpje), DecimalFormatUtil.ZERO));

                //验收金额
                BigDecimal ljyysje = BigDecimal.ZERO;
                List<ContractAcceptanceVo> contractAcceptanceVoList
                        = contractAcceptanceVoListMap.get(contract.getId());
                if (CollUtil.isNotEmpty(contractAcceptanceVoList)) {
                    for (ContractAcceptanceVo c :
                            contractAcceptanceVoList) {
                        ljyysje = ljyysje.add(new BigDecimal(StrUtil.isNotBlank(c.getBcjxqrsrjebhs()) ?
                                c.getBcjxqrsrjebhs().replace(",", "") : "0"));
                    }
                }
                contract.setLjyysje(DecimalFormatUtil.setThousandthAndTwoDecimal(ljyysje, DecimalFormatUtil.ZERO));
                contract.setDysje(DecimalFormatUtil.setThousandthAndTwoDecimal(contractVo.getHtjehs().subtract(ljyysje), DecimalFormatUtil.ZERO));
            }
            if (dto.getIfExport()) {
                //导出日志
                bcpLoggerUtils.log(FunctionConstants.CONTRACT_LEDGER, LogContentEnum.EXPORT_DATA, collect.size());
            }
            stopWatch6.stop();
            log.info("列表转换 took " + stopWatch6.getTime() + " milliseconds.");
        }
        stopWatch.stop();
        log.info("列表查询 took " + stopWatch.getTime() + " milliseconds.");
        return page;

    }


    @Override
    public Map<Integer, Map<Integer, String>> getProjectDictMap(Set<Integer> fieldIdSet) {
        List<Integer> fieldIds = CollUtil.isNotEmpty(fieldIdSet) ? new ArrayList<>(fieldIdSet) : new ArrayList<>();
        try {
            // 如果Redis中存在缓存
            if (stringRedisTemplate.hasKey(PROJECT_DICT_CACHE)) {
                return getDictMapByCache(fieldIds);
            }
        } catch (Exception e) {
            log.error("OA字典缓存获取异常", e);
            stringRedisTemplate.delete(PROJECT_DICT_CACHE);
        }
        // 如果Redis中没有缓存，则从数据库中获取数据
        List<ProjectDictVo> projectDictVoList = dbApiUtil.projectDict(null);
        // 如果数据库中数据为空，则返回空集合，不进行缓存
        if (CollUtil.isEmpty(projectDictVoList)) {
            return Collections.emptyMap();
        }
        Map<Integer, Map<Integer, String>> projectDictMap = new HashMap<>(16);
        // 将数据按fieldId分组
        projectDictVoList.forEach(vo -> {
            Map<Integer, String> selectMap = projectDictMap.getOrDefault(vo.getFieldid(), new HashMap<>(16));
            selectMap.put(vo.getSelectvalue(), vo.getSelectname());
            projectDictMap.put(vo.getFieldid(), selectMap);
        });

        // 将数据写入Redis
        setDictCache(projectDictMap);

        // 如果指定了fieldIds，则只返回这些fieldId的数据
        if (CollUtil.isNotEmpty(fieldIdSet)) {
            return projectDictMap.entrySet().stream()
                    .filter(entry -> fieldIdSet.contains(entry.getKey()))
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        }
        return projectDictMap;
    }




    private void setDictCache(Map<Integer, Map<Integer, String>> projectDictMap) {
        try {
            // 将数据写入Redis Hash
            Map<String, String> hashData = new HashMap<>(16);
            projectDictMap.forEach((fieldId, selectMap) ->
                    hashData.put(String.valueOf(fieldId), JSON.toJSONString(selectMap)));
            stringRedisTemplate.opsForHash().putAll(PROJECT_DICT_CACHE, hashData);
            stringRedisTemplate.expire(PROJECT_DICT_CACHE, 1, TimeUnit.DAYS);
        } catch (Exception e) {
            log.error("字典写入缓存异常", e);
        }
    }

    @NotNull
    private Map<Integer, Map<Integer, String>> getDictMapByCache(List<Integer> fieldIds) {
        Map<Integer, Map<Integer, String>> projectDictMap = new HashMap<>(16);
        // 如果指定了fieldIds，则只获取这些fieldId的数据
        if (CollUtil.isNotEmpty(fieldIds)) {
            List<Object> fieldIdKeys = fieldIds.stream().map(Object::toString).collect(Collectors.toList());
            List<Object> entries = stringRedisTemplate.opsForHash().multiGet(PROJECT_DICT_CACHE, fieldIdKeys);
            for (int i = 0; i < fieldIdKeys.size(); i++) {
                String value = (String) entries.get(i);
                if (StrUtil.isNotBlank(value)) {
                    Map<Integer, String> selectMap = JSON.parseObject(value, new TypeReference<Map<Integer, String>>() {
                    });
                    projectDictMap.put(fieldIds.get(i), selectMap);
                }
            }
        } else {
            // 如果没有指定fieldIds，则获取所有数据
            Map<Object, Object> entries = stringRedisTemplate.opsForHash().entries(PROJECT_DICT_CACHE);
            entries.forEach((k, v) -> {
                Integer fieldId = Integer.parseInt((String) k);
                String value = (String) v;
                Map<Integer, String> selectMap = JSON.parseObject(value, new TypeReference<Map<Integer, String>>() {
                });
                projectDictMap.put(fieldId, selectMap);
            });
        }
        return projectDictMap;
    }


    @Override
    public ContractLedgerSummaryStrVo getContractLedgerSummary(ContractLedgerListDTO dto) {
        //关联contract_ledger_detail 获取sum(skje) 会增加复杂度，单独拆分出来做合计返回map（key、value）
        List<ContractLedgerSummaryVo> summaryList = baseMapper.selSummary(dto);
        summaryList = summaryList.stream().filter(s -> s.getHtxl() != null && s.getHtxl() != 0).collect(Collectors.toList());

        //合同款项
        Map<Long, List<ContractPaymentVo>> contractPaymentInfoVoListMap = new HashMap<>();
        if (CollUtil.isNotEmpty(summaryList)) {
            List<Long> ids = summaryList.stream().map(s -> s.getId()).collect(Collectors.toList());
            contractPaymentInfoVoListMap = contractLedgerDetailService.getContractPaymentInfoVoList(ids);
        }


        ContractLedgerSummaryStrVo.ContractLedgerSummaryStrVoBuilder builder = ContractLedgerSummaryStrVo.builder();
        BigDecimal htje = BigDecimal.ZERO;
        BigDecimal dysje = BigDecimal.ZERO;
        BigDecimal dskje = BigDecimal.ZERO;
        BigDecimal yskje = BigDecimal.ZERO;
        BigDecimal zbjzyze = BigDecimal.ZERO;
        Integer contractCount = 0;
        if (CollUtil.isNotEmpty(summaryList)) {
            for (ContractLedgerSummaryVo e : summaryList) {

                //合同总金额
                if (Optional.ofNullable(e.getHtje()).isPresent()) {
                    htje = htje.add(e.getHtje());
                }
                //质保金占用总额
                if (CollUtil.isNotEmpty(contractPaymentInfoVoListMap)) {
                    List<ContractPaymentVo> contractPaymentVoList = contractPaymentInfoVoListMap.get(e.getId());
                    if (CollUtil.isNotEmpty(contractPaymentVoList)) {
                        contractPaymentVoList = contractPaymentVoList.stream()
                                .filter(c -> (ContractPaymentStatusEnum.NOT_RECEIVED.getValue().equals(c.getSkzt())
                                        || ContractPaymentStatusEnum.NOT_RETURN.getValue().equals(c.getSkzt()))
                                        && "质保金".equals(c.getDjbkx())).collect(Collectors.toList());
                        if (CollUtil.isNotEmpty(contractPaymentVoList)) {
                            for (ContractPaymentVo c :
                                    contractPaymentVoList) {
                                zbjzyze = zbjzyze.add(c.getSkje() == null ? BigDecimal.ZERO : c.getSkje());
                            }
                        }
                    }

                }
                //履约中合同数
                if (NumberUtils.INTEGER_ZERO.equals(e.getHtzt())) {
                    contractCount++;
                }
                // 已收款金额
                yskje = yskje.add(StrUtil.isNotBlank(e.getYskje()) ?
                        new BigDecimal(e.getYskje()) : BigDecimal.ZERO);
            }
            //待回款金额
            dskje = htje.subtract(yskje);

            //待验收金额
            dysje = getDysjeSum(htje, summaryList);
        }
        return builder
                .salesContractAmount(MoneyUtil.getInstance().transType(htje))
                .beAcceptedAmount(MoneyUtil.getInstance().transType(dysje))
                .accumulatedAmount(MoneyUtil.getInstance().transType(dskje))
                .contractsNumber(contractCount)
                .warrantyAmount(MoneyUtil.getInstance().transType(zbjzyze))
                .build();
    }

    /**
     * 待验收金额统计
     *
     * @return String 待验收金额
     */
    private BigDecimal getDysjeSum(BigDecimal dysje, List<ContractLedgerSummaryVo> summaryList) {
        List<Long> ids = summaryList.stream().map(c -> c.getId()).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(ids)) {
            Map<Long, List<ContractAcceptanceVo>> contractAcceptanceVoListMap = this.getContractAcceptanceVoListMap(ids);
            Set<Map.Entry<Long, List<ContractAcceptanceVo>>> entries = contractAcceptanceVoListMap.entrySet();
            for (Map.Entry<Long, List<ContractAcceptanceVo>> entry :
                    entries) {
                List<ContractAcceptanceVo> list = entry.getValue();
                //验收金额
                BigDecimal ljyysje = BigDecimal.ZERO;
                for (ContractAcceptanceVo v : list) {
                    ljyysje = ljyysje.add(new BigDecimal(StrUtil.isNotBlank(v.getBcjxqrsrjebhs()) ?
                            v.getBcjxqrsrjebhs().replace(",", "") : "0"));
                }
                dysje = dysje.subtract(ljyysje);
            }
        }

        return dysje;
    }

    @Override
    public ApiResult<ContractBaseHeadInfoVo> getContractBaseHeadInfoVo(Long id) {
        ContractBaseHeadInfoVo infoVo;
        ContractBaseHeadVo headVo = baseMapper.selHeadInfoById(id);


        if (Optional.ofNullable(headVo).isPresent()) {
            infoVo = BeanUtil.copyProperties(headVo, ContractBaseHeadInfoVo.class);
            ProjectInfo projectInfo = projectInfoMapper.selectById(infoVo.getXmmc());
            if (Optional.ofNullable(projectInfo).isPresent()) {
                infoVo.setIsNotInternalProject(projectInfo.getIsNotInternalProject());
            }
            Set<Integer> fieldIdSet = CollUtil.newHashSet(
                    Integer.valueOf(DictConstants.CONTRACT_DETAILS_ID),
                    Integer.valueOf(DictConstants.CONTRACT_TYPE_ID),
                    Integer.valueOf(DictConstants.CONTRACT_COMPANY_ID)
            );
            Map<Integer, Map<Integer, String>> projectDictMap = getProjectDictMap(fieldIdSet);
            //合同细类
            Map<Integer, String> htxlMap = projectDictMap.get(Integer.valueOf(DictConstants.CONTRACT_DETAILS_ID));
            infoVo.setHtxlText(htxlMap.getOrDefault(headVo.getHtxl(), ""));
            //合同类别
            Map<Integer, String> htlbMap = projectDictMap.get(Integer.valueOf(DictConstants.CONTRACT_TYPE_ID));
            infoVo.setHtlbText(htlbMap.getOrDefault(headVo.getHtlb(), ""));
            //合同所属公司
            Map<Integer, String> htssgsMap = projectDictMap.get(Integer.valueOf(DictConstants.CONTRACT_COMPANY_ID));
            infoVo.setHtssgsText(htssgsMap.getOrDefault(headVo.getHtssgs(), ""));
            if (Optional.ofNullable(headVo.getHtje()).isPresent()) {
                infoVo.setHtje(MoneyUtil.getInstance().transType(headVo.getHtje()));
            }
            infoVo.setLjskje(MoneyUtil.getInstance().transType(headVo.getLjskje()));
            infoVo.setHtztText(EnumUtils.getNameByValue(ContractStatusEnum.class, infoVo.getHtzt()));
            infoVo.setHtkxjd(headVo.getYskbl() != null ? headVo.getYskbl() + "%" : "0" + "%");

            return ApiResult.success(infoVo);
        }
        ApiResult apiResult = new ApiResult();
        apiResult.setCode(4001);
        return apiResult;
    }

    @Override
    public ContractOverviewInfoVo getContractOverviewInfoVo(Long id) {
        ContractOverviewInfoVo infoVo = baseMapper.selOverviewInfoById(id);
        if (!Optional.ofNullable(infoVo).isPresent()) {
            return new ContractOverviewInfoVo();
        }
        // 合同金额（含税）
        BigDecimal htje = infoVo.getHtje();
        // 合同款项
        List<ContractMilestoneVO> contractPaymentVoList = CollUtil.emptyIfNull(contractMilestoneService.getContractPaymentById(id));

        BigDecimal partialPaymentAmount = contractPaymentVoList.stream()
                .filter(e -> "部分回款".equals(e.getMilestoneStatus()) && null != e.getActualPaymentAmount())
                .map(ContractMilestoneVO::getActualPaymentAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 已回款金额
        BigDecimal yskje = contractPaymentVoList.stream()
                .filter(e -> "已回款".equals(e.getMilestoneStatus()) && null != e.getActualPaymentAmount())
                .map(ContractMilestoneVO::getActualPaymentAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        infoVo.setYskje(yskje.add(partialPaymentAmount));

        //待回款金额
        infoVo.setDskje(htje.subtract(yskje));

        // 已回款比例
        BigDecimal yhkbl = htje.compareTo(BigDecimal.ZERO) != 0
                ? yskje.divide(htje, 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100))
                : BigDecimal.ZERO;
        infoVo.setYskbl(StrUtil.format("{}%", yhkbl.toString()));

        // 已开票金额
        BigDecimal ykpje = BigDecimal.ZERO;
        List<ContractInvoiceVo> invoiceList = invoiceService.getContractInvoiceVoList(id);
        if (CollectionUtil.isNotEmpty(invoiceList)) {
            ykpje = invoiceList.stream()
                    .filter(c -> NumberUtils.INTEGER_ZERO.equals(c.getFpzt()) && Optional.ofNullable(c.getFpje()).isPresent())
                    .map(ContractInvoiceVo::getFpje)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        BigDecimal ykpbl = htje.compareTo(BigDecimal.ZERO) > NumberUtils.INTEGER_ZERO
                ? ykpje.divide(htje, 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100))
                : BigDecimal.ZERO.setScale(2,  RoundingMode.HALF_UP);
        BigDecimal dkpje = htje.subtract(ykpje);
        infoVo.setYkpje(ykpje);
        infoVo.setYkpbl(ykpbl + "%");
        infoVo.setDkpje(dkpje);

        infoVo.setXmczjd(MoneyUtil.getInstance().transType(Optional.ofNullable(infoVo.getXmczjd()).isPresent() ? new BigDecimal(infoVo.getXmczjd()) : null) + "%");
        return infoVo;
    }

    @Override
    public List<ContractProcessListVO> getContractProcessInfoVo(Long id) {
        ContractLedger contractLedger = baseMapper.selectById(id);
        Long projectId = contractLedger.getXmmc();

        List<ContractProcessInfoVO> contractProcessList = processInfoMapper.selByContractIdAndProjectId(id, projectId);

        List<ContractProcessListVO> processList;
        List<ContractProcessListVO> processListVOS = new ArrayList<>();

        if (CollUtil.isNotEmpty(contractProcessList)) {
            contractProcessList.stream().forEach(c ->
                    c.setProcessTypeName(EnumUtils.getNameByValue(ProcessTypeEnum.class, c.getProcessType()))
            );
            contractProcessList =
                    contractProcessList.stream().sorted(Comparator.comparing(ContractProcessInfoVO::getFilingDateTime).reversed()).collect(Collectors.toList());
            // 创建一个SimpleDateFormat对象来格式化日期
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");

            // 将Date对象格式化为字符串
            processList = contractProcessList.stream().map(e -> {
                        e.setSrje(MoneyUtil.getInstance().transType(Optional.ofNullable(e.getSrje()).isPresent() ? new BigDecimal(e.getSrje()) : null));
                        e.setHtje(MoneyUtil.getInstance().transType(Optional.ofNullable(e.getHtje()).isPresent() ? new BigDecimal(e.getHtje()) : null));
                        e.setSrze(MoneyUtil.getInstance().transType(Optional.ofNullable(e.getSrze()).isPresent() ? new BigDecimal(e.getSrze()) : null));
                        e.setZfje(MoneyUtil.getInstance().transType(Optional.ofNullable(e.getZfje()).isPresent() ? new BigDecimal(e.getZfje()) : null));
                        e.setKpje(MoneyUtil.getInstance().transType(Optional.ofNullable(e.getKpje()).isPresent() ? new BigDecimal(e.getKpje()) : null));
                        ContractProcessListVO build = ContractProcessListVO.builder()
                                .filingDate(formatter.format(e.getFilingDateTime()))
                                .processTypeName(e.getProcessTypeName())
                                .infoVOList(Arrays.asList(e)).build();
                        return build;
                    }
            ).collect(Collectors.toList());

            Map<String, Map<String, List<ContractProcessListVO>>> groupedProcesses = processList.stream()
                    .collect(Collectors.groupingBy(
                            ContractProcessListVO::getProcessTypeName,
                            Collectors.groupingBy(ContractProcessListVO::getFilingDate)));

            for (Map.Entry<String, Map<String, List<ContractProcessListVO>>> outerEntry : groupedProcesses.entrySet()) {
                for (Map.Entry<String, List<ContractProcessListVO>> innerEntry : outerEntry.getValue().entrySet()) {
                    List<ContractProcessInfoVO> infoVOList = new ArrayList<>();
                    ContractProcessListVO contractProcessListVO = new ContractProcessListVO();
                    String key = outerEntry.getKey();
                    contractProcessListVO.setProcessTypeName(key);
                    contractProcessListVO.setFilingDate(innerEntry.getKey());
                    List<ContractProcessListVO> value = innerEntry.getValue();
                    if (CollUtil.isNotEmpty(value)) {
                        for (ContractProcessListVO c : value) {
                            List<ContractProcessInfoVO> infoVOList1 = c.getInfoVOList();
                            infoVOList.addAll(infoVOList1);
                        }
                    }
                    infoVOList =
                            infoVOList.stream().sorted(Comparator.comparing(ContractProcessInfoVO::getFilingDateTime).reversed()).collect(Collectors.toList());
                    contractProcessListVO.setInfoVOList(infoVOList);
                    processListVOS.add(contractProcessListVO);
                }
            }
        }
        processListVOS =
                processListVOS.stream().sorted(Comparator.comparing(ContractProcessListVO::getFilingDate).reversed()).collect(Collectors.toList());

        return processListVOS;
    }

    @Override
    public List<ContractLedgerListVo> exportContractLedgerVoList(ContractLedgerListDTO dto) {
//        List<ContractListVo> contractList =baseMapper.selList(dto);
        List<ContractListVo> contractList = new ArrayList<>();
        if (CollUtil.isEmpty(contractList)) {
            return new ArrayList<>();
        }
        List<ContractDetailSumVo> sumVoList = detailMapper.selSkjeSum();
        Map<Long, BigDecimal> skjeSumMap = sumVoList.stream().collect(Collectors.toMap(ContractDetailSumVo::getId, ContractDetailSumVo::getSkje));
        Map<Integer, Map<Integer, String>> projectDictMap = getProjectDictMap(Collections.emptySet());
        List<ContractLedgerListVo> collect = contractList.stream().map(contractVo -> {
            ContractLedgerListVo contract = BeanUtil.copyProperties(contractVo, ContractLedgerListVo.class);
            //合同细类
            Map<Integer, String> htxlMap = projectDictMap.get(Integer.valueOf(DictConstants.CONTRACT_DETAILS_ID));
            contract.setHtxlText(htxlMap.getOrDefault(contractVo.getHtxl(), ""));
            //合同类别
            Map<Integer, String> htlbMap = projectDictMap.get(Integer.valueOf(DictConstants.CONTRACT_TYPE_ID));
            contract.setHtlbText(htlbMap.getOrDefault(contractVo.getHtlb(), ""));
            //合同所属公司
            Map<Integer, String> htssgsMap = projectDictMap.get(Integer.valueOf(DictConstants.CONTRACT_COMPANY_ID));
            contract.setHtssgsText(htssgsMap.getOrDefault(contractVo.getHtssgs(), ""));
            //业务板块
            Map<Integer, String> ywbkMap = projectDictMap.get(Integer.valueOf(DictConstants.BUSINESS_BLOCK_ID));
            contract.setYwbkText(ywbkMap.getOrDefault(contractVo.getYwbk(), ""));
            //结算方式
            Map<Integer, String> jsfsMap = projectDictMap.get(Integer.valueOf(DictConstants.SETTLEMENT_METHOD_ID));
            contract.setJsfsText(jsfsMap.getOrDefault(contractVo.getJsfs(), ""));
            //技术类型
            Map<Integer, String> jslxMap = projectDictMap.get(Integer.valueOf(DictConstants.SKILL_TYPE_ID));
            contract.setJslxText(jslxMap.getOrDefault(contractVo.getJslx(), ""));
            //结项启动条件
            Map<Integer, String> jxqdtjMap = projectDictMap.get(Integer.valueOf(DictConstants.COMPLETION_STARTING_ID));
            contract.setJxqdtjText(jxqdtjMap.getOrDefault(contractVo.getJxqdtj(), ""));

            BigDecimal skje = skjeSumMap.getOrDefault(contractVo.getId(), BigDecimal.ZERO);
            BigDecimal htkxjd = contractVo.getHtjehs().compareTo(BigDecimal.ZERO) > NumberUtils.INTEGER_ZERO ?
                    skje.divide(contractVo.getHtjehs(), 2, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100))
                    : BigDecimal.ZERO;

            Integer htzt = NumberUtils.INTEGER_TWO.equals(contractVo.getBglx()) ?
                    3 :
                    !Optional.ofNullable(contractVo.getSjhtqdrq()).isPresent() ?
                            NumberUtils.INTEGER_TWO :
                            htkxjd.compareTo(new BigDecimal("100")) >= NumberUtils.INTEGER_ZERO ?
                                    NumberUtils.INTEGER_ONE : NumberUtils.INTEGER_ZERO;
            contract.setHtzt(htzt);
            contract.setHtztText(EnumUtils.getNameByValue(ContractStatusEnum.class, htzt));
            contract.setHtkxjd(htkxjd + "%");

            return contract;
        }).collect(Collectors.toList());

        if (Optional.ofNullable(dto.getHtzt()).isPresent()) {
            List<ContractLedgerListVo> list = collect.stream().filter(e -> dto.getHtzt().equals(e.getHtzt())).collect(Collectors.toList());
            return list;
        }

        return collect;

    }

    @Override
    public List<ContractAcceptanceVo> getContractAcceptanceVoList(Long id) {
        List<ContractAcceptanceVo> contractChangeInfoList = dbApiUtil.getAcceptanceRecordsUrl(id);

        if (CollUtil.isNotEmpty(contractChangeInfoList)) {
            List<Long> docIds = new ArrayList<>();

            contractChangeInfoList.stream().forEach(c -> {
                if (Optional.ofNullable(c.getYsbgfj()).isPresent()) {
                    if (StringUtils.isNotBlank(c.getYsbgfj())) {
                        String[] split = StringUtils.split(c.getYsbgfj(), ",");
                        List<String> list = Arrays.asList(split);
                        list.stream().forEach(l -> docIds.add(Long.parseLong(l)));
                    }
                }
                //结项启动条件
                Map<Integer, Map<Integer, String>> projectDictMap = getProjectDictMap(Collections.singleton(Integer.valueOf(DictConstants.COMPLETION_STARTING_ID)));
                Map<Integer, String> jxqdtjMap = projectDictMap.get(Integer.valueOf(DictConstants.COMPLETION_STARTING_ID));
                c.setJxqdtjText(jxqdtjMap.getOrDefault(c.getJxqdtj(), ""));
                c.setJdysje(MoneyUtil.getInstance().transType(Optional.ofNullable(c.getJdysje()).isPresent() ? new BigDecimal(c.getJdysje()) : null));
                c.setBcjxqrsrjebhs(MoneyUtil.getInstance().transType(Optional.ofNullable(c.getBcjxqrsrjebhs()).isPresent() ? new BigDecimal(c.getBcjxqrsrjebhs()) : null));
                c.setBcjxqrsrje(MoneyUtil.getInstance().transType(Optional.ofNullable(c.getBcjxqrsrje()).isPresent() ? new BigDecimal(c.getBcjxqrsrje()) : null));
            });

            Map<Long, PmsDocImageFile> pmsDocImageFileMap = new HashMap<>();
            if (CollUtil.isNotEmpty(docIds)) {
                List<PmsDocImageFile> pmsDocImageFileList = pmsDocImageFileService.getByDocIds(docIds);
                pmsDocImageFileMap =
                        pmsDocImageFileList.stream().collect(Collectors.toMap(PmsDocImageFile::getDocId, Function.identity()));
            }
            if (CollUtil.isNotEmpty(pmsDocImageFileMap)) {
                for (ContractAcceptanceVo c :
                        contractChangeInfoList) {
                    List<OaFileInfoVo> fpsmjName = new ArrayList<>();
                    if (StringUtils.isNotBlank(c.getYsbgfj())) {
                        String[] split = StringUtils.split(c.getYsbgfj(), ",");
                        List<String> list = Arrays.asList(split);
                        for (String s : list) {
                            PmsDocImageFile pmsDocImageFile = pmsDocImageFileMap.get(Long.parseLong(s));
                            OaFileInfoVo oaFileInfoVo = new OaFileInfoVo();
                            oaFileInfoVo.setFileId(pmsDocImageFile.getDocId().toString());
                            oaFileInfoVo.setFilename(pmsDocImageFile.getImageFileName());
                            fpsmjName.add(oaFileInfoVo);
                        }
                    }
                    c.setYsbgfjName(fpsmjName);

                }
            }


        }
        return contractChangeInfoList;
    }

    @Override
    public Map<Long, List<ContractAcceptanceVo>> getContractAcceptanceVoListMap(List<Long> ids) {
        List<ContractAcceptanceVo> contractChangeInfoList = dbApiUtil.getAcceptanceRecordsUrlByIds(ids);
        if (CollUtil.isNotEmpty(contractChangeInfoList)) {
            contractChangeInfoList.stream().forEach(c -> {
                c.setBcjxqrsrjebhs(MoneyUtil.getInstance()
                        .transType(Optional.ofNullable(c.getBcjxqrsrjebhs()).isPresent() ?
                                new BigDecimal(c.getBcjxqrsrjebhs()) : null));
                c.setBcjxqrsrje(MoneyUtil.getInstance()
                        .transType(Optional.ofNullable(c.getBcjxqrsrje()).isPresent() ?
                                new BigDecimal(c.getBcjxqrsrje()) : null));
            });
        }
        Map<Long, List<ContractAcceptanceVo>> collect =
                contractChangeInfoList.stream().collect(Collectors.groupingBy(c -> c.getId()));
        return collect;
    }

    @Override
    public Map<Long, List<ContractRiskInfoVO>> getContractRiskInfoVo(List<Long> ids) {
        List<ContractRiskInfoVO> contractRiskList = new ArrayList<>();
        //时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtil.SIMPLE_DATE_FORMAT);
        //合同款项
        Map<Long, List<ContractMilestoneVO>> milestonesMap = CollUtil.emptyIfNull(milestoneMapper.findByContractIds(ids)).stream()
                .collect(Collectors.groupingBy(ContractMilestoneVO::getContractId, Collectors.toList()));
        if (CollUtil.isNotEmpty(milestonesMap)) {
            Set<Map.Entry<Long, List<ContractMilestoneVO>>> entries = milestonesMap.entrySet();
            for (Map.Entry<Long, List<ContractMilestoneVO>> entry : entries) {
                List<ContractMilestoneVO> contractPaymentInfoVoList = entry.getValue();
                //合同款项风险
                List<ContractRiskInfoVO> contractRiskList1 = this.getContractRiskList(contractPaymentInfoVoList);
                if (CollUtil.isNotEmpty(contractRiskList1)) {
                    contractRiskList.addAll(contractRiskList1);
                }

            }

        }
        //合同
        List<ContractLedger> contractLedgers = this.listByIds(ids);
        if (CollUtil.isNotEmpty(contractLedgers)) {
            for (ContractLedger contractLedger : contractLedgers) {
                //进度风险
                ContractRiskInfoVO contractRiskInfoVO = new ContractRiskInfoVO();
                contractRiskInfoVO.setContractId(contractLedger.getId());
                BigDecimal yskbl = contractLedger.getYskbl() == null ? BigDecimal.ZERO : contractLedger.getYskbl();
                String xmczjdStr = StrUtil.isBlank(contractLedger.getXmczjd()) ? "0" : contractLedger.getXmczjd().replace("%", StrUtil.EMPTY);
                BigDecimal xmczjd = new BigDecimal(xmczjdStr);
                BigDecimal subtract = xmczjd.subtract(yskbl);
                contractRiskInfoVO.setRiskContent("项目产值进度低于合同计划进度(" + subtract + "%)");
                contractRiskInfoVO.setRiskType(ContractRiskEnum.CONTROL.getValue());
                contractRiskInfoVO.setRiskTypeName(ContractRiskEnum.CONTROL.getName());
                contractRiskInfoVO.setPromptTime(LocalDate.now().format(formatter));
                if (subtract.compareTo(BigDecimal.ZERO) < 0 && subtract.compareTo(new BigDecimal("-30")) > 0) {
                    contractRiskInfoVO.setRiskMark("0");
                    contractRiskList.add(contractRiskInfoVO);
                } else if (subtract.compareTo(BigDecimal.ZERO) < 0 && subtract.compareTo(new BigDecimal("-30")) < 0) {
                    contractRiskInfoVO.setRiskMark("1");
                    contractRiskList.add(contractRiskInfoVO);
                }
            }
        }

        List<ContractRiskInfoVO> contractRiskdbApiList = dbApiUtil.getContractRiskInfoVoByIds(ids);
        if (CollUtil.isNotEmpty(contractRiskdbApiList)) {
            contractRiskdbApiList = contractRiskdbApiList.stream()
                    .filter(c -> c.getRiskType().equals(ContractRiskEnum.CONTROL.getValue())
                            && !c.getRiskContent().contains("项目产值进度低于合同计划进度"))
                    .collect(Collectors.toList());
            contractRiskList.addAll(contractRiskdbApiList);
        }

        Map<Long, List<ContractRiskInfoVO>> collect =
                contractRiskList.stream().collect(Collectors.groupingBy(ContractRiskInfoVO::getContractId));
        return collect;
    }

    @Override
    public List<ContractRiskListVO> getContractRiskInfoVo(Long id) {
        List<ContractRiskListVO> riskList = new ArrayList<>();
        List<ContractRiskInfoVO> contractRiskList = new ArrayList<>();
        //合同款项
        List<ContractMilestoneVO> contractPaymentInfoVoList
                = contractMilestoneService.getContractPaymentById(id);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtil.SIMPLE_DATE_FORMAT);

        //合同款项风险
        List<ContractRiskInfoVO> contractRiskList1 = this.getContractRiskList(contractPaymentInfoVoList);
        if (CollUtil.isNotEmpty(contractRiskList1)) {
            contractRiskList.addAll(contractRiskList1);
        }

        //进度风险
        ContractRiskInfoVO contractRiskInfoVO = new ContractRiskInfoVO();
        contractRiskInfoVO.setContractId(id);
        ContractLedger contractLedger = this.getById(id);
        BigDecimal yskbl = contractLedger.getYskbl() == null ? BigDecimal.ZERO : contractLedger.getYskbl();
        ContractOverviewInfoVo contractOverviewInfoVo = baseMapper.selOverviewInfoById(id);
        BigDecimal xmczjd = new BigDecimal(StrUtil.isBlank(contractOverviewInfoVo.getXmczjd()) ? "0" : contractOverviewInfoVo.getXmczjd());
        BigDecimal subtract = xmczjd.subtract(yskbl);
        contractRiskInfoVO.setRiskContent("项目产值进度低于合同计划进度(" + subtract + "%)");
        contractRiskInfoVO.setRiskType(ContractRiskEnum.CONTROL.getValue());
        contractRiskInfoVO.setRiskTypeName(ContractRiskEnum.CONTROL.getName());
        contractRiskInfoVO.setPromptTime(LocalDate.now().format(formatter));
        if (subtract.compareTo(BigDecimal.ZERO) < 0 && subtract.compareTo(new BigDecimal("-30")) > 0) {
            contractRiskInfoVO.setRiskMark("0");
            contractRiskList.add(contractRiskInfoVO);
        } else if (subtract.compareTo(BigDecimal.ZERO) < 0 && subtract.compareTo(new BigDecimal("-30")) < 0) {
            contractRiskInfoVO.setRiskMark("1");
            contractRiskList.add(contractRiskInfoVO);
        }

        List<ContractRiskInfoVO> contractRiskdbApiList = dbApiUtil.getContractRiskInfoVo(id);
        if (CollUtil.isNotEmpty(contractRiskdbApiList)) {
            contractRiskdbApiList = contractRiskdbApiList.stream()
                    .filter(c -> c.getRiskType().equals(ContractRiskEnum.CONTROL.getValue())
                            && !c.getRiskContent().contains("项目产值进度低于合同计划进度"))
                    .collect(Collectors.toList());
            contractRiskList.addAll(contractRiskdbApiList);
        }
        if (CollUtil.isNotEmpty(contractRiskList)) {
            contractRiskList.stream().forEach(c ->
                    c.setRiskTypeName(EnumUtils.getNameByValue(ContractRiskEnum.class, c.getRiskType()))
            );
            Map<String, List<ContractRiskInfoVO>> RiskTypeMap = contractRiskList.stream()
                    .collect(Collectors.groupingBy(ContractRiskInfoVO::getRiskTypeName));

            List<String> keyList = RiskTypeMap.keySet().stream().collect(Collectors.toList());

            riskList = keyList.stream().map(key -> {
                        List<ContractRiskInfoVO> infoVOList = RiskTypeMap.get(key);
                        if (CollUtil.isNotEmpty(infoVOList)) {
                            //倒序
                            infoVOList = infoVOList.stream()
                                    .sorted(Comparator.comparing(ContractRiskInfoVO::getPromptTime).reversed())
                                    .collect(Collectors.toList());
                        }
                        ContractRiskListVO build = ContractRiskListVO.builder()
                                .riskTypeName(key)
                                .infoVOList(infoVOList)
                                .build();
                        return build;
                    }
            ).collect(Collectors.toList());
        }
        return riskList;
    }

    /**
     * 查询判断合同款项风险
     *
     * @return list  合同款项风险
     */
    private List<ContractRiskInfoVO> getContractRiskList(List<ContractMilestoneVO> contractMilestoneVOList) {
        List<ContractRiskInfoVO> contractRiskList = new ArrayList<>();
        ContractPaymentVo ex = new ContractPaymentVo();
        //时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtil.SIMPLE_DATE_FORMAT);
        if (CollUtil.isEmpty(contractMilestoneVOList)) {
            return contractRiskList;
        }
        for (ContractMilestoneVO vo : contractMilestoneVOList) {
            LocalDate expectedCompleteDate = vo.getExpectedCompleteDate();
            if (null == expectedCompleteDate) {
                continue;
            }
            LocalDate date = expectedCompleteDate;
            // 获取当前日期
            LocalDate currentDate = Optional.ofNullable(vo.getActualPaymentDate()).orElse(LocalDate.now());
            // 逾期
            if (date.isBefore(currentDate)) {
                ContractRiskInfoVO contractRiskInfoVO = new ContractRiskInfoVO();
                contractRiskInfoVO.setContractId(vo.getContractId());
                contractRiskInfoVO.setRiskContent("款项【" + vo.getPaymentName() + "】逾期风险");
                contractRiskInfoVO.setRiskType(ContractRiskEnum.PAYMENT.getValue());
                contractRiskInfoVO.setRiskTypeName(ContractRiskEnum.PAYMENT.getName());
                //已收款
                if (ContractPaymentStatusEnum.RECEIVED.getName().equals(vo.getMilestoneStatus())) {
                    contractRiskInfoVO.setOverdueDays(vo.getActualPaymentDate() == null
                            ? StrUtil.EMPTY
                            : Math.abs(ChronoUnit.DAYS.between(date, vo.getActualPaymentDate())) + StrUtil.EMPTY);
                    contractRiskInfoVO.setRiskMark("2");
                    contractRiskInfoVO.setRiskContent(contractRiskInfoVO.getRiskContent() + "；【已付款】");
                } else if (ContractPaymentStatusEnum.RETURN.getName().equals(vo.getMilestoneStatus())) {
                    contractRiskInfoVO.setOverdueDays(vo.getActualPaymentDate() == null
                            ? StrUtil.EMPTY
                            : Math.abs(ChronoUnit.DAYS.between(date, vo.getActualPaymentDate())) + StrUtil.EMPTY);
                    contractRiskInfoVO.setRiskMark("2");
                    contractRiskInfoVO.setRiskContent(contractRiskInfoVO.getRiskContent() + "；【已回款】");
                } else {
                    long abs = Math.abs(ChronoUnit.DAYS.between(date, currentDate));
                    contractRiskInfoVO.setOverdueDays(StrUtil.toString(abs));
                    if (abs < 30) {
                        contractRiskInfoVO.setRiskMark("0");
                    } else {
                        contractRiskInfoVO.setRiskMark("1");
                    }
                }
                LocalDate promptDate = date.plusDays(1);
                contractRiskInfoVO.setPromptTime(promptDate.format(formatter));
                contractRiskList.add(contractRiskInfoVO);
            }
        }
        return contractRiskList;
    }

    @Override
    public Map<Long, List<ContractRiskInfoVO>> getContractRiskInfoVoListMap(List<Long> ids) {
        List<ContractRiskInfoVO> contractRiskList = dbApiUtil.getContractRiskInfoVoByIds(ids);

        Map<Long, List<ContractRiskInfoVO>> collect =
                contractRiskList.stream().collect(Collectors.groupingBy(c -> c.getContractId()));
        return collect;
    }

    @Override
    public ContractDownloadVo contractDownload(Long id) {
        ContractDownloadVo contractDownloadVo = new ContractDownloadVo();
        ContractLedger contractLedger = this.getById(id);
        QueryWrapper<PmsContractProcessInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(PmsContractProcessInfo::getContractId, id)
                .eq(PmsContractProcessInfo::getProcessType, 3);
        ;
        PmsContractProcessInfo pmsContractProcessInfo = processInfoMapper.selectOne(queryWrapper);
        List<OaFileVo> resourcesData;
        if (Optional.ofNullable(pmsContractProcessInfo).isPresent()
                && Optional.ofNullable(pmsContractProcessInfo.getRequestId()).isPresent()
                && Optional.ofNullable(pmsContractProcessInfo.getHtfjygz()).isPresent()) {
            resourcesData = oaUtil.getResourcesData(String.valueOf(pmsContractProcessInfo.getRequestId()),
                    String.valueOf(pmsContractProcessInfo.getCreater()));
            OaFileInfoVo htfjygzOaFileInfoVo = new OaFileInfoVo();
            htfjygzOaFileInfoVo.setRequestId(String.valueOf(pmsContractProcessInfo.getRequestId()));
            htfjygzOaFileInfoVo.setFileId(pmsContractProcessInfo.getHtfjygz());
            contractDownloadVo.setHtfjygzList(pmsDocImageFileService.getOaOaFileInfoList(htfjygzOaFileInfoVo
                    , resourcesData));
            contractDownloadVo.setId(id);
            contractDownloadVo.setHtmc(contractLedger.getHtmc());
        }
        //日志
        bcpLoggerUtils.log(FunctionConstants.CONTRACT_LEDGER, LogContentEnum.DOWNLOAD, contractLedger.getHtbh());
        return contractDownloadVo;
    }

    @Override
    public OaFileDownloadVo oaFileDownload(Long contractId, OaFileDownLoadTypeEnum fileType, String fileId) {
        OaFileDownloadVo oaFileDownloadVo = new OaFileDownloadVo();
        oaFileDownloadVo.setId(contractId);
        String downloadUrl = StrUtil.EMPTY;
        ContractLedger contractLedger = this.getById(contractId);
        if (Optional.ofNullable(contractLedger).isPresent()) {
            switch (fileType) {
                //验收附件
                case YSBGFJ:
                    List<ContractAcceptanceVo> contractChangeInfoList = dbApiUtil.getAcceptanceRecordsUrl(contractId);
                    if (CollUtil.isNotEmpty(contractChangeInfoList)) {
                        contractChangeInfoList = contractChangeInfoList.stream()
                                .filter(c -> StrUtil.isNotBlank(c.getYsbgfj()) && c.getYsbgfj().contains(fileId))
                                .collect(Collectors.toList());
                        if (CollUtil.isNotEmpty(contractChangeInfoList)) {
                            ContractAcceptanceVo c = contractChangeInfoList.get(0);
                            OaFileInfoVo ysbgOaFileInfoVo = new OaFileInfoVo();
                            ysbgOaFileInfoVo.setRequestId(String.valueOf(c.getRequestId()));
                            ysbgOaFileInfoVo.setFileId(c.getYsbgfj());
                            ysbgOaFileInfoVo.setImageFileId(c.getYsbgfjImageFileId());
                            if (Optional.ofNullable(c.getRequestId()).isPresent()
                                    && Optional.ofNullable(c.getApplicantid()).isPresent()) {
                                List<OaFileVo> resourcesData = oaUtil.getResourcesData(String.valueOf(c.getRequestId()),
                                        c.getApplicantid());
                                List<OaFileInfoVo> fileList = pmsDocImageFileService.getOaOaFileInfoList(ysbgOaFileInfoVo, resourcesData);
                                fileList = fileList.stream().filter(f -> fileId.contains(f.getFileId())).collect(Collectors.toList());
                                if (CollUtil.isNotEmpty(fileList)) {
                                    downloadUrl = fileList.get(0).getDownloadUrl();
                                }
                            }
                        }

                    }
                    break;
                case FPFJ:
                    QueryWrapper<ContractLedgerDetail> contractLedgerDetailQueryWrapper = new QueryWrapper<>();
                    contractLedgerDetailQueryWrapper.lambda().eq(ContractLedgerDetail::getMainid, contractId)
                            .eq(ContractLedgerDetail::getFpkjzt, InvoicingStatusEnum.YES.getValue());
                    List<ContractLedgerDetail> contractLedgerDetailList
                            = contractLedgerDetailService.list(contractLedgerDetailQueryWrapper);
                    if (CollectionUtil.isNotEmpty(contractLedgerDetailList)) {
                        List<Long> ids = contractLedgerDetailList
                                .stream().map(d -> d.getId()).collect(Collectors.toList());
                        QueryWrapper<ProjectContractInvoice> queryWrapper = new QueryWrapper<>();
                        queryWrapper.lambda().in(ProjectContractInvoice::getLlhtmxskbh, ids)
                                .or()
                                .eq(ProjectContractInvoice::getHtbh, contractLedger.getHtbh())
                                .orderByDesc(ProjectContractInvoice::getFprq);
                        List<ProjectContractInvoice> projectContractInvoiceList =
                                projectContractInvoiceMapper.selectList(queryWrapper);
                        projectContractInvoiceList =
                                projectContractInvoiceList.stream()
                                        .filter(c -> StrUtil.isNotBlank(c.getFpsmj()) && c.getFpsmj().contains(fileId))
                                        .collect(Collectors.toList());
                        if (CollUtil.isNotEmpty(projectContractInvoiceList)) {
                            ProjectContractInvoice c = projectContractInvoiceList.get(0);
                            if (Optional.ofNullable(c.getFpsmj()).isPresent()) {
                                OaFileInfoVo htfjOaFileInfoVo = new OaFileInfoVo();
                                htfjOaFileInfoVo.setFileId(c.getFpsmj());
                                htfjOaFileInfoVo.setImageFileId(c.getFpsmjImageFileId());
                                if (Optional.ofNullable(c.getRequestid()).isPresent()
                                        && Optional.ofNullable(c.getNodeoperator()).isPresent()) {
                                    List<OaFileVo> resourcesData = oaUtil.getResourcesData(String.valueOf(c.getRequestid()),
                                            String.valueOf(c.getNodeoperator()));
                                    List<OaFileInfoVo> fileList = pmsDocImageFileService.getOaOaFileInfoList(htfjOaFileInfoVo
                                            , resourcesData);
                                    fileList = fileList.stream().filter(f -> fileId.contains(f.getFileId())).collect(Collectors.toList());
                                    if (CollUtil.isNotEmpty(fileList)) {
                                        downloadUrl = fileList.get(0).getDownloadUrl();
                                    }
                                }
                            }
                        }
                    }

                    break;

            }
            oaFileDownloadVo.setDownloadUrl(downloadUrl);
        }

        return oaFileDownloadVo;
    }

    @Override
    public List<ContractLedgerSelectListVo> getContractLedger(ContractLedgerSelectListDTO dto) {
        List<ContractLedger> contractLedgers = baseMapper.selectList(Wrappers.<ContractLedger>lambdaQuery()
                .eq(ContractLedger::getHtxl, dto.getHtxl())
                .like(ContractLedger::getHtmc, dto.getHtmc()));
        if (CollectionUtil.isEmpty(contractLedgers)) {
            return ListUtil.empty();
        }
        Map<Integer, Map<Integer, String>> projectDictMap = getProjectDictMap(Collections.singleton(Integer.valueOf(DictConstants.CONTRACT_DETAILS_ID)));
        Map<Integer, String> htxlMap = projectDictMap.get(Integer.valueOf(DictConstants.CONTRACT_DETAILS_ID));

        List<ContractLedgerSelectListVo> voList = contractLedgers.stream().map(e -> {
            ContractLedgerSelectListVo vo = BeanUtil.copyProperties(e, ContractLedgerSelectListVo.class);
            vo.setHtxlText(htxlMap.getOrDefault(vo.getHtxl(), ""));
            return vo;
        }).collect(Collectors.toList());

        return voList;
    }


}