package com.gok.pboot.pms.cost.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 成本估算结果VO类
 *
 * <AUTHOR>
 * @create 2025/01/07
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CostManageSelectVO {

    /**
     * 成本科目ID
     */
    private Long accountId;

    /**
     * 成本科目名称
     */
    private String accountName;

    /**
     * 预算金额(含税)
     */
    private BigDecimal budgetAmountIncludedTax;

    /**
     * 科目OA ID
     */
    private Long accountOaId;

    /**
     * 税率
     */
    private Integer taxRate;


}
