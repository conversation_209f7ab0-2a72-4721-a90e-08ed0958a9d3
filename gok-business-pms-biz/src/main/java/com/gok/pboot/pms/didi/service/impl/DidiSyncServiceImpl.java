package com.gok.pboot.pms.didi.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.gok.components.common.constant.SecurityConstants;
import com.gok.pboot.common.core.exception.BusinessException;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.Util.DbApiUtil;
import com.gok.pboot.pms.common.exception.ServiceException;
import com.gok.pboot.pms.didi.client.DidiClient;
import com.gok.pboot.pms.didi.entity.dto.*;
import com.gok.pboot.pms.didi.service.DidiSyncService;
import com.gok.pboot.pms.didi.util.DidiSignUtil;
import com.gok.pboot.pms.entity.domain.DdRelation;
import com.gok.pboot.pms.entity.domain.Roster;
import com.gok.pboot.pms.enumeration.DdRelationTypeEnum;
import com.gok.pboot.pms.oa.dto.OaProjectDTO;
import com.gok.pboot.pms.service.IDdRelationService;
import com.gok.pboot.pms.service.IProjectInfoService;
import com.gok.pboot.pms.service.RosterService;
import com.gok.pboot.service.commons.base.ApiResult;
import com.gok.pboot.service.entity.hrm.vo.HrmStaffRosterVo;
import com.gok.pboot.service.feign.RemoteEhrService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 滴滴项目同步服务实现类
 *
 * <AUTHOR>
 * @date 2025/01/27
 */
@Slf4j
@Service
public class DidiSyncServiceImpl implements DidiSyncService {
    @Resource
    private DbApiUtil dbApiUtil;

    @Resource
    private DidiClient didiClient;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private RosterService rosterService;

    @Resource
    private RemoteEhrService remoteEhrService;

    @Resource
    private IProjectInfoService projectInfoService;

    @Resource
    private IDdRelationService ddRelationService;

    @Value("${didi.sync.req-interval:150}")
    private Long syncInterval;

    @Value("${didi.client.id:1}")
    public String clientId;

    @Value("${didi.client.company_id:1}")
    public String companyId;

    @Value("${didi.client.secret:1}")
    public String clientSecret;

    @Value("${didi.client.grant.type:client_credentials}")
    private String grantType;


    private static final String DIDI_ACCESS_TOKEN_KEY = "didi:access_token";

    private static final String DIDI_ACCESS_AUTH_REQ_KEY = "didi:auth_req";

    private static final long DIDI_ACCESS_EXPIRE_TIME = 25;

    public synchronized boolean syncProjectToDidi(OaProjectDTO projectInfo, Boolean withManager) {

        try {
            // 检查是否已存在关联关系
            DdRelation existingRelation = ddRelationService.getByRelateIdAndType(projectInfo.getProjectId(), DdRelationTypeEnum.PROJECT);
            boolean isUpdate = existingRelation != null;

            // 记录操作类型日志
            logOperationType(projectInfo, existingRelation, isUpdate);

            // 调用滴滴API
            callDidiApi(projectInfo, isUpdate, withManager);

            // 添加间隔，避免请求过于频繁（滴滴要求间隔150ms以上）
            if (syncInterval > 0) {
                Thread.sleep(syncInterval);
            }
        } catch (Exception e) {
            log.error("项目同步异常: {}", projectInfo.getProjectNo(), e);
            return false;
        }
        return true;
    }


    @Override
    public synchronized void batchSyncProjects(LocalDate dateParam, Boolean withManager) {

        List<OaProjectDTO> projectDTOList = dbApiUtil.getProjectInfoByAfterDate(dateParam);
        int sum = projectDTOList.stream().mapToInt(p -> syncProjectToDidi(p, withManager) ? 1 : 0).sum();
        log.info("批量同步完成，总数: {}, 成功: {}", projectDTOList.size(), sum);
    }


    @Override
    public DidiAuthReq getDidiAuthReq() {
        String str = stringRedisTemplate.opsForValue().get(DIDI_ACCESS_AUTH_REQ_KEY);
        if (str == null) {
            return null;
        }
        return JSONObject.parseObject(str, DidiAuthReq.class);
    }


    public boolean syncUserToDidi(HrmStaffRosterVo roster) {
        try {
            // 转换用户信息
            DidiUserSyncReq syncReq = convertToDidiUserSyncReq(roster);

            // 调用滴滴API同步用户
            DidiResult<DidiUserSyncRes> result = didiClient.addUser(syncReq);

            if (result == null || result.getErrno() != 0) {
                log.error("用户同步失败: {}, 错误信息: {}", roster.getWorkCode(), result != null ? result.getErrmsg() : "未知错误");
                return false;
            }
        } catch (Exception e) {
            log.error("用户同步异常: {}", roster.getWorkCode(), e);
            return false;
        }
        return true;
    }

    @Override
    public synchronized void batchSyncUsers(LocalDate dateParam) {
        Map<String, Object> reqMap = new HashMap<>(1);
        if (dateParam != null) {
            reqMap.put("recentlyUpdated", dateParam);
        }
        ApiResult<List<HrmStaffRosterVo>> result = remoteEhrService.getRecentlyUpdatedRosters(reqMap, SecurityConstants.FROM_IN);
        List<HrmStaffRosterVo> hrmStaffRosterVos = BaseBuildEntityUtil.apiResult(result);
        int successCount = 0;
        int failCount = 0;

        for (HrmStaffRosterVo roster : hrmStaffRosterVos) {
            try {
                boolean success = syncUserToDidi(roster);
                if (success) {
                    successCount++;
                } else {
                    failCount++;
                }

                // 添加间隔，避免请求过于频繁（滴滴要求间隔150ms以上）
                if (syncInterval > 0) {
                    Thread.sleep(syncInterval);
                }
            } catch (InterruptedException e) {
                log.warn("同步间隔被中断: {}", roster.getWorkCode());
                Thread.currentThread().interrupt();
            } catch (Exception e) {
                log.error("批量同步中用户同步异常: {}", roster.getWorkCode(), e);
                failCount++;
            }
        }

        log.info("批量同步完成，总数: {}, 成功: {}, 失败: {}", hrmStaffRosterVos.size(), successCount, failCount);
    }


    /**
     * 将Roster信息转换为滴滴用户同步请求
     * 员工工号、姓名、手机号、离职日期、直接上级、所属项目
     */
    private DidiUserSyncReq convertToDidiUserSyncReq(HrmStaffRosterVo roster) {
        DidiUserSyncReq syncReq = new DidiUserSyncReq();

        // 构建用户数据
        DidiUserSyncReq.UserData userData = new DidiUserSyncReq.UserData();

        // 使用工号作为唯一标识
        userData.setMember_type(1);
        // 基本信息
        userData.setRealname(roster.getAliasName());
        userData.setEmployee_number(roster.getWorkCode());
        userData.setSex(roster.getSex() == 0 ? 1 : 2);

        // 上级信息
        if (roster.getUserLeaderId() != null) {
            Roster leader = rosterService.getById(roster.getUserLeaderId());
            if (leader != null && StrUtil.isNotBlank(leader.getWorkCode())) {
                userData.setImmediate_superior_employee_number(leader.getWorkCode());
            }
        }


        // 将userData转换为JSON字符串
        syncReq.setData(JSONObject.toJSONString(userData));

        return syncReq;
    }

    /**
     * 获取滴滴访问令牌
     */
    @Override
    public synchronized String getAccessToken() {
        try {
            String accessToken = stringRedisTemplate.opsForValue().get(DIDI_ACCESS_TOKEN_KEY);
            if (StringUtils.isNotBlank(accessToken)) {
                return accessToken;
            }
            DidiAuthReq authReq = new DidiAuthReq();
            authReq.setClient_id(clientId);
            authReq.setClient_secret(clientSecret);
            authReq.setGrant_type(grantType);
            authReq.setCompany_id(companyId);
            authReq.setTimestamp((int) (System.currentTimeMillis() / 1000));
            // 这里需要根据滴滴的签名算法生成签名
            authReq.setSign(generateSign(authReq));

            DidiAuthRes authRes = didiClient.getToken(authReq);
            if (authRes != null && authRes.getAccess_token() != null) {
                stringRedisTemplate.opsForValue().set(DIDI_ACCESS_TOKEN_KEY, authRes.getAccess_token(), DIDI_ACCESS_EXPIRE_TIME, TimeUnit.MINUTES);
                stringRedisTemplate.opsForValue().set(DIDI_ACCESS_AUTH_REQ_KEY, JSONObject.toJSONString(authReq), DIDI_ACCESS_EXPIRE_TIME, TimeUnit.MINUTES);
                return authRes.getAccess_token();
            }
        } catch (Exception e) {
            log.error("获取滴滴访问令牌异常", e);
            stringRedisTemplate.delete(DIDI_ACCESS_TOKEN_KEY);
            stringRedisTemplate.delete(DIDI_ACCESS_AUTH_REQ_KEY);
            throw new BusinessException("获取滴滴访问令牌异常");
        }
        return null;
    }

    /**
     * 生成签名
     * 注意：这里需要根据滴滴的具体签名算法实现
     */
    private String generateSign(DidiAuthReq authReq) {
        return DidiSignUtil.generateSign(authReq);
    }


    /**
     * 将项目信息转换为滴滴项目同步请求
     *
     * @param projectInfo 项目信息
     * @param withManager 是否同步项目经理
     */
    private DidiProjectSyncReq convertToDidiProjectSyncReq(OaProjectDTO projectInfo, Boolean withManager) {
        String workCode = null;
        if (withManager) {
            workCode = projectInfo.getProjectManagerWorkCode();
        }
        // 【项目名称】、【项目编号】、【项目经理】
        // 设置为项目类型

        return new DidiProjectSyncReq()
                // 设置为项目类型
                .setType(2)
                .setName(projectInfo.getProjectName())
                .setOut_budget_id(projectInfo.getProjectNo())
                .setLeader_employee_id(workCode);
    }

    /**
     * 记录操作类型日志
     */
    private void logOperationType(OaProjectDTO projectInfo, DdRelation existingRelation, boolean isUpdate) {
        if (isUpdate && existingRelation != null && StrUtil.isNotBlank(existingRelation.getDidiId())) {
            log.info("检测到已存在关联关系，将进行更新操作: 项目编码={}, 滴滴项目ID={}",
                    projectInfo.getProjectNo(), existingRelation.getDidiId());
        } else {
            log.info("未检测到关联关系，将进行新增操作: 项目编码={}", projectInfo.getProjectNo());
        }
    }

    /**
     * 调用滴滴API
     */
    private void callDidiApi(OaProjectDTO projectInfo,
                             boolean isUpdate,
                             Boolean withManager) {
        DidiProjectSyncReq syncReq = convertToDidiProjectSyncReq(projectInfo, withManager);

        if (isUpdate) {
            didiClient.updateProject(syncReq);
            ddRelationService.updateSyncTime(projectInfo.getProjectId(), DdRelationTypeEnum.PROJECT);
        } else {
            DidiResult<DidiProjectSyncRes> result = didiClient.addProject(syncReq);
            DidiProjectSyncRes didiProjectSyncRes = didiResult(result);
            // 保存关联关系
            ddRelationService.saveOrUpdateRelation(
                    projectInfo.getProjectId(),
                    DdRelationTypeEnum.PROJECT,
                    didiProjectSyncRes.getId()
            );
        }
    }


    public static <T> T didiResult(DidiResult<T> apiResult) {
        if (apiResult == null) {
            throw new ServiceException("滴滴API调用失败！");
        }
        if (apiResult.getErrno() == 0) {
            return apiResult.getData();
        } else {
            throw new ServiceException(apiResult.getErrmsg());
        }
    }


}