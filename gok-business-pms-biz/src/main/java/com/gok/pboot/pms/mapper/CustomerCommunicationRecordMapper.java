package com.gok.pboot.pms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.data.datascope.BusinessDataScope;
import com.gok.pboot.pms.entity.domain.CustomerCommunicationRecord;
import com.gok.pboot.pms.entity.dto.CustomerCommunicationRecordDTO;
import com.gok.pboot.pms.entity.vo.CustomerCommunicationRecordVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 客户沟通记录
 *
 * <AUTHOR>
 * @date 2023/11/20
 */
@Mapper
public interface CustomerCommunicationRecordMapper extends BaseMapper<CustomerCommunicationRecord> {

    /**
     * 获取客户沟通记录-分页
     *
     * @param page 分页对象
     * @param filter 客户沟通记录查询DTO
     * @return {@link Page}<{@link CustomerCommunicationRecordVO}>
     */
    @BusinessDataScope(deptOrUser = "user", scopeUserNameList = {"account_manager_id", "submitter_id"})
    Page<CustomerCommunicationRecordVO> findPage(Page page, @Param("filter") CustomerCommunicationRecordDTO filter);

    /**
     * 根据requestId获取客户沟通记录
     * @param requestId requestId
     * @return {@link CustomerCommunicationRecordVO}
     */
    CustomerCommunicationRecordVO findCustomerCommunicationRecordByRequestId(Long requestId);

    /**
     * 查询ID列表
     * @param filter 参数
     * @return 项目ID列表
     */
    List<Long> findIds(@Param("filter") Map<String, Object> filter);
}
