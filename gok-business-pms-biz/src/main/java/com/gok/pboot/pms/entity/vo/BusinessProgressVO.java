package com.gok.pboot.pms.entity.vo;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gok.pboot.pms.Util.DateCalculationUtil;
import com.gok.pboot.pms.Util.DecimalFormatUtil;
import com.gok.pboot.pms.Util.PmsDictUtil;
import com.gok.pboot.pms.entity.domain.PmsDictItem;
import com.gok.pboot.pms.enumeration.PmsDictEnum;
import com.google.common.collect.HashMultimap;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.RoundingMode;
import java.text.DecimalFormat;

/**
 * 商机进展vo
 *
 * <AUTHOR>
 * @date 2023/11/24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BusinessProgressVO {
    /**
     * 进展ID
     */
    @ExcelIgnore
    private Long id;

    /**
     * 商机ID(项目ID)
     */
    @ExcelIgnore
    private Long businessId;

    /**
     * 请求附件的requestid
     */
    @ExcelIgnore
    private Long requestId;

    /**
     * 流程相关人id
     */
    @ExcelIgnore
    private Long nodeoperator;

    /**
     * 商机名称(项目名称)
     */
    @ExcelProperty({"商机名称"})
    private String businessName;

    /**
     * 客户名称
     */
    @ExcelProperty({"客户名称"})
    private String customerName;

    /**
     * 商机进展
     */
    @ExcelProperty({"商机进展"})
    private String businessProgress;

    /**
     * 下一步工作计划
     */
    @ExcelProperty({"下一步工作计划"})
    private String nextStepForwardPlan;

    /**
     * 下一步计划完成时间
     */
    @ExcelProperty({"下一步计划完成时间"})
    private String nextStepPlanFtime;

    /**
     * 遇到的问题及所需支持(存在问题及需要协同)
     */
    @ExcelProperty({"存在问题及需要协同"})
    private String yddwtjsxdzc;

    /**
     * 商机更新时限(天)
     */
    @ExcelProperty({"商机更新时限"})
    private String businessMtime;

    /**
     * 关键决策人支持情况
     */
    @ExcelProperty({"关键决策人支持情况"})
    private String supportFromKeyDecision;

    /**
     * 项目需求是否明确
     */
    @ExcelProperty({"项目需求是否明确"})
    private String projectRequirementClear;

    /**
     * 预算情况
     */
    @ExcelProperty({"预算情况"})
    private String budgetSituation;

    /**
     * 预计签单金额
     */
    @ExcelProperty({"预计签单金额"})
    private String expectedOrderAmount;

    /**
     * 预计签约日期（预计签单时间）
     */
    @ExcelProperty({"预计签约日期"})
    private String expectedCompleteTime;

    /**
     * 商机阶段
     */
    @ExcelProperty({"商机阶段"})
    private String businessStage;

    /**
     * 项目里程碑
     */
    @ExcelProperty({"项目里程碑"})
    private String businessMilestone;

    /**
     * 内部项目类别
     */
    @ExcelProperty({"内部项目类别"})
    private String internalProjectType;

    /**
     * 是否涉及外采
     */
    @ExcelProperty({"是否涉及外采"})
    private String isExternalProcurement;

    /**
     * 采购类别
     */
    @ExcelProperty({"采购类别"})
    private String purchasingCategories;

    /**
     * 商机状态
     */
    @ExcelProperty({"商机状态"})
    private String businessStatus;

    /**
     * 项目销售人员ID(客户经理ID)
     */
    @ExcelIgnore
    private Long salesmanUserId;

    /**
     * 项目销售人员(客户经理姓名)
     */
    @ExcelProperty({"客户经理姓名"})
    private String projectSalesperson;

    /**
     * 提交人ID
     */
    @ExcelIgnore
    private Long submitUserId;

    /**
     * 提交人
     */
    @ExcelProperty({"提交人"})
    private String submitUser;

    /**
     * 提交人头像
     */
    @ExcelIgnore
    private String submitUserAvatar;

    /**
     * 提交人所在部门id
     */
    @ExcelIgnore
    private Long submitUserDeptId;

    /**
     * 提交人所在部门
     */
    @ExcelIgnore
    private String submitUserDept;

    /**
     * 提交时间
     */
    @ExcelProperty({"提交时间"})
    private String submitTime;

    /**
     * 归档时间
     */
    private String archiveTime;

    /**
     * 更新结果
     *
     * @param dictItemMap 字典项map
     * @param r 商机进展vo
     */
    public static void updateResultParam(
            HashMultimap<String, PmsDictItem> dictItemMap,
            BusinessProgressVO r
    ) {
        // 1.1 更新字典字段
        r.setBusinessStage(PmsDictUtil.getLabelFromPmsDictItems(r.getBusinessStage(), dictItemMap.get(PmsDictEnum.BUSINESS_STAGE.getValue())));
        r.setBusinessMilestone(PmsDictUtil.getLabelFromPmsDictItems(r.getBusinessMilestone(), dictItemMap.get(PmsDictEnum.BUSINESS_MILESTONE.getValue())));
        r.setSupportFromKeyDecision(PmsDictUtil.getLabelFromPmsDictItems(r.getSupportFromKeyDecision(), dictItemMap.get(PmsDictEnum.SUPPORT_FROM_KEY_DECISION.getValue())));
        r.setProjectRequirementClear(PmsDictUtil.getLabelFromPmsDictItems(r.getProjectRequirementClear(), dictItemMap.get(PmsDictEnum.PROJECT_REQUIREMENT_CLEAR.getValue())));
        r.setBudgetSituation(PmsDictUtil.getLabelFromPmsDictItems(r.getBudgetSituation(), dictItemMap.get(PmsDictEnum.BUDGET_SITUATION.getValue())));
        r.setBusinessStatus(PmsDictUtil.getLabelFromPmsDictItems(r.getBusinessStatus(), dictItemMap.get(PmsDictEnum.BUSINESS_STATUS.getValue())));

        // 1.2 对商机更新时间描述进行调整（30天内）
        String submitTime = r.getSubmitTime();
        if (ObjectUtil.isNotEmpty(submitTime)) {
            r.setBusinessMtime(DateCalculationUtil.calculateDateDiff(DateUtil.parse(submitTime)));
        }

        // 1.4 对金额相关数值进行调整
        DecimalFormat decimalFormat = new DecimalFormat("#,##0.00");
        r.setExpectedOrderAmount(DecimalFormatUtil.setAndValidate(r.getExpectedOrderAmount(),2, RoundingMode.DOWN, decimalFormat));
    }
}
