package com.gok.pboot.pms.common.join;

import com.gok.pboot.pms.entity.domain.Task;
import lombok.*;

/**
 * - 日报条目中需要携带的任务信息 -
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/8/26 9:24
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@ToString
public class TaskInDailyPaperEntry {
    /**
     * ID
     */
    private Long id;
    /**
     * 名称
     */
    private String taskName;

    public TaskInDailyPaperEntry(Task task) {
        this.id = task.getId();
        this.taskName = task.getTaskName();
    }
}
