package com.gok.pboot.pms.entity.bo;

import com.gok.pboot.pms.common.base.BaseConstants;
import com.gok.pboot.pms.cost.entity.domain.CostDeliverTask;
import com.gok.pboot.pms.entity.domain.ProjectTask;
import com.gok.pboot.pms.entity.domain.ProjectTaske;
import com.gok.pboot.pms.entity.domain.Task;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Optional;

/**
 * 新旧任务Bo类
 *
 * <AUTHOR>
 * @since 2023-08-28
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskBo {

    private Long taskId;

    private String taskName;

    private Long projectId;

    /**
     * 任务类型（0=售前支撑，1=售后交付，null=内部项目不区分）
     */
    private Integer kind;

    public static TaskBo form(Task task, ProjectTask projectTask) {
        return form(null, task, projectTask);
    }

    public static TaskBo form(Integer oldTaskFlag, Task task, ProjectTask projectTask) {
        if (!Optional.ofNullable(oldTaskFlag).isPresent()) {
            return new TaskBo();
        }
        if (BaseConstants.YES.equals(oldTaskFlag)) {
            return form(task);
        } else if (BaseConstants.NO.equals(oldTaskFlag)) {
            return form(projectTask);
        }
        return new TaskBo();
    }

    public static TaskBo form(ProjectTask task) {
        if (!Optional.ofNullable(task).isPresent()) {
            return new TaskBo();
        }
        TaskBo taskBo = TaskBo.builder()
                .taskId(task.getId())
                .taskName(task.getTitle())
                .projectId(task.getProjectId())
                .build();
        return taskBo;
    }

    public static TaskBo form(Task task) {
        if (!Optional.ofNullable(task).isPresent()) {
            return new TaskBo();
        }
        TaskBo taskBo = TaskBo.builder()
                .taskId(task.getId())
                .taskName(task.getTaskName())
                .projectId(task.getProjectId())
                .build();
        return taskBo;
    }

    /**
     * 任务Bo类构建方法
     *
     * @param projectTaske 项目任务
     * @return {@link TaskBo}
     */
    public static TaskBo form(ProjectTaske projectTaske) {
        if (!Optional.ofNullable(projectTaske).isPresent()) {
            return new TaskBo();
        }
        return TaskBo.builder()
                .taskId(projectTaske.getId())
                .taskName(projectTaske.getTitle())
                .projectId(projectTaske.getProjectId())
                .kind(projectTaske.getKind())
                .build();
    }

    /**
     * 任务Bo类构建方法
     *
     * @param costDeliverTask 成本交付任务
     * @return {@link TaskBo}
     */
    public static TaskBo form(CostDeliverTask costDeliverTask) {
        if (!Optional.ofNullable(costDeliverTask).isPresent()) {
            return new TaskBo();
        }
        return TaskBo.builder()
                .taskId(costDeliverTask.getId())
                .taskName(costDeliverTask.getTaskName())
                .projectId(costDeliverTask.getProjectId())
                .kind(costDeliverTask.getTaskType())
                .build();
    }

}
