package com.gok.pboot.pms.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserPmsDTO {


    /**
     * 用户姓名
     */
    private String name;
    /**
     * 用户姓名列表
     */
    private List<String> nameList;
    /**
     * 部门ids
     */
    private List<Long> deptIds;
    /**
     * 过滤人员列表
     */
    private List<Long> filterUserIds;
    /**
     * userIds
     */
    private List<Long> userIds;
}
