package com.gok.pboot.pms.entity.dto;

import lombok.*;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * - 按月查询日报 -
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/9/5 16:54
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class DailyPaperMonthlyDTO {
    /**
     * 开始日期
     */
    @NotBlank(message = "日期不能为空")
    private String startDate;
    /**
     * 结束日期
     */
    private String endDate;

    /**
     * 用户id
     */
    private Long userId;
    /**
     * 任务名称
     */
    private String projectName;

    /**
     * 审核状态
     * @see com.gok.pboot.pms.enumeration.ApprovalStatusEnum
     */
    @Deprecated
    private Integer approvalStatus;

    /**
     * tab页切换的审核状态
     * @see com.gok.pboot.pms.enumeration.ApprovalStatusEnum
     */
    private Integer approvalStatusTab;

    /**
     * 筛选框筛选的审核状态
     * @see com.gok.pboot.pms.enumeration.ApprovalStatusEnum
     */
    private List<Integer> approvalStatusList;
}
