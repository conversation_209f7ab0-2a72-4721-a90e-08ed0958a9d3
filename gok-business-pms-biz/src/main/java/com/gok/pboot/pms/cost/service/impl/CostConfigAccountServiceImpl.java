package com.gok.pboot.pms.cost.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.bcp.common.util.BeanUtils;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.Util.DbApiUtil;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.cost.entity.domain.CostConfigAccount;
import com.gok.pboot.pms.cost.entity.domain.CostConfigVersion;
import com.gok.pboot.pms.cost.entity.domain.CostManageEstimationResults;
import com.gok.pboot.pms.cost.entity.domain.CostManagePersonnelLevelDetail;
import com.gok.pboot.pms.cost.entity.dto.CostConfigAccountDTO;
import com.gok.pboot.pms.cost.entity.vo.CostConfigAccountVO;
import com.gok.pboot.pms.cost.enums.CostConfigVersionTypeEnum;
import com.gok.pboot.pms.cost.enums.CostTypeEnum;
import com.gok.pboot.pms.cost.mapper.CostConfigAccountMapper;
import com.gok.pboot.pms.cost.service.ICostConfigAccountService;
import com.gok.pboot.pms.enumeration.YesOrNoEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 成本科目配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Service
@RequiredArgsConstructor
public class CostConfigAccountServiceImpl extends ServiceImpl<CostConfigAccountMapper, CostConfigAccount> implements ICostConfigAccountService {


    private final CostConfigVersionServiceImpl costConfigVersionService;
    private final CostManageEstimationResultsServiceImpl costManageEstimationResultsService;
    private final CostManagePersonnelLevelDetailServiceImpl costManagePersonnelLevelDetailService;

    private final DbApiUtil dbApiUtil;

    @Value("${oa.xsfyOaId:62}")
    private Long xsfyOaId;

    @Value("${oa.xmglfysqrgOaId:8102}")
    private Long xmglfysqrgOaId;

    /**
     * 从OA同步最新成本科目配置数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertCostSubjectConfigInfo() {
        // 存储要同步的信息
        List<CostConfigAccount> newCostSubjectConfigList = new ArrayList<>(0);
        List<CostConfigAccount> saveCostConfigAccountList;
        // 获取OA最新同步数据
        List<CostConfigAccount> costSubjectConfigInfoList = dbApiUtil.getCostSubjectConfigInfoList();
        // OA数据为空则不进行处理
        if (CollUtil.isEmpty(costSubjectConfigInfoList)) {
            return;
        }
        // 获取当前版本数据
        Map<Long, CostConfigAccount> costSubjectConfigInfoMap =
                CollUtil.emptyIfNull(BeanUtils.beanListCopier(getCostConfigAccountVoList(), CostConfigAccount.class))
                        .stream()
                        .collect(Collectors.toMap(CostConfigAccount::getOaId, account -> account));

        //获取版本ID
        Long versionId = costConfigVersionService.generateVersionName(CostConfigVersionTypeEnum.CBKMPZ);
        // 数据校验
        for (CostConfigAccount costSubjectConfigInfo : costSubjectConfigInfoList) {
            newCostSubjectConfigList.add(CostConfigAccount.convert(costSubjectConfigInfoMap, costSubjectConfigInfo));
        }
        // 批量保存
        saveCostConfigAccountList = newCostSubjectConfigList.stream()
                .map(item -> {
                    CostConfigAccount saveEntity = BaseBuildEntityUtil.buildInsert(item.setVersionId(versionId));
                    CostConfigAccount existCostConfig = costSubjectConfigInfoMap.get(item.getOaId());
                    if (null != existCostConfig) {
                        saveEntity.setForEstimateFlag(existCostConfig.getForEstimateFlag());
                        saveEntity.setAccountDefinitionDesc(existCostConfig.getAccountDefinitionDesc());
                    }
                    // 售前成本科目统一处理
                    if (xsfyOaId.equals(saveEntity.getOaId()) || xmglfysqrgOaId.equals(saveEntity.getOaId())) {
                        saveEntity.setForEstimateFlag(YesOrNoEnum.NO.getValue());
                    }
                    return saveEntity;
                })
                .collect(Collectors.toList());
        // 数据进行存储
        if (CollUtil.isNotEmpty(saveCostConfigAccountList)) {
            this.saveOrUpdateBatch(saveCostConfigAccountList);
        }
    }

    /**
     * 获取成本主体配置信息
     *
     * @return {@link List }<{@link CostConfigAccountVO }>
     */
    @Override
    public List<CostConfigAccountVO> getCostSubjectConfigInfo() {
        // 获取引用信息
        List<CostManageEstimationResults> results = costManageEstimationResultsService.list();
        List<CostManagePersonnelLevelDetail> levelDetails = costManagePersonnelLevelDetailService.list();
        // 数据转换
        Map<Long, CostManageEstimationResults> accountIdEstimationMap = CollUtil.isNotEmpty(results) ?
                results.stream().filter(Objects::nonNull).filter(item -> Objects.nonNull(item.getAccountId()))
                        .collect(Collectors.toMap(CostManageEstimationResults::getAccountId, cost -> cost, (a, b) -> a)) : Collections.emptyMap();
        Map<Long, CostManagePersonnelLevelDetail> accountIdPersonnelMap = CollUtil.isNotEmpty(levelDetails) ?
                levelDetails.stream().filter(Objects::nonNull).filter(item -> Objects.nonNull(item.getAccountId()))
                        .collect(Collectors.toMap(CostManagePersonnelLevelDetail::getAccountId, detail -> detail, (a, b) -> a)) : Collections.emptyMap();
        // 获取当前版本
        List<CostConfigAccountVO> configAccountVoList = getCostConfigAccountVoList();
        // 判断是否被引用
        configAccountVoList.forEach(info -> {
            if (accountIdEstimationMap.containsKey(info.getId()) || accountIdPersonnelMap.containsKey(info.getId())) {
                info.setCite(true);
            }
        });
        return handleAccountDataToList(configAccountVoList);
    }

    /**
     * 获取 Cost Config 账户 VO 列表
     *
     * @return {@link List }<{@link CostConfigAccountVO }>
     */
    private List<CostConfigAccountVO> getCostConfigAccountVoList() {
        CostConfigVersion crrCostConfigVersion = costConfigVersionService.getCrrCostConfigVersion(CostConfigVersionTypeEnum.CBKMPZ);
        return Objects.nonNull(crrCostConfigVersion)
                ? baseMapper.getCostSubjectConfigByVersionId(crrCostConfigVersion.getId())
                : Collections.emptyList();
    }

    /**
     * 处理要列出账户数据
     *
     * @param configAccountVoList 配置账户 VO 列表
     * @return {@link List }<{@link CostConfigAccountVO }>
     */
    private static List<CostConfigAccountVO> handleAccountDataToList(List<CostConfigAccountVO> configAccountVoList) {
        return configAccountVoList.stream()
                .peek(item -> {
                    item.setAccountTypeName(EnumUtils.getNameByValue(CostTypeEnum.class, item.getAccountType()));
                    item.setForEstimateFlagName(EnumUtils.getNameByValue(YesOrNoEnum.class, item.getForEstimateFlag()));
                })
                .collect(Collectors.toList());
    }

    /**
     * 更新成本主题配置信息
     *
     * @param costConfigAccountDTOList 成本配置账户 dtoList
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCostSubjectConfigInfo(List<CostConfigAccountDTO> costConfigAccountDTOList) {
        Long versionId = costConfigVersionService.generateVersionName(CostConfigVersionTypeEnum.CBKMPZ);
        List<CostConfigAccount> costConfigAccountList = costConfigAccountDTOList.stream()
                .map(item -> {
                            CostConfigAccount saveEntity = BeanUtil.copyProperties(item, CostConfigAccount.class);
                            saveEntity.setVersionId(versionId);
                            if (xsfyOaId.equals(saveEntity.getOaId()) || xmglfysqrgOaId.equals(saveEntity.getOaId())) {
                                saveEntity.setForEstimateFlag(YesOrNoEnum.NO.getValue());
                            }
                            BaseBuildEntityUtil.buildInsert(saveEntity);
                            return saveEntity;
                        }
                )
                .collect(Collectors.toList());
        // 保存批量插入的数据
        if (CollUtil.isNotEmpty(costConfigAccountList)) {
            this.saveBatch(costConfigAccountList);
        }
    }

    /**
     * 按版本 ID 获取成本主体配置
     *
     * @param versionId 版本 ID
     * @return <{@link List }<{@link CostConfigAccountVO }>>
     */
    @Override
    public List<CostConfigAccountVO> getCostSubjectConfigByVersionId(Long versionId) {
        List<CostConfigAccountVO> costConfigAccountList = baseMapper.getCostSubjectConfigByVersionId(versionId);
        return handleAccountDataToList(costConfigAccountList);
    }

    @Override
    public Map<Long, CostConfigAccount> getMapByIds(Collection<Long> accountIdSet) {
        if (CollUtil.isNotEmpty(accountIdSet)) {
            return lambdaQuery().in(CostConfigAccount::getId, accountIdSet).list()
                    .stream().collect(Collectors.toMap(CostConfigAccount::getId, e -> e));
        }
        return Collections.emptyMap();
    }

}
