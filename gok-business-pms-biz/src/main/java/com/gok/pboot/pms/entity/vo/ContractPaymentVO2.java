package com.gok.pboot.pms.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 归属合同收款明细
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ContractPaymentVO2 {

    /**
     * 合同收款明细id
     */
    private Long id;

    /**
     * 合同编码
     */
    private String contractNumber;

    /**
     * 款项名称
     */
    private String paymentName;

    /**
     * 款项实际金额
     */
    private BigDecimal price;

    /**
     * 合同名称
     */
    private String contractName;

    /**
     * 业务板块
     */
    private Integer businessBlock;

    /**
     * 业务板块
     */
    private String businessBlockTxt;

    /**
     * 技术类型
     */
    private Integer skillType;

    /**
     * 技术类型
     */
    private String skillTypeTxt;
}
