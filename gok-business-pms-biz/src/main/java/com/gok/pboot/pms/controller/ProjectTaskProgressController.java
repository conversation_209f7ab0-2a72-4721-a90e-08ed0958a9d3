package com.gok.pboot.pms.controller;

import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.entity.dto.ProjectTaskProgressAddDTO;
import com.gok.pboot.pms.service.IProjectTaskProgressService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 项目任务进度
 *
 * <AUTHOR>
 * @version 1.1.0
 */
@RestController
@RequestMapping("/projectTaskProgress")
@AllArgsConstructor
public class ProjectTaskProgressController {

    private final IProjectTaskProgressService service;

    /**
     * 新增任务进度
     * @param dto 请求参数对象
     * @return void
     */
    @PostMapping("/add")
    public ApiResult<Void> add(@Valid @RequestBody ProjectTaskProgressAddDTO dto){
        service.add(dto);

        return ApiResult.success(null);
    }

    /**
     * 删除进展
     * @param id ID
     * @return void
     */
    @DeleteMapping("/{id}")
    public ApiResult<Void> deleteById(@PathVariable("id") Long id){
        service.deleteById(id);

        return ApiResult.success(null);
    }
}
