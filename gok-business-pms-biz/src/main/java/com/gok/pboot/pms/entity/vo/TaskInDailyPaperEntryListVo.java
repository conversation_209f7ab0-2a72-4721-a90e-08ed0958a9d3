package com.gok.pboot.pms.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 任务-项目关联vo对象
 *
 * <AUTHOR>
 * @date 2023/10/16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class TaskInDailyPaperEntryListVo {

    private Long projectId;

    private Long id;

    private String taskName;

}
