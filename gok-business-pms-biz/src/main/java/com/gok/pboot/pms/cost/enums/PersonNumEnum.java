package com.gok.pboot.pms.cost.enums;

import com.gok.pboot.pms.enumeration.ValueEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 人员数量枚举
 *
 * <AUTHOR>
 * @date 2025/01/09
 */
@AllArgsConstructor
@Getter
public enum PersonNumEnum implements ValueEnum<Integer> {

    /**
     * 单人
     */
    SINGLE(0, "单人"),

    /**
     * 多人
     */
    MULTI(1, "多人");

    private final Integer value;

    private final String name;
}
