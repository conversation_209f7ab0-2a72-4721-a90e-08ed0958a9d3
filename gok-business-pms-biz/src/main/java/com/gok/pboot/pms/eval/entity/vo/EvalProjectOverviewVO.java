package com.gok.pboot.pms.eval.entity.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gok.module.file.entity.SysFile;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 项目评价详情VO类
 *
 * <AUTHOR>
 * @date 2025/05/08
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class EvalProjectOverviewVO {

    /**
     * ID
     */
    @ExcelIgnore
    private Long id;

    /**
     * 项目ID
     */
    @ExcelIgnore
    private Long projectId;

    /**
     * 项目编号
     */
    @ExcelProperty("项目编号")
    private String projectNo;

    /**
     * 项目名称
     */
    @ExcelProperty("项目名称")
    private String projectName;

    /**
     * 整体项目评分
     */
    @ExcelProperty("项目总得分")
    private BigDecimal totalScore;

    /**
     * 项目评价系数
     */
    @ExcelProperty("项目评价系数")
    private BigDecimal evaluationCoefficient;

    /**
     * 项目等级
     * {@link com.gok.pboot.pms.eval.enums.EvalGradeEnum}
     */
    @ExcelIgnore
    private Integer projectGrade;

    /**
     * 项目等级名称
     */
    @ExcelProperty("项目等级")
    private String projectGradeName;

    /**
     * 项目经理得分
     */
    @ExcelProperty("项目经理得分")
    private BigDecimal managerScore;

    /**
     * 项目经理得分等级
     * {@link com.gok.pboot.pms.eval.enums.EvalGradeEnum}
     */
    @ExcelIgnore
    private Integer managerGrade;

    /**
     * 项目经理等级名称
     */
    @ExcelProperty("项目经理等级")
    private String managerGradeName;

    /**
     * BU ID
     */
    @ExcelIgnore
    private Long businessId;

    /**
     * BU
     */
    @ExcelProperty("BU")
    private String businessName;

    /**
     * 所属客户ID
     */
    @ExcelIgnore
    private Long unitId;

    /**
     * 所属客户
     */
    @ExcelProperty("所属客户")
    private String unitName;

    /**
     * 交付类型
     * {@link com.gok.pboot.pms.enumeration.DeliverTypeEnum}
     */
    @ExcelIgnore
    private Integer deliverType;

    /**
     * 交付类型名称
     */
    @ExcelProperty("交付形式")
    private String deliverTypeName;

    /**
     * 项目类型ID
     */
    @ExcelIgnore
    private Integer projectType;

    /**
     * 项目类型名称
     */
    @ExcelProperty("项目类型")
    private String projectTypeName;

    /**
     * 项目经理id
     */
    @ExcelIgnore
    private Long managerUserId;

    /**
     * 项目经理
     */
    @ExcelProperty("项目经理")
    private String managerUserName;

    /**
     * 客户经理id
     */
    @ExcelIgnore
    private Long salesmanUserId;

    /**
     * 客户经理
     */
    @ExcelProperty("客户经理")
    private String salesmanUserName;

    /**
     * 预算成本（万元）
     */
    @ExcelProperty("预算成本（万元）")
    private BigDecimal budgetCost;

    /**
     * 实际成本（万元）
     */
    @ExcelProperty("实际成本（万元）")
    private BigDecimal actualCost;

    /**
     * 成本偏差率
     */
    @ExcelIgnore
    private BigDecimal costDeviationRate;

    @ExcelProperty("成本偏差率")
    private String costDeviationRateStr;

    /**
     * 计划完成周期
     */
    @ExcelProperty("计划完成周期")
    private Integer planCompletionCycle;

    /**
     * 实际完成周期
     */
    @ExcelProperty("实际完成周期")
    private Integer actualCompletionCycle;

    /**
     * 计划偏差率
     */
    @ExcelIgnore
    private BigDecimal planDeviationRate;

    @ExcelProperty("计划偏差率")
    private String planDeviationRateStr;

    /**
     * 变更次数（非客户原因）
     */
    @ExcelProperty("变更次数")
    private Integer changeCount;

    /**
     * 成本指标得分
     */
    @ExcelProperty("成本指标得分")
    private BigDecimal costIndexScore;

    /**
     * 进度指标得分
     */
    @ExcelProperty("进度指标得分")
    private BigDecimal processIndexScore;

    /**
     * 质量指标得分
     */
    @ExcelProperty("质量指标得分")
    private BigDecimal qualityIndexScore;

    /**
     * 项目工作评价得分
     */
    @ExcelProperty("项目工作评价得分")
    private BigDecimal projectWorkScore;

    /**
     * 职业行为评价得分
     */
    @ExcelProperty("职业行为评价得分")
    private BigDecimal professionalBehaviorScore;

    /**
     * 客户评价得分
     */
    @ExcelIgnore
    private BigDecimal customerEvalScore;

    /**
     * 表扬信文件id
     */
    @ExcelIgnore
    private String commendationLetter;

    /**
     * 表扬信文件信息列表
     */
    @ExcelIgnore
    private List<SysFile> commendationLetterList;

    /**
     * 项目评价集合
     */
    @ExcelIgnore
    private List<EvalProjectDetailVO> evalProjectDetailList;

    /**
     * 项目经理评价集合
     */
    @ExcelIgnore
    private List<EvalProjectManagerVO> evalProjectManagerList;

    /**
     * 总支撑官/职能领导ID
     */
    @ExcelIgnore
    private Long supportManagerId;

    @ExcelIgnore
    private String supportManagerName;

    /**
     * 评价状态
     * {@link com.gok.pboot.pms.eval.enums.EvalStatusEnum}
     */
    @ExcelIgnore
    private Integer evalStatus;

    @ExcelIgnore
    private String evalStatusName;

    /**
     * 项目经理评价状态
     * {@link com.gok.pboot.pms.eval.enums.ManagerEvalStatusEnum}
     */
    @ExcelIgnore
    private Integer managerEvalStatus;

    @ExcelIgnore
    private String managerEvalStatusName;

    /**
     * 项目状态
     */
    @ExcelIgnore
    private String projectStatus;

    @ExcelIgnore
    private String projectStatusName;

    /**
     * 创建时间
     */
    @ExcelIgnore
    private Date ctime;

} 