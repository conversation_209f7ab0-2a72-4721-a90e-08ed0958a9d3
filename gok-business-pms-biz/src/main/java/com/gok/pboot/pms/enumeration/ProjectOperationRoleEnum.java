package com.gok.pboot.pms.enumeration;

import cn.hutool.core.collection.ListUtil;
import com.google.common.collect.ImmutableList;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 项目操作角色枚举
 *
 * <AUTHOR>
 * @create 2025/07/04
 **/
@Getter
@AllArgsConstructor
public enum ProjectOperationRoleEnum implements ValueEnum<Integer> {

    /**
     * 项目经理
     */
    MANAGER(0, "项目经理", Arrays.asList(ProjectOperationModuleEnum.WARRANTY, ProjectOperationModuleEnum.CLOSE)),

    /**
     * 客户经理
     */
    SALESMAN(1, "客户经理", Arrays.asList(ProjectOperationModuleEnum.WARRANTY, ProjectOperationModuleEnum.CLOSE)),

    /**
     * 项目经理直接上级
     */
    MANAGER_LEADER(2, "项目经理直接上级", ImmutableList.of()),

    /**
     * 客户经理直接上级
     */
    SALES_LEADER(3, "项目经理直接上级", ImmutableList.of()),

    /**
     * PMO
     */
    PMO(4, "PMO", ListUtil.empty());

    private final Integer value;
    private final String name;

    /**
     * 拥有确认操作权限的模块集合
     */
    private final List<ProjectOperationModuleEnum> confirmModules;

    /**
     * 获取拥有对应模块操作权限的角色数量
     *
     * @return
     */
    public static int getConfirmModuleValue(ProjectOperationModuleEnum module) {
        return Arrays.stream(ProjectOperationRoleEnum.values())
                .filter(role -> role.confirmModules.contains(module))
                .collect(Collectors.toList())
                .size();
    }

}
