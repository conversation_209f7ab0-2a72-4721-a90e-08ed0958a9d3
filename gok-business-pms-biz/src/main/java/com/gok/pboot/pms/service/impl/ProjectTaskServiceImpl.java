package com.gok.pboot.pms.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.bcp.flowable.task.common.enums.task.*;
import com.gok.bcp.flowable.task.dto.task.*;
import com.gok.bcp.flowable.task.feign.RemoteTaskService;
import com.gok.bcp.flowable.task.req.TaskRemoteInfoReq;
import com.gok.bcp.message.dto.BcpMessageTargetDTO;
import com.gok.bcp.message.entity.enums.MsgTypeEnum;
import com.gok.bcp.message.entity.enums.SourceEnum;
import com.gok.bcp.message.entity.enums.TargetTypeEnum;
import com.gok.bcp.message.entity.model.WeComModel;
import com.gok.bcp.message.feign.RemoteSendMsgService;
import com.gok.bcp.upms.feign.RemoteUserService;
import com.gok.bcp.upms.vo.SysUserVo;
import com.gok.components.common.user.PigxUser;
import com.gok.pboot.common.core.constant.SecurityConstants;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.Util.BaseEntityUtils;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.Util.PageUtils;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.common.doc.entity.Doc;
import com.gok.pboot.pms.entity.DailyPaperEntry;
import com.gok.pboot.pms.entity.domain.*;
import com.gok.pboot.pms.entity.dto.ProjectTaskDTO;
import com.gok.pboot.pms.entity.dto.ProjectTaskTimeDTO;
import com.gok.pboot.pms.entity.vo.*;
import com.gok.pboot.pms.enumeration.ProjectTaskProgressEnum;
import com.gok.pboot.pms.enumeration.ProjectTaskStateEnum;
import com.gok.pboot.pms.enumeration.TaskWorkingState;
import com.gok.pboot.pms.enumeration.YesOrNoEnum;
import com.gok.pboot.pms.mapper.*;
import com.gok.pboot.pms.service.IProjectTaskService;
import com.gok.pboot.pms.service.fegin.CenterUserService;
import com.gok.pboot.pms.service.fegin.document.IRemoteDocService;
import com.gok.wx.service.WxCpService;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Multimap;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.validation.ValidationException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 项目任务管理服务
 *
 * <AUTHOR>
 * @date 2023/8/19
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ProjectTaskServiceImpl implements IProjectTaskService {

    private final ProjectTaskMapper projectTaskMapper;
    private final ProjectTaskeMapper projectTaskeMapper;
    private final ProjectTaskProgressMapper projectTaskProgressMapper;
    private final ProjectTaskUserMapper projectTaskUserMapper;
    private final ProjectTaskAttachmentMapper projectTaskAttachmentMapper;
    private final ProjectInfoMapper projectInfoMapper;
    private final ProjectTaskProgressFeedbackMapper projectTaskProgressFeedbackMapper;
    private final IRemoteDocService remoteDocService;
    private final DailyPaperEntryMapper dailyPaperEntryMapper;
    private final WxCpService wxCpService;
    private final CenterUserService centerUserService;
    private final RemoteSendMsgService remoteSendMsgService;
    private final RemoteTaskService remoteTaskService;
    private final RemoteUserService remoteUserService;

    private final RosterMapper rosterMapper;

    private static final Long PARENT_ID = 0L;
    private static final String PARAM_PROJECT_ID = "projectId";

    @Value("${project.task.leftUrl}")
    private String taskLeftUrl;
    @Value("${project.task.midUrl}")
    private String taskMidUrl;
    @Value("${project.task.fileUrl}")
    private String fileUrl;

    @Override
    public List<ProjectTaskVO> findList(Map<String, Object> filter) {
        List<ProjectTask> taskList;
        List<ProjectTask> allTaskList;
        List<ProjectTaskVO> projectTaskVoS = new ArrayList<>();
        List<ProjectTaskVO> allProjectTaskVoS = new ArrayList<>();
        List<ProjectTaskVO> newProjectTaskVoSFilter = new ArrayList<>();
        List<ProjectTaskVO> projectTaskVoSFilter;
        Map<Long, ProjectTaskVO> projectTaskVoMap;
        Map<Long, Integer> projectIdAndProgressMap;
        List<ProjectTaskProgress> projectIdAndProgressList;
        Long state = (Long) filter.get("state");
        Long projectId = (Long) filter.get(PARAM_PROJECT_ID);
        Map<String, Object> allFilter = new HashMap<>();

        if (projectId == null || !projectInfoMapper.isExist(projectId)) {
            return ImmutableList.of();
        }
        allFilter.put(PARAM_PROJECT_ID, projectId);
        allTaskList = projectTaskMapper.findList(allFilter);
        taskList = projectTaskMapper.findList(filter);
        if (CollUtil.isEmpty(taskList)) {
            return ImmutableList.of();
        }
        // 重新判断会议状态
        for (ProjectTask task : taskList) {
            updateTaskStates(task);
        }
        for (ProjectTask allTask : allTaskList) {
            updateTaskStates(allTask);
        }

        // 构建完整任务Map，用于模糊查询时子节点获取父节点信息
        projectIdAndProgressList = projectTaskProgressMapper.getAllProgress(filter);
        //projectIdAndProgressMap = projectIdAndProgressList.stream()
        //        .collect(Collectors.toMap(
        //                ProjectTaskProgress::getTaskId,
        //                ProjectTaskProgress::getProgress,
        //                (k1, k2) -> k1 > k2 ? k1 : k2)
        //        );
        projectIdAndProgressMap = projectIdAndProgressList.stream()
                .sorted(Comparator.comparing(ProjectTaskProgress::getCtime).reversed())
                .collect(Collectors.toMap(
                        ProjectTaskProgress::getTaskId,
                        ProjectTaskProgress::getProgress,
                        (k1, k2) -> k1)
                );

        // 构建工时挂靠判断条件
        List<Long> taskIds = allTaskList.stream()
                .map(ProjectTask::getId)
                .collect(Collectors.toList());
        List<DailyPaperEntry> dailyPaperEntries = dailyPaperEntryMapper.findByTaskIds(taskIds);
        List<Long> dailyPaperEntryTaskIds = dailyPaperEntries.stream()
                .map(DailyPaperEntry::getTaskId)
                .distinct()
                .collect(Collectors.toList());

        // 构建vo
        taskList.forEach(r -> {
            ProjectTaskVO vo = ProjectTaskVO.of(r, projectIdAndProgressMap, dailyPaperEntryTaskIds);
            projectTaskVoS.add(vo);
        });
        allTaskList.forEach(r -> {
            ProjectTaskVO vo = ProjectTaskVO.of(r, projectIdAndProgressMap, dailyPaperEntryTaskIds);
            allProjectTaskVoS.add(vo);
        });

        // 如果有父级任务，添加父级任务的计划开始时间、结束时间
        projectTaskVoS.forEach(r -> {
            Long parentId = r.getParentId();
            if (parentId != 0) {
                ProjectTask parent = projectTaskMapper.findOneById(parentId);
                r.setParentExpectedStartTime(parent.getExpectedStartTime());
                r.setParentExpectedEndTime(parent.getExpectedEndTime());
            }
        });
        allProjectTaskVoS.forEach(r -> {
            Long parentId = r.getParentId();
            if (parentId != 0) {
                ProjectTask parent = projectTaskMapper.findOneById(parentId);
                r.setParentExpectedStartTime(parent.getExpectedStartTime());
                r.setParentExpectedEndTime(parent.getExpectedEndTime());
            }
        });

        // 当传入状态值，进行以下操作
        projectTaskVoMap = allProjectTaskVoS.stream()
                .collect(Collectors.toMap(ProjectTaskVO::getTaskId, projectTask -> projectTask));
        if (state != null) {
            int intState = state.intValue();
            if (intState == TaskWorkingState.DEAD_LINE.getValue()) {
                // 传入为今日到期
                projectTaskVoSFilter = getDeadLineList(projectTaskVoS);
            } else {
                // 传入为其他值
                projectTaskVoSFilter = projectTaskVoS.stream()
                        .filter(vo -> vo
                                .getStateText()
                                .equals(EnumUtils.getNameByValue(TaskWorkingState.class, intState)))
                        .collect(Collectors.toList());
            }
            // 获取需要的状态及其所有父节点
            projectTaskVoSFilter.forEach(project -> {
                String treePathIds = project.getTreePathIds();
                List<String> parentIds = Arrays.asList(treePathIds.split(","));
                parentIds.forEach(parentId -> {
                    newProjectTaskVoSFilter.add(projectTaskVoMap.get(Long.valueOf(parentId)));
                });
            });
            // 将新集合根据id去重，再次排序，防止顺序出错
            List<ProjectTaskVO> result = newProjectTaskVoSFilter.stream()
                    .distinct()
                    .sorted(Comparator.comparing(ProjectTaskVO::getTreePathIds))
                    .collect(Collectors.toList());
            return result;
        }

        // 当未传入状态值，返回所有
        projectTaskVoS.forEach(project -> {
            String treePathIds = project.getTreePathIds();
            List<String> parentIds = Arrays.asList(treePathIds.split(","));
            parentIds.forEach(parentId -> {
                newProjectTaskVoSFilter.add(projectTaskVoMap.get(Long.valueOf(parentId)));
            });
        });
        List<ProjectTaskVO> result = newProjectTaskVoSFilter.stream()
                .distinct()
                .sorted(Comparator.comparing(ProjectTaskVO::getTreePathIds))
                .collect(Collectors.toList());
        return result;
    }

    /**
     * 过滤出符合今日到期的集合元素（构建列表结构时使用）
     *
     * @param projectTaskVoS 实体对象集合
     * @return {@link List<ProjectTaskVO>}
     */
    private List<ProjectTaskVO> getDeadLineList(List<ProjectTaskVO> projectTaskVoS) {
        List<ProjectTaskVO> projectTaskVoSFilter = new ArrayList<>();
        LocalDate today = LocalDate.now();

        for (ProjectTaskVO projectTaskVo : projectTaskVoS) {
            LocalDateTime actualEndTime = projectTaskVo.getActualEndTime();
            LocalDateTime expectedEndTime = projectTaskVo.getExpectedEndTime();
            LocalDate expectedEndDate = expectedEndTime.toLocalDate();

            if (expectedEndDate.equals(today) && actualEndTime == null) {
                projectTaskVoSFilter.add(projectTaskVo);
            }
        }
        if (projectTaskVoSFilter.isEmpty()) {
            return ImmutableList.of();
        }
        return projectTaskVoSFilter;
    }

    /**
     * 过滤出符合今日到期的集合元素（构建分页树结构时使用）
     *
     * @param projectTaskPageVoS 实体对象集合
     * @return {@link List<ProjectTaskPageVO>}
     */
    private List<ProjectTaskPageVO> getDeadLineList2(List<ProjectTaskPageVO> projectTaskPageVoS) {
        List<ProjectTaskPageVO> projectTaskPageVoSFilter = new ArrayList<>();
        LocalDate today = LocalDate.now();

        for (ProjectTaskPageVO projectTaskPageVo : projectTaskPageVoS) {
            LocalDateTime actualEndTime = projectTaskPageVo.getActualEndTime();
            LocalDateTime expectedEndTime = projectTaskPageVo.getExpectedEndTime();
            LocalDate expectedEndDate = expectedEndTime.toLocalDate();

            if (expectedEndDate.equals(today) && actualEndTime == null) {
                projectTaskPageVoSFilter.add(projectTaskPageVo);
            }
        }
        if (projectTaskPageVoSFilter.isEmpty()) {
            return ImmutableList.of();
        }
        return projectTaskPageVoSFilter;
    }

    /**
     * 判断传入的任务状态，返回值为当前状态code
     */
    private Integer analyseTaskState(ProjectTask task){
        Integer TaskStates = TaskWorkingState.NOT_START.getValue();
        LocalDateTime actualStartTime = task.getActualStartTime();
        LocalDateTime actualEndTime = task.getActualEndTime();
        // 判断基本状态类型
        if (actualEndTime != null) {
            TaskStates = TaskWorkingState.FINISHED.getValue();
        }
        if (actualStartTime != null && actualEndTime == null) {
            TaskStates = TaskWorkingState.WORKING.getValue();
        }
        return TaskStates;
    }

    /**
     * 更新任务状态（调整状态优先级）
     *
     * @param task 任务信息
     */
    private static void updateTaskStates(ProjectTask task) {
        LocalDate today = LocalDate.now();
        LocalDateTime currentDateTime = LocalDateTime.now();

        LocalDateTime actualStartTime = task.getActualStartTime();
        LocalDateTime actualEndTime = task.getActualEndTime();
        LocalDateTime expectedEndTime = task.getExpectedEndTime();

        // 重新判断基本状态类型
        if (actualEndTime != null) {
            task.setState(TaskWorkingState.FINISHED.getValue());
        }
        if (actualStartTime != null && actualEndTime == null) {
            task.setState(TaskWorkingState.WORKING.getValue());
        }
        if (actualStartTime == null && actualEndTime == null) {
            task.setState(TaskWorkingState.NOT_START.getValue());
        }
        if (currentDateTime.isAfter(expectedEndTime) && actualEndTime == null && actualStartTime == null) {
            task.setState(TaskWorkingState.DELAY.getValue());
        }

    }

    @Override
    @Transactional
    public Long save(ProjectTaskDTO request) {
        ProjectTask projectTask;
        Long taskId;
        Long projectId;
        List<Long> userIds;
        List<Long> fileIds;
        List<ProjectTaskAttachment> ProjectTaskAttachments = new ArrayList<>();

        projectId = request.getProjectId();
        if (projectId == null || !projectInfoMapper.isExist(projectId)) {
            throw new ValidationException("项目不存在");
        }

        projectTask = ProjectTask.save(request);
        BaseBuildEntityUtil.buildInsert(projectTask);
        taskId = projectTask.getId();
        // 获取状态
        updateTaskStates(projectTask);
        Long parentId = projectTask.getParentId();
        if (parentId != null) {
            List<Long> parentToSubtaskIds;
            // 获取全路径和层级
            parentToSubtaskIds = collectParentToSubtaskIds(projectTask);
            projectTask.setTreePathIds(Joiner.on(',').skipNulls().join(parentToSubtaskIds));
            projectTask.setTreeLevel(parentToSubtaskIds.size());
        } else {
            projectTask.setParentId(0L);
            projectTask.setTreePathIds(projectTask.getId().toString());
            projectTask.setTreeLevel(1);
        }

        // 用户表操作
        userIds = request.getUserIds();
        if (userIds != null && !userIds.isEmpty()) {
            List<String> userNames = request.getUserNames();
            List<ProjectTaskUser> projectTaskUserList = new ArrayList<>();

            for (int i = 0; i < userIds.size(); i++) {
                ProjectTaskUser user = ProjectTaskUser.save(userIds.get(i), userNames.get(i), taskId);
                projectTaskUserList.add(user);
            }
            for (ProjectTaskUser projectTaskUser : projectTaskUserList) {
                BaseBuildEntityUtil.buildInsert(projectTaskUser);
                projectTaskUserMapper.save(projectTaskUser);
            }
        }

        // 文件表操作
        fileIds = request.getFileIds();
        if (!CollectionUtils.isEmpty(fileIds)) {
            List<Doc> data = remoteDocService.findDocListInner(fileIds, SecurityConstants.FROM_IN).getData();
            if (CollectionUtils.isEmpty(data)) {
                throw new ValidationException("文件不存在");
            }
            Map<Long, String> docNameMap = data.stream()
                    .collect(Collectors.toMap(Doc::getId, Doc::getDocName));
            for (Long fileId : fileIds) {
                ProjectTaskAttachment attachment;
                attachment = ProjectTaskAttachment.of(taskId, projectId, fileId, docNameMap);
                BaseBuildEntityUtil.buildInsert(attachment);
                ProjectTaskAttachments.add(attachment);
                projectTaskAttachmentMapper.save(attachment);
            }
        }

        projectTaskMapper.save(projectTask);

        // 新建任务时，进行任务信息推送
        sendTaskMsgOfSaveOrUpdate(projectTask,ProjectTaskAttachments);
        // 若该任务有父任务，同步推送父任务的关联id
        if (ObjectUtils.isNotEmpty(parentId) && !parentId.equals(0L)) {
            updateRelateTaskIdList(parentId, taskId, false);
        }

        return taskId;
    }

    /**
     * 更新中台父级任务的关联任务id列表
     * @param parentId 父级任务id
     */
    private void updateRelateTaskIdList(Long parentId, Long taskId, Boolean isDelete) {
        List<String> childIdList = projectTaskMapper.getChildIdList(parentId);
        // 赋值通用信息
        RelateTaskSyncDto relateTaskSyncDto = new RelateTaskSyncDto();
        relateTaskSyncDto.setSourceEnum(SourceEnum.PROJECT);
        relateTaskSyncDto.setThirdTaskId(String.valueOf(parentId));
        // 赋值关联id信息，根据是否为【删除任务】行为判断
        if (isDelete) {
            if (ObjectUtils.isNotEmpty(childIdList)){
                childIdList.remove(String.valueOf(taskId));
            }
        } else {
            childIdList.add(String.valueOf(taskId));
        }
        relateTaskSyncDto.setThirdTaskIdList(childIdList);
        remoteTaskService.relateTaskSync(relateTaskSyncDto);
    }

    /**
     * 新建或编辑任务时，进行 中台消息推送
     * @param projectTask 任务信息
     */
    private void sendTaskMsgOfSaveOrUpdate(ProjectTask projectTask,List<ProjectTaskAttachment> ProjectTaskAttachments) {
        // 推送数据：任务来源，任务类型，任务名称，任务描述，关联类型，优先级，该任务详情地址，状态，开始时间，截止时间，所属项目id，所属项目名
        Long taskId = projectTask.getId();
        LocalDateTime actualEndTime = projectTask.getActualEndTime();
        LocalDateTime actualStartTime = projectTask.getActualStartTime();
        Long managerUserId = projectTask.getManagerUserId();
        String managerUserName = projectTask.getManagerUserName();

        TaskSaveDto taskSaveDto = new TaskSaveDto();
        taskSaveDto.setThirdTaskId(String.valueOf(taskId));
        taskSaveDto.setSourceEnum(SourceEnum.PROJECT);
        taskSaveDto.setTypeEnum(TaskTypeEnum.PROJECT);
        taskSaveDto.setJobTypeEnum(JobTypeEnum.TASK);
        taskSaveDto.setRelateTypeEnum(RelateTypeEnum.PROJECT);
        taskSaveDto.setPriorityEnum(PriorityEnum.MEDIUM);
        // 赋值任务状态
        if (actualEndTime != null) {
            taskSaveDto.setStatusEnum(TaskStatusEnum.COMPLETED);
        }
        if (actualStartTime != null && actualEndTime == null) {
            taskSaveDto.setStatusEnum(TaskStatusEnum.IN_PROGRESS);
        }
        if (actualStartTime == null && actualEndTime == null) {
            taskSaveDto.setStatusEnum(TaskStatusEnum.NOT_STARTED);
        }
        taskSaveDto.setName(projectTask.getTitle());
        taskSaveDto.setDescription(Strings.nullToEmpty(projectTask.getContent()));

        // Nacos 配置对应的任务跳转全路径，动态拼接获取
        String rightUrl = String.valueOf(taskId);
        taskSaveDto.setJumpLink(taskLeftUrl + taskMidUrl + rightUrl);

        if (ObjectUtils.isNotEmpty(ProjectTaskAttachments)) {
            // Nacos配置 对应的任务文件下载全路径，动态拼接获取
            List<String> FileUrlList = new ArrayList<>();
            ProjectTaskAttachments.forEach(p-> FileUrlList.add(taskLeftUrl + fileUrl + p.getFileId().toString()));
            taskSaveDto.setFileUrlList(FileUrlList);
        }

        taskSaveDto.setReminderEnumList(Lists.newArrayList(ReminderTypeEnum.TODO));
        taskSaveDto.setStartDate(projectTask.getExpectedStartTime());
        taskSaveDto.setEndDate(projectTask.getExpectedEndTime());
        taskSaveDto.setRelateId(String.valueOf(projectTask.getProjectId()));
        ProjectInfo projectInfo = projectInfoMapper.selectById(projectTask.getProjectId());
        taskSaveDto.setRelateName(projectInfo.getItemName());
        // 负责人信息
        List<LeaderDto> leaderDtos = new ArrayList<>();
        LeaderDto leaderDto = new LeaderDto();
        leaderDto.setLeaderId(managerUserId);
        leaderDto.setLeader(managerUserName);
        leaderDtos.add(leaderDto);
        taskSaveDto.setLeaderList(leaderDtos);
        // 抄送人信息（要通知到的人，当前赋值为任务负责人）
        List<CopyForDto> copyForDtos = new ArrayList<>();
        CopyForDto copyForDto = new CopyForDto();
        copyForDto.setCopyForId(managerUserId);
        copyForDto.setCopyForName(managerUserName);
        copyForDtos.add(copyForDto);
        taskSaveDto.setCopyForList(copyForDtos);

        taskSaveDto.setCreatorId(projectTask.getCreatorId());
        taskSaveDto.setCreator(projectTask.getCreator());
        remoteTaskService.saveOrUpdate(taskSaveDto);
    }

    /**
     * 获取父集到子集的id集合
     *
     * @param task ProjectTask
     */
    private List<Long> collectParentToSubtaskIds(ProjectTask task) {
        List<Long> result = new ArrayList<>();

        // 将 ID 添加到结果列表的开头
        result.add(0, task.getId());
        ProjectTask parent = projectTaskMapper.findOneById(task.getParentId());
        if (parent != null) {
            result.add(0, parent.getId());
            ProjectTask parent2 = projectTaskMapper.findOneById(parent.getParentId());
            if (parent2 != null) {
                result.add(0, parent2.getId());
            }
        }
        return result;
    }

    @Override
    @Transactional
    public Long update(ProjectTaskDTO request) {
        Long taskId;
        Long projectId;
        ProjectTask projectTask;
        ProjectTask update;
        List<Long> userIds;
        List<Long> fileIds;
        List<ProjectTaskAttachment> projectTaskAttachments = new ArrayList<>();

        taskId = request.getTaskId();
        projectTask = projectTaskMapper.findOneById(taskId);
        if (projectTask == null) {
            throw new ValidationException("任务不存在");
        }

        projectId = projectTask.getProjectId();
        update = ProjectTask.update(request);
        update.setProjectId(request.getProjectId());
        BaseBuildEntityUtil.buildUpdate(update);
        userIds = request.getUserIds();
        if (userIds == null || userIds.isEmpty()) {
            projectTaskUserMapper.delete(taskId);
        }
        if (userIds != null && !userIds.isEmpty()) {
            List<String> userNames = request.getUserNames();
            List<ProjectTaskUser> projectTaskUserList = new ArrayList<>();
            ProjectTaskUser user;

            // 插入用户名
            for (int i = 0; i < userIds.size(); i++) {
                user = ProjectTaskUser.save(userIds.get(i), userNames.get(i), taskId);
                projectTaskUserList.add(user);
            }
            projectTaskUserMapper.delete(taskId);
            for (ProjectTaskUser projectTaskUser : projectTaskUserList) {
                BaseBuildEntityUtil.buildInsert(projectTaskUser);
                projectTaskUserMapper.save(projectTaskUser);
            }
        }

        fileIds = request.getFileIds();
        if (CollectionUtils.isEmpty(fileIds)) {
            projectTaskAttachmentMapper.delete(taskId);
        }
        if (!CollectionUtils.isEmpty(fileIds)) {
            List<Doc> data;
            Map<Long, String> docNameMap;

            data = remoteDocService.findDocListInner(fileIds, SecurityConstants.FROM_IN).getData();
            if (CollectionUtils.isEmpty(data)) {
                throw new ValidationException("文件不存在");
            }
            docNameMap = data.stream().collect(Collectors.toMap(Doc::getId, Doc::getDocName));
            projectTaskAttachmentMapper.delete(taskId);
            for (Long fileId : fileIds) {
                ProjectTaskAttachment attachment;
                attachment = ProjectTaskAttachment.of(taskId, projectId, fileId, docNameMap);
                BaseBuildEntityUtil.buildInsert(attachment);
                projectTaskAttachments.add(attachment);
                projectTaskAttachmentMapper.save(attachment);
            }
        }
        projectTaskMapper.update(update);

        // 编辑任务时，进行任务信息推送
        sendTaskMsgOfSaveOrUpdate(update,projectTaskAttachments);

        return taskId;
    }

    @Override
    @Transactional
    public Boolean delete(Long id) {
        ProjectTask projectTask;
        int childCount;

        projectTask = projectTaskMapper.findOneById(id);
        if (projectTask == null) {
            throw new ValidationException("任务不存在，无法删除");
        }
        childCount = projectTaskMapper.getChildCount(id);
        if (childCount > 1) {
            throw new ValidationException("任务有子任务，无法删除");
        }
        Boolean exists = dailyPaperEntryMapper.existsByTaskIds(id);
        if(exists){
            throw new ValidationException("任务存在工时挂靠，无法删除");
        }

        // 删除任务时，进行任务信息推送
        sendTaskMsgOfDelete(id);
        // 若该任务有父任务，同步推送父任务的关联id
        Long parentId = projectTask.getParentId();
        if (ObjectUtils.isNotEmpty(parentId) && !parentId.equals(0L)) {
            updateRelateTaskIdList(parentId, id, true);
        }

        projectTaskMapper.delete(id);
        projectTaskUserMapper.delete(id);
        projectTaskAttachmentMapper.delete(id);
        return Boolean.TRUE;
    }

    /**
     * 删除任务时，进行中台消息推送
     * @param id 任务id
     */
    private void sendTaskMsgOfDelete(Long id) {
        TaskRemoteInfoReq taskRemoteInfoReq = new TaskRemoteInfoReq();
        taskRemoteInfoReq.setSourceEnum(SourceEnum.PROJECT);
        taskRemoteInfoReq.setThirdTaskId(String.valueOf(id));
        remoteTaskService.taskRemove(taskRemoteInfoReq);
    }

    @Override
    public ProjectTaskDetailsVO findDetailsById(Long id) {
        ProjectTask task = projectTaskMapper.selectById(id);
        ProjectInfo projectInfo;
        Long taskId;
        Long parentTaskId;
        String parentTaskName = null;
        List<ProjectTaskUser> members;
        List<ProjectTaskAttachment> attachments;
        List<ProjectTaskProgress> progresses;
        Multimap<Long, ProjectTaskProgressFeedback> progressIdAndFeedBackMap;
        ImmutableMultimap.Builder<Long, ProjectTaskProgressFeedback> progressIdAndFeedBackMapBuilder;

        if (task == null) {
            return null;
        }
        taskId = task.getId();
        parentTaskId = task.getParentId();
        if (parentTaskId != 0) {
            parentTaskName = projectTaskMapper.findTitleById(parentTaskId);
        }
        projectInfo = projectInfoMapper.selectById(task.getProjectId());
        members = projectTaskUserMapper.findByTaskId(taskId);
        attachments = projectTaskAttachmentMapper.findByTaskId(taskId);
        progresses = projectTaskProgressMapper.findByTaskId(taskId);
        if (progresses.isEmpty()) {
            progressIdAndFeedBackMap = ImmutableMultimap.of();
        } else {
            progressIdAndFeedBackMapBuilder = ImmutableMultimap.builder();
            projectTaskProgressFeedbackMapper.findByProgressIds(BaseEntityUtils.mapToIdList(progresses))
                    .forEach(fb -> progressIdAndFeedBackMapBuilder.put(fb.getProgressId(), fb));
            progressIdAndFeedBackMap = progressIdAndFeedBackMapBuilder.build();
        }

        return ProjectTaskDetailsVO.of(
                task, parentTaskName, projectInfo, members, attachments, progresses, progressIdAndFeedBackMap,
                SecurityUtils.getUser().getId()
        );
    }

    @Override
    public ProjectTaskEditVo findOne(Long id) {
        ProjectTask task = projectTaskMapper.selectById(id);
        String parentTaskName = null;
        Long taskId;
        Long parentTaskId;
        List<ProjectTaskUser> members;
        List<ProjectTaskAttachment> attachments;

        if (task == null) {
            return null;
        }
        taskId = task.getId();
        parentTaskId = task.getParentId();
        if (parentTaskId != 0) {
            parentTaskName = projectTaskMapper.findTitleById(parentTaskId);
        }
        members = projectTaskUserMapper.findByTaskId(taskId);
        attachments = projectTaskAttachmentMapper.findByTaskId(taskId);
        return ProjectTaskEditVo.of(task, parentTaskName, members, attachments);
    }

    @Override
    public ProjectTaskStateVO findStateCount(Map<String, Object> filter) {
        ProjectTaskStateVO projectTaskStateVO;
        Long projectId = (Long) filter.get(PARAM_PROJECT_ID);
        List<ProjectTask> taskList;

        if (projectId == null) {
            throw new ValidationException("请输入项目id");
        }
        taskList = projectTaskMapper.findList(filter);
        if (CollUtil.isEmpty(taskList)) {
            return null;
        }
        // 更新状态值
        for (ProjectTask task : taskList) {
            updateTaskStates(task);
        }
        projectTaskStateVO = ProjectTaskStateVO.of(taskList);
        return projectTaskStateVO;
    }

    @Override
    public Page<ProjectTaskPageVO> findPage(PageRequest pageRequest, Map<String, Object> filter) {
        Long projectId = (Long) filter.get(PARAM_PROJECT_ID);
        Long state = (Long) filter.get("state");
        List<ProjectTask> projectTasks;
        List<ProjectTaskPageVO> copyProjectTaskPageVoS;
        List<ProjectTaskPageVO> projectTaskPageVoSFilter = new ArrayList<>();
        List<ProjectTaskPageVO> rootNodes = new ArrayList<>();
        List<ProjectTaskPageVO> projectTaskPageVoS = new ArrayList<>();
        List<ProjectTaskPageVO> newProjectTaskPageVoS = new ArrayList<>();
        Map<Long, ProjectTaskPageVO> nodeMap = new HashMap<>();
        Map<Long, ProjectTaskPageVO> projectTaskPageVOMap;

        if (projectId == null) {
            return new Page<>();
        }

        // 获取数据，转换成vo
        projectTasks = projectTaskMapper.findList(filter);
        projectTasks.forEach(projectTask -> {
            updateTaskStates(projectTask);
            projectTaskPageVoS.add(ProjectTaskPageVO.of(projectTask));
        });
        copyProjectTaskPageVoS = projectTaskPageVoS;

        // 若传入的state不为空，则进行筛选逻辑
        projectTaskPageVOMap = projectTaskPageVoS.stream()
                .collect(Collectors.toMap(ProjectTaskPageVO::getTaskId, projectTask -> projectTask));
        if (state != null) {
            int intState = state.intValue();
            if (intState == TaskWorkingState.DEAD_LINE.getValue()) {
                // 传入为今日到期
                projectTaskPageVoSFilter = getDeadLineList2(projectTaskPageVoS);
            } else {
                // 传入为其他值
                projectTaskPageVoSFilter = projectTaskPageVoS.stream()
                        .filter(vo -> vo
                                .getStateText()
                                .equals(EnumUtils.getNameByValue(TaskWorkingState.class, intState)))
                        .collect(Collectors.toList());
            }
            // 获取需要的状态及其所有父节点
            projectTaskPageVoSFilter.forEach(project -> {
                String treePathIds = project.getTreePathIds();
                List<String> parentIds = Arrays.asList(treePathIds.split(","));
                parentIds.forEach(parentId -> {
                    newProjectTaskPageVoS.add(projectTaskPageVOMap.get(Long.valueOf(parentId)));
                });
            });
            // 将新集合根据id去重，按照tree_path_ids再次排序，防止顺序出错
            copyProjectTaskPageVoS = newProjectTaskPageVoS.stream()
                    .distinct()
                    .sorted(Comparator.comparing(ProjectTaskPageVO::getTreePathIds))
                    .collect(Collectors.toList());
        }

        // 构建树结构
        copyProjectTaskPageVoS.forEach(node -> {
            nodeMap.put(node.getTaskId(), node);
            if (node.getParentId().equals(PARENT_ID)) {
                rootNodes.add(node);
            }
        });
        copyProjectTaskPageVoS.forEach(node -> {
            Long parentId = node.getParentId();
            if (!parentId.equals(PARENT_ID)) {
                ProjectTaskPageVO parent = nodeMap.get(parentId);
                if (parent != null) {
                    parent.getChildren().add(node);
                }
            }
        });

        return PageUtils.page(rootNodes, pageRequest);
    }

    @Override
    public ApiResult<Integer> findChargeTaskNum() {
        Long id = SecurityUtils.getUser().getId();
        Integer myTaskNum = projectTaskMapper.findChargeTaskNum(id);

        if (YesOrNoEnum.NO.getValue().equals(myTaskNum)) {
            return ApiResult.success(YesOrNoEnum.NO.getValue());
        }
        return ApiResult.success(myTaskNum);
    }

    @Override
    public Page<ProjectTaskOfChargeVO> findPageOfCharge(PageRequest pageRequest, Map<String, Object> filter) {
        Long chargeUserId = SecurityUtils.getUser().getId();
        List<ProjectTask> projectTasks;
        List<ProjectTaskOfChargeVO> result = new ArrayList<>();

        filter.put("chargeUserId", chargeUserId);
        projectTasks = projectTaskMapper.findChargeList(filter);
        // 更新状态
        for (ProjectTask projectTask : projectTasks) {
            updateTaskStates(projectTask);
        }
        // 获取list
        projectTasks.forEach(projectTask -> {
            result.add(ProjectTaskOfChargeVO.of(projectTask));
        });
        // 补全字段
        result.forEach(re -> {
            String itemName;
            List<ProjectTaskProgress> progresses;
            Optional<Integer> max;
            Integer maxProgress;

            itemName = projectInfoMapper.selectById(re.getProjectId()).getItemName();
            re.setProjectName(itemName);
            // 补全进度字段（String）
            progresses = projectTaskProgressMapper.findByTaskId(re.getTaskId());
            max = progresses.stream()
                    .sorted(Comparator.comparing(ProjectTaskProgress::getCtime).reversed())
                    .map(ProjectTaskProgress::getProgress)
                    .findFirst();
            maxProgress = max.orElse(null);
            if (maxProgress != null) {
                re.setProgress(EnumUtils.getNameByValue(ProjectTaskProgressEnum.class, maxProgress));
            }
        });

        return PageUtils.page(result, pageRequest);
    }

    @Override
    public Long updateTaskTime(ProjectTaskTimeDTO dto) {
        ProjectTask projectTask = ProjectTask.updateTime(dto);
        BaseBuildEntityUtil.buildUpdate(projectTask);
        projectTaskMapper.updateById(projectTask);

        // 任务状态变更时，进行任务信息推送
        sendTaskMsgOfStateChange(projectTask);

        return projectTask.getId();
    }

    /**
     * 任务状态变更时，进行中台消息推送
     */
    private void sendTaskMsgOfStateChange(ProjectTask projectTask) {
        TaskStatusSyncDto taskStatusSyncDto = new TaskStatusSyncDto();
        taskStatusSyncDto.setSourceEnum(SourceEnum.PROJECT);
        taskStatusSyncDto.setThirdTaskId(String.valueOf(projectTask.getId()));
        if (ObjectUtils.isNotEmpty(projectTask.getActualStartTime())) {
            taskStatusSyncDto.setTaskStatusEnum(TaskStatusEnum.IN_PROGRESS);
        } else {
            taskStatusSyncDto.setTaskStatusEnum(TaskStatusEnum.COMPLETED);
        }
        remoteTaskService.statusSync(taskStatusSyncDto);
    }

    @Override
    public Long rollbackEndTime(Long id) {
        projectTaskMapper.rollbackEndTime(id);

        // 任务状态回滚（已完成->进行中）时，进行任务信息推送
        sendTaskMsgOfStateRollback(id);

        return id;
    }

    /**
     * 任务从已完成状态回滚时，进行中台消息推送
     * @param id 任务id
     */
    private void sendTaskMsgOfStateRollback(Long id) {
        TaskStatusSyncDto taskStatusSyncDto = new TaskStatusSyncDto();
        taskStatusSyncDto.setSourceEnum(SourceEnum.PROJECT);
        taskStatusSyncDto.setThirdTaskId(String.valueOf(id));
        taskStatusSyncDto.setTaskStatusEnum(TaskStatusEnum.IN_PROGRESS);
        remoteTaskService.statusSync(taskStatusSyncDto);
    }

    @Override
    public void deadLineNotify() {
        // 获取的即将结束任务列表，并设置定时任务
        List<DeadLineNotifyVO> deadLineNotifyVoS = new ArrayList<>();
        List<ProjectTaske> projectTasks;
        LocalDate currentDate = LocalDate.now();
//        Map<Long, String> userIdToNameMap;
//        UserPmsDTO userPmsDTO = new UserPmsDTO();
//        List<SysUserOutVO> sysUserOutVOList;
        // 获取中台人员名称对应人员id映射关系
        Map<String, Long> userMap = remoteUserService.getAllUser().getData()
                .stream()
                .collect(Collectors.toMap(SysUserVo::getUsername, SysUserVo::getUserId, (a, b) -> a));
        // 获取项目周报内容
        Map<Long, ProjectInfo> projectInfoMap = projectInfoMapper.selectList(null).stream()
                .collect(Collectors.toMap(ProjectInfo::getId, p -> p, (a, b) -> a));

        projectTasks = projectTaskeMapper.selectList(null);
        projectTasks.forEach(projectTask -> {
            LocalDate expectEndDate = projectTask.getExpectEndDate();
            if (ProjectTaskStateEnum.NORMAL.getValue().equals(projectTask.getState()) && expectEndDate != null) {
                if (expectEndDate.equals(currentDate)
                        || expectEndDate.isAfter(currentDate.minusDays(3))
                        && expectEndDate.isBefore(currentDate)) {
                    String projectName;
                    DeadLineNotifyVO vo;
                    String title = projectTask.getTitle();
                    Long projectId = projectTask.getProjectId();
                    if (ObjectUtils.isNotEmpty(projectTask.getLeaderNames())) {
                        String[] leaderNames = projectTask.getLeaderNames().split(",");
                        for (String leaderName : leaderNames) {
                            projectName = projectInfoMap.get(projectId).getItemName();
                            vo = DeadLineNotifyVO.of(projectName, title, userMap.get(leaderName), leaderName);
                            deadLineNotifyVoS.add(vo);
                        }
                    }
                }
            }
        });

//        sysUserOutVOList = centerUserService.getUserListByMultiParameterPms(userPmsDTO).unpack().orElse(ImmutableList.of());
//        userIdToNameMap = sysUserOutVOList.stream()
//                .filter(u -> u.getUserId() != null && u.getName() != null)
//                .collect(Collectors.toMap(SysUserOutVO::getUserId, SysUserOutVO::getName,(k1, k2) -> k1));
        // 获取用户id-name map
        Map<Long, String> userIdToNameMap = rosterMapper.selectList(null)
                .stream().collect(Collectors.toMap(Roster::getId, Roster::getAliasName));
        // 发送企业微信通知（通过中台接口）
        for (DeadLineNotifyVO deadLineNotifyVo : deadLineNotifyVoS) {
            sendMsgToWeCom(userIdToNameMap, deadLineNotifyVo);
        }

    }

    @Override
    public void updateStatusToDelay() {
        LocalDateTime currentDateTime = LocalDateTime.now();
        // 获取所有状态为‘未开始’的任务
        List<ProjectTask> notStartTaskList = projectTaskMapper.getAllNotStartTask();
        List<ProjectTask> delayTaskList = new ArrayList<>();
        // 判断这些任务是否满足‘已延期’
        if (CollectionUtil.isNotEmpty(notStartTaskList)) {
            notStartTaskList.forEach(task -> {
                LocalDateTime actualStartTime = task.getActualStartTime();
                LocalDateTime actualEndTime = task.getActualEndTime();
                LocalDateTime expectedEndTime = task.getExpectedEndTime();
                if (currentDateTime.isAfter(expectedEndTime) && actualEndTime == null && actualStartTime == null) {
                    delayTaskList.add(task);
                }
            });
        }

        // 将满足条件的任务状态推送到中台
        if(CollectionUtil.isNotEmpty(delayTaskList)){
            delayTaskList.forEach(task -> {
                TaskStatusSyncDto taskStatusSyncDto = new TaskStatusSyncDto();
                taskStatusSyncDto.setSourceEnum(SourceEnum.PROJECT);
                taskStatusSyncDto.setThirdTaskId(String.valueOf(task.getId()));
                taskStatusSyncDto.setTaskStatusEnum(TaskStatusEnum.DELAYED);
                remoteTaskService.statusSync(taskStatusSyncDto);
            });
        }

    }

    /**
     * 发送企业微信通知
     * @param userIdToNameMap 用户姓名映射
     * @param deadLineNotifyVo 传输信息
     */
    private void sendMsgToWeCom(Map<Long, String> userIdToNameMap, DeadLineNotifyVO deadLineNotifyVo) {
        PigxUser user = SecurityUtils.getUser();
        Long userId = deadLineNotifyVo.getManagerUserId();
        String userName = userIdToNameMap.get(userId);
        if (userId == null || userName == null) {
            return;
        }
        List<BcpMessageTargetDTO> targetList = new ArrayList<>(1);
        targetList.add(new BcpMessageTargetDTO(String.valueOf(userId), Optional.ofNullable(userName).orElse("defaultName"), null));
        // 设置传输信息
        WeComModel weComModel = new WeComModel();
        weComModel.setSource(SourceEnum.PROJECT.getValue());
        weComModel.setType(MsgTypeEnum.WARN_MSG.getValue());
        weComModel.setTitle("任务即将超期发送企业微信通知");
        weComModel.setContent(deadLineNotifyVo.getInfo());
        if (user == null) {
            weComModel.setSenderId(10000L);
            weComModel.setSender("admin");
        } else {
            weComModel.setSenderId(user.getId());
            weComModel.setSender(user.getName());
        }
        weComModel.setTargetType(TargetTypeEnum.USERS.getValue());
        weComModel.setTargetList(targetList);
        // 发送企微消息
        try {
            remoteSendMsgService.sendMsg(weComModel);
        } catch (Exception e) {
            log.info("中台企业微信消息推送失败: {}", e.getMessage());
            e.printStackTrace();
        }
    }


}
