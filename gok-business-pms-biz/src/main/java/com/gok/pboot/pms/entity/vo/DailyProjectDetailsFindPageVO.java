package com.gok.pboot.pms.entity.vo;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * @Auther chenhc
 * @Date 2022-09-01 10:33
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class DailyProjectDetailsFindPageVO {
    /**
     * ID
     */
    private Long id;
    /**
     * 项目ID
     */
    private Long projectId;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 提交人姓名
     */
    private String userRealName;
    /**
     * 任务名称
     */
    private String taskName;
    /**
     * 提交日期
     */
    private LocalDate submissionDate;

    /**
     * 填报状态（0=正常，1=滞后）
     */
    private String fillingStateName;
    /**
     * 正常工时
     */
    private BigDecimal normalHours;

    /**
     * 加班工时
     */
    private BigDecimal addedHours;

    /**
     * 描述
     */
    private String description;

    /**
     * 审核状态（0=未提交，1=已退回，2=待审核，3=不通过，4=已通过）
     */
    private Integer approvalStatus;

    /**
     * 审核状态（0=未提交，1=已退回，2=待审核，3=不通过，4=已通过）
     */
    private String approvalStatusName;

    /**
     * 审核人
     */
    private String approvalName;

    /**
     * 日报ID
     */
    private Long dailyPaperId;

    /**
     * 不通过原因
     */
    private String approvalReason;

    /**
     * 工时类型
     */
    private Integer workType;

    /**
     * 昨日计划
     */
    private String yesterdayPlan;

    /**
     * 是否可编辑 ,1可0不可
     * {@link com.gok.pboot.enumeration.entity.YesOrNoEnum}
     */
    private Integer isEdit;
}
