package com.gok.pboot.pms.cost.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.cost.entity.domain.CostExpensesShare;
import com.gok.pboot.pms.cost.entity.dto.CostExpensesShareListDto;
import com.gok.pboot.pms.cost.entity.vo.CostExpensesShareListVO;

import javax.servlet.http.HttpServletResponse;

/**
 * 交付管理费用分摊 服务类
 * <AUTHOR>
 */
public interface ICostExpensesShareService extends IService<CostExpensesShare> {

    /**
     * 费用分摊列表
     * @param dto
     * @return
     */
    CostExpensesShareListVO findExpensesShare(CostExpensesShareListDto dto);

    /**
     * 导出费用分摊
     * @param dto
     * @param response
     * @return
     */
    void exportExpensesShare(HttpServletResponse response, CostExpensesShareListDto dto);
}
