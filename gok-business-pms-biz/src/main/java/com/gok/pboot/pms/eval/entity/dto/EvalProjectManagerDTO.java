package com.gok.pboot.pms.eval.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 项目经理评分DTO
 *
 * <AUTHOR>
 * @date 2025/05/15
 */
@Data
@ApiModel("项目经理评分DTO")
public class EvalProjectManagerDTO {

    /**
     * 项目经理评价ID
     */
    @NotNull(message = "项目经理评价ID不能为空")
    @ApiModelProperty("项目经理评价ID")
    private Long id;

    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空")
    private Long projectId;

    /**
     * 项目整体评价表ID
     */
    @NotNull(message = "项目整体评价表ID不能为空")
    private Long overviewId;

    /**
     * 评定项目
     */
    @NotNull(message = "评定项目不能为空")
    private Integer assessmentProject;

    /**
     * 总支撑官/职能领导评分
     */
    private BigDecimal managerScore;

    /**
     * PMO评分
     */
    private BigDecimal pmoScore;

} 