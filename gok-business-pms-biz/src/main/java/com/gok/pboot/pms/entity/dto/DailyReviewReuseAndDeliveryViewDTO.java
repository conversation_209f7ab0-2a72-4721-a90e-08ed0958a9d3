package com.gok.pboot.pms.entity.dto;

import com.gok.pboot.pms.common.base.PageRequest;
import lombok.*;

/**
 * 工时审核 复用+交付 条目查询
 *
 * <AUTHOR>
 * @version 1.3.2
 */
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class DailyReviewReuseAndDeliveryViewDTO extends PageRequest {

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 审批状态 0待审批 1已审批
     * @see com.gok.pboot.pms.enumeration.YesOrNoEnum
     */
    private Integer auditStatus;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 开始时间
     * eg. 2023-12
     */
    private String startTime;

    /**
     * 结束时间
     * eg. 2024-01
     */
    private String endTime;

    /**
     * 姓名
     */
    private String username;

}
