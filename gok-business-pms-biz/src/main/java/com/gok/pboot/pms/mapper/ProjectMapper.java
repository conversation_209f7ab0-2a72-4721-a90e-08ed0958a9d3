package com.gok.pboot.pms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.data.datascope.BusinessDataScope;
import com.gok.pboot.pms.common.join.ProjectInDailyPaperEntry;
import com.gok.pboot.pms.entity.Project;
import com.gok.pboot.pms.entity.domain.ProjectAttention;
import com.gok.pboot.pms.entity.dto.*;
import com.gok.pboot.pms.entity.vo.*;
import com.gok.pboot.pms.entity.vo.facade.ProjectInfoVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 项目 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-19
 */
@Mapper
public interface ProjectMapper extends BaseMapper<Project> {


    /**
     * 逻辑删除
     *
     * @param id 唯一标识
     * @return
     */
    int deleteByLogic(@Param("id") Long id);

    /**
     * 批量插入
     *
     * @param poList 实体集合
     */
    void batchSave(@Param("poList") List<Project> poList);


    /**
     * 批量修改
     *
     * @param list 实体集合
     */
    void batchUpdate(@Param("list") List<Project> list);

    /**
     * 批量逻辑删除
     *
     * @param list id集合
     */
    void batchDel(@Param("list") List<Long> list);

    /**
     * 批量修改不为空字段
     *
     * @param list id集合
     */
    void updateBatch(@Param("list") List<Long> list);

    /**
     * 项目信息查询 项目经理使用接口
     */
    List<ProjectVO> findListXmjl(@Param("filter") Map<String, Object> filter);

    /**
     * 项目信息分页查询 -- 无权限控制
     */
    Page<ProjectVO> findListPageNoRole(Page<ProjectVO> page, @Param("filter") Map<String, Object> filter);

    /**
     * 项目信息查询项目id集合-数据权限控制--前置接口
     */
    @BusinessDataScope
    List<ProjectVO> findListFront(@Param("filter") Map<String, Object> filter);

    /**
     * 项目信息查询项目id集合-数据权限控制--前置接口
     */
    @BusinessDataScope
    List<ProjectVO> findListFrontByRole(@Param("filter") Map<String, Object> filter, @Param("roleFilter") RoleProjectPageDto roleProjectPageDto);

    List<Long> filterIdsByEntryApprovalStatusTag(
            @Param("ids") List<Long> ids,
            @Param("tag") Integer tag,
            @Param("filter") Map<String, Object> filter
    );

    /**
     * 项目信息详情查询
     */
    ProjectInfoVO selectByIdVo(@Param("id") Long id);

    /**
     * ~ 根据userId查询该user可用于日报填写的项目 ~
     *
     * @param userId      userId
     * @param collectType 收藏类别
     * @return java.util.List<com.gok.pboot.pms.entity.Project>
     * <AUTHOR>
     * @date 2022/8/25 11:06
     */
    List<ProjectInDailyPaperEntry> findByUserIdForDailyPaperEntry(@Param("userId") Long userId, @Param("collectType") Integer collectType);

    /**
     * ~ 根据userId查询该user可用于日报填写的项目（新任务） ~
     *
     * @param userId      userId
     * @param collectType 收藏类别
     * @return java.util.List<com.gok.pboot.pms.entity.Project>
     * <AUTHOR>
     * @date 2022/8/25 11:06
     */
    @Deprecated
    List<ProjectInDailyPaperEntry> findByUserIdForDailyPaperEntryNew(@Param("userId") Long userId, @Param("collectType") Integer collectType);

    /**
     * 获取项目的审核员信息
     *
     * @param projectIds
     * @return {@link List}<{@link ProjectInDailyPaperEntry}>
     */
    @Deprecated
    List<ProjectInDailyPaperEntry> findByUserIdForDailyPaperEntryNew2(@Param("list") List<Long> projectIds);

    /**
     * 获取项目的收藏信息
     *
     * @param projectIds
     * @param userId
     * @param collectType
     * @return {@link List}<{@link ProjectInDailyPaperEntry}>
     */
    @Deprecated
    List<ProjectInDailyPaperEntry> findByUserIdForDailyPaperEntryNew3(@Param("list") List<Long> projectIds, @Param("userId") Long userId, @Param("collectType") Integer collectType);

//    /**
//     * ~ 根据ID列表查询可用于日报填写的项目 ~
//     *
//     * @param ids ID列表
//     * @return java.util.List<com.gok.pboot.pms.common.join.ProjectInDailyPaperEntry>
//     * <AUTHOR>
//     * @date 2022/8/26 10:43
//     */
//    List<ProjectInDailyPaperEntry> findByIdsForDailyPaperEntry(List<Long> ids);

    /**
     * 获取项目id和相关人员的map集合
     *
     * @param para
     * @return
     */
    List<ProjectUpdataVO> selectUpdateList(@Param("para") String para);

    /**
     * 分页查询项目工时明细
     *
     * @param dto 搜索条件
     * @return {@link Page<ProjectHourDetailsFindPageVO>}
     */
    List<ProjectHourDetailsFindPageVO> pageProjectHourDetails(@Param("filter") ProjectHourDetailsFindPageDTO dto);

    /**
     * 查询权限内的id
     *
     * @param dto 查询条件
     * @return {@link Page<DelivererFindPageVO>}
     */
    @BusinessDataScope
    List<Long> delivererFindIdPage(@Param("filter") DelivererFindPageDTO dto, @Param("approvalStatus") Integer approvalStatus);

    /**
     * 分页查询 交付人员
     *
     * @param page 分页参数
     * @param dto  查询条件
     * @return {@link Page<DelivererFindPageVO>}
     */
    Page<DelivererFindPageVO> delivererFindPage(Page page, @Param("filter") DelivererFindPageDTO dto, @Param("approvalStatus") Integer approvalStatus);

    /**
     * 查询 交付人员
     *
     * @param dto 查询条件
     * @return {@link List<DelivererFindPageVO> }
     */
    List<DelivererFindPageVO> delivererFindPage(@Param("filter") DelivererFindPageDTO dto, @Param("approvalStatus") Integer approvalStatus);

    /**
     * 按项目id列表和查询条件 分页统计日报审批信息
     *
     * @param projectIds 项目id列表
     * @param dto        查询条件
     * @param page       分页参数
     * @return {@link Page< DailyReviewPageVO >}
     */
    Page<DailyReviewPageVO> pageCountProjectByIds(@Param("projectIds") List<Long> projectIds, @Param("filter") DailyReviewPageDTO dto, Page page);

    /**
     * 按项目id查询项目信息
     *
     * @param viewTotalDto 日报请求实体
     * @param userIdList   用户id
     * @return {@link DailyReviewPageVO}
     */
    @Deprecated
    DailyReviewPageVO selectProjectById(@Param("viewTotalDto") DimensionViewTotalDto viewTotalDto, @Param("userIdList") List<Long> userIdList);

    /**
     * 按项目id列表和查询条件 分页统计复用、交付人员日报信息
     *
     * @param projectIds 项目id
     * @param filter     查询条件
     * @param page       分页参数
     * @return {@link Page<DailyReviewPageVO>}
     */
    @Deprecated
    Page<DailyReviewPageVO> selectPageReuseAndDelivery(@Param("projectIds") List<Long> projectIds, @Param("filter") DailyReviewPageDTO filter, Page<Object> page);

    /**
     * 按项目id 和审核状态 查询项目信息
     *
     * @param projectId   项目id
     * @param auditStatus 审核状态
     * @return {@link DailyReviewPageVO}
     */
    @Deprecated
    DailyReviewPageVO reuseAndDeliveryViewTotal(
            @Param("projectId") Long projectId,
            @Param("auditStatus") Integer auditStatus,
            @Param("startTime") String startTime,
            @Param("endTime") String endTime
    );

    /**
     * 按查询条件 分页统计复用、交付人员日报详细信息
     *
     * @param filter 查询条件
     * @param page   分页参数
     * @return {@link Page<DailyReviewReuseAndDeliveryPageVO>}
     */
    @Deprecated
    Page<DailyReviewReuseAndDeliveryPageVO> reuseAndDeliveryViewPage(@Param("filter") DailyReviewPageDTO filter, Page<Object> page);

    @Deprecated
    List<Project> selectProjectsByIds(@Param("projectIds") List<Object> projectIds);

    /**
     * 统计复用、交付工时待审核数
     *
     * @param projectIds 项目id
     * @return 待审核数
     */
    int countUnauditedNumber(@Param("projectIds") List<Long> projectIds);

    /**
     * 子任务管理，项目工时汇总通过不通权限查询项目
     *
     * @param page 分页参数
     * @param dto  实体参数
     * @return com.gok.pboot.pms.common.base.ApiResult<com.baomidou.mybatisplus.extension.plugins.pagination.Page < com.gok.pboot.pms.entity.vo.ProjectVO>>
     * @create by yzs at 2023/4/20
     */
    Page<ProjectVO> findByPermissions(Page page, @Param("filter") RoleProjectPageDto dto);

    /**
     * 每隔10分钟更新项目的销售人员赋予销售角色，获取需要更新的项目数据
     *
     * @return List<ProjectUpdataVO>
     * @para 0:更新近期数据，1：全量更新
     */
    List<Project> findByOaSalesTask(@Param("para") String para);


    void deleteAttentionProjectByUserId(Long userId);

    void addAttentionProject(@Param("list") List<ProjectAttention> projectAttentions);

    List<ProjectAttention> findAttentionProjectByUserId(@Param("userId") Long userId);

    List<ProjectVO> getAttentionProjectList(@Param("userId") Long userId);

    Page<ProjectAttentionVO> findAttentionProjectPage(Page<ProjectVO> page,
                                                        @Param("filter") Map<String, Object> filter);


    void removeAttentionProject(@Param("param")ProjectAttention projectAttention);
}
