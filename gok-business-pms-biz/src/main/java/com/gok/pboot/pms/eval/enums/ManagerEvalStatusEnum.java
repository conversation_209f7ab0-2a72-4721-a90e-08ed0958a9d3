/*
 * @Author: <PERSON>@goktech.cn
 * @Date: 2025-05-14 14:17:38
 * @LastEditors: <PERSON> <EMAIL>
 * @LastEditTime: 2025-05-22 16:29:28
 * @FilePath: \display-platform\gok-business-pms\gok-business-pms-biz\src\main\java\com\gok\pboot\pms\eval\enums\ManagerEvalStatusEnum.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.gok.pboot.pms.eval.enums;

import com.gok.pboot.pms.enumeration.ValueEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 项目经理评价状态枚举类
 *
 * <AUTHOR>
 * @create 2025/05/12
 **/
@Getter
@AllArgsConstructor
public enum ManagerEvalStatusEnum implements ValueEnum<Integer> {

    /**
     * 未评价
     */
    UNEVALUATED(0, "未评价"),

    /**
     * 总支撑官已评价
     */
    MANAGER(1, "总支撑官已评价"),

    /**
     * PMO已评价
     */
    PMO(2, "PMO已评价"),

    /**
     * 全部评价
     */
    ALL(3, "全部评价");

    private final Integer value;
    private final String name;

}
