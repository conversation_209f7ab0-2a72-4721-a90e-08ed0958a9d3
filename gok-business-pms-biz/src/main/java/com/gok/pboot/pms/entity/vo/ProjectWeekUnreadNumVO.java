package com.gok.pboot.pms.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 周报未读数量vo
 *
 * <AUTHOR>
 * @date 2023/9/1
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectWeekUnreadNumVO {

    /**
     * 全部未读数量
     */
    private Integer allUnreadNum;

    /**
     * 我的关注未读数量
     */
    private Integer attentionUnreadNum;


    public static ProjectWeekUnreadNumVO of(Integer allUnreadNum, Integer attentionUnreadNum) {
        ProjectWeekUnreadNumVO result = new ProjectWeekUnreadNumVO();

        result.setAllUnreadNum(allUnreadNum);
        result.setAttentionUnreadNum(attentionUnreadNum);
        return result;
    }
}
