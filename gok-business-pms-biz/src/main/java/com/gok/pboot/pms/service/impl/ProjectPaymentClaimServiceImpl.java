package com.gok.pboot.pms.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.pboot.pms.Util.DbApiUtil;
import com.gok.pboot.pms.common.constant.DictConstants;
import com.gok.pboot.pms.entity.domain.ProjectPaymentClaim;
import com.gok.pboot.pms.entity.vo.BusinessBlockVO;
import com.gok.pboot.pms.entity.vo.ProjectDictVo;
import com.gok.pboot.pms.mapper.ProjectPaymentClaimMapper;
import com.gok.pboot.pms.service.IProjectPaymentClaimService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 项目回款认领 ServiceImpl
 *
 * <AUTHOR>
 * @since 2023-09-26
 */
@Service
@RequiredArgsConstructor
public class ProjectPaymentClaimServiceImpl
        extends ServiceImpl<ProjectPaymentClaimMapper, ProjectPaymentClaim> implements IProjectPaymentClaimService {

    private final DbApiUtil dbApi;

    /**
     * 获取列表中的业务板块
     *
     * @return {@link List}<{@link BusinessBlockVO}>
     */
    @Override
    public List<BusinessBlockVO> queryBusinessBlock() {
        // 1、获取字典中的键值对
        Map<Integer, String> map = dbApi.projectDict(DictConstants.BUSINESS_BLOCK_ID)
                .stream()
                .collect(Collectors.toMap(ProjectDictVo::getSelectvalue, ProjectDictVo::getSelectname, (a, b) -> a));
        // 2、返回列表中存在的业务板块
        List<BusinessBlockVO> businessBlock = new ArrayList<>();
        baseMapper.selectList(new LambdaQueryWrapper<>()).stream()
                .map(ProjectPaymentClaim::getBusinessBlock)
                .filter(p -> Optional.ofNullable(p).isPresent())
                .forEach(p ->  businessBlock.add(new BusinessBlockVO(p, map.get(p))));
        return businessBlock.stream()
                .distinct()
                .filter(p -> Optional.ofNullable(p.getName()).isPresent())
                .collect(Collectors.toList());
    }
}
