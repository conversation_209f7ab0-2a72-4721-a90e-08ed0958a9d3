package com.gok.pboot.pms.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 项目商务里程碑定时器
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-02-23 10:46:40
 */
@Data
@TableName("project_business_milestones_schedule")
public class ProjectBusinessMilestonesSchedule implements Serializable {
	private static final long serialVersionUID = 1L;
	/**
	 * 合同id
	 */
     @TableId(value = "id", type = IdType.ASSIGN_ID)
	private Long id;
    /**
     * 里程碑id
     */
    private Long milestoneId;

    /**
     * 里程碑类型
     */
    private Integer type;

}
