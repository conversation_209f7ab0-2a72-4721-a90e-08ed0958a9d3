package com.gok.pboot.pms.cost.entity.dto;

import com.gok.pboot.pms.enumeration.CostSalaryRelateTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 成本工资 DTO
 *
 * <AUTHOR>
 * @date 2025/05/08
 */
@Data
@Accessors(chain = true)
public class CostSalaryDTO {


    /**
     * 关联 ID
     */
    private Long relateId;

    /**
     * 关联类型枚举
     * @see CostSalaryRelateTypeEnum
     */
    private Integer relateType;

    /**
     * 人员级别配置id
     */
    private Long configLevelPriceId;

    /**
     * 人工成本
     */
    private BigDecimal laborCost;

    /**
     * 工资
     */
    private BigDecimal salary;

    /**
     * 社保
     */
    private BigDecimal socialSecurity;

    /**
     * 公积金
     */
    private BigDecimal housingFund;

    /**
     * 残保金
     */
    private BigDecimal disabilityFee;

    /**
     * 休息日加班费
     */
    private BigDecimal weekendOvertimePay;

    /**
     * 节假日加班费
     */
    private BigDecimal holidayOvertimePay;


   public static CostSalaryDTO empty(){
        return new CostSalaryDTO()
                .setLaborCost(BigDecimal.ZERO)
                .setSalary(BigDecimal.ZERO)
                .setSocialSecurity(BigDecimal.ZERO)
                .setHousingFund(BigDecimal.ZERO)
                .setDisabilityFee(BigDecimal.ZERO)
                .setWeekendOvertimePay(BigDecimal.ZERO)
                .setHolidayOvertimePay(BigDecimal.ZERO);
    }



}
