package com.gok.pboot.pms.eval.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.eval.entity.domain.EvalTaskCalibration;
import com.gok.pboot.pms.eval.entity.vo.ProjectEvalRankVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2025/5/13
 **/
@Mapper
public interface EvalTaskCalibrationMapper  extends BaseMapper<EvalTaskCalibration> {

    /**
     * 查询项目工单评价排名计算
     * @param projectId
     * @param taskType
     * @return
     */
    List<ProjectEvalRankVO> getProjectEvalRankList(@Param("projectId") Long projectId, @Param("taskType") Integer taskType);

    /**
     * 批量修改
     *
     * @param list 实体集合
     */
    void batchUpdate(@Param("list") List<EvalTaskCalibration> list);

    /**
     * 逻辑删除
     * @param id
     */
    void delById(@Param("id") Long id);
}
