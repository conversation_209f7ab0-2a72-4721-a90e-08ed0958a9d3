package com.gok.pboot.pms.cost.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.List;

/**
 * 工单工时填报VO
 *
 * <AUTHOR>
 * @date 2024/04/16
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CostDeliverTaskInDailyPaperEntryVO {

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 是否内部项目
     */
    private Integer isInsideProject;

    /**
     * 销售人员名
     */
    private String salesmanUserName;


    /**
     * 售前经理
     */
    private String preSaleUserName;

    /**
     * 项目经理人名
     */
    private String managerUserName;

    /**
     * 工单列表
     */
    private List<CostDeliverTaskVO> tasks;

    /**
     * 收藏项目实体ID
     */
    private Long collectId;

    /**
     * 收藏人id
     */
    private Long collectUserId;

    /**
     * 收藏时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp collectTime;
} 