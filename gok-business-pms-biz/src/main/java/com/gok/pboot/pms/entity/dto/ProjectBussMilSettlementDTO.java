package com.gok.pboot.pms.entity.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 商务里程碑 结算 DTO
 *
 * <AUTHOR>
 * @date 2025/03/03
 */
@Data
@Accessors(chain = true)
public class ProjectBussMilSettlementDTO {

    /**
     * 结算金额（含税)
     */
    private BigDecimal settlementAmount;

    /**
     * 结算金额（不含税)
     */
    private BigDecimal settlementAmountExcludingTax;

    /**
     * 税率OA字典ID
     */
    private String taxRate;

    /**
     * 结算周期开始时间
     */
    private String settlementPeriodStartDate;


    /**
     * 结算周期结束时间
     */
    private String settlementPeriodEndDate;

    /**
     * 跳转链接
     */
    private Map<String,String> jumpLinkMap;
}
