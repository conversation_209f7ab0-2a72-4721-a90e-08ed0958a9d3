package com.gok.pboot.pms.common.base;


import com.gok.pboot.pms.enumeration.ApiResultEnum;

/**
 * date:  2016/2/27 16:34
 *
 * <AUTHOR>
 * @version 1.0
 */
public class ApiResult<T> {

    private static final long serialVersionUID = -19369146633454193L;
    private int code;
    private String msg;
    private T data;

    public ApiResult() {
    }

    private ApiResult(Builder<T> builder) {
        this.code = builder.code;
        this.msg = builder.msg;
        this.data = builder.data;
    }

    public static <T> Builder<T> builder() {
        return new Builder<>();
    }

    public static class Builder<T> {
        private int code;
        private String msg;
        private T data;

        public Builder<T> retCode(int code) {
            this.code = code;
            return this;
        }

        public Builder<T> retMessage(String msg) {
            this.msg = msg;
            return this;
        }

        public Builder<T> result(T data) {
            this.data = data;
            return this;
        }

        public Builder<T> apiResultEnum(ApiResultEnum apiResultEnum) {
            this.code = apiResultEnum.getValue();
            this.msg = apiResultEnum.getName();
            return this;
        }

        public ApiResult<T> build() {
            return new ApiResult(this);
        }
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return "ApiResult{" +
                "code='" + code + '\'' +
                ", msg='" + msg + '\'' +
                ", data=" + data +
                '}';
    }

    /**
     * 简单渲染成功信息
     *
     * @param data
     * @return
     */
    public static <T> ApiResult<T> success(T data) {
        return ApiResult.<T>builder().apiResultEnum(ApiResultEnum.SUCCESS).result(data).build();
    }

    /**
     * 简单渲染成功信息
     * @param msg
     * @param data
     * @return
     */
    public static <T> ApiResult<T> success(T data, String msg) {
        return ApiResult.<T>builder().apiResultEnum(ApiResultEnum.SUCCESS).result(data).retMessage(msg).build();
    }

    /**
     * 简单渲染通用失败信息
     *
     * @param data
     * @return
     */
    public static <T> ApiResult<T> failure(T data) {
        return ApiResult.<T>builder().apiResultEnum(ApiResultEnum.FAILURE).result(data).build();
    }


    /**
     * 简单渲染成功信息
     *
     * @param msg
     * @return
     */
    public static ApiResult<String> successMsg(String msg) {
        return ApiResult.<String>builder().apiResultEnum(ApiResultEnum.SUCCESS).retMessage(msg).build();
    }


    /**
     * 简单渲染通用失败信息
     *
     * @param msg
     * @return
     */
    public static ApiResult<String> failureMsg(String msg, ApiResultEnum apiResultEnum) {
        return ApiResult.<String>builder().apiResultEnum(apiResultEnum).retMessage(msg).build();
    }

    /**
     * 简单渲染通用失败信息
     *
     * @param msg
     * @return
     */
    public static <T> ApiResult<T> failure(String msg) {
        return ApiResult.<T>builder().apiResultEnum(ApiResultEnum.FAILURE).retMessage(msg).build();
    }
}
