package com.gok.pboot.pms.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * - 人员审核权限 -
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/8/23 14:42
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(Privilege.ALIAS)
public class Privilege extends BeanEntity<Long> {

    public static final String ALIAS = "mhour_privilege";

    /**
     * 人员ID
     */
    private Long userId;

    /**
     * 人员姓名
     */
    private String userName;

    /**
     * 权限类型（0=业务部门管理员，1=项目审核员，2=管理员，3=项目操作员）
     */
    private Integer privilegeType;

    /**
     * 可以审核的项目ID
     */
    private Long projectId;

    /**
     * 可以审核的部门ID
     */
    private Long deptId;
}
