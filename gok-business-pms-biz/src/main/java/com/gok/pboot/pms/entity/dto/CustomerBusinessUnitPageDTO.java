package com.gok.pboot.pms.entity.dto;


import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;


/**
* <p>
* 客户经营单元-所属客户查询
* </p>
*
* <AUTHOR>
* @since 2024-10-12
*/
@Data
@Accessors(chain = true)
@ApiModel(value = "CustomerBusinessUnitPageDTO", description = "客户经营单元-所属客户查询")
public class CustomerBusinessUnitPageDTO {

    // 页号
    private int pageNumber = 0;
    // 每页大小
    private int pageSize = 10;


    /**
     * 所属客户经理姓名
     */
    private String unitManager;

    /**
     * 所属客户名称
     */
    private String unitName;


    /**
     * 经营单元
     */
    private String businessName;

    /**
     * 数据权限过滤id
     */
    private List<Long> businessIdsInDataScope;

    /**
     * 权限范围
     */
    private String scope;
}
