package com.gok.pboot.pms.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.common.security.annotation.Inner;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.common.base.PropertyFilters;
import com.gok.pboot.pms.common.base.R;
import com.gok.pboot.pms.entity.vo.ProjectInfoOverViewVO;
import com.gok.pboot.pms.entity.vo.ProjectInfoVO;
import com.gok.pboot.pms.entity.vo.ProjectOverViewInnerVO;
import com.gok.pboot.pms.enumeration.IsNoInternalProjectEnum;
import com.gok.pboot.pms.service.IProjectInfoService;
import com.gok.pboot.pms.service.IProjectStatusService;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 项目信息
 *
 * <AUTHOR>
 * @menu 项目信息
 * @since 2023-07-14
 */
@RestController
@RequestMapping("/projectInfo")
@AllArgsConstructor
public class ProjectInfoController {

    private final IProjectInfoService service;

    private final IProjectStatusService projectStatusService;

    /**
     * 外部项目分页查询
     *
     * @param pageRequest 分页参数
     * @param request     后端请求参数
     * @return data
     * @customParam filter_S_itemName 项目名称
     * @customParam filter_S_itemNo 项目编号
     * @customParam filter_S_projectStatusList 项目状态
     * @customParam filter_I_isNotInternalProject 是否为内部项目
     * @customParam filter_S_customerName 客户名称
     * @customParam filter_S_ironTriangleUserIds 铁三角ID
     * @customParam filter_I_isAttention 是否为查询关注项目
     * @customParam filter_S_businessDepartment 业务归属部门
     * @customParam filter_S_proDeliveryDepartment 项目交付部门
     * @customParam filter_S_secondaryBusinessType 业务类型
     * @customParam filter_S_deliverType 交付形式
     * @customParam filter_S_businessId 经营单元
     * @customParam filter_S_unitIds 所属客户
     */
    @GetMapping("/findPage")
    @PreAuthorize("@pms.hasPermission('PROJECT_MANAGE_LIST')")
    public ApiResult<Page<ProjectInfoOverViewVO>> findPage(PageRequest pageRequest, HttpServletRequest request) {
        Map<String, Object> filter = PropertyFilters.get(request, true);
        // 外部项目
        filter.put("isNotInternalProject", IsNoInternalProjectEnum.no.getValue());
        return ApiResult.success(service.findPage(pageRequest, filter));
    }

    /**
     * 内部项目分页查询
     *
     * @param pageRequest 分页参数
     * @param request     后端请求参数
     * @return data
     * @customParam filter_S_itemNameOrItemNo 项目名称/项目编号
     * @customParam filter_S_projectStatusList 项目状态
     * @customParam filter_S_projectTypeList 项目类型
     * @customParam filter_S_businessDeptIds 业务归属部门
     * @customParam filter_S_projectDeptIds 项目归属部门
     * @customParam filter_S_projectApprovalStart 立项开始日期
     * @customParam filter_S_projectApprovalEnd 立项结束日期
     * @customParam filter_S_managerUserName 业务经理姓名
     * @customParam filter_S_businessManager 项目经理姓名
     * @customParam filter_I_isNotInternalProject 是否为内部项目
     * @customParam filter_I_isAttention 是否为查询关注项目
     */
    @GetMapping("/inner/findPage")
    @PreAuthorize("@pms.hasPermission('PROJECT_MANAGE_LIST_INSIDE')")
    public ApiResult<Page<ProjectInfoOverViewVO>> findInnerPage(PageRequest pageRequest, HttpServletRequest request) {
        Map<String, Object> filter = PropertyFilters.get(request, true);
        // 内部项目
        filter.put("isNotInternalProject", IsNoInternalProjectEnum.yes.getValue());
        return ApiResult.success(service.findPage(pageRequest, filter));
    }

    /**
     * 通过Id查询项目基本信息
     *
     * @param id 项目ID
     * @return {@link R}<{@link ProjectOverViewInnerVO}>>
     */
    @ApiOperation(value = "通过Id查询项目基本信息", notes = "通过Id查询项目基本信息")
    @GetMapping("/{id}")
    public R<ProjectInfoVO> getProjectInfoById(@PathVariable("id") Long id) {
        return R.ok(service.getProjectInfoById(id));
    }

    /**
     * 通过Id查询内部项目概况
     *
     * @param id 项目ID
     * @return {@link R}<{@link ProjectOverViewInnerVO}>>
     */
    @ApiOperation(value = "通过Id查询内部项目概况", notes = "通过Id查询内部项目概况")
    @GetMapping("/overviewInner/{id}")
    public R<ProjectOverViewInnerVO> getProjectOverviewInnerById(@PathVariable("id") Long id) {
        return R.ok(service.getProjectOverviewInnerById(id));
    }

    /**
     * 同步项目干系人
     *
     * @return {@link ApiResult}<{@link Void}>
     */
    @Inner(value = false)
    @GetMapping("syncMembers")
    public ApiResult<Void> syncMembers() {
        service.syncProjectMembers();
        return ApiResult.success(null);
    }

    /**
     * 项目操作提醒
     *
     * @param projectId 项目ID
     * @return {@link ApiResult}<{@link Map}<{@link String}, {@link Object}>>
     */
    @GetMapping("/operateRemind/{projectId}")
    public ApiResult<Map<String, Object>> projectOperateRemind(@PathVariable("projectId") Long projectId) {
        return ApiResult.success(projectStatusService.projectOperateRemind(projectId));
    }

    /**
     * 项目状态流转消息提醒
     *
     * @return {@link ApiResult}<{@link Void}>
     */
    @Inner(false)
    @GetMapping("/statusTransferRemind")
    public ApiResult<String> statusTransferRemind() {
        projectStatusService.statusTransferRemind();
        return ApiResult.successMsg("操作成功");
    }

}
