package com.gok.pboot.pms.cost.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 成本现金计划版本 VO
 *
 * <AUTHOR>
 * @date 2025/04/22
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CostCashPlanVersionVO {

    private Long id;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 版本号
     */
    private String versionNo;

    /**
     * 版本状态（0=当前版本，1=历史版本）
     */
    private Integer versionStatus;

    /**
     * 关联最早成本估算版本ID
     */
    private Long costVersionId;

    /**
     * 关联最早成本估算版本
     */
    private String costVersion;

    /**
     * 创建人当前角色
     */
    private String creatorRole;

    /**
     * 备注
     */
    private String remark;

    /**
     * 关联流程 ID
     */
    private Long requestId;

    private String requestName;

    private Integer requestStatus;

    private String requestStatusName;
    /**
     * 现金计划版本状态
     */
    private Integer status;

    private String statusStr;

    /**
     * 创建者 ID
     */
    private Long creatorId;

    private String creator;

    private LocalDateTime ctime;
}
