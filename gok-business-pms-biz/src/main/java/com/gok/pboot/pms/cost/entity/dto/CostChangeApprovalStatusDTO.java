package com.gok.pboot.pms.cost.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.sql.Timestamp;

/**
 * 改变工单工时审核状态DTO
 * <AUTHOR>
 * @Date 2025-04-18 11:27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class CostChangeApprovalStatusDTO {

    /**
     * 审核状态（0=未提交，1=已退回，2=待审核，3=不通过，4=已审核）
     */
    @NotNull(message = "审核状态不能为空！")
    private Integer approvalStatus;

    /**
     * 日报条目Id
     */
    @NotNull(message = "日报条目id不能为空！")
    private Long id;


    /**
     * 不通过原因
     */
    private String approvalReason;

    /**
     * 审核人
     */
    private String approvalName;

    /**
     * 审核人
     */
    private Long approvalId;

    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private Timestamp mtime;
}
