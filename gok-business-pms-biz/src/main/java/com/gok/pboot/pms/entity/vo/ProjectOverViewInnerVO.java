package com.gok.pboot.pms.entity.vo;

import com.gok.pboot.pms.entity.domain.ProjectInfo;
import com.gok.pboot.pms.enumeration.BusinessDirectionEnum;
import com.gok.pboot.pms.enumeration.InternalProjectTypeEnum;
import com.google.common.base.Strings;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

/**
 * 内部项目概况VO
 *
 * <AUTHOR>
 * @since 2023-07-14
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class ProjectOverViewInnerVO {


    /**
     * ID
     */
    private String id;

    /**
     * 项目编号
     */
    private String itemNo;

    /**
     * 项目名称
     */
    private String itemName;

    /**
     * 业务方向 （0=ICT，1=信息安全，2=软件开发，3=综合，4=数据治理）
     *
     * @see BusinessDirectionEnum Y
     */
    private String businessDirection;


    /**
     * 项目类型
     */
    private Integer projectType;

    /**
     * 项目类型（公司信息化、通用课程开发、自研产品研发、标准化解决方案打造、专项人才供应链构建、部门工作）
     *
     * @see InternalProjectTypeEnum
     */
    private String projectTypeName;


    /**
     * 项目归属部门id
     */
    private Long projectDepartmentId;

    /**
     * 项目归属部门
     */
    private String projectDepartment;

    /**
     * 业务归属部门id
     */
    private Long businessDepartmentId;

    /**
     * 业务归属部门
     */
    private String businessDepartment;


    public static ProjectOverViewInnerVO from(ProjectInfo po) {
        ProjectOverViewInnerVO result = new ProjectOverViewInnerVO();
        result.setId(String.valueOf(po.getId()));
        result.setItemNo(Strings.nullToEmpty(po.getItemNo()));
        result.setItemName(Strings.nullToEmpty(po.getItemName()));
        result.setProjectType(po.getProjectType());
        //项目类型
        result.setProjectTypeName(result.getProjectType() == null ? StringUtils.EMPTY :
                InternalProjectTypeEnum.getNameByVal(result.getProjectType()));
        return result;
    }

}
