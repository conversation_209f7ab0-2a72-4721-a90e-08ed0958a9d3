package com.gok.pboot.pms.cost.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.pboot.pms.cost.entity.domain.CostManagePersonnelCustomDetail;
import com.gok.pboot.pms.cost.mapper.CostManagePersonnelCustomDetailMapper;
import com.gok.pboot.pms.cost.service.ICostManagePersonnelCustomDetailService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 成本管理人员级别自定义补贴明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Service
public class CostManagePersonnelCustomDetailServiceImpl extends ServiceImpl<CostManagePersonnelCustomDetailMapper, CostManagePersonnelCustomDetail> implements ICostManagePersonnelCustomDetailService {

}
