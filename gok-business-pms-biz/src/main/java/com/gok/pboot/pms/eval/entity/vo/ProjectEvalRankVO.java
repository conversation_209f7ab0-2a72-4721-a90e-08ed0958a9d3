package com.gok.pboot.pms.eval.entity.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 项目工单评价排名计算VO
 * <AUTHOR>
 * @create 2025/5/13
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class ProjectEvalRankVO {

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 员工id
     */
    private Long employeeId;

    /**
     * 结算工单产值
     */
    private BigDecimal income;

    /**
     * 综合得分
     */
    private BigDecimal comprehensiveScore;

    /**
     * 当前等级
     */
    private Integer currentLevel;
}
