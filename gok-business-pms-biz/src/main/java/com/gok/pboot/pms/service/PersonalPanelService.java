package com.gok.pboot.pms.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.entity.dto.PanelRequestDTO;
import com.gok.pboot.pms.entity.vo.PanelProjectSituationAnalysisVO;
import com.gok.pboot.pms.entity.vo.PanelProjectSituationDetailsExportVO;
import com.gok.pboot.pms.entity.vo.PanelProjectSituationDetailsVO;
import com.gok.pboot.pms.entity.vo.PanelProjectSituationVO;

import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @createTime 2023/2/21 15:18
 */
public interface PersonalPanelService {
    /**
     * 个人面板-项目情况-分页
     *
     * @param filter 分页条件
     * @return {@link ApiResult<Page<PanelProjectSituationVO>>}
     */
    ApiResult<Page<PanelProjectSituationVO>> page(PanelRequestDTO filter);

    /**
     * 个人面板-项目情况-导出
     *
     * @param filter 查询条件
     * @return 导出文件
     */
    List<PanelProjectSituationVO> export(PanelRequestDTO filter);

    /**
     * 个人面板-项目情况-明细-分页
     *
     * @param filter 查询条件
     * @return {@link ApiResult<Page<PanelProjectSituationDetailsVO>>}
     */
    ApiResult<Page<PanelProjectSituationDetailsVO>> detailPage(PanelRequestDTO filter);

    /**
     * 个人面板-项目情况-明细-导出
     *
     * @param filter 查询条件
     * @return 导出文件
     */
    List<PanelProjectSituationDetailsExportVO> detailExport(PanelRequestDTO filter);

    /**
     * 个人面板-饱和度分析-分页
     *
     * @param filter 查询条件
     * @return {@link ApiResult<Page<PanelProjectSituationAnalysisVO>>}
     */
    ApiResult<Page<PanelProjectSituationAnalysisVO>> analysisPage(PanelRequestDTO filter);

    /**
     * 统计个人面板-饱和度分析-总计
     * @param filter 查询条件
     * @return {@link ApiResult<PanelProjectSituationAnalysisVO>}
     */
    ApiResult<PanelProjectSituationAnalysisVO> analysisTotal(PanelRequestDTO filter);
}
