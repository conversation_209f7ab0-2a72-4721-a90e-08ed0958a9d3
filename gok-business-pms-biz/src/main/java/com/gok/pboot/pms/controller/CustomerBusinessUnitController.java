package com.gok.pboot.pms.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.Util.CollectionUtils;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.entity.dto.CustomerBusinessSearchDTO;
import com.gok.pboot.pms.entity.dto.CustomerBusinessUnitDTO;
import com.gok.pboot.pms.entity.dto.CustomerBusinessUnitPageDTO;
import com.gok.pboot.pms.entity.vo.CustomerBusinessListVO;
import com.gok.pboot.pms.entity.vo.CustomerBusinessUnitPageVO;
import com.gok.pboot.pms.entity.vo.CustomerBusinessUnitProjectVO;
import com.gok.pboot.pms.entity.vo.CustomerBusinessUnitVO;
import com.gok.pboot.pms.service.ICustomerBusinessUnitService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 客户经营单元-所属客户 controller
 *
 * <AUTHOR>
 * @menu 客户经营单元-所属客户
 * @since 2024-10-12
 */
@RestController
@RequestMapping("/customerBusinessUnit")
public class CustomerBusinessUnitController {

    @Autowired
    private ICustomerBusinessUnitService service;

    /**
     * 保存所属客户
     *
     * @param customerBusinessUnitDTO dto对象
     * @return {@link ApiResult}
     */
    @PostMapping("/batchSave")
    public ApiResult<String> batchSave(@RequestBody CustomerBusinessUnitDTO customerBusinessUnitDTO) {
        if (CollectionUtils.isNotEmpty(customerBusinessUnitDTO.getUnits())) {
            service.saveList(customerBusinessUnitDTO.getUnits(), customerBusinessUnitDTO.getSource());
        }
        return ApiResult.success("操作成功");
    }


    /**
     * 分页查询所属客户
     *
     * @param dto
     * @return {@link ApiResult}
     */
    @GetMapping("/findPage")
    @PreAuthorize("@pms.hasPermission('BELONG_CUSTOMERS')")
    public ApiResult<Page<CustomerBusinessUnitPageVO>> findPage(CustomerBusinessUnitPageDTO dto) {
        return ApiResult.success(service.findPageList(dto));
    }

    /**
     * 批量删除所属客户
     *
     * @param customerBusinessUnitDTO
     * @return
     */
    @PostMapping("/batchDelete")
    public ApiResult<String> deletedById(@RequestBody CustomerBusinessUnitDTO customerBusinessUnitDTO) {
        if (CollectionUtils.isNotEmpty(customerBusinessUnitDTO.getIds())) {
            service.batchDelete(customerBusinessUnitDTO.getIds());
        }
        return ApiResult.success("删除成功");
    }

    /**
     * 获取所属客户名称列表
     *
     * @param customerBusinessSearchDTO
     * @return
     */
    @PostMapping("/findNameList")
    public ApiResult<List<CustomerBusinessListVO>> findNameList(@RequestBody CustomerBusinessSearchDTO customerBusinessSearchDTO) {
        return ApiResult.success(service.findNameList(customerBusinessSearchDTO));
    }


    /**
     * 根据id查询所属客户
     *
     * @param id 所属主键id
     * @return {@link ApiResult}{@link CustomerBusinessUnitVO}
     */
    @GetMapping("/{id}")
    public ApiResult<CustomerBusinessUnitVO> findById(@PathVariable("id") Long id) {
        return ApiResult.success(service.getById(id));
    }

    /**
     * 校验客户是否关联商机及项目
     * @param id
     * @return
     */
    @GetMapping("/checkUnit/{id}")
    public ApiResult<CustomerBusinessUnitProjectVO> checkUnit(@PathVariable("id") Long id) {
        CustomerBusinessUnitProjectVO customerBusinessUnitProjectVO = service.checkUnit(id);
        return ApiResult.success(customerBusinessUnitProjectVO);
    }
}
