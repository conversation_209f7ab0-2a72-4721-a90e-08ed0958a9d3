package com.gok.pboot.pms.entity.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 滴滴机票订单Excel导出VO
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
public class DdFlightTicketOrderExcelVO {

    @ExcelProperty("订单号")
    private String orderNo;

    @ExcelProperty("乘机人工号")
    private String passengerEmployeeNo;

    @ExcelProperty("乘机人姓名")
    private String passengerName;

    @ExcelProperty("乘机人部门名称")
    private String passengerDeptName;

    @ExcelProperty("票号")
    private String ticketNo;

    @ExcelProperty("票号状态")
    private String ticketStatus;

    @ExcelProperty("出发地")
    private String departureLocation;

    @ExcelProperty("到达地")
    private String arrivalLocation;

    @ExcelProperty("航班号")
    private String flightNo;

    @ExcelProperty("起飞时间")
    private LocalDateTime departureTime;

    @ExcelProperty("降落时间")
    private LocalDateTime landingTime;

    @ExcelProperty("企业实付金额")
    private BigDecimal companyActualPayment;

    @ExcelProperty("服务费")
    private BigDecimal serviceFee;

    @ExcelProperty("预订日期")
    private LocalDate bookingDate;

    @ExcelProperty("预订人工号")
    private String bookingEmployeeNo;

    @ExcelProperty("预订人姓名")
    private String bookingEmployeeName;

    @ExcelProperty("预订人部门名称")
    private String bookingDeptName;

    @ExcelProperty("出差申请单号")
    private String businessTripApplicationNo;

    @ExcelProperty("出差事由")
    private String businessTripReason;

    @ExcelProperty("成本中心名称")
    private String costCenterName;

    @ExcelProperty("所属项目名称")
    private String projectName;

    @ExcelProperty("项目编码")
    private String projectCode;

    @ExcelProperty("所属公司名称")
    private String companyName;

    @ExcelProperty("所属账期")
    private String accountingPeriod;
}
