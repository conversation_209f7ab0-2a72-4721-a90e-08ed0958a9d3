package com.gok.pboot.pms.entity.vo;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 合同基本信息vo
 *
 * <AUTHOR>
 * @date 2024/2/22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class ContractBaseInfoVo {

    /**
     * 合同细类{
     *   "0": "采购合同",
     *   "1": "销售合同",
     *   "2": "协议合同",
     *   "3": "其它类"
     * }
     */
    private Integer htxl;

    /**
     * 合同编号
     */
    private String htbh;

    /**
     * 实际合同编号
     */
    private String sjhtbh;

    /**
     * 结算方式
     */
    private Integer jsfs;

    /**
     * 结算方式txt
     */
    private String jsfsTxt;

    /**
     * 合同所属一级部门id
     */
    private Long htssbm;
    /**
     * 业务归属一级部门
     *
     * （原合同所属一级部门）
     */
    private String htssbmName;

    /**
     * 合同所属二级部门id
     */
    private Long htssejbm;
    /**
     * 业务归属一级部门
     *
     * （原合同所属一级部门）
     */
    private String htssejbmName;

    /**
     * 合同起始日期
     */
    private String htqsrq;

    /**
     * 合同截止日期
     */
    private String htjzrq;
    /**
     * 实际合同签订日期
     */
    private String sjhtqdrq;
    /**
     * 合同金额(不含税)
     */
    private String htjebhs;



    /**
     * 合同附件集合
     */
    List<OaFileInfoVo> htfjList;




    /**
     * 合同附件（已盖章）集合
     */
    List<OaFileInfoVo> htfjygzList;

    /**
     * 对方名称  改 客户名称/供应商名称
     */
    private String khmcName;
    /**
     * 客户编号 改 备案编号
     */
    private String khbh;

    /**
     * 联系人
     */
    private String lxr;
    /**
     * 联系方式
     */
    private String lxfs;
    /**
     * 联系地址
     */
    private String lxdz;

    /**
     * 客户方开户银行
     */
    private String khfkhyx;
    /**
     * 客户方账号
     */
    private String khfzh;
    /**
     * 客户方纳税人登记号
     */
    private String khfnsrdjh;
    /**
     * 项目id
     */
    private Long xmmc;
    /**
     * 项目名称
     */
    private String xmmcName;

    /**
     * 项目编号
     */
    private String xmbh;
    /**
     * 项目所在地
     */
    private String xmszd;
    /**
     * 业务板块
     */
    private Integer ywbk;

    /**
     * 业务板块txt
     */
    private String ywbkTxt;
    /**
     * 收入类型
     */
    private Integer srlx;
    /**
     * 收入类型txt
     */
    private String srlxTxt;
    /**
     * 技术类型
     */
    private Integer jslx;

    /**
     * 技术类型Txt
     */
    private String jslxTxt;
    /**
     * 交付形式
     */
    private Integer jfxs;

    /**
     * 交付形式txt
     */
    private String jfxsTxt;

    /**
     * 项目经理
     */
    private String xmjlName;
    /**
     * 项目负责人
     */
    private String xmfzrName;
    /**
     * 项目毛利测算流程
     */
    private String xmmlcslc;

    /**
     * 项目毛利测算流程名字
     */
    private String xmmlcslcName;

    /**
     * 项目毛利测算流程相关人id
     */
    private Long xmmlcslcNodeoperator;

    /**
     * 项目预计毛利率
     */
    private String xmyjmll;
    /**
     * 项目预计毛利
     */
    private String xmyjml;
    /**
     * 项目销售人员（客户经理）id
     */
    private String xmxsry;

    /**
     * 项目销售人员（客户经理）
     */
    private String xmxsryName;

    /**
     * 项目销售人员2id
     */
    private String xmxsry2;

    /**
     * 项目销售人员2
     */
    private String xmxsry2Name;
    /**
     * 是否涉及硬件采购
     */
    private Integer sfsjyjcg;
    /**
     * 是否涉及硬件采购
     */
    private String sfsjyjcgTxt;

    /**
     * 采购申请流程
     */
    private String xzxmcgsqlcName;

    /**
     * 采购申请流程id
     */
    private String xzxmcgsqlc;


    /**
     * 项目设备签收单附件集合
     */
    List<OaFileInfoVo> xmsbqsdfjList;

    /**
     * 投标审批流程
     */
    private String  approvalProcess;

    /**
     * 中标通知书
     */
    private String zbtzs;

    /**
     * 相关流程
     */
    private String relateprocess;



    /**
     * 中标通知书附件集合
     */
     List<OaFileInfoVo>zbtzsfjList;



    /**
     * 相关附件集合
     */
    List<OaFileInfoVo> relateattachmentList;

    /**
     * 备注
     */
    private String remark;

    /**
     * 合同会签流程id
     */
    private String hthqlc;

    /**
     * 合同会签流程相关人id
     */
    private String hthqlcNodeoperator;

    /**
     * 合同变更记录
     */
    List<ContractChangeInfoVo> contractChangeInfoVoList;

}
