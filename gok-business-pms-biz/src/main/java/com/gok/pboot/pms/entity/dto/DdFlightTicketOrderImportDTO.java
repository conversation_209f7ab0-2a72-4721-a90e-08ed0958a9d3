package com.gok.pboot.pms.entity.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 滴滴机票订单导入DTO
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DdFlightTicketOrderImportDTO {

    /**
     * 订单号
     */
    @ExcelProperty(value = "*订单号")
    private String orderNo;

    /**
     * 乘机人工号
     */
    @ExcelProperty(value = "乘机人工号")
    private String passengerEmployeeNo;

    /**
     * 乘机人姓名
     */
    @ExcelProperty(value = "*乘机人")
    private String passengerName;

    /**
     * 乘机人部门ID
     */
    @ExcelIgnore
    private Long passengerDeptId;


    /**
     * 乘机人ID
     */
    @ExcelIgnore
    private Long passengerId;

    /**
     * 票号
     */
    @ExcelProperty(value = "票号")
    private String ticketNo;

    /**
     * 票号状态
     */
    @ExcelProperty(value = "票号状态")
    private String ticketStatus;

    /**
     * 出发地
     */
    @ExcelProperty(value = "出发地")
    private String departureLocation;

    /**
     * 到达地
     */
    @ExcelProperty(value = "到达地")
    private String arrivalLocation;

    /**
     * 航班号
     */
    @ExcelProperty(value = "航班")
    private String flightNo;

    /**
     * 起飞时间
     */
    @ExcelProperty(value = "起飞时间")
    private LocalDateTime departureTime;

    /**
     * 降落时间
     */
    @ExcelProperty(value = "降落时间")
    private LocalDateTime landingTime;

    /**
     * 企业实付金额
     */
    @ExcelProperty(value = "*企业实付")
    private BigDecimal companyActualPayment;

    /**
     * 服务费
     */
    @ExcelProperty(value = "服务费")
    private BigDecimal serviceFee;

    /**
     * 预订日期
     */
    @ExcelProperty(value = "*预订日期")
    private LocalDate bookingDate;


    /**
     * 预订人ID
     */
    @ExcelIgnore
    private Long bookingUserId;

    /**
     * 预订人部门ID
     */
    @ExcelIgnore
    private Long bookingDeptId;


    /**
     * 预订人工号
     */
    @ExcelProperty(value = "*预订人工号")
    private String bookingEmployeeNo;

    /**
     * 预订人姓名
     */
    @ExcelProperty(value = "预订人")
    private String bookingEmployeeName;

    /**
     * 出差申请单号
     */
    @ExcelProperty(value = "出差申请单号")
    private String businessTripApplicationNo;

    /**
     * 出差事由
     */
    @ExcelProperty(value = "出差事由")
    private String businessTripReason;

    /**
     * 成本中心/所属项目
     */
    @ExcelProperty(value = "*成本中心/所属项目")
    private String costCenterProject;

    @ExcelIgnore
    private Long companyId;
    /**
     * 所属公司
     */
    @ExcelProperty(value = "*所属公司")
    private String companyName;
    /**
     * 成本中心ID
     */
    @ExcelIgnore
    private Long costCenterId;

    /**
     * 成本中心名称
     */
    @ExcelIgnore
    private String costCenterName;

    /**
     * 所属项目ID
     */
    @ExcelIgnore
    private Long projectId;

    /**
     * 所属项目名称
     */
    @ExcelIgnore
    private String projectName;

    @ExcelIgnore
    private String projectCode;
} 