package com.gok.pboot.pms.entity.vo;


import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Objects;

/**
 * 项目
 *
 * <AUTHOR>
 * @since 2022-08-19
 */
@Data
@Accessors(chain = true)
public class ProjectVO {

    /**
     * 项目ID
     */
    private Long id;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 项目编号
     */
    private String code;
    /**
     * 项目状态
     */
    private Integer projectStatus;
    /**
     * 项目状态值
     */
    private String projectStatusName;

    /**
     * 业务归属部门
     */
    private String projectDepartment;

    /**
     * 业务归属部门id
     */
    private Long projectDepartmentId;

    /**
     * 收藏项目实体ID
     */
    private Long collectId;
    /**
     * 收藏人id
     */
    private Long collectUserId;
    /**
     * 收藏时间
     */
    private String collectTime;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ProjectVO projectVO = (ProjectVO) o;
        return Objects.equals(id, projectVO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
}
