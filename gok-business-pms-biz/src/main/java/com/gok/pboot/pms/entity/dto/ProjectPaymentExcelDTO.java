package com.gok.pboot.pms.entity.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 项目回款追踪导入Excel
 *
 * <AUTHOR>
 * @since 2023-10-07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProjectPaymentExcelDTO {

    /**
     * 收款公司
     * {@link com.gok.pboot.pms.enumeration.AttributableSubjectEnum}
     */
    @ExcelProperty("收款公司")
    @NotBlank(message = "收款公司不能为空")
    private String paymentCompanyTxt;

    /**
     * 客户名称
     */
    @ExcelProperty("客户名称")
    private String customerName;

    /**
     * 企业名称
     */
    @ExcelProperty("企业名称")
    private String enterpriseName;

    /**
     * 收款日期
     */
    @NotBlank(message = "收款日期不能为空")
    @ExcelProperty("收款日期")
    private String paymentDate;

    /**
     * 收款金额
     */
    @NotNull(message = "收款金额不能为空")
    @ExcelProperty("收款金额")
    private BigDecimal paymentAmount;

    /**
     * 收款平台
     */
    @NotBlank(message = "收款平台不能为空")
    @ExcelProperty("收款平台")
    private String paymentPlatform;

    /**
     * 预算回款金额
     */
    @ExcelProperty("预算回款金额")
    private String budgetCollectionAmount;

    /**
     * 预算内回款
     * {@link com.gok.pboot.pms.enumeration.CollectionWithinBudgetEnum}
     */
    @ExcelProperty("预算内回款")
    private String collectionWithinBudgetTxt;

    /**
     * 银行账户
     */
    @NotBlank(message = "银行账户不能为空")
    @ExcelProperty("银行账户")
    private String bankAccount;

    /**
     * 归属区域
     */
    @ExcelProperty("归属区域")
    private String belongingArea;

    /**
     * 销售负责
     */
    @ExcelProperty("销售负责")
    private String salesmanUserName;

    /**
     * 归属业务线
     * {@link com.gok.pboot.pms.enumeration.BusinessLineEnum}
     */
    @ExcelProperty("归属业务线")
    private String businessLineTxt;

    /**
     * 项目名称
     */
    @ExcelProperty("项目名称")
    private String projectName;

    /**
     * 合同名称
     */
    @ExcelProperty("合同名称")
    private String contractName;

    /**
     * 回款一级部门
     */
    @ExcelProperty("回款一级部门")
    private String paymentDept;

    /**
     * 回款二级部门
     */
    @ExcelProperty("回款二级部门")
    private String paymentSecondaryDept;

    /**
     * 认领状态
     * {@link com.gok.pboot.pms.enumeration.ClaimStatusEnum}
     */
    @ExcelProperty("认领状态")
    private String claimStatusTxt;

    /**
     * 锁定状态
     * {@link com.gok.pboot.pms.enumeration.LockStatusEnum}
     */
    @ExcelProperty("锁定状态")
    private String lockStatusTxt;

    /**
     * 推送状态
     * {@link com.gok.pboot.pms.enumeration.PushStatusEnum}
     */
    @ExcelProperty("推送状态")
    private String pushStatusTxt;

    /**
     * 创建人名称
     */
    @ExcelProperty("创建人")
    private String creatorName;

    /**
     * 认领人姓名
     */
    @ExcelProperty("认领人")
    private String claimantName;

    /**
     * 备注
     */
    @ExcelProperty("备注")
    private String paymentNote;
}
