package com.gok.pboot.pms.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 任务-永辉关联Vo
 *
 * <AUTHOR>
 * @date 2024/01/22
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectTaskeUserVo {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 人员ID
     */
    private Long userId;

    /**
     * 人员姓名
     */
    private String userName;

    /**
     * 任务角色（0=负责人，1=参与人）
     * @link com.gok.pboot.pms.enumeration.ProjectTaskRoleEnum
     */
    private Integer taskRole;

    /**
     * 部门名
     */
    private String deptName;

    /**
     * 岗位/职位
     */
    private String position;

    /**
     * 员工状态
     */
    private Integer employeeStatus;

    /**
     * 员工状态txt
     */
    private String employeeStatusTxt;

}
