package com.gok.pboot.pms.entity.vo;


import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 合同台账列表vo
 *
 * <AUTHOR>
 * @date 2024/2/22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class ContractLedgerSelectListVo {

    /**
     * 合同id
     */
    private Long id;

    /**
     * 合同编号
     */
    private String htbh;

    /**
     * 合同名称
     */
    private String htmc;

    /**
     * 合同细类
     */
    @ExcelIgnore
    private Integer htxl;
    /**
     * 合同细类
     */
    @ExcelProperty({"合同细类"})
    private String htxlText;


}
