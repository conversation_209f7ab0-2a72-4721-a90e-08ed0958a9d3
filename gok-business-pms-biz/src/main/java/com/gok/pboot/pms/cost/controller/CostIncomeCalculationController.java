package com.gok.pboot.pms.cost.controller;

import com.gok.module.excel.api.annotation.ResponseExcel;
import com.gok.pboot.common.security.annotation.Inner;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.cost.entity.dto.CostIncomeCalculationDTO;
import com.gok.pboot.pms.cost.entity.vo.CostIncomeCalculationDetailVO;
import com.gok.pboot.pms.cost.entity.vo.CostIncomeCalculationVO;
import com.gok.pboot.pms.cost.entity.vo.CostTaskTopCountMsgVO;
import com.gok.pboot.pms.cost.service.ICostIncomeCalculationDetailService;
import com.gok.pboot.pms.cost.service.ICostIncomeCalculationService;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * 交付管理-收入测算前端控制器
 *
 * <AUTHOR>
 * @menu 交付管理-收入测算
 * @create 2025/02/18
 **/
@RestController
@AllArgsConstructor
@RequestMapping("/costIncomeCalculations")
public class CostIncomeCalculationController {

    private final ICostIncomeCalculationService calculationService;
    private final ICostIncomeCalculationDetailService calculationDetailService;
    public static final Integer DETAIL_FLAG = 0;

    /**
     * 汇总列表查询接口
     *
     * @return {@link ApiResult }<{@link CostTaskTopCountMsgVO }>
     */
    @GetMapping("/list/{projectId}")
    public ApiResult<List<CostIncomeCalculationVO>> findList(@PathVariable("projectId") Long projectId,
                                                             CostIncomeCalculationDTO request) {
        return ApiResult.success(calculationService.findList(projectId, request));
    }

    /**
     * 明细列表查询接口
     *
     * @return {@link ApiResult }<{@link CostTaskTopCountMsgVO }>
     */
    @GetMapping("/details/list/{projectId}")
    public ApiResult<List<CostIncomeCalculationDetailVO>> findDetailList(@PathVariable("projectId") Long projectId,
                                                                         CostIncomeCalculationDTO request) {
        return ApiResult.success(calculationDetailService.findDetailList(projectId, request));
    }

    /**
     * 生成或更新测算明细列表接口
     *
     * @return {@link ApiResult }<{@link CostTaskTopCountMsgVO }>
     */
    @Inner(value = false)
    @GetMapping("/details")
    public ApiResult<String> saveOrUpdateCostIncomeCalculation() {
        calculationDetailService.saveOrUpdateCostIncomeCalculation(null);
        return ApiResult.success("操作成功");
    }

    /**
     * 重新生成测算
     * request.ids:明细ID集合
     * request.source:数据来源
     *
     * @param request 请求
     */
    @PutMapping("/regenerate")
    public ApiResult<List<Long>> batchRegenerate(@Validated(CostIncomeCalculationDTO.UpdateValidation.class) @RequestBody CostIncomeCalculationDTO request) {
        return ApiResult.success(DETAIL_FLAG.equals(request.getSource())
                ? calculationDetailService.batchRegenerate(request)
                : calculationService.batchRegenerate(request));
    }

    /**
     * 根据项目ID重新生成测算
     *
     * @param projectId 项目ID
     * @return
     */
    @PutMapping("/regenerate/{projectId}")
    public ApiResult<String> regenerateByProjectId(@PathVariable("projectId") Long projectId) {
        calculationDetailService.saveOrUpdateCostIncomeCalculation(Arrays.asList(projectId));
        return ApiResult.success("操作成功");
    }

    /**
     * 数据批量确认
     * request.ids:明细ID集合
     * request.source:数据来源
     *
     * @param request 确认请求
     */
    @PutMapping("/confirm")
    public ApiResult<List<Long>> batchConfirm(@Validated(CostIncomeCalculationDTO.UpdateValidation.class) @RequestBody CostIncomeCalculationDTO request) {
        return ApiResult.success(DETAIL_FLAG.equals(request.getSource()) ? calculationDetailService.batchConfirm(request) : calculationService.batchConfirm(request));
    }

    /**
     * 取消确认
     * request.ids:明细ID集合
     * request.source:数据来源
     *
     * @param request 取消确认请求
     * @return
     */
    @PutMapping("/cancel/confirm")
    public ApiResult<List<Long>> cancelConfirm(@Validated(CostIncomeCalculationDTO.UpdateValidation.class) @RequestBody CostIncomeCalculationDTO request) {
        return ApiResult.success(DETAIL_FLAG.equals(request.getSource()) ? calculationDetailService.batchCancelConfirm(request) : calculationService.batchCancelConfirm(request));
    }

    /**
     * 批量结算收入测算
     *
     * @param request 结算请求
     * @return
     */
    @PutMapping("/settlement")
    public ApiResult<List<Long>> settlement(@Validated(CostIncomeCalculationDTO.UpdateValidation.class) @RequestBody CostIncomeCalculationDTO request) {
        return ApiResult.success(DETAIL_FLAG.equals(request.getSource()) ? calculationDetailService.batchSettlement(request) : calculationService.batchSettlement(request));
    }

    /**
     * 测算数据导出
     *
     * @param request 导出请求
     */
    @GetMapping("/export/{projectId}")
    @ResponseExcel(name = "收入测算")
    public void export(HttpServletResponse response, @PathVariable("projectId") Long projectId, CostIncomeCalculationDTO request) {
        request.setProjectIds(Arrays.asList(projectId));
        calculationService.export(response, request);
    }

    /**
     * 定时发送收入测算数据确认提醒
     *
     * @return
     */
    @Inner(value = false)
    @GetMapping("/unconfirmedMessages")
    public ApiResult<List<Long>> sendUnconfirmedMsg() {
        return ApiResult.success(calculationService.sendUnconfirmedMsg());
    }

}
