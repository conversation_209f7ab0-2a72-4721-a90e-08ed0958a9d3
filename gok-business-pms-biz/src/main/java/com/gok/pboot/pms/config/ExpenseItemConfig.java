package com.gok.pboot.pms.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * 费用项配置类
 * 用于配置不同表单类型、科目代码和订单类型对应的费用项
 *
 * <AUTHOR>
 * @date 2025-08-04
 */
@Data
@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "oa.expense")
public class ExpenseItemConfig {

    /**
     * 费用报销单(FYBXD)费用项配置
     * key: 科目代码_订单类型, value: 费用项编码
     */
    private Map<String, String> fybxd;

    /**
     * 商机项目费用报销(SJFYBX)费用项配置
     * key: 科目代码_订单类型, value: 费用项编码
     */
    private Map<String, String> sjfybx;

    /**
     * 在建项目费用报销(ZJXMFYBX)费用项配置
     * key: 科目代码_订单类型, value: 费用项编码
     */
    private Map<String, String> zjxmfybx;

    /**
     * 根据表单类型、科目代码和订单类型获取费用项
     *
     * @param formType 表单类型
     * @param subjectCode 科目代码
     * @param orderType 订单类型
     * @return 费用项编码
     */
    public String getExpenseItem(String formType, String subjectCode, String orderType) {
        String key = subjectCode + "_" + orderType;
        
        switch (formType) {
            case "FYBXD":
                return fybxd != null ? fybxd.get(key) : "";
            case "SJFYBX":
                return sjfybx != null ? sjfybx.get(key) : "";
            case "ZJXMFYBX":
                return zjxmfybx != null ? zjxmfybx.get(key) : "";
            default:
                return "";
        }
    }
}
