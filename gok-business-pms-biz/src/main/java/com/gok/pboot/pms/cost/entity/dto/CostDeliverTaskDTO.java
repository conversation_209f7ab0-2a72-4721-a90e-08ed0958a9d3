package com.gok.pboot.pms.cost.entity.dto;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 成本交付任务 DTO
 *
 * <AUTHOR>
 * @date 2025/01/15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CostDeliverTaskDTO {

    /**
     * id
     */
    private Long id;

    /**
     * 父级ID
     */
    private Long parentId;

    private Collection<Long> projectIds;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 工单名称
     */
    private String taskName;

    /**
     * 工单类型（0=售前支撑，1=售后交付）
     *
     * @see com.gok.pboot.pms.enumeration.ProjectTaskKindEnum
     */
    private Integer taskType;

    /**
     * 工单级别
     */
    private Integer taskLevel;

    /**
     * 工单描述
     */
    private String taskDesc;

    /**
     * 成本科目ID
     */
    private Long accountId;

    /**
     * 成本科目OA ID
     */
    private String accountOaId;

    /**
     * 成本科目名称
     */
    private String accountName;

    /**
     * 预算成本
     */
    private BigDecimal budgetCost;

    /**
     * 拆解类型（0=标准工单，1=总成工单）
     *
     * @see com.gok.pboot.pms.cost.enums.CostTaskDisassemblyTypeEnum
     */
    private Integer disassemblyType;

    /**
     * 工单负责人ID
     */
    private Long managerId;

    /**
     * 工单负责人姓名
     */
    private String managerName;

    /**
     * 开始时间
     */
    private LocalDate startDate;

    /**
     * 结束时间
     */
    private LocalDate endDate;

    /**
     * 交付说明
     */
    private String deliverDesc;

    /**
     * 完成佐证文档id，多个逗号隔开
     */
    private String completeFiles;

    /**
     * 工作日加班工时
     */
    private BigDecimal workOverTimeHours;

    /**
     * 休息日加班工时
     */
    private BigDecimal restOverTimeHours;

    /**
     * 节假日加班工时
     */
    private BigDecimal holidayOverTimeHours;

    /**
     * 退回原因
     */
    private String returnReason;

    /**
     * 退回时间
     */
    private LocalDateTime returnTime;

    /**
     * 退回人ID
     */
    private Long returnerId;

    /**
     * 退回人姓名
     */
    private String returnerName;

    /**
     * 完成时间
     */
    private LocalDateTime completionTime;

    /**
     * 提交完成时间
     */
    private LocalDateTime submitCompletionTime;

    /**
     * 开始提交完成时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date startSubmitCompletionTime;

    /**
     * 结束提交完成时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date endSubmitCompletionTime;

    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

    /**
     * 工单状态
     *
     * @see com.gok.pboot.pms.cost.enums.CostTaskStatusEnum
     */
    private Integer taskStatus;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 是否已审核（待审核0，已审核1）
     */
    private Integer approvalStatus;

    /**
     * 状态列表
     */
    private List<Integer> taskStatusList;

    /**
     *
     * 项目经理id
     */
    private Long projectManagerUserId;

    /**
     * 项目经理审核
     */
    private Integer projectManagerApproval;

    /**
     * 部门负责人id
     */
    private Long projectHeaderId;

    private Long userId;

    /**
     * 任务级别id
     */
    private List<Integer> taskLevelList;

    private List<Long> subUserIds;

    /**
     * 个人
     */
    private Boolean personal;
}
