package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;

/**
 * 业务角色内部代码枚举
 *
 * <AUTHOR>
 * @date 2024/02/04
 */
@AllArgsConstructor
public enum BusinessRoleCodeEnum implements ValueEnum<Integer> {

    PROJECT_SALES_MANAGER(0, "PROJECT_SALES_MANAGER"),
    PROJECT_PRE_SALES_MANAGER(1, "PROJECT_PRE_SALES_MANAGER"),
    PROJECT_MANAGER(2, "PROJECT_MANAGER"),
    PROJECT_OPERATIONS_ASSISTANT(3, "PROJECT_OPERATIONS_ASSISTANT"),
    PROJECT_MEMBER(4, "PROJECT_MEMBER"),
    TASK_LEADER(5, "TASK_LEADER");

    private final Integer value;

    private final String name;

    @Override
    public Integer getValue() {
        return value;
    }

    @Override
    public String getName() {
        return name;
    }

}
