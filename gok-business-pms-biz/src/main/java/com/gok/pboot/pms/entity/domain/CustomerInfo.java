package com.gok.pboot.pms.entity.domain;


import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@TableName( "customer_info")
public class CustomerInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;

    /**
     * 客户名称
     */
    private String khmc;
    /**
     * 客户分级
     */
    private Integer khfj;
    /**
     * 客户所在地
     */
    private Integer khszd;
    /**
     * 行业"
     */
    private Integer khxyyj;
    /**
     * 归属业务部门
     */
    private Long gsywbmejbm;
    /**
     * 客户经理id
     */
    private String khjl;
    /**
     * 客户经理名字
     */
    private String khjlxm;
    /**
     * 客户编号
     */
    private String khbh;
    /**
     * 是否集团类客户
     */
    private Integer sfjtlkh;
    /**
     * 备案人姓名
     */
    private String barxm;
    /**
     * 联系人1
     */
    private String lxr1;
    /**
     * 联系人角色1
     */
    private String lxrjs1;
    /**
     * 联系电话1
     */
    private String lxdh1;
    /**
     * 申请时间1
     */
    private String sqsj1;
    /**
     * 联系人2
     */
    private String lxr2;
    /**
     * 联系人角色2
     */
    private String lxrjs2;
    /**
     * 联系电话2
     */
    private String lxdh2;
    /**
     * 申请时间2
     */
    private String sqsj2;
    /**
     * 联系人3
     */
    private String lxr3;
    /**
     * 联系人角色3
     */
    private String lxrjs3;
    /**
     * 联系电话3
     */
    private String lxdh3;
    /**
     * 申请时间3
     */
    private String sqsj3;
    /**
     * 开票邮寄地址
     */
    private String kpyjxx;
    /**
     * 开票开户银行
     */
    private String kpkhyx;
    /**
     * 开票开户行账号
     */
    private String kpkhxzh;
    /**
     * 开票电话
     */
    private String kpdh;
    /**
     * 统一社会信用代码
     */
    private String tyshxydm;
    /**
     * 开票地址
     */
    private String kpdz;
    /**
     * 开票附件
     */
    private String kpfj;
    /**
     * 客户备案流程id
     */
    private String khbalcRequestid;
    /**
     * 客户备案流程Oaid
     */
    private String khbalcRelateid;

    /**
     * 创建时间
     */
    private LocalDateTime ctime;
}