package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;

/**
 * 项目周报提交状态
 *
 * <AUTHOR>
 * @date 2023/10/26
 */
@AllArgsConstructor
public enum WeeklySubmitStateEnum implements ValueEnum<Integer> {
    /**
     * 未提交
     */
    UN_SUBMIT(0, "未提交"),
    /**
     * 已提交
     */
    SUBMIT(1, "已提交"),
    /**
     * 滞后提交
     */
    LATE_SUBMIT(2, "滞后提交");

    /**
     * 值
     */
    private final Integer value;

    /**
     * 名称
     */
    private final String name;


    @Override
    public Integer getValue() {
        return value;
    }

    @Override
    public String getName() {
        return name;
    }
}
