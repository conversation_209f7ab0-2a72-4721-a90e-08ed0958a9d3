package com.gok.pboot.pms.cost.enums;

import com.gok.pboot.pms.enumeration.ValueEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 成本流程状态 枚举
 *
 * <AUTHOR>
 * @date 2025/01/08
 */
@AllArgsConstructor
@Getter
public enum CostRequestStatusEnum implements ValueEnum<Integer> {

    /**
     * 未提交
     */
    UNCOMMIT(0, "未提交", 0),

    /**
     * 审批中
     */
    INAPPROVAL(1, "审批中", 1),

    /**
     * 已归档
     */
    FINISH(2, "已归档", 3),

    /**
     * 部分提交
     */
    PARTIALLY_COMMIT(3, "部分提交", -1);

    private final Integer value;

    private final String name;

    //对应OA流程状态
    private final Integer currentNodeType;

    public static Integer getValByNodeType(Integer currentNodeType) {
        for (CostRequestStatusEnum statusEnum : CostRequestStatusEnum.values()) {
            if (statusEnum.getCurrentNodeType().equals(currentNodeType)) {
                return statusEnum.getValue();
            }
        }
        return null;
    }

    public static String getNameByNodeType(Integer currentNodeType) {
        for (CostRequestStatusEnum statusEnum : CostRequestStatusEnum.values()) {
            if (statusEnum.getCurrentNodeType().equals(currentNodeType)) {
                return statusEnum.getName();
            }
        }
        return null;
    }
}
