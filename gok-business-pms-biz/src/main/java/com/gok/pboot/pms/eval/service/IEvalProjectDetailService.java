package com.gok.pboot.pms.eval.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.eval.entity.domain.EvalProjectDetail;
import com.gok.pboot.pms.eval.entity.domain.EvalProjectOverview;
import com.gok.pboot.pms.eval.entity.vo.EvalProjectDetailVO;
import com.gok.pboot.pms.eval.entity.vo.EvalProjectOverviewVO;

import java.util.List;
import java.util.Map;


/**
 * 项目评价Service
 *
 * <AUTHOR>
 * @date 2025/05/08
 */
public interface IEvalProjectDetailService extends IService<EvalProjectDetail> {

    /**
     * 根据项目整体评价自动保存项目评价
     *
     * @param evalProjectOverviews 项目整体评价集合
     * @return
     */
    List<Long> autoSaveEvalProjectDetail(List<EvalProjectOverview> evalProjectOverviews);

    /**
     * 批量获取项目评价集合
     *
     * @param overviewVOList 项目整体评价VO集合
     * @return 项目ID -> 项目评价集合的映射
     */
    Map<Long, List<EvalProjectDetailVO>> findEvalProjectDetailVOList(List<EvalProjectOverviewVO> overviewVOList);

} 