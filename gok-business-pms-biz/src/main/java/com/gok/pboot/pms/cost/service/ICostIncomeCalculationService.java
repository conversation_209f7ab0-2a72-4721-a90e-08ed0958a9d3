package com.gok.pboot.pms.cost.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.cost.entity.domain.CostIncomeCalculation;
import com.gok.pboot.pms.cost.entity.domain.CostIncomeCalculationDetail;
import com.gok.pboot.pms.cost.entity.dto.CostIncomeCalculationDTO;
import com.gok.pboot.pms.cost.entity.vo.CostExpensesShareDetailsVO;
import com.gok.pboot.pms.cost.entity.vo.CostIncomeCalculationVO;
import com.gok.pboot.pms.entity.vo.AllocationFindPageVO;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 收入测算服务接口
 *
 * <AUTHOR>
 * @create 2025/02/18
 **/
public interface ICostIncomeCalculationService extends IService<CostIncomeCalculation> {

    /**
     * 汇总列表查询
     *
     * @param projectId
     * @param request
     * @return
     */
    List<CostIncomeCalculationVO> findList(@PathVariable("projectId") Long projectId,
                                           CostIncomeCalculationDTO request);

    /**
     * 更新或新增收入测算汇总
     *
     * @param detailList               收入测算明细集合[]
     * @param allocationList           对应项目分摊工时集合[]
     * @param expensesShareDetailsList 费用分摊明细集合[]
     */
    List<CostIncomeCalculation> batchSaveOrUpdate(List<CostIncomeCalculationDetail> detailList,
                                                  List<AllocationFindPageVO> allocationList,
                                                  List<CostExpensesShareDetailsVO> expensesShareDetailsList);

    /**
     * 批量确认收入测算数据
     *
     * @param request
     * @return
     */
    List<Long> batchConfirm(CostIncomeCalculationDTO request);

    /**
     * 批量取消确认
     *
     * @param request
     * @return
     */
    List<Long> batchCancelConfirm(CostIncomeCalculationDTO request);

    /**
     * 批量结算汇总数据
     *
     * @param request
     * @return
     */
    List<Long> batchSettlement(CostIncomeCalculationDTO request);

    /**
     * 批量重新生成收入测算数据
     *
     * @return 收入测算ID
     */
    List<Long> batchRegenerate(CostIncomeCalculationDTO request);

    /**
     * 导出
     *
     * @param response
     * @param request
     */
    void export(HttpServletResponse response, @RequestBody CostIncomeCalculationDTO request);

    /**
     * 收入测算数据确认提醒
     *
     * @return 发送收入测算数据确认提醒的项目ID集合
     */
    List<Long> sendUnconfirmedMsg();

}
