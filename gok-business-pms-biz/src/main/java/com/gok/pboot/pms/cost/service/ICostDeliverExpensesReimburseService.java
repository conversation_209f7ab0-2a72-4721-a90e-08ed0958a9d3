package com.gok.pboot.pms.cost.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.cost.entity.domain.CostDeliverExpensesReimburse;
import com.gok.pboot.pms.cost.entity.dto.CostDeliverPurchasePlanDTO;
import com.gok.pboot.pms.cost.entity.dto.DeliverCostBudgetListDto;
import com.gok.pboot.pms.cost.entity.dto.DeliverExpensesReimburseListDto;
import com.gok.pboot.pms.cost.entity.vo.CostDeliverPurchasePlanListVO;
import com.gok.pboot.pms.cost.entity.vo.CostDeliverPurchasePlanVO;
import com.gok.pboot.pms.cost.entity.vo.DeliverCostBudgetListVO;
import com.gok.pboot.pms.cost.entity.vo.DeliverExpensesReimburseListVO;

import java.math.BigDecimal;
import java.util.List;

/**
 * 成本交付管理费用报销 服务类
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
public interface ICostDeliverExpensesReimburseService extends IService<CostDeliverExpensesReimburse> {


    /**
     * 交付管理费用预算列表
     * @param dto
     * @return
     */
    List<DeliverCostBudgetListVO> findDeliverCostBudget(DeliverCostBudgetListDto dto);

    /**
     * 通过费用报销查询成本预算明细对应已用预算、剩余预算
     *
     * @param projectId          项目id
     * @param costBudgetListList 人工成本预算明细
     */
    void getUsedCostByReimburse(Long projectId, List<DeliverCostBudgetListVO> costBudgetListList);

    /**
     * 通过采购付款查询成本预算明细对应已用预算、剩余预算
     *
     * @param projectId          项目id
     * @param costBudgetListList 人工成本预算明细
     */
    void getUsedCostByPurchase(Long projectId, List<DeliverCostBudgetListVO> costBudgetListList);

    /**
     * 交付管理费用报销台账
     * @param reimburseListDto
     * @return
     */
    Page<DeliverExpensesReimburseListVO> findExpensesReimburse(DeliverExpensesReimburseListDto reimburseListDto);


    /**
     * 采购计划列表
     * @param projectId
     * @return
     */
    List<CostDeliverPurchasePlanListVO> findPlanList(Long projectId);

    /**
     * 采购计划详情
     * @param id
     * @return
     */
    CostDeliverPurchasePlanVO getPlan(Long id);

    /**
     * 新增或编辑采购计划
     * @param dto
     * @return
     */
    ApiResult<String> addOrEditPlan(CostDeliverPurchasePlanDTO dto);

    /**
     * 删除采购计划
     * @param id
     * @return
     */
    ApiResult<String> delPlan(Long id);

    /**
     * 获取预计总产值
     * 当项目只有A表时，所有一级工单的产值加总不可超过 A表预算收入总额-外采预算成本
     * 当项目有B表数据时，所有一级工单的产值加总不可超过B表预算收入总额-外采预算成本
     *
     * @param projectId 项目ID
     * @return 预计总产值
     */
    BigDecimal getEstimatedTotalIncome(Long projectId);
}
