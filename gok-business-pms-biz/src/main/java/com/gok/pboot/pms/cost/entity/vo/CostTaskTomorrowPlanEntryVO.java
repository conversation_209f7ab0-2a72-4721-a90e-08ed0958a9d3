package com.gok.pboot.pms.cost.entity.vo;

import com.gok.pboot.pms.common.join.ProjectInDailyPaperEntry;
import com.gok.pboot.pms.cost.entity.domain.CostTomorrowPlanPaperEntry;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * 工单工时明日计划条目VO
 *
 * <AUTHOR>
 * @date 2024/04/16
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CostTaskTomorrowPlanEntryVO {

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 日报ID
     */
    private Long dailyPaperId;

    /**
     * 审核状态（0=未提交，1=已退回，2=待审核，3=不通过，4=已通过）
     */
    private Integer approvalStatus;

    /**
     * 提交日期
     */
    private LocalDate submissionDate;

    /**
     * 工时类型（0=售前支撑，1=售后交付）
     */
    private Integer workType;

    /**
     * 工时类型（0=售前支撑，1=售后交付）
     */
    private String workTypeTxt;

    /**
     * 描述
     */
    private String description;

    /**
     * 审核不通过原因
     */
    private String approvalReason;

    /**
     * 提交人ID
     */
    private Long userId;

    /**
     * 提交人姓名
     */
    private String userRealName;

    /**
     * 提交人所属部门ID
     */
    private Long userDeptId;

    /**
     * 是否内部项目
     */
    private Integer isInsideProject;

    /**
     * 旧任务标识
     */
    private Integer oldTaskFlag;

    /**
     * 任务结束标识
     */
    private Boolean taskFinishFlag;

    public CostTaskTomorrowPlanEntryVO(CostTomorrowPlanPaperEntry entry) {
        this.projectId = entry.getProjectId();
        this.projectName = entry.getProjectName();
        this.taskId = entry.getTaskId();
        this.taskName = entry.getTaskName();
        this.dailyPaperId = entry.getDailyPaperId();
        this.approvalStatus = entry.getApprovalStatus();
        this.submissionDate = entry.getSubmissionDate();
        this.workType = entry.getWorkType();
        this.description = entry.getDescription();
        this.approvalReason = entry.getApprovalReason();
        this.userId = entry.getUserId();
        this.userRealName = entry.getUserRealName();
        this.userDeptId = entry.getUserDeptId();
        this.oldTaskFlag = entry.getOldTaskFlag();
    }

    public CostTaskTomorrowPlanEntryVO(CostTomorrowPlanPaperEntry entry, ProjectInDailyPaperEntry project) {
        this(entry);
        this.isInsideProject = project.getIsInsideProject();
    }
} 