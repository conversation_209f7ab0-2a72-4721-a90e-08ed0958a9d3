package com.gok.pboot.pms.entity.dto;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * @Description: 项目风险 修改风险应对计划的Dto类
 * @Author: x<PERSON><PERSON><PERSON>
 * @Data: 2023/8/18 14:34
 */
@Data
public class ProjectResponsePlanDTO {

    /**
     * 项目风险主键id
     */
    private Long id;

    /**
     * 风险应对计划
     */
    @NotBlank(message = "风险应对计划不能为空")
    @Length(max = 500, message = "风险应对计划长度需小于等于500")
    private String responsePlan;


}
