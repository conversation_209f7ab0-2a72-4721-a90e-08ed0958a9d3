package com.gok.pboot.pms.eval.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.bcp.message.dto.BcpMessageTargetDTO;
import com.gok.bcp.message.entity.enums.ChannelEnum;
import com.gok.bcp.message.entity.enums.MsgTypeEnum;
import com.gok.bcp.message.entity.enums.TargetTypeEnum;
import com.gok.pboot.pms.common.exception.ServiceException;
import com.gok.pboot.pms.entity.domain.ProjectInfo;
import com.gok.pboot.pms.entity.dto.BaseSendMsgDTO;
import com.gok.pboot.pms.entity.dto.BaseSendMsgOutDTO;
import com.gok.pboot.pms.eval.entity.domain.EvalCustomerSatisfactionSurvey;
import com.gok.pboot.pms.eval.entity.dto.EvalCustomerSatisfactionSurveyDTO;
import com.gok.pboot.pms.eval.entity.vo.EvalCustomerSatisfactionSurveyVO;
import com.gok.pboot.pms.eval.entity.vo.EvalSatisfactionSurveyProjectVo;
import com.gok.pboot.pms.eval.enums.EvalSatisfactionResultEnum;
import com.gok.pboot.pms.eval.enums.EvalSatisfactionSurveyStatusEnum;
import com.gok.pboot.pms.eval.enums.EvalSendStatusEnum;
import com.gok.pboot.pms.eval.mapper.EvalCustomerSatisfactionSurveyMapper;
import com.gok.pboot.pms.eval.service.IEvalCustomerSatisfactionSurveyService;
import com.gok.pboot.pms.eval.service.IEvalProjectOverviewService;
import com.gok.pboot.pms.service.BcpMessageService;
import com.gok.pboot.pms.service.IHolidayService;
import com.gok.pboot.pms.service.IProjectInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static com.gok.pboot.pms.eval.entity.vo.EvalCustomerSatisfactionSurveyVO.convertToVo;
import static com.gok.pboot.pms.eval.entity.vo.EvalCustomerSatisfactionSurveyVO.emptyVo;
import static com.gok.pboot.pms.eval.entity.vo.EvalSatisfactionSurveyProjectVo.voToEntitySave;

/**
 * 客户满意度调查Service实现类
 *
 * <AUTHOR>
 * @date 2025/05/08
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EvalCustomerSatisfactionSurveyServiceImpl extends ServiceImpl<EvalCustomerSatisfactionSurveyMapper, EvalCustomerSatisfactionSurvey>
        implements IEvalCustomerSatisfactionSurveyService {

    private static final Integer WORK_DAYS_THRESHOLD = 10;

    private final IProjectInfoService projectInfoService;

    private final BcpMessageService bcpMessageService;

    private final IHolidayService holidayService;

    private final IEvalProjectOverviewService evalProjectOverviewService;

    private static final String SATISFACTION_SURVEY_URL = "https://www.goktech.cn/satisfaction/";

    /**
     * 按项目 ID 获取满意度调查
     *
     * @param projectId 项目 ID
     * @return {@link EvalCustomerSatisfactionSurveyVO }
     */
    @Override
    public EvalCustomerSatisfactionSurveyVO getSatisfactionSurveyByProjectId(Long projectId) {
        if (projectId == null) {
            throw new ServiceException("项目ID不能为空");
        }

        // 获取项目数据
        ProjectInfo projectInfo = projectInfoService.getById(projectId);

        if (projectInfo == null) {
            throw new ServiceException("未找到该项目");
        }

        // 查询数据
        EvalCustomerSatisfactionSurvey customerSatisfactionSurvey = this.lambdaQuery()
                .eq(EvalCustomerSatisfactionSurvey::getProjectId, projectId)
                .eq(EvalCustomerSatisfactionSurvey::getSendStatus, EvalSendStatusEnum.SENT.getValue())
                .one();

        // 如果为空，则返回空对象
        if (customerSatisfactionSurvey == null) {
            return emptyVo(projectInfo);
        }
        // 转换为VO对象
        return convertToVo(customerSatisfactionSurvey, projectInfo);
    }

    /**
     * 更新满意度调查
     *
     * @param dto DTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSatisfactionSurvey(EvalCustomerSatisfactionSurveyDTO dto) {
        // 查询现有数据
        EvalCustomerSatisfactionSurvey satisfactionSurvey = this.lambdaQuery()
                .eq(EvalCustomerSatisfactionSurvey::getId, dto.getId())
                .eq(EvalCustomerSatisfactionSurvey::getProjectId, dto.getProjectId())
                .one();

        if (satisfactionSurvey == null) {
            throw new ServiceException("未找到该项目的满意度调查数据");
        }

        if (EvalSatisfactionSurveyStatusEnum.EVALUATED.getValue().equals(satisfactionSurvey.getEvalStatus())) {
            throw new ServiceException("该项目的满意度调查已评价，请勿重复评价");
        }

        // 计算工作日
        LocalDate evaluationDate = LocalDate.now();
        LocalDate releaseDate = satisfactionSurvey.getReleaseDate();
        if (releaseDate == null) {
            throw new ServiceException("该项目的满意度调查的调查下发日期不能为空");
        }

        // 获取指定时间内工作日天数
        Integer workDays = holidayService.getRequiredAttendanceDays(releaseDate, evaluationDate);

        // 检查是否超过10个工作日
        if (workDays <= WORK_DAYS_THRESHOLD) {
            // 更新评价数据
            satisfactionSurvey.setProblemResponseResult(dto.getProblemResponseResult())
                    .setDesignSchemeResult(dto.getDesignSchemeResult())
                    .setProgressControlResult(dto.getProgressControlResult())
                    .setQualityControlResult(dto.getQualityControlResult())
                    .setServiceAttitudeCommunicationResult(dto.getServiceAttitudeCommunicationResult())
                    .setOtherSuggestion(dto.getOtherSuggestion())
                    .setSurveyDate(evaluationDate)
                    .setEvalStatus(EvalSatisfactionSurveyStatusEnum.EVALUATED.getValue())
                    .setTotalScore(calculateAverageScore(satisfactionSurvey));

            // 更新数据
            this.updateById(satisfactionSurvey);

            // 更新项目评价校准
            evalProjectOverviewService.batchCalibrateEval(Collections.singletonList(satisfactionSurvey));
        }
    }

    /**
     * 设置默认的满意评价结果
     *
     * @param satisfactionSurvey 满意度调查
     * @param evaluationDate     评估日期
     */
    private void setDefaultSatisfiedResults(EvalCustomerSatisfactionSurvey satisfactionSurvey, LocalDate evaluationDate) {
        Integer satisfiedValue = EvalSatisfactionResultEnum.SATISFIED.getValue();
        satisfactionSurvey.setProblemResponseResult(satisfiedValue)
                .setDesignSchemeResult(satisfiedValue)
                .setProgressControlResult(satisfiedValue)
                .setQualityControlResult(satisfiedValue)
                .setServiceAttitudeCommunicationResult(satisfiedValue)
                .setTotalScore(calculateAverageScore(satisfactionSurvey))
                .setSurveyDate(evaluationDate);
    }

    /**
     * 计算平均分
     *
     * @param satisfactionSurvey 满意度调查
     * @return {@link BigDecimal }
     */
    private BigDecimal calculateAverageScore(EvalCustomerSatisfactionSurvey satisfactionSurvey) {
        List<Integer> scores = Arrays.asList(
                satisfactionSurvey.getProblemResponseResult(),
                satisfactionSurvey.getDesignSchemeResult(),
                satisfactionSurvey.getProgressControlResult(),
                satisfactionSurvey.getQualityControlResult(),
                satisfactionSurvey.getServiceAttitudeCommunicationResult()
        );

        BigDecimal total = BigDecimal.ZERO;
        for (Integer score : scores) {
            total = total.add(BigDecimal.valueOf(score));
        }

        return total.divide(BigDecimal.valueOf(scores.size()), 2, RoundingMode.HALF_UP);
    }

    /**
     * 处理逾期满意度调查
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleOverdueSurveyList() {
        // 查询未评价的满意度调查
        List<EvalCustomerSatisfactionSurvey> unEvaluatedSurveyList = this.lambdaQuery()
                .eq(EvalCustomerSatisfactionSurvey::getSendStatus, EvalSendStatusEnum.SENT.getValue())
                .eq(EvalCustomerSatisfactionSurvey::getEvalStatus, EvalSatisfactionSurveyStatusEnum.UNEVALUATED.getValue())
                .list();

        // 如果没有未评价的满意度调查，则直接返回
        if (CollUtil.isEmpty(unEvaluatedSurveyList)) {
            return;
        }

        List<EvalCustomerSatisfactionSurvey> overdueSurveyList = new ArrayList<>();

        LocalDate nowDate = LocalDate.now();
        // 遍历未评价的满意度调查
        for (EvalCustomerSatisfactionSurvey satisfactionSurvey : unEvaluatedSurveyList) {

            LocalDate releaseDate = satisfactionSurvey.getReleaseDate();
            if (releaseDate == null) {
                continue;
            }

            // 获取指定时间内工作日天数
            Integer workDays = holidayService.getRequiredAttendanceDays(releaseDate, nowDate);

            // 如果超过10个工作日
            if (workDays > WORK_DAYS_THRESHOLD) {
                // 设置为超期未评价状态
                satisfactionSurvey.setEvalStatus(EvalSatisfactionSurveyStatusEnum.OVERDUE.getValue());
                // 所有评价设置为满意
                setDefaultSatisfiedResults(satisfactionSurvey, nowDate);

                // 添加到列表中
                overdueSurveyList.add(satisfactionSurvey);
            }
        }

        // 批量更新数据
        if (CollUtil.isNotEmpty(overdueSurveyList)) {
            this.updateBatchById(overdueSurveyList);
        }

        // 同步变更项目评价
        evalProjectOverviewService.batchCalibrateEval(overdueSurveyList);
    }

    /**
     * 满意度调查消息推送
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void satisfactionSurveyMsgPush() {
        // 当前调查下发日期
        LocalDate releaseDate = LocalDate.now();

        // 获取结项项目且该项目没有在满意度调查中
        List<EvalSatisfactionSurveyProjectVo> satisfactionSurveyProjectVoList =
                projectInfoService.getCompletedProjectsWithoutSatisfactionSurvey();

        // 如果没有结项项目，则直接返回
        if (CollUtil.isEmpty(satisfactionSurveyProjectVoList)) {
            return;
        }

        // 获邮件推送数据集合
        List<BaseSendMsgDTO> baseSendMsgDTOList = new ArrayList<>();

        // 获取要保存的满意度调查集合
        List<EvalCustomerSatisfactionSurvey> satisfactionSurveyList = new ArrayList<>();

        for (EvalSatisfactionSurveyProjectVo vo : satisfactionSurveyProjectVoList) {
            // 添加到列表中
            satisfactionSurveyList.add(voToEntitySave(vo, releaseDate));

            // 创建消息推送数据
            BaseSendMsgDTO baseSendMsgDTO = sendSatisfactionSurveyMsg(vo);

            // 添加到列表中
            baseSendMsgDTOList.add(baseSendMsgDTO);
        }

        // 批量保存数据
        if (CollUtil.isNotEmpty(satisfactionSurveyList)) {
            // 保存满意度调查
            this.saveBatch(satisfactionSurveyList);

            // 发送邮件通知
            sendSatisfactionSurveyEmail(satisfactionSurveyList);

            // 消息推送到客户经理
            bcpMessageService.batchSendMsg(baseSendMsgDTOList);
        }
    }

    /**
     * 发送满意度调查 MSG
     *
     * @param vo VO
     * @return {@link BaseSendMsgDTO }
     */
    private BaseSendMsgDTO sendSatisfactionSurveyMsg(EvalSatisfactionSurveyProjectVo vo) {
        // 项目名称
        String projectName = vo.getProjectName();
        // 客户经理ID
        Long salesmanUserId = vo.getSalesmanUserId();
        // 客户经理名称
        String salesmanUser = vo.getProjectSalesperson();
        // 客户名称
        String customerName = vo.getCustomerName();
        // 发送消息通知 对象：项目销售
        BaseSendMsgDTO baseSendMsgDTO = new BaseSendMsgDTO();
        final String title = "客户满意度调查抄送提醒";
        String content = String.format("您好，%s项目的客户满意度调查已下发短信给客户%s，请提醒客户及时配合完成填写～", projectName, customerName);
        return baseSendMsgDTO.setTitle(title)
                .setContent(content)
                .setMsgTypeEnum(MsgTypeEnum.NOTICE_MSG)
                .setTargetTypeEnum(TargetTypeEnum.USERS)
                .setPath(null)
                .setChannelEnums(CollUtil.newLinkedHashSet(ChannelEnum.WECOM, ChannelEnum.MAIL))
                .toOneTarget(salesmanUserId, salesmanUser)
                .populateSender();
    }

    /**
     * 发送满意度调查短信
     *
     * @param satisfactionSurveyList 满意度调查列表
     */
    private void sendSatisfactionSurveySms(List<EvalCustomerSatisfactionSurvey> satisfactionSurveyList) {
        satisfactionSurveyList.forEach(item -> {
            // 项目ID
            Long projectId = item.getProjectId();
            // 客户干系人ID
            Long stakeholderId = item.getStakeholderId();
            // 客户名称
            String customerName = item.getCustomerName();
            // 客户联系方式
            String contactPhone = item.getContactPhone();
            // 创建发送短信通知 对象：客户
            BaseSendMsgOutDTO baseSendMsgOutDTO = new BaseSendMsgOutDTO();
            final String title = "满意度调查";
            String surveyUrl = SATISFACTION_SURVEY_URL + projectId;
            String content = String.format("尊敬的%s，您好！感谢您对福建国科信息科技有限公司的支持，为持续优化服务诚邀您参与满意度调研，点击%s参与，拒收请回复R",
                    customerName, surveyUrl);
            baseSendMsgOutDTO.setTitle(title)
                    .setContent(content)
                    .setProjectId(String.valueOf(projectId))
                    .setMsgTypeEnum(MsgTypeEnum.TEXT_MSG)
                    .setTargetTypeEnum(TargetTypeEnum.USERS)
                    .toOneTarget(stakeholderId, customerName, contactPhone)
                    .populateSender();
            try {
                bcpMessageService.sendSmsMsg(baseSendMsgOutDTO);
            } catch (Exception e) {
                log.error("发送短信失败：{}", e.getMessage());
            }

        });
    }

    /**
     * 发送满意度调查邮件
     *
     * @param satisfactionSurveyList 满意度调查列表
     */
    private void sendSatisfactionSurveyEmail(List<EvalCustomerSatisfactionSurvey> satisfactionSurveyList) {
        satisfactionSurveyList = Optional.ofNullable(satisfactionSurveyList).orElse(Collections.emptyList()).stream()
                .filter(item -> StrUtil.isNotBlank(item.getEmail()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(satisfactionSurveyList)) {
            return;
        }

        List<BaseSendMsgDTO> msgDTOList = new ArrayList<>(satisfactionSurveyList.size());
        final String title = "满意度调查";
        for (EvalCustomerSatisfactionSurvey item : satisfactionSurveyList) {
            Long projectId = item.getProjectId();
            String customerName = item.getCustomerName();
            String surveyUrl = SATISFACTION_SURVEY_URL + projectId;
            String content = StrUtil.format(
                    "<p>尊敬的{}：</p>" +
                    "<p>&nbsp;&nbsp;&nbsp;&nbsp;您好！</p>" +
                    "<p>&nbsp;&nbsp;&nbsp;&nbsp;感谢您对我司福建国科信息科技有限公司的大力帮助与支持！</p>" +
                    "<p>&nbsp;&nbsp;&nbsp;&nbsp;追求客户满意度是我司一贯的宗旨，为了提升我司的团队能力及服务质量，我们将占用您几分钟宝贵的时间，敬请您参与填写此调查表。我们会积极采纳您的宝贵建议和意见，并持续改进我们的服务，提升团队的能力，谢谢您的配合。</p>" ,
                    customerName)
                    + "<p><a href=\"" + surveyUrl + "\" style=\"color: blue;\">" + "点击进入评价。</a></p>";
            BcpMessageTargetDTO targetDTO =
                    new BcpMessageTargetDTO(item.getEmail(), customerName, null);
            BaseSendMsgDTO baseSendMsgDTO = new BaseSendMsgDTO()
                    .setTitle(title)
                    .setContent(content)
                    .setMsgTypeEnum(MsgTypeEnum.TEXT_MSG)
                    .setTargetTypeEnum(TargetTypeEnum.USERS)
                    .setChannelEnums(CollUtil.newLinkedHashSet(ChannelEnum.EMAIL))
                    .setTargetList(Collections.singletonList(targetDTO))
                    .populateSender();
            msgDTOList.add(baseSendMsgDTO);
        }
        bcpMessageService.batchSendMsg(msgDTOList);
    }

}