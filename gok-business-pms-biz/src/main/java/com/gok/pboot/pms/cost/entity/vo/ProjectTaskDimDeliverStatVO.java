package com.gok.pboot.pms.cost.entity.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gok.pboot.common.secret.AESEncryptor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * 工单维度交付任务统计信息 vo
 *
 * <AUTHOR>
 * @date 2025/05/13
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ProjectTaskDimDeliverStatVO extends ProjectDeliverStatBaseVO {

    /**
     * 工单 ID
     */
    @ExcelIgnore
    private String taskId;

    /**
     * 人员id
     */
    @ExcelIgnore
    private Long userId;

    /**
     * 项目编号
     */
    @ExcelProperty("项目编号")
    private String projectNo;

    /**
     * 项目名称
     */
    @ExcelProperty("项目名称")
    private String projectName;

    /**
     * 员工工号
     */
    @ExcelProperty("员工工号")
    private String workCode;

    /**
     * 员工姓名
     */
    @ExcelProperty("员工姓名")
    private String employeeName;


    /**
     * 工单编号
     */
    @ExcelProperty("工单编号")
    private String taskNo;

    /**
     * 工单名称
     */
    @ExcelProperty("工单名称")
    private String taskName;

    /**
     * 结算工单产值
     */
    @ExcelProperty("结算工单产值")
    private BigDecimal settleIncome;

    /**
     * 结算工单产值占比
     * 单项目个人工单结算产值 /  单项目个人所有工单结算产值
     */
    @ExcelProperty("结算工单产值占比")
    private BigDecimal settlementValueRatio;

    /**
     * 工单综合评分
     */
    @ExcelProperty("工单综合评分")
    private BigDecimal comprehensiveScore;

    /**
     * 工单预算人工成本
     */
    @ExcelProperty("工单预算人工成本")
    private BigDecimal budgetCost;

    /**
     * 工单实际已发生人工成本
     */
    @ExcelProperty("工单实际人工成本")
    private BigDecimal actualLaborCost;


    /**
     * 工单预算工时
     */
    @ExcelProperty("工单预算工时")
    private BigDecimal estimatedHours;

    /**
     * 工单实际工时
     *
     */
    @ExcelProperty("工单实际工时")
    private BigDecimal actualHours;


    /**
     * 工单状态
     */
    @ExcelIgnore
    private Integer taskStatus;

    /**
     * 工单状态
     */
    @ExcelProperty("工单状态")
    private String taskStatusStr;


    /**
     * 工单预算人工成本
     */
    @ExcelIgnore
    private String budgetLaborCostStr;

    /**
     * 工单实际人工成本
     */
    @ExcelIgnore
    private String actualLaborCostStr;



    /**
     * 解密
     *
     */
    public void decrypt() {
        String budgetCost = AESEncryptor.justDecrypt(this.budgetLaborCostStr);
        if (StringUtils.isNotBlank(budgetCost)) {
            this.setBudgetCost(new BigDecimal(budgetCost));
        }
        String actualLaborCost = AESEncryptor.justDecrypt(this.actualLaborCostStr);
        if (StringUtils.isNotBlank(actualLaborCost)) {
            this.setActualLaborCost(new BigDecimal(actualLaborCost));
        }
    }
}
