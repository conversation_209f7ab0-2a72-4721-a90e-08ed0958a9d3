package com.gok.pboot.pms.eval.entity.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 项目整体评价分布
 * <AUTHOR>
 * @create 2025/5/16
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("eval_project_overall_distribution")
public class EvalProjectOverallDistribution  extends BeanEntity<Long> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 项目整体评分等级
    */
    private String projectOverallGrade;

    /**
    * 项目整体起始评分
    */
    private BigDecimal overallStartingScore;

    /**
    * 项目整体截止评分
    */
    private BigDecimal overallEndScore;

    /**
    * 建议分布评分等级
    */
    private String evalGrade;

    /**
    * 建议分布起始范围
    */
    private BigDecimal startingRange;

    /**
    * 建议分布截止范围
    */
    private BigDecimal endRange;
}