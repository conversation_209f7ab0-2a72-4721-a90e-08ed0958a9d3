package com.gok.pboot.pms.cost.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 收入结算发起结算明细表审批DTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CostIncomeSettlementRequestDetailDTO {

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 结算开始日期
     */
    private LocalDate startDate;

    /**
     * 结算截止日期
     */
    private LocalDate endDate;

    /**
     * 结算含税金额
     */
    private BigDecimal budgetAmountIncludedTax;

    /**
     * 税率OA字典ID
     */
    private String taxRate;

    /**
     * 结算不含税金额
     */
    private BigDecimal budgetAmountExcludingTax;

    /**
     * 结算单号
     */
    private String number;

    /**
     * oa流程id
     */
    private Long requestId;
}
