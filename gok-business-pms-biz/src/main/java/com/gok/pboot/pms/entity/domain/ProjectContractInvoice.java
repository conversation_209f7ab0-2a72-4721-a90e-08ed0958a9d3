package com.gok.pboot.pms.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 发票记录（数仓同步）
 *
 * <AUTHOR>
 * @date 2024-02-26 14:58:58
 */
@Data
@TableName("contract_invoice")
public class ProjectContractInvoice extends Model<ProjectContractInvoice> {
    private static final long serialVersionUID = 1L;

    /**
     * ID id
     */
    private Long id;
    /**
     * 发票合同收款明细id
     */
    private Long llhtmxskbh;
    /**
     * 合同编号
     */
    private String htbh;
    /**
     * oa流程id
     */
    private Long requestid;
    /**
     * 发票代码
     */
    private String fpdm;
    /**
     * 发票号码
     */
    private String fphm;
    /**
     * 发票类型
     */
    private Integer kplx;
    /**
     * 发票状态
     */
    private Integer fpzt;
    /**
     * 发票日期
     */
    private String fprq;
    /**
     * 发票金额（含税）
     */
    private BigDecimal fpje;
    /**
     * 税率
     */
    private Integer kpsl;
    /**
     * 发票附件
     */
    private String fpsmj;
    /**
     * 发票附件图像id
     */
    private String fpsmjImageFileId;
    /**
     * 发票附件件名
     */
    private String fpsmjImageFileName;

    /**
     * 流程相关人id
     */
    private Long nodeoperator;

}
