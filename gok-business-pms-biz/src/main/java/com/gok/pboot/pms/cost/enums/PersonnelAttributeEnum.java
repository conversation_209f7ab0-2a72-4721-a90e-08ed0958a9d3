package com.gok.pboot.pms.cost.enums;

import com.gok.pboot.pms.enumeration.ValueEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 人员属性枚举
 *
 * <AUTHOR>
 * @create 2025/02/18
 **/
@Getter
@AllArgsConstructor
public enum PersonnelAttributeEnum implements ValueEnum<Integer> {

    /**
     * 国科人员
     */
    GOK_STAFF(0, "国科人员"),

    /**
     * 第三方
     */
    THIRD_PARTY(1, "第三方");

    private final Integer value;

    private final String name;

}
