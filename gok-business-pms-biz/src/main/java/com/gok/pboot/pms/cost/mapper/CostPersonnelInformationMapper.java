package com.gok.pboot.pms.cost.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.cost.entity.domain.CostIncomeCalculationDetail;
import com.gok.pboot.pms.cost.entity.domain.CostPersonnelInformation;
import com.gok.pboot.pms.cost.entity.dto.AutoBringConditionDTO;
import com.gok.pboot.pms.cost.entity.dto.PersonInfoConditionDTO;
import com.gok.pboot.pms.cost.entity.vo.CostPersonnelInformationVO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 人员信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
public interface CostPersonnelInformationMapper extends BaseMapper<CostPersonnelInformation> {

    /**
     * 获取第三方最大工号
     *
     * @return {@link String}
     */
    String getMaxWorkCode();

    /**
     * 根据条件获取在场/已离场人员信息 - 分页
     *
     * @param dto dto实体
     * @return {@link List}<{@link CostPersonnelInformationVO}>
     */
    List<CostPersonnelInformationVO> getPersonInfoList(@Param("dto") PersonInfoConditionDTO dto);

    /**
     * 根据条件获取含税报价信息
     *
     * @param dto dto实体
     * @return {@link BigDecimal}
     */
    BigDecimal getQuotationIncludeTax(@Param("dto") AutoBringConditionDTO dto);

    /**
     * 根据条件自动带出人员信息列表
     *
     * @param dto dto实体
     * @return {@link List}<{@link CostPersonnelInformationVO}>
     */
    List<CostPersonnelInformationVO> getPersonAutoBringInfo(@Param("dto") AutoBringConditionDTO dto);

    /**
     * 根据测算明细查询对应人员信息
     *
     * @param query 测算明细查询条件
     * @return 人员信息
     */
    CostPersonnelInformation getByIncomeCalculationDetail(@Param("query") CostIncomeCalculationDetail query);

    /**
     * 批量更新人员信息
     *
     * @param updateCostPersonnelInformationList 人员信息列表
     */
    void updateBatch(@Param("list") List<CostPersonnelInformation> updateCostPersonnelInformationList);

    /**
     * 根据工号列表查询人员信息
     * 工号不为空且去重
     *
     * @param workCodes 工号列表
     * @return 人员信息列表
     */
    List<CostPersonnelInformation> findByWorkCodes(@Param("workCodes") List<String> workCodes);
}