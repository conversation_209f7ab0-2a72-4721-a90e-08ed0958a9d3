package com.gok.pboot.pms.common.base;

/**
 * <AUTHOR>
 * @Description: 常量
 * @date 2019/11/01 17:58
 */
public class BaseConstants {

    private BaseConstants() {
        throw new IllegalStateException("SecurityUtils class");
    }

    public static final String JOB_PARAM_KEY = "parameter";

    public static final String AUTH_KEY = "Authorization";
    /**
     * REDIS键命名开头定时器
     */
    public static final String REDIS_KEY_BEGIN_JOBS = "JOB_";

    /**
     * REDIS键命名开头导入
     */
    public static final String REDIS_KEY_BEGIN_IMPORT_EXCEL = "IMPORT_EXCEL_";

    /**
     * 已锁
     */
    public static final String JOB_LOCK_VALUE_LOCKED = "1";

    /**
     * 未锁
     */
    public static final String JOB_LOCK_VALUE_NOT_LOCK = "0";

    public static final String YES_STR = "1";

    public static final String NO_STR = "0";

    public static final Integer YES = 1;

    public static final Integer NO = 0;

    /**
     * 切割符号
     */
    public static final String SPLIT_STR = ",";

    /**
     * 首页显示服务数；数据字典code字段
     */
    public static final String APP_HOME_SERVICE_NUM = "APP_HOME_SERVICE_NUM";

    /**
     * 临时文件夹
     **/
    public final static String TEMP_FOLDER = "temp";

    /**
     * 文件名称的逗号
     **/
    public final static String FILE_SPLIT = ".";

    /**
     * 正斜线
     */
    public final static String SLASH = "/";

    /**
     * 反斜线
     */
    public final static String BACKSLASH = "\\";

    /**
     * NULL枚举值
     */
    public final static Integer NULL_ENUM_VALUE = 99;

    /**
     * 空枚举值
     */
    public final static Integer BLANK_ENUM_VALUE = 98;

    /**
     * 空枚举值Long
     */
    public final static Long BLANK_ENUM_VALUE_L = 98L;
}
