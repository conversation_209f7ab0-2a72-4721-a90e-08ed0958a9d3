package com.gok.pboot.pms.cost.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.R;
import com.gok.pboot.pms.cost.entity.domain.CostIncomeSettlement;
import com.gok.pboot.pms.cost.entity.domain.CostIncomeSettlementDetail;
import com.gok.pboot.pms.cost.entity.dto.CostIncomeSettlementEditDTO;
import com.gok.pboot.pms.cost.entity.dto.CostIncomeSettlementImportDTO;
import com.gok.pboot.pms.cost.entity.dto.CostIncomeSettlementListDTO;
import com.gok.pboot.pms.cost.entity.dto.CostIncomeSettlementRequestDTO;
import com.gok.pboot.pms.cost.entity.vo.CostIncomeSettlementVO;

import java.util.List;

/**
 * 收入结算服务接口
 * <AUTHOR>
 */
public interface ICostIncomeSettlementService  extends IService<CostIncomeSettlement> {

    /**
     * 查询收入结算汇总列表
     * @param dto
     * @return
     */
    List<CostIncomeSettlementVO> findList(CostIncomeSettlementListDTO dto);

    /**
     * 新增或编辑收入结算汇总
     * @param dtoList
     * @return
     */
    List<CostIncomeSettlement> addOrUpdate(List<CostIncomeSettlementEditDTO> dtoList);

    /**
     * 导入收入结算汇总
     * @param projectId
     * @param importDTOList
     * @return
     */
    ApiResult<String> importExcel(Long projectId, List<CostIncomeSettlementImportDTO> importDTOList);

    /**
     * 删除收入测算
     * @param ids
     * @return
     */
    ApiResult<String> delete(List<Long> ids);

    /**
     * 结算审批
     * @param dto
     * @return
     */
    R<String> createSettlementRequest(CostIncomeSettlementRequestDTO dto);

    /**
     * 判断是否隐藏【结算审批】
     * @param projectId
     * @return
     */
    ApiResult<Boolean> isCreateSettlementRequest(String projectId);

    void updateSettlementNumber(List<CostIncomeSettlement> settlementList, List<CostIncomeSettlementDetail> detailList, Long requestId);
}
