package com.gok.pboot.pms.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import com.gok.pboot.pms.entity.dto.ProjectFollowupPlanDTO;
import lombok.*;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 项目后续计划实体类
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("project_followup_plan")
@NoArgsConstructor
@AllArgsConstructor
public class ProjectFollowupPlan extends BeanEntity<Long> {

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 工作项
     */
    private String workItem;

    /**
     * 负责人id集合 以,分割
     */
    private String manager;

    /**
     * 预计投入周期
     */
    private Integer planCycle;

    /**
     * 预计工时(人天)
     */
    private BigDecimal planWorkHours;

    /**
     * 预计项目管理费用
     */
    private BigDecimal projectManageCostEst;

    /**
     * 客户侧或外部对接人
     */
    private String clientExternalContact;

    /**
     * 交接或说明文档id集合 以,分割
     */
    private String handoverDescDocs;

    public static ProjectFollowupPlan of(ProjectFollowupPlanDTO dto) {
        return ProjectFollowupPlan.builder()
                .projectId(dto.getProjectId())
                .workItem(dto.getWorkItem())
                .manager(dto.getManager())
                .planCycle(dto.getPlanCycle())
                .planWorkHours(dto.getPlanWorkHours())
                .projectManageCostEst(dto.getProjectManageCostEst())
                .clientExternalContact(dto.getClientExternalContact())
                .handoverDescDocs(dto.getHandoverDescDocs())
                .build();
    }

} 