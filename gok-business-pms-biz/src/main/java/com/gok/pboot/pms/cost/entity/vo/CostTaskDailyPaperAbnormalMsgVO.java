package com.gok.pboot.pms.cost.entity.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 异常工时消息推送VO
 */
@Data
public class CostTaskDailyPaperAbnormalMsgVO {
    
    /**
     * 审核人ID
     */
    private Long reviewerId;
    
    /**
     * 审核人名称
     */
    private String reviewerName;
    
    /**
     * 未审核工单数量
     */
    private Integer taskCount;
    
    /**
     * 未审核工时总数
     */
    private BigDecimal totalHours;
} 