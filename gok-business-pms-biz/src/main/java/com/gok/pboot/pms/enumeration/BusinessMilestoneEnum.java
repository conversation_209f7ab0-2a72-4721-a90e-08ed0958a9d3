package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 商机里程碑枚举
 *
 * <AUTHOR>
 * @create 2024/07/10
 **/
@Getter
@AllArgsConstructor
public enum BusinessMilestoneEnum implements ValueEnum<Integer> {

    NOT_START(0, "项目未启动"),
    DEMAND_PLAN(1, "需求方案编制"),
    FEASIBILITY_DESIGN(2, "可研设计"),
    TENDERING(3, "招投标");

    private final Integer value;

    private final String name;

}
