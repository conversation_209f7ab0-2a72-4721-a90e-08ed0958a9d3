package com.gok.pboot.pms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.bcp.upms.feign.RemoteUserService;
import com.gok.bcp.upms.vo.SysUserVo;
import com.gok.module.file.entity.SysFile;
import com.gok.module.file.service.SysFileService;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.entity.domain.ProjectFollowupPlan;
import com.gok.pboot.pms.entity.dto.ProjectFollowupPlanDTO;
import com.gok.pboot.pms.entity.vo.ProjectFollowupPlanVO;
import com.gok.pboot.pms.enumeration.YesOrNoEnum;
import com.gok.pboot.pms.mapper.ProjectFollowupPlanMapper;
import com.gok.pboot.pms.service.IProjectFollowupPlanService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2025/07/03
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class ProjectFollowupPlanServiceImpl extends ServiceImpl<ProjectFollowupPlanMapper, ProjectFollowupPlan> implements IProjectFollowupPlanService {

    private final SysFileService sysFileService;
    private final RemoteUserService remoteUserService;

    @Override
    @Transactional
    public List<Long> batchSave(List<ProjectFollowupPlanDTO> dtoList) {
        if (CollUtil.isEmpty(dtoList)) {
            return ListUtil.empty();
        }

        // 批量删除
        baseMapper.batchDel(dtoList.get(0).getProjectId());

        // 创建旧数据
        List<ProjectFollowupPlan> entityList = dtoList.stream().map(e -> {
            ProjectFollowupPlan entity = ProjectFollowupPlan.of(e);
            BaseBuildEntityUtil.buildInsert(entity);
            return entity;
        }).collect(Collectors.toList());

        this.saveBatch(entityList);

        return entityList.stream().map(ProjectFollowupPlan::getId).collect(Collectors.toList());
    }

    @Override
    public List<ProjectFollowupPlanVO> findByProjectId(Long projectId) {
        if (null == projectId) {
            return ListUtil.empty();
        }

        // 查询项目后续计划
        List<ProjectFollowupPlan> projectFollowupPlans = CollUtil.emptyIfNull(baseMapper.selectList(
                Wrappers.<ProjectFollowupPlan>lambdaQuery()
                        .eq(ProjectFollowupPlan::getProjectId, projectId)
                        .eq(ProjectFollowupPlan::getDelFlag, YesOrNoEnum.NO.getValue())
                        .orderByDesc(ProjectFollowupPlan::getCtime)
        ));

        // 获取后续计划对应的文件信息
        List<String> fileIds = projectFollowupPlans.stream()
                .map(ProjectFollowupPlan::getHandoverDescDocs)
                .filter(e -> StrUtil.isNotBlank(e))
                .flatMap(str -> Arrays.stream(str.split(",")))
                .map(String::trim)
                .distinct()
                .collect(Collectors.toList());
        List<SysFile> fileList = CollUtil.isNotEmpty(fileIds) ? sysFileService.listByIds(fileIds) : ListUtil.empty();
        Map<Long, SysFile> fileMap = CollUtil.isEmpty(fileList)
                ? Collections.emptyMap()
                : fileList.stream()
                .collect(Collectors.toMap(SysFile::getId, Function.identity(), (v1, v2) -> v1));

        // 获取后续计划对应负责人信息
        List<Long> managerIds = projectFollowupPlans.stream()
                .map(ProjectFollowupPlan::getManager)
                .filter(e -> StrUtil.isNotBlank(e))
                .flatMap(str -> Arrays.stream(str.split(",")))
                .map(item -> Long.valueOf(item.trim()))
                .distinct()
                .collect(Collectors.toList());
        List<SysUserVo> sysUserVos = remoteUserService.listByIds(managerIds).getData();
        Map<Long, SysUserVo> managerInfoMap = CollUtil.isEmpty(sysUserVos)
                ? Collections.emptyMap()
                : sysUserVos.stream()
                .collect(Collectors.toMap(SysUserVo::getUserId, Function.identity(), (v1, v2) -> v1));

        List<ProjectFollowupPlanVO> followupPlanVOList = projectFollowupPlans.stream()
                .map(e -> {
                    ProjectFollowupPlanVO vo = BeanUtil.copyProperties(e, ProjectFollowupPlanVO.class);
                    if (StrUtil.isNotBlank(vo.getHandoverDescDocs())) {
                        List<String> fileIdList = StrUtil.split(vo.getHandoverDescDocs(), StrUtil.COMMA);
                        List<SysFile> docFiles = fileIdList.stream()
                                .map(fileId -> fileMap.get(Long.valueOf(fileId)))
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList());
                        vo.setHandoverDescDocsList(docFiles);
                    }

                    if (StrUtil.isNotBlank(vo.getManager())) {
                        List<String> manageIds = StrUtil.split(vo.getManager(), ",");
                        String managerName = manageIds.stream()
                                .map(managerId -> managerInfoMap.get(Long.valueOf(managerId)))
                                .filter(Objects::nonNull)
                                .map(SysUserVo::getName)
                                .collect(Collectors.joining(StrUtil.COMMA));
                        vo.setManagerName(managerName);
                    }
                    return vo;
                })
                .collect(Collectors.toList());


        return followupPlanVOList;
    }

}
