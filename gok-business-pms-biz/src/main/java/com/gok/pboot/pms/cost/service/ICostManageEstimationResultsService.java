package com.gok.pboot.pms.cost.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.cost.entity.domain.CostConfigAccount;
import com.gok.pboot.pms.cost.entity.domain.CostManageEstimationResults;
import com.gok.pboot.pms.cost.entity.domain.CostManageVersion;
import com.gok.pboot.pms.cost.entity.dto.CostManageCalculateDTO;
import com.gok.pboot.pms.cost.entity.dto.CostManageEstimationResultsSaveDTO;
import com.gok.pboot.pms.cost.entity.dto.CostManageSelectDTO;
import com.gok.pboot.pms.cost.entity.dto.CostManageVersionDTO;
import com.gok.pboot.pms.cost.entity.vo.CostManageConfigVersionVO;
import com.gok.pboot.pms.cost.entity.vo.CostManageEstimationResultsVO;
import com.gok.pboot.pms.cost.entity.vo.CostManageListVO;
import com.gok.pboot.pms.cost.entity.vo.CostManageSelectVO;
import com.gok.pboot.pms.enumeration.OAFormTypeEnum;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 成本管理估算结果 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
public interface ICostManageEstimationResultsService extends IService<CostManageEstimationResults> {

    /**
     * 查询项目最新成本估算结果
     *
     * @param request 查询请求
     * @return 成本估算结果集合
     */
    List<CostManageEstimationResultsVO> findLatestEstimationResults(CostManageVersionDTO request);

    /**
     * 保存成本估算结果
     *
     * @param request 请求
     */
    void saveEstimationResults(CostManageEstimationResultsSaveDTO request);

    /**
     * 成本管理列表
     *
     * @param projectId          项目ID
     * @param costBudgetTypeList 成本预算类型枚举值 {@link com.gok.pboot.pms.cost.enums.CostBudgetTypeEnum}
     * @return
     */
    CostManageListVO getCostManageListVO(Long projectId, List<Integer> costBudgetTypeList);

    /**
     * 获取成本管理历史版本明细
     *
     * @param versionId
     * @return
     */
    CostManageListVO getCostManageById(Long versionId);

    /**
     * 获取成本管理使用配置版本
     *
     * @param projectId 项目ID
     * @return
     */
    List<CostManageConfigVersionVO> findConfigVersion(Long projectId);

    /**
     * 单个人工成本项成本管理计算
     *
     * @param request
     * @return
     */
    List<CostManageEstimationResultsVO> calculateEstimationResults(CostManageCalculateDTO request);

    /**
     * 同步OA项目预算台账最新成本管理信息内容
     */
    void getProjectBudgetInfo();

    /**
     * 获取项目成本科目预算
     *
     * @param dto
     * @return
     */
    CostManageSelectVO findCostSelect(CostManageSelectDTO dto);

    /**
     * 查询流程携带成本数据
     *
     * @param request
     * @return
     */
    List<CostManageEstimationResultsVO> findProcessEstimationResults(CostManageVersionDTO request, OAFormTypeEnum businessTypeEnum);

    /**
     * 根据版本ID查询项目结果
     *
     * @param versionIdList
     * @return
     */
    List<CostManageEstimationResultsVO> findByVersionId(List<Long> versionIdList);

    /**
     * 获取最新版本的售前成本预算
     *
     * @param projectId
     * @return
     */

    CostManageVersion getLatestConfirmedPreSalesCost(Long projectId);

    /**
     * 获取组装完成的成本估算结果VO类集合
     * 包含父子关联关系
     *
     * @param versionList              版本对应数据类型集合
     * @param currentVersionAccountMap 最新版本科目Map key-oaId value-最新的版本科目实体类
     * @return 组装完成的成本估算结果VO类集合
     */
    List<CostManageEstimationResultsVO> getAssembleEstimationResultsVOList(
            CostManageVersion costManageVersion,
            List<CostManageEstimationResultsVO> versionList,
            Map<Long, CostConfigAccount> currentVersionAccountMap,
            Long cashPlanVersionId
    );

}
