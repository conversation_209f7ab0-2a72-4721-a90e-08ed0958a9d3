package com.gok.pboot.pms.entity.vo;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.Util.DecimalFormatUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.RoundingMode;
import java.text.DecimalFormat;

/**
 * 财务数据-追加预算总收入
 *
 * <AUTHOR>
 * @date 2023/08/23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class AdditionalIncomeVo {

    /**
     * 销售金额合计（含税）
     */
    private String totalSalesAmount;

    /**
     * 销售金额合计（不含税）
     */
    private String totalSalesAmountBhs;

    /**
     * 财务数据-追加预算总收入 分页数据
     */
    private Page<AdditionalIncomePageVo> additionalIncomePageVoPage;

    /**
     * 设置小数位和舍进规则
     *
     * @param newScale 小数保留位数
     * @param roundingMode 舍进规则
     * @param decimalFormat 小数
     */
    public void setScale(int newScale, RoundingMode roundingMode, DecimalFormat decimalFormat) {
        this.totalSalesAmount = DecimalFormatUtil.setAndValidate(totalSalesAmount, newScale, roundingMode, decimalFormat);
        this.totalSalesAmountBhs = DecimalFormatUtil.setAndValidate(totalSalesAmountBhs, newScale, roundingMode, decimalFormat);
    }

}
