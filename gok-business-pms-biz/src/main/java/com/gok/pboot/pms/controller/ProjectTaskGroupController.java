package com.gok.pboot.pms.controller;

import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PropertyFilters;
import com.gok.pboot.pms.entity.dto.ProjectTaskGroupAddDTO;
import com.gok.pboot.pms.entity.dto.ProjectTaskGroupUpdateDTO;
import com.gok.pboot.pms.entity.dto.ProjectTaskToGroupDTO;
import com.gok.pboot.pms.entity.vo.ProjectTaskGroupVO;
import com.gok.pboot.pms.service.IProjectTaskGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 项目任务看板（包括分组）
 *
 * <AUTHOR>
 * @date 2023/8/23
 */
@RestController
@RequestMapping("/projectTaskGroup")
public class ProjectTaskGroupController {

    @Autowired
    private IProjectTaskGroupService service;

    /**
     * 新增分组
     * @param dto 传入对象
     * @return {@link ApiResult<Long>} 返回分组id
     */
    @PostMapping("/add")
    public ApiResult<Long> add(@Valid @RequestBody ProjectTaskGroupAddDTO dto, BindingResult result){
        if (result.hasErrors()) {
            return ApiResult.failure(result.getAllErrors().get(0).getDefaultMessage());
        }
        return ApiResult.success(service.add(dto));
    }

    /**
     * 更新分组名称
     * @param dto 传入对象
     * @return {@link ApiResult<Long>} 返回分组id
     */
    @PutMapping("/title")
    public ApiResult<Long> updateTitle(@Valid @RequestBody ProjectTaskGroupUpdateDTO dto, BindingResult result){
        if (result.hasErrors()) {
            return ApiResult.failure(result.getAllErrors().get(0).getDefaultMessage());
        }
        return ApiResult.success(service.updateTitle(dto));
    }

    /**
     * 更新在制品数量
     * @param dto 传入对象
     * @return {@link ApiResult<Long>} 返回分组id
     */
    @PutMapping("/capacity")
    public ApiResult<Long> updateCapacity(@Valid @RequestBody ProjectTaskGroupUpdateDTO dto, BindingResult result){
        if (result.hasErrors()) {
            return ApiResult.failure(result.getAllErrors().get(0).getDefaultMessage());
        }
        return ApiResult.success(service.updateCapacity(dto));
    }

    /**
     * 删除分组
     * @param id 分组id
     * @return {@link ApiResult<Boolean>}
     */
    @DeleteMapping("/{id}")
    public ApiResult<Boolean> deleteById(@PathVariable Long id) {
        return ApiResult.success(service.delete(id));
    }

    /**
     * 添加项目到分组下
     * @param dto 传入对象
     * @return {@link ApiResult<Long>} 被操作的任务id
     */
    @PutMapping("/toGroup")
    public ApiResult<Long> putToGroup(@Valid @RequestBody ProjectTaskToGroupDTO dto,BindingResult result){
        if (result.hasErrors()) {
            return ApiResult.failure(result.getAllErrors().get(0).getDefaultMessage());
        }
        return ApiResult.success(service.putToGroup(dto));
    }

    /**
     * 查询分组的项目列表
     * @param request 请求参数
     * @customParam filter_L_projectId 项目的id
     * @customParam filter_S_title 模糊查询的任务标题
     * @customParam filter_S_managerUserName 模糊查询的负责人姓名
     * @customParam filter_I_treeLevel 任务层级
     * @return {@link ApiResult<List<ProjectTaskGroupVO>>}
     */
    @GetMapping("/findList")
    public ApiResult<List<ProjectTaskGroupVO>> findList(HttpServletRequest request){
        Map<String, Object> filter = PropertyFilters.get(request, true);
        return ApiResult.success(service.findList(filter));
    }

}
