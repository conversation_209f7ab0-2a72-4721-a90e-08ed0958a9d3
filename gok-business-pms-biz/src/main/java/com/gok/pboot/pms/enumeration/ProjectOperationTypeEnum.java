package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 项目操作类型枚举
 *
 * <AUTHOR>
 * @create 2025/07/04
 **/
@Getter
@AllArgsConstructor
public enum ProjectOperationTypeEnum implements ValueEnum<Integer> {

    /**
     * 否
     */
    NO(0, "否"),

    /**
     * 确认
     */
    CONFIRM(1, "确认"),

    /**
     * 之后提醒
     */
    DELAY(2, "之后提醒"),

    /**
     * 知悉
     */
    KNOW(3, "知悉");

    private final Integer value;
    private final String name;

}
