package com.gok.pboot.pms.service;

import com.gok.pboot.pms.common.base.R;
import com.gok.pboot.pms.entity.ProjectCollect;

import java.util.List;

/**
 * <p>
 * 任务 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-19
 */
public interface IProjectCollectService {


    /**
     * 添加或编辑
     *
     * @param entity 任务实体
     * @return Result
     */
    R<String> saveOrUpdate(ProjectCollect entity);

    /**
     * 查询详情
     *
     * @param id 唯一标识
     * @return 任务
     */
    ProjectCollect getById(Long id);


    /**
     * 批量插入
     *
     * @param poList 任务实体集合
     * @return Result
     */
    R<String> batchSave(List<ProjectCollect> poList);


    /**
     * 批量修改
     *
     * @param list 任务实体集合
     * @return Result
     */
    R<String> batchUpdate(List<ProjectCollect> list);


    /**
     * 批量修改
     *
     * @param list id集合
     * @return Result
     */
    R<String> batchDel(List<Long> list);
}
