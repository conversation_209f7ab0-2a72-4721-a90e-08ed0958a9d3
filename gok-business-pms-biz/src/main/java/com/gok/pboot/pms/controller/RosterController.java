package com.gok.pboot.pms.controller;

import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.entity.vo.RosterSelectionVO;
import com.gok.pboot.pms.service.RosterService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 花名册控制器
 *
 * <AUTHOR>
 * @version 1.3.4
 */
@RequiredArgsConstructor
@RequestMapping("/roster")
@RestController
public class RosterController {

    private final RosterService service;

    /**
     * 根据姓名查询人员选择框数据
     * @param aliasName 姓名
     * @return 人员选择框数据
     */
    @GetMapping("/findSelections")
    public ApiResult<List<RosterSelectionVO>> findSelections(
            @RequestParam(name = "aliasName", required = false) String aliasName
    ) {
        return ApiResult.success(service.findSelectionList(aliasName));
    }
}
