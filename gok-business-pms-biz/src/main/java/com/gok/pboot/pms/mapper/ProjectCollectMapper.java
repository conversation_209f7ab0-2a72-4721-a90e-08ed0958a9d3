package com.gok.pboot.pms.mapper;

import com.gok.components.data.datascope.BaseMapper;
import com.gok.pboot.pms.entity.ProjectCollect;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 任务 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-19
 */
@Mapper
public interface ProjectCollectMapper extends BaseMapper<ProjectCollect> {


    /**
     * 根据项目id列表查找项目收藏
     *
     * @param projectIds  项目id列表
     * @param collectType 收藏类型
     * @param userId      用户id
     * @return {@link List}<{@link ProjectCollect}>
     */
    List<ProjectCollect> findForDailyPaperEntry(@Param("projectIds") Collection<Long> projectIds, @Param("collectType") Integer collectType, @Param("userId") Long userId);


    /**
     * 批量插入
     *
     * @param poList 实体集合
     */
    void batchSave(@Param("poList") List<ProjectCollect> poList);


    /**
     * 批量修改
     *
     * @param list 实体集合
     */
    void batchUpdate(@Param("list") List<ProjectCollect> list);

    /**
     * 批量逻辑删除
     *
     * @param list id集合
     */
    void batchDel(@Param("list") List<Long> list);


}
