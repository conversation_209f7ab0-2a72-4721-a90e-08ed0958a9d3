package com.gok.pboot.pms.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.pboot.pms.entity.domain.ProjectEstimatedCost;
import com.gok.pboot.pms.mapper.ProjectEstimatedCostMapper;
import com.gok.pboot.pms.service.IProjectEstimatedCostService;
import org.springframework.stereotype.Service;

import java.util.List;


@Service("projectEstimatedCostService")
public class IProjectEstimatedCostServiceImpl extends ServiceImpl<ProjectEstimatedCostMapper, ProjectEstimatedCost> implements IProjectEstimatedCostService {


    @Override
    public List<ProjectEstimatedCost> getByProjectId(Long id) {
        QueryWrapper<ProjectEstimatedCost> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ProjectEstimatedCost::getProjectId, id);
        return baseMapper.selectList(queryWrapper);

    }
}