package com.gok.pboot.pms.entity.vo;

import com.gok.pboot.pms.entity.domain.ProjectTask;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 项目任务看板的任务卡片vo
 *
 * <AUTHOR>
 * @date 2023/8/23
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectTaskCardVO {

    /**
     * 任务id
     */
    private Long taskId;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 任务标题
     */
    private String title;

    /**
     * 负责人姓名
     */
    private String managerUserName;

    /**
     * 分组ID
     */
    private Long groupId;

    public static ProjectTaskCardVO of(ProjectTask projectTask) {
        ProjectTaskCardVO result = new ProjectTaskCardVO();

        result.setTaskId(projectTask.getId());
        result.setTitle(projectTask.getTitle());
        result.setGroupId(projectTask.getGroupId());
        result.setProjectId(projectTask.getProjectId());
        result.setManagerUserName(projectTask.getManagerUserName());
        return result;
    }
}
