package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 成本关联类型枚举
 *
 * <AUTHOR>
 * @date 2025/05/08
 */
@AllArgsConstructor
@Getter
public enum CostSalaryRelateTypeEnum implements ValueEnum<Integer> {

    /**
     * 人工成本估算
     */
    ARTIFICIAL_COST_ESTIMATION(1, "人工成本估算"),

    /**
     * 售前支撑工单实际成本
     */
    PRE_TASK_ACTUAL_COST(2, "售前支撑工单实际成本"),

    /**
     * 售后交付工单估算
     */
    AFTER_TASK_ESTIMATE(3, "售后交付工单估算"),

    /**
     * 售后交付工单实际成本
     */
    AFTER_TASK_ACTUAL_COST(4, "售后交付工单实际成本"),
    ;
    private final Integer value;

    private final String name;
}
