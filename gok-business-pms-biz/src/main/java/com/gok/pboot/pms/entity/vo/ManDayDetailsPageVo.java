package com.gok.pboot.pms.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 项目管理-人天明细
 *
 * <AUTHOR>
 * @date 2023/08/23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class ManDayDetailsPageVo {

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 提交日期
     */
    private LocalDateTime submissionDate;

    /**
     * 工时类型
     */
    private String workType;

    /**
     * 日常工时
     */
    private BigDecimal normalHours;

    /**
     * 加班工时
     */
    private BigDecimal addedHours;

    /**
     * 工作内容
     */
    private String description;

}
