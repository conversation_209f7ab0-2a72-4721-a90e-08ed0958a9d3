package com.gok.pboot.pms.cost.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 成本配置补贴自定义 VO
 *
 * <AUTHOR>
 * @date 2025/01/08
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class CostConfigSubsidyCustomVO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 补贴名称
     */
    private String subsidyName;

    /**
     * 补贴金额
     */
    private BigDecimal subsidyPrice;

    /**
     * 计算方式
     */
    private Integer calculationMethod;

    private String calculateMethodStr;

    /**
     * 版本名称
     */
    private String versionName;
}
