package com.gok.pboot.pms.cost.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 成本管理估算结果
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("cost_manage_estimation_results")
public class CostManageEstimationResults extends BeanEntity<Long> {

    private static final long serialVersionUID = 1L;

    /**
     * 版本ID
     */
    private Long versionId;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 成本项来源 0=新增成本项 1=人工成本测算
     * {@link com.gok.pboot.pms.cost.enums.CostManageSourceEnum}
     */
    private Integer source;

    /**
     * 成本预算类型（0=售前成本，1=A表成本，2=B表成本）
     * {@link com.gok.pboot.pms.cost.enums.CostBudgetTypeEnum}
     */
    private Integer costBudgetType;

    /**
     * 成本科目ID
     */
    private Long accountId;

    /**
     * 成本科目名称
     */
    private String accountName;

    /**
     * 自定义补贴配置ID
     * todo 删除
     */
    private Long subsidyCustomConfigId;

    /**
     * 自定义补贴配置名称
     * todo 删除
     */
    private String subsidyCustomConfigName;

    /**
     * 成本科目类型
     * {@link com.gok.pboot.pms.cost.enums.AccountTypeEnum}
     */
    private Integer accountType;

    /**
     * 预算金额(含税)
     */
    private BigDecimal budgetAmountIncludedTax;

    /**
     * 税率OA字典ID
     */
    private Integer taxRate;

    /**
     * 预算金额(不含税)
     */
    private BigDecimal budgetAmountExcludingTax;

    /**
     * 备注说明
     */
    private String remark;

}
