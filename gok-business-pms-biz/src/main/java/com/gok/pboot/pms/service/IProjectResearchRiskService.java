package com.gok.pboot.pms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.entity.domain.ProjectResearchRisk;

import java.util.List;

/**
 * 研发风险表（Oa项目项目台账-明细5）
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-17 11:11:28
 */
public interface IProjectResearchRiskService extends IService<ProjectResearchRisk> {

    /**
     * 项目id查询
     * @param id
     */
    List<ProjectResearchRisk> getByProjectId(Long id);
}

