package com.gok.pboot.pms.controller;


import com.gok.pboot.common.security.annotation.Inner;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.BaseController;
import com.gok.pboot.pms.service.IHolidayService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;

/**
 *
 * @description 非工作日 前端控制器
 * <AUTHOR>
 * @since 2022-08-23
 */
@Slf4j
@RestController
@RequestMapping("holiday")
@RequiredArgsConstructor
public class HolidayController extends BaseController {

    private final IHolidayService holidayService;

    /**
     * 更新年度非工作日数据（普通休息日+法定节假日）
     *
     * @param year 年份
     * @return {@link ApiResult}
     */
    @Inner(value = false)
    @GetMapping("/update/{year}")
    public ApiResult<String> updateHoliday(@PathVariable("year") Integer year) {
        return holidayService.updateHoliday(year);
    }


    /**
         * 根据日期返回节假日类型
     *
     * @param date 日期
     * @return {@link ApiResult}
     */
    @Inner(value = false)
    @GetMapping("/getHolidayTypeByDate/{date}")
    public ApiResult<Integer> getHolidayTypeByDate(@PathVariable("date") LocalDate date) {
        return holidayService.getHolidayTypeByDate(date);
    }


}
