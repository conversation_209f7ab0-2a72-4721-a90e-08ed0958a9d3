package com.gok.pboot.pms.common.aspect;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.gok.pboot.common.core.exception.BusinessException;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.common.annotation.ProjectStatusCheck;
import com.gok.pboot.pms.entity.domain.ProjectInfo;
import com.gok.pboot.pms.enumeration.ProjectStatusEnum;
import com.gok.pboot.pms.mapper.ProjectInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.jetbrains.annotations.Nullable;
import org.springframework.core.LocalVariableTableParameterNameDiscoverer;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.*;

/**
 * 项目状态检查切面
 * <p>
 * 实现项目状态检查逻辑：
 * 1. 当项目处于关闭、商机终止、异常终止状态时，抛出异常
 * 2. 当项目处于挂起状态时，仅允许项目经理操作
 *
 * <AUTHOR> Assistant
 * @since 2025-07-01
 */
@Aspect
@Component
@Slf4j
public class ProjectStatusCheckAspect {

    @Resource
    private ProjectInfoMapper projectInfoMapper;

    /**
     * 项目状态检查切点
     */
    @Before("@annotation(projectStatusCheck)")
    public void checkProjectStatus(JoinPoint joinPoint, ProjectStatusCheck projectStatusCheck) {
        // 初始化上下文
        EvaluationContext context = contextInit(joinPoint);
        // 获取项目ID集合
        Set<Long> projectIds = getIdSet(context, joinPoint, projectStatusCheck.projectIdField());

        if (CollUtil.isEmpty(projectIds)) {
            log.warn("项目状态检查：项目ID为空，跳过检查");
            return;
        }
        // 查询项目信息
        List<ProjectInfo> projectInfoList = projectInfoMapper.selectBatchIds(projectIds);
        for (ProjectInfo projectInfo : projectInfoList) {
            Long projectId = projectInfo.getId();
            String projectStatus = projectInfo.getProjectStatus();
            ProjectStatusEnum statusEnum = ProjectStatusEnum.getEnumByStrVal(projectStatus);
            if (StrUtil.isBlank(projectStatus) || statusEnum == null) {
                log.warn("项目状态检查：项目状态为空，项目ID：{}", projectId);
                return;
            }

            // 检查禁止状态
            // 项目处于关闭、商机终止、异常终止状态时，冻结项目成本支出，不允许报工和任何费用支出
            Set<ProjectStatusEnum> forbiddenStatuses = CollUtil.newHashSet(ProjectStatusEnum.SJZZ, ProjectStatusEnum.YCZZ, ProjectStatusEnum.JX);
            if (forbiddenStatuses.contains(statusEnum)) {
                String message = String.format("项目【%s】当前状态为【%s】，%s",
                        projectInfo.getItemName(), statusEnum.getName(),
                        projectStatusCheck.disableMessage());
                throw new BusinessException(message);
            }

            // 检查挂起状态的项目经理权限
            if (ProjectStatusEnum.GQ.equals(statusEnum)) {
                // 获取用户ID列表
                Set<Long> userIds = getIdSet(context, joinPoint, projectStatusCheck.userIdField());

                if (CollUtil.isEmpty(userIds)) {
                    return;
                }
                checkSuspendedStatusForUsers(projectStatusCheck, projectInfo, userIds);
            }
        }

    }

    /**
     * 获取ID集合（支持SpEL表达式和传统字段名）
     */
    private Set<Long> getIdSet(EvaluationContext context, JoinPoint joinPoint, String expression) {
        if (StrUtil.isBlank(expression)) {
            return new HashSet<>();
        }
        try {
            // 判断是否为SpEL表达式（以#开头）
            if (expression.startsWith(StringPool.HASH)) {
                return evaluateSpElExpressionForCollection(context, expression, Long.class);
            } else {
                // 传统字段名方式（向后兼容）
                return getLongValues(joinPoint, expression);
            }
        } catch (Exception e) {
            log.error("项目状态检查，获取字段值失败，表达式：{}", expression, e);
            return new HashSet<>();
        }
    }

    /**
     * 检查挂起状态的项目经理权限（支持多个用户ID）
     */
    private void checkSuspendedStatusForUsers(ProjectStatusCheck annotation,
                                              ProjectInfo projectInfo,
                                              Set<Long> userIds) {
        // 项目处于挂起状态，检查是否为项目经理
        Long loginUserId;
        try {
            loginUserId = SecurityUtils.getUser().getId();
        } catch (Exception e) {
            throw new BusinessException("获取当前登录用户ID失败");
        }

        // 获取项目经理id
        Long projectManagerId = projectInfo.getManagerUserId();
        // 异常消息
        String message = String.format("项目【%s】当前处于挂起状态，%s",
                projectInfo.getItemName(),
                annotation.suspendMessage());
        // 检查是否为项目经理
        if (!Objects.equals(loginUserId, projectManagerId)) {
            throw new BusinessException(message);
        }
        // 相关负责人也必须为项目经理
        userIds.remove(projectManagerId);
        if (CollUtil.isNotEmpty(userIds)) {
            throw new BusinessException(message);
        }
    }


    /**
     * 使用SpEL表达式求值（集合值）
     */
    @SuppressWarnings("unchecked")
    private <T> Set<T> evaluateSpElExpressionForCollection(EvaluationContext context, String expression, Class<T> targetType) {

        if (context == null) {
            return null;
        }
        Set<T> result = new HashSet<>();


        Object evalResult = new SpelExpressionParser().parseExpression(expression).getValue(context);

        if (evalResult == null) {
            return result;
        }

        // 处理集合结果
        if (evalResult instanceof Collection) {
            Collection<?> collection = (Collection<?>) evalResult;
            for (Object item : collection) {
                if (item != null) {
                    T convertedItem = convertToTargetType(item, targetType);
                    if (convertedItem != null) {
                        result.add(convertedItem);
                    }
                }
            }
        } else {
            // 单个值
            T convertedItem = convertToTargetType(evalResult, targetType);
            if (convertedItem != null) {
                result.add(convertedItem);
            }
        }


        return result;
    }

    @Nullable
    private static EvaluationContext contextInit(JoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Object[] args = joinPoint.getArgs();

        // 构建参数名和参数值的映射关系
        String[] paramNames = (new LocalVariableTableParameterNameDiscoverer()).getParameterNames(method);
        if (paramNames == null || args == null) {
            return null;
        }

        EvaluationContext context = new StandardEvaluationContext();
        for (int i = 0; i < paramNames.length; i++) {
            context.setVariable(paramNames[i], args[i]);
        }
        return context;
    }

    /**
     * 类型转换辅助方法
     */
    @SuppressWarnings("unchecked")
    private <T> T convertToTargetType(Object value, Class<T> targetType) {
        if (value == null) {
            return null;
        }

        if (targetType.isAssignableFrom(value.getClass())) {
            return (T) value;
        }

        if (targetType == Long.class) {
            if (value instanceof Number) {
                return (T) Long.valueOf(((Number) value).longValue());
            } else if (value instanceof String) {
                try {
                    return (T) Long.valueOf((String) value);
                } catch (NumberFormatException e) {
                    log.warn("无法将字符串转换为Long：{}", value);
                    return null;
                }
            }
        } else if (targetType == String.class) {
            return (T) value.toString();
        }

        return null;
    }

    /**
     * 获取多个Long值（支持集合和嵌套对象）
     */
    private static Set<Long> getLongValues(JoinPoint joinPoint, String fieldName) {
        Set<Long> result = new HashSet<>();

        // 首先尝试获取单个值
        Long singleValue = getFieldValue(joinPoint, fieldName, Long.class);
        if (singleValue != null) {
            result.add(singleValue);
        }

        // 然后尝试获取集合值
        List<Long> collectionValues = getCollectionValues(joinPoint, fieldName, Long.class);
        if (CollUtil.isNotEmpty(collectionValues)) {
            result.addAll(collectionValues);
        }

        // 最后尝试从嵌套对象中提取值
        List<Long> nestedValues = getNestedValues(joinPoint, fieldName, Long.class);
        if (CollUtil.isNotEmpty(nestedValues)) {
            result.addAll(nestedValues);
        }

        return result;
    }


    /**
     * 获取集合类型的字段值
     */
    @SuppressWarnings("unchecked")
    private static <T> List<T> getCollectionValues(JoinPoint joinPoint, String fieldName, Class<T> targetType) {
        List<T> result = new ArrayList<>();
        Object[] args = joinPoint.getArgs();
        if (args == null || args.length == 0) {
            return result;
        }

        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Parameter[] parameters = method.getParameters();

        // 遍历方法参数
        for (int i = 0; i < parameters.length && i < args.length; i++) {
            Object arg = args[i];
            if (arg == null) {
                continue;
            }

            // 如果参数本身是集合类型
            if (Collection.class.isAssignableFrom(arg.getClass())) {
                Collection<?> collection = (Collection<?>) arg;
                for (Object item : collection) {
                    if (item != null) {
                        // 从集合元素中获取字段值
                        T value = getFieldValueFromObject(item, fieldName, targetType);
                        if (value != null) {
                            result.add(value);
                        }
                    }
                }
            }

            // 从对象的集合字段中获取值
            List<T> collectionFromField = getCollectionFieldFromObject(arg, fieldName, targetType);
            if (CollUtil.isNotEmpty(collectionFromField)) {
                result.addAll(collectionFromField);
            }
        }
        return result;
    }

    /**
     * 获取嵌套对象中的字段值
     */
    private static <T> List<T> getNestedValues(JoinPoint joinPoint, String fieldName, Class<T> targetType) {
        List<T> result = new ArrayList<>();
        Object[] args = joinPoint.getArgs();
        if (args == null) {
            return result;
        }

        // 遍历方法参数
        for (Object arg : args) {
            if (arg == null) {
                continue;
            }

            // 递归获取嵌套对象中的值
            List<T> nestedValues = getNestedValuesFromObject(arg, fieldName, targetType, new HashSet<>());
            if (CollUtil.isNotEmpty(nestedValues)) {
                result.addAll(nestedValues);
            }
        }
        return result;
    }

    /**
     * 获取字段值（原有方法，保持向后兼容）
     */
    @SuppressWarnings("unchecked")
    private static <T> T getFieldValue(JoinPoint joinPoint, String fieldName, Class<T> targetType) {
        Object[] args = joinPoint.getArgs();
        if (args == null || args.length == 0) {
            return null;
        }

        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Parameter[] parameters = method.getParameters();

        // 遍历方法参数
        for (int i = 0; i < parameters.length && i < args.length; i++) {
            Object arg = args[i];
            if (arg == null) {
                continue;
            }

            // 如果参数名就是目标字段名，且类型匹配
            if (fieldName.equals(parameters[i].getName()) && targetType.isAssignableFrom(arg.getClass())) {
                return (T) arg;
            }

            // 如果是对象，尝试从对象的字段中获取
            T value = getFieldValueFromObject(arg, fieldName, targetType);
            if (value != null) {
                return value;
            }
        }
        return null;
    }

    /**
     * 从对象中获取字段值（支持点号分隔的嵌套路径）
     */
    @SuppressWarnings("unchecked")
    private static <T> T getFieldValueFromObject(Object obj, String fieldName, Class<T> targetType) {
        if (obj == null || StrUtil.isBlank(fieldName)) {
            return null;
        }

        try {
            // 支持点号分隔的嵌套字段路径，如：user.profile.userId
            if (fieldName.contains(".")) {
                String[] fieldParts = fieldName.split("\\.");
                Object currentObj = obj;

                for (String fieldPart : fieldParts) {
                    if (currentObj == null) {
                        return null;
                    }
                    currentObj = getSimpleFieldValue(currentObj, fieldPart);
                }

                if (currentObj != null && targetType.isAssignableFrom(currentObj.getClass())) {
                    return (T) currentObj;
                }
            } else {
                // 简单字段名
                Object value = getSimpleFieldValue(obj, fieldName);
                if (value != null && targetType.isAssignableFrom(value.getClass())) {
                    return (T) value;
                }
            }
        } catch (Exception e) {
            log.error("获取字段值失败：{}.{}", obj.getClass().getSimpleName(), fieldName, e);
        }

        return null;
    }

    /**
     * 获取简单字段值（不支持嵌套）
     */
    private static Object getSimpleFieldValue(Object obj, String fieldName) {
        if (obj == null || StrUtil.isBlank(fieldName)) {
            return null;
        }

        try {
            Class<?> clazz = obj.getClass();
            Field field = findField(clazz, fieldName);
            if (field != null) {
                field.setAccessible(true);
                return field.get(obj);
            }
        } catch (Exception e) {
            log.error("获取简单字段值失败：{}.{}", obj.getClass().getSimpleName(), fieldName, e);
        }

        return null;
    }

    /**
     * 从对象的集合字段中获取值
     */
    @SuppressWarnings("unchecked")
    private static <T> List<T> getCollectionFieldFromObject(Object obj, String fieldName, Class<T> targetType) {
        List<T> result = new ArrayList<>();
        if (obj == null) {
            return result;
        }

        try {
            Class<?> clazz = obj.getClass();
            Field[] fields = getAllFields(clazz);

            for (Field field : fields) {
                field.setAccessible(true);
                Object fieldValue = field.get(obj);

                if (fieldValue instanceof Collection) {
                    Collection<?> collection = (Collection<?>) fieldValue;
                    for (Object item : collection) {
                        if (item != null) {
                            T value = getFieldValueFromObject(item, fieldName, targetType);
                            if (value != null) {
                                result.add(value);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("从集合字段获取值失败：{}.{}", obj.getClass().getSimpleName(), fieldName, e);
        }

        return result;
    }

    /**
     * 递归获取嵌套对象中的字段值
     */
    @SuppressWarnings("unchecked")
    private static <T> List<T> getNestedValuesFromObject(Object obj, String fieldName, Class<T> targetType, Set<Object> visited) {
        List<T> result = new ArrayList<>();
        if (obj == null || visited.contains(obj)) {
            return result;
        }

        visited.add(obj);

        try {
            Class<?> clazz = obj.getClass();

            // 跳过基本类型和常见的系统类
            if (isSystemClass(clazz)) {
                return result;
            }

            Field[] fields = getAllFields(clazz);

            for (Field field : fields) {
                field.setAccessible(true);
                Object fieldValue = field.get(obj);

                if (fieldValue == null) {
                    continue;
                }

                // 如果字段值是目标类型且字段名匹配
                if (fieldName.equals(field.getName()) && targetType.isAssignableFrom(fieldValue.getClass())) {
                    result.add((T) fieldValue);
                }

                // 如果字段值是集合，递归处理集合元素
                if (fieldValue instanceof Collection) {
                    Collection<?> collection = (Collection<?>) fieldValue;
                    for (Object item : collection) {
                        if (item != null && !visited.contains(item)) {
                            result.addAll(getNestedValuesFromObject(item, fieldName, targetType, visited));
                        }
                    }
                }
                // 如果字段值是对象，递归处理
                else if (!isSystemClass(fieldValue.getClass())) {
                    result.addAll(getNestedValuesFromObject(fieldValue, fieldName, targetType, visited));
                }
            }
        } catch (Exception e) {
            log.error("递归获取嵌套值失败：{}.{}", obj.getClass().getSimpleName(), fieldName, e);
        }

        return result;
    }

    /**
     * 获取类的所有字段（包括父类）
     */
    private static Field[] getAllFields(Class<?> clazz) {
        List<Field> fields = new ArrayList<>();
        while (clazz != null && clazz != Object.class) {
            fields.addAll(Arrays.asList(clazz.getDeclaredFields()));
            clazz = clazz.getSuperclass();
        }
        return fields.toArray(new Field[0]);
    }

    /**
     * 判断是否为系统类（避免无限递归）
     */
    private static boolean isSystemClass(Class<?> clazz) {
        String packageName = clazz.getPackage() != null ? clazz.getPackage().getName() : "";
        return clazz.isPrimitive() ||
                clazz.isEnum() ||
                packageName.startsWith("java.") ||
                packageName.startsWith("javax.") ||
                packageName.startsWith("sun.") ||
                packageName.startsWith("com.sun.") ||
                Number.class.isAssignableFrom(clazz) ||
                CharSequence.class.isAssignableFrom(clazz) ||
                Boolean.class.equals(clazz) ||
                Character.class.equals(clazz);
    }

    /**
     * 查找字段（包括父类）
     */
    private static Field findField(Class<?> clazz, String fieldName) {
        while (clazz != null && clazz != Object.class) {
            try {
                return clazz.getDeclaredField(fieldName);
            } catch (NoSuchFieldException e) {
                clazz = clazz.getSuperclass();
            }
        }
        return null;
    }
}
