package com.gok.pboot.pms.cost.enums;


import com.gok.components.common.str.StrUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum RequestTypeEnum {

    AB(0, "A表"),
    BB(1, "B表"),
    HTHQ(2, "合同会签"),
    XMBG(3, "项目变更");

    private final Integer value;

    private final String name;

    public static Integer getValueByStr(String str) {
        if (StrUtils.isNotEmpty(str)) {
            for (RequestTypeEnum type : RequestTypeEnum.values()) {
                if (str.contains(type.getName())) {
                    return type.getValue();
                }
            }
        }
        return null;
    }

}