package com.gok.pboot.pms.cost.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import lombok.*;

/**
 * 现金流计划版本记录表
 *
 * <AUTHOR> generated
 * @date 2024-03-19
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("cost_cash_plan_version")
public class CostCashPlanVersion extends BeanEntity<Long> {

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 版本号
     */
    private String versionNo;

    /**
     * 版本状态（0=当前版本，1=历史版本）
     */
    private Integer versionStatus;

    /**
     * 关联最早成本估算版本ID
     */
    private Long costVersionId;

    /**
     * 关联最早成本估算版本
     */
    private String costVersion;

    /**
     * 创建人当前角色
     */
    private String creatorRole;

    /**
     * 备注
     */
    private String remark;

    /**
     * 关联流程 ID
     */
    private Long requestId;

    /**
     *  流程名称
     */
    private String requestName;

}