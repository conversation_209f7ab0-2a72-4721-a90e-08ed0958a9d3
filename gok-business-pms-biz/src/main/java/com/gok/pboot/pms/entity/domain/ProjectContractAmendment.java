package com.gok.pboot.pms.entity.domain;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;




/**
 * 合同变更流程
 *
 * <AUTHOR>
 * @date 2024/2/22
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("contract_amendment")
public class ProjectContractAmendment  extends Model<ProjectContractAmendment> {

    private static final long serialVersionUID = 1L;


    /**
     * id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 流程id
     */
    private Long requestId;
    /**
     * '项目id'
     */
    private Long projectId;

    /**
     * ''申请人id''
     */
    private String applicatId;


    /**
     * ''申请人''
     */
    private String applicat;

    /**
     * '流程类型'
     */
    private String processType;

    /**
     * '流程状态'
     */
    private String status;

    /**
     * ''流程名'
     */
    private String name;

    /**
     * '创建时间'
     */
    private String ctime;

    /**
     * 归档时间
     */
    private String filingDateTime;

    /**
     * 关联合同id
     */
    private Long llhtmc;

    /**
     * 变更类型
     */
    private Integer bglx;

    /**
     * 新实际合同签订时间
     */
    private String xsjhtqdsj;


    /**
     * 变更协议原件数量
     */
    private Integer bgxyyjsl;


    /**
     * 合同附件id
     */
    private String htfj;

    /**
     * 合同附件图像id
     */
    private String htfjImageFileId;
    /**
     * 合同附件文件名
     */
    private String htfjImageFileName;

    /**
     * 新合同附件id
     */
    private String xhtfj;

    /**
     * 新合同附件图像id
     */
    private String xhtfjImageFileId;
    /**
     * 新合同附件文件名
     */
    private String xhtfjImageFileName;


    /**
     * 新合同附件id（已盖章）
     */
    private String htfjygz;

    /**
     * 新合同附件图像id（已盖章）
     */
    private String htfjygzImageFileId;
    /**
     * 新合同附件文件名（已盖章）
     */
    private String htfjygzImageFileName;


    /**
     * 变更/补充/解除/终止协议附件（已盖章）
     */
    private String bgbcjczzxyfjygz;

    /**
     * 变更/补充/解除/终止协议附件（已盖章）
     */
    private String bgbcjczzxyfjygzImageFileId;
    /**
     * 变更/补充/解除/终止协议附件（已盖章）
     */
    private String bgbcjczzxyfjygzImageFileName;

    /**
     * 合同变更/补充流程编号
     */
    private String flowno;

    /**
     * 新原件数量
     */
    private Integer xyjsl;

    /**
     * 新实际合同编号
     */
    private String sjhtbh;

    /**
     * 变更协议签订日期
     */
    private String bgxyqdrq;

    /**
     * 变更原因
     */
    private String bgyy;

    /**
     * 变更内容详情及说明
     */
    private String bgnrxqjsm;

    /**
     * 备注
     */
    private String remark;

    /**
     * 流程相关人id
     */
    private String nodeoperator;


}
