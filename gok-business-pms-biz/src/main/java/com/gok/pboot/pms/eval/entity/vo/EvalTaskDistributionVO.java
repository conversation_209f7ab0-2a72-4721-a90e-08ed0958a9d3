package com.gok.pboot.pms.eval.entity.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 项目人员工单评价分布VO
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class EvalTaskDistributionVO {

    /**
     * 排名
     */
    @ExcelProperty("排名")
    private Integer rank;

    /**
     * 员工ID
     */
    @ExcelIgnore
    private Long managerId;

    /**
     * 员工编号
     */
    @ExcelProperty("员工编号")
    private String managerNo;

    /**
     * 员工姓名
     */
    @ExcelProperty("员工姓名")
    private String managerName;

    /**
     * 单项目个人评分
     */
    @ExcelProperty("单项目个人评分")
    private BigDecimal personalScore;

    /**
     * 单项目个人等级
     */
    @ExcelIgnore
    private Integer personalLevelInt;

    /**
     * 单项目个人等级
     */
    @ExcelProperty("单项目个人等级")
    private String personalLevel;

    /**
     * 单项目个人调整后评分
     */
    @ExcelProperty("单项目个人调整后评分")
    private BigDecimal adjustedScore;

    /**
     * 已评价工单数
     */
    @ExcelProperty("已评价工单数")
    private Integer evaluatedTaskCount;
}