package com.gok.pboot.pms.cost.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.cost.entity.domain.CostManageTarget;
import com.gok.pboot.pms.cost.entity.dto.CostManageTargetDTO;
import com.gok.pboot.pms.cost.entity.vo.CostManageTargetVO;
import com.gok.pboot.pms.cost.entity.vo.CostManageTargetVersionIInfoVO;

/**
 * <p>
 * 成本目标管理 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
public interface ICostManageTargetService extends IService<CostManageTarget> {

    /**
     * 添加项目目标信息
     *
     * @param costManageTargetDTO DTO信息
     */
    void insertCostManageTargetInfo(CostManageTargetDTO costManageTargetDTO);

    /**
     * 查询项目目标信息
     *
     * @param versionId 版本ID
     * @param projectId 项目ID
     * @return CostManageTargetVO
     */
    CostManageTargetVO getCostManageTargetInfo(Long versionId, Long projectId);

    /**
     * 获取历史版本数据
     *
     * @param pageRequest 分页参数
     * @param projectId   项目ID
     * @return {@link ApiResult }<{@link Page}<{@link CostManageTargetVersionIInfoVO }>>
     */
    Page<CostManageTargetVersionIInfoVO> getCostManageTargetVersionInfo(PageRequest pageRequest, Long projectId);

    /**
     * 同步OA项目台账最新目标内容
     */
    void getProjectTargetInfo();
}
