package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;

/**
 * 任务里程碑
 *
 * <AUTHOR>
 * @date 2023/08/20
 */
@AllArgsConstructor
public enum TaskMilestone implements ValueEnum<Boolean> {
    YES(true, "是"),
    NO(false, "否")
    ;

    private final Boolean value;
    private final String name;

    @Override
    public Boolean getValue() {
        return value;
    }

    @Override
    public String getName() {
        return name;
    }
}
