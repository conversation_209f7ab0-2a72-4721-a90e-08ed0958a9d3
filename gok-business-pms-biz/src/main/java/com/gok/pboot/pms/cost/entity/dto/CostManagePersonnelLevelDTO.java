package com.gok.pboot.pms.cost.entity.dto;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 成本管理人员级别测算明细DTO
 *
 * <AUTHOR>
 * @create 2025/01/09
 **/
@Data
public class CostManagePersonnelLevelDTO {

    /**
     * 成本科目ID
     */
    @NotNull(message = "成本科目ID不能为空")
    private Long accountId;

    /**
     * 成本科目ID
     */
    @NotNull(message = "成本科目OA ID不能为空")
    private Long accountOaId;

    /**
     * 成本科目名称
     */
    @NotBlank(message = "成本科目名称不能为空")
    private String accountName;

    /**
     * 人员级别配置ID
     */
    @NotNull(message = "人员级别配置ID不能为空")
    private Long levelConfigId;

    /**
     * 人员类型名称
     */
    private String personnelType;

    /**
     * 岗位类别id
     */
    private Long jobActivityId;

    /**
     * 地域
     */
    private String region;

    /**
     * 职级id
     */
    private Long personnelLevel;

    /**
     * 人天单价
     */
    private BigDecimal personnelPrice;

    /**
     * 人员数量
     */
    private Integer personnelNum;

    /**
     * 预计人天
     */
    private Integer expectedPersonDay;

    /**
     * 是否出差（0=否，1=是）
     */
    @NotNull(message = "是否出差不能为空")
    @Min(value = 0, message = "是否出差非法")
    @Max(value = 1, message = "是否出差非法")
    private Integer businessTripFlag;

    /**
     * 出差天数
     */
    private Integer businessTripDay;

    /**
     * 出差城市ID
     */
    private String businessTripCityId;

    /**
     * 是否单人出差（0=否，1=是）
     */
    @Min(value = 0, message = "是否单人出差非法")
    @Max(value = 1, message = "是否单人出差非法")
    private Integer singlePersonFlag;

    /**
     * 是否自行解决住宿（0=否，1=是）
     */
    @Min(value = 0, message = "是否自行解决住宿非法")
    @Max(value = 1, message = "是否自行解决住宿非法")
    private Integer selfStayFlag;

    /**
     * 差旅住宿标准ID
     */
    private Long travelStayConfigId;

    /**
     * 差旅住宿标准类型（0=总经办，1=总监级以上，2=总监级以下）
     */
    @Min(value = 0, message = "差旅住宿标准类型非法")
    @Max(value = 2, message = "差旅住宿标准类型非法")
    private Integer travelStayType;

    /**
     * 差旅补贴配置ID
     */
    private Long travelSubsidyConfigId;

    /**
     * 预估人员级别费用
     */
    @NotNull(message = "预估人员级别费用不能为空")
    private BigDecimal levelPriceCost;

    /**
     * 预估住宿费用
     */
    private BigDecimal estimatedStayCost;

    /**
     * 差旅补贴费用
     */
    private BigDecimal travelSubsidyCost;

    /**
     * 备注
     */
    @Length(max = 200, message = "备注不能超过200个字")
    private String remark;

    /**
     * 自定义补贴费用集合
     */
    private List<CostManagePersonnelCustomDetailDto> customSubsidyCostList;

}
