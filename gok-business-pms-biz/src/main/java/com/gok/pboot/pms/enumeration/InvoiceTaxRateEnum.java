package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 开票税率枚举
 *
 * <AUTHOR>
 * @date 13/12/2023
 */
@Getter
@AllArgsConstructor
public enum InvoiceTaxRateEnum implements ValueEnum<Integer> {


    THREE(0, "3"),
    SIX(1, "6"),
    NINE(2, "9"),
    THIRTEEN(3, "13"),
    ONE(4, "1"),
    ZERO(5, "0");

    private final Integer value;

    private final String name;
}
