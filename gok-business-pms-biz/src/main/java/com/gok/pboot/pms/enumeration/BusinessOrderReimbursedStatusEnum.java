package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 商旅订单报销状态枚举
 *
 * <AUTHOR>
 * @date 2025/07/31
 */
@AllArgsConstructor
@Getter
public enum BusinessOrderReimbursedStatusEnum implements ValueEnum<Integer> {
    /**
     * 待发起
     */
    WAIT_INITIATION(0, "待发起"),
    /**
     * 已发起
     */
    INITIATED(1, "已发起"),
    /**
     * 发起错误-OA接口报错
     */
    INITIATION_ERROR_OA(2, "发起错误-OA接口报错"),
    /**
     * 发起错误-预算不足
     */
    INITIATION_ERROR_BUDGET(3, "发起错误-预算不足");

    private final Integer value;

    private final String name;
}
