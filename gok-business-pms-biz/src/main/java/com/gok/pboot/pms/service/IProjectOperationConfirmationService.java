package com.gok.pboot.pms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.entity.domain.ProjectOperationConfirmation;
import com.gok.pboot.pms.entity.dto.ProjectOperationConfirmationDTO;
import com.gok.pboot.pms.enumeration.ProjectOperationRoleEnum;

import java.util.List;

/**
 * 项目操作状态确认记录表服务
 *
 * <AUTHOR> Generated
 * @since 2025-07-02
 */
public interface IProjectOperationConfirmationService extends IService<ProjectOperationConfirmation> {

    /**
     * 保存或更新单条数据
     *
     * @param dto
     * @return
     */
    List<Long> saveOrUpdate(ProjectOperationConfirmationDTO dto);

    /**
     * 判断项目是否确认完成
     *
     * @param projectId 项目ID
     * @param module    模块
     * @return true-项目对应模块全部确认 false-项目对应模块未全部确认
     */
    boolean judgeConfirmedComplete(Long projectId, Integer module);

    /**
     * 获取当前用户项目角色集合
     *
     * @param projectId
     * @param currentUserId
     * @return
     */
    List<Integer> getOperationRole(Long projectId, Long currentUserId);

    /**
     * 批量保存知悉操作消息
     *
     * @param projectId    项目ID
     * @param module       模块
     * @param roleEnumList 角色枚举列表[]
     * @return
     */
    List<Long> batchSaveKnowOperation(Long projectId, Integer module, List<ProjectOperationRoleEnum> roleEnumList);

}
