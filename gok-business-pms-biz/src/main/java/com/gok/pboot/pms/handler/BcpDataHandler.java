package com.gok.pboot.pms.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.gok.bcp.upms.dto.MultiDimensionDeptDto;
import com.gok.bcp.upms.feign.RemoteOutMultiDeptService;
import com.gok.bcp.upms.feign.RemoteOutService;
import com.gok.bcp.upms.vo.SysDeptOutVO;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.Util.SysDeptUtils;
import com.gok.pboot.pms.entity.SysDept;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Queues;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * BCP 数据处理程序
 *
 * <AUTHOR>
 * @date 2025/01/09
 */
@Component
public class BcpDataHandler {


    @Resource
    private RemoteOutService remoteOutService;

    @Resource
    private RemoteOutMultiDeptService remoteOutMultiDeptService;

    /**
     * 获取所有系统部门Map
     *
     * @return {@link Map }<{@link Long }, {@link SysDept }>
     */
    public Map<Long, SysDept> getAllSysDeptMap() {
        List<SysDeptOutVO> deptList = ObjectUtils.defaultIfNull(remoteOutService.getOrgStructList().getData(), ImmutableList.of());
        return deptList.stream()
                .filter(Objects::nonNull)
                .map(SysDeptUtils::mapToSysDept)
                .collect(Collectors.toMap(SysDept::getDeptId, a -> a));
    }

    public List<MultiDimensionDeptDto> getMultiDeptList(String deptCatName) {
        if (StrUtil.isBlank(deptCatName)) {
            return Collections.emptyList();
        }
        return BaseBuildEntityUtil.apiResult(remoteOutMultiDeptService.getDeptList(deptCatName, null, null));
    }


    public List<SysDeptOutVO> getAllSysDeptList() {
        return ObjectUtils.defaultIfNull(remoteOutService.getOrgStructList().getData(), ImmutableList.of());
    }


    public static Map<String, Long> getDeptIdMapByName(Collection<SysDeptOutVO> allDept, String symbol) {
        Map<Long, SysDeptOutVO> deptMap = allDept.stream()
                .filter(h -> h.getName() != null && h.getLevel() != null)
                .collect(Collectors.toMap(SysDeptOutVO::getDeptId, h -> h, (h1, h2) -> h1));
        Map<String, Long> resMap = new HashMap<>(allDept.size());
        allDept.forEach(dept -> {
            Long deptId = dept.getDeptId();
            String fullName = StringPool.EMPTY;
            fullName = recursionDeptName(fullName, deptMap, dept, symbol);
            fullName = fullName.substring(0, fullName.length() - 1);
            resMap.put(fullName, deptId);
        });
        return resMap;
    }

    public String collectFullName(Map<Long, SysDept> deptIdMap,
                                  Long deptId,
                                  String separator) {
        if (deptIdMap.isEmpty() || deptId == null || CollUtil.isEmpty(deptIdMap)) {
            return StringUtils.EMPTY;
        }

        Long deptIdNow = deptId;
        SysDept dept = deptIdMap.get(deptIdNow);
        Deque<String> nameStack = Queues.newArrayDeque();
        StringJoiner joiner = new StringJoiner(separator);

        while (dept != null && StringUtils.isNotBlank(dept.getName())) {
            nameStack.push(dept.getName());
            deptIdNow = dept.getParentId();
            dept = deptIdMap.get(deptIdNow);
            if (isTopDeptParentId(deptIdNow)) {
                break;
            }
        }
        nameStack.forEach(joiner::add);

        return joiner.toString();
    }
    public static boolean isTopDeptParentId(Long id) {
        return id != null && (id == 0 || id == -1);
    }


    private static String recursionDeptName(String fullName, Map<Long, SysDeptOutVO> deptMap, SysDeptOutVO dept, String str) {
        fullName = dept.getName() + str + fullName;
        if (dept.getParentId() == -1L) {
            return fullName;
        } else {
            SysDeptOutVO sysDept = deptMap.get(dept.getParentId());
            if (Optional.ofNullable(sysDept).isPresent()) {
                fullName = recursionDeptName(fullName, deptMap, sysDept, str);
            } else {
                return fullName;
            }
        }
        return fullName;
    }
}
