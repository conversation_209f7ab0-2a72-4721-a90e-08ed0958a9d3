package com.gok.pboot.pms.entity.dto;


import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.enumeration.ProjectCollectTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Collection;

/**
 * 不通角色查询项目权限判断参数
 *
 * <AUTHOR>
 * @since 2023-5-16
 */
@Data
public class RoleProjectPageDto extends PageRequest {

    /**
    * 项目名称
    */
    private String projectName;
    /**
    * 项目编号
    */
    private String code;
    /**
    * 项目经理人员ID
    */
    private Long managerUserId;
    /**
     * 审核人员人员ID,该参数必须默认设置当前登录用户
     */
    private Long privilegeUserId;
    /**
    * 业务归属（一级）部门ID
    */
    private Long deptId;
    /**
     * 收藏类型（0=任务管理收藏，1=工时填报收藏）
     * {@link ProjectCollectTypeEnum}
     */
    @ApiModelProperty(value = "收藏类型")
    private Integer collectType;
    /**
     * userId
     */
    private Long userId;
    /**
     * 项目ids
     */
    private Collection<Long> projectIds;

    /**
     * 项目状态
     */
    private Collection<Long> projectStatus;
}
