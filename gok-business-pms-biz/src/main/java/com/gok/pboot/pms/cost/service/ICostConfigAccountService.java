package com.gok.pboot.pms.cost.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.cost.entity.domain.CostConfigAccount;
import com.gok.pboot.pms.cost.entity.dto.CostConfigAccountDTO;
import com.gok.pboot.pms.cost.entity.vo.CostConfigAccountVO;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 成本科目配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
public interface ICostConfigAccountService extends IService<CostConfigAccount> {

    /**
     * 从OA同步最新成本科目配置数据
     */
    void insertCostSubjectConfigInfo();

    /**
     * 获取成本主体配置信息
     *
     * @return {@link List }<{@link CostConfigAccountVO }>
     */
    List<CostConfigAccountVO> getCostSubjectConfigInfo();

    /**
     * 更新成本主题配置信息
     *
     * @param dtoList DTO 列表
     */
    void updateCostSubjectConfigInfo(List<CostConfigAccountDTO> dtoList);

    /**
     * 按版本 ID 获取成本主体配置
     *
     * @param versionId 版本 ID
     * @return {@link List }<{@link CostConfigAccountVO }>
     */
    List<CostConfigAccountVO> getCostSubjectConfigByVersionId(Long versionId);

    /**
     * 按 ID 获取地图
     *
     * @param accountIdSet 已设置帐户 ID
     * @return {@link Map }<{@link Long }, {@link CostConfigAccount }>
     */
    Map<Long, CostConfigAccount> getMapByIds(Collection<Long> accountIdSet);
}
