package com.gok.pboot.pms.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @desc 下级日报工时统计 基础VO
 * @createTime 2023/5/11 11:09
 */
@Data
public class SubordinatePaperEntryStaticStrVO {
    /**
     * 正常工时（人天）
     */
    @ApiModelProperty(value = "正常工时（人天）")
    private String normalHours;
    /**
     * 加班工时（人天）
     */
    @ApiModelProperty(value = "加班工时（人天）")
    private String addedHours;

    /**
     * '工作日加班工时'
     */
    @ApiModelProperty(value = "工作日加班工时（人天）")
    private String workOvertimeHours;

    /**
     * '休息日加班工时'
     */
    @ApiModelProperty(value = "休息日加班工时（人天）")
    private String restOvertimeHours;
    /**
     * '节假日加班工时'
     */
    @ApiModelProperty(value = "节假日加班工时（人天）")
    private String holidayOvertimeHours;

    /**
     * 调休工时（人天）
     */
    @ApiModelProperty(value = "调休工时（人天）")
    private String ompensatoryHours;

    /**
     * 项目分摊工时（人天）
     */
    @ApiModelProperty("项目分摊工时（人天）")
    private String projectShareHours;
}
