package com.gok.pboot.pms.cost.entity.vo;

import lombok.Data;

/**
 * 工单工时月度统计VO
 *
 * <AUTHOR>
 * @date 2024/04/16
 */
@Data
public class CostTaskDailyPapersStatisticMonthlyVO {

    private Integer total;

    private Integer waitingReview;

    private Integer abnormal;

    public static CostTaskDailyPapersStatisticMonthlyVO of(Integer total, Integer waitingReview, Integer abnormal) {
        CostTaskDailyPapersStatisticMonthlyVO vo = new CostTaskDailyPapersStatisticMonthlyVO();
        vo.setTotal(total);
        vo.setWaitingReview(waitingReview);
        vo.setAbnormal(abnormal);
        return vo;
    }
} 