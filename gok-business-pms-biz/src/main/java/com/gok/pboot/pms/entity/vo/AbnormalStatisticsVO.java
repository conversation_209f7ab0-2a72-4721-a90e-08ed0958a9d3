package com.gok.pboot.pms.entity.vo;

import lombok.*;

/**
 * 异常日报统计信息
 *
 * <AUTHOR>
 * @version 1.3.4
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class AbnormalStatisticsVO {

    /**
     * 总人数
     */
    private Integer totalPeople;

    /**
     * 异常人数
     */
    private Integer abnormalPeople;

    /**
     * 总异常日报天数
     */
    private Integer totalAbnormalDays;

    public final static AbnormalStatisticsVO EMPTY = of(0, 0, 0);

    public static AbnormalStatisticsVO of(Integer totalPeople, Integer abnormalPeople, Integer totalAbnormalDays) {
        AbnormalStatisticsVO result = new AbnormalStatisticsVO();

        result.setTotalPeople(totalPeople);
        result.setAbnormalPeople(abnormalPeople);
        result.setTotalAbnormalDays(totalAbnormalDays);

        return result;
    }
}
