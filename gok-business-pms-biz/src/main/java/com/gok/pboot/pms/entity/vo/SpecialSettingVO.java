package com.gok.pboot.pms.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gok.pboot.pms.entity.SpecialSetting;
import lombok.*;

import java.sql.Timestamp;

/**
 * - 特殊配置 -
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@ToString
public class SpecialSettingVO {

    private String id;

    private String userId;

    private String topDeptId;

    private String userRealName;

    private String topDeptName;
    private String creator;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp ctime;

    public static SpecialSettingVO from(SpecialSetting specialSetting, String userRealName, String topDeptName){
        return new SpecialSettingVO(
                String.valueOf(specialSetting.getId()),
                String.valueOf(specialSetting.getUserId()),
                String.valueOf(specialSetting.getTopDeptId()),
                userRealName,
                topDeptName,
                specialSetting.getCreator(),
                specialSetting.getCtime()
        );
    }
}
