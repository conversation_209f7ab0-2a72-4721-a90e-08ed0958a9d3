package com.gok.pboot.pms.entity.dto;

import lombok.Data;

/**
 * 项目任务-人员关系Dto
 *
 * <AUTHOR>
 * @date 2024/01/22
 */
@Data
public class ProjectTaskeUserDto {

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 人员ID
     */
    private Long userId;

    /**
     * 人员姓名
     */
    private String userName;

    /**
     * 任务角色（0=负责人，1=参与人）
     * @link com.gok.pboot.pms.enumeration.ProjectTaskRoleEnum
     */
    private Integer taskRole;

}
