package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;

/**
 * 项目阶段枚举
 *
 * <AUTHOR>
 * @date 2023-07-21
 */
@AllArgsConstructor
public enum ProjectStageEnum implements ValueEnum<String> {

    SJWJ("0", "商机挖掘"),
    XMLX("1", "项目立项"),
    JSRK("2", "技术认可"),
    DCTM("3", "达成同盟"),
    YDZB("4", "引导招标"),
    XMZB("5", "项目中标"),
    HTHK("6", "合同回款"),
    DD("7", "丢单"),
    XMWQD("8", "项目未启动"),
    XQDY("9", "需求调研"),
    FABX("10", "方案编写"),
    FAGH("11", "方案过会"),
    KYBG("12", "可研报告"),
    ZSLX("13", "正式立项"),
    SHSJ("14", "深化设计"),
    CS("15", "财审"),
    ZBZB("16", "招标准备"),
    GB("17", "挂标"),
    TBZB("18", "投标准备"),
    TB("19", "投标"),
    HTQY("20", "合同签约"),
    HTYS("21", "合同验收"),
    XMHK("22", "项目回款"),
    QT("23", "其他");

    private final String value;

    private final String name;

    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getName() {
        return name;
    }

}
