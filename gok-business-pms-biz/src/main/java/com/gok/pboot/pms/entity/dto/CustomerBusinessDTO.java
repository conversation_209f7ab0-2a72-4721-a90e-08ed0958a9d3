package com.gok.pboot.pms.entity.dto;


import com.gok.pboot.pms.entity.CustomerBusinessPerson;
import com.gok.pboot.pms.entity.CustomerBusinessUnit;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
* <p>
* 客户经营单元(基础概况)DTO
* </p>
*
* <AUTHOR>
* @since 2024-10-12
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class CustomerBusinessDTO {

    // 页号
    private int pageNumber = 0;
    // 每页大小
    private int pageSize = 10;
    /**
     * id
     */
    private Long id;

    /**
     * 经营单元名称
     */
    private String name;

    /**
     * 人员姓名
     */
    private String mangerName;

    /**
     * 主营业务
     */
    private String overview;

    /**
     * 组织架构附件
     */
    private String structureUrl;

    /**
     * 分析报告附件
     */
    private String reportUrl;

    /**
     * 经营计划附件
     */
    private String plan;

    /**
     * 客户列表
     */
    private List<CustomerBusinessUnit> unitList;

    /**
     * 相关负责人列表
     */
    private List<CustomerBusinessPerson> personList;

    /**
     * 数据权限过滤id
     */
    private List<Long> businessIdsInDataScope;

    /**
     * 权限范围
     */
    private String scope;
}
