package com.gok.pboot.pms.cost.entity.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 异常工单返回VO
 */
@Data
public class CostTaskAbnormalVO {
    
    /**
     * 工单ID
     */
    @ExcelIgnore
    private Long id;
    
    /**
     * 工单名称
     */
    @ExcelProperty("工单名称")
    private String taskName;
    
    /**
     * 项目ID
     */
    @ExcelIgnore
    private Long projectId;
    
    /**
     * 项目名称
     */
    @ExcelProperty("项目名称")
    private String projectName;
    
    /**
     * 工单类别
     */
    @ExcelIgnore
    private Integer taskCategory;

    /**
     * 工单类别文本
     */
    @ExcelProperty("工单类别")
    private String taskCategoryTxt;
    
    /**
     * 描述
     */
    @ExcelProperty("描述")
    private String taskDesc;
    
    /**
     * 拆解类型
     */
    @ExcelIgnore
    private Integer disassemblyType;
    
    /**
     * 拆解类型名称
     */
    @ExcelProperty("拆解类型")
    private String disassemblyTypeTxt;

    /**
     * 工单负责人ID
     */
    @ExcelIgnore
    private Long managerId;

    /**
     * 工单负责人姓名
     */
    @ExcelProperty("工单负责人")
    private String managerName;
    
    /**
     * 起止日期-开始日期
     */
    @ExcelIgnore
    private LocalDate startDate;
    
    /**
     * 起止日期-结束日期
     */
    @ExcelIgnore
    private LocalDate endDate;

    /**
     * 起止日期-结束日期
     */
    @ExcelProperty("起止日期")
    private String startEndDate;

    /**
     * 审核人ID
     */
    @ExcelIgnore
    private Long reviewerId;
    
    /**
     * 审核人姓名
     */
    @ExcelProperty("审核人姓名")
    private String reviewerName;
    
    /**
     * 工单状态
     */
    @ExcelIgnore
    private Integer taskStatus;
    
    /**
     * 工单状态名称
     */
    @ExcelProperty("工单状态")
    private String taskStatusTxt;
    
    /**
     * 提交完成时间
     */
    @ExcelProperty("提交完成时间")
    private LocalDateTime submitCompletionTime;

    /**
     * 审核时间
     */
    @ExcelProperty("审核时间")
    private LocalDateTime auditTime;
    /**
     * 项目经理ID
     */
    @ExcelIgnore
    private Long projectManagerId;
    
    /**
     * 项目经理姓名
     */
    @ExcelProperty("项目经理")
    private String projectManagerName;
    
    /**
     * 异常类型
     */
    @ExcelIgnore
    private Integer abnormalType;
    
    /**
     * 异常类型名称
     */
    @ExcelProperty("异常类型")
    private String abnormalTypeTxt;
} 