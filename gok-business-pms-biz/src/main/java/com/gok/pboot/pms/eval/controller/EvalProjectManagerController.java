package com.gok.pboot.pms.eval.controller;

import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.eval.entity.dto.EvalProjectManagerDTO;
import com.gok.pboot.pms.eval.service.IEvalProjectManagerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * 项目经理评价前端控制器
 *
 * <AUTHOR>
 * @menu 项目经理评价
 * @date 2025/05/15
 */
@Slf4j
@Api(tags = "项目经理评价")
@RestController
@RequestMapping("/evalProjectManager")
@RequiredArgsConstructor
public class EvalProjectManagerController {

    private final IEvalProjectManagerService evalProjectManagerService;

    /**
     * 项目经理评分
     *
     * @param requestList 评分数据集合
     * @return 操作结果
     */
    @ApiOperation("项目经理评分")
    @PostMapping("/score")
    public ApiResult<Void> score(@Valid @RequestBody List<EvalProjectManagerDTO> requestList) {
        evalProjectManagerService.score(requestList);
        return ApiResult.success(null);
    }


}