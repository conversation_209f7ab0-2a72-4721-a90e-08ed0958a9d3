package com.gok.pboot.pms.cost.enums;

import com.gok.pboot.pms.enumeration.ValueEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * cost 类型 enum
 *
 * <AUTHOR> 2025/01/09
 */
@Getter
@AllArgsConstructor
public enum CostTypeEnum implements ValueEnum<Integer> {
    /**
     * 人工成本
     */
    RGCB(0, "人工成本"),

    /**
     * 费用报销
     */
    FYBX(1, "费用报销"),

    /**
     * 外采费用
     */
    WCFY(2, "外采费用");

    private final Integer value;

    private final String name;

    public static CostTypeEnum fromValue(Integer value) {
        for (CostTypeEnum type : CostTypeEnum.values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        return null;
    }

}