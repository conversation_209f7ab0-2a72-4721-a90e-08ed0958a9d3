package com.gok.pboot.pms.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 客户经营单元-客户组成(关联客户)
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-12
 */
@Data
@Accessors(chain = true)
@TableName("customer_business_unit")
@ApiModel(value="CustomerBusinessUnit对象", description="客户经营单元-客户组成(关联客户)")
public class CustomerBusinessUnit extends BeanEntity<Long> {

    private static final long serialVersionUID = 1L;

    /**
     * 经营单元id
     */
    private Long businessId;


    /**
     * 所属客户经理id
     */
    private Long unitManagerId;

    /**
     * 所属客户经理姓名
     */
    private String unitManager;

    /**
     * 所属客户名称
     */
    private String unitName;
}
