package com.gok.pboot.pms.cost.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <p>
 * 人员信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CostPersonnelInformationDTO {

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 人员信息列表
     */
    private List<PersonnelInformationDTO> personnelInformationList;

}