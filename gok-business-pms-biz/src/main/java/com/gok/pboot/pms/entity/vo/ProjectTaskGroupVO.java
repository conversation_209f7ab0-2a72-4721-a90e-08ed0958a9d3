package com.gok.pboot.pms.entity.vo;

import com.gok.pboot.pms.entity.domain.ProjectTaskGroup;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 项目任务看板 vo
 *
 * <AUTHOR>
 * @date 2023/8/23
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectTaskGroupVO {

    /**
     * 分组id
     */
    private Long groupId;

    /**
     * 分组名称
     */
    private String title;

    /**
     * 在制品数量（最大任务数限制）
     */
    private Integer capacity;

    /**
     * 任务卡片（按分组）
     */
    List<ProjectTaskCardVO> projectTaskCards;

    public static ProjectTaskGroupVO of(ProjectTaskGroup group) {
        ProjectTaskGroupVO result = new ProjectTaskGroupVO();

        result.setGroupId(group.getId());
        result.setTitle(group.getTitle());
        result.setCapacity(group.getCapacity());
        //result.setProjectTaskCards(new ArrayList<>());

        return result;
    }
}
