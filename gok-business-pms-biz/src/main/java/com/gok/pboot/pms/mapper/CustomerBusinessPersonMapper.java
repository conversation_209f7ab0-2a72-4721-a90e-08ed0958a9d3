package com.gok.pboot.pms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.entity.CustomerBusinessPerson;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 客户经营单元-经营组织架构（相关负责人） Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-12
 */
@Mapper
public interface CustomerBusinessPersonMapper extends BaseMapper<CustomerBusinessPerson> {

    /**
     * 批量逻辑删除
     *
     * @param list id集合
     */
    void batchDel(@Param("list") List<Long> list);

    /**
     * 过滤businessId
     *
     * @param filter 参数
     * @return 项目ID列表
     */
    List<Long> findId(@Param("filter") Map<String, Object> filter);

    void deletedByBusinessId(@Param("businessId") Long businessId);
}
