package com.gok.pboot.pms.entity.vo;

import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.Util.SysDeptUtils;
import com.gok.pboot.pms.entity.SysDept;
import com.gok.pboot.pms.entity.domain.ProjectInfo;
import com.gok.pboot.pms.enumeration.CustomerGradeEnum;
import com.gok.pboot.pms.enumeration.DeliverTypeEnum;
import com.gok.pboot.pms.enumeration.ProjectStatusEnum;
import com.gok.pboot.pms.enumeration.SecondaryBusinessTypeEnum;
import com.google.common.base.Strings;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description 外部项目概览
 * @since 2024/8/2
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class ProjectInfoOuterVO extends ProjectInfoOverViewVO {

    /**
     * 最终客户
     */
    private String endCustomer;

    /**
     * 最终客户分级
     */
    private String endCustomerGrade;

    /**
     * 签约客户
     */
    private String contractCustomer;

    /**
     * 业务类型
     */
    private Integer secondaryBusinessType;

    /**
     * 业务类型描述
     */
    private String secondaryBusinessTypeName;

    /**
     * 交付形式
     *
     * @see com.gok.pboot.pms.enumeration.DeliverTypeEnum
     */
    private Integer deliverType;

    /**
     * 交付形式描述
     */
    private String deliverTypeName;

    /**
     * 项目交付部门id
     */
    private String proDeliveryDepartmentId;

    /**
     * 项目交付部门
     */
    private String proDeliveryDepartment;

    /**
     * 项目销售人员ID
     */
    private String salesmanUserId;

    /**
     * 项目销售人员（客户经理）
     */
    private String projectSalesperson;

    /**
     * 售前人员姓名(售前经理)
     */
    private String preSaleUserName;


    public static ProjectInfoOuterVO from(ProjectInfo po, Map<Long, SysDept> deptIdMap) {

        ProjectInfoOuterVO result = new ProjectInfoOuterVO();
        Long salesmanUserId = po.getSalesmanUserId();
        Long managerUserId = po.getManagerUserId();
        //是否内部项目
        Long firstLevelDepartmentId = po.getFirstLevelDepartmentId();
        Long secondLevelDepartmentId = po.getSecondLevelDepartmentId();

        result.setId(String.valueOf(po.getId()));
        result.setItemNo(Strings.nullToEmpty(po.getItemNo()));
        result.setItemName(Strings.nullToEmpty(po.getItemName()));
        result.setBusinessId(po.getBusinessId());
        result.setBusinessUnitName(Strings.nullToEmpty(po.getBusinessUnitName()));
        result.setUnitId(po.getUnitId());
        result.setUnitName(Strings.nullToEmpty(po.getUnitName()));
        result.setSalesmanUserId(salesmanUserId == null ? StringUtils.EMPTY : String.valueOf(salesmanUserId));
        result.setProjectSalesperson(Strings.nullToEmpty(po.getProjectSalesperson()));
        result.setManagerUserId(managerUserId == null ? StringUtils.EMPTY : String.valueOf(managerUserId));
        result.setManagerUserName(Strings.nullToEmpty(po.getManagerUserName()));

        //部门处理
        result.setBusinessDepartmentId(Optional.ofNullable(secondLevelDepartmentId).orElse(firstLevelDepartmentId));
        result.setBusinessDepartment(po.getBusinessDepartmentId() == null
                ? po.getBusinessDepartment()
                : SysDeptUtils.collectFullName(deptIdMap, po.getBusinessDepartmentId()));
        result.setProDeliveryDepartment(po.getProDeliveryDepartmentId() == null
                ? StringUtils.EMPTY
                : SysDeptUtils.collectFullName(deptIdMap, po.getProDeliveryDepartmentId()));

        // 新业务类型
        result.setSecondaryBusinessType(po.getSecondaryBusinessType());
        result.setSecondaryBusinessTypeName(EnumUtils.getNameByValue(SecondaryBusinessTypeEnum.class, po.getSecondaryBusinessType()));

        //项目状态
        String projectStatus = Strings.nullToEmpty(po.getProjectStatus());
        result.setProjectStatus(projectStatus);
        result.setProjectStatusName(StringUtils.EMPTY.equals(projectStatus) ? StringUtils.EMPTY :
                ProjectStatusEnum.getNameByStrVal(projectStatus));

        result.setPreSaleUserName(Strings.nullToEmpty(po.getPreSaleUserName()));
        result.setManagerUserName(Strings.nullToEmpty(po.getManagerUserName()));

        //客户信息
        result.setEndCustomer(po.getEndCustomer());
        result.setContractCustomer(po.getContractCustomer());
        result.setEndCustomerGrade(EnumUtils.getNameByValue(CustomerGradeEnum.class, po.getEndCustomerGrade()));
        //交付形式
        result.setDeliverType(po.getDeliverType());
        result.setDeliverTypeName(EnumUtils.getNameByValue(DeliverTypeEnum.class, po.getDeliverType()));

        return result;
    }
}
