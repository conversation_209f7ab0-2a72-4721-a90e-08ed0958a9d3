package com.gok.pboot.pms.eval.enums;

import com.gok.pboot.pms.enumeration.ValueEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 满意度调查状态枚举
 *
 * <AUTHOR>
 * @date 2025/05/08
 */
@Getter
@AllArgsConstructor
public enum EvalSatisfactionSurveyStatusEnum implements ValueEnum<Integer> {

    /**
     * 未评价
     */
    UNEVALUATED(0, "未评价"),
    /**
     * 已评价
     */
    EVALUATED(1, "已评价"),
    /**
     * 超期未评价
     */
    OVERDUE(2, "超期未评价");

    private final Integer value;
    private final String name;
} 