package com.gok.pboot.pms.cost.entity.vo;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 售前工单工时审核统计结果
 *
 * <AUTHOR>
 * @date 2024/04/16
 */
@Data
@Builder
public class CostSupportTaskApprovalDetailVO {

    /**
     * ID
     */
    private Long id;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 正常工时
     */
    private BigDecimal normalHours;

    /**
     * 工作日加班工时
     */
    private BigDecimal workOvertimeHours;

    /**
     * 休息日加班工时
     */
    private BigDecimal restOvertimeHours;

    /**
     * 节假日加班工时
     */
    private BigDecimal holidayOvertimeHours;

    /**
     * 待审核预估成本
     */
    private String pendingEstimatedCost;
}