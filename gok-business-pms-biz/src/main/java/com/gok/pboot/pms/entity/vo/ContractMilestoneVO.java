package com.gok.pboot.pms.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gok.pboot.pms.Util.DateUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 合同里程碑分页查询VO
 *
 * <AUTHOR>
 * @date 2024/03/20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class ContractMilestoneVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 关联合同ID
     */
    private Long contractId;

    /**
     * 里程碑
     */
    private String milestone;

    /**
     * 款项名称ID
     */
    private String paymentNameId;

    /**
     * 款项名称
     */
    private String paymentName;

    /**
     * 款项比例(%)
     */
    private BigDecimal milestoneRatio;

    /**
     * 预计完成日期（原预计日期）
     */
    @JsonFormat(pattern = DateUtil.SIMPLE_DATE_FORMAT, timezone = DateUtil.GMT_8)
    private LocalDate expectedCompleteDate;

    /**
     * 实际完成日期
     */
    @JsonFormat(pattern = DateUtil.SIMPLE_DATE_FORMAT, timezone = DateUtil.GMT_8)
    private LocalDate actualCompleteDate;

    /**
     * 预估款项金额_含税
     */
    private String estimatedAmountIncludeTaxTxt;
    private BigDecimal estimatedAmountIncludeTax;

    /**
     * 预估款项金额_不含税
     */
    private String estimatedAmountTxt;
    private BigDecimal estimatedAmount;

    /**
     * 预估税率
     */
    private Integer estimatedTaxRate;
    private String estimatedTaxRateTxt;

    /**
     * 计划款项金额_含税（原款项金额）
     */
    private String plannedAmountIncludeTaxTxt;
    private BigDecimal plannedAmountIncludeTax;

    /**
     * 计划款项金额_不含税（原款项金额(不含税)）
     */
    private String plannedAmountTxt;
    private BigDecimal plannedAmount;

    /**
     * 计划税率（原税率）
     */
    private Integer plannedTaxRate;
    private String plannedTaxRateTxt;

    /**
     * 款项状态
     */
    private String milestoneStatus;

    /**
     * 实际回款金额（原实际款项金额）
     */
    private String actualPaymentAmountTxt;
    private BigDecimal actualPaymentAmount;

    /**
     * 实际回款日期（原实际款项日期）
     */
    @JsonFormat(pattern = DateUtil.SIMPLE_DATE_FORMAT, timezone = DateUtil.GMT_8)
    private LocalDate actualPaymentDate;

    /**
     * 回款账期
     */
    private String paymentPeriodDate;

    /**
     * 款项差额
     */
    private String amountDifferenceTxt;
    private BigDecimal amountDifference;

    /**
     * 款项条件
     */
    private String milestoneCondition;

    /**
     * 结算单附件(盖章)（原结算单（扫描件））
     */
    private String settlementAttachment;

    /**
     * 结算单附件集合
     */
    private List<OaFileInfoVo> settlementAttachmentList;

    /**
     * 里程碑达成佐证（原结算单（电子版））
     */
    private String milestoneEvidence;

    /**
     * 里程碑达成佐证附件集合
     */
    private List<OaFileInfoVo> milestoneEvidenceList;

    /**
     * 坏账流程编号
     */
    private String badDebtProcessNumber;

    /**
     * 坏账金额
     */
    private String badDebtAmountTxt;
    private BigDecimal badDebtAmount;

    /**
     * 坏账归档时间
     */
    private LocalDate badDebtFilingTime;

}
