package com.gok.pboot.pms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.entity.domain.ProjectTaskAttachment;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface ProjectTaskAttachmentMapper extends BaseMapper<ProjectTaskAttachment> {

    void save(ProjectTaskAttachment attachment);

    void delete(Long taskId);

    List<ProjectTaskAttachment> findByTaskId(Long taskId);
}
