package com.gok.pboot.pms.entity.dto;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;


/**
* <p>
* 客户经营单元(搜索)DTO
* </p>
*
* <AUTHOR>
* @since 2024-10-12
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class CustomerBusinessSearchDTO {


    /**
     * 条数
     */
    private int limit = 50;
    /**
     * 关键字
     */
    private String key;


    /**
     * 经营单元id（搜索所属客户时用，不按照经营单元id过滤可不传）
     */
    private Long businessId;


}
