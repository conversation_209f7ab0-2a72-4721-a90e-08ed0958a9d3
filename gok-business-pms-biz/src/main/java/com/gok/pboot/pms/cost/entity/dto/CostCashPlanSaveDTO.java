package com.gok.pboot.pms.cost.entity.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class CostCashPlanSaveDTO {

    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空")
    private Long projectId;

    /**
     * 当前版本ID
     */
    private Long versionId;

    /**
     * 现金流计划列表
     */
    @NotEmpty(message = "现金流计划列表不能为空")
    private List<CostCashPlanDetailDTO> planList;

}