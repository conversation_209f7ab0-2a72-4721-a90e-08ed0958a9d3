package com.gok.pboot.pms.cost.entity.vo;

import com.gok.pboot.pms.cost.entity.dto.CostManagePersonnelCustomDetailDto;
import lombok.*;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 成本管理人员级别测算明细
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class CostManagePersonnelLevelDetailVO {

    /**
     * 明细ID
     */
    private Long id;

    /**
     * 版本ID
     */
    private Long versionId;

    /**
     * 成本管理估算结果ID
     */
    private Long estimationResultsId;

    /**
     * 成本科目ID
     */
    private Long accountId;

    /**
     * 成本科目名称
     */
    private String accountName;

    /**
     * 人员级别单价配置ID
     */
    private Long levelConfigId;

    /**
     * 职务io
     */
    private Long jobActivityId;
    /**
     * 人员类型名称
     */
    private String personnelType;

    /**
     * 地域
     */
    private String region;

    /***
     * 职级id
     * */
    private Long personnelLevel;

    private String personnelLevelName;

    /**
     * 人天单价
     */
    private BigDecimal personnelPrice;

    /**
     * 人员数量
     */
    private Integer personnelNum;

    /**
     * 预计人天
     */
    private Integer expectedPersonDay;

    /**
     * 是否出差（0=否，1=是）
     */
    private Integer businessTripFlag;

    /**
     * 出差天数
     */
    private Integer businessTripDay;

    /**
     * 出差城市ID
     */
    private String businessTripCityId;

    /**
     * 是否单人出差（0=否，1=是）
     */
    private Integer singlePersonFlag;

    /**
     * 是否自行解决住宿（0=否，1=是）
     */
    private Integer selfStayFlag;

    /**
     * 差旅住宿标准ID
     */
    private Long travelStayConfigId;

    /**
     * 差旅住宿标准类型（0=总经办，1=总监级以上，2=总监级以下）
     */
    private Integer travelStayType;

    /**
     * 差旅补贴配置ID
     */
    private Long travelSubsidyConfigId;

    /**
     * 预估人员级别费用
     */
    private BigDecimal levelPriceCost;

    /**
     * 预估住宿费用
     */
    private BigDecimal estimatedStayCost;

    /**
     * 差旅补贴费用
     */
    private BigDecimal travelSubsidyCost;

    /**
     * 自定义补贴
     */
    private String customSubsidyCost;
    private List<CostManagePersonnelCustomDetailDto> customSubsidyCostList;

    /**
     * 备注说明
     */
    private String remark;


}
