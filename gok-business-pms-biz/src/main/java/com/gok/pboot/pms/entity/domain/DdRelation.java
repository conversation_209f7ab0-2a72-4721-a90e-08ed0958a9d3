package com.gok.pboot.pms.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 滴滴关联表（支持项目和人员关联）
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dd_relation")
public class DdRelation {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 关联ID（项目ID或人员ID）
     */
    private Long relateId;

    /**
     * 关联类型 1-项目，2-人员
     */
    private Integer relateType;

    /**
     * 滴滴返回的ID
     */
    private String didiId;

    /**
     * 同步时间
     */
    private LocalDateTime syncTime;


}
