package com.gok.pboot.pms.entity.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import com.gok.pboot.pms.enumeration.ProjectOperationModuleEnum;
import com.gok.pboot.pms.enumeration.ProjectOperationRoleEnum;
import com.gok.pboot.pms.enumeration.ProjectOperationTypeEnum;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 项目操作状态确认记录实体类
 *
 * <AUTHOR>
 * @create 2025/07/02
 **/
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("project_operation_confirmation")
@NoArgsConstructor
@AllArgsConstructor
public class ProjectOperationConfirmation extends BeanEntity<Long> {

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 操作用户ID
     */
    private Long userId;

    /**
     * 操作用户名
     */
    private String userName;

    /**
     * 角色 0-项目经理 1-客户经理 2-项目经理上级 3-客户经理上级 4-PMO
     * {@link ProjectOperationRoleEnum#getValue()}
     */
    private Integer role;

    /**
     * 操作类型  0-否 1-确认 2-之后提醒
     *
     * @see ProjectOperationTypeEnum#getValue()
     */
    private Integer operationType;

    /**
     * 操作模块 0-质保 1-关闭
     *
     * @see ProjectOperationModuleEnum#getValue()
     */
    private Integer module;

    /**
     * 确认时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime confirmTime;

    /**
     * 提醒日期
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDate reminderDate;


    public ProjectOperationConfirmation init( ProjectOperationModuleEnum moduleEnum) {
        return this.setOperationType(ProjectOperationTypeEnum.NO.getValue())
                .setModule(moduleEnum.getValue())
                .setReminderDate(LocalDate.now());
    }
}
