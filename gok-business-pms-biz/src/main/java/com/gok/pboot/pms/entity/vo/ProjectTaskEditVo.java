package com.gok.pboot.pms.entity.vo;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.entity.domain.ProjectTask;
import com.gok.pboot.pms.entity.domain.ProjectTaskAttachment;
import com.gok.pboot.pms.entity.domain.ProjectTaskUser;
import com.gok.pboot.pms.enumeration.TaskMilestone;
import com.google.common.base.Strings;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;

import javax.annotation.Nullable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 编辑时查询返回的对象
 *
 * <AUTHOR>
 * @date 2023/8/23
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectTaskEditVo {

    /**
     * 是否有上级任务
     */
    private Boolean hasParent;

    /**
     * 父级ID
     */
    private Long parentId;

    /**
     * 父级任务标题
     */
    private String parentTitle;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 任务标题
     */
    private String title;

    /**
     * 负责人ID
     */
    private Long managerUserId;

    /**
     * 负责人姓名
     */
    private String managerUserName;

    /**
     * 参与人id集合
     */
    private List<Long> userIds;

    /**
     * 参与人姓名集合
     */
    private List<String> userNames;

    /**
     * 计划开始时间
     */
    private String expectedStartTime;

    /**
     * 计划结束时间
     */
    private String expectedEndTime;

    /**
     * 实际开始时间
     */
    private String actualStartTime;

    /**
     * 实际结束时间
     */
    private String actualEndTime;

    /**
     * 是否里程碑
     */
    private Integer milestone;

    /**
     * 里程碑文本描述
     */
    private String milestoneText;

    /**
     * 详细描述
     */
    private String content;

    /**
     * 附件列表
     */
    private List<ProjectTaskAttachmentVO> attachments;

    public static ProjectTaskEditVo of(
            ProjectTask task,
            @Nullable String parentTaskName,
            List<ProjectTaskUser> members,
            List<ProjectTaskAttachment> attachments
    ) {
        ProjectTaskEditVo result = new ProjectTaskEditVo();
        List<Long> memberUserIds = new ArrayList<>();
        List<String> memberUserNames = new ArrayList<>();

        result.setTaskId(task.getId());
        result.setTitle(task.getTitle());
        result.setManagerUserId(task.getManagerUserId());
        result.setManagerUserName(task.getManagerUserName());
        result.setMilestone(BooleanUtils.toBoolean(task.getMilestone()) ? 1 : 0);
        result.setMilestoneText(EnumUtils.getNameByValue(TaskMilestone.class, task.getMilestone()));
        result.setContent(Strings.nullToEmpty(task.getContent()));
        result.setExpectedStartTime(formatDateTime(task.getExpectedStartTime()));
        result.setExpectedEndTime(formatDateTime(task.getExpectedEndTime()));
        result.setActualStartTime(formatDateTime(task.getActualStartTime()));
        result.setActualEndTime(formatDateTime(task.getActualEndTime()));

        result.setHasParent(parentTaskName != null);
        if (result.getHasParent()) {
            result.setParentTitle(parentTaskName);
            result.setParentId(task.getParentId());
        }

        for (ProjectTaskUser member : members) {
            memberUserIds.add(member.getUserId());
            memberUserNames.add(member.getUserName());
        }
        result.setUserIds(memberUserIds);
        result.setUserNames(memberUserNames);
        result.setAttachments(ProjectTaskAttachmentVO.batchFrom(attachments));
        return result;
    }

    private static String formatDateTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return "";
        }
        return LocalDateTimeUtil.formatNormal(dateTime);
        //if (dateTime.getHour() == 0 && dateTime.getMinute() == 0 && dateTime.getSecond() == 0){
        //    return LocalDateTimeUtil.formatNormal(dateTime.toLocalDate());
        //}else{
        //    return LocalDateTimeUtil.formatNormal(dateTime);
        //}
    }
}
