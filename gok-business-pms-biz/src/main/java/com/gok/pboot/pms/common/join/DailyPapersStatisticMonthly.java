package com.gok.pboot.pms.common.join;

import lombok.*;

import java.math.BigDecimal;

/**
 * - 按月份的日报填报数据 -
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/9/5 17:28
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@ToString
public class DailyPapersStatisticMonthly {
    /**
     * 日报总数
     */
    private Integer total;

    /**
     * 正常工时
     */
    private BigDecimal normalHours;

    /**
     * 加班工时
     */
    private BigDecimal addedHours;
}
