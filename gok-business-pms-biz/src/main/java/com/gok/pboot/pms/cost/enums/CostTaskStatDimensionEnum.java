package com.gok.pboot.pms.cost.enums;

import com.gok.pboot.pms.cost.entity.vo.*;
import com.gok.pboot.pms.enumeration.ProjectTaskKindEnum;
import com.gok.pboot.pms.enumeration.ValueEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 工单统计维度枚举
 *
 * <AUTHOR>
 * @date 2025/05/13
 */
@AllArgsConstructor
@Getter
public enum CostTaskStatDimensionEnum implements ValueEnum<Integer> {


    /**
     * 项目维度
     */
    PROJECT(1, "项目维度",
            ProjectProDimDeliverStatVO.class,
            ProjectProDimPreSaleStatVO.class,
            PersonnelProDimDeliverStatVO.class,
            PersonnelProDimPreSaleStatVO.class,
            Collections.emptyList()),

    /**
     * 人员维度
     */
    PERSONNEL(2, "人员维度",
            ProjectPerDimDeliverStatVO.class,
            ProjectPerDimPreSaleStatVO.class,
            PersonnelPerDimDeliverStatVO.class,
            PersonnelPerDimPreSaleStatVO.class,
            Arrays.asList(0, 1)),

    /**
     * 工单维度
     */
    TASK(3, "工单维度",
            ProjectTaskDimDeliverStatVO.class,
            ProjectTaskDimPreSaleStatVO.class,
            PersonnelTaskDimDeliverStatVO.class,
            PersonnelTaskDimPreSaleStatVO.class,
            Arrays.asList(0, 1, 2, 3));

    private final Integer value;

    private final String name;

    /**
     * 项目看板交付工单vo类
     */
    private final Class<?> proKanbanDeliverClazz;

    /**
     * 项目看板售前工单vo类
     */
    private final Class<?> proKanbanPreSaleClazz;

    /**
     * 人员看板交付工单vo类
     */
    private final Class<?> perKanbanDeliverClazz;

    /**
     * 人员看板售前工单vo类
     */
    private final Class<?> perKanbanPreSaleClazz;

    /**
     * 项目/人员 看板合并列索引
     */
    private final List<Integer> mergeColumnIndex;


    /**
     * 获取项目看板 vo clazz
     *
     * @param taskTypeEnum 任务类型枚举
     * @return {@link Class }<{@link ? }>
     */
    public Class<?> getProKanbanVoClazz(ProjectTaskKindEnum taskTypeEnum) {
        if (null == taskTypeEnum) {
            return null;
        }
        switch (taskTypeEnum) {
            case AFTER_SALES_DELIVERY:
                return this.proKanbanDeliverClazz;
            case PRE_SALES_SUPPORT:
                return this.proKanbanPreSaleClazz;
            default:
                return null;
        }
    }

    /**
     * 获取人员看板 vo clazz
     *
     * @param taskTypeEnum 任务类型枚举
     * @return {@link Class }<{@link ? }>
     */
    public Class<?> getPerKanbanVoClazz(ProjectTaskKindEnum taskTypeEnum) {
        if (null == taskTypeEnum) {
            return null;
        }
        switch (taskTypeEnum) {
            case AFTER_SALES_DELIVERY:
                return this.perKanbanDeliverClazz;
            case PRE_SALES_SUPPORT:
                return this.perKanbanPreSaleClazz;
            default:
                return null;
        }
    }
}
