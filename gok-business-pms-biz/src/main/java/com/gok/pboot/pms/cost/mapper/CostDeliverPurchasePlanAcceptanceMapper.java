package com.gok.pboot.pms.cost.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.cost.entity.domain.CostDeliverPurchasePlanAcceptance;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface CostDeliverPurchasePlanAcceptanceMapper extends BaseMapper<CostDeliverPurchasePlanAcceptance>{

    Boolean batchSave(@Param("list") List<CostDeliverPurchasePlanAcceptance> list);

    Boolean delByIds(@Param("idList") List<Long> acceptanceIds);
}