package com.gok.pboot.pms.entity.bo;

import lombok.*;

/**
 * 花名册选项
 *
 * <AUTHOR>
 * @version 1.3.4
 */
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class RosterSelectionBO {

    /**
     * 人员ID
     */
    private Long id;

    /**
     * 人员姓名
     */
    private String aliasName;

    /**
     * 人员类型
     * @see com.gok.pboot.pms.enumeration.EmployeeStatusEnum
     */
    private Integer employeeStatus;

    /**
     * 部门ID
     */
    private Long deptId;
}
