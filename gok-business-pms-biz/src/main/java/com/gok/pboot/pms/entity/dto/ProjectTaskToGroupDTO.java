package com.gok.pboot.pms.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 任务添加到分组dto
 *
 * <AUTHOR>
 * @date 2023/8/23
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProjectTaskToGroupDTO {

    /**
     * 任务ID
     */
    @NotNull(message = "任务id不能为空")
    private Long taskId;

    /**
     * 分组ID
     */
    @NotNull(message = "分组id不能为空")
    private Long groupId;

}
