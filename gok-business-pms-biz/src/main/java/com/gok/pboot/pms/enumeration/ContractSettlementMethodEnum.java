package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 合同结算方式枚举
 *
 * <AUTHOR>
 * @date 13/12/2023
 */
@Getter
@AllArgsConstructor
public enum ContractSettlementMethodEnum implements ValueEnum<Integer> {

    /**
     * 人力结算（订单结算）
     */
    HUMAN_SETTLEMENT(0, "人力结算（订单结算）"),

    /**
     * 项目结算（固定总价）
     */
    PROJECT_SETTLEMENT(1, "项目结算（固定总价）"),

    /**
     * 项目结算（固定总价）
     */
    NO_SETTLEMENT(2, "无结算");

    private final Integer value;

    private final String name;

}
