package com.gok.pboot.pms.entity.vo;


import lombok.AllArgsConstructor;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 加班、请假、销假数据同步
 *
 * <AUTHOR>
 * @since 2022-10-12
 */
@Data
@AllArgsConstructor
public class CompensatoryLeaveDataDetailVO {

    private Long id;
    /**
    * OA编号
    */
    private Long oaId;
    /**
    * 分钟数
    */
    private Integer minuteData;
    /**
    * 小时数
    */
    private BigDecimal hourData;
    /**
    * 流程归属日期
    */
    private LocalDate belongDate;
    /**
    * 项目编号
    */
    private Long xmmc;
    /**
    * 数据类型(1:加班、2:请假、3:销假)
    */
    private String type;


}
