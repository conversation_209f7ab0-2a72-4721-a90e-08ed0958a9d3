package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 工时类型枚举
 *
 * <AUTHOR>
 * @date 2024/02/20
 */
@Getter
@AllArgsConstructor
public enum WorkHourEnum implements ValueEnum<Integer> {

    /**
     * 售前支撑
     */
    SQZC(0, "售前支撑"),
    /**
     * 售后交付
     */
    SHJF(1, "售后交付");

    private final Integer value;

    private final String name;


}
