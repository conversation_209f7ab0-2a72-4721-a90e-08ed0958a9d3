package com.gok.pboot.pms.cost.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.cost.entity.domain.CostIncomeCalculationDetail;
import com.gok.pboot.pms.cost.entity.dto.CostIncomeCalculationDTO;
import org.apache.ibatis.annotations.Param;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2025/02/18
 **/
public interface CostIncomeCalculationDetailMapper extends BaseMapper<CostIncomeCalculationDetail> {

    /**
     * 查询测算明细列表
     *
     * @param query
     * @return
     */
    List<CostIncomeCalculationDetail> findList(@Param("query") CostIncomeCalculationDTO query);

    /**
     * 批量保存
     *
     * @param saveEntries
     * @return
     */
    int batchSave(@Param("saveEntries") List<CostIncomeCalculationDetail> saveEntries);

    /**
     * 批量更新
     *
     * @param updateEntries
     * @return
     */
    int batchUpdate(@Param("updateEntries") List<CostIncomeCalculationDetail> updateEntries);

    /**
     * 测算汇总批量确认明细
     *
     * @param calculationIds 汇总ID集合
     * @param confirmStatus  确认状态
     * @param modifierId     操作者ID
     * @param modifier       操作人
     * @param confirmDate    确认时间
     * @return
     */
    int batchConfirmByCalculation(@NotNull @Param("calculationIds") List<Long> calculationIds,
                                  @Param("confirmStatus") Integer confirmStatus,
                                  @Param("confirmDate") String confirmDate,
                                  @Param("modifierId") Long modifierId,
                                  @Param("modifier") String modifier);

}
