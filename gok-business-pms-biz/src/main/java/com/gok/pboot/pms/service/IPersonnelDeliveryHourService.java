package com.gok.pboot.pms.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.entity.dto.AttendanceHourImportDTO;
import com.gok.pboot.pms.entity.dto.PersonnelDeliveryHourImportDTO;
import com.gok.pboot.pms.entity.dto.PersonnelReuseUpdateDTO;
import com.gok.pboot.pms.entity.vo.PersonnelDeliveryHourPageVO;
import org.springframework.validation.BindingResult;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/2/9
 */
public interface IPersonnelDeliveryHourService {

    /**
     * 考勤工时导入
     *
     * @param projectId     项目ID
     * @param excelList     数据列表
     * @param bindingResult 参数校验
     * @param file          文件
     * @return {@link ApiResult}
     */
    ApiResult attendanceHourImport(Long projectId, List<AttendanceHourImportDTO> excelList, BindingResult bindingResult, MultipartFile file);

    /**
     * 导入交付人员工时
     *
     * @param excelVOList   列表
     * @param bindingResult 错误信息列表
     * @return R
     */
    ApiResult importUser(List<PersonnelDeliveryHourImportDTO> excelVOList, BindingResult bindingResult, MultipartFile file);

    /**
     * 交付人员工时分页查询
     *
     * @param pageRequest 分页
     * @param projectId   项目ID
     * @param projectName 项目名称
     * @param time        时间范围
     * @param status      审核状态
     * @param name        人员姓名
     * @return {@link ApiResult<PersonnelDeliveryHourPageVO>}
     */
    Page<PersonnelDeliveryHourPageVO> pagePersonnelDeliveryHour(Long projectId, PageRequest pageRequest, String projectName, LocalDate time, Integer status, String name);

    /**
     * 编辑
     *
     * @param personnelReuseUpdateDTO
     * @return {@link ApiResult}
     */
    ApiResult<String> update(PersonnelReuseUpdateDTO personnelReuseUpdateDTO);

    /**
     * 批量逻辑删除
     *
     * @param list 要删除的id集合[]
     * @return {@link ApiResult}
     */
    ApiResult<String> batchDel(List<Long> list);

    /**
     * 考勤工时导入消息提醒
     *
     * @return {@link String}
     */
    Boolean messageAlert();

}
