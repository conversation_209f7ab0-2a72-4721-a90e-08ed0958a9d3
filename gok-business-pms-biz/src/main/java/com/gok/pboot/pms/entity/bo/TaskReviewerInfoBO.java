package com.gok.pboot.pms.entity.bo;

import com.gok.pboot.pms.enumeration.TaskReviewerTypeEnum;
import com.google.common.collect.ImmutableList;
import lombok.*;

import java.util.List;
import java.util.Set;

/**
 * 审核人信息（只包含主责）
 *
 * <AUTHOR>
 * @version 1.3.2
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@ToString
public class TaskReviewerInfoBO {

    /**
     * 审核人类型（主责）
     */
    private Set<TaskReviewerTypeEnum> types;

    /**
     * 业务经理人员ID
     */
    private Long businessManagerUserId;

    /**
     * 项目经理人员ID
     */
    private Long projectManagerUserId;

    /**
     * 项目售前人员ID
     */
    private Long projectPreSalesmanUserId;

    /**
     * 项目销售人员ID
     */
    private Long projectSalesmanUserId;

    /**
     * 直接上级人员ID
     */
    private Long directLeaderUserId;

    /**
     * 任务负责人ID列表
     */
    private List<Long> taskLeaderUserIds;

    private final static TaskReviewerInfoBO EMPTY = new TaskReviewerInfoBO(
            null, null, null, null, null, null,  ImmutableList.of()
    );

    public static TaskReviewerInfoBO empty() {
        return EMPTY;
    }
}
