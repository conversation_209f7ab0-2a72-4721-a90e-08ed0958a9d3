package com.gok.pboot.pms.cost.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 自定义补贴配置
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("cost_config_subsidy_custom")
public class CostConfigSubsidyCustom extends BeanEntity<Long> {

    private static final long serialVersionUID = 1L;

    /**
     * 版本ID
     */
    private Long versionId;

    /**
     * 补贴名称
     */
    private String subsidyName;

    /**
     * 补贴金额
     */
    private BigDecimal subsidyPrice;

    /**
     * 计算方式（0=按预计人天计算，1=按出差人天计算）
     */
    private Integer calculationMethod;
}
