package com.gok.pboot.pms.controller;

import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.base.admin.dto.PaymentDTO;
import com.gok.base.admin.dto.ProjectPaymentClaimDTO;
import com.gok.base.admin.dto.ProjectPaymentDTO;
import com.gok.base.admin.feign.RemotePaymentService;
import com.gok.bcp.upms.dto.MultiDimensionDeptDto;
import com.gok.bcp.upms.feign.RemoteOutMultiDeptService;
import com.gok.bcp.upms.feign.RemoteOutService;
import com.gok.components.common.util.R;
import com.gok.module.excel.api.annotation.ResponseExcel;
import com.gok.pboot.common.core.constant.SecurityConstants;
import com.gok.pboot.common.security.annotation.Inner;
import com.gok.pboot.pms.Util.DbApiUtil;
import com.gok.pboot.pms.common.constant.DictConstants;
import com.gok.pboot.pms.entity.vo.*;
import com.gok.pboot.pms.enumeration.*;
import com.gok.pboot.pms.service.IProjectPaymentClaimService;
import com.gok.pboot.pms.service.IProjectPaymentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 项目回款跟踪
 *
 * <AUTHOR>
 * @since 2023-09-26
 * @menu 项目回款跟踪
 */
@RestController
@RequiredArgsConstructor
@Api(tags = "项目回款跟踪")
@RequestMapping("/project-payment")
public class ProjectPaymentController {

    private final IProjectPaymentService projectPaymentService;

    private final IProjectPaymentClaimService projectPaymentClaimService;

    private final RemoteOutService remoteOutService;

    private final RemoteOutMultiDeptService remoteOutMultiDeptService;

    private final DbApiUtil dbApi;

    private final RemotePaymentService remotePaymentService;

    /**
     * 模糊查询带分页
     *
     * @param projectPaymentDTO {@link ProjectPaymentDTO}
     * @return {@link R}<{@link Page}<{@link ProjectPaymentVO}>>
     */
    @PostMapping("/findPage")
    @PreAuthorize("@pms.hasPermission('PROJECT_PAYBACK')")
    @ApiOperation(value = "模糊查询带分页", notes = "模糊查询带分页")
    public R<Page<ProjectPaymentVO>> findPage(@RequestBody ProjectPaymentDTO projectPaymentDTO) {
        return R.ok(projectPaymentService.findPage(projectPaymentDTO));
    }

    /**
     * 锁定
     *
     * @param projectPaymentDTO {@link ProjectPaymentDTO}
     * @return {@link R}
     */
    @PostMapping("/lock")
    @ApiOperation(value = "锁定选中数据", notes = "锁定选中数据")
    public R<String> lock(@RequestBody ProjectPaymentDTO projectPaymentDTO) {
        return Boolean.TRUE.equals(projectPaymentService.lock(projectPaymentDTO)) ? R.ok("修改成功") : R.failed("修改失败");
    }

    /**
     * 导出Excel选中数据（异步导出）
     *
     * @param projectPaymentDTO {@link ProjectPaymentDTO}
     * @return {@link List}
     */
    @PostMapping("/export")
    @ResponseExcel(name = "项目回款跟踪表")
    @ApiOperation(value = "导出选中数据", notes = "导出选中数据")
    public List export(@RequestBody ProjectPaymentDTO projectPaymentDTO) {
        return projectPaymentService.export(projectPaymentDTO);
    }

    /**
     * 插入数据
     *
     * @param paymentDTO {@link PaymentDTO}
     * @return {@link R}<{@link Boolean}>
     */
    @PostMapping("/save")
    @ApiOperation(value = "插入数据", notes = "插入数据")
    public R<Boolean> save(@Valid @RequestBody PaymentDTO paymentDTO) {
        return projectPaymentService.save(paymentDTO) > NumberUtils.INTEGER_ZERO
                ? R.ok(Boolean.TRUE) : R.failed(Boolean.FALSE);
    }

    /**
     * 根据id获取详情
     *
     * @param id 项目id
     * @return {@link R}<{@link ProjectPaymentClaimVO}>
     */
    @GetMapping("/getOne/{id}")
    @ApiOperation(value = "根据id获取详情", notes = "根据id获取详情")
    public R<ProjectPaymentClaimVO> getOne(@PathVariable("id") Long id) {
        return R.ok(projectPaymentService.getOne(id));
    }

    /**
     * 根据id更新数据
     *
     * @param projectPaymentClaimDTO {@link ProjectPaymentClaimDTO}
     * @return {@link R}<{@link Boolean}>
     */
    @PutMapping("/update")
    @ApiOperation(value = "根据id更新数据", notes = "根据id更新数据")
    public R<Boolean> update(@Valid @RequestBody ProjectPaymentClaimDTO projectPaymentClaimDTO) {
        return Boolean.TRUE.equals(projectPaymentService.update(projectPaymentClaimDTO))
                ? R.ok(Boolean.TRUE) : R.failed(Boolean.FALSE);
    }

    /**
     * 删除
     *
     * @param paymentDTO {@link PaymentDTO}
     * @return {@link R}
     */
    @DeleteMapping("/delete")
    @ApiOperation(value = "删除选中数据", notes = "删除选中数据")
    public R<String> delete(@RequestBody PaymentDTO paymentDTO) {
        return projectPaymentService.delete(paymentDTO.getIds());
    }

    /**
     * 认领操作(已认领 -> 待认领)
     *
     * @param id 项目回款跟踪id
     * @return {@link R}<{@link Boolean}>
     */
    @PutMapping("/claim/{id}")
    @ApiOperation(value = "认领操作(已认领 -> 待认领)", notes = "认领操作(已认领 -> 待认领)")
    public R<Boolean> unClaim(@PathVariable("id") Long id) {
        return Boolean.TRUE.equals(projectPaymentService.claim(id))
                ? R.ok(Boolean.TRUE) : R.failed(Boolean.FALSE);
    }

    /**
     * 认领操作(待认领 -> 已认领)
     *
     * @param projectPaymentClaimDTO {@link ProjectPaymentClaimDTO}
     * @return {@link R}<{@link Boolean}>
     */
    @PostMapping("/claim")
    @ApiOperation(value = "认领操作(待认领 -> 已认领)", notes = "认领操作(待认领 -> 已认领)")
    public R<Boolean> claim(@Valid @RequestBody ProjectPaymentClaimDTO projectPaymentClaimDTO) {
        return Boolean.TRUE.equals(projectPaymentService.claim(projectPaymentClaimDTO))
                ? R.ok(Boolean.TRUE) : R.failed(Boolean.FALSE);
    }

    /**
     * 客户台帐
     *
     * @param jsonObject {@link JSONObject}
     * @return {@link R}<{@link List}<{@link CustomerAccountVO}>>
     */
    @PostMapping("/customerAccount")
    @ApiOperation(value = "客户台帐", notes = "客户台帐")
    public R<List<CustomerAccountVO>> customerAccount(@RequestBody JSONObject jsonObject) {
        return R.ok(dbApi.getCustomerAccount((String) jsonObject.get("customerName")));
    }

    /**
     * 银行台帐
     *
     * @param jsonObject {@link JSONObject}
     * @return {@link R}<{@link List}<{@link BackAccountVO}>>
     */
    @PostMapping("/backAccount")
    @ApiOperation(value = "银行台帐", notes = "银行台帐")
    public R<List<BackAccountVO>> getBankAccount(@RequestBody JSONObject jsonObject) {
        return R.ok(dbApi.getBankAccount((String) jsonObject.get("name")));
    }

    /**
     * 根据合同编码搜索归属合同收款明细
     *
     * @param jsonObject {@link JSONObject}
     * @return {@link R}<{@link List}<{@link ContractPaymentVO2}>>
     */
    @PostMapping("/contractPayment")
    @ApiOperation(value = "归属合同收款明细", notes = "归属合同收款明细")
    public R<List<ContractPaymentVO2>> contractPayment(@RequestBody JSONObject jsonObject) {
        List<ContractPaymentVO2> contractPaymentVOList = dbApi.getContractPayment((String) jsonObject.get("id"))
                .stream()
                .filter(c -> ObjectUtils.isNotEmpty(c.getPaymentName()))
                .collect(Collectors.toList());
        return R.ok(contractPaymentVOList);
    }

    /**
     * 根据合同编码搜索归属合同收款明细
     *
     * @param jsonObject {@link JSONObject}
     * @return {@link R}<{@link List}<{@link ContractPaymentVO2}>>
     */
    @PostMapping("/contractName")
    @ApiOperation(value = "根据合同编码搜索归属合同收款明细", notes = "根据合同编码搜索归属合同收款明细")
    public R<List<ContractPaymentVO2>> contractName(@RequestBody JSONObject jsonObject) {
        return R.ok(dbApi.getContractName((String) jsonObject.get("contractName")));
    }

    /**
     * 项目台帐
     *
     * @param jsonObject {@link JSONObject}
     * @return {@link R}<{@link List}<{@link ContractPaymentVO2}>>
     */
    @PostMapping("/projectAccount")
    @ApiOperation(value = "项目台帐", notes = "项目台帐")
    public R<List<ProjectAccountVO>> projectAccount(@RequestBody JSONObject jsonObject) {
        return R.ok(dbApi.getProjectAccount((String) jsonObject.get("projectName")));
    }

    /**
     * 人员信息（id + 姓名）
     *
     * @param jsonObject {@link JSONObject}
     * @return {@link R}<{@link List}<{@link UserVO}>>
     */
    @PostMapping("/info")
    @ApiOperation(value = "人员信息", notes = "人员信息")
    public R<List<UserVO>> userInfo(@RequestBody JSONObject jsonObject) {
        List<UserVO> userList = new ArrayList<>();
        String username = (String) jsonObject.get("username");
        if (CharSequenceUtil.isNotBlank(username)) {
            remoteOutService.getUserList().getData().stream()
                    .filter(u -> CharSequenceUtil.isNotBlank(u.getUsername()))
                    .filter(user -> user.getUsername().contains(username))
                    .forEach(u -> {
                        UserVO userVO = UserVO.builder()
                                .userId(u.getUserId())
                                .username(u.getUsername())
                                .build();
                        userList.add(userVO);
                    });
            return R.ok(userList);
        }
        remoteOutService.getUserList().getData()
                .forEach(u -> {
                    UserVO userVO = UserVO.builder()
                            .userId(u.getUserId())
                            .username(u.getUsername())
                            .build();
                    userList.add(userVO);
                });
        return R.ok(userList);
    }

    /**
     * 行政部门信息（归属部门）
     *
     * @param deptName 组织名称
     * @param level 部门层级
     * @return {@link R}<{@link List}<{@link MultiDimensionDeptDto}>>
     */
    @Inner(value = false)
    @GetMapping("/innerDeptInfo")
    @ApiOperation(value = "行政部门信息（归属部门）", notes = "行政部门信息（归属部门）")
    public R<List<MultiDimensionDeptDto>> innerDeptInfo(@RequestParam(required = false, name = "deptName") String deptName,
                                                        @RequestParam(required = false, name = "level") Integer level) {
        if (CharSequenceUtil.isNotBlank(deptName)) {
            // 获取全部部门数据
            List<MultiDimensionDeptDto> deptList = remoteOutMultiDeptService
                    .getDeptList("行政组织", null, level)
                    .getData()
                    .stream().filter(d -> NumberUtils.INTEGER_ZERO.toString().equals(d.getStatus()))
                    .collect(Collectors.toList());
            // 用户输入的一级部门数据
            MultiDimensionDeptDto multiDimensionDeptDto = remoteOutMultiDeptService
                    .getDeptList("行政组织", null, null)
                    .getData()
                    .stream().filter(dept -> dept.getName().equals(deptName))
                    .collect(Collectors.toList()).get(NumberUtils.INTEGER_ZERO);

            return R.ok(deptList.stream().filter(dept -> dept.getParentId().equals(multiDimensionDeptDto.getDeptId())).collect(Collectors.toList()));
        }
        return remoteOutMultiDeptService.getDeptList("行政组织", deptName, level);
    }

    /**
     * 虚拟部门信息（核算部门）
     *
     * @param deptName 组织名称
     * @param level 部门层级
     * @return {@link R}<{@link List}<{@link MultiDimensionDeptDto}>>
     */
    @GetMapping("/virtualDeptInfo")
    @ApiOperation(value = "虚拟部门信息（核算部门）", notes = "虚拟部门信息（核算部门）")
    public R<List<MultiDimensionDeptDto>> virtualDeptInfo(@RequestParam(required = false, name = "deptName") String deptName,
                                                          @RequestParam(required = false, name = "level") Integer level) {
        return remoteOutMultiDeptService.getDeptList("行政组织", deptName, level);
    }

    /**
     * 待认领且收款日期超过三十天
     *
     * @return {@link R}
     */
    @Inner(false)
    @PostMapping("/updateLockStatus")
    public R<Void> updateLockStatus() {
        return projectPaymentService.updateLockStatus();
    }

    /**
     * 根据合同名称搜索归属合同收款明细
     *
     * @param jsonObject {@link JSONObject}
     * @return {@link R}<{@link List}<{@link ContractPaymentVO2}>>
     */
    @PostMapping("/getNames")
    @ApiOperation(value = "根据合同名称搜索归属合同收款明细", notes = "根据合同名称搜索归属合同收款明细")
    public R<List<ContractPaymentVO2>> getNames(@RequestBody JSONObject jsonObject) {
        // 1、获取业务板块键值对
        Map<Integer, String> businessBlockMap = dbApi.projectDict(DictConstants.BUSINESS_BLOCK_ID)
                .stream()
                .collect(Collectors.toMap(ProjectDictVo::getSelectvalue, ProjectDictVo::getSelectname, (a, b) -> a));
        // 2、获取技术类型键值对
        Map<Integer, String> skillTypeMap = dbApi.projectDict(DictConstants.SKILL_TYPE_ID)
                .stream()
                .collect(Collectors.toMap(ProjectDictVo::getSelectvalue, ProjectDictVo::getSelectname, (a, b) -> a));
        // 3、封装返回数据 根据id去重过滤数据
        List<ContractPaymentVO2> voList = new ArrayList<>();
        Set<Long> idSet = new HashSet<>();
        dbApi.getContractName((String) jsonObject.get("contractName")).forEach(c -> {
            if (idSet.add(c.getId())) {
                voList.add(c);
            }
        });
        voList.forEach(c -> {
            c.setBusinessBlockTxt(businessBlockMap.get(c.getBusinessBlock()));
            c.setSkillTypeTxt(skillTypeMap.get(c.getSkillType()));
        });
        return R.ok(voList);
    }

    /**
     * 页面列表查询字段
     *
     * @return {@link R}<{@link SalesQueryFormVO}>
     */
    @GetMapping("/dictItemList")
    @ApiOperation(value = "页面列表查询字段", notes = "页面列表查询字段")
    public R<ProjectQueryFormVO> dictItemList() {
        ProjectQueryFormVO.ProjectQueryFormVOBuilder builder = ProjectQueryFormVO.builder();
        // 1、业务板块
        builder.businessBlockVoList(projectPaymentClaimService.queryBusinessBlock());
        // 2、归属业务线
        List<BusinessLineVO> businessLineVoList = new ArrayList<>();
        for (BusinessLineEnum businessLine: BusinessLineEnum.values()) {
            businessLineVoList.add(new BusinessLineVO(businessLine.getValue(), businessLine.getName()));
        }
        builder.businessLineVoList(businessLineVoList);
        // 3、收款公司
        List<AttributableSubjectVO> attributableSubjectVoList = new ArrayList<>();
        for (AttributableSubjectEnum attributableSubject: AttributableSubjectEnum.values()) {
            attributableSubjectVoList.add(new AttributableSubjectVO(attributableSubject.getValue(), attributableSubject.getName()));
        }
        builder.attributableSubjectVoList(attributableSubjectVoList);
        // 4、单据状态
        List<DocumentStatusVO> documentStatusVoList = new ArrayList<>();
        for (ClaimStatusEnum c : ClaimStatusEnum.values()) {
            documentStatusVoList.add(new DocumentStatusVO(c.getName(), c.getName()));
        }
        for (PushStatusEnum p : PushStatusEnum.values()) {
            documentStatusVoList.add(new DocumentStatusVO(p.getName(), p.getName()));
        }
        for (LockStatusEnum l : LockStatusEnum.values()) {
            documentStatusVoList.add(new DocumentStatusVO(l.getName(), l.getName()));
        }
        builder.documentStatusVoList(documentStatusVoList);
        // 5、收款平台
        builder.paymentPlatformVoList(remotePaymentService.list(SecurityConstants.FROM_IN).getData());

        return R.ok(builder.build());
    }

}
