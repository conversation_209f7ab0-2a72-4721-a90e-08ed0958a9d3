package com.gok.pboot.pms.service.fegin;


import com.gok.bcp.upms.vo.SysUserInfoPmsVo;
import com.gok.bcp.upms.vo.SysUserOutVO;
import com.gok.bcp.upms.vo.SysUserTalentVO;
import com.gok.bcp.upms.vo.SysUserVo;
import com.gok.pboot.pms.common.base.R;
import com.gok.pboot.pms.entity.dto.UserPmsDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * 中台用户服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "centerUserService", value = "gok-bcp-upms-biz")
public interface CenterUserService {

    /**
     * 获取传入的userId的用户信息+下级用户列表
     *
     * @param id 用户id
     * @return 用户信息+下级用户列表
     */
    @GetMapping("/outPms/getUserAndUnderUserListById")
    R<List<SysUserOutVO>> getUserAndUnderUserListById(@RequestParam("id") Long id);

    /**
     * 多参数获取用户信息
     *
     * @param userPmsDTO 系统用户传输对象
     * @return 用户信息列表
     */
    @GetMapping("/outPms/getUserListByMultiParameterPms")
    R<List<SysUserOutVO>> getUserListByMultiParameterPms(@SpringQueryMap UserPmsDTO userPmsDTO);

    /**
     * 获取传入的userIds的上级
     *
     * @param ids 用户id集合
     * @return 上级用户信息
     */
    @GetMapping("/outPms/getTopUserByIds")
    R<Map<Long, List<SysUserOutVO>>> getTopUserByIds(@SpringQueryMap @RequestParam(name = "ids") List<Long> ids);

    /**
     * 获得用户登录后的信息
     *
     * @param principal 账号（account）
     * @return {@link R<SysUserInfoPmsVo>}
     */
    @GetMapping("/outPms/getUserInfoPmsByPrincipal")
    R<SysUserInfoPmsVo> getUserInfoPmsByPrincipal(
            @RequestParam(name = "principal") String principal,
            @RequestParam(name = "clientId") String clientId
    );

    /**
     * 通过用户id获取用户信息
     *
     * @param userId 用户id
     * @return {@link R<SysUserVo>} 应用列表
     */
    @GetMapping("/out/getUserInfoById")
    R<SysUserVo> getUserInfoById(@RequestParam("userId") Long userId);

    /**
     * 账号密码查询用户信息
     *
     * @param account  账号
     * @param password 密码
     * @return 提示信息
     */
    @PostMapping("/out/user/getUserInfoByAccountAndPassword")
    R<SysUserOutVO> getUserInfoByAccountAndPassword(@RequestParam("account") String account,
                                                    @RequestParam("password") String password);

    /**
     * 通过电话号码批量获取用户信息
     * @param phones
     * @return {@link com.gok.pboot.pms.common.base.R}<{@link SysUserTalentVO}>应用列表
     * @return list  应用列表
     */
    @ApiOperation(value = "通过电话号码批量获取用户信息", tags = "通过电话号码批量获取用户信息")
    @GetMapping("/outTalent/getUserListByPhones")
   R<List<SysUserTalentVO>> getUserListByPhones(@RequestParam(name = "phones")List<String> phones);


}
