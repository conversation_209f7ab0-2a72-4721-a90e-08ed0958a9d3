package com.gok.pboot.pms.service.impl;

import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.entity.domain.ProjectTask;
import com.gok.pboot.pms.entity.domain.ProjectTaskGroup;
import com.gok.pboot.pms.entity.dto.ProjectTaskGroupAddDTO;
import com.gok.pboot.pms.entity.dto.ProjectTaskGroupUpdateDTO;
import com.gok.pboot.pms.entity.dto.ProjectTaskToGroupDTO;
import com.gok.pboot.pms.entity.vo.ProjectTaskCardVO;
import com.gok.pboot.pms.entity.vo.ProjectTaskGroupVO;
import com.gok.pboot.pms.mapper.ProjectInfoMapper;
import com.gok.pboot.pms.mapper.ProjectTaskGroupMapper;
import com.gok.pboot.pms.mapper.ProjectTaskMapper;
import com.gok.pboot.pms.service.IProjectTaskGroupService;
import com.google.common.collect.ImmutableList;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.ValidationException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 项目任务看板服务
 *
 * <AUTHOR>
 * @date 2023/8/23
 */
@Service
@AllArgsConstructor
public class ProjectTaskGroupServiceImpl implements IProjectTaskGroupService {

    private final ProjectTaskGroupMapper mapper;
    private final ProjectInfoMapper projectInfoMapper;
    private final ProjectTaskMapper projectTaskMapper;

    private static final int GROUP_COUNT_MAX = 15;
    private static final long UNGROUPED_ID = 0L;
    private static final String UNGROUPED_NAME = "未分组";
    private static final String GROUP_NO_EXIST = "分组不存在";

    @Override
    public Long add(ProjectTaskGroupAddDTO dto) {
        Long projectId = dto.getProjectId();
        Integer groupCount;
        ProjectTaskGroup po;

        if (projectId == null || !projectInfoMapper.isExist(projectId)) {
            throw new ValidationException("项目不存在");
        }
        // 判断当前已有分组数是否大于最大数量
        groupCount = mapper.getCountByProjectId(projectId);
        if (groupCount >= GROUP_COUNT_MAX) {
            throw new ValidationException("分组超过最大数量");
        }
        po = ProjectTaskGroup.of(dto);
        BaseBuildEntityUtil.buildInsert(po);
        mapper.insert(po);

        return po.getId();
    }

    @Override
    @Transactional
    public Long updateTitle(ProjectTaskGroupUpdateDTO dto) {
        Long groupId = dto.getGroupId();
        String title = dto.getTitle();
        ProjectTaskGroup group;
        ProjectTaskGroup po;

        group = mapper.selectById(groupId);
        if (group == null) {
            throw new ValidationException(GROUP_NO_EXIST);
        }
        if (title == null || title.isEmpty()) {
            throw new ValidationException("分组名称不能为空");
        }
        po = ProjectTaskGroup.update(dto);
        BaseBuildEntityUtil.buildUpdate(po);
        mapper.updateById(po);

        return group.getId();
    }

    @Override
    @Transactional
    public Long updateCapacity(ProjectTaskGroupUpdateDTO dto) {
        Long groupId = dto.getGroupId();
        Integer capacity = dto.getCapacity();
        ProjectTaskGroup group;
        Integer projectCount;
        ProjectTaskGroup po;

        group = mapper.selectById(groupId);
        if (group == null) {
            throw new ValidationException(GROUP_NO_EXIST);
        }
        // 判断当前数据库数量是否大于这个数字，大于则不能更新
        projectCount = projectTaskMapper.getCountByGroupId(groupId);
        if (capacity == null || (capacity < projectCount && capacity != -1)) {
            throw new ValidationException("当前任务数大于规定的在制品数量上限，无法操作");
        }
        po = ProjectTaskGroup.update(dto);
        BaseBuildEntityUtil.buildUpdate(po);
        mapper.updateById(po);

        return group.getId();
    }

    @Override
    @Transactional
    public Boolean delete(Long groupId) {
        ProjectTaskGroup group = mapper.selectById(groupId);
        if (group == null) {
            throw new ValidationException("分组不存在，无法删除");
        }
        mapper.delete(groupId);
        projectTaskMapper.groupIdInit(groupId);
        return Boolean.TRUE;
    }

    @Override
    @Transactional
    public Long putToGroup(ProjectTaskToGroupDTO dto) {
        Long taskId = dto.getTaskId();
        Long groupId = dto.getGroupId();
        ProjectTaskGroup group;
        Integer capacity;
        Integer projectCount;

        // 组别为0时，直接置为0
        if(groupId == 0L){
            projectTaskMapper.groupIdUpdate(taskId, groupId);
            return taskId;
        }

        // 组别不为0时进行判断
        group = mapper.selectById(groupId);
        if (group == null) {
            throw new ValidationException(GROUP_NO_EXIST);
        }
        // 若设有最大数量，则判断数量，超出则不能插入
        capacity = group.getCapacity();
        if (capacity < 0) {
            projectTaskMapper.groupIdUpdate(taskId, groupId);
            return taskId;
        }
        projectCount = projectTaskMapper.getCountByGroupId(groupId);
        if (capacity <= projectCount) {
            throw new ValidationException("超出分组在制品数量设置上限，无法操作");
        }
        projectTaskMapper.groupIdUpdate(taskId, groupId);

        return taskId;
    }

    @Override
    public List<ProjectTaskGroupVO> findList(Map<String, Object> filter) {
        List<ProjectTaskGroup> groups;
        List<ProjectTask> projectTasks;
        List<ProjectTaskCardVO> taskCardVoS = new ArrayList<>();
        List<ProjectTaskGroupVO> taskGroupVoS = new ArrayList<>();
        ProjectTaskGroupVO unGrouped =
                new ProjectTaskGroupVO(UNGROUPED_ID, UNGROUPED_NAME, null, null);
        Map<Long, List<ProjectTaskCardVO>> cardVoMap;

        Long projectId = (Long) filter.get("projectId");
        if (projectId == null || !projectInfoMapper.isExist(projectId)) {
            return ImmutableList.of();
        }

        // 查询group
        groups = mapper.getListByProjectId(projectId);
        taskGroupVoS.add(unGrouped);
        groups.forEach(group -> {
            ProjectTaskGroupVO taskGroupVO = ProjectTaskGroupVO.of(group);
            taskGroupVoS.add(taskGroupVO);
        });

        // 查询task
        projectTasks = projectTaskMapper.findList(filter);
        projectTasks = projectTasks.stream()
                .sorted(Comparator.comparing(ProjectTask::getExpectedStartTime))
                .collect(Collectors.toList());
        projectTasks.forEach(projectTask -> {
            ProjectTaskCardVO taskCardVO = ProjectTaskCardVO.of(projectTask);
            taskCardVoS.add(taskCardVO);
        });

        // 组装vo
        cardVoMap = taskCardVoS.stream()
                .collect(Collectors.groupingBy(ProjectTaskCardVO::getGroupId));
        taskGroupVoS.forEach(groupVO -> {
            groupVO.setProjectTaskCards(cardVoMap.getOrDefault(groupVO.getGroupId(), ImmutableList.of()));
        });

        return taskGroupVoS;
    }


}
