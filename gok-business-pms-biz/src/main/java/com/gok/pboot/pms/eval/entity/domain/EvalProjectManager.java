package com.gok.pboot.pms.eval.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import lombok.*;

import java.math.BigDecimal;

/**
 * 项目经理评价表
 *
 * <AUTHOR>
 * @create 2025/05/08
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("eval_project_manager")
public class EvalProjectManager extends BeanEntity<Long> {

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 项目整体评价表ID
     */
    private Long overviewId;

    /**
     * 交付类型
     */
    private Integer deliverType;

    /**
     * 指标类型
     * {@link com.gok.pboot.pms.eval.enums.EvalIndexTypeEnum}
     */
    private Integer indexType;

    /**
     * 评定项目
     * {@link com.gok.pboot.pms.eval.enums.AssessmentProjectEnum}
     */
    private Integer assessmentProject;

    /**
     * 权重
     */
    private BigDecimal weight;

    /**
     * 总支撑官/职能领导评分
     */
    private BigDecimal managerScore;

    /**
     * PMO评分
     */
    private BigDecimal pmoScore;

    /**
     * 得分
     */
    private BigDecimal score;

}
