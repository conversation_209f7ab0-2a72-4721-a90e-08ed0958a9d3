package com.gok.pboot.pms.enumeration;

import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import com.gok.pboot.pms.Util.EnumUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 技术类型B表枚举
 *
 * <AUTHOR>
 * @date 13/12/2023
 */
@Getter
@AllArgsConstructor
public enum ContractTechnologyTypeBTableEnum implements ValueEnum<Integer> {

    /**
     * ICT集成
     */
    ICT_INTEGRATED(0, "ICT集成"),

    /**
     * 安全服务
     */
    SECURITY_SERVICE(1, "安全服务"),

    /**
     * 软件开发
     */
    SOFTWARE_DEVELOPMENT(2, "软件开发"),

    /**
     * ERP交付
     */
    ERP_DELIVERY(3, "ERP交付"),

    /**
     * 数据治理
     */
    DATA_GOVERNANCE(4, "数据治理");

    private final Integer value;

    private final String name;

    /**
     * 根据值列表字符串获取名称列表字符串
     *
     * @param valueListStr 值列表字符串
     * @return 名称列表字符串 无则返回空字符串
     */
    public static String getNameListStrByValueListStr(String valueListStr) {
        if (StrUtil.isBlank(valueListStr)) {
            return StrUtil.EMPTY;
        }
        List<Integer> valueList = Arrays.asList(valueListStr.split(StrPool.COMMA)).stream()
                .map(Integer::valueOf)
                .sorted()
                .collect(Collectors.toList());
        return valueList.stream()
                .map(value -> EnumUtils.getNameByValue(ContractTechnologyTypeBTableEnum.class, value))
                .filter(name -> StrUtil.isNotBlank(name))
                .sorted()
                .collect(Collectors.joining(StrPool.COMMA));
    }

}
