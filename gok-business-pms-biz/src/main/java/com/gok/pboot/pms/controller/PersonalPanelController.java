package com.gok.pboot.pms.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.module.excel.api.annotation.ResponseExcel;
import com.gok.module.excel.api.enums.ExcelDateEnum;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.entity.dto.PanelRequestDTO;
import com.gok.pboot.pms.entity.vo.PanelProjectSituationAnalysisVO;
import com.gok.pboot.pms.entity.vo.PanelProjectSituationDetailsExportVO;
import com.gok.pboot.pms.entity.vo.PanelProjectSituationDetailsVO;
import com.gok.pboot.pms.entity.vo.PanelProjectSituationVO;
import com.gok.pboot.pms.service.PersonalPanelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @desc 统计查询-个人面板
 * @createTime 2023/2/21 14:29
 */
@Slf4j
@RestController
@RequestMapping("/panel")
@RequiredArgsConstructor
public class PersonalPanelController {

    private final PersonalPanelService panelService;


    /**
     * 统计个人面板-项目情况/饱和度分析-总计
     * @param filter 查询条件
     * @return {@link ApiResult<PanelProjectSituationAnalysisVO>}
     */
    @PostMapping("/analysis/total")
    public ApiResult<PanelProjectSituationAnalysisVO> analysisTotal(@RequestBody PanelRequestDTO filter){
        return panelService.analysisTotal(filter);
    }

    /**
     * 个人面板-项目情况-分页
     *
     * @param filter 分页条件
     * @return {@link ApiResult<Page<PanelProjectSituationVO>>}
     */
    @PostMapping("/projectSituation/page")
    public ApiResult<Page<PanelProjectSituationVO>> page(@RequestBody PanelRequestDTO filter) {
        return panelService.page(filter);
    }

    /**
     * 个人面板-项目情况-导出
     *
     * @param filter 查询条件
     * @return 导出文件
     */
    @PostMapping("/projectSituation/export")
    @ResponseExcel(name = "个人工时统计报表", nameWithDate = ExcelDateEnum.DATE, dateFormat = "yyyy-MM-dd")
    public List<PanelProjectSituationVO> export(@RequestBody PanelRequestDTO filter) {
        return panelService.export(filter);
    }

    /**
     * 个人面板-项目情况-明细-分页
     * @param filter 查询条件
     * @return {@link ApiResult<Page<PanelProjectSituationDetailsVO>>}
     */
    @PostMapping("/projectSituation/detail/page")
    public ApiResult<Page<PanelProjectSituationDetailsVO>> detailPage(@RequestBody PanelRequestDTO filter) {
        return panelService.detailPage(filter);
    }

    /**
     * 个人面板-项目情况-明细-导出
     *
     * @param filter 查询条件
     * @return 导出文件
     */
    @PostMapping("/projectSituation/detail/export")
    @ResponseExcel(name = "个人工时统计明细报表",nameWithDate = ExcelDateEnum.DATE, dateFormat = "yyyy-MM-dd")
    public List<PanelProjectSituationDetailsExportVO> detailExport(@RequestBody PanelRequestDTO filter) {
        return panelService.detailExport(filter);
    }

    /**
     * 个人面板-饱和度分析-分页
     * @param filter 查询条件
     * @return {@link ApiResult<Page<PanelProjectSituationAnalysisVO>>}
     */
    @PostMapping("/analysis/page")
    public ApiResult<Page<PanelProjectSituationAnalysisVO>> analysisPage(@RequestBody PanelRequestDTO filter){
        return panelService.analysisPage(filter);
    }
}
