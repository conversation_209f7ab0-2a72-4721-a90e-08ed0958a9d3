package com.gok.pboot.pms.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.entity.domain.ContractLedgerDetail;
import com.gok.pboot.pms.entity.vo.ContractPaymentVo;

import java.util.List;
import java.util.Map;

/**
 * 合同台账明细表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-02-23 10:46:40
 */
public interface IContractLedgerDetailService extends IService<ContractLedgerDetail> {

    /**
     * 根据合同id查询合同款项记录
     *
     * @param id 合同id
     * @return 款项记录
     */
    List<ContractPaymentVo> getContractPaymentInfoVoList(Long id);

    /**
     * 批量查询合同款项记录
     *
     * @param ids 合同ids
     * @return 款项记录
     */
    Map<Long, List<ContractPaymentVo>> getContractPaymentInfoVoList(List<Long> ids);

    /**
     * 批量查询合同明细
     *
     * @param ids 合同ids
     * @return
     */
    Map<Integer, List<ContractLedgerDetail>> getContractLedgerDetailByMainIds(List<Long> ids);

}

