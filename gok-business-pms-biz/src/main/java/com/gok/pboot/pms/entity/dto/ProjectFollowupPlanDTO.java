package com.gok.pboot.pms.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目后续计划表 DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectFollowupPlanDTO {

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 工作项
     */
    private String workItem;

    /**
     * 负责人id集合 以,分割
     */
    private String manager;

    /**
     * 预计投入周期
     */
    private Integer planCycle;

    /**
     * 预计工时(人天)
     */
    private BigDecimal planWorkHours;

    /**
     * 预计项目管理费用
     */
    private BigDecimal projectManageCostEst;

    /**
     * 客户侧或外部对接人
     */
    private String clientExternalContact;

    /**
     * 交接或说明文档id集合 以,分割
     */
    private String handoverDescDocs;

} 