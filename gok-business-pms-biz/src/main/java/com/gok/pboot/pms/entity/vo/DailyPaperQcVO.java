package com.gok.pboot.pms.entity.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * - 日报一览表查询vo -
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/8/24 14:34
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(DailyPaperQcVO.ALIAS)
public class DailyPaperQcVO  {

    public static final String ALIAS = "mhour_daily_paper";

    /**
     * ID
     */
    private Long id;

    /**
     * 部门ID
     */
    private Long userDeptId;

    /**
     * 人员状态
     * @see com.gok.pboot.pms.enumeration.PersonnelStatusEnum
     */
    private Integer userStatus;

    /**
     * 提交日期
     */
    private LocalDate submissionDate;

    /**
     * 是否工作日（0=否，1=是）
     */
    private Integer workday;

    /**
     * 假期类型：1-法定节假日 0-普通休息日
     */
    private Integer holidayType;

    /**
     * 填报状态（0=正常，1=滞后）
     */
    private Integer fillingState;

    /**
     * 审核状态（0=未提交，1=已退回，2=待审核，3=不通过，4=已通过）
     */
    private Integer approvalStatus;

    /**
     * 日常工时数量
     */
    private BigDecimal dailyHourCount;

    /**
     * 加班工时数量
     */
    private BigDecimal addedHourCount;

    /**
     * 是否异常日报
     */
    private Boolean abnormalFlag;

    /**
     * 是否有请假
     */
    private Boolean hasLeaveHour;

    /**
     * 有效请假时长
     */
    private BigDecimal avaliableLeavehour;

    /**
     * 请假信息提示
     */
    private String leaveInfoPrompt;

    /**
     * 请假小时提示
     */
    private String leaveHourPrompt;

    /**
     * 请假数据类型
     */
    @Deprecated
    private String type;
}
