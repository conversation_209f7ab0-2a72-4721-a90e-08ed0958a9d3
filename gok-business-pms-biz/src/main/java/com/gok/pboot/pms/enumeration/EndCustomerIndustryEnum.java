package com.gok.pboot.pms.enumeration;

/**
 * 项目-最终客户行业枚举
 *
 * <AUTHOR>
 */
public enum EndCustomerIndustryEnum implements ValueEnum<Integer> {
    /**
     * 政府
     */
    government(0, "政府"),
    /**
     * 教育
     */
    education(1, "教育"),
    /**
     * 金融
     */
    finance(2, "金融"),
    /**
     * 能源
     */
    energy(3, "能源"),
    /**
     * 运营商
     */
    operator(4, "运营商"),
    /**
     * 工业制造
     */
    industrial_manufacturing(5, "工业制造"),
    /**
     * 医疗
     */
    health(6, "医疗"),
    /**
     * 其他
     */
    other(7, "其他");

    //值
    private Integer  value;
    //名称
    private String name;

    EndCustomerIndustryEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
    /**
     * 获取值
     *
     * @return Integer
     */
    @Override
    public Integer getValue() {
        return value;
    }

    /**
     * 获取名称
     *
     * @return String
     */
    @Override
    public String getName() {
        return name;
    }

    public static String getNameByVal(Integer value) {
        for (EndCustomerIndustryEnum endCustomerIndustryEnum : EndCustomerIndustryEnum.values()) {
            if (endCustomerIndustryEnum.value.equals(value)) {
                return endCustomerIndustryEnum.name;
            }
        }
        return "";
    }
    public static Integer getValByName(String name) {
        for (EndCustomerIndustryEnum endCustomerIndustryEnum : EndCustomerIndustryEnum.values()) {
            if (endCustomerIndustryEnum.getName().equals(name)) {
                return endCustomerIndustryEnum.getValue();
            }
        }
        return null;
    }

    public static EndCustomerIndustryEnum getEndCustomerIndustryEnum(Integer value) {
        for (EndCustomerIndustryEnum endCustomerIndustryEnum : EndCustomerIndustryEnum.values()) {
            if (endCustomerIndustryEnum.value.equals(value)) {
                return endCustomerIndustryEnum;
            }
        }
        return null;
    }
}
