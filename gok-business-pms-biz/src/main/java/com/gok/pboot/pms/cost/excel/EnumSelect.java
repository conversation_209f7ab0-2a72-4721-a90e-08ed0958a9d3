package com.gok.pboot.pms.cost.excel;

import cn.hutool.core.util.StrUtil;
import com.gok.module.excel.api.service.ExcelDynamicSelect;
import com.gok.pboot.pms.cost.enums.ExcelSelectEnum;


/**
 * 枚举选择
 *
 * <AUTHOR>
 * @date 2025/06/20
 */
public class EnumSelect implements ExcelDynamicSelect {


    @Override
    public String[] getSource(String code) {
        if (StrUtil.isBlank(code)) {
            return new String[0];
        }
        ExcelSelectEnum excelSelectEnum = ExcelSelectEnum.valueOf(code);
        return excelSelectEnum.getSource();
    }
}
