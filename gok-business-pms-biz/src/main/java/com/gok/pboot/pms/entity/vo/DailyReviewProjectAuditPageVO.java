package com.gok.pboot.pms.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @desc 日报审核-分页展示 基础VO
 * @createTime 2023/2/15 11:09
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@ApiModel("日报审核-分页展示")
@Deprecated
public class DailyReviewProjectAuditPageVO {
    /**
     * 项目名称
     */
    @ApiModelProperty("项目名称")
    private String projectName;
    /**
     * 日报条目id
     */
    @ApiModelProperty("日报条目id")
    private Long id;
    /**
     * 提交人id
     */
    @ApiModelProperty("提交人id")
    private Long userId;
    /**
     * 提交人姓名
     */
    @ApiModelProperty("提交人姓名")
    private String userRealName;
    /**
     * 任务名称
     */
    @ApiModelProperty("任务名称")
    private String taskName;
    /**
     * 任务ID
     */
    private Long taskId;
    /**
     * 工时类型
     */
    @ApiModelProperty("工时类型")
    private Integer workType;
    /**
     * 工时类型
     */
    @ApiModelProperty("工时类型")
    private String workTypeName;
    /**
     * 填报日期
     */
    @ApiModelProperty("填报日期")
    private LocalDate submissionDate;

    /**
     * 填报日期带周
     */
    @ApiModelProperty("填报日期带周")
    private String submissionDateFormatted;
    /**
     * 提交日期
     */
    @ApiModelProperty("提交日期")
    private LocalDate commitDate;
    /**
     * 填报状态
     */
    @ApiModelProperty("填报状态")
    private Integer fillingState;
    /**
     * 填报状态
     */
    @ApiModelProperty("填报状态")
    private String fillingStateName;
    /**
     * 正常工时
     */
    @ApiModelProperty("正常工时")
    private Double normalHours;
    /**
     * 加班工时
     */
    @ApiModelProperty("加班工时")
    private Double addedHours;
    /**
     * 工作内容
     */
    @ApiModelProperty("工作内容")
    private String description;
    /**
     * 昨日计划
     */
    @ApiModelProperty("昨日计划")
    private String yesterdayPlan;
    /**
     * 审核人
     */
    @ApiModelProperty("审核人")
    private String approvalName;
    /**
     * 审核状态
     */
    @ApiModelProperty("审核状态")
    private Integer approvalStatus;
    /**
     * 审核状态
     */
    @ApiModelProperty("审核状态")
    private String approvalStatusName;
    /**
     * OA加班工时
     */
    private BigDecimal oaAddedHours;
    /**
     * 是否可编辑 ,1可0不可
     * {@link com.gok.pboot.enumeration.entity.YesOrNoEnum}
     */
    private Integer isEdit;

    /**
     * 假期类型：1-法定节假日 0-普通休息日
     */
    private Integer holidayType;
}
