package com.gok.pboot.pms.cost.entity.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gok.module.excel.api.annotation.ExcelSelected;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 收入结算导入DTO
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CostIncomeSettlementImportDTO {

    /**
     * 结算开始日期
     */
    @ExcelProperty("结算开始日期")
    private String startDate;

    /**
     * 结算截止日期
     */
    @ExcelProperty("结算截止日期")
    private String endDate;

    /**
     * 结算含税金额
     */
    @ExcelProperty("结算含税金额")
    private String budgetAmountIncludedTax;

    /**
     * 税率OA字典ID
     */
    @ExcelProperty("税率")
    @ExcelSelected(source = {"0","1%","3%","6%","9%","13%"})
    private String taxRate;

    /**
     * 备注说明
     */
    @ExcelProperty("备注说明")
    private String remarksDesc;

    /**
     * 结算单编号
     */
    @ExcelProperty("结算单编号")
    private String settlementNumber;
}
