package com.gok.pboot.pms.eval.entity.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 工单评价校准
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("eval_task_calibration")
public class EvalTaskCalibration  extends BeanEntity<Long>{

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 工单类型
     */
    private Integer taskType;

    /**
     * 员工id
     */
    private Long employeeId;

    /**
     * 员工姓名
     */
    private String employeeName;

    /**
     * 当前评分
     */
    private BigDecimal currentScore;

    /**
     * 当前等级
     */
    private Integer currentLevel;

    /**
     * 调整后的评分
     */
    private BigDecimal adjustedScore;

    /**
     * 调整后等级
     */
    private Integer adjustedLevel;

    /**
     * 调整原因
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String adjustmentReason;

    /**
     * 审核状态
     */
    private Integer calibrationStatus;

    /**
     * 退回原因
     */
    private String returnReason;

    /**
     * 排名
     */
    private Integer rank;
}