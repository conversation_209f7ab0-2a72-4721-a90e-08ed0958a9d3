package com.gok.pboot.pms.cost.entity.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 工单工时月度查询DTO
 *
 * <AUTHOR>
 * @date 2024/04/16
 */
@Data
public class CostTaskDailyPaperMonthlyDTO {

    @NotBlank(message = "月份不能为空")
    private String startDate;

    private String projectName;

    private Integer approvalStatusTab;

    private List<Integer> approvalStatusList;
} 