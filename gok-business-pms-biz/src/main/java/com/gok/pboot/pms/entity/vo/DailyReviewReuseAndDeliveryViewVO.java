package com.gok.pboot.pms.entity.vo;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.entity.bo.DailyReviewReuseAndDeliveryViewBO;
import com.gok.pboot.pms.enumeration.ApprovalStatusEnum;
import com.google.common.base.Strings;
import lombok.*;
import org.apache.commons.lang3.ObjectUtils;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 工时审核 复用+交付 条目查询
 *
 * <AUTHOR>
 * @version 1.3.2
 */
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class DailyReviewReuseAndDeliveryViewVO {

    /**
     * ID
     */
    private String id;

    /**
     * 人员名称
     */
    private String userRealName;

    /**
     * 所属月份
     * eg. "2023-12"
     */
    private String month;

    /**
     * 汇总工时
     */
    private Double aggregatedDays;

    /**
     * 类型（"人才复用" | "交付人员"）
     */
    private String type;

    /**
     * 审核状态
     * @see com.gok.pboot.pms.enumeration.ApprovalStatusEnum
     */
    private Integer approvalStatus;

    /**
     * 审核状态名称
     */
    private String approvalStatusName;

    /**
     * 审核人
     */
    private String approvalName;

    /**
     * 工作日正常工时（天）
     */
    private BigDecimal normalWorkDays;

    /**
     * 休息日加班工时（天）
     */
    private BigDecimal restWorkDays;

    /**
     * 节假日加班工时（天）
     */
    private BigDecimal holidaysWorkDays;

    /**
     * 工作日调休工时（天）
     */
    private BigDecimal ompensatoryDays;


    public static DailyReviewReuseAndDeliveryViewVO from(DailyReviewReuseAndDeliveryViewBO bo) {
        DailyReviewReuseAndDeliveryViewVO result = new DailyReviewReuseAndDeliveryViewVO();
        LocalDate monthDate = bo.getMonthDate();
        Integer approvalStatus = bo.getApprovalStatus();
        result.setId(String.valueOf(bo.getId()));
        result.setUserRealName(bo.getUserRealName());
        result.setMonth(LocalDateTimeUtil.format(monthDate, DatePattern.NORM_MONTH_FORMATTER));
        result.setAggregatedDays(ObjectUtils.defaultIfNull(bo.getAggregatedDays(), 0D));
        result.setType(bo.getType());
        result.setApprovalStatus(approvalStatus);
        result.setApprovalStatusName(EnumUtils.getNameByValue(ApprovalStatusEnum.class, approvalStatus));
        result.setApprovalName(Strings.nullToEmpty(bo.getApprovalName()));

        result.setNormalWorkDays(bo.getNormalWorkDays());
        result.setRestWorkDays(bo.getRestWorkDays());
        result.setHolidaysWorkDays(bo.getHolidaysWorkDays());
        result.setOmpensatoryDays(bo.getOmpensatoryDays());
        return result;
    }
}
