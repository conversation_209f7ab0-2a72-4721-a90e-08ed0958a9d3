package com.gok.pboot.pms.entity.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 工时
 * @since 2024/7/16
 */
@Data
public class DailyPaperHourVO {

    /**
     * 正常工时
     */
    private BigDecimal normalHours;

    /**
     * 加班工时
     */
    private BigDecimal addedHours;

    /**
     * 工作日加班工时
     */
    private BigDecimal workOvertimeHours;

    /**
     * 休息日加班工时
     */
    private BigDecimal restOvertimeHours;

    /**
     * 节假日加班工时
     */
    private BigDecimal holidayOvertimeHours;

    /**
     * 总工时
     */
    private BigDecimal totalHours;
}
