package com.gok.pboot.pms.handler.perm;

import com.gok.pboot.pms.entity.bo.TaskReviewerInfoBO;
import com.gok.pboot.pms.entity.dto.CustomerBusinessDTO;
import com.gok.pboot.pms.entity.dto.CustomerBusinessUnitPageDTO;
import com.google.common.collect.Table;
import org.springframework.data.util.Pair;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * PMS数据检索器
 *
 * <AUTHOR>
 * @version 1.3.2
 */
public interface PmsRetriever {

    String PROJECT_AVAILABLE_KEY = "projectIdsInDataScope";
    String BUSINESS_AVAILABLE_KEY = "businessIdsInDataScope";
    String SCOPE= "scope";
    String ALL = "all";


    /**
     * 客户经营单元获取所有可用客户经营单元ID
     *
     * @return 项目ID集合
     */
    List<Long> getBusinessIdsAvailable(CustomerBusinessDTO dto);

    /**
     * 所属客户获取所有可用客户经营单元ID
     *
     * @return 项目ID集合
     */
    List<Long> getBusinessIdsAvailable(CustomerBusinessUnitPageDTO dto);



    /**
     * 获取所有可用项目ID
     *
     * @return 项目ID集合
     */
    List<Long> getProjectIdsAvailable();

    /**
     * <p>如果权限为all，仅填充scope字段为all到参数filter中</p>
     * <p>否则返回所有可用项目ID</p>
     *
     * @return 项目ID集合
     */
    List<Long> getProjectIdsAvailable(Map<String, Object> filter);

    /**
     * <p>如果权限为all，仅填充scope字段为all到参数filter中</p>
     * <p>否则返回所有可用商机ID</p>
     *
     * @return 商机ID集合
     */
    List<Long> getBusinessIdsAvailable(Map<String, Object> filter);

    /**
     * 获取所有可用项目ID填充到参数Map
     *
     * @param filter 参数Map
     */
    void calcProjectIdsAvailable(Map<String, Object> filter);

    /**
     * 验证项目是否可以操作
     *
     * @param projectId 项目ID
     * @return bool
     */
    boolean isProjectAvailable(Long projectId);

    /**
     * 验证任务是否可以操作
     *
     * @param taskId 任务ID
     * @return bool
     */
    boolean isTaskAvailable(Long taskId);

    /**
     * 根据用户、任务ID列表获取主要审核人
     *
     * @param userId 用户ID
     * @param taskId 任务ID
     * @return 任务审核主责人信息
     */
    TaskReviewerInfoBO getReviewerInfo(Long userId, Long taskId);

    /**
     * 根据用户、任务ID列表获取主要审核人
     *
     * @param userId  用户ID
     * @param taskIds 任务ID集合
     * @return 任务审核主责人信息
     */
    Map<Long, TaskReviewerInfoBO> getReviewerInfo(Long userId, Collection<Long> taskIds);

    /**
     * 根据用户ID-任务ID组合获取主要审核人
     *
     * @param userIdAndTaskIdPairs 用户ID-任务ID组合
     * @return 任务审核主责人信息 userId-taskId-TaskReviewerInfoBO
     */
    Table<Long, Long, TaskReviewerInfoBO> getReviewerInfo(Collection<Pair<Long, Long>> userIdAndTaskIdPairs);


    /**
     * 获取项目 ID 范围
     * 总支撑管可以看到自己团队下的所有项目。
     * -项目经理、客户经理可以看自己负责的项目
     * -PMO可以看到全部
     *
     * @param filter          filter
     * @param functionsLeader 是否包含职能主管
     * @return {@link List }<{@link Long }>
     */
    List<Long> getProjectIdScope(Map<String, Object> filter, boolean functionsLeader);
}
