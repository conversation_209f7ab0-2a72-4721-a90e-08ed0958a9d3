package com.gok.pboot.pms.cost.controller;

import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.cost.entity.vo.SupplierInfoVO;
import com.gok.pboot.pms.cost.service.ISupplierService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * OA供应商台账 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @menu 供应商台账
 * @since 2025-02-18
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/supplier")
public class SupplierController {

    private final ISupplierService supplierService;

    /**
     * 获取所有供应商信息
     *
     * @return {@link ApiResult}<{@link List}<{@link SupplierInfoVO}>>
     */
    @GetMapping("/getAllSupplierInfo")
    public ApiResult<List<SupplierInfoVO>> getAllSupplierInfo() {
        return ApiResult.success(supplierService.getAllSupplierInfo());
    }

}