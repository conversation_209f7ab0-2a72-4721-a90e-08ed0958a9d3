package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum NoOrYesEnum {

    /**
     * 是
     */
    YES(0, "是"),
    NO(1, "否");

    /**
     * 值
     */
    private final Integer value;

    /**
     * 名称
     */
    private final String name;

    public static String getNameByVal(Integer value) {
        for (NoOrYesEnum noOrYesEnum : NoOrYesEnum.values()) {
            if (noOrYesEnum.value.equals(value)) {
                return noOrYesEnum.name;
            }
        }
        return "";
    }

}