package com.gok.pboot.pms.entity.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 项目回款追踪页面展示
 *
 * <AUTHOR>
 * @since 2023-09-27
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProjectPaymentVO {

    /**
     * id
     */
    @ExcelIgnore
    private Long id;

    /**
     * 单据编号
     */
    @ColumnWidth(20)
    @ExcelProperty("单据编号")
    private String documentNumber;

    /**
     * 收款公司code
     * {@link com.gok.pboot.pms.enumeration.AttributableSubjectEnum}
     */
    @ExcelIgnore
    private String paymentCompany;

    /**
     * 收款公司value
     * {@link com.gok.pboot.pms.enumeration.AttributableSubjectEnum}
     */
    @ColumnWidth(40)
    @ExcelProperty("收款公司")
    private String paymentCompanyTxt;

    /**
     * 客户名称
     */
    @ColumnWidth(25)
    @ExcelProperty("客户名称")
    private String customerName;

    /**
     * 企业名称
     */
    @ColumnWidth(25)
    @ExcelProperty("企业名称")
    private String enterpriseName;

    /**
     * 收款日期
     */
    @ColumnWidth(20)
    @ExcelProperty("收款日期")
    private String paymentDate;

    /**
     * 收款金额
     */
    @ColumnWidth(20)
    @ExcelProperty("收款金额")
    private String paymentAmount;

    /**
     * 收款平台
     */
    @ColumnWidth(20)
    @ExcelProperty("收款平台")
    private String paymentPlatform;

    /**
     * 项目回款
     * {@link com.gok.pboot.pms.enumeration.ProjectCollectionEnum}
     */
    @ExcelIgnore
    private Integer projectCollection;

    /**
     * 项目回款
     * {@link com.gok.pboot.pms.enumeration.ProjectCollectionEnum}
     */
    @ColumnWidth(20)
    @ExcelProperty("项目回款")
    private String projectCollectionTxt;

    /**
     * 凭证编号
     */
    @ExcelIgnore
    private String voucherNumber;

    /**
     * 归属区域id
     */
    @ExcelIgnore
    private String belongingAreaId;

    /**
     * 销售负责id
     */
    @ExcelIgnore
    private Long salesmanUserId;

    /**
     * 销售负责
     */
    @ColumnWidth(20)
    @ExcelProperty("销售负责")
    private String salesmanUserName;

    /**
     * 归属业务线code
     * {@link com.gok.pboot.pms.enumeration.BusinessLineEnum}
     */
    @ExcelIgnore
    private Integer businessLine;

    /**
     * 归属业务线value
     * {@link com.gok.pboot.pms.enumeration.BusinessLineEnum}
     */
    @ColumnWidth(20)
    @ExcelProperty("归属业务线")
    private String businessLineTxt;

    /**
     * 项目id
     */
    @ExcelIgnore
    private Long projectId;

    /**
     * 项目编码
     */
    @ColumnWidth(25)
    @ExcelProperty("项目编码")
    private String projectNumber;

    /**
     * 项目名称
     */
    @ColumnWidth(25)
    @ExcelProperty("项目名称")
    private String projectName;

    /**
     * 合同id
     */
    @ExcelIgnore
    private Long contractId;

    /**
     * 合同编码
     */
    @ColumnWidth(25)
    @ExcelProperty("合同编码")
    private String contractNumber;

    /**
     * 合同名称
     */
    @ColumnWidth(25)
    @ExcelProperty("合同名称")
    private String contractName;

    /**
     * 合同款项明细
     */
    @ColumnWidth(25)
    @ExcelProperty("合同款项明细")
    private String contractPayment;

    /**
     * 回款一级部门
     */
    @ColumnWidth(25)
    @ExcelProperty("回款一级部门")
    private String paymentDept;

    /**
     * 回款二级部门
     */
    @ColumnWidth(25)
    @ExcelProperty("回款二级部门")
    private String paymentSecondaryDept;

    /**
     * 业务板块
     */
    @ExcelIgnore
    private Integer businessBlock;

    /**
     * 业务板块
     */
    @ColumnWidth(20)
    @ExcelProperty("业务板块")
    private String businessBlockTxt;

    /**
     * 技术类型
     */
    @ExcelIgnore
    private Integer skillType;

    /**
     * 技术类型
     */
    @ColumnWidth(20)
    @ExcelProperty("技术类型")
    private String skillTypeTxt;

    /**
     * 归属区域
     */
    @ColumnWidth(20)
    @ExcelProperty("归属区域")
    private String belongingArea;

    /**
     * 认领状态code（0已认领，1未认领）
     * {@link com.gok.pboot.pms.enumeration.ClaimStatusEnum}
     */
    @ExcelIgnore
    private Integer claimStatus;

    /**
     * 认领状态value（0已认领，1未认领）
     * {@link com.gok.pboot.pms.enumeration.ClaimStatusEnum}
     */
    @ColumnWidth(20)
    @ExcelProperty("认领状态")
    private String claimStatusTxt;

    /**
     * 锁定状态code（0已锁定，1待锁定）
     * {@link com.gok.pboot.pms.enumeration.LockStatusEnum}
     */
    @ExcelIgnore
    private Integer lockStatus;

    /**
     * 锁定状态value（0已锁定，1待锁定）
     * {@link com.gok.pboot.pms.enumeration.LockStatusEnum}
     */
    @ColumnWidth(20)
    @ExcelProperty("锁定状态")
    private String lockStatusTxt;

    /**
     * 推送状态(0已推送,1待推送,2推送失败)
     * {@link com.gok.pboot.pms.enumeration.PushStatusEnum}
     */
    @ExcelIgnore
    private Integer pushStatus;

    /**
     * 推送状态(0已推送,1待推送,2推送失败)
     * {@link com.gok.pboot.pms.enumeration.PushStatusEnum}
     */
    @ColumnWidth(20)
    @ExcelProperty("推送状态")
    private String pushStatusTxt;

    /**
     * 银行账户
     */
    @ExcelIgnore
    private String bankAccount;

    /**
     * 认领日期
     */
    @ExcelIgnore
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    private LocalDateTime claimantDate;

    /**
     * 创建人名称
     */
    @ColumnWidth(20)
    @ExcelProperty("创建人")
    private String creatorName;

    /**
     * 认领人id
     */
    @ExcelIgnore
    private Long claimantId;

    /**
     * 认领人姓名
     */
    @ColumnWidth(20)
    @ExcelProperty("认领人")
    private String claimantName;

    /**
     * 备注
     */
    @ColumnWidth(60)
    @ExcelProperty("备注")
    private String paymentNote;

}
