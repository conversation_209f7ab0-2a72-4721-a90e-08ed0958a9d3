package com.gok.pboot.pms.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.entity.domain.ProjectRisk;
import com.gok.pboot.pms.entity.dto.ProjectResponsePlanDTO;
import com.gok.pboot.pms.entity.dto.ProjectRiskDTO;
import com.gok.pboot.pms.entity.vo.ProjectRiskFindPageVO;
import com.gok.pboot.pms.entity.vo.ProjectRiskSpecialVo;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 项目风险表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-12
 **/
public interface IProjectRiskService {

    /**
     * 项目风险分页查询
     *
     * @param pageRequest 分页请求实体
     * @param filter      查询参数
     * @return {@link Page}{@link ProjectRiskFindPageVO}
     */
    Page<ProjectRiskFindPageVO> findPage(PageRequest pageRequest, Map<String, Object> filter);

    ProjectRisk findById(Long id);

    /**
     * 新增项目风险
     *
     * @param request 新增请求实体
     * @return 新增项目风险主键Id
     */
    Long save(ProjectRiskDTO request);

    /**
     * 编辑项目风险
     *
     * @param request 新增请求实体
     * @return 编辑项目风险主键Id
     */
    Long update(ProjectRiskDTO request);

    /**
     * 批量逻辑删除
     *
     * @param list 要删除的id集合[]
     * @return {@link ApiResult}
     */
    ApiResult<String> batchDel(List<Long> list);

    /**
     * 查询责任人的风险数量
     * @return {@link ApiResult<Integer>}
     */
    ApiResult<Integer> findChargeRiskNum();

    /**
     * 分页查询负责人风险表
     * @param pageRequest 分页请求实体
     * @customParam pageNumber 页码
     * @customParam pageSize 页长
     * @param filter      查询参数
     * @customParam filter_L_chargeUserId 负责人id
     * @customParam filter_S_projectName 项目名称
     * @return {@link ApiResult<Page<ProjectRiskFindPageVO>>}
     */
    Page<ProjectRiskFindPageVO> findPageOfCharge(PageRequest pageRequest, Map<String, Object> filter);

    ///**
    // * 根据项目名称模糊查询负责人的风险项目
    // * @param pageRequest 分页请求实体
    // * @param filter      查询参数
    // * @return {@link ApiResult<Page<ProjectRiskFindPageVO>>}
    // */
    //Page<ProjectRiskFindPageVO> findByItemName(PageRequest pageRequest, Map<String, Object> filter);

    /**
     * 编辑风险应对计划字段
     * @param request
     * @return {@link ApiResult<Long>}
     */
    Long updateResponsePlan(ProjectResponsePlanDTO request);

    /**
     * 根据风险项id查询基本信息，可查出已删除风险项
     * @param id 风险id
     * @return {@link ProjectRiskSpecialVo}
     */
    ProjectRiskSpecialVo findRiskSpecialById(Long id);
}
