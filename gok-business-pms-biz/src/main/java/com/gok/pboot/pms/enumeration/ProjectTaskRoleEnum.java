package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;

/**
 * 任务角色
 *
 * <AUTHOR>
 * @date 2024/01/19
 */
@AllArgsConstructor
public enum ProjectTaskRoleEnum implements ValueEnum<Integer> {

    LEADER(0, "任务负责人"),
    MEMBER(1, "任务参与人")
    ;

    private final Integer value;

    private final String name;

    @Override
    public Integer getValue() {
        return value;
    }

    @Override
    public String getName() {
        return name;
    }

}
