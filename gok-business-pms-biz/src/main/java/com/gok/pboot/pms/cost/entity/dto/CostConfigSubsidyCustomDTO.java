package com.gok.pboot.pms.cost.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 成本配置补贴自定义 DTO
 *
 * <AUTHOR>
 * @date 2025/01/08
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class CostConfigSubsidyCustomDTO {

    /**
     * 补贴名称
     */
    @Length(max = 25, message = "补贴名称长度不能超过25")
    @NotBlank(message = "补贴名称不能为空")
    private String subsidyName;

    /**
     * 补贴金额
     */
    @DecimalMin(value = "0.00", message = "补贴金额必须大于或等于0")
    @DecimalMax(value = "9999.00", message = "补贴金额必须小于或等于9999")
    @NotNull(message = "补贴金额不能为空")
    private BigDecimal subsidyPrice;

    /**
     * 计算方式
     */
    @NotNull(message = "计算方式不能为空")
    private Integer calculationMethod;

}
