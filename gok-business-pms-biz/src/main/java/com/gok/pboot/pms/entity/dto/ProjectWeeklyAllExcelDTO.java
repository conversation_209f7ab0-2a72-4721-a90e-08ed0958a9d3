package com.gok.pboot.pms.entity.dto;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gok.pboot.pms.common.base.PageRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 多项目周报导入
 *
 * <AUTHOR>
 * @date 2023/9/25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectWeeklyAllExcelDTO extends PageRequest {

    /**
     * ID id
     */
    private Long id;
    /**
     * 项目id
     */
    private Long projectId;
    /**
     * '项目名称'
     */
    @ExcelProperty(
            value = {"项目名称"},
            index = 0
    )
    private String projectName;
    /**
     * '业务归属部门'
     */
    private String projectDepartment;
    /**
     * 汇报周期-开始
     */

    private LocalDate reportStart;
    /**
     * 汇报周期-结束
     */
    private LocalDate reportEnd;

    @ExcelProperty(
            value = {"汇报周期"},
            index = 1
    )
    /**
     * 汇报周期
     */
    private String reportStartEnd;
    /**
     * 汇报人id
     */
    private Long reportUserId;
    /**
     * 汇报人
     */
    @ExcelProperty(
            value = {"汇报人"},
            index = 2
    )
    private String reportUser;
    /**
     * 本周进展情况
     */
    @ExcelProperty(
            value = {"本周进展情况"},
            index = 3
    )
    @Length(max = 500, message = "本周进展情况长度不能超过500")
    private String currentWorkProgress;
    /**
     * 当前项目进度
     */
    @ExcelProperty(
            value = {"当前项目进度(%)"},
            index = 4
    )
    @Length(max = 3, message = "当前项目进度长度不能超过3")
    private String currentProgress;
    /**
     * 当期新增工时（人天）
     */
    @ExcelProperty(
            value = {"当期新增工时（人天）"},
            index = 5
    )
    private BigDecimal currentHours;
    /**
     * 累计工时（人天）
     */
    @ExcelProperty(
            value = {"累计工时（人天）"},
            index = 6
    )
    private BigDecimal totalHours;
    /**
     * 下周工作计划
     */
    @ExcelProperty(
            value = {"下周工作计划"},
            index = 7
    )
    @Length(max = 500, message = "下周工作计划长度不能超过500")
    private String nextWorkPlan;
    /**
     * 需配合支撑事项
     */
    @Length(max = 500, message = "需配合支撑事项长度不能超过500")
    @ExcelProperty(
            value = {"需配合支撑事项"},
            index = 8
    )
    private String needSupportItem;
    /**
     * 文件id集合，用逗号分割
     */
    private String docIds;

    /**
     * 文件名集合
     */
    private String docNames;

    /**
     * 周报风险
     */
    @ExcelProperty(
            value = {"周报风险"},
            index = 9
    )
    @Length(max = 500, message = "周报风险长度不能超过500")
    private String weeklyRisk;

    public void validate() {
        if (StrUtil.isNotBlank(reportStartEnd)) {
            Assert.isTrue(reportStartEnd.contains("~"), "项目周期格式不符合！");
            String regex = "^(\\d{4})-(\\d{2})-(\\d{2})";
            String[] split = reportStartEnd.split("~");
            Pattern pattern = Pattern.compile(regex);
            for (String s : split) {
                Matcher matcher = pattern.matcher(s);
                Assert.isTrue(matcher.matches(), "项目周期格式不符合！");
            }
        } else {
            Assert.isTrue(false, "项目周期不能为空！");
        }

        if (StrUtil.isNotBlank(currentProgress)) {
            Assert.isTrue(StrUtil.isNumeric(currentProgress), "当前项目进度必须为数字!");
        }

        if(StrUtil.isBlank(projectName)){
            Assert.isTrue(false,"项目名称不能为空！");
        }
    }
}
