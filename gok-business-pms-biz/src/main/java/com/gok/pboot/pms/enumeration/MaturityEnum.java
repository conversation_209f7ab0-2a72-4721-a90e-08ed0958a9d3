package com.gok.pboot.pms.enumeration;

/**
 * 项目-成熟度枚举
 *
 * <AUTHOR>
 */
public enum MaturityEnum implements ValueEnum<Integer> {
    /**
     * 成熟
     */
    maturity(0, "成熟"),
    /**
     * 非成熟
     */
    non_Maturity(1, "非成熟");

    //值
    private Integer  value;
    //名称
    private String name;

    MaturityEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
    /**
     * 获取值
     *
     * @return Integer
     */
    @Override
    public Integer getValue() {
        return value;
    }

    /**
     * 获取名称
     *
     * @return String
     */
    @Override
    public String getName() {
        return name;
    }

    public static String getNameByVal(Integer value) {
        for (MaturityEnum maturityEnum : MaturityEnum.values()) {
            if (maturityEnum.value.equals(value)) {
                return maturityEnum.name;
            }
        }
        return "";
    }
    public static Integer getValByName(String name) {
        for (MaturityEnum maturityEnum : MaturityEnum.values()) {
            if (maturityEnum.getName().equals(name)) {
                return maturityEnum.getValue();
            }
        }
        return null;
    }

    public static MaturityEnum getMaturityEnum(Integer value) {
        for (MaturityEnum maturityEnum : MaturityEnum.values()) {
            if (maturityEnum.value.equals(value)) {
                return maturityEnum;
            }
        }
        return null;
    }
}
