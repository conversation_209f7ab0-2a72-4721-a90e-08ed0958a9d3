package com.gok.pboot.pms.cost.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.cost.entity.domain.CostSalaryDetails;
import com.gok.pboot.pms.cost.entity.dto.CostSalaryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * 成本明细表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025/05/08
 */
@Mapper
public interface CostSalaryDetailsMapper extends BaseMapper<CostSalaryDetails> {
    /**
     * 获取列表
     *
     * @param salaryDTOList 薪资 dto list
     * @return {@link List }<{@link CostSalaryDetails }>
     */
    List<CostSalaryDetails> getList(@Param("list") Collection<CostSalaryDTO> salaryDTOList);
} 