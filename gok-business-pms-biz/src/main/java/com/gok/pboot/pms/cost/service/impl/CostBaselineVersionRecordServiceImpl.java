package com.gok.pboot.pms.cost.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.cost.entity.domain.CostBaselineVersionRecord;
import com.gok.pboot.pms.cost.entity.domain.CostCashPlanVersion;
import com.gok.pboot.pms.cost.entity.domain.CostManageVersion;
import com.gok.pboot.pms.cost.entity.vo.CostBaselineVersionRecordVO;
import com.gok.pboot.pms.cost.enums.CostManageVersionEnum;
import com.gok.pboot.pms.cost.mapper.CostBaselineVersionRecordMapper;
import com.gok.pboot.pms.cost.mapper.CostCashPlanVersionMapper;
import com.gok.pboot.pms.cost.mapper.CostManageVersionMapper;
import com.gok.pboot.pms.cost.service.ICostBaselineVersionRecordService;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.gok.pboot.pms.cost.service.impl.CostConfigVersionServiceImpl.V;

/**
 * 成本基线版本记录服务实施
 *
 * <AUTHOR>
 * @date 2025/01/15
 */
@Service
@AllArgsConstructor
public class CostBaselineVersionRecordServiceImpl extends ServiceImpl<CostBaselineVersionRecordMapper, CostBaselineVersionRecord>
        implements ICostBaselineVersionRecordService {

    private final CostCashPlanVersionMapper costCashPlanVersionMapper;
    private final CostManageVersionMapper costManageVersionMapper;

    /**
     * 根据项目生成基线版本名称
     *
     * @param projectId 项目 ID
     * @return {@link String }
     */
    public String getBaseLineVersionName(Long projectId) {
        // 获取所有版本值
        Set<Integer> versionNumSet = lambdaQuery().eq(CostBaselineVersionRecord::getProjectId, projectId).list().stream()
                .filter(item -> item.getVersionName().startsWith(V))
                .map(item -> Integer.parseInt(item.getVersionName().substring(NumberUtils.INTEGER_ONE)))
                .collect(Collectors.toSet());
        // 获取最大版本值
        int maxVersionNum = versionNumSet.stream()
                .max(Integer::compare)
                .orElse(NumberUtils.INTEGER_ZERO) + NumberUtils.INTEGER_ONE;
        return V + maxVersionNum;
    }

    /**
     * 获取版本记录分页列表
     *
     * @param projectId   项目 ID
     * @param pageRequest 页面请求
     * @return {@link Page }<{@link CostBaselineVersionRecordVO }>
     */
    @Override
    public Page<CostBaselineVersionRecordVO> getVersionRecordPage(Long projectId, PageRequest pageRequest) {
        Page<CostBaselineVersionRecordVO> page = Page.of(pageRequest.getPageNumber(), pageRequest.getPageSize());
        Page<CostBaselineVersionRecordVO> versionRecordVoPage = baseMapper.getVersionRecordPage(projectId, page);
        if (CollUtil.isEmpty(versionRecordVoPage.getRecords())) {
            return page;
        }
        
        // 获取所有版本记录,按创建时间倒序排序
        List<CostBaselineVersionRecord> allRecords = lambdaQuery()
            .eq(CostBaselineVersionRecord::getProjectId, projectId)
            .orderByDesc(CostBaselineVersionRecord::getCtime)
            .list();
            
        // 遍历当前页的记录,计算更新标识
        for (CostBaselineVersionRecordVO record : versionRecordVoPage.getRecords()) {
            // 找到当前记录在完整列表中的索引
            int currentIndex = -1;
            for (int i = 0; i < allRecords.size(); i++) {
                if (allRecords.get(i).getVersionName().equals(record.getVersionName())) {
                    currentIndex = i;
                    break;
                }
            }
            
            // 如果不是最早版本,则与上一次创建的版本比较
            if (currentIndex < allRecords.size() - 1) {
                CostBaselineVersionRecord prevRecord = allRecords.get(currentIndex + 1);
                int updateFlag = 0;
                // 比较报价与毛利测算版本
                if (!Objects.equals(record.getQuotationVersionName(), prevRecord.getQuotationVersionName())) {
                    updateFlag = 1;
                }
                // 比较目标版本
                else if (!Objects.equals(record.getTargetVersionName(), prevRecord.getTargetVersionName())) {
                    updateFlag = 2;
                }
                // 比较成本版本
                else if (!Objects.equals(record.getCostVersionName(), prevRecord.getCostVersionName())) {
                    updateFlag = 3;
                }
                // 比较现金流版本
                else if (!Objects.equals(record.getCashPlanVersionName(), prevRecord.getCashPlanVersionName())) {
                    updateFlag = 4;
                }
                record.setUpdateFlag(updateFlag);
            } else {
                // 最早版本,无更新
                record.setUpdateFlag(0);
            }
        }
        return versionRecordVoPage;
    }


    /**
     * 同步基准版本记录
     *
     * @param projectId   项目 ID
     * @param requestName 请求名称
     * @param budgetType  预算类型
     */
    @Override
    public void syncCostBaselineVersionRecord(Long projectId, String requestName, Integer budgetType) {
        CostBaselineVersionRecord baselineVersionRecord = new CostBaselineVersionRecord();

        // 目标版本
        CostManageVersion targetVersion = costManageVersionMapper.getLatestCostManageVersion(projectId, CostManageVersionEnum.MBGL.getValue(), null);
        if (Objects.nonNull(targetVersion)) {
            baselineVersionRecord.setTargetVersionId(targetVersion.getId())
                    .setTargetVersionName(targetVersion.getVersionName());
        }
        // 成本版本
        CostManageVersion costVersion = costManageVersionMapper.getLatestCostManageVersion(projectId, CostManageVersionEnum.CBGL.getValue(), budgetType);
        if (Objects.nonNull(costVersion)) {
            baselineVersionRecord.setCostVersionId(costVersion.getId())
                    .setCostVersionName(costVersion.getVersionName());
        }
        // 如果关联流程名称为空，则生成基线版本名称，否则设置为关联流程名称
        baselineVersionRecord.setProjectId(projectId)
                .setVersionName(StrUtil.isBlank(requestName) ? getBaseLineVersionName(projectId) : requestName);
        // 获取对应的项目现金流版本
        CostCashPlanVersion costCashPlanVersion = costCashPlanVersionMapper.getCurrentVersion(projectId);
        if (null != costCashPlanVersion) {
            baselineVersionRecord.setCashPlanVersionId(costCashPlanVersion.getId());
            baselineVersionRecord.setCashPlanVersionName(costCashPlanVersion.getVersionNo());
        }
        baseMapper.insert(BaseBuildEntityUtil.buildInsert(baselineVersionRecord));
    }
}




