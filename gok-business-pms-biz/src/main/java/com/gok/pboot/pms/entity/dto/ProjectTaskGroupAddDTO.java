package com.gok.pboot.pms.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 添加任务分组dto
 *
 * <AUTHOR>
 * @date 2023/8/23
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProjectTaskGroupAddDTO {

    /**
     * 项目ID
     */
    @NotNull(message = "项目id不能为空")
    private Long projectId;

    /**
     * 分组名称
     */
    @NotBlank(message = "分组名称不能为空")
    private String title;

    /**
     * 在制品数量（最大任务数限制）
     */
    private Integer capacity;

}
