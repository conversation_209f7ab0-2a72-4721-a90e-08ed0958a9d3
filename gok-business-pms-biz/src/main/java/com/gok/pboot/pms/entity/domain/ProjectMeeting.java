package com.gok.pboot.pms.entity.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.common.base.BeanEntity;
import com.gok.pboot.pms.entity.dto.ProjectMeetingDTO;
import com.gok.pboot.pms.entity.dto.ProjectMeetingImportExcelDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * 项目会议纪要表
 *
 * <AUTHOR>
 * @since 2023-07-13
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(ProjectMeeting.ALIAS)
public class ProjectMeeting extends BeanEntity<Long> {

    public static final String ALIAS = "project_meeting";

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 会议名称
     */
    private String name;

    /**
     * 召集人id
     */
    private Long convenerId;

    /**
     * 召集人
     */
    private String convener;

    /**
     * 会议日期
     * 格式为：yyyy-MM-dd
     */
    private LocalDate meetingDate;

    /**
     * 开始时间
     */
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private String startTime;

    /**
     * 结束时间
     */
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private String endTime;

    /**
     * 记录人id
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long recorderId;

    /**
     * 记录人
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String recorder;

    /**
     * 会议地点
     */
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private String place;

    /**
     * 参会人员
     */
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private String member;

    /**
     * 会议目标
     */
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private String objective;

    /**
     * 会议过程
     */
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private String process;

    /**
     * 会议决议
     */
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private String resolution;

    /**
     * 待办事项
     */
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private String backlog;

    /**
     * 文件id集合
     */
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private String docIds;

    /**
     * 附件名
     */
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private String docNames;

    /**
     * 请求对象赋值为实体类
     *
     * @param request 会议纪要新增/编辑请求实体
     * @return 会议纪要实体类
     */
    public static ProjectMeeting buildSave(ProjectMeetingDTO request) {
        ProjectMeeting entity = new ProjectMeeting();
        BeanUtil.copyProperties(request, entity);
        if (!Optional.ofNullable(request.getId()).isPresent()) {
            BaseBuildEntityUtil.buildInsert(entity);
        }
        return entity;
    }

    /**
     * Excel导入数据封装为实体类
     *
     * @param request Excel导入数据实体类
     * @return 会议纪要实体类
     */
    public static ProjectMeeting buildSave(ProjectMeetingImportExcelDTO request) {
        ProjectMeeting entity = new ProjectMeeting();
        BeanUtil.copyProperties(request, entity);
        if (StrUtil.isNotBlank(request.getTime())) {
            List<String> timeArray = StrUtil.split(request.getTime(), StrUtil.DASHED);
            entity.setStartTime(timeArray.get(0));
            entity.setEndTime(timeArray.get(1));
        }
        BaseBuildEntityUtil.buildInsert(entity);
        return entity;
    }

}
