package com.gok.pboot.pms.enumeration;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum IncomeSegmentEnum {

    SERVICE_REVENUE(3, "服务收入"),
    TRAINING_CERTIFICATION(7, "培训认证"),
    INDUSTRIAL_SERVICES(0, "产业服务"),
    TALENT_SERVICES(6, "人才服务"),
    EDUCATION_B_SIDE(4, "教育B端"),
    EDUCATION_C_SIDE(5, "教育C端"),
    EDUCATIONAL_SERVICES(1, "教育服务"),
    PRODUCT_REVENUE(2, "产品收入"),
    ;

    /**
     * 收入板块编码
     */
    private final Integer code;

    /**
     * 收入板块名称
     */
    private final String name;

    public static String getNameByVal(Integer value) {
        for (IncomeSegmentEnum incomeSegmentEnum : IncomeSegmentEnum.values()) {
            if (incomeSegmentEnum.code.equals(value)) {
                return incomeSegmentEnum.name;
            }
        }
        return StrUtil.EMPTY;
    }

}