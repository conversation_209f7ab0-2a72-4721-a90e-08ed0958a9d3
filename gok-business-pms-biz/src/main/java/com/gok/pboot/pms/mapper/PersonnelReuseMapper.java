package com.gok.pboot.pms.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.data.datascope.BusinessDataScope;
import com.gok.pboot.pms.common.base.MapperHandler;
import com.gok.pboot.pms.entity.PersonnelReuse;
import com.gok.pboot.pms.entity.bo.DailyReviewReuseAndDeliveryViewBO;
import com.gok.pboot.pms.entity.bo.DailyReviewReuseAndDeliveryViewTotalBO;
import com.gok.pboot.pms.entity.dto.ChangeApprovalStatusDTO;
import com.gok.pboot.pms.entity.dto.DailyPaperCommonDTO;
import com.gok.pboot.pms.entity.dto.PersonnelReuseFindPageDTO;
import com.gok.pboot.pms.entity.dto.PersonnelReuseUpdateDTO;
import com.gok.pboot.pms.entity.vo.PersonnelReuseFindPageVO;
import com.gok.pboot.pms.entity.vo.PersonnelReusePageVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 人才复用 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-29
 */
@Mapper
public interface PersonnelReuseMapper extends MapperHandler<PersonnelReuse> {


    /**
     * 逻辑删除
     *
     * @param id 唯一标识
     * @return
     */
    int deleteByLogic(@Param("id") Long id);

    /**
     * 批量插入
     *
     * @param poList 实体集合
     */
    void batchSave(@Param("poList") List<PersonnelReuse> poList);


    /**
     * 批量修改
     *
     * @param list 实体集合
     */
    void batchUpdate(@Param("list") List<PersonnelReuse> list);

    /**
     * 批量逻辑删除
     *
     * @param list id集合
     */
    void batchDel(@Param("list") List<Long> list);

    /**
     * 批量修改不为空字段
     *
     * @param list id集合
     */
    void updateBatch(@Param("list") List<Long> list);

    @BusinessDataScope
    List<Long> personnelReuseIdPage(@Param("personnelReuseFindPageDTO") PersonnelReuseFindPageDTO personnelReuseFindPageDTO,
                                    @Param("approvalStatus") Integer approvalStatus);

    Page<PersonnelReuseFindPageVO> personnelReuseFindPage(Page<PersonnelReuseFindPageVO> page,
                                                          @Param("personnelReuseFindPageDTO") PersonnelReuseFindPageDTO personnelReuseFindPageDTO,
                                                          @Param("approvalStatus") Integer approvalStatus);

    int updateById(@Param("personnelReuseUpdateDTO") PersonnelReuseUpdateDTO personnelReuseUpdateDTO);

    /**
     * 根据id 查询复用工时信息
     *
     * @param id id
     * @return 复用工时信息
     */
    DailyPaperCommonDTO selectDailyPaperById(Long id);

    /**
     * 修改人才复用的工时审批记录
     *
     * @param changeApprovalStatusDTO 审核数据
     */
    void updateApprovalStatusById(@Param("param") ChangeApprovalStatusDTO changeApprovalStatusDTO);

    /**
     * 按 ID 更新审批状态
     *
     * @param approvalStatus 审批状态
     * @param auditName      审计名称
     * @param auditId        审核 ID
     * @param updateTime     updateTime
     * @param reuseIds       重用 ID
     */
    void updateApprovalStatusByIds(@Param("approvalStatus") Integer approvalStatus, @Param("auditName") String auditName,
                                   @Param("auditId") Long auditId, @Param("mTime") LocalDateTime updateTime,
                                   @Param("reuseIds") List<Long> reuseIds);

    /**
     * 按查询条件 分页统计复用、交付人员日报详细信息
     *
     * @param filter 查询条件
     * @param page   分页参数
     * @return 分页数据
     */
    Page<DailyReviewReuseAndDeliveryViewBO> findReuseAndDeliveryView(
            Page<DailyReviewReuseAndDeliveryViewBO> page,
            @Param("filter") Map<String, Object> filter
    );

    /**
     * 按查询条件 复用、交付人员日报详细信息
     *
     * @param filter 滤波器
     * @return {@link List }<{@link DailyReviewReuseAndDeliveryViewBO }>
     */
    List<DailyReviewReuseAndDeliveryViewBO>findReuseAndDeliveryView(@Param("filter") Map<String, Object> filter);

    /**
     * 查询复用交付项目审核统计数据
     *
     * @param filter 查询条件
     * @return 复用交付项目审核统计数据
     */
    DailyReviewReuseAndDeliveryViewTotalBO findReuseAndDeliveryViewTotal(@Param("filter") Map<String, Object> filter);

    /**
     * 人才复用导入列表
     *
     * @param page
     * @param filter
     * @return
     */
    Page<PersonnelReusePageVO> findPage(@Param("page") Page<PersonnelReusePageVO> page, @Param("filter") Map<String, Object> filter);
}
