package com.gok.pboot.pms.cost.entity.dto;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 类别管理表DTO
 *
 * <AUTHOR>
 * @date 2024/03/26
 */
@Data
public class CostTaskCategoryManagementDTO {

    /**
     * 主键ID
     */
    private Long id;


    /**
     * 工单类别名称
     */
    @NotBlank(message = "工单类别名称不能为空")
    @Length(max = 25, message = "工单类别名称不能超过25个字符")
    private String taskCategoryName;

    /**
     * 工单类型(0=售前支撑, 1=售后交付)
     * @see com.gok.pboot.pms.enumeration.ProjectTaskKindEnum
     */
    @NotNull(message = "工单类型不能为空")
    private Integer taskType;

    /**
     * 是否可自创建(0=否, 1=是)
     */
    @NotNull(message = "是否可自创建不能为空")
    private Integer canSelfCreate;
} 