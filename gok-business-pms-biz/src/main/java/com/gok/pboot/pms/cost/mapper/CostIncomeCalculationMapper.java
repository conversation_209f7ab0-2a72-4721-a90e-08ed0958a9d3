package com.gok.pboot.pms.cost.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.cost.entity.domain.CostIncomeCalculation;
import com.gok.pboot.pms.cost.entity.dto.CostIncomeCalculationDTO;
import com.gok.pboot.pms.cost.entity.vo.CostIncomeCalculationVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2025/02/18
 **/
public interface CostIncomeCalculationMapper extends BaseMapper<CostIncomeCalculation> {

    /**
     * 查询列表
     *
     * @param query 查询请求
     * @return
     */
    List<CostIncomeCalculation> findList(@Param("query") CostIncomeCalculationDTO query);

    /**
     * 根据明细批量确认汇总数据
     *
     * @param statusType 状态类型 0-确认状态 1-结算状态
     * @return
     */
    int batchUpdateStatusByDetails(@Param("calculationConfirmMap") Map<Long, Integer> calculationConfirmMap,
                                   @Param("statusType") Integer statusType,
                                   @Param("modifierId") Long modifierId,
                                   @Param("modifier") String modifier);

    /**
     * 批量更新
     *
     * @param calculationList
     */
    void batchUpdate(@Param("updateEntries") List<CostIncomeCalculation> calculationList);

    /**
     * 获取未确认项目
     *
     * @return
     */
    List<CostIncomeCalculationVO> findUnconfirmedProject();

}
