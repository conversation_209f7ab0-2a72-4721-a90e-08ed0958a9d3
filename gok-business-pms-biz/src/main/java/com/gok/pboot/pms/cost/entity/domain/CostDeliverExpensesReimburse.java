package com.gok.pboot.pms.cost.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
    * 交付管理-费用报销台账
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("cost_deliver_expenses_reimburse")
public class CostDeliverExpensesReimburse extends BeanEntity<Long> {

    private static final long serialVersionUID = 1L;

    /**
    * 项目id
    */
    private Long projectId;

    /**
    * 项目名称
    */
    private String projectName;

    /**
    * 收款人姓名
    */
    private String recipientUserName;

    /**
     * 项目预算id
     */
    private Long budgetId;

    /**
     * 税率OA字典ID
     */
    private Integer taxRate;

    /**
    * 科目名称id
    */
    private Long accountId;

    /**
    * 科目名称
    */
    private String accountName;

    /**
    * 成本科目类别id
    */
    private Long accountCategoryId;

    /**
    * 成本科目类别名称
    */
    private String accountCategoryName;

    /**
     * 费用项id
     */
    private Long accountItemId;

    /**
     * 费用项名称
     */
    private String accountItemName;

    /**
    * 报销金额
    */
    private BigDecimal reimburseMoney;

    /**
    * 报销说明
    */
    private String expensesDesc;

    /**
    * 关联流程ID
    */
    private Long requestId;

    /**
    * 关联流程名称
    */
    private String requestName;

    /**
    * 申请人id
    */
    private Long applicantId;

    /**
    * 申请人姓名
    */
    private String applicantName;

    /**
    * 申请时间
    */
    private String applicantTime;

    /**
    * 归档时间
    */
    private String filingTime;

    /**
     * 流程状态
     */
    private Integer requestStatus;

    /**
     * 流程类型0项目费用报销 1采购报销 2商机费用报销
     */
    private Integer requestType;

}