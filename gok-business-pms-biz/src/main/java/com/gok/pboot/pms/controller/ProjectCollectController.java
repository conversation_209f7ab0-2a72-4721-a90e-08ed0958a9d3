package com.gok.pboot.pms.controller;


import com.gok.pboot.pms.Util.CollectionUtils;
import com.gok.pboot.pms.common.base.R;
import com.gok.pboot.pms.entity.ProjectCollect;
import com.gok.pboot.pms.service.IProjectCollectService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @description 项目收藏 前端控制器
 * @menu 项目收藏
 * @since 2023-04-19
 */
@Slf4j
@RestController
@RequestMapping("projectCollect")
@RequiredArgsConstructor
public class ProjectCollectController {

    private final IProjectCollectService service;

    /**
     * 添加/编辑
     *
     * @param entity 实体
     * @return {@link R<ProjectCollect>}
     */
    @PostMapping("/save")
    public R<String> save(@RequestBody ProjectCollect entity) {
        return service.saveOrUpdate(entity);
    }


    /**
     * 编辑
     *
     * @param id id
     * @return {@link R }<{@link ProjectCollect }>
     */
    @GetMapping("/{id}")
    public R<ProjectCollect> id(@PathVariable("id") Long id) {
        ProjectCollect entity = service.getById(id);
        return R.ok(entity);
    }

    /**
     * 逻辑删除
     *
     * @param ids 多个id用逗号隔开
     * @return {@link R<ProjectCollect>}
     */
    @PostMapping("/delete")
    public R<String> del(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return R.failed("请选择删除记录!");
        }

        return service.batchDel(ids);
    }
}
