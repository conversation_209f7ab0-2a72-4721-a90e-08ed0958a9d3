package com.gok.pboot.pms.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 项目预估成本表（Oa项目预算台账）
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-17 11:11:29
 */
@Data
@TableName("project_estimated_cost")
public class ProjectEstimatedCost extends BaseEntity<Long> {

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 成本费用项
     */
    private Integer costExpenseItems;

    /**
     * 成本费用项
     */
    private String costExpenseItemsValue;

    /**
     * 成本费用项科目代码
     */
    private String subjectCode;

    /**
     * 成本费用项科目名称
     */
    private String subjectName;

    /**
     * 预算金额
     */
    private BigDecimal budgetAmount;

    /**
     * 预算金额不含税
     */
    private BigDecimal budgetAmountNoTax;

    /**
     * 税率
     */
    private Integer taxRate;

    /**
     * 税率文本
     */
    private String taxRateValue;

    /**
     * 预算人天
     */
    private Integer budgetManDays;

    /**
     * 成本费用说明
     */
    private String costExplanation;

    /**
     * 生成时间
     */
    private LocalDate createDate;

}
