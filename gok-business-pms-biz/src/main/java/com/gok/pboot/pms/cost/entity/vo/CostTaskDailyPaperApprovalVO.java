package com.gok.pboot.pms.cost.entity.vo;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 工单工时审核查询结果
 *
 * <AUTHOR>
 * @date 2024/04/16
 */
@Data
@Builder
@ApiModel("工单工时审核查询结果")
public class CostTaskDailyPaperApprovalVO {

    /**
     * 待审核数
     */
    private Integer pendingCount;

    /**
     * 总工时
     */
    private BigDecimal totalHours;

    /**
     * 总正常工时
     */
    private BigDecimal totalNormalHours;

    /**
     * 总加班工时
     */
    private BigDecimal totalOvertimeHours;

    /**
     * 总工作日加班
     */
    private BigDecimal totalWorkOvertimeHours;

    /**
     * 总休息日加班
     */
    private BigDecimal totalRestOvertimeHours;

    /**
     * 总节假日加班
     */
    private BigDecimal totalHolidayOvertimeHours;

    /**
     * 审核明细数据列表
     */
    private Page<CostTaskDailyPaperApprovalDetailVO> detailsPage;
} 