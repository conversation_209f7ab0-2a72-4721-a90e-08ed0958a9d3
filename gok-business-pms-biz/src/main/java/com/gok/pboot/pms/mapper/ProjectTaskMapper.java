package com.gok.pboot.pms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.common.join.TaskInDailyPaperEntry;
import com.gok.pboot.pms.entity.domain.ProjectStakeholderMember;
import com.gok.pboot.pms.entity.domain.ProjectTask;
import com.gok.pboot.pms.entity.vo.TaskInDailyPaperEntryListVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;


@Mapper
public interface ProjectTaskMapper extends BaseMapper<ProjectTask> {

    List<ProjectTask> findList(@Param("filter") Map<String, Object> filter);

    List<ProjectTask> findChargeList(@Param("filter") Map<String, Object> filter);

    ProjectTask findOneById(@Param("id")Long id);

    void save(ProjectTask projectTask);

    void update(ProjectTask update);

    void delete(Long id);

    /**
     * ~ 根据用户ID查询目标用户可用的任务 ~
     * @param userId 用户ID
     * @param projectId 项目ID
     * @return java.util.List<com.gok.pboot.pms.common.join.TaskInDailyPaperEntry>
     * <AUTHOR>
     * @date 2023/8/22 11:28
     */
    List<TaskInDailyPaperEntry> findByUserIdAndProjectIdForEntry(
            @Param("userId") Long userId,
            @Param("projectId") Long projectId
    );

    /**
     * 批量根据用户ID查询目标用户可用的任务
     *
     * @param list
     * @return {@link List}<{@link TaskInDailyPaperEntryListVo}>
     */
    List<TaskInDailyPaperEntryListVo> findByUserIdAndProjectIdForEntryVos(
            @Param("userId") Long userId,
            @Param("list") List<Long> list
    );

    /**
     * 批量根据用户ID查询目标用户可用的任务
     *
     * @param list
     * @return {@link List}<{@link TaskInDailyPaperEntryListVo}>
     */
    List<TaskInDailyPaperEntryListVo> findByUserIdAndProjectIdForEntryVos2(
            @Param("userId") Long userId,
            @Param("list") List<Long> list
    );

    int getChildCount(Long id);
    
    /** 
     * 获取改任务id的所有一级子任务id列表
     * @param parentId 父级id
     * @return {@link List<Long>} 子任务id列表
     */
    List<String> getChildIdList(@Param("parentId")Long parentId);

    String findTitleById(Long id);

    /**
     * 根据分组id查询该分组下的任务数量
     * @param groupId 分组id
     * @return {@link Integer}
     */
    Integer getCountByGroupId(@Param("groupId") Long groupId);

    /**
     * 将groupId初始化
     * @param groupId 分组id
     */
    void groupIdInit(@Param("groupId") Long groupId);

    /**
     * 更新groupId的值
     * @param taskId 任务id
     * @param groupId 分组id
     */
    void groupIdUpdate(@Param("taskId") Long taskId, @Param("groupId") Long groupId);

    /**
     * 分页查询根节点
     * @param page 分页参数
     * @param filter 查询参数
     */
    Page<ProjectTask> findRootPage(Page<ProjectTask> page, @Param("filter") Map<String, Object> filter);

    /**
     * 根据负责人id查询任务数
     * @param id 负责人id
     * @return {@link Integer}
     */
    Integer findChargeTaskNum(@Param("managerUserId") Long id);

    /**
     * 回滚实际结束时间
     * @param id 主键id
     */
    void rollbackEndTime(Long id);

    /**
     * 获取所有未开始状态的任务
     */
    List<ProjectTask> getAllNotStartTask();

    List<ProjectTask> findAll();

    List<ProjectStakeholderMember> findToSyncMember();
}
