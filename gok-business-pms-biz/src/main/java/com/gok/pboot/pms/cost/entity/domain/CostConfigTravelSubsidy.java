package com.gok.pboot.pms.cost.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 差旅补贴标准配置
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("cost_config_travel_subsidy")
public class CostConfigTravelSubsidy extends BeanEntity<Long> {

    private static final long serialVersionUID = 1L;

    /**
     * 版本ID
     */
    private Long versionId;

    /**
     * 人员数量（0=单人，1=多人）
     */
    private Integer personNum;

    /**
     * 出差天数（0=不限制，1=小于30天，2=大于等于30天）
     */
    private Integer awayDay;

    /**
     * 是否自行解决住宿（0=否，1=是）
     */
    private Integer stayOwn;

    /**
     * 补贴标准
     */
    private BigDecimal subsidyPrice;

}
