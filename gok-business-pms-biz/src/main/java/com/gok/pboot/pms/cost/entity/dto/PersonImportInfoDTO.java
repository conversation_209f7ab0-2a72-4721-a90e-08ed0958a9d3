package com.gok.pboot.pms.cost.entity.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025-02-19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PersonImportInfoDTO {

    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名", index = 0)
    private String name;

    /**
     * 工号
     */
    @ExcelProperty(value = "工号", index = 1)
    private String workCode;

    /**
     * 人员属性名称
     */
    @ExcelProperty(value = "人员属性", index = 2)
    private String personnelAttributeName;

    /**
     * 岗位名称
     */
    @ExcelProperty(value = "岗位", index = 3)
    private String jobName;

    /**
     * 职级名称
     */
    @ExcelProperty(value = "职级", index = 4)
    private String positionName;

    /**
     * 入场时间
     */
    @ExcelProperty(value = "入场时间", index = 5)
    private String entryTime;

    /**
     * 驻场时长(天)
     */
    @ExcelProperty(value = "驻场时长(天)", index = 6)
    private String durationDays;

    /**
     * 驻场地点
     */
    @ExcelProperty(value = "驻场地点", index = 7)
    private String domicile;

    /**
     * 报价方式名称
     */
    @ExcelProperty(value = "报价方式", index = 8)
    private String quotationTypeName;

    /**
     * 含税报价
     */
    @ExcelProperty(value = "含税报价", index = 9)
    private String quotationIncludeTax;

    /**
     * 报价税率
     */
    @ExcelProperty(value = "报价税率", index = 10)
    private String quotedRate;

    /**
     * 固定费率
     */
    @ExcelProperty(value = "固定费率", index = 11)
    private String flatRate;

    /**
     * 归属月份
     */
    @ExcelProperty(value = "归属月份", index = 12)
    private String belongMonth;

}