package com.gok.pboot.pms.entity.dto;

import com.gok.pboot.pms.common.validate.constraint.ManHour;
import lombok.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 添加/编辑 日报请求
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/8/23 15:29
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class DailyPaperAddOrUpdateDTO {

    /**
     * 【编辑时】工时日报ID
     */
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 日报条目列表
     */
    @Valid
    private List<DailyPaperEntry> entries;

    /**
     * 明日计划条目列表
     */
    @Valid
    private List<TomorrowPlanPaperEntry> tomorrowPlanPaperEntries;

    /**
     * 填报日期
     */
    @NotNull(message = "填报日期不能为空")
    private LocalDate submissionDate;

    @NotNull(message = "请传入submit参数（true | false）")
    private Boolean submit;

    @Setter
    @Getter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DailyPaperEntry {

        /**
         * 日报条目ID
         */
        private Long id;

        /**
         * 项目ID
         */
        @NotNull(message = "项目ID不能为空")
        private Long projectId;

        /**
         * 任务ID
         */
        @NotNull(message = "任务ID不能为空")
        private Long taskId;

        /**
         * 工时类型
         */
        private Integer workType;

        /**
         * 正常工时
         */
        @NotNull(message = "正常工时不能为空")
        @ManHour(max = 7, maxDecimalPlaces = 1, message = "正常工时最低只能以0.5（半小时）为单位，且单条不能超过7")
        private BigDecimal normalHours;

        /**
         * 加班工时
         */
        @NotNull(message = "加班工时不能为空")
        @ManHour(message = "加班工时必须为整数，且必须在0-24之间")
        private BigDecimal addedHours;

        /**
         * 工作日加班工时
         */
        private BigDecimal workOvertimeHours;

        /**
         * 休息日加班工时
         */
        private BigDecimal restOvertimeHours;
        /**
         * 节假日加班工时
         */
        private BigDecimal holidayOvertimeHours;
        /**
         * 工作内容
         */
        private String description;

        /**
         * 是否是内部项目
         */
        private Integer isInsideProject;

        /**
         * 旧任务标记
         * 0-新任务 1-旧任务
         */
        private Integer oldTaskFlag;

        /**
         * 审核状态（0=未提交，1=已退回，2=待审核，3=不通过，4=已通过）
         */
        private Integer approvalStatus;
    }


    @Setter
    @Getter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TomorrowPlanPaperEntry {

        /**
         * 日报条目ID
         */
        private Long id;

        /**
         * 项目ID
         */
        @NotNull(message = "项目ID不能为空")
        private Long projectId;

        /**
         * 任务ID
         */
        @NotNull(message = "任务ID不能为空")
        private Long taskId;

        /**
         * 工时类型
         */
        private Integer workType;

        /**
         * 工作内容
         */
        private String description;

        /**
         * 是否是内部项目
         */
        private Integer isInsideProject;

        /**
         * 旧任务标记
         * 0-新任务 1-旧任务
         */
        private Integer oldTaskFlag;

    }

}
