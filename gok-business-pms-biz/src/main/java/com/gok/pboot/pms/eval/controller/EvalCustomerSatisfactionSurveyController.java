package com.gok.pboot.pms.eval.controller;

import com.gok.pboot.common.security.annotation.Inner;
import com.gok.pboot.pms.eval.entity.dto.EvalCustomerSatisfactionSurveyDTO;
import com.gok.pboot.pms.eval.entity.vo.EvalCustomerSatisfactionSurveyVO;
import com.gok.pboot.pms.eval.service.IEvalCustomerSatisfactionSurveyService;
import com.gok.pboot.service.commons.base.ApiResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 客户满意度调查Controller
 *
 * <AUTHOR>
 * @date 2025/05/08
 */
@Slf4j
@Api(tags = "客户满意度调查")
@RestController
@RequestMapping("/evalCustomerSatisfactionSurvey")
@RequiredArgsConstructor
public class EvalCustomerSatisfactionSurveyController {

    private final IEvalCustomerSatisfactionSurveyService evalCustomerSatisfactionSurveyService;

    /**
     * 按项目 ID 获取
     *
     * @param projectId 项目 ID
     * @return {@link ApiResult }<{@link EvalCustomerSatisfactionSurveyVO }>
     */
    @ApiOperation("根据项目ID获取满意度调查数据")
    @GetMapping("/{projectId}")
    public ApiResult<EvalCustomerSatisfactionSurveyVO> getSatisfactionSurveyByProjectId(
            @ApiParam(value = "项目ID", required = true) @PathVariable Long projectId) {
        return ApiResult.success(evalCustomerSatisfactionSurveyService.getSatisfactionSurveyByProjectId(projectId));
    }

    /**
     * 按项目 ID 获取
     *
     * @param projectId 项目 ID
     * @return {@link ApiResult }<{@link EvalCustomerSatisfactionSurveyVO }>
     */
    @ApiOperation("根据项目ID获取满意度调查数据")
    @GetMapping("/mobile/{projectId}")
    @Inner(false)
    public ApiResult<EvalCustomerSatisfactionSurveyVO> getMobileSatisfactionSurveyByProjectId(
            @ApiParam(value = "项目ID", required = true) @PathVariable Long projectId) {
        return ApiResult.success(evalCustomerSatisfactionSurveyService.getSatisfactionSurveyByProjectId(projectId));
    }

    /**
     * 更新满意度调查
     *
     * @param dto 满意度调查DTO
     * @return {@link ApiResult }<{@link String }>
     */
    @ApiOperation("更新满意度调查")
    @PutMapping("/update")
    @Inner(false)
    public ApiResult<String> updateSatisfactionSurvey(
            @ApiParam(value = "满意度调查数据", required = true) @Validated @RequestBody EvalCustomerSatisfactionSurveyDTO dto) {
        evalCustomerSatisfactionSurveyService.updateSatisfactionSurvey(dto);
        return ApiResult.success("操作成功");
    }

    /**
     * 定时触发满意度调查推送
     */
    @Inner(value = false)
    @GetMapping("/push")
    public ApiResult<String> satisfactionSurveyPush() {
        evalCustomerSatisfactionSurveyService.satisfactionSurveyMsgPush();
        return ApiResult.success("推送成功");
    }

    /**
     * 定时处理超期满意度调查
     */
    @Inner(value = false)
    @GetMapping("/overdue")
    public ApiResult<String> handleOverdueSurveyList() {
        log.info("开始处理超期满意度调查...");
        try {
            evalCustomerSatisfactionSurveyService.handleOverdueSurveyList();
            log.info("处理超期满意度调查完成");
        } catch (Exception e) {
            log.error("处理超期满意度调查失败：{}", e.getMessage());
        }
        return ApiResult.success("处理成功");
    }
} 