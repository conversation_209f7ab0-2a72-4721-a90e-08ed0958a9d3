package com.gok.pboot.pms.entity.vo;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 合同下载vo
 *
 * <AUTHOR>
 * @date 2024/2/22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class ContractDownloadVo {

    /**
     * 合同id
     */
    private Long id;
    /**
     * 合同名称
     */
    private String htmc;

    /**
     * 合同附件（已盖章）集合
     */
    private List<OaFileInfoVo> htfjygzList;





















}
