package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 操作 枚举
 *
 * <AUTHOR>
 * @date 2025/01/16
 */
@Getter
@AllArgsConstructor
public enum OperateEnum implements ValueEnum<Integer> {
    /**
     * 默认
     */
    DEFAULT(0, "默认"),
    /**
     * 新增
     */
    ADD(1, "新增"),
    /**
     * 更新
     */
    UPDATE(2, "修改"),
    /**
     * 删除
     */
    DELETE(3, "删除");

    private final Integer value;
    private final String name;
}
