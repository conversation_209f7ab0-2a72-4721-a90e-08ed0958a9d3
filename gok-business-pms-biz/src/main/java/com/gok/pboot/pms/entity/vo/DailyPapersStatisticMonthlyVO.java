package com.gok.pboot.pms.entity.vo;

import lombok.*;

/**
 * - 日报按月统计数据 -
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/9/5 17:21
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@ToString
public class DailyPapersStatisticMonthlyVO {
    /**
     * 日报总数
     */
    private Integer total;

    /**
     * 待审核数
     */
    private Integer waitingReview;

    /**
     * 异常日报数
     */
    private Integer abnormal;

    public static DailyPapersStatisticMonthlyVO of(Integer total, Integer waitingReview, Integer abnormal) {
        DailyPapersStatisticMonthlyVO result = new DailyPapersStatisticMonthlyVO();

        result.setTotal(total);
        result.setWaitingReview(waitingReview);
        result.setAbnormal(abnormal);

        return result;
    }
}
