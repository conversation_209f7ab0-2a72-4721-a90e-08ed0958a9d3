package com.gok.pboot.pms.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.entity.domain.BusinessContact;
import com.gok.pboot.pms.entity.domain.PmsDictItem;
import com.gok.pboot.pms.entity.domain.PmsDictItemMultiLevel;
import com.gok.pboot.pms.entity.dto.BusinessInfoDTO;
import com.gok.pboot.pms.entity.dto.BusinessProgressDTO;
import com.gok.pboot.pms.entity.vo.BusinessDataLogVO;
import com.gok.pboot.pms.entity.vo.BusinessInfoVO;
import com.gok.pboot.pms.entity.vo.BusinessProgressDetailsVO;
import com.gok.pboot.pms.entity.vo.BusinessProgressVO;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 商机管理 服务类接口
 *
 * <AUTHOR>
 * @date 2023/11/21
 */
public interface IBusinessInfoService {

    /**
     * 商机台账 - 根据字典类型查找字典项列表
     * @param dictType 字典类型
     * @return {@link ApiResult}<{@link List}<{@link PmsDictItem}>>
     */
    ApiResult<List<PmsDictItem>> findDictItemByDictType(String dictType);

    /**
     * 商机台账 - 分页
     *
     * @param dto 传入参数
     * @return {@link ApiResult}<{@link Page}<{@link BusinessInfoVO}>>
     */
    ApiResult<Page<BusinessInfoVO>> findBusinessInfoPage(BusinessInfoDTO dto);

    /**
     * 商机台账 - 导出
     * @param dto 传入参数
     * @return 导出文件
     */
    List<BusinessInfoVO> exportBusinessInfo(BusinessInfoDTO dto);

    /**
     * 商机详情 - 详细信息
     * @param id 商机ID
     * @return {@link ApiResult}<{@link BusinessInfoVO}>
     */
    ApiResult<BusinessInfoVO> findOne(Long id);

    /**
     * 商机详情 - 联系人列表
     * @param id 商机ID
     * @return {@link ApiResult}<{@link List}<{@link BusinessContact}>>
     */
    ApiResult<List<BusinessContact>> findContact(Long id);

    /**
     * 商机台账 - 通过字典类型列表查找字典类型-字典项Map
     * @param dictTypes 字典类型列表
     * @return {@link ApiResult}<{@link Map}<{@link String},{@link Collection}<{@link PmsDictItem}>>>
     */
    ApiResult<Map<String, Collection<PmsDictItem>>> findDictMapByTypeList(String dictTypes);

    /**
     * 商机台账 - 动态获取所有项目所在地列表
     * @return {@link ApiResult}<{@link List}<{@link String}>>
     */
    ApiResult<List<String>> findAllProjectLocation();

    /**
     * 商机进展记录 - 分页
     * @param dto 传入参数
     * @return {@link ApiResult}<{@link Page}<{@link BusinessProgressVO}>>
     */
    ApiResult<Page<BusinessProgressVO>> findBusinessProgressPage(BusinessProgressDTO dto);

    /**
     * 商机进展记录 - 导出
     * @param dto 传入参数
     * @return {@link List}<{@link BusinessProgressVO}>
     */
    List<BusinessProgressVO> exportBusinessProgress(BusinessProgressDTO dto);

    /**
     * 商机进展记录 - 详细信息
     * @param id 商机进展 ID
     * @return {@link ApiResult}<{@link BusinessProgressDetailsVO}>
     */
    ApiResult<BusinessProgressDetailsVO> findOneProgress(Long id);

    /**
     * 商机详情 - 单个项目的进展情况列表
     * @param id 商机ID
     * @return {@link ApiResult}<{@link List}<{@link BusinessProgressVO}>>
     */
    ApiResult<List<BusinessProgressVO>> findProgressGroupByBusinessId(Long id);

    /**
     * 商机详情 - 数据日志
     * @param id 商机ID
     * @return {@link ApiResult}<{@link List}<{@link BusinessDataLogVO}>>
     */
    ApiResult<List<BusinessDataLogVO>> findDataLog(Long id);

    /**
     * 根据类型查找具有二级字典项的字典项列表
     * @param dictType 字典类型
     * @return {@link ApiResult}<{@link List}<{@link PmsDictItemMultiLevel}>>
     */
    ApiResult<List<PmsDictItemMultiLevel>> findDictItemMultiLevelByDictType(String dictType);

    /**
     * 商机流程文件接口（获取文件对应的下载路径信息）
     * @param requestId 请求id
     * @return {@link ApiResult}<{@link Object}>
     */
    ApiResult<Object> getProgressFile(Long requestId);


    Integer countByUnit(Long unitId,Integer businessStatus);

}
