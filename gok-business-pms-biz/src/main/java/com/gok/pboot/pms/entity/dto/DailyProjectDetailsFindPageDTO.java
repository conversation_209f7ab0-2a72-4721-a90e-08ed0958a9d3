package com.gok.pboot.pms.entity.dto;

import com.gok.pboot.pms.common.base.PageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
 * PMS日报审核
 *
 * @Auther chenhc
 * @Date 2022-08-24 11:24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class DailyProjectDetailsFindPageDTO extends PageRequest {

    /**
     * 提交日期
     */
    @NotNull(message = "提交日期不能为空！")
    private LocalDate submissionDate;

    /**
     * 项目id
     */
    @NotNull(message = "项目id不能为空！")
    private String projectId;

    /**
     * 审核状态（0：未审核、1：已审核）
     */
    @NotNull(message = "审核状态不能为空！")
    private Integer status;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 提交人姓名
     */
    private String userRealName;

    /**
     * 用户ID列表
     */
    private List<Long> userIds;

    /**
     * 我负责的（0：不勾选，1：勾选）
     * @see com.gok.pboot.enumeration.entity.YesOrNoEnum
     */
    private Integer inCharge;

}
