package com.gok.pboot.pms.entity.vo.facade;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 项目
 *
 * <AUTHOR>
 * @since 2022-08-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ProjectInfoVO {

    /**
     * 项目ID
     */
    private Long id;

    /**
    * 项目名称
    */
    private String projectName;
    /**
    * 项目编号
    */
    private String code;
    /**
    * 项目状态
    */
    private Integer projectStatus;
    /**
     * 项目状态值
     */
    private String projectStatusName;
    /**
    * 总任务数
    */
    private Long totalTasks;
    /**
    * 总工时数
    */
    private BigDecimal totalMhours;

    /**
     * 业务归属部门
     */
    private String projectDepartment;




}
