package com.gok.pboot.pms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.entity.domain.ProjectEstimatedCost;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * 项目预估成本表（Oa项目预算台账）
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-17 11:11:29
 */
@Mapper
public interface ProjectEstimatedCostMapper extends BaseMapper<ProjectEstimatedCost> {

    /**
     * 项目信息分页查询 -- 无权限控制
     */
    Page<ProjectEstimatedCost> findPage(Page<ProjectEstimatedCost> page, @Param("filter") Map<String, Object> filter);

}
