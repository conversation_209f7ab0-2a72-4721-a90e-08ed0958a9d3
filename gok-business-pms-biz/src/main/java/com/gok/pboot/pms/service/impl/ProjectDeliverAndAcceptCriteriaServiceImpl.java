package com.gok.pboot.pms.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.pboot.pms.entity.domain.ProjectDeliverAndAcceptCriteria;
import com.gok.pboot.pms.mapper.ProjectDeliverAndAcceptCriteriaMapper;
import com.gok.pboot.pms.service.IProjectDeliverAndAcceptCriteriaService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 商机管理 Service
 *
 * <AUTHOR>
 * @date 2023/11/21
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProjectDeliverAndAcceptCriteriaServiceImpl extends
        ServiceImpl<ProjectDeliverAndAcceptCriteriaMapper, ProjectDeliverAndAcceptCriteria>
        implements IProjectDeliverAndAcceptCriteriaService {
    @Override
    public List<ProjectDeliverAndAcceptCriteria> getByProjectId(Long id) {
        QueryWrapper<ProjectDeliverAndAcceptCriteria> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ProjectDeliverAndAcceptCriteria::getProjectId, id);
        return baseMapper.selectList(queryWrapper);
    }
}
