package com.gok.pboot.pms.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 项目商务里程碑（OA同步）
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-02-23 10:46:40
 */
@Data
@TableName("project_business_milestones")
public class ProjectBusinessMilestones implements Serializable {
	private static final long serialVersionUID = 1L;
	/**
	 * 合同id
	 */
	@TableId
	private Long id;
    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 项目名字
     */
    private String projectName;
    /**
     * 里程碑类型（1=B表商务里程碑，2=合同会签商务里程碑）
     */
    private Integer type;
    /**
     * 商务里程碑
     */
    private String businessMilestones;
    /**
     * 商务里程碑OaId
     */
    private Long oaId;
    /**
     * 里程碑说明
     */
    private String milestoneDesc;
    /**
     * 预计完成日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate expectedCompleteDate;
    /**
     * 实际完成日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate actualCompleteDate;
    /**
     * 佐证材料
     */
    private String supportMaterials;

    /**
     * '里程碑达成流程id'
     */
    private String lcbdclcRequestid;
    /**
     * '里程碑达成流程关联人Oaid'
     */
    private String lcbdclcRelateid;
    /**
     * 是否已完成(0是, 1否)
     */
    private String ifFinish;
    /**
     * 账款类型
     */
    private String accountType;
    /**
     * 所属合同id
     */
    private String contractId;
    /**
     * 所属合同名
     */
    private String contractName;
    /**
     * 合同付款条款
     */
    private String contractPayTerms;
    /**
     * 回款账期（天）
     */
    private Integer collectDays;
    /**
     * 回款比例（%）
     */
    private String collectRatio;
    /**
     * 预估结算金额（含税）
     */
    private BigDecimal  expectCollectAmount;
    /**
     * 回款状态
     */
    private Integer paymentStatus;
    /**
     * 实际款项金额
     */
    private BigDecimal currentPaymentMoney;

    /**
     * 结算金额（含税)
     */
    private BigDecimal settlementAmount;

    /**
     * 结算金额（不含税)
     */
    private BigDecimal settlementAmountExcludingTax;

    /**
     * 税率OA字典ID
     */
    private Integer taxRate;

    /**
     * 发起的流程请求 ID
     */
    private Long requestId;

    /**
     * 请求状态
     */
    private Integer requestStatus;

    @TableLogic(value = "0", delval = "1")
    private Integer delFlag;
}
