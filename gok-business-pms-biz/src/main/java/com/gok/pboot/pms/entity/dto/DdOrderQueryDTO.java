package com.gok.pboot.pms.entity.dto;

import lombok.Data;

import java.util.List;

/**
 * 滴滴订单查询DTO
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
public class DdOrderQueryDTO {

    /**
     * 页码
     */
    public int pageNumber;

    /**
     * 页面大小
     */
    public int pageSize;
    /**
     * 工号/姓名（模糊查询）
     */
    private String employeeNoOrName;

    /**
     * 订单号/报账编号（模糊查询）
     */
    private String orderNoOrExpenseReportNo;


    private List<Integer> initiationStatues;

    /**
     * 公司 ID集合
     */
    private List<String> companyIds;

    /**
     * 成本中心 ID集合
     */
    private List<String> costCenterIds;

    /**
     * 项目名称/编码（模糊查询）
     */
    private String projectNameOrCode;

    /**
     * 所属账期
     */
    private String accountingPeriod;

    /**
     * 已选择的订单ID列表
     */
    private List<Long> selectedOrderIds;

} 