package com.gok.pboot.pms.cost.entity.vo;

import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.cost.entity.domain.CostTaskCategoryManagement;
import com.gok.pboot.pms.enumeration.ProjectTaskKindEnum;
import com.gok.pboot.pms.enumeration.YesOrNoEnum;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 类别管理表VO
 *
 * <AUTHOR>
 * @date 2024/03/26
 */
@Data
@Accessors(chain = true)
public class CostTaskCategoryManagementVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 工单类别
     */
    private Integer taskCategory;

    /**
     * 工单类别名称
     */
    private String taskCategoryName;

    /**
     * 工单类型(0=售前支撑, 1=售后交付)
     * @see com.gok.pboot.pms.enumeration.ProjectTaskKindEnum
     */
    private Integer taskType;

    /**
     * 工单类型文本
     */
    private String taskTypeTxt;

    /**
     * 是否可自创建(0=否, 1=是)
     */
    private Integer canSelfCreate;

    /**
     * 是否可自创建文本
     */
    private String canSelfCreateTxt;

    /**
     * 排序
     */
    private Integer sort;

    public static CostTaskCategoryManagementVO buildVo(CostTaskCategoryManagement entity) {
        return new CostTaskCategoryManagementVO()
                .setId(entity.getId())
                .setTaskCategory(entity.getTaskCategory())
                .setTaskCategoryName(entity.getTaskCategoryName())
                .setTaskType(entity.getTaskType())
                .setTaskTypeTxt(EnumUtils.getNameByValue(ProjectTaskKindEnum.class, entity.getTaskType()))
                .setCanSelfCreate(entity.getCanSelfCreate())
                .setCanSelfCreateTxt(EnumUtils.getNameByValue(YesOrNoEnum.class, entity.getCanSelfCreate()))
                .setSort(entity.getSort());
    }
} 