package com.gok.pboot.pms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.entity.domain.ProjectFollowupPlan;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 项目后续计划实体类
 *
 * <AUTHOR>
 * @create 2025/07/03
 **/
@Mapper
public interface ProjectFollowupPlanMapper extends BaseMapper<ProjectFollowupPlan> {

    /**
     * 批量逻辑删除
     *
     * @param projectId 项目ID
     */
    void batchDel(@Param("projectId") Long projectId);

}
