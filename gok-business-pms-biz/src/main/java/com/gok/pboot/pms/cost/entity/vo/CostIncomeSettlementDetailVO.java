package com.gok.pboot.pms.cost.entity.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 收入结算明细列表VO
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CostIncomeSettlementDetailVO {

    /**
     * id
     */
    @ExcelIgnore
    private Long id;

    /**
     * 项目id
     */
    @ExcelIgnore
    private Long projectId;

    /**
     * 结算开始日期
     */
    @ExcelProperty("结算开始日期")
    private LocalDate startDate;

    /**
     * 结算截止日期
     */
    @ExcelProperty("结算截止日期")
    private LocalDate endDate;

    /**
     * 姓名
     */
    @ExcelProperty("姓名")
    private String userName;

    /**
     * 工号
     */
    @ExcelProperty("工号")
    private String workCode;

    /**
     * 收入测算id
     */
    @ExcelIgnore
    private Long costIncomeCalculationId;

    /**
     * 收入测算明细id
     */
    @ExcelIgnore
    private Long costIncomeCalculationDetailId;

    /**
     * 测算含税金额
     */
    @ExcelProperty("测算含税金额")
    private BigDecimal estimatedInclusiveAmountTax;

    /**
     * 结算含税金额
     */
    @ExcelProperty("结算含税金额")
    private BigDecimal budgetAmountIncludedTax;

    /**
     * 税率OA字典ID
     */
    @ExcelIgnore
    private String taxRate;

    /**
     * 税率OA字典Txt
     */
    @ExcelProperty("税率")
    private String taxRateTxt;

    /**
     * 结算不含税金额
     */
    @ExcelProperty("结算不含税金额")
    private BigDecimal budgetAmountExcludingTax;

    /**
     * 备注说明
     */
    @ExcelProperty("备注说明")
    private String remarksDesc;

    /**
     * 归档日期
     */
    @ExcelProperty("归档日期")
    private String filingTime;

    /**
     * OA结算单流程id
     */
    @ExcelIgnore
    private Long requestId;

    /**
     * OA结算单编号
     */
    @ExcelProperty("OA结算单编号")
    private String requestNumber;

    /**
     * 结算明细编号
     */
    @ExcelProperty("结算明细编号")
    private String settlementDetailsNumber;

    /**
     * 结算单编号
     */
    @ExcelProperty("结算单编号")
    private String settlementNumber;

    /**
     * 数据来源
     */
    @ExcelIgnore
    private Integer dataSources;

    /**
     * 数据来源Txt
     */
    @ExcelProperty("数据来源")
    private String dataSourcesTxt;

    /**
     * 审批状态
     */
    @ExcelIgnore
    private Integer approvalStatus;

    /**
     * 审批状态Txt
     */
    @ExcelProperty("审批状态")
    private String approvalStatusTxt;

    /**
     * 操作结算日期
     */
    @ExcelIgnore
    private String operationSettlementDate;

}
