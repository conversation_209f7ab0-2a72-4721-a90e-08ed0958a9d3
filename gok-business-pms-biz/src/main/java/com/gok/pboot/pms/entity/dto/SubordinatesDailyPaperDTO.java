package com.gok.pboot.pms.entity.dto;

import com.gok.pboot.pms.common.base.PageRequest;
import lombok.Data;

import java.util.Collection;

/**
 * 下属日报查询条件实体
 *
 * <AUTHOR>
 * @Date 2022-08-24 11:24
 */
@Data
public class SubordinatesDailyPaperDTO extends PageRequest {
    /**
     * 日期开始时间
     */
    private String startTime;
    /**
     * 日期结束时间
     */
    private String endTime;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 任务名称
     */
    private String taskName;
    /**
     * 提交人姓名
     */
    private String userName;
    /**
     * userId
     */
    private Long userId;
    /**
     * 下级id集合
     */
    private Collection<Long> userIds;
    /**
     * 任务id集合
     */
    private Collection<Long> taskIds;
}
