package com.gok.pboot.pms.eval.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2025/05/15
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EvalProjectOverviewDTO {

    /**
     * 项目整体评价主键ID
     */
    private Long id;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 项目ID集合
     */
    private List<Long> projectIds;

    /**
     * 项目编码
     */
    private String projectNo;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 评价状态
     * {@link com.gok.pboot.pms.eval.enums.EvalStatusEnum}
     */
    private Integer evalStatus;

    /**
     * 表扬信文件id，多个逗号隔开
     */
    private String commendationLetter;

}
