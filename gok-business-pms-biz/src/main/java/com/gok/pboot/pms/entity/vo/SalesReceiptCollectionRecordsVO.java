package com.gok.pboot.pms.entity.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 催款记录-收款方式展示
 *
 * <AUTHOR>
 * @since 2024-02-20
 */
@Data
public class SalesReceiptCollectionRecordsVO implements Serializable {

    /**
     * id
     */
    private Long id;

    /**
     * 销售收款id
     */
    private Long salesReceiptId;

    /**
     * 预计收款日期
     */
    private LocalDate expectedReceiptDate;

    /**
     * 催收日期
     */
    private LocalDate collectionDate;

    /**
     * 催收金额
     */
    private String collectionAmount;

    /**
     * 催收金额
     */
    private String collectionAmountTxt;

    /**
     * 催款方式
     * {@link com.gok.pboot.pms.enumeration.CollectionMethodEnum}
     */
    private Integer collectionMethod;

    /**
     * 催款方式
     * {@link com.gok.pboot.pms.enumeration.CollectionMethodEnum}
     */
    private String collectionMethodTxt;

    /**
     * 催款情况
     */
    private String collectionSituation;

    /**
     * 催款备注
     */
    private String collectionRemarks;

}
