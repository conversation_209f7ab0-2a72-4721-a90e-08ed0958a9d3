package com.gok.pboot.pms.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.entity.domain.ContractLedger;
import com.gok.pboot.pms.entity.dto.ContractLedgerListDTO;
import com.gok.pboot.pms.entity.dto.ContractLedgerSelectListDTO;
import com.gok.pboot.pms.entity.vo.*;
import com.gok.pboot.pms.enumeration.OaFileDownLoadTypeEnum;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 合同台账数据（OA同步）
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-02-23 10:46:40
 */
public interface IContractLedgerService extends IService<ContractLedger> {

    /**
     *  根据合同id查询合同基本信息
     * @param id 合同id
     * @return 合同基本信息
     */
    ApiResult<ContractBaseInfoVo> getContractBaseInfoVoList(Long id);

    /**
     * 查询合同台账列表
     * @param dto
     * @return
     */
    Page<ContractLedgerListVo> getContractLedgerVoList(ContractLedgerListDTO dto);

    /**
     * 合同台账查询字典值
     * @param fieldIds
     * @return
     */
    Map<Integer, Map<Integer, String>> getProjectDictMap(Set<Integer> fieldIds);

    /**
     * 合同台账列表汇总数据
     * @param dto
     * @return
     */
    ContractLedgerSummaryStrVo getContractLedgerSummary(ContractLedgerListDTO dto);

    /**
     * 根据合同id查询合同头部信息
     * @param id
     * @return
     */
    ApiResult<ContractBaseHeadInfoVo> getContractBaseHeadInfoVo(Long id);

    /**
     * 根据合同id查询合同概览信息
     * @param id
     * @return
     */
    ContractOverviewInfoVo getContractOverviewInfoVo(Long id);

    /**
     * 根据合同id查询合同流程信息
     * @param id
     * @return
     */
    List<ContractProcessListVO> getContractProcessInfoVo(Long id);

    /**
     * 导出合同台账列表
     * @param dto
     * @return
     */
    List<ContractLedgerListVo> exportContractLedgerVoList(ContractLedgerListDTO dto);

    /**
     * 根据合同id查询合同验收记录
     * @param id
     * @return
     */
    List<ContractAcceptanceVo> getContractAcceptanceVoList(Long id);

    /**
     * 根据合同ids批量查询合同验收记录
     * @param ids
     * @return
     */
    Map<Long,List<ContractAcceptanceVo>>  getContractAcceptanceVoListMap(List<Long> ids);

    /**
     * 根据合同id查询合同风险信息
     * @param id
     * @return
     */
    List<ContractRiskListVO> getContractRiskInfoVo(Long id);

    /**
     * 批量查询合同风险信息
     * @param ids
     * @return
     */
    Map<Long,List<ContractRiskInfoVO>> getContractRiskInfoVo(List<Long> ids);

    /**
     * 根据合同ids批量查询合同风险信息
     * @param ids
     * @return
     */
    Map<Long,List<ContractRiskInfoVO>> getContractRiskInfoVoListMap(List<Long> ids);

    /**
     *  合同下载日志记录
     * @param id 合同id
     * @return
     */
    ContractDownloadVo contractDownload(Long id);
    /**
     *  Oa附件下载
     * @param contractId 合同id
     * @param fileType 文件类型
     * @return
     */
    OaFileDownloadVo oaFileDownload(Long contractId, OaFileDownLoadTypeEnum fileType, String fileId);

    List<ContractLedgerSelectListVo> getContractLedger(ContractLedgerSelectListDTO dto);
}

