package com.gok.pboot.pms.cost.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 成本 任务 类别 排序 vo
 *
 * <AUTHOR>
 * @date 2025/04/16
 */
@Data
@AllArgsConstructor
public class CostTaskCategorySortVO {

    /**
     * 工单类别
     */
    private Integer taskCategory;

    /**
     * 工单类别名称
     */
    private String taskCategoryName;

    /**
     * 工单类别排序
     */
    private Integer sort;

    /**
     * 是否可自创建(0=否, 1=是)
     */
    private Integer canSelfCreate;
}
