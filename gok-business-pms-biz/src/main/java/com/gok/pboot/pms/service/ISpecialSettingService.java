package com.gok.pboot.pms.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.entity.dto.SpecialSettingAddDTO;
import com.gok.pboot.pms.entity.vo.SpecialSettingUserVO;
import com.gok.pboot.pms.entity.vo.SpecialSettingVO;

import java.util.List;
import java.util.Map;

/**
 * - 特殊配置服务 -
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public interface ISpecialSettingService {

    Page<SpecialSettingVO> queryPage(PageRequest pageRequest, Map<String, Object> filter);

    void add(SpecialSettingAddDTO dto);

    void delete(Long id);

    List<SpecialSettingUserVO> findUserList();
}
