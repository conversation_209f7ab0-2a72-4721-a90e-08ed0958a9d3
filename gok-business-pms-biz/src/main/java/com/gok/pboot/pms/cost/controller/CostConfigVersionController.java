package com.gok.pboot.pms.cost.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.cost.entity.vo.VersionHistoryVO;
import com.gok.pboot.pms.cost.enums.CostConfigVersionTypeEnum;
import com.gok.pboot.pms.cost.service.ICostConfigVersionService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 成本配置版本记录 控制器
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@RestController
@AllArgsConstructor
@RequestMapping("/costConfigVersion")
public class CostConfigVersionController {

    private final ICostConfigVersionService service;


    /**
     * 获取历史版本数据
     *
     * @param pageRequest
     * @return {@link ApiResult }<{@link Page }<{@link VersionHistoryVO }>>
     */
    @PostMapping("/historyPage/{versionTypeEnum}")
    public ApiResult<Page<VersionHistoryVO>> getHistoryVersions(@PathVariable CostConfigVersionTypeEnum versionTypeEnum, @RequestBody PageRequest pageRequest) {
        return ApiResult.success(service.getHistoryVersions(pageRequest, versionTypeEnum), "获取成功");
    }

}
