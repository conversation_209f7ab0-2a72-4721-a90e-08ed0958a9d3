package com.gok.pboot.pms.eval.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 项目人员售后工单评价分布明细VO
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class EvalTaskDistributionAfterDetailVO extends EvalTaskDistributionDetailVO{

    /**
     * 结算工单产值
     */
    private BigDecimal income;

    /**
     * 结算工单产值占比
     */
    private String settlementValueRatio;

    /**
     * 工单预算人工成本
     */
    private String budgetCost;

    /**
     * 工单实际人工成本
     */
    private String actualLaborCost;

    /**
     * 工单预算工时
     */
    private BigDecimal estimatedHours;

    /**
     * 工单实际工时
     */
    private BigDecimal actualHours;

}