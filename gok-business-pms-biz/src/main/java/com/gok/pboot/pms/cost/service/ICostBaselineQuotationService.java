//package com.gok.pboot.pms.cost.service;
//
//import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
//import com.baomidou.mybatisplus.extension.service.IService;
//import com.gok.pboot.pms.common.base.PageRequest;
//import com.gok.pboot.pms.cost.entity.domain.CostBaselineQuotation;
//import com.gok.pboot.pms.cost.entity.dto.ProjectBusinessInfoDTO;
//import com.gok.pboot.pms.cost.entity.dto.VersionAuditDTO;
//import com.gok.pboot.pms.cost.entity.vo.CostBaselineQuotationHistoryVersionVO;
//import com.gok.pboot.pms.cost.entity.vo.CostBaselineQuotationVO;
//
//import java.math.BigDecimal;
//
///**
// * <AUTHOR>
// */
//public interface ICostBaselineQuotationService extends IService<CostBaselineQuotation> {
//
//    /**
//     * 同步商业论证B表报价与毛利测算归档信息
//     *
//     * @return {@link Boolean}
//     */
//    boolean insertGrossProfitMeasurementAndVersionInfo();
//
//    /**
//     * 获取报价与毛利测算版本信息
//     *
//     * @param projectId 项目ID
//     * @param versionId 版本ID
//     * @return {@link CostBaselineQuotationVO}
//     */
//    CostBaselineQuotationVO getGrossProfitMeasurementVersionInfo(Long projectId, Long versionId);
//
//    /**
//     * 处理毛利测算详情
//     *
//     * @param projectId             项目ID
//     * @param costBaselineQuotation info
//     */
//    void setGrossProfitMeasurementDetail(Long projectId, CostBaselineQuotationVO costBaselineQuotation);
//
//    /**
//     * 获取报价与毛利测算历史版本信息
//     *
//     * @param pageRequest 分页参数
//     * @param projectId   项目ID
//     * @return {@link Page}<{@link  CostBaselineQuotationHistoryVersionVO}>
//     */
//    Page<CostBaselineQuotationHistoryVersionVO> getGrossProfitMeasurementHistoryVersionInfo(PageRequest pageRequest, Long projectId);
//
//    /**
//     * 更新报价与毛利测算信息
//     *
//     * @param projectBusinessInfoDTO dto
//     */
//    void insertGrossProfitMeasurementInfo(ProjectBusinessInfoDTO projectBusinessInfoDTO);
//
//    /**
//     * 获取收入总额(不含税)
//     *
//     * @param projectId 项目ID
//     * @return {@link BigDecimal}
//     */
//    BigDecimal getIncomeAmountExcludingTax(Long projectId);
//
//    /**
//     * 更新当前版本审核状态
//     *
//     * @param versionAuditDTO dto
//     */
//    void updateAuditStatusInfo(VersionAuditDTO versionAuditDTO);
//
//}