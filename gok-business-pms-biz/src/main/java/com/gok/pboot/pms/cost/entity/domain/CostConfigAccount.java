package com.gok.pboot.pms.cost.entity.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * <p>
 * 成本科目配置
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("cost_config_account")
public class CostConfigAccount extends BeanEntity<Long> {

    private static final long serialVersionUID = 1L;

    /**
     * 版本ID
     */
    @TableField("version_id")
    private Long versionId;

    /**
     * 对应OAid
     */
    @TableField("oa_id")
    private Long oaId;

    /**
     * 成本科目类别id
     */
    @TableField("account_category_id")
    private Long accountCategoryId;

    /**
     * 成本科目类别名称
     */
    @TableField("account_category_name")
    private String accountCategoryName;

    /**
     * 科目名称
     */
    @TableField("account_name")
    private String accountName;

    /**
     * 科目代码
     */
    @TableField("account_code")
    private String accountCode;

    /**
     * 对应成本类型（0=人工成本，1=费用报销，2=外采费用）
     */
    @TableField(value = "account_type", insertStrategy = FieldStrategy.IGNORED)
    private Integer accountType;

    /**
     * 科目定义说明
     */
    @TableField("account_definition_desc")
    private String accountDefinitionDesc;

    /**
     * 是否用于成本估算
     * {@link com.gok.pboot.pms.enumeration.YesOrNoEnum}
     */
    @TableField("for_estimate_flag")
    private Integer forEstimateFlag;

    public static CostConfigAccount convert(Map<Long, CostConfigAccount> costSubjectConfigInfoMap, CostConfigAccount costSubjectConfigInfo) {
        CostConfigAccount costConfigAccount = new CostConfigAccount();
        boolean flag = costSubjectConfigInfoMap.containsKey(costSubjectConfigInfo.getOaId());
        costConfigAccount.setAccountType(flag ? costSubjectConfigInfoMap.get(costSubjectConfigInfo.getOaId()).getAccountType() : null);
        costConfigAccount.setOaId(costSubjectConfigInfo.getOaId());
        costConfigAccount.setAccountCode(costSubjectConfigInfo.getAccountCode());
        costConfigAccount.setAccountName(costSubjectConfigInfo.getAccountName());
        costConfigAccount.setAccountCategoryId(costSubjectConfigInfo.getAccountCategoryId());
        costConfigAccount.setAccountCategoryName(costSubjectConfigInfo.getAccountCategoryName());
        costConfigAccount.setAccountDefinitionDesc(costSubjectConfigInfo.getAccountDefinitionDesc());
        costConfigAccount.setForEstimateFlag(costSubjectConfigInfo.getForEstimateFlag());
        return costConfigAccount;
    }

}
