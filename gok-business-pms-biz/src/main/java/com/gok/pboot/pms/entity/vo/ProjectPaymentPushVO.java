package com.gok.pboot.pms.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 项目回款跟踪推送参数
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProjectPaymentPushVO {
    /**
     * id
     */
    private Long id;

    /**
     * 单据编号
     */
    private String documentNumber;

    /**
     * 收款公司
     * {@link com.gok.pboot.pms.enumeration.AttributableSubjectEnum}
     */
    private Integer paymentCompany;

    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 企业名称
     */
    private String enterpriseName;

    /**
     * 收款日期
     */
    private String paymentDate;

    /**
     * 收款金额
     */
    private BigDecimal paymentAmount;

    /**
     * 收款平台
     */
    private String paymentPlatform;

    /**
     * 凭证编号
     */
    private String voucherNumber;

    /**
     * 预算回款金额
     */
    private BigDecimal budgetCollectionAmount;

    /**
     * 银行账号
     */
    private String bankAccount;

    /**
     * 归属业务线
     * {@link com.gok.pboot.pms.enumeration.BusinessLineEnum}
     */
    private Integer businessLine;
}
