package com.gok.pboot.pms.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * - 实体选项（用于标识特殊对象） -
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(EntityOption.ALIAS)
public class EntityOption extends BeanEntity<Long> {

    public static final String ALIAS = "mhour_daily_option";

    /**
     * 实体ID
     */
    private Long entityId;

    /**
     * 标识
     * @see com.gok.pboot.pms.common.constant.EntitySign
     */
    private String sign;
}
