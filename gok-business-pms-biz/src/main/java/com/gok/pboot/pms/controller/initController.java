package com.gok.pboot.pms.controller;


import com.gok.pboot.common.security.annotation.Inner;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.BaseController;
import com.gok.pboot.pms.service.IInitService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 数据初始化前端控制器
 *
 * <AUTHOR>
 * @description 工时确认 前端控制器
 * @since 2022-08-30
 */
@Slf4j
@Inner(value = false)
@RestController
@RequestMapping("/init")
@RequiredArgsConstructor
public class initController extends BaseController {

    private final IInitService service;

    /**
     * 初始化7月1号以后加班类型
     * @return String
     */
    @GetMapping("/overworkTimeAndType")
    public ApiResult<String> overworkTimeAndType() {
        return service.overworkTimeAndType();
    }
}
