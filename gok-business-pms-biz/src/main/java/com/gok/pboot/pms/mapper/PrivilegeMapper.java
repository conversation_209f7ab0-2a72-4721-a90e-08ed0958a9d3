package com.gok.pboot.pms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.Util.PageAdapter;
import com.gok.pboot.pms.entity.Privilege;
import com.gok.pboot.pms.entity.domain.ProjectStakeholderMember;
import com.gok.pboot.pms.entity.dto.PrivilegeFindPageDTO;
import com.gok.pboot.pms.entity.vo.AdminConfigFindPageVo;
import com.gok.pboot.pms.entity.vo.PrivilegeFindPageVO;
import com.gok.pboot.pms.entity.vo.SysUserDataScopeVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import javax.annotation.Nullable;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 部门审核人员及权限表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-29
 */
@Mapper
public interface PrivilegeMapper extends BaseMapper<Privilege> {


    /**
     * 逻辑删除
     *
     * @param id 唯一标识
     * @return
     */
    int deleteByLogic(@Param("id") Long id);

    /**
     * 批量插入
     *
     * @param poList 实体集合
     */
    void batchSave(@Param("poList") List<Privilege> poList);


    /**
     * 批量修改
     *
     * @param list 实体集合
     */
    void batchUpdate(@Param("list") List<Privilege> list);

    /**
     * 批量逻辑删除
     *
     * @param list id集合
     */
    void batchDel(@Param("list") List<Long> list);

    /**
     * 批量修改不为空字段
     *
     * @param list id集合
     */
    void updateBatch(@Param("list") List<Long> list);

    /**
     * 根据项目编号和用户id和用户类型进行删除
     *
     * @param projectId
     * @param userIds
     * @param privilegeType
     */
    void batchDelByPjIdAndUserIdsAndType(@Param("projectId") Long projectId, @Param("userIds") List<Long> userIds, @Param("privilegeType") Integer privilegeType);

    List<AdminConfigFindPageVo> adminConfigFindPageVo(
            @Param("adapter") PageAdapter adapter,
            @Param("privilegeFindPageDTO") PrivilegeFindPageDTO privilegeFindPageDTO,
            @Param("dataScope") SysUserDataScopeVO dataScope
    );

    Map<String, Object> totalMap(
            @Param("privilegeFindPageDTO") PrivilegeFindPageDTO privilegeFindPageDTO,
            @Param("dataScope") SysUserDataScopeVO dataScope
    );

    List<Privilege> getPrivilegeByProjectIdAndPrivilegeType(
            @Param("projectIds") List<Long> projectIds,
            @Nullable @Param("privilegeType") Integer privilegeType
    );

    /**
     * @create by yzs at 2023/5/12
     * @description:项目id集合查询
     * @param: projectIds
     * @return: java.util.List<com.gok.pboot.pms.entity.Privilege>
     */
    List<Privilege> getByProjectId(@Param("projectIds") List<Long> projectIds);

    /**
     * 获取可审核的项目id
     * 1. 商机、商机终止状态下，销售、工时审核员可进行审核
     * 2. 在建、结项、异常终止状态下，项目经理、工时审核员可进行审核
     *
     * @param userId 用户id
     * @return 项目id列表
     */
    List<Long> selectAuditProjectId(Long userId);

    /**
     * 获取可操作的项目id
     * 项目操作员在项目管理可见的项目列表
     *
     * @param userId 用户id
     * @return 项目id列表
     */
    List<Long> selectOperatorProjectId(@Param("userId") Long userId);

    List<ProjectStakeholderMember> findToSyncMember();
}
