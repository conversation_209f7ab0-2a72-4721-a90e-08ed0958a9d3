package com.gok.pboot.pms.cost.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.cost.entity.domain.CostBaselineQuotation;
import com.gok.pboot.pms.cost.entity.vo.CostBaselineQuotationHistoryVersionVO;
import com.gok.pboot.pms.cost.entity.vo.CostBaselineQuotationVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 报价与毛利测算表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Mapper
public interface CostBaselineQuotationMapper extends BaseMapper<CostBaselineQuotation> {

    CostBaselineQuotationVO getGrossProfitMeasurementVersionInfo(@Param("projectId") Long projectId, @Param("versionId") Long versionId, @Param("auditStatus") Integer auditStatus);

    Page<CostBaselineQuotationHistoryVersionVO> getGrossProfitMeasurementHistoryVersionInfo(@Param("page") Page<CostBaselineQuotationHistoryVersionVO> page, @Param("projectId") Long projectId);

    int updateByVersionId(@Param("versionId") Long versionId, @Param("auditStatus") Integer auditStatus);

    /**
     * 查询最新且A表流程同步、已审核通过的报价与毛利测算数据
     * @param projectId
     * @return
     */
    CostBaselineQuotationVO getCostBaselineQuotationLast(@Param("projectId") Long projectId);

}
