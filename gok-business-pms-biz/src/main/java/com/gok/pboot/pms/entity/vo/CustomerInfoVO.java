package com.gok.pboot.pms.entity.vo;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerInfoVO {

    private Long id;

    /**
     * 客户名称
     */
    private String khmc;

    /**
     * 客户所在地
     */
    private Integer khszd;
    /**
     * 客户所在地Txt
     */
    private String khszdTxt;

    /**
     * 行业"
     */
    private Integer khxyyj;

    /**
     * 行业Txt
     */
    private String khxyyjTxt;
    /**
     * 归属业务部门
     */
    private Long gsywbmejbm;

    /**
     * 归属业务部门Txt
     */
    private String gsywbmejbmTxt;


    /**
     * 客户经理id
     */
    private String khjl;
    /**
     * 客户经理名字
     */
    private String khjlxm;

    /**
     * 客户分级
     */
    private Integer khfj;
    /**
     * 客户分级Txt
     */
    private String khfjTxt;

    /**
     * 客户编号
     */
    private String khbh;

    /**
     * 是否集团类客户
     */
    private Integer sfjtlkh;

    /**
     * 是否集团类客户Txt
     */
    private String sfjtlkhTxt;
    /**
     * 备案人姓名
     */
    private String barxm;
    /**
     * 联系人
     */
    private List<ContactVO> lxrList;

    /**
     * 开票邮寄信息
     */
    private String kpyjxx;
    /**
     * 开票开户银行
     */
    private String kpkhyx;
    /**
     * 开票开户行账号
     */
    private String kpkhxzh;
    /**
     * 开票电话
     */
    private String kpdh;
    /**
     * 统一社会信用代码
     */
    private String tyshxydm;
    /**
     * 开票地址
     */
    private String kpdz;
    /**
     * 开票附件
     */
    private String kpfj;
    /**
     * 客户备案流程id
     */
    private String khbalcRequestid;
    /**
     * 客户备案流程Oaid
     */
    private String khbalcRelateid;

    /**
     * 开票附件
     */
    private List<OaFileInfoVo> kpfjList;

    /**
     * true=已关注  false未关注
     */
    private Boolean isAttention=false;
}