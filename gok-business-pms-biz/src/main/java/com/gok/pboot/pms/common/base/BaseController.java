package com.gok.pboot.pms.common.base;

import com.gok.pboot.pms.Util.FantasyCollections;
import com.gok.pboot.pms.Util.FastJsonUtil;
import com.gok.pboot.pms.enumeration.ApiResultEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * 基础Controller,提供多数常用方法,谁用谁知道
 *
 * <AUTHOR> @ 163.com)
 * @version 1.0
 * @date 2016年07月25日
 */
@Slf4j
public abstract class BaseController {

    /**
     * 快速构造HashMap
     *
     * @param arrays
     * @param <K>
     * @param <V>
     * @return
     */
    public <K, V> Map<K, V> map(Object... arrays) {
        return FantasyCollections.map(arrays);
    }

    /**
     * 快速构造List
     *
     * @param arrays
     * @param <T>
     * @return
     */
    public <T> List<T> list(T... arrays) {
        return FantasyCollections.list(arrays);
    }

    /**
     * 快速新建HashSet
     *
     * @param arrays
     * @param <T>
     * @return
     */
    public <T> Set<T> newHashSet(T... arrays) {
        return FantasyCollections.newHashSet(arrays);
    }

    /**
     * Array转成HashSet
     *
     * @param array
     * @param <T>
     * @return
     */
    public <T> Set<T> hashSet(T[] array) {
        return FantasyCollections.hashSet(array);
    }

    /**
     * Array转成ArrayList
     *
     * @param array
     * @param <T>
     * @return
     */
    public <T> List<T> asList(T[] array) {
        return FantasyCollections.asList(array);
    }

    public Pattern regEx(String reg) {
        return Pattern.compile(reg);
    }

    /**
     * 渲染json
     *
     * @return
     */
    public static ApiResult render(int code, String message, Object result) {
        return ApiResult.builder().retCode(code).retMessage(message).result(result).build();
    }

    /**
     * 使用枚举传入retCode与retMessage
     *
     * @param valueEnum
     * @param result
     * @return
     */
    public static <T> ApiResult<T> render(ApiResultEnum valueEnum, T result) {
        return ApiResult.<T>builder().apiResultEnum(valueEnum).result(result).build();
    }

    /**
     * 简单渲染成功信息
     *
     * @param result
     * @return
     */
    public static <T> ApiResult<T> success(T result) {
        return ApiResult.<T>builder().apiResultEnum(ApiResultEnum.SUCCESS).result(result).build();
    }

    /**
     * 简单渲染通用失败信息
     *
     * @param result
     * @return
     */
    public static <T> ApiResult<T> failure(T result) {
        return ApiResult.<T>builder().apiResultEnum(ApiResultEnum.SUCCESS).result(result).build();
    }


    /**
     * 简单渲染成功信息
     *
     * @param msg
     * @return
     */
    public static ApiResult<String> successMsg(String msg) {
        return ApiResult.<String>builder().apiResultEnum(ApiResultEnum.SUCCESS).retMessage(msg).build();
    }


    /**
     * 简单渲染通用失败信息
     *
     * @param msg
     * @return
     */
    public ApiResult<String> failureMsg(String msg) {
        return ApiResult.<String>builder().apiResultEnum(ApiResultEnum.VALIDATION_FAILURE).result(msg).build();
    }


    /**
     * 设置session
     *
     * @param request
     * @param key
     * @param value
     */
    public void session(HttpServletRequest request, String key, Serializable value) {
        request.getSession(true).setAttribute(key, value);
    }

    /**
     * 获取session
     *
     * @param request
     * @param key
     * @return
     */
    public Object session(HttpServletRequest request, String key) {
        return request.getSession().getAttribute(key);
    }

    /**
     * json字条串转换成java bean列表
     *
     * @param json
     * @param clazz
     * @param <T>
     * @return
     */
    public <T> List<T> parseJson(String json, Class<T> clazz) {
        return FastJsonUtil.getListBeans(json, clazz);
    }

    /**
     * json字符串转换成 map列表
     *
     * @param json
     * @return
     */
    public List<Map<String, Object>> parseJson(String json) {
        return FastJsonUtil.getListMap(json);
    }

    /**
     * json字符串转换成java bean
     *
     * @param json
     * @param clazz
     * @param <T>
     * @return
     */
    public <T> T parseJsonMap(String json, Class<T> clazz) {
        return FastJsonUtil.getBean(json, clazz);
    }

    /**
     * 获取当前时间
     *
     * @return
     */
    public Date now() {
        return new Date();
    }

    /**
     * 获取当前时间戳
     *
     * @return
     */
    public long currentTimeStamp() {
        return System.currentTimeMillis();
    }

    /**
     * 获取当前短时间戳(微信接口)
     *
     * @return
     */
    public long currentTimeStampShort() {
        return System.currentTimeMillis() / 1000;
    }

    /**
     * 判断是不是在微信浏览器中打开
     *
     * @param request
     * @return
     */
    protected boolean isWeixinBrowser(HttpServletRequest request) {
        String ua = request.getHeader("user-agent").toLowerCase();
        if (ua.indexOf("micromessenger") > 0) {// 是微信浏览器
            return true;
        }
        return false;
    }

    /**
     * 合并两个java bean
     *
     * @param dest
     * @param origin
     */
    protected void merge(Object dest, Object origin) {
        BeanUtils.copyProperties(dest, origin);
    }

    /**
     * 输出文件
     *
     * @param response
     * @param wb
     * @param fileName
     * @throws IOException
     */
    public void commonDownload(HttpServletResponse response, Workbook wb, String fileName) throws IOException {
        ServletOutputStream out = null;
        try {
            response.setHeader("content-Type", "application/ms-excel");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            response.setCharacterEncoding("UTF-8");
            out = response.getOutputStream();
            wb.write(out);
            out.flush();
        } catch (UnsupportedEncodingException e) {
            log.error("编码转换异常", e);
        } catch (IOException e) {
            log.error("IO异常", e);
        } finally {
            if (wb != null) {
                wb.close();
            }
            if (out != null) {
                out.close();
            }
        }
    }


    /**
     * 输出doc后缀文件
     *
     * @param response
     * @param wb
     * @param fileName
     * @throws IOException
     */
    public void commonDownloadDoc(HttpServletResponse response, InputStream wb, String fileName) throws IOException {
        ServletOutputStream out = null;
        try {
            response.setHeader("content-Type", "application/msword");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            response.setCharacterEncoding("UTF-8");
            out = response.getOutputStream();
            byte[] buffer = new byte[512]; // 缓冲区
            int bytesToRead = -1;
            // 通过循环将读入的Word文件的内容输出到浏览器中
            while ((bytesToRead = wb.read(buffer)) != -1) {
                out.write(buffer, 0, bytesToRead);
            }
            out.flush();
        } catch (UnsupportedEncodingException e) {
            log.error("编码转换异常", e);
        } catch (IOException e) {
            log.error("IO异常", e);
        } finally {
            if (wb != null) {
                wb.close();
            }
            if (out != null) {
                out.close();
            }
        }
    }
}
