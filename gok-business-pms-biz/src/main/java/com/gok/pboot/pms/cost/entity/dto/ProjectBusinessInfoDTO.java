package com.gok.pboot.pms.cost.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class ProjectBusinessInfoDTO {

    /**
     * 版本ID
     */
    private Long versionId;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 收入总额(含税)
     */
    private BigDecimal incomeAmountIncludedTax;

    /**
     * 收入总额(不含税)
     */
    private BigDecimal incomeAmountExcludingTax;


    /**
     * 预计提前投入成本(万元)
     */
    private BigDecimal expectedEarlyInvestmentCost;

    /**
     * 流程ID
     */
    private Long requestId;

    /**
     * 流程名称
     */
    private String requestName;

    /**
     * 审核状态（0=A表流程同步,1=未审核，2=未通过，3=已审核）
     */
    private Integer auditStatus;

}
