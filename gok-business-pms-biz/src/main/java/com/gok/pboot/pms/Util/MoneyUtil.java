package com.gok.pboot.pms.Util;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import com.gok.pboot.common.secret.AESEncryptor;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 金额转换工具
 *
 * <AUTHOR>
 * @since 2023-09-14
 */
public class MoneyUtil {

    public static final String ZERO = "0";

    public static final String ZERO_TWO = "0.0";

    public static final String ZERO_THREE = "0.00";

    public static final String TYPE = "---";

    public static final String ZERO_PERCENT = "0.00%";

    public static final String ZERO_PERCENT_INT = "0%";

    private MoneyUtil() {}

    private static class MoneyUtilsInstance {
        private static final MoneyUtil INSTANCE = new MoneyUtil();
    }

    public static MoneyUtil getInstance() {
        return MoneyUtilsInstance.INSTANCE;
    }

    /**
     * 保留两位小数返回
     *
     * @param bigDecimal 金额
     * @return 金额
     */
    public String transType(BigDecimal bigDecimal) {
        if (Optional.ofNullable(bigDecimal).isPresent()) {
            // 创建DecimalFormat对象，并设置模式来实现千分位和两位小数
            DecimalFormat formatter = new DecimalFormat("##,##0.00");

            // 格式化数字
            return formatter.format(bigDecimal);
        }
        return ZERO_THREE;
    }

    public String percent(BigDecimal bigDecimal){
        //建立百分比格式化引用
        NumberFormat percent = NumberFormat.getPercentInstance();
        if (Optional.ofNullable(bigDecimal).isPresent()){
            String format = percent.format(bigDecimal);

            return format;
        }

        return ZERO_PERCENT;
    }
    
    /**
     * V1.1.1 优化显示：采用千位分隔符（每三位添加一个分割符号）
     *
     * @param num 金额
     * @return 金额
     */
    private String addSeparator(String num) {
        final int three = 3;
        if (ZERO_THREE.equals(num)) {
            return TYPE;
        }
        // 切割金额 前者为整数 后者为小数
        String[] numArr = num.split(StrPool.BACKSLASH + StrPool.DOT);
        num = numArr[NumberUtils.INTEGER_ZERO];

        int length = num.length();
        List<String> list = new ArrayList<>();
        while (length > three) {
            list.add(num.substring(length - three, length));
            length -= three;
        }
        // 将前面小于三位的数字添加到ArrayList中
        list.add(num.substring(0, length));
        StringBuilder stringBuilder = new StringBuilder();
        // 倒序拼接
        for (int i = list.size() - 1; i > 0; --i) {
            stringBuilder.append(list.get(i)).append(StrPool.COMMA);
        }
        String s = stringBuilder.append(list.get(NumberUtils.INTEGER_ZERO))
                + StrPool.DOT
                + numArr[NumberUtils.INTEGER_ONE];
        if (StrPool.DASHED.equals(s.substring(NumberUtils.INTEGER_ZERO, NumberUtils.INTEGER_ONE))
                && StrPool.COMMA.equals(s.substring(NumberUtils.INTEGER_ONE, NumberUtils.INTEGER_TWO))) {
            s = s.replaceFirst(StrPool.COMMA, CharSequenceUtil.EMPTY);
        }
        return s;
    }


    /**
     * 获取解密
     *
     * @param str str
     * @return {@link String }
     */
    public static BigDecimal decryptToDecimal(String str) {
        if (StringUtils.isBlank(str)) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(AESEncryptor.justDecrypt(str));
    }

    /**
     * 返回占比
     *
     * @param s 除数字符串占比
     * @return 商字符串占比
     */
    public String proportion(String s) {
        // 为空直接返回即可
        if (!Optional.ofNullable(s).isPresent()) {
            return ZERO_PERCENT_INT;
        }
        // 为0直接返回
        BigDecimal b = new BigDecimal(s);
        if (b.compareTo(BigDecimal.ZERO) == 0) {
            return ZERO_PERCENT;
        }
        return b.multiply(new BigDecimal("100")).setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP) + "%";
    }

    /**
     * 返回占比整数
     *
     * @param s 除数字符串占比
     * @return 商字符串占比
     */
    public String proportionInt(String s) {
        // 为空直接返回即可
        if (!Optional.ofNullable(s).isPresent()) {
            return null;
        }
        // 为0直接返回
        BigDecimal b = new BigDecimal(s);
        if (b.compareTo(BigDecimal.ZERO) == 0) {
            return ZERO_PERCENT_INT;
        }
        return b.multiply(new BigDecimal("100")).setScale(NumberUtils.INTEGER_ZERO, RoundingMode.HALF_UP) + "%";
    }
}
