/*
 * @Author: <PERSON> l<PERSON>@goktech.cn
 * @Date: 2025-05-14 14:17:38
 * @LastEditors: <PERSON> <EMAIL>
 * @LastEditTime: 2025-05-14 18:10:17
 * @FilePath: \display-platform\gok-business-pms\gok-business-pms-biz\src\main\java\com\gok\pboot\pms\eval\enums\EvalIndexTypeEnum.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.gok.pboot.pms.eval.enums;

import com.gok.pboot.pms.enumeration.ValueEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 项目评价指标类别枚举
 *
 * <AUTHOR>
 * @create 2025/05/12
 **/
@Getter
@AllArgsConstructor
public enum EvalIndexTypeEnum implements ValueEnum<Integer> {

    COST_INDEX(0, "成本指标"),
    PROCESS_INDEX(1, "进度指标"),
    QUALITY_INDEX(2, "质量指标"),
    PROJECT_WORK(3, "项目工作"),
    PROFESSIONAL_BEHAVIOR(4, "职业行为"),
    ;

    private final Integer value;
    private final String name;

}
