package com.gok.pboot.pms.cost.entity.vo;

import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.math.NumberUtils;

/**
 * 异常工单统计信息
 *
 * <AUTHOR>
 * @date 2025/01/15
 */
@Data
@Builder
public class CostTaskAbnormalCountVO {
    /**
     * 全部数量
     */
    private Integer totalCount;

    /**
     * 未提交数量
     */
    private Integer unSubmittedCount;

    /**
     * 未审核数量
     */
    private Integer unReviewedCount;

    /**
     * 未拆解数量
     */
    private Integer unDecomposedCount;

    /**
     * 未评价数量
     */
    private Integer unEvaluatedCount;


    public static CostTaskAbnormalCountVO empty() {
        return CostTaskAbnormalCountVO.builder()
                .totalCount(NumberUtils.INTEGER_ZERO)
                .unSubmittedCount(NumberUtils.INTEGER_ZERO)
                .unReviewedCount(NumberUtils.INTEGER_ZERO)
                .unDecomposedCount(NumberUtils.INTEGER_ZERO)
                .unEvaluatedCount(NumberUtils.INTEGER_ZERO)
                .build();
    }
} 