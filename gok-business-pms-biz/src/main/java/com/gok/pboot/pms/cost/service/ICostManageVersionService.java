package com.gok.pboot.pms.cost.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.cost.entity.domain.CostManageVersion;
import com.gok.pboot.pms.cost.entity.dto.CostConfirmDTO;
import com.gok.pboot.pms.cost.entity.dto.CostManageVersionDTO;
import com.gok.pboot.pms.cost.entity.vo.CostManageVersionVO;
import com.gok.pboot.pms.cost.entity.vo.PreCostConfirmVO;
import com.gok.pboot.pms.cost.enums.ChangeContentEnum;
import com.gok.pboot.pms.cost.enums.CostManageVersionEnum;

import java.util.List;
import java.util.Map;

/**
 * 成本管理版本记录 服务类
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
public interface ICostManageVersionService extends IService<CostManageVersion> {

    /**
     * 售前成本确认
     *
     * @param costConfirmDTO 成本确认 DTO
     */
    void preSaleCostConfirm(CostConfirmDTO costConfirmDTO);

    /**
     * 售前成本估算消息推送
     *
     * @param costManageVersionId 成本管理版本 ID
     */
    void preSaleEstimateMessagePush(Long costManageVersionId);


    /**
     * 售前成本确认查询
     *
     * @param projectId
     * @return
     */
    PreCostConfirmVO queryPreCostConfirm(Long projectId);

    /**
     * 获取历史版本数据
     *
     * @param pageRequest 页面请求
     * @param request     请求
     * @return {@link Page }<{@link CostManageVersionVO }>
     */
    Page<CostManageVersionVO> getHistoryVersions(PageRequest pageRequest, CostManageVersionDTO request);

    /**
     * 获取当前成本管理版本记录
     *
     * @param versionEnum version 类型 enum
     * @param projectId   项目ID
     * @return {@link CostManageVersion }
     */
    CostManageVersion getCurrentCostManageVersion(CostManageVersionEnum versionEnum, Long projectId);

    /**
     * 查询项目版本
     *
     * @param projectId
     * @return
     */
    List<CostManageVersionVO> queryProjectVersion(Long projectId);

    /**
     * 判断项目是否存在指定类型的成本估算版本
     *
     * @param projectId 项目ID
     * @param isDraft 是否草稿态
     * @return Map<Integer, Boolean> key为{@link ChangeContentEnum}，value为是否存在
     */
    Map<Integer, Boolean> checkCostBudgetTypeExist(Long projectId, boolean isDraft);

    /**
     * 根据已确认售前成本创建项目总成本
     *
     * @return
     */
    List<Long> saveByPreSalesCost();

    /**
     * 获取当前最新的成本管理版本记录
     *
     * @param request
     * @return {@link CostManageVersion }
     */
    CostManageVersion getLatestCostManageVersion(CostManageVersionDTO request);

}
