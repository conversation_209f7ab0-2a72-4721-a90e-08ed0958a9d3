package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description 筛选的维度
 * @since 2024/7/23
 */
@Getter
@AllArgsConstructor
public enum DimensionEnum implements ValueEnum<Integer> {

    /**
     * 人员维度
     */
    USER(0, "人员维度"),

    /**
     * 任务维度
     */
    TASK(1, "任务维度"),

    /**
     * 日期维度
     */
    DATE(2, "日期维度");

    private final Integer value;
    private final String name;
}
