package com.gok.pboot.pms.cost.entity.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 工单审核分页查询VO类
 *
 * <AUTHOR>
 * @create 2025/01/20
 **/
@Data
public class CostDeliverApprovalVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 待审核任务数
     */
    private Integer taskNum;

    /**
     * 预算成本
     */
    private BigDecimal budgetCost;

}
