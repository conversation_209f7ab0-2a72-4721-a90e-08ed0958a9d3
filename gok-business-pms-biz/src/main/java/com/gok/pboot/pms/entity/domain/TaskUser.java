package com.gok.pboot.pms.entity.domain;


import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 任务-人员多对多关联
 *
 * <AUTHOR>
 * @since 2022-08-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(TaskUser.ALIAS)
public class TaskUser extends BeanEntity<Long> {

    public static final String ALIAS = "mhour_task_user";
    /**
    * 任务ID
    */
    private Long taskId;
    /**
    * 人员ID
    */
    private Long userId;
    /**
     * 人员姓名
     */
    private String userName;


}
