package com.gok.pboot.pms.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.common.base.PropertyFilters;
import com.gok.pboot.pms.entity.dto.SpecialSettingAddDTO;
import com.gok.pboot.pms.entity.vo.SpecialSettingUserVO;
import com.gok.pboot.pms.entity.vo.SpecialSettingVO;
import com.gok.pboot.pms.service.ISpecialSettingService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

/**
 * - 特殊配置控制器 -
 * @menu 配置白名单
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("specialSetting")
@AllArgsConstructor
public class SpecialSettingController {

    private final ISpecialSettingService service;

    /**
     * 分页查询
     * @param pageRequest
     * @param request
     * @return
     */
    @GetMapping("/findPage")
    @PreAuthorize("@pms.hasPermission('STAFFING_WHITELIST_CONFIG')")
    public ApiResult<Page<SpecialSettingVO>> findPage(PageRequest pageRequest, HttpServletRequest request){
        return ApiResult.success(
                service.queryPage(pageRequest, PropertyFilters.get(request, true))
        );
    }

    /**
     * 添 加
     * @param dto
     * @return
     */
    @PostMapping("/add")
    public ApiResult<Void> add(@RequestBody @Valid SpecialSettingAddDTO dto){
        service.add(dto);
        return ApiResult.success(null);
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    public ApiResult<Void> delete(@PathVariable("id") Long id){
        service.delete(id);

        return ApiResult.success(null);
    }

    /**
     * 获取白名单用户基本信息
     * @return {@link ApiResult},{@link List},{@link SpecialSettingUserVO}
     */
    @GetMapping("/findUserList")
    @PreAuthorize("@pms.hasPermission('STAFFING_WHITELIST_CONFIG')")
    public ApiResult<List<SpecialSettingUserVO>> findUserList(){
        return ApiResult.success(service.findUserList());
    }
}
