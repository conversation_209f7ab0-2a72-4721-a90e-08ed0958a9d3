package com.gok.pboot.pms.entity.vo;

import com.gok.pboot.pms.Util.DailyPaperDateUtils;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.entity.DailyPaperEntry;
import com.gok.pboot.pms.entity.domain.TomorrowPlanPaperEntry;
import com.gok.pboot.pms.enumeration.ApprovalStatusEnum;
import com.gok.pboot.pms.enumeration.DailyPaperFillingStateEnum;
import com.gok.pboot.pms.enumeration.WorkTypeEnum;
import lombok.*;

import javax.annotation.Nullable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.HashMap;

/**
 * 日报审核项目维度条目
 *
 * <AUTHOR>
 * @version 1.3.2
 */
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class DailyReviewProjectViewVO {

    /**
     * ID
     */
    private String id;

    /**
     * 提交人姓名
     */
    private String userRealName;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 工作内容
     */
    private String description;

    /**
     * 昨日计划
     */
    private String yesterdayPlan;

    /**
     * 工时类型
     * @see com.gok.pboot.pms.enumeration.WorkTypeEnum
     */
    private Integer workType;

    /**
     * 工时类型名称
     */
    private String workTypeName;

    /**
     * 填报日期
     */
    private LocalDate submissionDate;

    /**
     * 填报日期带周
     */
    private String submissionDateFormatted;


    /**
     * 提交日期
     */
    private LocalDate commitDate;

    /**
     * 填报状态
     * @see com.gok.pboot.pms.enumeration.DailyPaperFillingStateEnum
     */
    private Integer fillingState;

    /**
     * 填报状态名称
     */
    private String fillingStateName;

    /**
     * 正常工时
     */
    private BigDecimal normalHours;

    /**
     * 加班工时
     */
    private BigDecimal addedHours;

    /**
     * 审核状态
     * @see com.gok.pboot.pms.enumeration.ApprovalStatusEnum
     */
    private Integer approvalStatus;

    /**
     * 审核状态名称
     */
    private String approvalStatusName;

    /**
     * 审核人
     */
    private String approvalName;

    /**
     * 假期类型：1-法定节假日 0-普通休息日
     */
    private Integer holidayType;

    public static DailyReviewProjectViewVO of(
            DailyPaperEntry entry, @Nullable TomorrowPlanPaperEntry lastDayPlanEntry, Integer fillingState,
            HashMap<LocalDate, Integer> holidayMap
    ) {
        DailyReviewProjectViewVO result = new DailyReviewProjectViewVO();
        Integer workType = entry.getWorkType();
        Timestamp ctime = entry.getCtime();
        BigDecimal normalHours = entry.getNormalHours();
        BigDecimal addedHours = entry.getAddedHours();
        Integer approvalStatus = entry.getApprovalStatus();

        result.setId(String.valueOf(entry.getId()));
        result.setUserRealName(entry.getUserRealName());
        result.setTaskName(entry.getTaskName());
        if (workType != null) {
            result.setWorkType(workType);
            result.setWorkTypeName(EnumUtils.getNameByValue(WorkTypeEnum.class, workType));
        }
        result.setSubmissionDate(entry.getSubmissionDate());
        if (ctime != null) {
            result.setCommitDate(ctime.toLocalDateTime().toLocalDate());
        }
        result.setFillingState(fillingState);
        result.setFillingStateName(EnumUtils.getNameByValue(DailyPaperFillingStateEnum.class, fillingState));
        result.setNormalHours(normalHours == null ? BigDecimal.ZERO : normalHours);
        result.setAddedHours(addedHours == null ? BigDecimal.ZERO : addedHours);
        result.setDescription(entry.getDescription());
        if (lastDayPlanEntry != null) {
            result.setYesterdayPlan(lastDayPlanEntry.getDescription());
        }
        result.setApprovalStatus(approvalStatus);
        result.setApprovalStatusName(EnumUtils.getNameByValue(ApprovalStatusEnum.class, approvalStatus));
        result.setApprovalName(entry.getModifier());

        result.setSubmissionDateFormatted(DailyPaperDateUtils.asDateString(result.getSubmissionDate()));
        result.setHolidayType(holidayMap.get(result.getSubmissionDate()));

        return result;
    }
}
