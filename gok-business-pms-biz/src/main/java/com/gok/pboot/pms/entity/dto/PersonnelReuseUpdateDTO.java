package com.gok.pboot.pms.entity.dto;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * 人才复用
 *
 * <AUTHOR>
 * @since 2022-08-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class PersonnelReuseUpdateDTO  {

    /**
     * 人才复用表明细ID
     */
    @NotNull(message = "编号不能为空")
    private Long id;

    /**
    * 汇总工时（人天）
    */
    @NotNull(message = "汇总工时不能为空")
    private BigDecimal aggregatedDays;

    /**
    * 备注
    */
    private String remark;

    /**
     * 修改人
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long modifierId;
    /**
     * 修改人
     */
    private String modifier;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp mtime;

    /**
     * 审核状态（0=未提交，1=已退回，2=待审核，3=不通过，4=已通过） 详见ApprovalStatusEnum
     */
    private Integer approvalStatus;
}
