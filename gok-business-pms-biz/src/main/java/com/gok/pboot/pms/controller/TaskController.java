package com.gok.pboot.pms.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.BaseController;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.common.base.PropertyFilters;
import com.gok.pboot.pms.entity.domain.Task;
import com.gok.pboot.pms.entity.dto.TaskAddDTO;
import com.gok.pboot.pms.entity.dto.TaskAddUserDTO;
import com.gok.pboot.pms.entity.dto.TaskUpdateDTO;
import com.gok.pboot.pms.entity.vo.OperatingRecordPageVO;
import com.gok.pboot.pms.entity.vo.TaskInDailyPaperEntryVO;
import com.gok.pboot.pms.entity.vo.TaskInfoVO;
import com.gok.pboot.pms.service.ITaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @description 任务 前端控制器
 * @menu PMS业务模块-任务
 * @since 2022-08-19
 */
@Slf4j
@RestController
@RequestMapping("task")
public class TaskController extends BaseController {

    public final ITaskService service;

    @Autowired
    public TaskController(ITaskService service) {
        this.service = service;
    }

    /**
     * 新增
     *
     * @param entity
     * @return {@link ApiResult}
     */
    @PostMapping("/add")
    public ApiResult<String> newAdd(@RequestBody TaskAddDTO entity) {
        return service.newAdd(entity);
    }

    /**
     * 添加人员
     *
     * @return {@link ApiResult}
     */
    @PostMapping("/add/taskUser")
    public ApiResult<String> addTaskUser(@RequestBody TaskAddUserDTO entity) {
        return service.addTaskUser(entity);
    }

    /**
     * 编辑
     *
     * @param entity
     * @return {@link ApiResult}
     */
    @PostMapping("/update")
    public ApiResult<String> update(@RequestBody TaskUpdateDTO entity) {
        return service.update(entity);
    }

    /**
     * 逻辑删除
     *
     * @param id
     * @return {@link ApiResult}
     */
    @DeleteMapping("/del/{id}")
    public ApiResult<String> del(@PathVariable("id") Long id) {
        return service.deleteByLogic(id);
    }


    /**
     * 分页查询项目下的任务
     *
     * @param pageRequest
     * @param id          项目id
     * @param request
     * @return {@link ApiResult<Page<Task>>}
     */
    @GetMapping("/findPage/{id}")
    public ApiResult<Page<TaskInfoVO>> findProjectIdPage(@PathVariable("id") Long id, PageRequest pageRequest, HttpServletRequest request) {
        return success(service.findProjectIdPage(id, pageRequest, PropertyFilters.get(request)));
    }

    /**
     * 分页查询任务下的操作记录
     *
     * @param pageRequest
     * @param id          任务id
     * @return {@link ApiResult<Page<Task>>}
     */
    @GetMapping("/findOperatingRecordPage/{id}")
    public ApiResult<Page<OperatingRecordPageVO>> findOperatingRecordPage(@PathVariable("id") Long id, PageRequest pageRequest) {
        return success(service.findOperatingRecordPage(id, pageRequest));
    }

    /**
     * ~ 根据项目ID查询目标项目下当前用户可用的任务（已废弃，不使用该接口） ~
     *
     * @param projectId 项目ID
     * @return com.gok.pboot.pms.common.base.ApiResult<java.util.List < com.gok.pboot.pms.entity.vo.TaskInDailyPaperEntryVO>>
     * <AUTHOR>
     * @date 2022/8/26 9:37
     */
    @Deprecated
    @GetMapping("/availableTasksForCurrentUserByProjectId/{projectId}")
    public ApiResult<List<TaskInDailyPaperEntryVO>> availableTasksForCurrentUserByProjectId(
            @PathVariable("projectId") Long projectId
    ) {
        return success(service.findAvailableTasksForCurrentUserByProjectId(projectId));
    }

    /**
     * @create by yzs at 2023/4/19
     * @description:查询项目下的任务
     * @param: id
     * @return: com.gok.pboot.pms.common.base.ApiResult<java.util.List < com.gok.pboot.pms.entity.vo.TaskInfoVO>>
     */
    @GetMapping("/findByprojectId/{id}")
    public ApiResult<List<Task>> findByprojectId(@PathVariable("id") Long id) {
        return success(service.findByprojectId(id));
    }
}
