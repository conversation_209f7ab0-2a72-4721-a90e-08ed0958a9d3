package com.gok.pboot.pms.cost.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.cost.entity.domain.CostTaskCategoryManagement;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 类别管理表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2024/03/26
 */
@Mapper
public interface CostTaskCategoryManagementMapper extends BaseMapper<CostTaskCategoryManagement> {

    /**
     * 获取最大的工单类别值
     *
     * @return 最大工单类别值
     */
    Integer getMaxTaskCategory();

    /**
     * 获取最大的排序值
     *
     * @return 最大排序值
     */
    Integer getMaxSort();

    /**
     * 获取所有类别列表
     *
     * @return {@link List }<{@link CostTaskCategoryManagement }>
     */
    List<CostTaskCategoryManagement> getAllCategoryList();
}