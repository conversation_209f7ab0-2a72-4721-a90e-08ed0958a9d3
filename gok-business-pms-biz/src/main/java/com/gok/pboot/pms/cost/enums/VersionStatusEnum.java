package com.gok.pboot.pms.cost.enums;

import com.gok.pboot.pms.enumeration.ValueEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 版本状态 枚举
 *
 * <AUTHOR>
 * @date 2025/01/07
 */
@AllArgsConstructor
@Getter
public enum VersionStatusEnum implements ValueEnum<Integer> {

    /**
     * 当前版本
     */
    CURRENT(0, "当前版本"),

    /**
     * 历史版本
     */
    HISTORY(1, "历史版本");

    private final Integer value;

    private final String name;
}
