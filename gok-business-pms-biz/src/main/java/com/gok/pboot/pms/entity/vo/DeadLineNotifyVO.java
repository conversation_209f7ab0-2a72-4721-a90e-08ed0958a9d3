package com.gok.pboot.pms.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 任务即将结束通知vo
 *
 * <AUTHOR>
 * @date 2023/8/25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeadLineNotifyVO {

    /**
     * 负责人id
     */
    Long managerUserId;

    /**
     * 负责人姓名
     */
    String managerUserName;

    /**
     * 通知内容
     */
    String info;

    public static DeadLineNotifyVO of(String projectName, String title, Long managerUserId, String managerUserName) {
        DeadLineNotifyVO result = new DeadLineNotifyVO();
        String info = "{" + projectName + "}-{" + title + "}，即将逾期，请到工作台-个人面板-我的任务处理";

        result.setManagerUserId(managerUserId);
        result.setManagerUserName(managerUserName);
        result.setInfo(info);
        return result;
    }
}
