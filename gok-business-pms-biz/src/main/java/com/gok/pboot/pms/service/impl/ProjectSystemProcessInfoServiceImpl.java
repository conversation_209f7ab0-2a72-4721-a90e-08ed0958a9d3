package com.gok.pboot.pms.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.components.common.user.PigxUser;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.entity.domain.ProjectSystemProcessInfo;
import com.gok.pboot.pms.entity.dto.ProjectSystemProcessInfoDTO;
import com.gok.pboot.pms.entity.vo.ProjectProcessInfoFindPageVO;
import com.gok.pboot.pms.enumeration.ProcessInfoStatusEnum;
import com.gok.pboot.pms.mapper.ProjectSystemProcessInfoMapper;
import com.gok.pboot.pms.service.IProjectSystemProcessInfoService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 项目系统过程动态信息 服务实现类
 *
 * <AUTHOR>
 * @since 2023-07-14
 **/
@Slf4j
@Service
@RefreshScope
public class ProjectSystemProcessInfoServiceImpl extends ServiceImpl<ProjectSystemProcessInfoMapper, ProjectSystemProcessInfo>
        implements IProjectSystemProcessInfoService {

    /**
     * OA获取Token地址
     */
    @Value("${oa.url.getToken}")
    private String oaGetTokenUrl;

    /**
     * OA获取Token appId请求参数
     */
    @Value("${oa.appId}")
    private String appId;

    /**
     * OA获取Token 失败响应
     */
    private final String OA_FAILED_RESPONSE = "Token获取失败";

    @Override
    public Page<ProjectProcessInfoFindPageVO> findPage(PageRequest pageRequest, Map<String, Object> filter) {
        Page<ProjectProcessInfoFindPageVO> pageResult =
                baseMapper.findListPage(new Page<>(pageRequest.getPageNumber(), pageRequest.getPageSize()), filter);
        pageResult.getRecords()
                .forEach(o -> {
                    o.setStatusTxt(EnumUtils.getNameByValue(ProcessInfoStatusEnum.class, Integer.valueOf(o.getStatus())));
                    o.setFilingDateTime(o.getCtime());
                });
        return pageResult;
    }

    @Override
    public void innerBatchSave(List<ProjectSystemProcessInfoDTO> dtoList) {
        if (CollUtil.isEmpty(dtoList)) {
            return;
        }
        List<ProjectSystemProcessInfo> entityList = new ArrayList<>();
        dtoList.forEach(o -> {
            ProjectSystemProcessInfo entityItem = ProjectSystemProcessInfo.buildSave(o);
            entityList.add(entityItem);
        });
        baseMapper.batchSave(entityList);
    }

    @Override
    @SneakyThrows
    public String getRedirectOaToken() {
        HttpRequest httpRequest = HttpUtil.createPost(oaGetTokenUrl);

        // 设置头字段
        httpRequest.header("application/x-www-form-urlencoded");
        PigxUser user = SecurityUtils.getUser();
        if (!Optional.ofNullable(user).isPresent()) {
            log.info("用户不存在，获取跳转token失败");
            return StrUtil.EMPTY;
        }
        String loginId = user.getUsername();

        // 设置内容信息
        HashMap paramMap = new HashMap();
        paramMap.put("appid", appId);
        paramMap.put("loginid", loginId);
        httpRequest.form(paramMap);

        // 发送请求
        HttpResponse response = httpRequest.execute();

        // 处理返回结果
        String resCont = response.body();
        log.info("请求OA Token接口响应结果为：{}", resCont);
        if (StrUtil.contains(resCont, OA_FAILED_RESPONSE)) {
            log.info("接口请求失败，获取跳转token失败");
            return StrUtil.EMPTY;
        }

        return resCont;
    }

}
