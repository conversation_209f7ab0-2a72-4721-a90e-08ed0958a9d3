package com.gok.pboot.pms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.entity.DailyPaperEntry;
import com.gok.pboot.pms.entity.vo.ProjectTaskFindPageVO;
import com.gok.pboot.pms.entity.vo.ProjectWorkHoursVo;
import com.gok.pboot.pms.entity.vo.WorkHoursDetailVO;
import com.gok.pboot.pms.enumeration.DimensionEnum;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 项目详情-工时统计
 *
 * <AUTHOR>
 * @since 2023-07-17
 **/
@Mapper
public interface ProjectWorkHoursMapper extends BaseMapper<DailyPaperEntry> {

    /**
     * 根据项目ID和时间分页获取每人每天每个任务工时信息
     * 包含已审、未审的日报数据
     *
     * @param page   分页请求
     * @param filter 查询参数
     * @return {@link Page}{@link DailyPaperEntry} 每人每天每个任务日报条目分页数据
     */
    @Deprecated
    Page<ProjectTaskFindPageVO> findApprovalPageByProjectIdAndBetween(Page<DailyPaperEntry> page,
                                                                      @Param("filter") Map<String, Object> filter);

    /**
     * 根据项目IDs查询JOB_ACTIVITY
     * 包含已审、未审的日报数据
     *
     * @param filter 查询参数
     * @param voList 每人每天每个任务工时信息集合
     * @return key - 日报条目id  value - 任务分页查询结果Vo类集合[]
     */
    @MapKey("id")
    Map<Long, WorkHoursDetailVO> getYesterdayPlan(@Param("filter") Map<String, Object> filter,
                                                  @Param("voList") List<ProjectTaskFindPageVO> voList);

    /**
     * 根据任务id集合和提交人id集合获取对应时间段内数据
     *
     * @param taskIdList 任务id集合
     * @param userIdList 提交人id集合
     * @param startDate  开始时间
     * @param endDate    结束时间
     * @return
     */
    List<ProjectTaskFindPageVO> findByTaskIdAndUserIdBetween(@Param("taskIdList") List<Long> taskIdList,
                                                             @Param("userIdList") List<Long> userIdList,
                                                             @Param("startDate") LocalDate startDate,
                                                             @Param("endDate") LocalDate endDate);

    /**
     * 统计单个项目工时数据
     *
     * @param filter 查询参数
     * @return
     */
    ProjectWorkHoursVo calculateWorkHours(@Param("filter") Map<String, Object> filter);

    /**
     * 批量统计工时数据
     *
     * @param filterList 查询参数集合
     * @return key-projectId,value-周报工时数据
     */
    List<ProjectWorkHoursVo> batchCalculateAvgWorkHours(@Param("filterList") List<Map<String, Object>> filterList);

    List<ProjectTaskFindPageVO> getTaskByTaskNameLike(@Param("projectId") Long projectId);

    /**
     * 条件分组查询（用户维度或任务维度分组）
     *
     * @param page   分页参数
     * @param filter 条件参数
     * @return 分组后数据
     */
    default Page<ProjectTaskFindPageVO> findGroupByDimension(Page<ProjectTaskFindPageVO> page, @Param("filter") Map<String, Object> filter) {
        Integer dimension = (Integer) filter.get("dimension");
        if (DimensionEnum.USER.getValue().equals(dimension)) {
            return findGroupByUserId(page, filter);
        } else if (DimensionEnum.TASK.getValue().equals(dimension)) {
            return findGroupByTaskId(page, filter);
        }
        return new Page<>();
    }

    Page<ProjectTaskFindPageVO> findGroupByUserId(Page<ProjectTaskFindPageVO> page, @Param("filter") Map<String, Object> filter);

    Page<ProjectTaskFindPageVO> findGroupByTaskId(Page<ProjectTaskFindPageVO> page, @Param("filter") Map<String, Object> filter);

    /**
     * 分页查询工时详情（不同维度）
     *
     * @param page   分页对象
     * @param filter 参数列表
     * @return 工时详情
     */
    Page<WorkHoursDetailVO> getDetailByDimension(Page<WorkHoursDetailVO> page, @Param("filter") Map<String, Object> filter);

    /**
     * @param filter 过滤参数
     * @param records 查询参数
     * @return 昨日计划内容表
     */
    List<WorkHoursDetailVO> getYesterdayPlan2(@Param("filter") Map<String, Object> filter, @Param("records") List<WorkHoursDetailVO> records);
}
