package com.gok.pboot.pms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.entity.domain.ProjectTaske;
import com.gok.pboot.pms.entity.vo.MyTaskVo;
import com.gok.pboot.pms.entity.vo.ProjectTaskFindPageVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 项目任务Mapper
 *
 * <AUTHOR>
 * @date 2024/01/19
 */
@Mapper
public interface ProjectTaskeMapper extends BaseMapper<ProjectTaske> {

    /**
     * 根据过滤条件获取分页列表
     *
     * @param page   分页对象
     * @param filter 过滤条件
     * @param ids    id列表
     * @return {@link Page}<{@link ProjectTaske}>
     */
    Page<ProjectTaske> findListItem(Page<ProjectTaske> page, @Param("filter") Map<String, Object> filter, @Param("ids") List<Long> ids);

    /**
     * 获取我的任务
     *
     * @param page   分页对象
     * @param filter 过滤条件
     * @return {@link Page}<{@link MyTaskVo}>
     */
    Page<MyTaskVo> findMyTask(Page<ProjectTaske> page, @Param("filter") Map<String, Object> filter);

    /**
     * 获取我的任务
     *
     * @param userId 用户id
     * @return int
     */
    int findMyTaskCount(@Param("userId") Long userId);

    /**
     * 根据过滤条件获取分页列表（铁三角）
     *
     * @param page 分页对象
     * @param filter 过滤条件
     * @return {@link Page}<{@link ProjectTaske}>
     */
    Page<ProjectTaske> findListByAllItem(Page<ProjectTaske> page, @Param("filter") Map<String, Object> filter);

    /**
     * 根据过滤条件获取分页列表（任务负责人）
     *
     * @param page 分页对象
     * @param filter 过滤条件
     * @return {@link Page}<{@link ProjectTaske}>
     */
    Page<ProjectTaske> findListByOwnItem(Page<ProjectTaske> page, @Param("filter") Map<String, Object> filter);

    /**
     * 根据过滤条件获取分页列表（任务参与人）
     *
     * @param page 分页对象
     * @param filter 过滤条件
     * @return {@link Page}<{@link ProjectTaske}>
     */
    Page<ProjectTaske> findListByMyItem(Page<ProjectTaske> page, @Param("filter") Map<String, Object> filter);

    /**
     * 通过id判断任务是否存在
     *
     * @param id 任务id
     * @return int
     */
    int isExistById(@Param("id") Long id);

    /**
     * 通过id逻辑删除任务
     *
     * @param id 任务id
     * @return int
     */
    int logicDeleteById(@Param("id") Long id);

    /**
     * 根据id结束任务
     *
     * @param id      任务id
     * @param endDate 结束时间
     * @return int
     */
    int finishById(@Param("id") Long id, @Param("endDate") LocalDate endDate);

    /**
     * 根据id重启任务
     *
     * @param id 任务id
     * @return int
     */
    int restartById(@Param("id") Long id);

    /**
     * 根据任务ID查询项目ID
     * @param taskId 任务ID
     * @return 项目ID
     */
    Long findProjectIdByTaskId(Long taskId);

    /**
     * 通过项目id获取任务列表
     *
     * @param projectId 项目id
     * @return {@link List}<{@link ProjectTaske}>
     */
    List<ProjectTaske> findListByProjectId(@Param("projectId") Long projectId);

    /**
     * 根据项目ID列表查询任务ID列表
     * @param projectIds 项目ID列表
     * @return 任务ID列表
     */
    List<Long> findTaskIdByProjectIds(@Param("projectIds") Collection<Long> projectIds);

    /**
     * 根据用户id和项目id列表获取日报所需信息
     *
     * @param userId     用户id
     * @param projectIds 项目id列表
     * @param date       日期
     * @return {@link List}<{@link ProjectTaske}>
     */
    List<ProjectTaske> findByUserIdAndProjectIdForEntry(@Param("userId") Long userId, @Param("projectIds") List<Long> projectIds, @Param("date") LocalDate date);

    /**
     * 通过任务id列表获取
     *
     * @param taskIds 任务id列表
     * @return {@link List}<{@link ProjectTaske}>
     */
    List<ProjectTaske> findByTaskIds(@Param("taskIds") Collection<Long> taskIds);

    /**
     * 批量更新任务开始时间（首次日报填报时间）
     *
     * @param list 任务列表
     * @return int
     */
    int batchUpdateStartDate(@Param("list") List<ProjectTaske> list);

    /**
     * 根据项目id列表获取任务
     *
     * @param projectIds 项目id列表
     * @return {@link List}<{@link ProjectTaske}>
     */
    List<ProjectTaske> findCreatedDefaultByProjectIds(@Param("projectIds") List<Long> projectIds);

    /**
     * 批量保存任务
     *
     * @param list 任务列表
     * @return int
     */
    int batchSave(@Param("list") List<ProjectTaske> list);

    /**
     * 批量更新任务
     * @param list 任务列表
     */
    void batchUpdate(@Param("list") List<ProjectTaske> list);

    /**
     * 通过项目id列表获取未结束任务列表
     *
     * @param projectIds 项目id列表
     * @return {@link List}<{@link ProjectTaske}>
     */
    List<ProjectTaske> findUnfinishedByProjectIds(@Param("projectIds") List<Long> projectIds);

    /**
     * 批量结束
     *
     * @param ids id列表
     * @return int
     */
    int batchFinish(@Param("ids") List<Long> ids);

    /**
     * 查找未结束任务id列表
     *
     * @param taskIds 任务id
     * @param userId 用户id
     * @return {@link List}<{@link Long}>
     */
    Set<Long> findUnFinishedTaskIds(@Param("taskIds") List<Long> taskIds, @Param("userId") Long userId);


    /**
     * 获取任务负责人id、name
     * @param taskIds 任务id列表
     * @return {@link ProjectTaskFindPageVO}
     */
    List<ProjectTaskFindPageVO> findTaskLeaders(@Param("taskIds") Collection<Long> taskIds);

}
