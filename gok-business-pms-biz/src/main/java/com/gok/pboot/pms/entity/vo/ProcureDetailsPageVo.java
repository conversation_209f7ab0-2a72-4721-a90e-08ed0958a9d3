package com.gok.pboot.pms.entity.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.gok.pboot.pms.Util.DecimalFormatUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.time.LocalDate;

/**
 * 项目采购付款明细
 *
 * <AUTHOR>
 * @date 2023/08/23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class ProcureDetailsPageVo {

    /**
     * 请求名
     */
    @ExcelIgnore
    private String requestName;

    /**
     * 申请人id
     */
    @ExcelIgnore
    private Long ApplicantID;

    /**
     * 申请人
     */
    @ExcelProperty({"申请人"})
    @ColumnWidth(20)
    private String applicant;

    /**
     * 付款日期
     */
    @ExcelProperty({"付款日期"})
    @ColumnWidth(50)
    private LocalDate fkrq;

    /**
     * 科目名称
     */
    @ExcelProperty({"科目名称"})
    @ColumnWidth(20)
    private String kmmc;

    /**
     * 费用项类别id
     */
    @ExcelIgnore
    private Integer fyxlb;

    /**
     * 费用项类别
     */
    @ExcelProperty({"费用项类别"})
    @ColumnWidth(20)
    private String fyxlbValue;

    /**
     * 付款金额（含税）
     */
    @ExcelProperty({"付款金额(含税)"})
    @ColumnWidth(20)
    private String je;

    /**
     * 付款金额（不含税）
     */
    @ExcelProperty({"付款金额(不含税)"})
    @ColumnWidth(20)
    private String fkjebhs;

    public void setScale(int newScale, RoundingMode roundingMode, DecimalFormat decimalFormat) {
        this.je = DecimalFormatUtil.setAndValidate(je, newScale, roundingMode, decimalFormat);
        this.fkjebhs = DecimalFormatUtil.setAndValidate(fkjebhs, newScale, roundingMode, decimalFormat);
    }

}
