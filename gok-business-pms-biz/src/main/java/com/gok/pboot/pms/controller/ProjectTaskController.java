package com.gok.pboot.pms.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.common.security.annotation.Inner;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.common.base.PropertyFilters;
import com.gok.pboot.pms.entity.dto.ProjectTaskDTO;
import com.gok.pboot.pms.entity.dto.ProjectTaskTimeDTO;
import com.gok.pboot.pms.entity.vo.*;
import com.gok.pboot.pms.service.IProjectTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 项目任务管理
 *
 * <AUTHOR>
 * @date 2023/8/19
 */
@RestController
@RequestMapping("/projectTask")
public class ProjectTaskController {


    @Autowired
    private IProjectTaskService service;

    /**
     * 查询列表
     *
     * @param request 请求参数
     * @customParam filter_L_projectId 传入的项目id
     * @customParam filter_L_state 传入的状态参数，不传默认查所有
     * @customParam filter_S_title 模糊查询的任务标题
     * @customParam filter_S_managerUserName 模糊查询的负责人姓名
     * @return {@link ApiResult<List<ProjectTaskVO>>}
     */
    @GetMapping("/findList")
    public ApiResult<List<ProjectTaskVO>> findList(HttpServletRequest request) {
        Map<String, Object> map = PropertyFilters.get(request, true);
        return ApiResult.success(service.findList(map));
    }

    /**
     * 根据任务id查询单个任务详情（编辑任务时使用）
     * @param id 任务id
     * @return {@link ApiResult<ProjectTaskEditVo>}
     */
    @GetMapping("/findOne/{id}")
    public ApiResult<ProjectTaskEditVo> findOne(@PathVariable("id") Long id){
        return ApiResult.success(service.findOne(id));
    }

    /**
     * 创建任务
     * @param request 请求参数
     * @param result
     * @return 任务id
     */
    @PostMapping
    public ApiResult<Long> save(@Valid @RequestBody ProjectTaskDTO request, BindingResult result) {
        if (result.hasErrors()) {
            return ApiResult.failure(result.getAllErrors().get(0).getDefaultMessage());
        }
        return ApiResult.success(service.save(request));
    }

    /**
     * 编辑任务
     * @param request 请求参数
     * @param result
     * @return 任务id
     */
    @PutMapping
    public ApiResult<Long> update(@Valid @RequestBody ProjectTaskDTO request, BindingResult result) {
        if (result.hasErrors()) {
            return ApiResult.failure(result.getAllErrors().get(0).getDefaultMessage());
        }
        return ApiResult.success(service.update(request));
    }

    /**
     * 删除任务
     * @param id 传入的任务id
     * @return
     */
    @DeleteMapping("/{id}")
    public ApiResult<Boolean> deleteById(@PathVariable Long id) {
        return ApiResult.success(service.delete(id));
    }

    /**
     * 根据ID查询详情
     * @param id 任务ID
     * @return 任务详情 + 进展
     */
    @GetMapping("/details/{id}")
    public ApiResult<ProjectTaskDetailsVO> getDetailsById(@PathVariable("id") Long id){
        return ApiResult.success(service.findDetailsById(id));
    }

    /**
     * 任务状态统计
     * @param request 请求参数
     * @customParam filter_L_projectId 传入的项目id
     * @customParam filter_S_title 模糊查询的任务标题
     * @customParam filter_S_managerUserName 模糊查询的负责人姓名
     * @return {@link ApiResult<ProjectTaskStateVO>} 任务状态统计信息
     */
    @GetMapping("/stateCount")
    public ApiResult<ProjectTaskStateVO> getStateCount(HttpServletRequest request){
        Map<String, Object> map = PropertyFilters.get(request, true);
        return ApiResult.success(service.findStateCount(map));
    }

    /**
     * 任务列表分页（树结构）
     * @param pageRequest 分页请求参数
     * @customParam pageNumber 页码
     * @customParam pageSize 页长
     * @param request 请求参数
     * @customParam filter_L_projectId 传入的项目id
     * @customParam filter_L_state 传入的状态参数，不传默认查所有
     * @return {@link ApiResult<Page<ProjectTaskPageVO>>}
     */
    @GetMapping("/findPage")
    public ApiResult<Page<ProjectTaskPageVO>> findPage(PageRequest pageRequest,HttpServletRequest request) {
        return ApiResult.success(
                service.findPage(pageRequest,PropertyFilters.get(request,true))
        );
    }

    /**
     * 查询负责人项目数量
     * @return {@link ApiResult<Integer>}
     */
    @GetMapping("/findChargeTaskNum")
    public ApiResult<Integer> findChargeTaskNum(){
        return service.findChargeTaskNum();
    }

    /**
     * 分页查询负责人任务表
     * @param pageRequest 分页请求实体
     * @customParam pageNumber 页码
     * @customParam pageSize   页长
     * @customParam filter_S_projectName 项目名称（模糊查询）
     * @return 返回值
     */
    @GetMapping("/findPageOfCharge")
    public ApiResult<Page<ProjectTaskOfChargeVO>> findPageOfCharge(PageRequest pageRequest, HttpServletRequest request){
        return ApiResult.success(service.findPageOfCharge(pageRequest,PropertyFilters.get(request,true)));
    }

    /** 
     * 更新任务实际开始时间和结束时间
     * @param dto 传参
     * @return {@link ApiResult<Long>}
     */
    @PutMapping("/updateTaskTime")
    public ApiResult<Long> updateTaskTime(@Valid @RequestBody ProjectTaskTimeDTO dto) {
        return ApiResult.success(service.updateTaskTime(dto));
    }

    /**
     * 回滚结束时间（撤销完成）
     * @param id 任务id
     * @return {@link ApiResult<Long>}
     */
    @PutMapping("/rollbackEndTime/{id}")
    public ApiResult<Long> rollbackEndTime(@PathVariable Long id) {
        return ApiResult.success(service.rollbackEndTime(id));
    }

    /**
     * 任务 即将结束通知（调用中台接口发送）
     */
    @Inner
    @GetMapping("/deadLineNotify")
    public ApiResult<Void> deadLineNotify(){
        service.deadLineNotify();
        return ApiResult.success(null);
    }

    /**
     * 每日十点，全量判断 并更新任务状态（未开始->已逾期）
     */
    @Inner
    @GetMapping("/updateStatusToDelay")
    public ApiResult<Void> updateStatusToDelay(){
        service.updateStatusToDelay();
        return ApiResult.success(null);
    }

}
