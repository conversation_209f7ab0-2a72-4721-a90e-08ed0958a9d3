package com.gok.pboot.pms.cost.entity.dto;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.cost.entity.vo.TaskDailyPaperDetailVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 成本 任务 每日 明细 vo
 *
 * <AUTHOR>
 * @date 2025/04/21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class CostTaskDailyDetailVO {

    private Long taskId;

    private String taskName;

    private Long managerId;

    private String managerName;

    private Long approvalUserId;

    private String approvalUserName;

    private Page<TaskDailyPaperDetailVO> dailyPaperDetailPage;

}
