package com.gok.pboot.pms.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.sql.Timestamp;

/**
 * 项目动态分页查询 Vo
 *
 * <AUTHOR>
 * @since 2023-07-14
 **/
@Data
public class ProjectProcessInfoFindPageVO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 请求id
     */
    private Long requestId;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 申请人id
     */
    private Long applicatId;

    /**
     * 申请人
     */
    private String applicat;

    /**
     * 流程类型
     */
    private String processType;

    /**
     * 流程状态
     */
    private String status;

    /**
     * 流程状态文本
     */
    private String statusTxt;

    /**
     * 当前节点名称
     */
    private String currentNodeName;

    /**
     * 流程名
     */
    private String name;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp ctime;

    /**
     * 归档时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp filingDateTime;

    /**
     * 模块 0-质保 1-关闭
     * {@link com.gok.pboot.pms.enumeration.ProjectOperationModuleEnum}
     */
    private Integer module;

}
