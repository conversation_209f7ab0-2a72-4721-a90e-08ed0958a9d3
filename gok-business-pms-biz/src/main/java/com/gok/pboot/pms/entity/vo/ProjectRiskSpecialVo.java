package com.gok.pboot.pms.entity.vo;

import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.entity.domain.ProjectRisk;
import com.gok.pboot.pms.enumeration.InfluenceDegreeEnum;
import com.gok.pboot.pms.enumeration.RiskLevelEnum;
import com.gok.pboot.pms.enumeration.RiskStatusEnum;
import com.gok.pboot.pms.enumeration.YesOrNoEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 特殊：能够查询已删除风险项的vo
 *
 * <AUTHOR>
 * @date 2023/10/25
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class ProjectRiskSpecialVo {

    /**
     * 风险id
     */
    private Long id;

    /**
     * 风险描述
     */
    private String description;

    /**
     *  发生概率 (%)
     */
    private BigDecimal probability;

    /**
     * 影响程度  极大、大、中、小（字典设置）
     * {@link com.gok.pboot.pms.enumeration.InfluenceDegreeEnum}
     */
    private Integer influenceDegree;

    /**
     * 影响程度: 极大、大、中、小
     * {@link com.gok.pboot.pms.enumeration.InfluenceDegreeEnum}
     */
    private String influenceDegreeTxt;

    /**
     * 风险等级 高 中 低
     * 高风险： >60%发生风险的可能性    中风险： 30-60%发生风险的可能性    低风险：<30%发生防线的可能性
     */
    private Integer level;

    /**
     * 风险等级: 高 中 低
     */
    private String levelTxt;

    /**
     * 风险应对计划
     */
    private String responsePlan;

    /**
     * 责任人id
     */
    private Long chargeUserId;

    /**
     * 责任人
     */
    private String chargeUser;

    /**
     * 状态 开放0、关闭1、已删除-1
     */
    private Integer status;

    /**
     * 状态: 开放、关闭、已删除
     */
    private String statusTxt;

    public static ProjectRiskSpecialVo of(ProjectRisk request) {
        ProjectRiskSpecialVo result = new ProjectRiskSpecialVo();
        result.setId(request.getId());
        result.setDescription(request.getDescription());
        result.setProbability(request.getProbability());
        result.setInfluenceDegree(request.getInfluenceDegree());
        result.setInfluenceDegreeTxt(EnumUtils.getNameByValue(InfluenceDegreeEnum.class, request.getInfluenceDegree()));
        result.setLevel(request.getLevel());
        result.setLevelTxt(EnumUtils.getNameByValue(RiskLevelEnum.class, request.getLevel()));
        result.setResponsePlan(request.getResponsePlan());
        result.setChargeUser(request.getChargeUser());
        result.setChargeUserId(request.getChargeUserId());
        // 对于状态做特殊处理，包含已删除
        if (request.getDelFlag().equals(YesOrNoEnum.YES.getValue())) {
            // 已删除
            result.setStatus(-1);
            result.setStatusTxt("已删除");
        } else {
            result.setStatus(request.getStatus());
            result.setStatusTxt(EnumUtils.getNameByValue(RiskStatusEnum.class, request.getStatus()));
        }

        return result;
    }
}
