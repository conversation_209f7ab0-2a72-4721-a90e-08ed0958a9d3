package com.gok.pboot.pms.entity.vo;


import lombok.Data;

import java.time.LocalDate;

/**
 * 项目商务里程碑Vo
 *
 * <AUTHOR>
 * @date 2024-02-23 10:46:40
 */
@Data
public class ProjectBusinessMilestonesVo {
    /**
     * 合同id
     */
    private Long id;
    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 项目名字
     */
    private String projectName;
    /**
     * 里程碑类型（1=B表商务里程碑，2=合同会签商务里程碑）
     */
    private Integer type;
    /**
     * 商务里程碑
     */
    private String businessMilestones;
    /**
     * 里程碑说明
     */
    private String milestoneDesc;
    /**
     * 预计完成日期
     */
    private LocalDate expectedCompleteDate;
    /**
     * 预计完成日期超期标志(0按时完成1超期)
     */
    private Integer ifOverdueFlag = 0;
    /**
     * 实际完成日期
     */
    private LocalDate actualCompleteDate;
    /**
     * 是否已完成(0是, 1否)
     *
     */
    private String ifFinish;
    /**
     * 是否超期完成标志(0按时完成1超期完成)
     */
    private Integer ifOverdueFinishFlag = 0;
    /**
     * 款项名称(款项类型)
     */
    private String accountType;
    /**
     * 所属合同id
     */
    private String contractId;
    /**
     * 所属合同名
     */
    private String contractName;
    /**
     * 回款条件（合同付款条款）
     */
    private String contractPayTerms;
    /**
     * 回款账期（天）
     */
    private Integer collectDays;
    /**
     * 回款比例（%）
     */
    private String collectRatio = "0";
    /**
     * 预估结算金额（含税）
     */
    private String expectCollectAmount;
    /**
     * 回款状态
     */
    private Integer paymentStatus;
    /**
     * 回款状态
     */
    private String paymentStatusTxt;
    /**
     * 实际款项金额
     */
    private String currentPaymentMoney;
    /**
     * 结算金额（含税)
     */
    private String settlementAmount;

    /**
     * 结算金额（不含税)
     */
    private String settlementAmountExcludingTax;

    /**
     * 税率OA字典ID
     */
    private Integer taxRate;
    /**
     * 税率OA字典txt
     */
    private String taxRateTxt;

    /**
     * 请求状态
     */
    private Integer requestStatus;

    /**
     * 关联流程ID
     */
    private Long requestId;
}
