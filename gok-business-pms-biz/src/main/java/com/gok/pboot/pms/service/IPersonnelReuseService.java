package com.gok.pboot.pms.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.entity.dto.PersonnelReuseImproExcelDTO;
import com.gok.pboot.pms.entity.dto.PersonnelReuseUpdateDTO;
import com.gok.pboot.pms.entity.vo.PersonnelReusePageVO;
import org.springframework.validation.BindingResult;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 人才复用 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-01
 */
public interface IPersonnelReuseService {

    ApiResult importUser(List<PersonnelReuseImproExcelDTO> excelVOList, BindingResult bindingResult, MultipartFile file);

    Page<PersonnelReusePageVO> findPage(PageRequest pageRequest, Map<String, Object> filter);


    /**
     * 批量修改
     *
     * @param list  id集合
     * @return ApiResult
     */
    ApiResult batchDel(List<Long> list);

    ApiResult<String> update(PersonnelReuseUpdateDTO personnelReuseUpdateDTO);
}
