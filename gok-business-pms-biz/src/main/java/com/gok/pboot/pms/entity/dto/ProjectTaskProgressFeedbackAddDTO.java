package com.gok.pboot.pms.entity.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 项目任务进度回复
 *
 * <AUTHOR>
 * @version 1.1.0
 */
@Getter
@Setter
@ToString
public class ProjectTaskProgressFeedbackAddDTO {

    /**
     * 进展ID
     */
    @NotNull(message = "进展ID不能为空")
    private Long progressId;

    /**
     * 回复内容
     */
    @NotBlank(message = "回复内容不能为空")
    @Length(max = 600, message = "回复内容不能超过600字")
    private String content;
}
