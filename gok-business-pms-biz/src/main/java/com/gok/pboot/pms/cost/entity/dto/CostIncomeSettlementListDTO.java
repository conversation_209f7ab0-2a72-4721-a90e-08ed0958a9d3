package com.gok.pboot.pms.cost.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 收入结算列表查询DTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CostIncomeSettlementListDTO {

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 结算开始日期
     */
    private String startDate;

    /**
     * 结算截止日期
     */
    private String endDate;

    /**
     * 姓名
     */
    private String userName;

    /**
     * 结算明细ID集合
     */
    private List<Long> costIncomeCalculationDetailIds;

    /**
     * 明细导出结算明细编号集合
     */
    private List<String> detailsNumberList;

    /**
     * 导出结算编号集合
     */
    private List<String> numberList;

    /**
     * 税率集合
     */
    private List<Integer> taxRateList;

    /**
     * 测算汇总ID集合
     */
    private List<Long> costIncomeCalculationIds;

    /**
     * 结算单编号
     */
    private String settlementNumber;

    /**
     * 可查看权限范围人员工号集合
     */
    private List<String> purviewNames;

    /**
     * 可查看权限范围结算单编号集合
     */
    private List<String> purviewNumbers;

    /**
     * 查看权限
     */
    private Boolean purview;
}
