package com.gok.pboot.pms.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.common.security.annotation.Inner;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.entity.domain.Task;
import com.gok.pboot.pms.entity.dto.ProjectTaskeDto;
import com.gok.pboot.pms.entity.dto.ProjectTaskeUserDto;
import com.gok.pboot.pms.entity.vo.*;
import com.gok.pboot.pms.service.IProjectTaskeService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.time.LocalDate;
import java.util.List;

/**
 * 项目任务前端控制器
 *
 * <AUTHOR>
 * @date 2024/01/19
 * @menu 项目任务
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/projectTaske")
public class ProjectTaskeController {


    private final IProjectTaskeService service;

    /**
     * 获取任务列表
     *
     * @param projectId 项目id
     * @return {@link ApiResult}<{@link List}<{@link ProjectTaskeVo}>>
     */
    @GetMapping("/getByProjectId/{projectId}")
    public ApiResult<List<ProjectTaskeVo>> getByProjectId(@PathVariable Long projectId) {
        return ApiResult.success(service.getByProjectId(projectId));
    }

    /**
     * 获取项目概览
     *
     * @param pageRequest 分页请求对象
     * @param request 请求对象
     * @customParam filter_L_projectId 传入的项目id
     * @customParam filter_L_kind 存入的任务类型参数，不传默认查所有
     * @customParam filter_S_title 模糊查询的任务标题
     * @customParam filter_S_leaderName 模糊查询的负责人姓名
     * @customParam filter_L_state 传入的状态参数，不传默认查所有
     * @return {@link ApiResult}<{@link ProjectOverviewVo}>
     */
    @GetMapping("/findOverview")
    @PreAuthorize("@pms.hasPermission('PROJECT_MANAGE_DETAIL')")
    public ApiResult<ProjectOverviewVo> findOverview(PageRequest pageRequest, HttpServletRequest request) {
        return ApiResult.success(service.findOverview(pageRequest, request));
    }

    /**
     * 获取分页列表
     *
     * @param pageRequest 分页请求对象
     * @param request 请求对象
     * @customParam filter_L_projectId 传入的项目id
     * @customParam filter_L_kind 存入的任务类型参数，不传默认查所有
     * @customParam filter_S_title 模糊查询的任务标题
     * @customParam filter_S_leaderName 模糊查询的负责人姓名
     * @customParam filter_L_state 传入的状态参数，不传默认查所有
     * @return {@link ApiResult}<{@link Page}<{@link ProjectTaskeVo}>>
     */
    @GetMapping("/findList")
    @PreAuthorize("@pms.hasPermission('PROJECT_MANAGE_DETAIL')")
    public ApiResult<Page<ProjectTaskeVo>> findList(PageRequest pageRequest, HttpServletRequest request) {
        return ApiResult.success(service.findList(pageRequest, request));
    }

    /**
     * 获取我的任务
     *
     * @param pageRequest 分页请求对象
     * @param request 请求对象
     * @customParam filter_S_projectName 项目名称
     * @return {@link ApiResult}<{@link Page}<{@link MyTaskVo}>>
     */
    @GetMapping("/myTaskList")
    public ApiResult<Page<MyTaskVo>> findMyTaskList(PageRequest pageRequest, HttpServletRequest request) {
        return ApiResult.success(service.findMyTaskList(pageRequest, request));
    }

    /**
     * 获取分页列表（甘特图）
     *
     * @param pageRequest 分页请求对象
     * @param request     请求对象
     * @return {@link ApiResult}<{@link Page}<{@link ProjectTaskeGanttVo}>>
     * @customParam filter_L_projectId 传入的项目id
     * @customParam filter_L_kind 存入的任务类型参数，不传默认查所有
     * @customParam filter_S_title 模糊查询的任务标题
     * @customParam filter_S_leaderName 模糊查询的负责人姓名
     * @customParam filter_L_state 传入的状态参数，不传默认查所有
     */
    @GetMapping("/findGanttList")
    @PreAuthorize("@pms.hasPermission('PROJECT_MANAGE_DETAIL')")
    public ApiResult<Page<ProjectTaskeGanttVo>> findGanttList(PageRequest pageRequest, HttpServletRequest request) {
        return ApiResult.success(service.findGanttList(pageRequest, request));
    }

    /**
     * 获取任务详情
     *
     * @param id 任务id
     * @return {@link ApiResult}<{@link ProjectTaskeVo}>
     */
    @GetMapping("/findOne/{id}")
    public ApiResult<ProjectTaskeVo> findOne(@PathVariable("id") Long id) {
        return ApiResult.success(service.findOne(id));
    }

    /**
     * 分页查询任务下的操作记录
     *
     * @param pageRequest 分页对象
     * @param id          任务id
     * @return {@link ApiResult<Page< Task >>}
     */
    @GetMapping("/findOperatingRecordPage/{id}")
    public ApiResult<Page<OperatingRecordPageVO>> findOperatingRecordPage(@PathVariable("id") Long id, PageRequest pageRequest) {
        return ApiResult.success(service.findOperatingRecordPage(id, pageRequest));
    }

    /**
     * 获取最新填报日期
     *
     * @param id 任务id
     * @return {@link ApiResult}<{@link LocalDate}>
     */
    @GetMapping("/findFinalDailyDate/{id}")
    public ApiResult<LocalDate> findFinalDailyDate(@PathVariable("id") Long id) {
        return ApiResult.success(service.findFinalDailyDate(id));
    }

    /**
     * 新建项目任务
     *
     * @param request 任务保存请求对象
     * @param result 校验结果
     * @return {@link ApiResult}<{@link Long}>
     */
    @PostMapping
    public ApiResult<Long> save(@Valid @RequestBody ProjectTaskeDto request, BindingResult result) {
        if (result.hasErrors()) {
            return ApiResult.failure(result.getAllErrors().get(0).getDefaultMessage());
        }

        return ApiResult.success(service.save(request));
    }

    /**
     * 编辑项目任务
     *
     * @param request 任务更新请求对象
     * @param result 校验结果
     * @return {@link ApiResult}<{@link Long}>
     */
    @PutMapping
    public ApiResult<Long> update(@Valid @RequestBody ProjectTaskeDto request, BindingResult result) {
        if (result.hasErrors()) {
            return ApiResult.failure(result.getAllErrors().get(0).getDefaultMessage());
        }
        if (request.getId() == null) {
            return ApiResult.failure("任务ID不能为空");
        }

        return ApiResult.success(service.update(request));
    }

    /**
     * 根据任务id删除任务
     *
     * @param id 任务id
     * @return {@link ApiResult}<{@link Boolean}>
     */
    @DeleteMapping("/{id}")
    public ApiResult<Boolean> deleteById(@PathVariable("id") Long id) {
        return ApiResult.success(service.deleteById(id));
    }

    /**
     * 获取任务参与人列表
     *
     * @param id          任务id
     * @param pageRequest 分页请求
     * @return {@link ApiResult}<{@link List}<{@link ProjectTaskeUserVo}>>
     */
    @GetMapping("/getMembers/{id}")
    public ApiResult<Page<ProjectTaskeUserVo>> getMembers(@PathVariable("id") Long id, PageRequest pageRequest) {
        return ApiResult.success(service.getMembers(id, pageRequest));
    }

    /**
     * 删除参与人
     *
     * @param id 关联表id
     * @return {@link ApiResult}<{@link Boolean}>
     */
    @DeleteMapping("/delMember/{id}")
    public ApiResult<Boolean> delMember(@PathVariable("id") Long id) {
        return ApiResult.success(service.delMember(id));
    }

    /**
     * 结束任务
     *
     * @param id         任务id
     * @param finishDate 结束日期
     * @return {@link ApiResult}<{@link Long}>
     */
    @PutMapping("/finish/{id}")
    public ApiResult<Long> finish(@PathVariable("id") Long id, @RequestParam("finishDate") LocalDate finishDate) {
        return ApiResult.success(service.finish(id, finishDate));
    }

    /**
     * 重启任务
     *
     * @param id 任务id
     * @return {@link ApiResult}<{@link Long}>
     */
    @PutMapping("/restart/{id}")
    public ApiResult<Long> restart(@PathVariable("id") Long id) {
        return ApiResult.success(service.restart(id));
    }

    /**
     * 添加项目参与人
     *
     * @param id      任务id
     * @param members 项目参与人列表
     * @return {@link ApiResult}<{@link Long}>
     */
    @PutMapping("/addMember/{id}")
    public ApiResult<Long> addMember(@PathVariable("id") Long id, @RequestBody List<ProjectTaskeUserDto> members) {
        return ApiResult.success(service.addMember(id, members));
    }

    /**
     * 定时器
     * 同步创建项目默认任务（定时任务）  0 0/30 * * * ? *
     *
     * @return {@link ApiResult}<{@link Boolean}>
     */
    @Inner(value = false)
    @GetMapping("/syncCreateDefaultTask")
    public ApiResult<Boolean> syncCreateDefaultTask() {
        log.info("定时任务触发, 每半小时全量判断并创建项目默认任务执行开始");
        ApiResult<Boolean> success = ApiResult.success(service.syncCreateDefaultTask());
        log.info("定时任务触发, 每半小时全量判断并创建项目默认任务执行结束");
        return success;
    }

    /**
     * 定时器
     * 任务 即将结束通知（调用中台接口发送）  0 0 10 * * ? *
     *
     * @return {@link ApiResult}<{@link Void}>
     */
    @Inner(value = false)
    @GetMapping("/deadLineNotify")
    public ApiResult<Void> deadLineNotify() {
        log.info("定时任务触发, 每天10:00自动查询即将结束任务并通知对应负责人执行开始");
        service.deadLineNotify();
        log.info("定时任务触发, 每天10:00自动查询即将结束任务并通知对应负责人执行完成");
        return ApiResult.success(null);
    }

    /**
     * 定时器
     * 项目任务自动结束  0 0 1 * * ? *
     *
     * @return {@link ApiResult}<{@link Void}>
     */
    @Inner(value = false)
    @GetMapping("/autoFinish")
    public ApiResult<Void> autoFinish() {
        log.info("定时任务触发, 每天1:00自动查询需要自动结束项目执行开始");
        service.autoFinish();
        log.info("定时任务触发, 每天1:00自动查询需要自动结束项目执行完成");
        return ApiResult.success(null);
    }

    /**
     * 同步旧任务
     *
     * @return {@link ApiResult}<{@link Void}>
     */
    //@Inner(false)
    @GetMapping("/syncOldTask")
    public ApiResult<Void> syncOldTask() {
        service.syncOldTask();

        return ApiResult.success(null);
    }

    //@Inner(false)
    @GetMapping("/syncOldTaskFix")
    public ApiResult<Void> syncOldTaskFix() {
        service.syncOldTaskFix();

        return ApiResult.success(null);
    }

}
