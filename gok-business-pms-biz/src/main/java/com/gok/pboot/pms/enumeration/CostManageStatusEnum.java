package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 成本管理状态枚举
 *
 * <AUTHOR>
 * @date 2025/01/08
 */
@Getter
@AllArgsConstructor
public enum CostManageStatusEnum implements ValueEnum<Integer> {

    /**
     * 确认中
     */
    UNCONFIRMED(0, "确认中",1),

    /**
     * 已确认
     */
    CONFIRMED(1, "已确认",3),

    /**
     * 草稿
     */
    DRAFT(2, "草稿",null),

    /**
     * 未提交
     */
    UNCOMMITTED(3, "未提交",0);

    private final Integer value;
    private final String name;

    private final Integer flowStatus;

    public static CostManageStatusEnum getEnumByBool(Boolean bool) {
        if (bool == null) {
            return UNCONFIRMED;
        }
        return bool ? CONFIRMED : UNCONFIRMED;
    }

    public static CostManageStatusEnum getEnumByFlowStatus(Integer requestStatus) {
        for (CostManageStatusEnum statusEnum : values()) {
            if (statusEnum.getFlowStatus() != null && statusEnum.getFlowStatus().equals(requestStatus)) {
                return statusEnum;
            }
        }
        return DRAFT;
    }
}
