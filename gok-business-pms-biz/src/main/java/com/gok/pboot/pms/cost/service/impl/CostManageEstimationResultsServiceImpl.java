package com.gok.pboot.pms.cost.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.bcp.admin.vo.DictKvVo;
import com.gok.components.common.user.PigxUser;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.Util.DbApiUtil;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.common.exception.ServiceException;
import com.gok.pboot.pms.cost.entity.domain.*;
import com.gok.pboot.pms.cost.entity.dto.*;
import com.gok.pboot.pms.cost.entity.vo.*;
import com.gok.pboot.pms.cost.enums.*;
import com.gok.pboot.pms.cost.mapper.*;
import com.gok.pboot.pms.cost.service.*;
import com.gok.pboot.pms.enumeration.CostManageStatusEnum;
import com.gok.pboot.pms.enumeration.OAFormTypeEnum;
import com.gok.pboot.pms.service.IDictService;
import com.gok.pboot.pms.service.IEhrService;
import com.google.common.collect.ImmutableList;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * <p>
 * 成本管理估算结果 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CostManageEstimationResultsServiceImpl extends ServiceImpl<CostManageEstimationResultsMapper, CostManageEstimationResults>
        implements ICostManageEstimationResultsService {

    private final CostManageVersionMapper costManageVersionMapper;
    private final CostConfigAccountMapper costConfigAccountMapper;
    private final CostCashPlanVersionMapper costCashPlanVersionMapper;
    private final CostManagePersonnelLevelDetailMapper costManagePersonnelLevelDetailMapper;
    private final CostManagePersonnelCustomDetailMapper costManagePersonnelCustomDetailMapper;
    private final ICostConfigLevelPriceService costConfigLevelPriceService;
    private final ICostSalaryDetailsService costSalaryDetailsService;


    private final IDictService idictService;
    private final ICostManageVersionService costManageVersionService;
    private final DbApiUtil dbApiUtil;
    private final ICostBaselineVersionRecordService baselineVersionRecordService;
    @Lazy
    @Autowired
    private ICostDeliverTaskService icostDeliverTaskService;
    private final ICostCashPlanService costCashPlanService;

    private final IEhrService IEhrService;

    private static final String PROJECT_MANAGER_ACCOUNT_NAME = "项目-管理费用";
    public static final String ADVANCE_FUNDING_NAME = "垫资成本";

    @Value("${oa.xsfyOaId:62}")
    private Long xsfyOaId;

    @Value("${oa.xmglfysqrgOaId:8102}")
    private Long xmglfysqrgOaId;


    @Override
    public List<CostManageEstimationResultsVO> findLatestEstimationResults(CostManageVersionDTO request) {
        Long projectId = request.getProjectId();
        List<Integer> costBudgetTypeList = request.getCostBudgetTypeList();
        if (CostBudgetTypeEnum.BBCB.getValue().equals(costBudgetTypeList.get(0))) {
            Map<Integer, Boolean> integerBooleanMap = costManageVersionService.checkCostBudgetTypeExist(projectId, false);
            if (!integerBooleanMap.get(ChangeContentEnum.BBCB.getValue())) {
                costBudgetTypeList = Arrays.asList(CostBudgetTypeEnum.ABCB.getValue());
                request.setCostBudgetTypeList(costBudgetTypeList);
            }
        }
        CostManageVersion costManageVersion = costManageVersionMapper.getLatestCostManageEstimation(request);
        // 查询A表或者B表成成本，获取最新售前成本
        boolean editTotalCostFlag =
                CollUtil.containsAny(costBudgetTypeList, ImmutableList.of(CostBudgetTypeEnum.ABCB.getValue(), CostBudgetTypeEnum.BBCB.getValue()));
        CostManageVersion lastestPreCostManageVersion = null;
        if (editTotalCostFlag) {
            // 获取最新的已确认售前成本版本
            lastestPreCostManageVersion = getLatestConfirmedPreSalesCost(projectId);
        }
        if (null == costManageVersion && null == lastestPreCostManageVersion) {
            return ListUtil.empty();
        }

        // 获取对应的实体类数据
        costManageVersion = Optional.ofNullable(costManageVersion).orElse(lastestPreCostManageVersion);
        Long versionId = costManageVersion.getId();
        List<CostManageEstimationResultsVO> latestResults = null == versionId
                ? new ArrayList<>()
                : CollUtil.emptyIfNull(baseMapper.findByVersionId(Arrays.asList(versionId)));
        if (editTotalCostFlag && null != lastestPreCostManageVersion) {
            latestResults = latestResults.stream()
                    .filter(e -> !e.getAccountOaId().equals(xsfyOaId) && !e.getAccountOaId().equals(xmglfysqrgOaId))
                    .collect(toList());
            // 查询售前成本科目
            List<CostManageEstimationResultsVO> preCostResults = null != lastestPreCostManageVersion
                    ? baseMapper.findByVersionId(Arrays.asList(lastestPreCostManageVersion.getId()))
                    : new ArrayList<>(0);
            latestResults.addAll(preCostResults);
        }
        if (CollUtil.isEmpty(latestResults)) {
            return ListUtil.empty();
        }
        List<Long> ids = latestResults.stream().map(CostManageEstimationResultsVO::getId).collect(toList());

        // 最新的版本科目
        List<CostConfigAccount> currentVersionAccountList =
                costConfigAccountMapper.getCurrentVersionCostSubjectConfigInfoList(null);
        Map<Long, CostConfigAccount> currentVersionAccountMap =
                CollUtil.emptyIfNull(currentVersionAccountList).stream()
                        .collect(Collectors.toMap(CostConfigAccount::getOaId, Function.identity()));
        // 判断对应科目是否被删除
        List<Long> currentOaIds = currentVersionAccountMap.keySet().stream().collect(toList());
        List<Long> existOaIds = latestResults.stream()
                .map(CostManageEstimationResultsVO::getAccountOaId)
                .collect(toList());
        Collection<Long> deleteAccountIds = CollUtil.subtract(existOaIds, currentOaIds);

        // 售前成本只返回指定科目
        if (CostBudgetTypeEnum.SQCB.getValue().equals(costManageVersion.getCostBudgetType())) {
            return getAssembleEstimationResultsVOList(costManageVersion, latestResults, currentVersionAccountMap, null);
        }
        Map<Long, String> jobActivityNameMap = IEhrService.getJobActivityNameMap();
        Map<Long, String> positionGradeNameMap = IEhrService.getPositionGradeNameMap();
        // 获取对应人工测算成本明细
        Map<Long, List<CostManagePersonnelLevelDetailVO>> personnelLevelDetailMap =
                CollUtil.emptyIfNull(costManagePersonnelLevelDetailMapper.findByEstimateResultIdList(ids)).stream()
                        .peek(item -> {
                            item.setPersonnelType(jobActivityNameMap.get(item.getJobActivityId()));
                            item.setPersonnelLevelName(item.getPersonnelLevel() != null ? positionGradeNameMap.get(item.getPersonnelLevel()) : null);
                        })
                        .peek(e -> e.setCustomSubsidyCostList(StrUtil.isNotBlank(e.getCustomSubsidyCost()) ? JSONUtil.toList(e.getCustomSubsidyCost(), CostManagePersonnelCustomDetailDto.class) : ListUtil.empty()))
                        .collect(Collectors.groupingBy(CostManagePersonnelLevelDetailVO::getEstimationResultsId));

        // 获取对应的自定义补贴明细
        Map<Long, List<CostManagePersonnelCustomDetailVO>> customDetailMap =
                CollUtil.emptyIfNull(costManagePersonnelCustomDetailMapper.findByEstimateResultIdList(ids)).stream()
                        .collect(Collectors.groupingBy(CostManagePersonnelCustomDetailVO::getEstimationResultsId));
        latestResults.forEach(e -> {
            e.setCustomDetailList(customDetailMap.get(e.getId()));
            e.setPersonnelLevelList(personnelLevelDetailMap.get(e.getId()));
            e.setAccountDeleteFlag(CollUtil.contains(deleteAccountIds, e.getAccountOaId()));
        });

        // 获取最新的现金流计划
        CostCashPlanVersion costCashPlanVersion =
                Optional.ofNullable(costCashPlanVersionMapper.getCurrentVersion(projectId)).orElse(new CostCashPlanVersion());
        return getAssembleEstimationResultsVOList(costManageVersion, latestResults, currentVersionAccountMap, costCashPlanVersion.getId());
    }

    /**
     * 获取最新的售前成本版本
     *
     * @param projectId
     * @return
     */
    @Override
    public CostManageVersion getLatestConfirmedPreSalesCost(Long projectId) {
        List<CostManageVersionVO> costManageVersionList = costManageVersionMapper.queryProjectVersionByProjectId(projectId, null);
        Map<Long, CostManageStatusEnum> statusEnumMap = dbApiUtil.getOaRequestStatusToObj(costManageVersionList.stream().map(CostManageVersionVO::getRequestId).collect(toList()), CostManageVersionVO.class).stream()
                .collect(Collectors.toMap(CostManageVersionVO::getRequestId, e -> getCostManageStatusByCurrentNodeType(e.getRequestStatus()), (a, b) -> a));
        return costManageVersionList.stream()
                .filter(version -> CostManageStatusEnum.CONFIRMED.equals(statusEnumMap.get(version.getRequestId()))
                        && CostManageVersionEnum.CBGL.getValue().equals(version.getVersionType())
                        && CostBudgetTypeEnum.SQCB.getValue().equals(version.getCostBudgetType())) // 筛选已确认状态
                .sorted(Comparator.comparing(CostManageVersionVO::getCtime).reversed()) // 按创建时间倒序
                .findFirst()
                .map(vo -> {
                    CostManageVersion version = new CostManageVersion();
                    BeanUtil.copyProperties(vo, version);
                    return version;
                })
                .orElse(null);
    }

    @Override
    public List<CostManageEstimationResultsVO> getAssembleEstimationResultsVOList(
            CostManageVersion costManageVersion,
            List<CostManageEstimationResultsVO> versionList,
            Map<Long, CostConfigAccount> currentVersionAccountMap,
            Long cashPlanVersionId
    ) {
        if (null != costManageVersion && !CostBudgetTypeEnum.SQCB.getValue().equals(costManageVersion.getCostBudgetType())) {
            // 获取垫资成本
            Optional.ofNullable(getAdvanceFundingCost(
                            costManageVersion.getProjectId(),
                            null != cashPlanVersionId ? cashPlanVersionId : costManageVersion.getCashPlanVersionId(),
                            null != currentVersionAccountMap ? currentVersionAccountMap.values().stream().collect(toList()) : null))
                    .ifPresent(versionList::add);
        }
        if (CollUtil.isEmpty(versionList)) {
            return ListUtil.empty();
        }

        List<CostManageEstimationResultsVO> parentList =
                versionList.stream().filter(e -> e.getParentId() == null).collect(toList());

        List<CostManageEstimationResultsVO> childrenList =
                versionList.stream().filter(e -> e.getParentId() != null).collect(toList());
        Map<Long, List<CostManageEstimationResultsVO>> childrenMap = CollUtil.isNotEmpty(childrenList)
                ? childrenList.stream().collect(Collectors.groupingBy(CostManageEstimationResultsVO::getParentId))
                : new HashMap<>(0);

        // VO类组装
        Map<Integer, String> taxRateMap = idictService.getDictKvList("税率").getData().stream()
                .collect(Collectors.toMap(
                        key -> Integer.parseInt(key.getValue()),
                        DictKvVo::getName));

        // 查询所有（人工成本、费用报销、外采费用）成本预算明细对应已用预算、剩余预算、已确认成本
        Map<String, DeliverCostBudgetListVO> costBudgetMap =
                icostDeliverTaskService.getUsedAllCostBudget(costManageVersion);

        parentList.forEach(e -> assembleEstimationResultsVO(e, childrenMap, taxRateMap, currentVersionAccountMap, costBudgetMap));
        return parentList;
    }

    /**
     * 组装成本估算结果VO类
     *
     * @param vo                成本估算结果VO类
     * @param taxRateMap        税率字典
     * @param versionAccountMap 版本科目id
     */
    private void assembleEstimationResultsVO(CostManageEstimationResultsVO vo,
                                             Map<Long, List<CostManageEstimationResultsVO>> childrenMap,
                                             Map<Integer, String> taxRateMap,
                                             Map<Long, CostConfigAccount> versionAccountMap,
                                             Map<String, DeliverCostBudgetListVO> costBudgetMap) {
        // 关联展示最新的科目id和科目名称
        if (CollUtil.isNotEmpty(versionAccountMap)) {
            CostConfigAccount costConfigAccount = versionAccountMap.get(vo.getAccountOaId());
            if (null != costConfigAccount) {
                vo.setAccountId(costConfigAccount.getId());
                vo.setAccountName(costConfigAccount.getAccountName());
            }
        }

        String accountTypeName = EnumUtils.getNameByValue(AccountTypeEnum.class, vo.getAccountType());
        vo.setAccountTypeTxt(accountTypeName);
        vo.setTaxRateTxt(null != vo.getTaxRate() ? taxRateMap.get(vo.getTaxRate()) : null);
        vo.setCostBudgetTypeTxt(EnumUtils.getNameByValue(CostBudgetTypeEnum.class, vo.getCostBudgetType()));

        if (CollUtil.isNotEmpty(costBudgetMap)) {
            String key = StrUtil.format("{}-{}", vo.getAccountOaId(), vo.getTaxRate());
            DeliverCostBudgetListVO deliveryListVO = costBudgetMap.getOrDefault(key, DeliverCostBudgetListVO.builder().build());
            vo.setUsedBudget(deliveryListVO.getUsedBudget());
            vo.setRemainBudget(deliveryListVO.getRemainBudget());
        }

        // 查询人本成本测算是否有预算明细
        if (CostManageSourceEnum.RGCBCS.getValue().equals(vo.getSource())) {
            LambdaQueryWrapper<CostManagePersonnelLevelDetail> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CostManagePersonnelLevelDetail::getEstimationResultsId, vo.getId());
            List<CostManagePersonnelLevelDetail> details = costManagePersonnelLevelDetailMapper.selectList(queryWrapper);
            if (CollUtil.isNotEmpty(details)) {
                vo.setHasDetail(1);
            }
        }

        if (null == childrenMap) {
            return;
        }

        List<CostManageEstimationResultsVO> children = childrenMap.get(vo.getId());
        if (CollUtil.isNotEmpty(children)) {
            children.forEach(e -> assembleEstimationResultsVO(e, null, taxRateMap, versionAccountMap, costBudgetMap));
            vo.setChildren(children);
        }
    }

    /**
     * 获取垫资成本数据
     *
     * @param projectId
     * @return
     */
    private CostManageEstimationResultsVO getAdvanceFundingCost(Long projectId,
                                                                Long cashPlanVersionId,
                                                                List<CostConfigAccount> currentVersionAccountList) {
        if (null == cashPlanVersionId) {
            return null;
        }
        CostConfigAccount account = Optional.ofNullable(currentVersionAccountList).orElse(ImmutableList.of()).stream()
                .filter(e -> e.getAccountName().equals(ADVANCE_FUNDING_NAME))
                .findFirst()
                .orElse(new CostConfigAccount());

        List<CostCashPlanVO> costCashPlanList =
                costCashPlanService.getCostCashPlanList(CostCashPlanDto.builder().projectId(projectId).versionId(cashPlanVersionId).build());
        if (CollUtil.isEmpty(costCashPlanList)) {
            return null;
        }

        BigDecimal cashFlowInterest = costCashPlanList.stream()
                .map(CostCashPlanVO::getCashFlowInterest)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal budgetAmountIncludedTax = cashFlowInterest.compareTo(BigDecimal.ZERO) >= 0
                ? null
                : cashFlowInterest.abs();

        return null != budgetAmountIncludedTax
                ? CostManageEstimationResultsVO.builder()
                .accountName(ADVANCE_FUNDING_NAME)
                .accountId(account.getId())
                .accountOaId(account.getOaId())
                .accountCode(account.getAccountCode())
                .accountCategoryId(account.getAccountCategoryId())
                .accountCategoryName(account.getAccountCategoryName())
                .budgetAmountIncludedTax(budgetAmountIncludedTax)
                .budgetAmountExcludingTax(budgetAmountIncludedTax)
                .source(CostManageSourceEnum.XTZDSC.getValue())
                .taxRate(5)
                .remark("根据现金流计划自动生成")
                .build()
                : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveEstimationResults(CostManageEstimationResultsSaveDTO request) {
        // 重置当前使用成本估算版本状态
        Long projectId = request.getProjectId();
        Integer costBudgetType = request.getCostBudgetType();
        // 获取当前对应预算类型最新的成本估算版本
        CostManageVersionDTO costManageVersionQuery = CostManageVersionDTO.builder()
                .projectId(projectId)
                .costBudgetTypeList(Arrays.asList(costBudgetType))
                .build();
        CostManageVersion costManageVersion = costManageVersionMapper.getLatestCostManageEstimation(costManageVersionQuery);
        // 创建新的成本估算版本记录
        CostCashPlanVersion costCashPlanVersion =
                Optional.ofNullable(costCashPlanVersionMapper.getCurrentVersion(projectId)).orElse(new CostCashPlanVersion());
        String newVersionName =
                CostConfigVersionServiceImpl.getVersionName((null != costManageVersion) ? costManageVersion.getVersionName() : null);
        PigxUser user = SecurityUtils.getUser();
        CostManageVersion newCostCostManageVersion = CostManageVersion.builder()
                .versionType(CostManageVersionEnum.CBGL.getValue())
                .versionStatus(VersionStatusEnum.CURRENT.getValue())
                .versionName(newVersionName)
                .costBudgetType(costBudgetType)
                .operatorId(user.getId())
                .operatorName(user.getName())
                .cashPlanVersionId(costCashPlanVersion.getId())
                .cashPlanVersion(costCashPlanVersion.getVersionNo())
                .projectId(projectId)
                .build();
        BaseBuildEntityUtil.buildInsert(newCostCostManageVersion);
        // 编辑成本估算状态
        if (null != costManageVersion) {
            costManageVersion.setVersionStatus(VersionStatusEnum.HISTORY.getValue());
            costManageVersionMapper.updateById(costManageVersion);
        }

        // 绑定售前成本版本
        if (CostBudgetTypeEnum.ABCB.getValue().equals(costBudgetType) || CostBudgetTypeEnum.BBCB.getValue().equals(costBudgetType)) {
            CostManageVersion lastPreSalesVersion = getLatestConfirmedPreSalesCost(projectId);
            newCostCostManageVersion.setPreSaleVersionId(null != lastPreSalesVersion ? lastPreSalesVersion.getId() : null);
            newCostCostManageVersion.setPreSaleVersionName(null != lastPreSalesVersion ? lastPreSalesVersion.getVersionName() : null);
        }

        Long newVersionId = newCostCostManageVersion.getId();
        costManageVersionMapper.insert(newCostCostManageVersion);

        // 查询所有（人工成本、费用报销、外采费用）成本预算明细对应已用预算、剩余预算、已确认成本
        Map<String, DeliverCostBudgetListVO> costBudgetMap = icostDeliverTaskService.getUsedAllCostBudget(costManageVersion);

        // 获取所有要计算的成本估算结果
        List<CostManageEstimationResultsSaveDTO.CostManageEstimationResultsEntry> requestEntries = new ArrayList<>();
        CollUtil.addAll(requestEntries, request.getResultEntries());
        // 根据科目和税率进行分组合计 key-accountOaId+taxRate value-成本估算结果明细集合
        Map<String, List<CostManageEstimationResultsSaveDTO.CostManageEstimationResultsEntry>> requestEntriesMap =
                requestEntries.stream()
                        .filter(e -> !CostManageSourceEnum.XTZDSC.getValue().equals(e.getSource()))
                        .collect(Collectors.groupingBy(e -> StrUtil.format("{}-{}", e.getAccountOaId(), e.getTaxRate())));

        // 获取当前的成本科目配置信息
        List<CostConfigAccount> costConfigAccountList =
                CollUtil.emptyIfNull(costConfigAccountMapper.getCurrentVersionCostSubjectConfigInfoList(null));
        Map<Long, CostConfigAccount> costConfigAccountMap =
                costConfigAccountList.stream().collect(Collectors.toMap(CostConfigAccount::getOaId, Function.identity()));

        List<CostManageEstimationResults> saveEntries = new ArrayList<>();
        Map<String, CostManageEstimationResults> saveEntriesMap = new HashMap<>();
        requestEntriesMap.forEach((k, v) -> {
            CostManageEstimationResultsSaveDTO.CostManageEstimationResultsEntry firstItem = CollUtil.getFirst(v);
            // 获取累计金额
            BigDecimal budgetAmountIncludedTax = v.stream()
                    .map(CostManageEstimationResultsSaveDTO.CostManageEstimationResultsEntry::getBudgetAmountIncludedTax)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal budgetAmountExcludingTax = v.stream()
                    .map(CostManageEstimationResultsSaveDTO.CostManageEstimationResultsEntry::getBudgetAmountExcludingTax)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            DeliverCostBudgetListVO deliverCostBudget = costBudgetMap.getOrDefault(k, null);
            BigDecimal usedBudget = Optional.ofNullable(deliverCostBudget)
                    .map(DeliverCostBudgetListVO::getUsedBudget)
                    .orElse(BigDecimal.ZERO);

            // 判断是否小于已用预算
            if (!BigDecimal.ZERO.equals(usedBudget) && budgetAmountIncludedTax.compareTo(usedBudget) < 0) {
                throw new ServiceException(StrUtil.format("科目【{}】的预算金额不可少于已用预算{}元！", firstItem.getAccountName(), usedBudget));
            }

            CostManageEstimationResults resultItem = BeanUtil.copyProperties(firstItem, CostManageEstimationResults.class);
            CostConfigAccount costConfigAccount = costConfigAccountMap.get(firstItem.getAccountOaId());
            if (null != costConfigAccount) {
                resultItem.setAccountName(costConfigAccount.getAccountName());
            }
            resultItem.setVersionId(newVersionId);
            resultItem.setProjectId(projectId);
            resultItem.setCostBudgetType(firstItem.getCostBudgetType() != null ? firstItem.getCostBudgetType() : costBudgetType);
            resultItem.setBudgetAmountIncludedTax(budgetAmountIncludedTax);
            resultItem.setBudgetAmountExcludingTax(budgetAmountExcludingTax);
            BaseBuildEntityUtil.buildInsert(resultItem);
            saveEntries.add(resultItem);
            saveEntriesMap.put(k, resultItem);
        });

        // 获取所有绑定人员测算明细数据
        DictKvVo defaultTaxRate = idictService.getDictKvList("税率").getData().stream()
                .filter(item -> "0".equals(item.getName()))
                .findFirst().orElse(new DictKvVo("0", "5"));
        List<CostManagePersonnelLevelDetail> savePersonnelLevelDetails = new ArrayList<>();
        Map<String, List<CostManagePersonnelLevelDTO>> allPersonnelLevelEntriesMap = request.getResultEntries().stream()
                .filter(e -> CollUtil.isNotEmpty(e.getPersonnelLevelEntries()))
                .flatMap(entry -> entry.getPersonnelLevelEntries().stream())
                .collect(toList())
                .stream()
                .collect(Collectors.groupingBy(e -> StrUtil.format("{}-{}", e.getAccountOaId(), defaultTaxRate.getValue())));
        if (CollUtil.isNotEmpty(allPersonnelLevelEntriesMap)) {
            allPersonnelLevelEntriesMap.forEach((k, v) -> {
                CostManageEstimationResults resultItem = saveEntriesMap.getOrDefault(k, new CostManageEstimationResults());
                CollUtil.addAll(savePersonnelLevelDetails, assemblePersonnelLevelDetail(v, resultItem));
            });
        }

        // 保存自定义补贴数据
        List<CostManagePersonnelCustomDetailDto> customDetails = request.getResultEntries().stream()
                .filter(e -> CollUtil.isNotEmpty(e.getPersonnelCustomDetailEntries()))
                .flatMap(entry -> entry.getPersonnelCustomDetailEntries().stream())
                .collect(toList());
        List<CostManagePersonnelCustomDetail> savePersonnelCustomDetails = new ArrayList<>();
        if (CollUtil.isNotEmpty(customDetails)) {
            // 保存自定义补贴明细数据以及结果项关联
            Map<String, List<CostManagePersonnelCustomDetailDto>> allPersonnelCustomDetailsMap = customDetails.stream()
                    .collect(Collectors.groupingBy(e -> StrUtil.format("{}-{}", e.getAccountOaId(), defaultTaxRate.getValue())));
            if (CollUtil.isNotEmpty(allPersonnelCustomDetailsMap)) {
                allPersonnelCustomDetailsMap.forEach((k, v) -> {
                    CostManageEstimationResults resultItem = saveEntriesMap.getOrDefault(k, new CostManageEstimationResults());
                    CollUtil.addAll(savePersonnelCustomDetails, assemblePersonnelCustomDetail(v, resultItem));
                });
            }
        }

        // 批量持久化成本估算结果明细数据
        baseMapper.batchSave(saveEntries);
        if (CollUtil.isNotEmpty(savePersonnelLevelDetails)) {
            costManagePersonnelLevelDetailMapper.batchSave(savePersonnelLevelDetails);
            List<CalculateLaborCostDTO> laborCostDTOList = savePersonnelLevelDetails.stream()
                    .map(CalculateLaborCostDTO::build).collect(toList());
            // 计算人工成本
            Map<Long, CostSalaryDTO> salaryMap = costConfigLevelPriceService.batchCalculateLaborCostByConfigId(laborCostDTOList);
            // 保存成本薪资明细
            costSalaryDetailsService.batchSaveOrUpdate(salaryMap.values());
        }
        if (CollUtil.isNotEmpty(savePersonnelCustomDetails)) {
            // 去重自定义补贴
            List<CostManagePersonnelCustomDetail> distinctList = savePersonnelCustomDetails.stream()
                    .collect(Collectors.toMap(
                            CostManagePersonnelCustomDetail::getSubsidyCustomConfigId,
                            dto -> dto,
                            (existing, replacement) -> existing
                    ))
                    .values()
                    .stream()
                    .collect(toList());
            costManagePersonnelCustomDetailMapper.batchSave(distinctList);
        }

        // 售前成本估算消息推送
        costManageVersionService.preSaleEstimateMessagePush(newVersionId);

        // 同步基线版本记录
        baselineVersionRecordService.syncCostBaselineVersionRecord(projectId, StrUtil.EMPTY, newCostCostManageVersion.getCostBudgetType());

        // 同步绑定现金流版本
        if (null == costCashPlanVersion.getCostVersionId()) {
            costCashPlanVersionMapper.updateCostVersion(costCashPlanVersion.getId(), newVersionId, newVersionName);
        }

    }

    /**
     * 组装成本管理人员级别测算明细实体类集合
     *
     * @param personnelLevelEntries 成本管理人员级别测算明细集合
     * @param resultItem            成本估算结果明细实体类
     * @return
     */
    private List<CostManagePersonnelLevelDetail> assemblePersonnelLevelDetail(
            List<CostManagePersonnelLevelDTO> personnelLevelEntries,
            CostManageEstimationResults resultItem) {
        if (CollUtil.isEmpty(personnelLevelEntries) || null == resultItem) {
            return null;
        }

        Long versionId = resultItem.getVersionId();
        Long estimationResultsId = resultItem.getId();
        List<CostManagePersonnelLevelDetail> saveEntries = new ArrayList<>(personnelLevelEntries.size());
        for (CostManagePersonnelLevelDTO personnelLevelEntry : personnelLevelEntries) {
            CostManagePersonnelLevelDetail personnelLevelDetail =
                    BeanUtil.copyProperties(personnelLevelEntry, CostManagePersonnelLevelDetail.class);
            personnelLevelDetail.setAccountId(resultItem.getAccountId());
            personnelLevelDetail.setAccountName(resultItem.getAccountName());
            personnelLevelDetail.setVersionId(versionId);
            personnelLevelDetail.setEstimationResultsId(estimationResultsId);

            // 保存自定义补贴金额
            List<CostManagePersonnelCustomDetailDto> customSubsidyCostList =
                    personnelLevelEntry.getCustomSubsidyCostList();
            String customSubsidyCostStr = CollUtil.isNotEmpty(customSubsidyCostList)
                    ? JSONUtil.toJsonStr(customSubsidyCostList)
                    : StrUtil.EMPTY;
            personnelLevelDetail.setCustomSubsidyCost(customSubsidyCostStr);

            BaseBuildEntityUtil.buildInsert(personnelLevelDetail);
            saveEntries.add(personnelLevelDetail);
        }

        return saveEntries;
    }

    /**
     * 组装成本人员自定义补贴明细实体类集合
     *
     * @param personnelCustomDetailEntries 成本人员自定义补贴明细集合
     * @param resultItem                   成本估算结果明细实体类
     * @return
     */
    private List<CostManagePersonnelCustomDetail> assemblePersonnelCustomDetail(
            List<CostManagePersonnelCustomDetailDto> personnelCustomDetailEntries,
            CostManageEstimationResults resultItem) {
        if (CollUtil.isEmpty(personnelCustomDetailEntries) || null == resultItem) {
            return null;
        }

        Long versionId = resultItem.getVersionId();
        Long estimationResultsId = resultItem.getId();
        List<CostManagePersonnelCustomDetail> saveEntries = new ArrayList<>(personnelCustomDetailEntries.size());
        for (CostManagePersonnelCustomDetailDto personnelCustomDetailEntry : personnelCustomDetailEntries) {
            CostManagePersonnelCustomDetail customerDetail =
                    BeanUtil.copyProperties(personnelCustomDetailEntry, CostManagePersonnelCustomDetail.class);
            customerDetail.setVersionId(versionId);
            customerDetail.setEstimationResultsId(estimationResultsId);
            BaseBuildEntityUtil.buildInsert(customerDetail);
            saveEntries.add(customerDetail);
        }
        return saveEntries;
    }


    @Override
    public CostManageListVO getCostManageListVO(Long projectId, List<Integer> costBudgetTypeList) {
        CostManageListVO costManageListVO = new CostManageListVO();
        //查询最新版本
        CostManageVersionDTO costManageVersionDTO = CostManageVersionDTO.builder()
                .projectId(projectId)
                .costBudgetTypeList(costBudgetTypeList)
                .build();
        CostManageVersion currentVersion = costManageVersionMapper.getLatestCostManageEstimation(costManageVersionDTO);
        if (null != currentVersion) {
            //查询当前最新预算成本列表
            List<CostManageEstimationResultsVO> latestResults = baseMapper.findByVersionId(Arrays.asList(currentVersion.getId()));
            //获取成本科目费用集合
            List<CostManageEstimationResultsVO> costList =
                    getAssembleEstimationResultsVOList(currentVersion, latestResults, null, currentVersion.getCashPlanVersionId());
            costManageListVO.setCostList(costList);
            //查询当前使用的版本配置
            costManageListVO.setCurrVersionList(getCostConfigVersionList(currentVersion));
            //获取版本名称及预算类型
            costManageListVO.setCostBudgetType(currentVersion.getCostBudgetType());
            costManageListVO.setCostBudgetTypeName(EnumUtils.getNameByValue(CostBudgetTypeEnum.class, currentVersion.getCostBudgetType()));
            costManageListVO.setVersionId(currentVersion.getId());
            costManageListVO.setVersionName(currentVersion.getVersionName());
            //查询预算状态及流程
            buildStatus(costManageListVO, currentVersion);
        }
        return costManageListVO;
    }

    /**
     * 查询预算状态及流程
     */
    private void buildStatus(CostManageListVO costManageListVO, CostManageVersion currentVersion) {
        //获取流程
        costManageListVO.setRequestId(currentVersion.getRequestId());
        costManageListVO.setRequestName(currentVersion.getRequestName());

        // 如果requestId为空，说明是草稿状态
        if (null == currentVersion.getRequestId()) {
            costManageListVO.setStatus(CostManageStatusEnum.DRAFT.getValue());
            costManageListVO.setStatusName(CostManageStatusEnum.DRAFT.getName());
            return;
        }

        //查询OA流程状态
        Integer currentNodeType = getCurrentNodeType(currentVersion.getRequestId());
        costManageListVO.setRequestStatus(CostRequestStatusEnum.getValByNodeType(currentNodeType));
        costManageListVO.setRequestStatusName(CostRequestStatusEnum.getNameByNodeType(currentNodeType));
        // 根据currentNodeType判断状态
        CostManageStatusEnum costManageStatus = getCostManageStatusByCurrentNodeType(currentNodeType);
        costManageListVO.setStatus(costManageStatus.getValue());
        costManageListVO.setStatusName(costManageStatus.getName());
        if (null == currentNodeType) {
            costManageListVO.setRequestId(null);
            costManageListVO.setRequestName(null);
            costManageListVO.setStatus(CostManageStatusEnum.DRAFT.getValue());
            costManageListVO.setStatusName(CostManageStatusEnum.DRAFT.getName());
        }
    }

    /**
     * 根据requestId获取当前流程状态
     *
     * @param requestId 关联流程ID
     * @return
     */
    private Integer getCurrentNodeType(Long requestId) {
        if (null == requestId) {
            return null;
        }
        List<CostManageVersionVO> costManageVersionList =
                dbApiUtil.getOaRequestStatusToObj(Arrays.asList(requestId), CostManageVersionVO.class);
        Integer currentNodeType = ObjectUtil.isEmpty(costManageVersionList) ? null : costManageVersionList.get(0).getRequestStatus();
        return currentNodeType;
    }

    /**
     * 根据currentNodeType获取状态
     *
     * @param currentNodeType 当前流程节点状态
     * @return
     */
    public static CostManageStatusEnum getCostManageStatusByCurrentNodeType(Integer currentNodeType) {
        if (currentNodeType == null) {
            return CostManageStatusEnum.DRAFT;
        } else if (currentNodeType.equals(CostRequestStatusEnum.UNCOMMIT.getCurrentNodeType())) {
            return CostManageStatusEnum.UNCOMMITTED;
        } else if (currentNodeType.equals(CostRequestStatusEnum.FINISH.getCurrentNodeType())) {
            return CostManageStatusEnum.CONFIRMED;
        } else {
            return CostManageStatusEnum.UNCONFIRMED;
        }
    }

    @Override
    public CostManageListVO getCostManageById(Long versionId) {
        CostManageVersion costManageVersion = costManageVersionService.getById(versionId);
        CostManageListVO costManageListVO = new CostManageListVO();
        if (costManageVersion == null) {
            return costManageListVO;
        }
        BeanUtil.copyProperties(costManageVersion, costManageListVO);
        //预算类型
        costManageListVO.setVersionId(costManageVersion.getId());
        costManageListVO.setCostBudgetType(costManageVersion.getCostBudgetType());
        costManageListVO.setCostBudgetTypeName(EnumUtils.getNameByValue(CostBudgetTypeEnum.class, costManageVersion.getCostBudgetType()));
        //查询预算状态及流程
        buildStatus(costManageListVO, costManageVersion);
        //对应版本
        costManageListVO.setCurrVersionList(getCostConfigVersionList(costManageVersion));
        //成本列表
        List<CostManageEstimationResultsVO> latestResults = baseMapper.findByVersionId(Arrays.asList(versionId));
        List<CostManageEstimationResultsVO> costList =
                getAssembleEstimationResultsVOList(costManageVersion, latestResults, null, costManageVersion.getCashPlanVersionId());
        costManageListVO.setCostList(costList);
        return costManageListVO;
    }

    @Override
    public List<CostManageConfigVersionVO> findConfigVersion(Long projectId) {
        CostManageVersion costManageVersion =
                costManageVersionMapper.getLatestCostManageEstimation(CostManageVersionDTO.builder().projectId(projectId).build());
        // 获取当前成本估算使用版本
        return null != costManageVersion ? getCostConfigVersionList(costManageVersion) : ListUtil.empty();
    }

    private List<CostManageConfigVersionVO> getCostConfigVersionList(CostManageVersion costManageVersion) {
        List<CostManageConfigVersionVO> costConfigVersionList = baseMapper.getCostConfigVersion(costManageVersion.getId(), costManageVersion.getProjectId());
        if (CollUtil.isNotEmpty(costConfigVersionList)) {
            costConfigVersionList.forEach(e ->
                    e.setCostConfigVersionTypeTxt(EnumUtils.getNameByValue(CostConfigVersionTypeEnum.class, e.getCostConfigVersionType())));
        }
        return costConfigVersionList;
    }

    @Override
    public List<CostManageEstimationResultsVO> calculateEstimationResults(CostManageCalculateDTO request) {
        List<CostManagePersonnelLevelDTO> personnelLevelEntries = request.getPersonnelLevelEntries();
        List<CostManagePersonnelCustomDetailDto> customDetailEntries = CollUtil.emptyIfNull(request.getCustomDetailEntries());
        // 人员级别预计花费
        BigDecimal levelPriceCost = BigDecimal.ZERO;
        // 项目管理费用
        BigDecimal projectManagerCost = BigDecimal.ZERO;
        List<CostManagePersonnelLevelDetailVO> personnelLevelVoList = new ArrayList<>(personnelLevelEntries.size());
        List<CostManagePersonnelCustomDetailVO> customDetailList = new ArrayList<>(customDetailEntries.size());
        // 获取职位等级名称
        Map<Long, String> positionGradeNameMap = IEhrService.getPositionGradeNameMap();
//        Map<Long, String> positionGradeNameMap = new HashMap<>();

        for (CostManagePersonnelLevelDTO entry : personnelLevelEntries) {
            // 人员级别费用合计
            levelPriceCost = levelPriceCost.add(entry.getLevelPriceCost());
            // 差旅费和住宿费用合计
            if (null != entry.getEstimatedStayCost()) {
                projectManagerCost = projectManagerCost.add(entry.getEstimatedStayCost());
            }
            if (null != entry.getTravelSubsidyCost()) {
                projectManagerCost = projectManagerCost.add(entry.getTravelSubsidyCost());
            }

            // 自定义补贴金额累计
            List<CostManagePersonnelCustomDetailDto> customSubsidyCostList = CollUtil.emptyIfNull(entry.getCustomSubsidyCostList());
            for (CostManagePersonnelCustomDetailDto dto : customSubsidyCostList) {
                projectManagerCost = projectManagerCost.add(dto.getSubsidyCustomAmount());
            }

            CostManagePersonnelLevelDetailVO vo = BeanUtil.copyProperties(entry, CostManagePersonnelLevelDetailVO.class);
            vo.setPersonnelLevelName(positionGradeNameMap.get(entry.getPersonnelLevel()));
            personnelLevelVoList.add(vo);
        }

        if (CollUtil.isNotEmpty(customDetailEntries)) {
            customDetailList.addAll(customDetailEntries.stream().map(CostManagePersonnelCustomDetailVO::of).collect(toList()));
        }

        // 组装成本估算结果明细
        List<CostManageEstimationResultsVO> estimationResults = new ArrayList<>();
        Long parentResultId = IdWorker.getId();

        // 父级测算数据处理
        CostManageEstimationResultsVO levelPriceCostResult = new CostManageEstimationResultsVO();
        levelPriceCostResult.setId(parentResultId);
        levelPriceCostResult.setAccountId(request.getAccountId());
        levelPriceCostResult.setAccountOaId(request.getAccountOaId());
        levelPriceCostResult.setAccountName(request.getAccountName());
        levelPriceCostResult.setBudgetAmountIncludedTax(levelPriceCost);
        levelPriceCostResult.setBudgetAmountExcludingTax(levelPriceCost);
        levelPriceCostResult.setPersonnelLevelList(personnelLevelVoList);
        levelPriceCostResult.setCustomDetailList(customDetailList);
        estimationResults.add(levelPriceCostResult);

        // 获取最新的项目管理费用科目
        List<CostConfigAccount> costConfigAccountList =
                CollUtil.emptyIfNull(costConfigAccountMapper.getCurrentVersionCostSubjectConfigInfoList(null));
        Map<Long, CostConfigAccount> costConfigAccountMap =
                costConfigAccountList.stream().collect(Collectors.toMap(CostConfigAccount::getOaId, Function.identity()));
        CostConfigAccount projectManagerAccount = costConfigAccountList.stream()
                .filter(item -> item.getAccountName().equals(PROJECT_MANAGER_ACCOUNT_NAME)).findFirst().orElse(new CostConfigAccount());
        Long projectManagerAccountId = projectManagerAccount.getId();
        Long projectManagerAccountOaId = projectManagerAccount.getOaId();

        // 项目管理费用处理
        CostManageEstimationResultsVO projectManagerResult = new CostManageEstimationResultsVO();
        projectManagerResult.setId(IdWorker.getId());
        projectManagerResult.setParentId(parentResultId);
        projectManagerResult.setAccountId(projectManagerAccountId);
        projectManagerResult.setAccountOaId(projectManagerAccountOaId);
        projectManagerResult.setAccountName(PROJECT_MANAGER_ACCOUNT_NAME);
        projectManagerResult.setBudgetAmountIncludedTax(projectManagerCost);
        projectManagerResult.setBudgetAmountExcludingTax(projectManagerCost);
        estimationResults.add(projectManagerResult);

        // 通用字段赋值
        DictKvVo defaultTaxRate = idictService.getDictKvList("税率").getData().stream()
                .filter(item -> "0".equals(item.getName()))
                .findFirst().orElse(new DictKvVo("0", "5"));
        estimationResults.forEach(item -> {
            item.setSource(CostManageSourceEnum.RGCBCS.getValue());
            item.setCostBudgetType(request.getCostBudgetType());
            CostConfigAccount configAccount = costConfigAccountMap.getOrDefault(item.getAccountOaId(), new CostConfigAccount());
            item.setAccountCategoryId(configAccount.getAccountCategoryId());
            item.setAccountCategoryName(configAccount.getAccountCategoryName());
            item.setTaxRate(Integer.valueOf(defaultTaxRate.getValue()));
            item.setTaxRateTxt(defaultTaxRate.getName());
        });

        List<CostConfigAccount> currentVersionAccountList =
                costConfigAccountMapper.getCurrentVersionCostSubjectConfigInfoList(null);
        Map<Long, CostConfigAccount> currentVersionAccountMap =
                CollUtil.emptyIfNull(currentVersionAccountList).stream()
                        .collect(Collectors.toMap(CostConfigAccount::getOaId, Function.identity()));

        return getAssembleEstimationResultsVOList(null, estimationResults, currentVersionAccountMap, null);
    }

    /**
     * 同步OA项目预算台账最新成本管理信息内容
     *
     * @Description
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void getProjectBudgetInfo() {
    }

    @Override
    public CostManageSelectVO findCostSelect(CostManageSelectDTO dto) {
        CostManageSelectVO selectListVO = new CostManageSelectVO();
        //查询最新且确认版本
        List<CostManageVersionVO> costManageVersionVOList = costManageVersionService.getHistoryVersions(
                new PageRequest(1, 1000),
                CostManageVersionDTO.builder().projectId(dto.getProjectId()).build()).getRecords();
        CostManageVersionVO costManageVersionVO = costManageVersionVOList.stream().filter(
                costManageVersion -> CostManageStatusEnum.CONFIRMED.getValue().equals(costManageVersion.getStatus())).findFirst().orElse(null);
        if (ObjectUtil.isEmpty(costManageVersionVO)) {
            return selectListVO;
        }

        selectListVO = baseMapper.findCostManageSelectListVO(costManageVersionVO.getVersionId(), dto.getAccountId());

        return selectListVO;
    }

    @Override
    public List<CostManageEstimationResultsVO> findProcessEstimationResults(CostManageVersionDTO request, OAFormTypeEnum businessTypeEnum) {
        Long projectId = request.getProjectId();
        List<Integer> costBudgetTypeList = request.getCostBudgetTypeList();
        CostManageVersion preSaleVersionVO;
        CostManageVersion totalVersionVO;
        List<CostManageVersionVO> costManageVersionList = costManageVersionMapper.queryProjectVersionByProjectId(projectId, null);
        if (costBudgetTypeList.contains(CostBudgetTypeEnum.SQCB.getValue())) {
            // 获取最新的售前成本
            request.setCostBudgetTypeList(Arrays.asList(CostBudgetTypeEnum.SQCB.getValue()));
            preSaleVersionVO = costManageVersionMapper.getLatestCostManageEstimation(request);
            // 获取最新已确认的项目总成本
            Map<Long, CostManageStatusEnum> statusEnumMap = dbApiUtil.getOaRequestStatusToObj(costManageVersionList.stream().map(CostManageVersionVO::getRequestId).collect(toList()), CostManageVersionVO.class).stream()
                    .collect(Collectors.toMap(CostManageVersionVO::getRequestId, e -> getCostManageStatusByCurrentNodeType(e.getRequestStatus()), (a, b) -> a));
            totalVersionVO = costManageVersionList.stream()
                    .filter(version -> CostManageStatusEnum.CONFIRMED.equals(statusEnumMap.get(version.getRequestId())) && !CostBudgetTypeEnum.SQCB.getValue().equals(version.getCostBudgetType())) // 筛选已确认状态
                    .sorted(Comparator.comparing(CostManageVersionVO::getCtime).reversed()) // 按创建时间倒序
                    .findFirst()
                    .map(vo -> {
                        CostManageVersion version = new CostManageVersion();
                        BeanUtil.copyProperties(vo, version);
                        return version;
                    })
                    .orElse(null);
        } else {
            request.setCostBudgetTypeList(Arrays.asList(CostBudgetTypeEnum.BBCB.getValue(), CostBudgetTypeEnum.ABCB.getValue()));
            totalVersionVO = Optional.ofNullable(costManageVersionMapper.getLatestCostManageEstimation(request)).orElse(null);
            Map<Long, CostManageStatusEnum> statusEnumMap = dbApiUtil.getOaRequestStatusToObj(costManageVersionList.stream().map(CostManageVersionVO::getRequestId).collect(toList()), CostManageVersionVO.class).stream()
                    .collect(Collectors.toMap(CostManageVersionVO::getRequestId, e -> getCostManageStatusByCurrentNodeType(e.getRequestStatus()), (a, b) -> a));
            preSaleVersionVO = costManageVersionList.stream()
                    .filter(version -> CostManageStatusEnum.CONFIRMED.equals(statusEnumMap.get(version.getRequestId())) && CostBudgetTypeEnum.SQCB.getValue().equals(version.getCostBudgetType())) // 筛选已确认状态
                    .sorted(Comparator.comparing(CostManageVersionVO::getCtime).reversed()) // 按创建时间倒序
                    .findFirst()
                    .map(vo -> {
                        CostManageVersion version = new CostManageVersion();
                        BeanUtil.copyProperties(vo, version);
                        return version;
                    })
                    .orElse(null);
        }

        List<CostManageEstimationResultsVO> results = new ArrayList<>();
        if (null != preSaleVersionVO) {
            List<CostManageEstimationResultsVO> preSaleVersionResults = baseMapper.findByVersionId(Arrays.asList(preSaleVersionVO.getId()));
            CollUtil.addAll(results, preSaleVersionResults);
        }
        if (null != totalVersionVO) {
            List<CostManageEstimationResultsVO> totalVersionResults = CollUtil.emptyIfNull(baseMapper.findByVersionId(Arrays.asList(totalVersionVO.getId()))).stream()
                    .filter(e -> null != e.getAccountOaId() && !xsfyOaId.equals(e.getAccountOaId()) && !xmglfysqrgOaId.equals(e.getAccountOaId()))
                    .collect(toList());
            CollUtil.addAll(results, totalVersionResults);
        }

        // 查询是否有发起过项目立项B表
        LambdaQueryWrapper<CostManageVersion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CostManageVersion::getProjectId, projectId);
        queryWrapper.eq(CostManageVersion::getRequestType, 1);
        List<Long> requestIds = CollUtil.emptyIfNull(costManageVersionService.getBaseMapper().selectList(queryWrapper)).stream()
                .map(CostManageVersion::getRequestId)
                .filter(Objects::nonNull)
                .collect(toList());
        boolean confirmBBCB = dbApiUtil.getOaRequestStatusToObj(requestIds, CostManageVersionVO.class).stream()
                .map(e -> getCostManageStatusByCurrentNodeType(e.getRequestStatus()))
                .filter(Objects::nonNull)
                .collect(toList())
                .contains(CostManageStatusEnum.CONFIRMED);
        List<CostConfigAccount> currentVersionAccountList =
                costConfigAccountMapper.getCurrentVersionCostSubjectConfigInfoList(null);

        // 带入垫资成本
        if (confirmBBCB || OAFormTypeEnum.XMLXBB.equals(businessTypeEnum)) {
            CostManageEstimationResultsVO advanceFundingCost = getAdvanceFundingCost(projectId,
                    costBudgetTypeList.contains(CostBudgetTypeEnum.SQCB.getValue())
                            ? preSaleVersionVO.getCashPlanVersionId()
                            : totalVersionVO == null ? null
                            : totalVersionVO.getCashPlanVersionId(),
                    currentVersionAccountList
            );
            Optional.ofNullable(advanceFundingCost).ifPresent(results::add);
        }

        return results;
    }

    @Override
    public List<CostManageEstimationResultsVO> findByVersionId(List<Long> versionIdList) {
        if (CollUtil.isEmpty(versionIdList)) {
            return ListUtil.empty();
        }
        List<CostManageEstimationResultsVO> resultsVOList = baseMapper.findByVersionId(versionIdList);
        return CollUtil.emptyIfNull(resultsVOList);
    }

}
