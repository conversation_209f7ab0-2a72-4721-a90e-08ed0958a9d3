package com.gok.pboot.pms.entity.vo;

import lombok.Data;

/**
 * 项目组成员表VO
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-17 11:11:29
 */
@Data
public class ProjectMemberVO {

    /**
     * id
     */
    private Long id;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 角色
     */
    private String memberRole;

    /**
     * 级别
     */
    private Integer memberLevel;

    /**
     * 级别
     */
    private String memberLevelName;

    /**
     * 数量
     */
    private Integer memberNumber;

    /**
     * 预计投入周期
     */
    private String expectedCycle;

    /**
     * 预计投入度（百分比）
     */
    private String expectedEngagement;

    /**
     * 指派人ID
     */
    private Long assignorId;

    /**
     * 指派人
     */
    private String assignor;

}
