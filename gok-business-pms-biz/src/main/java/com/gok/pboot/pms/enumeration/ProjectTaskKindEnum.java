package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 任务类型
 *
 * <AUTHOR>
 * @date 2024/01/19
 */
@AllArgsConstructor
@Getter
public enum ProjectTaskKindEnum implements ValueEnum<Integer> {

    /**
     * 售前支撑
     */
    PRE_SALES_SUPPORT(0, "售前支撑"),

    /**
     * 售后交付
     */
    AFTER_SALES_DELIVERY(1, "售后交付");

    private final Integer value;

    private final String name;

}
