package com.gok.pboot.pms.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
    * 合同台账相关流程信息vo
    * <AUTHOR>
*/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ContractProcessListVO {

    /**
    * 流程类型名
    */
    private String processTypeName;

    /**
     * 归档日期
     */
    private  String filingDate ;

    /**
     * 流程信息
     */
    private List<ContractProcessInfoVO> infoVOList;

}