package com.gok.pboot.pms.handler;

import com.alibaba.excel.context.AnalysisContext;
import com.gok.components.common.user.PigxUser;
import com.gok.module.excel.api.domain.vo.ErrorMessageVo;
import com.gok.module.excel.api.enums.FunctionEnum;
import com.gok.module.excel.api.listener.ListAnalysisEventListener;
import com.gok.module.excel.api.support.Validators;
import com.gok.module.file.entity.SysFile;
import lombok.extern.slf4j.Slf4j;

import javax.validation.ConstraintViolation;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * 人才复用 PersonnelReuseAnalysisEventListener
 *
 * <AUTHOR>
 * @date 2022/9/1
 */
@Slf4j
public class PersonnelReuseAnalysisEventListener extends ListAnalysisEventListener<Object> {

	private final List<Object> list = new ArrayList<>();

	private final List<ErrorMessageVo> errorMessageList = new ArrayList<>();

	private Long lineNum = 1L;

	@Override
	public void invoke(Object o, AnalysisContext analysisContext) {

		lineNum++;

		if (lineNum == 2){
			return;
		}

		Set<ConstraintViolation<Object>> violations = Validators.validate(o);
		if (!violations.isEmpty()){
			Set<String> messageSet = violations.stream().map(ConstraintViolation::getMessage)
					.collect(Collectors.toSet());
			errorMessageList.add(new ErrorMessageVo(lineNum, messageSet));
		}
		else {
			list.add(o);
		}
	}

	@Override
	public void invokeHeadMap(Map headMap, AnalysisContext context) {
		log.info("解析到的表头数据: {}", headMap);
		list.add(headMap.get(0));
	}

	@Override
	public void doAfterAllAnalysed(AnalysisContext analysisContext) {
		log.debug("Excel read analysed");
	}

	@Override
	public List<Object> getList() {
		return list;
	}

	@Override
	public <T> void validCustomize(Class aClass, List<T> list, PigxUser pigxUser) throws ParseException {

	}

	@Override
	public void intData(FunctionEnum functionEnum, SysFile sysFile, PigxUser pigxUser, int i) {

	}

	@Override
	public List<ErrorMessageVo> getErrors() {
		return errorMessageList;
	}

}
