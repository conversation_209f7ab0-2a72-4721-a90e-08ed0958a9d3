package com.gok.pboot.pms.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 商机变更日志记录vo
 *
 * <AUTHOR>
 * @date 2023/11/24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BusinessDataLogVO {

    /**
     * 数据日志ID
     */
    private Long id;

    /**
     * 商机ID(项目ID)
     */
    private Long businessId;

    /**
     * 数据变更描述
     */
    private String dataChangeRecord;

    /**
     * 变更人员ID
     */
    private Long dataChangeUserId;

    /**
     * 变更人员姓名
     */
    private String dataChangeUserName;

    /**
     * 变更人所在部门id
     */
    private Long dataChangeUserDeptId;

    /**
     * 变更人员所在部门
     */
    private String dataChangeUserDept;

    /**
     * 数据变更时间
     */
    private String dataChangeTime;

    /**
     * 更新结果
     *
     * @param r 商机变更日志记录vo
     */
    public static void updateResultParam(BusinessDataLogVO r) {
        // 处理返回值：若变更人姓名、变更人所在部门为空时，将返回值的null置为空字符
        String dataChangeUserName = r.getDataChangeUserName();
        r.setDataChangeUserName(dataChangeUserName == null ? " " : dataChangeUserName);
        String dataChangeUserDept = r.getDataChangeUserDept();
        r.setDataChangeUserDept(dataChangeUserDept == null ? " " : dataChangeUserDept);
    }
}
