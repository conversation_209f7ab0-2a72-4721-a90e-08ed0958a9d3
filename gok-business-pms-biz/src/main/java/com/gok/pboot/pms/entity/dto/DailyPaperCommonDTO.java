package com.gok.pboot.pms.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @desc 日报、复用工时、交付工时公共DTO
 * @createTime 2023/2/20 9:49
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class DailyPaperCommonDTO {
    /**
     * 日报id
     */
    private Long dailyPaperId;
    /**
     * 项目id
     */
    private Long projectId;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 提交日期
     */
    private LocalDate submissionDate;
    /**
     * 提交人id
     */
    private Long userId;

    /**
     * 汇总工时（天）
     */
    private BigDecimal aggregatedDays;
}
