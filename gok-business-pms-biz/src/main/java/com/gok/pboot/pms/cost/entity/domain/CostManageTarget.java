package com.gok.pboot.pms.cost.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 成本目标管理
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("cost_manage_target")
public class CostManageTarget extends BeanEntity<Long> {

    private static final long serialVersionUID = 1L;

    /**
     * 版本ID
     */
    private Long versionId;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 项目需求
     */
    private String projectRequirements;

    /**
     * 交付要求
     */
    private String deliveryRequirements;

    /**
     * 交付物
     */
    private String deliveryItems;

    /**
     * 交付期限
     */
    private String deliveryDeadline;

    /**
     * 交付地点
     */
    private String deliveryPlace;

    /**
     * 质保期
     */
    private String warrantyPeriod;

    /**
     * 保密要求
     */
    private String secrecyRequirements;

    /**
     * 其他要求
     */
    private String otherRequirements;

    /**
     * 其他要求附件id，多个逗号隔开
     */
    private String detailFiles;

}
