package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;

/**
 * 项目任务进度枚举
 *
 * <AUTHOR>
 * @version 1.1.0
 */
@AllArgsConstructor
public enum ProjectTaskProgressEnum implements ValueEnum<Integer> {

    ZERO(0, "0%"),
    TEN(1, "10%"),
    TWENTY(2, "20%"),
    THIRTY(3, "30%"),
    FORTY(4, "40%"),
    FIFTY(5, "50%"),
    SIXTY(6, "60%"),
    SEVENTY(7, "70%"),
    EIGHTY(8, "80%"),
    NINETY(9, "90%"),
    HUNDRED(10, "100%")
    ;

    private final Integer value;

    private final String name;

    @Override
    public Integer getValue() {
        return value;
    }

    @Override
    public String getName() {
        return name;
    }
}
