package com.gok.pboot.pms.Util;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.gok.components.common.constant.CommonConstants;
import com.gok.components.common.util.R;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.common.base.BaseConstants;
import com.gok.pboot.pms.common.base.BeanEntity;
import com.gok.pboot.pms.common.base.TenantBeanEntity;
import com.gok.pboot.pms.common.exception.ServiceException;
import com.gok.pboot.pms.enumeration.ApiResultEnum;
import com.gok.pboot.service.commons.base.ApiResult;

import java.sql.Timestamp;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2019-11-19 14:19
 * @desc 基础保存或更新填充字段
 **/
public class BaseBuildEntityUtil {

    /**
     * 添加或修改时补充字段
     *
     * @param record
     * @param <T>    继承{@link BeanEntity} 的泛型
     * @return
     */
    public static <T extends BeanEntity<Long>> T buildSave(T record) {
        if (record.getId() == null) {
            return buildInsert(record);
        }
        return buildUpdate(record);
    }

    /**
     * 添加时补充字段
     *
     * @param record
     * @param <T>    继承{@link BeanEntity} 的泛型
     * @return
     */
    public static <T extends BeanEntity<Long>> T buildInsert(T record) {
        record.setId(IdWorker.getId());
        record.setCreator(SecurityUtils.getUser() == null ? "" : SecurityUtils.getUser().getName());
        record.setCreatorId(SecurityUtils.getUser() == null ? 0 : SecurityUtils.getUser().getId());
        record.setCtime(new Timestamp(System.currentTimeMillis()));
        record.setDelFlag(BaseConstants.NO);
        return record;
    }

    /**
     * 添加时补充字段
     *
     * @param record
     * @param <T>    继承{@link BeanEntity} 的泛型
     * @return
     */
    public static <T extends BeanEntity<Long>> T buildInsertNoUser(T record, String creator) {
        record.setId(IdWorker.getId());
        record.setCreator(Optional.ofNullable(creator).orElse(""));
        record.setCreatorId(0L);
        record.setCtime(new Timestamp(System.currentTimeMillis()));
        record.setDelFlag(BaseConstants.NO);
        return record;
    }

    /**
     * 添加时补充字段 不添加id
     *
     * @param record
     * @param <T>    继承{@link BeanEntity} 的泛型
     * @return
     */
    public static <T extends BeanEntity<Long>> T buildInsertNoId(T record) {
        record.setCreator(SecurityUtils.getUser() == null ? "" : SecurityUtils.getUser().getName());
        record.setCreatorId(SecurityUtils.getUser() == null ? 0 : SecurityUtils.getUser().getId());
        record.setCtime(new Timestamp(System.currentTimeMillis()));
        record.setDelFlag(BaseConstants.NO);
        return record;
    }

    /**
     * 添加时补充字段 包含租户id
     *
     * @param record
     * @param <T>    继承{@link TenantBeanEntity} 的泛型
     * @return
     */
    public static <T extends TenantBeanEntity<Long>> T tenantBuildInsert(T record) {
        buildInsert(record);
        record.setTenantId(SecurityUtils.getUser() == null ? 0 : SecurityUtils.getUser().getTenantId());
        return record;
    }

    /**
     * 编辑时补充字段
     *
     * @param record
     * @return
     */
    public static <T extends BeanEntity<Long>> T buildUpdate(T record) {
        record.setModifier(SecurityUtils.getUser() == null ? "" : SecurityUtils.getUser().getName());
        record.setModifierId(SecurityUtils.getUser() == null ? 0 : SecurityUtils.getUser().getId());
        record.setMtime(new Timestamp(System.currentTimeMillis()));
        return record;
    }

    /**
     * 生成数据时同步编辑时间为生成时间
     *
     * @param record
     * @return
     */
    public static <T extends BeanEntity<Long>> T setModify(T record) {
        record.setModifier(record.getCreator());
        record.setModifierId(record.getCreatorId());
        record.setMtime(record.getCtime());
        return record;
    }

    /**
     * 判断微服务调用是否成功
     *
     * @param apiResult
     * @return
     */
    public static <T> T apiResult(ApiResult<T> apiResult) {
        if (apiResult.getCode() == ApiResultEnum.SUCCESS.getValue()) {
            return apiResult.getData();
        } else {
            //如果有具体异常信息，返回具体异常信息，如果没有返回基础异常信息
            String errorMsg = apiResult.getMsg();
            if (apiResult.getData() instanceof String && StringUtils.isNotBlank(apiResult.getData().toString())) {
                errorMsg = apiResult.getData().toString();
            }
            throw new ServiceException(errorMsg);
        }
    }

    /**
     * API 结果
     *
     * @param apiResult API 结果
     * @return {@link T }
     */
    public static <T> T apiResult(R<T> apiResult) {
        if (apiResult.getCode() == CommonConstants.SUCCESS) {
            return apiResult.getData();
        } else {
            //如果有具体异常信息，返回具体异常信息，如果没有返回基础异常信息
            String errorMsg = apiResult.getMsg();
            if (apiResult.getData() instanceof String && StringUtils.isNotBlank(apiResult.getData().toString())) {
                errorMsg = apiResult.getData().toString();
            }
            throw new ServiceException(errorMsg);
        }
    }
}
