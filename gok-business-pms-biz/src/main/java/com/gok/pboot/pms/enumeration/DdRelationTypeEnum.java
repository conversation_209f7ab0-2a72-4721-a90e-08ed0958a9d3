package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 滴滴关联类型枚举
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Getter
@AllArgsConstructor
public enum DdRelationTypeEnum implements ValueEnum<Integer>{

    /**
     * 项目关联
     */
    PROJECT(1, "项目"),

    /**
     * 人员关联
     */
    USER(2, "人员");

    /**
     * 类型值
     */
    private final Integer value;

    /**
     * 类型描述
     */
    private final String name;


}
