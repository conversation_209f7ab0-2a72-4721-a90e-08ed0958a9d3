package com.gok.pboot.pms.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 销售收款明细
 * @TableName sales_receipt_details
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName(value ="sales_receipt_details")
@ApiModel("销售收款明细")
public class SalesReceiptDetails extends Model<SalesReceiptDetails> {
    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键id")
    private Long id;

    /**
     * 销售收款主表ID
     */
    @ApiModelProperty(value = "销售收款主表ID")
    private Long mainId;

    /**
     * 款项名称
     */
    @ApiModelProperty(value = "款项名称")
    private String paymentName;

    /**
     * 款项占比(预留字段)
     */
    @ApiModelProperty(value = "款项占比(预留字段)")
    private String proportionFunds;

    /**
     * 款项金额(预留字段)
     */
    @ApiModelProperty(value = "款项金额(预留字段)")
    private BigDecimal fundsAmount;

    /**
     * 预计收(付)款日期
     */
    @ApiModelProperty(value = "预计收(付)款日期")
    private String expectedDate;

    /**
     * 实际收(付)款日期
     */
    @ApiModelProperty(value = "实际收(付)款日期")
    private String actualDate;

    /**
     * 收(付)款金额（含税）
     */
    @ApiModelProperty(value = "收(付)款金额（含税）")
    private BigDecimal paymentAmountIncludingTax;

    /**
     * 税率
     */
    @ApiModelProperty(value = "税率")
    private Integer taxRate;

    /**
     * 收(付)款金额(不含税)
     */
    @ApiModelProperty(value = "收(付)款金额(不含税)")
    private BigDecimal paymentAmount;

    /**
     * 收(付)款条件（项目进度）
     */
    @ApiModelProperty(value = "收(付)款条件（项目进度）")
    private String projectSchedule;

    /**
     * 收(付)款状态
     */
    @ApiModelProperty(value = "收(付)款状态")
    private Integer paymentStatus;

    /**
     * 收(付)款状态Txt
     */
    private String paymentStatusTxt;

    /**
     * 收(付)款备注
     */
    @ApiModelProperty(value = "收(付)款备注")
    private String paymentRemark;

    /**
     * 发票开具状态
     */
    @ApiModelProperty(value = "发票开具状态")
    private Integer invoiceStatus;

    /**
     * 发票开具状态txt
     */
    private String invoiceStatusTxt;

    /**
     * 实际开票日期
     */
    @ApiModelProperty(value = "实际开票日期")
    private String actualInvoicingDate;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remarks;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * delFlag
     */
    @TableLogic(value = "0", delval = "1")
    @ApiModelProperty(value = "delFlag")
    private String delFlag;

    /**
     * 所属租户
     */
    @ApiModelProperty(value = "所属租户", hidden = true)
    private Long tenantId;
}