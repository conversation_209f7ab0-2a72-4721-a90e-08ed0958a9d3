package com.gok.pboot.pms.cost.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * 工单退回原因 DTO
 *
 * <AUTHOR>
 * @date 2025/01/15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CostTaskReturnReasonDTO {

    /**
     * 退回原因
     */
    @NotBlank(message = "退回原因不能为空")
    @Length(max = 200, message = "退回原因长度不能超过200")
    private String returnReason;

}
