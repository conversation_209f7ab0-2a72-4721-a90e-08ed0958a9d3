package com.gok.pboot.pms.cost.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 计算人工成本 DTO
 *
 * <AUTHOR>
 * @date 2025/01/15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class CostCalculateLaborCostDTO {
    /**
     * 工单ID
     */
    @NotNull(message = "工单ID不能为空")
    private Long taskId;

    /**
     * 正常工时
     */
    private BigDecimal normalHours;

    /**
     * 工作日加班工时
     */
    private BigDecimal workOvertimeHours;

    /**
     * 休息日加班工时
     */
    private BigDecimal restOvertimeHours;

    /**
     * 节假日加班工时
     */
    private BigDecimal holidayOvertimeHours;
} 