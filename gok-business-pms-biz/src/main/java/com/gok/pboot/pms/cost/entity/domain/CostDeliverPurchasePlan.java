package com.gok.pboot.pms.cost.entity.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 交付采购计划表
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("cost_deliver_purchase_plan")
public class CostDeliverPurchasePlan extends BeanEntity<Long>{

    private static final long serialVersionUID = 1L;


    /**
    * 项目ID
    */
    private Long projectId;

    /**
    * 采购物
    */
    private String purchaseItem;

    /**
    * 销售合同ID
    */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long saleContractId;

    /**
    * 销售合同名称
    */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String saleContractName;

    /**
    * 采购合同ID
    */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long purchaseContractId;

    /**
    * 采购合同名称
    */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String purchaseContractName;

    /**
    * 关联成本科目ID
    */
    private Long accountId;

    /**
     * 科目OA ID
     */
    private Long accountOaId;

    /**
    * 关联成本科目名称
    */
    private String accountName;

    /**
     * 税率
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer taxRate;

    /**
    * 预算金额（含税）
    */
    private BigDecimal budgetAmountIncludedTax;

    /**
    * 计划到货(场)日期
    */
    private LocalDate plannedDeliveryDate;

    /**
    * 当前状态
    */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String planStatus;

    /**
    * 实际到货(场)日期
    */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private LocalDate actualDeliveryDate;

    /**
    * 验收条件及状态,ids
    */
    private String acceptanceConditionsStatusIds;


}