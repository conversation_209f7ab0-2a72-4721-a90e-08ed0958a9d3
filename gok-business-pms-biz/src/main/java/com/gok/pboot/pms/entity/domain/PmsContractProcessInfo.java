package com.gok.pboot.pms.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
    * 合同台账相关流程信息表
    * <AUTHOR>
*/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("pms_contract_process_info")
public class PmsContractProcessInfo {
    /**
    * id
    */
    private Long id;

    /**
    * oa流程id
    */
    private Long requestId;

    /**
    * 流程编号
    */
    private String requestmark;

    /**
    * 项目id
    */
    private Long projectId;

    /**
    * 合同id
    */
    private Long contractId;

    /**
    * 申请人id
    */
    private Long applicatId;

    /**
    * 申请人
    */
    private String applicat;

    /**
     * 申请人oaId
     */
    private String creater;

    /**
    * 申请人部门id
    */
    private Long applicantDeptId;

    /**
    * 申请人部门
    */
    private String applicantDept;

    /**
    * 流程类型
    */
    private Integer processType;

    /**
    * 流程类型名
    */
    private String processTypeName;

    /**
    * 流程状态
    */
    private String status;

    /**
    * 流程名
    */
    private String name;

    /**
    * 归档时间
    */
    private Date filingDateTime;

    /**
    * 收入总额
    */
    private String srze;

    /**
    * 合同金额
    */
    private String htje;

    /**
    * 开票金额
    */
    private String kpje;

    /**
    * 作废金额
    */
    private String zfje;

    /**
    * 收入金额
    */
    private String srje;

    /**
     * 合同附件
     */
    private String htfj;

    /**
     * 合同附件已盖章
     */
    private String htfjygz;

    /**
    * 创建人
    */
    private String creator;

    /**
    * 创建人ID
    */
    private Long creatorId;

    /**
    * 修改人
    */
    private String modifier;

    /**
    * 修改人ID
    */
    private Long modifierId;

    /**
    * 创建时间
    */
    private Date ctime;

    /**
    * 修改时间
    */
    private Date mtime;

    /**
    * 删除标识
    */
    private Boolean delFlag;

}