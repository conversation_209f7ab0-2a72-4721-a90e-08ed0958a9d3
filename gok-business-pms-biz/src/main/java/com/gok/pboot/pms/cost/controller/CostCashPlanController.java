package com.gok.pboot.pms.cost.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.common.core.exception.BusinessException;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.cost.entity.dto.CostCashPlanDto;
import com.gok.pboot.pms.cost.entity.dto.CostCashPlanSaveDTO;
import com.gok.pboot.pms.cost.entity.vo.CostCashPlanVO;
import com.gok.pboot.pms.cost.entity.vo.CostCashPlanVersionVO;
import com.gok.pboot.pms.cost.service.ICostCashPlanService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * @menu 现金流管理
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/costCashPlan")
public class CostCashPlanController {

    private final ICostCashPlanService costCashPlanService;

    /**
     * 现金流列表查询
     *
     * @param projectId 项目ID
     * @param request   查询请求
     * @return
     */
    @GetMapping("/list/{projectId}")
    public ApiResult<List<CostCashPlanVO>> getCostCashPlanList(@PathVariable Long projectId, CostCashPlanDto request) {
        request.setProjectId(projectId);
        return ApiResult.success(costCashPlanService.getCostCashPlanList(request));
    }

    /**
     * 现金流流入流出预测
     *
     * @param dto
     * @return
     */
    @PostMapping
    public ApiResult<Long> saveCostCashPlan(@Valid @RequestBody CostCashPlanSaveDTO dto) {
        try {
            return ApiResult.success(costCashPlanService.saveCostCashPlan(dto));
        }catch (BusinessException e){
            return ApiResult.failure(e.getMsg());
        }
    }

    /**
     * 现金流历史版本分页查询
     *
     * @param projectId   项目ID
     * @param pageRequest 分页请求
     * @return {@link ApiResult }<{@link Page }<{@link CostCashPlanVersionVO }>>
     */
    @GetMapping("/version/{projectId}")
    public ApiResult<Page<CostCashPlanVersionVO>> getVersionList(@PathVariable Long projectId, PageRequest pageRequest) {
        return ApiResult.success(costCashPlanService.findVersionPage(projectId, pageRequest));
    }

}