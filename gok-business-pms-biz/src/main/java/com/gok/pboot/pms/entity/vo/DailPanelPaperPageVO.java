package com.gok.pboot.pms.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 个人面板日报详情
 * @createTime 2023/5/12 11:09
 */
@Data
public class DailPanelPaperPageVO {
    /**
     * 日报id
     */
    @ApiModelProperty("日报id")
    private Long id;
    /**
     * 项目名称
     */
    @ApiModelProperty("项目名称")
    private String projectName;
    /**
     * 项目id
     */
    @ApiModelProperty("项目id")
    private Long projectId;
    /**
     * 提交人id
     */
    @ApiModelProperty("提交人id")
    private Long userId;
    /**
     * 提交人姓名
     */
    @ApiModelProperty("提交人姓名")
    private String userRealName;
    /**
     * 任务名称
     */
    @ApiModelProperty("任务名称")
    private String taskName;
    /**
     * 任务ID
     */
    private Long taskId;
    /**
     * 提交日期
     */
    @ApiModelProperty("提交日期")
    private LocalDate submissionDate;

    /**
     * 提交日期带周
     */
    @ApiModelProperty("提交日期带周")
    private String submissionDateFormatted;

    /**
     * 正常工时
     */
    @ApiModelProperty("正常工时")
    private BigDecimal normalHours;
    /**
     * 加班工时
     */
    @ApiModelProperty("加班工时")
    private BigDecimal addedHours;
    /**
     * 工作内容
     */
    @ApiModelProperty("工作内容")
    private String description;
    /**
     * 昨日计划
     */
    @ApiModelProperty("昨日计划")
    private String yesterdayPlan;
    /**
     * 审核状态
     */
    @ApiModelProperty("审核状态")
    private Integer approvalStatus;
    /**
     * 审核状态
     */
    @ApiModelProperty("审核状态")
    private String approvalStatusName;
    /**
     * OA加班工时
     */
    private BigDecimal oaAddedHours;
    /**
     * 合计
     */
    private BigDecimal sum;

    /**
     * 销售人员名
     */
    private String salesmanUserName;
    /**
     * 项目经理人名
     */
    private String managerUserName;
    /**
     * 工时审核人姓名列表
     */
    private List<String> auditorNames;
    /**
     * 项目编号
     */
    @ApiModelProperty("项目编号")
    private String code;

    /**
     * 假期类型：1-法定节假日 0-普通休息日
     */
    private Integer holidayType;
}
