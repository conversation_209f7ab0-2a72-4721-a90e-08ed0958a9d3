package com.gok.pboot.pms.enumeration;

import lombok.Getter;

import java.math.BigDecimal;

/**
 * 风险等级枚举类
 *
 * <AUTHOR>
 * @since 2023-07-14
 **/
@Getter
public enum RiskLevelEnum implements ValueEnum<Integer> {

    /**
     * 低风险：<30%发生风险的可能性
     */
    LOW(0, "低风险", BigDecimal.valueOf(0.0000), BigDecimal.valueOf(29.9999)),

    /**
     * 中风险： 30-60%发生风险的可能性
     */
    MIDDLE(1, "中风险", BigDecimal.valueOf(30.0000), BigDecimal.valueOf(60.0000)),

    /**
     * 高风险： >60%发生风险的可能性
     */
    HIGH(2, "高风险", BigDecimal.valueOf(60.0001), BigDecimal.valueOf(100.0000));

    /**
     * 值
     */
    private Integer value;

    /**
     * 名称
     */
    private String name;

    /**
     * 发生概率最小范围
     */
    private BigDecimal min;

    /**
     * 发生概率最大范围
     */
    private BigDecimal max;

    RiskLevelEnum(Integer value, String name, BigDecimal min, BigDecimal max) {
        this.value = value;
        this.name = name;
        this.min = min;
        this.max = max;
    }

}
