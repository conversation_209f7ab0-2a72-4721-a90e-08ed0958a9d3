package com.gok.pboot.pms.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.common.base.R;
import com.gok.pboot.pms.entity.domain.ProjectInfo;
import com.gok.pboot.pms.entity.domain.ProjectWeekly;
import com.gok.pboot.pms.entity.dto.ProjectWeeklyAllExcelDTO;
import com.gok.pboot.pms.entity.dto.ProjectWeeklyDTO;
import com.gok.pboot.pms.entity.dto.ProjectWeeklyExcelDTO;
import com.gok.pboot.pms.entity.vo.*;
import com.gok.pboot.pms.enumeration.ProcessInfoStatusEnum;
import org.springframework.validation.BindingResult;

import java.util.List;
import java.util.Map;

/**
 * 项目周报表
 *
 * <AUTHOR>
 * @date 2023-07-11 17:05:26
 */
public interface IProjectWeeklyService extends IService<ProjectWeekly> {

    /**
     * 查询项目周报列表
     *
     * @param pageRequest 分页请求实体
     * @param filter      查询参数
     * @return {@link Page}{@link ProjectWeeklyVO}
     */
    Page<ProjectWeeklyVO> findPage(PageRequest pageRequest, Map<String, Object> filter);

    /**
     * 查询项目周报列表
     *
     * @param pageRequest 分页请求实体
     * @param filter      查询参数
     * @return {@link Page}{@link ProjectWeeklyVO}
     */
    Page<ProjectWeeklyVO> findAllPage(PageRequest pageRequest, Map<String, Object> filter);

    /**
     * 获取周报工时数据
     *
     * @param filter 请求
     * @return {@link ProjectWeeklyPreSaveDataVo}
     */
    ProjectWeeklyPreSaveDataVo findPreSaveData(Map<String, Object> filter);

    /**
     * 批量获取周报工时数据
     *
     * @param filterList 查询请求集合
     * @return key-项目id+开始时间+结束时间,value-周报工时数据
     */
    Map<String, ProjectWeeklyPreSaveDataVo> batchFindPreSaveData(List<Map<String, Object>> filterList);

    /**
     * 项目周报导出
     *
     * @param pageRequest 分页请求实体
     * @param filter      查询参数
     * @return {@link Page}{@link ProjectWeeklyVO}
     */
    List<ProjectWeeklyVO> export(PageRequest pageRequest, Map<String, Object> filter);

    /**
     * 全部项目周报导出
     *
     * @param pageRequest 分页请求实体
     * @param filter      查询参数
     * @return {@link Page}{@link ProjectWeeklyAllVO}
     */
    List<ProjectWeeklyAllVO> exportAll(PageRequest pageRequest, Map<String, Object> filter);

    /**
     * 项目周报导入
     *
     * @param projectId         项目id
     * @param projectName       项目名称
     * @param projectDepartment 业务归属部门
     * @param excelVOList       列表
     * @param projectInfoMap    项目信息
     * @return 导出文件
     */
    ApiResult importProjectWeekly(Long projectId, String projectName, String projectDepartment,
                                  List<ProjectWeeklyExcelDTO> excelVOList, BindingResult bindingResult,
                                  Map<Long, ProjectInfo> projectInfoMap);

    /**
     * 批量同步项目周报过程动态
     *
     * @param statusEnum 流程动态类型枚举
     * @param entityList 项目周报实体类
     */
    void syncProcessInfo(ProcessInfoStatusEnum statusEnum, List<ProjectWeekly> entityList);

    /**
     * 分页查询已关注的周报
     *
     * @param pageRequest 分页请求实体
     * @param filter      查询参数
     * @return {@link Page}{@link ProjectWeeklyVO}
     */
    Page<ProjectWeeklyVO> findAllAttentionPage(PageRequest pageRequest, Map<String, Object> filter);

    /**
     * 全部已关注周报导出
     *
     * @param pageRequest 分页请求实体
     * @param filter      查询参数
     * @return {@link Page}{@link ProjectWeeklyAllVO}
     */
    List<ProjectWeeklyAllVO> exportAllAttention(PageRequest pageRequest, Map<String, Object> filter);

    ProjectWeekUnreadNumVO getUnreadNum(Map<String, Object> filter);

    /**
     * 根据id查询项目周报详情
     *
     * @param id 项目周报ID
     * @return {@link ProjectWeeklyVO}
     */
    ProjectWeeklyVO findById(Long id);

    /**
     * 多项目周报导入
     *
     * @param excelVOList 列表
     * @return {@link ApiResult}
     */
    ApiResult importAllProjectWeekly(List<ProjectWeeklyAllExcelDTO> excelVOList, BindingResult bindingResult);

    /**
     * 未提交项目数、正常提交项目数，滞后提交数查询接口
     * @param filter 请求参数
     * @return {@link WeeklySubmitNum}
     */
    WeeklySubmitNum submitNum(Map<String, Object> filter);

    /**
     * 未提交项目、正常提交项目，滞后提交项目查询接口
     * @param pageRequest 分页参数
     * @param filter 请求参数
     * @return {@link Page}<{@link WeeklySubmitInfo}>
     */
    Page<WeeklySubmitInfo> submitInfo(PageRequest pageRequest, Map<String, Object> filter);
    /**
     * 保存or修改项目周报
     *
     * @param dto dto
     * @return {@link ApiResult}
     */
    Boolean saveOrUpdateProjectWeekly(ProjectWeeklyDTO dto);

    /**
     * 删除项目周报
     *
     * @param id 周报id
     * @return {@link R}
     */
    Object deleteById(Long id);
}

