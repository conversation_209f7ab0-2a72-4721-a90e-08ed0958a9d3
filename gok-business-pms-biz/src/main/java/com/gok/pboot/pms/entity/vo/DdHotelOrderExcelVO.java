package com.gok.pboot.pms.entity.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 滴滴酒店订单Excel导出VO
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
public class DdHotelOrderExcelVO {

    @ExcelProperty("订单号")
    private String orderNo;

    @ExcelProperty("入住人工号")
    private String checkinEmployeeNo;

    @ExcelProperty("入住人姓名")
    private String checkinPersonName;

    @ExcelProperty("入住人部门名称")
    private String checkinDeptName;

    @ExcelProperty("城市名称")
    private String cityName;

    @ExcelProperty("酒店名称")
    private String hotelName;

    @ExcelProperty("房间房型")
    private String roomType;

    @ExcelProperty("入住时间")
    private LocalDateTime checkinTime;

    @ExcelProperty("离店时间")
    private LocalDateTime checkoutTime;

    @ExcelProperty("企业实付金额")
    private BigDecimal companyActualPayment;

    @ExcelProperty("服务费")
    private BigDecimal serviceFee;

    @ExcelProperty("天数")
    private BigDecimal numberOfDays;

    @ExcelProperty("房间数")
    private BigDecimal numberOfRooms;

    @ExcelProperty("间夜")
    private BigDecimal roomNights;

    @ExcelProperty("单价")
    private BigDecimal unitPrice;

    @ExcelProperty("房间差标")
    private BigDecimal roomStandardDifference;

    @ExcelProperty("订单状态")
    private String orderStatus;

    @ExcelProperty("预订日期")
    private LocalDate bookingDate;

    @ExcelProperty("预订人工号")
    private String bookingEmployeeNo;

    @ExcelProperty("预订人姓名")
    private String bookingEmployeeName;

    @ExcelProperty("预订人部门名称")
    private String bookingDeptName;

    @ExcelProperty("出差申请单号")
    private String businessTripApplicationNo;

    @ExcelProperty("出差事由")
    private String businessTripReason;

    @ExcelProperty("成本中心名称")
    private String costCenterName;

    @ExcelProperty("所属项目名称")
    private String projectName;

    @ExcelProperty("项目编码")
    private String projectCode;

    @ExcelProperty("所属公司名称")
    private String companyName;

    @ExcelProperty("所属账期")
    private String accountingPeriod;
}
