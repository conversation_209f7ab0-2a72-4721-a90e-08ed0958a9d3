package com.gok.pboot.pms.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.entity.dto.ProjectSystemProcessInfoDTO;
import com.gok.pboot.pms.entity.vo.ProjectProcessInfoFindPageVO;

import java.util.List;
import java.util.Map;

/**
 * 项目系统过程动态信息 服务类
 *
 * <AUTHOR>
 * @since 2023-07-14
 **/
public interface IProjectSystemProcessInfoService {

    /**
     * 请求OA接口获取跳转OA的token
     *
     * @return OA token
     */
    String getRedirectOaToken();

    /**
     * 分页查询项目过程信息（系统维护）
     *
     * @param pageRequest
     * @param filter
     * @return
     */
    Page<ProjectProcessInfoFindPageVO> findPage(PageRequest pageRequest, Map<String, Object> filter);

    /**
     * 内部调用，批量保存项目系统流程
     *
     * @param dtoList 批量保存请求实体集合
     */
    void innerBatchSave(List<ProjectSystemProcessInfoDTO> dtoList);


    // 项目周报动态前缀
    String WEEKLY_PROCESS_PREFIX = "项目周报：";

    // 项目周报动态时间格式
    String WEEKLY_PROCESS_TIME_FORMAT = "yyyy.MM.dd";

    // 项目会议纪要动态前缀
    String MEETING_PROCESS_PREFIX = "会议纪要：";

}
