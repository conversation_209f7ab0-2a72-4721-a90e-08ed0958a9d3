package com.gok.pboot.pms.controller;

import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.entity.dto.CustomerBusinessPersonDTO;
import com.gok.pboot.pms.service.ICustomerBusinessPersonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;


/**
 * <p>
 * 客户经营单元-经营组织架构（相关负责人 controller
 * </p>
 *
 * <AUTHOR>
 * @menu 客户经营单元-相关负责人
 * @since 2024-10-12
 */

@RestController
@RequestMapping("/customerBusinessPerson")
public class CustomerBusinessPersonController {
    @Autowired
    private ICustomerBusinessPersonService service;

    /**
     * 批量操作相关负责人
     *
     * @param customerBusinessPersonDTO dto对象
     * @return {@link ApiResult}
     */
    @PostMapping("/batchOperate")
    public ApiResult<String> batchOperate(@RequestBody CustomerBusinessPersonDTO customerBusinessPersonDTO) {
        if (Optional.ofNullable(customerBusinessPersonDTO.getPersons()).isPresent()) {
            service.batchCreate(customerBusinessPersonDTO.getPersons());
        }
        if (Optional.ofNullable(customerBusinessPersonDTO.getIds()).isPresent()) {
            service.batchDel(customerBusinessPersonDTO.getIds());
        }
        return ApiResult.success("操作成功");
    }
}
