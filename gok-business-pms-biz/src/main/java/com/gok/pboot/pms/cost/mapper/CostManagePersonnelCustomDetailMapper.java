package com.gok.pboot.pms.cost.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.cost.entity.domain.CostManagePersonnelCustomDetail;
import com.gok.pboot.pms.cost.entity.vo.CostManagePersonnelCustomDetailVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 成本管理人员级别自定义补贴明细 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Mapper
public interface CostManagePersonnelCustomDetailMapper extends BaseMapper<CostManagePersonnelCustomDetail> {

    /**
     * 批量保存
     *
     * @param saveEntries 保存对象集合
     */
    void batchSave(@Param("saveEntries") List<CostManagePersonnelCustomDetail> saveEntries);

    /**
     * 根据成本管理估算结果ID集合查询
     *
     * @param estimateResultIdList 成本管理估算结果ID集合
     * @return 预估结果集合
     */
    List<CostManagePersonnelCustomDetailVO> findByEstimateResultIdList(@Param("estimateResultIdList") List<Long> estimateResultIdList);

}
