package com.gok.pboot.pms.entity.vo;


import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gok.pboot.pms.enumeration.ProjectStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 合同台账列表vo
 *
 * <AUTHOR>
 * @date 2024/2/22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class ContractLedgerListVo {

    /**
     * 合同id
     */
    @ExcelIgnore
    private Long id;

    /**
     * 序号
     */
    @ExcelProperty({"序号"})
    private Integer xh;

    /**
     * 合同编号
     */
    @ExcelProperty({"合同编号"})
    private String htbh;

    /**
     * 合同名称
     */
    @ExcelProperty({"合同名称"})
    private String htmc;

    /**
     * 签约客户
     */
    @ExcelProperty({"签约客户"})
    private String khmcnewName;

    /**
     * 客户名称
     */
    @ExcelIgnore
    private String khmcnewId;

    /**
     * 供应商名称
     */
    @ExcelIgnore
    private String gysmcId;

    /**
     * 供应商名称
     */
    @ExcelProperty({"供应商名称"})
    private String gysmcName;

    /**
     * 合同细类
     */
    @ExcelIgnore
    private Integer htxl;
    /**
     * 合同细类
     */
    @ExcelProperty({"合同细类"})
    private String htxlText;

    /**
     * 合同金额（含税）
     */
    @ExcelProperty({"合同金额（含税）"})
    private String htjehs;



    /**
     * 对方名称id
     */
    @ExcelIgnore
    private String khmc;
    /**
     * 对方名称
     */
    @ExcelIgnore
    private String khmcName;



    /**
     * 合同金额(不含税)
     */
    @ExcelProperty({"合同金额(不含税)"})
    private String htjebhs;

    /**
     * 合同款项进度
     */
    @ExcelProperty({"合同款项进度"})
    private String htkxjd;


    /**
     * 合同风险项目(合计风险个数小于等于3个，显示黄色标识；合同合同风险大于3个，显示红色标识)
     */
    @ExcelProperty({"合同风险项"})
    private Integer riskProjectNum;

    /**
     * 累计已回款金额
     */
    @ExcelProperty({"累计已回款金额"})
    private String yskje;

    /**
     * 待回款金额
     */
    @ExcelProperty({"待回款金额"})
    private String dskje;



    /**
     * 合同状态
     */
    @ExcelIgnore
    private Integer htzt;

    /**
     * 合同状态
     */
    @ExcelIgnore
    private Integer htztOrder;

    /**
     * 合同状态
     */
    @ExcelProperty({"合同状态"})
    private String htztText;



    /**
     * 合同类别
     */
    @ExcelIgnore
    private Integer htlb;
    /**
     * 合同类别
     */
    @ExcelProperty({"合同类别"})
    private String htlbText;



    /**
     * 结算方式
     */
    @ExcelIgnore
    private Integer jsfs;
    /**
     * 结算方式
     */
    @ExcelProperty({"结算方式"})
    private String jsfsText;


    /**
     * 实际合同签订日期
     */
    @ExcelProperty({"实际合同签订日期"})
    private String sjhtqdrq;

    /**
     * 合同起始日期
     */
    @ExcelProperty({"合同起始日期"})
    private String htqsrq;

    /**
     * 合同截止日期
     */
    @ExcelProperty({"合同截止日期"})
    private String htjzrq;


    /**
     * 客户经理id
     */
    @ExcelIgnore
    private Long xmxsry;
    /**
     * 客户经理
     */
    @ExcelProperty({"客户经理"})
    private String xmxsryName;



    /**
     * 项目编号
     */
    @ExcelProperty({"项目编号"})
    private String xmbh;

    /**
     * 合同所属项目名称id
     */
    @ExcelIgnore
    private Long xmmc;

    /**
     * 项目名称
     */
    @ExcelProperty({"项目名称"})
    private String xmmcName;


    /**
     * 业务板块
     */
    @ExcelIgnore
    private Integer ywbk;
    /**
     * 业务板块
     */
    @ExcelProperty({"业务板块"})
    private String ywbkText;


    /**
     * 收入类型
     */
    @ExcelIgnore
    private Integer srlx;
    /**
     * 收入类型
     */
    @ExcelProperty({"收入类型"})
    private String srlxText;

    /**
     * 技术类型
     */
    @ExcelIgnore
    private Integer jslx;

    /**
     * 技术类型
     */
    @ExcelProperty({"技术类型"})
    private String jslxText;

    /**
     * 项目状态
     *
     * @see ProjectStatusEnum
     */
    @ExcelIgnore
    private String projectStatus;

    /**
     * 项目状态名称
     */
    @ExcelProperty({"项目状态"})
    private String projectStatusName;



    /**
     * 项目经理id
     */
    @ExcelIgnore
    private Long xmjl;
    /**
     * 项目经理
     */
    @ExcelProperty({"项目经理"})
    private String xmjlName;


    /**
     * 合同所属公司
     */
    @ExcelIgnore
    private Integer htssgs;
    /**
     * 合同所属公司
     */
    @ExcelProperty({"合同所属公司"})
    private String htssgsText;


    /**
     * 合同所属一级部门id
     */
    @ExcelIgnore
    private Long htssbm;
    /**
     * 合同所属一级部门
     */
    @ExcelProperty({"业务归属一级部门"})
    private String htssbmName;

    /**
     * 合同所属二级部门id
     */
    @ExcelIgnore
    private Long htssejbm;
    /**
     * 合同所属二级部门
     */
    @ExcelProperty({"业务归属二级部门"})
    private String htssejbmName;



    /**
     * 累计已开票金额
     */
    @ExcelProperty({"累计已开票金额"})
    private String ykpje;
    /**
     * 累计已开票金额
     */
    @ExcelProperty({"待开票金额"})
    private String dkpje;

    /**
     * 结项启动条件
     */
    @ExcelIgnore
    private Integer jxqdtj;
    /**
     * 结项启动条件
     */
    @ExcelProperty({"结项启动条件"})
    private String jxqdtjText;

    /**
     * 验收日期
     */
    @ExcelProperty({"验收日期"})
    private String ysrq;


    /**
     * 累计已验收金额
     */
    @ExcelProperty({"累计已验收金额"})
    private String ljyysje;

    /**
     * 待验收金额
     */
    @ExcelProperty({"待验收金额"})
    private String dysje;


    /**
     * 合同附件（已盖章）集合
     */
    @ExcelIgnore
    private List<OaFileInfoVo> htfjygzList;





















}
