package com.gok.pboot.pms.cost.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.cost.entity.domain.CostPresalesTaskConfig;
import com.gok.pboot.pms.cost.entity.dto.CostPresalesTaskConfigDTO;
import com.gok.pboot.pms.cost.entity.vo.CostPresalesTaskConfigVO;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 售前报工工单配置表 服务接口
 *
 * <AUTHOR>
 * @date 2025/04/09
 */
public interface ICostPresalesTaskConfigService extends IService<CostPresalesTaskConfig> {

    /**
     * 分页查询工单配置列表
     *
     * @param pageRequest 分页参数
     * @return 分页结果
     */
    Page<CostPresalesTaskConfigVO> findPage(PageRequest pageRequest);

    /**
     * 删除工单配置
     *
     * @param id 工单配置ID
     * @return 是否成功
     */
    void deletePresalesTaskConfig(Long id);

    /**
     * 批量编辑子工单
     *
     * @param batchDto 批量 DTO
     */
    void batchEditPresalesTaskConfig(CostPresalesTaskConfigDTO batchDto);

    /**
     * 批量保存默认工单配置
     *
     * @param batchDto 批量 DTO
     */
    void batchSaveDefaultTaskConfig(CostPresalesTaskConfigDTO batchDto);

    /**
     * 同步生成默认工单
     * 针对在建、商机状态的项目，同步生成对应的默认工单
     */
    void syncGenerateDefaultTask();
} 