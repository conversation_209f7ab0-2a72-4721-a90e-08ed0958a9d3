package com.gok.pboot.pms.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.common.base.R;
import com.gok.pboot.pms.entity.domain.ProjectStakeholderCustomer;
import com.gok.pboot.pms.entity.vo.ProjectStakeholderCustomerVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 项目干系人-客户
 * 
 * <AUTHOR>
 * @date 2023-07-11 17:05:26
 */
@Mapper
public interface ProjectStakeholderCustomerMapper extends BaseMapper<ProjectStakeholderCustomer> {

    /**
     * 查询项目客户列表
     *
     * @param projectId 项目id
     * @return {@link R}<{@link List}<{@link ProjectStakeholderCustomerVO}>>
     */
    List<ProjectStakeholderCustomer> getCustomerByProjectId(Long projectId);

}
