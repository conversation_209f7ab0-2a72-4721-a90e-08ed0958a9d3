package com.gok.pboot.pms.entity;

import com.gok.pboot.pms.common.base.BeanEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 交付人员工时实体
 */
@Data
public class PersonnelDeliveryHour extends BeanEntity<Long> {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 日期（存储为x年x月1日）
     */
    private LocalDate reuseDate;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 项目编号
     */
    private String projectCode;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目状态
     */
    private Integer projectStatus;

    /**
     * 收入归属（一级）部门ID
     */
    private Long revenueDeptId;

    /**
     * 人员名称
     */
    private String userRealName;

    /**
     * 工号
     */
    private String workCode;

    /**
     * 应出勤天数
     */
    private BigDecimal cwDueAttendance;

    /**
     * 出勤天数
     */
    private BigDecimal attendanceDays;

    /**
     * 项目耗用工时
     */
    private BigDecimal projectConsumed;

    /**
     * 税前工资
     */
    private BigDecimal grossPay;

    /**
     * 社保
     */
    private BigDecimal socialSecurity;

    /**
     * 公积金
     */
    private BigDecimal accumulationFund;

    /**
     * 工资成本
     */
    private BigDecimal wageCost;

    /**
     * 社保成本
     */
    private BigDecimal socialSecurityCost;

    /**
     * 公积金成本
     */
    private BigDecimal accumulationFundCost;

    /**
     * 合计
     */
    private BigDecimal total;

    /**
     * 项目成本合计
     */
    private BigDecimal totalProjectCost;

    /**
     * 工资发放地
     */
    private String payingPlace;

    /**
     * 人员归属部门
     */
    private Long deptId;

    /**
     * 人员归属部门名称
     */
    private String deptName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 导入人ID
     */
    private Long executorUserId;

    /**
     * 导入人姓名
     */
    private String executorUserRealName;

    /**
     * 审核状态（1=不通过，2=待审核，3=已通过）
     */
    private Integer approvalStatus;

    /**
     * 审核不通过原因
     */
    private String approvalReason;

    /**
     * 工作日正常工时（天）
     */
    private BigDecimal normalWorkDays;

    /**
     * 休息日加班工时（天）
     */
    private BigDecimal restWorkDays;

    /**
     * 节假日加班工时（天）
     */
    private BigDecimal holidaysWorkDays;

    /**
     * 工作日调休工时（天）
     */
    private BigDecimal ompensatoryDays;

}

