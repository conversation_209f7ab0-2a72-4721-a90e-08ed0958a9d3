package com.gok.pboot.pms.didi.dto;

import lombok.Data;

/**
 * 滴滴用户证件信息DTO
 * 根据滴滴企业版API文档构建：/river/Member/single
 *
 * <AUTHOR>
 * @date 2025/08/04
 */
@Data
public class DidiCardInfo {

    /**
     * 证件类型
     * 枚举值数字：
     * 1. 身份证
     * 2. 护照
     * 3. 港澳台居民居住证
     * 4. 台胞证
     * 5. 军官证
     * 6. 回乡证
     * 7. 外国人永久居留身份证
     */
    private Integer card_type;

    /**
     * 证件号码
     * 注：
     * 1、若采用AES256整体加密，此字段需明文传输，无需单独再加密
     * 2、若不整体加密传输时，此字段只可采用AES128加密传输
     * 3、若采用AES128整体加密，此字段仍需采用AES128单独加密（存在历史客户原因）
     */
    private String card_no;

    /**
     * 证件过期日期
     * 格式：2050-01-01
     * 注：
     * 1、若采用AES256整体加密，此字段需明文传输，无需单独再加密
     * 2、若不整体加密传输时，此字段只可采用AES128加密传输
     * 3、若采用AES128整体加密，此字段仍需采用AES128单独加密（存在历史客户原因）
     */
    private String expire_date;
}
