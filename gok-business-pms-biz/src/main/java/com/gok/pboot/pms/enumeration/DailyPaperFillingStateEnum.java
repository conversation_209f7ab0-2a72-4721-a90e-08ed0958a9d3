package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;

/**
 * - 日报填报状态枚举 -
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/8/24 11:56
 */
@AllArgsConstructor
public enum DailyPaperFillingStateEnum implements ValueEnum<Integer> {
    // 注释见name属性
    NORMAL(0, "正常"),
    LAG(1, "滞后");

    private final Integer value;
    private final String name;

    @Override
    public Integer getValue() {
        return value;
    }

    @Override
    public String getName() {
        return name;
    }
    public static String getNameByVal(Integer value) {
        for (DailyPaperFillingStateEnum statusEnum : DailyPaperFillingStateEnum.values()) {
            if (statusEnum.value .equals(value) ) {
                return statusEnum.name;
            }
        }
        return "";
    }
}
