package com.gok.pboot.pms.entity.vo;

import com.gok.pboot.pms.common.join.ProjectInDailyPaperEntry;
import com.google.common.base.Strings;
import lombok.*;

import java.util.List;

/**
 * - 在日报条目中展示的项目VO -
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/8/25 9:33
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@ToString
public class ProjectInDailyPaperEntryVO {
    /**
     * ID
     */
    private Long id;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 销售人员名
     */
    private String salesmanUserName;
    /**
     * 项目经理人名
     */
    private String managerUserName;
    /**
     * 工时审核人姓名列表
     */
    private List<String> auditorNames;

    /**
     * 是否内部项目
     */
    private Integer isInsideProject;

    /**
     * 项目状态
     * {@link com.gok.pboot.pms.enumeration.BusinessStatusEnum}
     */
    private Integer projectStatus;
    /**
     * 收藏项目实体ID
     */
    private Long collectId;
    /**
     * 收藏人id
     */
    private Long collectUserId;
    /**
     * 收藏时间
     */
    private String collectTime;
    public ProjectInDailyPaperEntryVO(ProjectInDailyPaperEntry join){
        this.id = join.getId();
        this.projectName = join.getProjectName();
        this.isInsideProject = join.getIsInsideProject();
        this.salesmanUserName = Strings.nullToEmpty(join.getSalesmanUserName());
        this.managerUserName = Strings.nullToEmpty(join.getManagerUserName());
        this.auditorNames = join.getAuditorNames();
        this.projectStatus = join.getProjectStatus();
        this.collectId = join.getCollectId();
        this.collectUserId = join.getCollectUserId();
        this.collectTime = join.getCollectTime();
    }
}
