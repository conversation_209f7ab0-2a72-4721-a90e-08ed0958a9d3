package com.gok.pboot.pms.cost.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 成本配置版本记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("cost_config_version")
public class CostConfigVersion extends BeanEntity<Long> {

    private static final long serialVersionUID = 1L;

    /**
     * 版本类型（0=差旅住宿标准，1=差旅补贴标准，2=自定义补贴，3=成本科目配置，4=人员级别单价配置）
     */
    private Integer versionType;

    /**
     * 版本名称
     */
    private String versionName;

    /**
     * 版本状态（0=当前版本，1=历史版本）
     */
    private Integer versionStatus;

    /**
     * 操作人id
     */
    private Long operatorId;

    /**
     * 操作人姓名
     */
    private String operatorName;

    /**
     * 操作人部门id
     */
    private Long operatorDeptId;

    /**
     * 操作人部门名称
     */
    private String operatorDeptName;
}
