package com.gok.pboot.pms.service.processor.impl;

import com.gok.pboot.pms.Util.BigDecimalUtils;
import com.gok.pboot.pms.Util.CollectionUtils;
import com.gok.pboot.pms.Util.CommonUtils;
import com.gok.pboot.pms.common.exception.ServiceException;
import com.gok.pboot.pms.entity.OvertimeLeaveData;
import com.gok.pboot.pms.entity.PayAttendanceData;
import com.gok.pboot.pms.entity.domain.Roster;
import com.gok.pboot.pms.entity.vo.CompensatoryLeaveDataVO;
import com.gok.pboot.pms.enumeration.LeaveStatusEnum;
import com.gok.pboot.pms.mapper.*;
import com.gok.pboot.pms.service.processor.SaturationCalculator;
import com.google.common.collect.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import javax.validation.ValidationException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 饱和度相关计算器
 *
 * <AUTHOR>
 * @version 1.1.0
 */
@Slf4j
@Component
@AllArgsConstructor
public class SaturationCalculatorImpl implements SaturationCalculator {

    private final PayAttendanceDataMapper payAttendanceDataMapper;

    private final HolidayMapper holidayMapper;

    private final RosterMapper rosterMapper;

    private final OvertimeLeaveDataMapper overtimeLeaveDataMapper;

    private final CompensatoryLeaveDataMapper compensatoryLeaveDataMapper;

    @Override
    @SuppressWarnings("all")
    public Map<Long, Pair<BigDecimal, BigDecimal>> calcAttendance(
            LocalDate startDate, LocalDate endDate, @Nullable Collection<Long> userIds
    ) {
        Map<Long, Roster> actualUserIdMap;
        Table<Long, LocalDate, BigDecimal> attendanceTable;
        Map<Long, List<OvertimeLeaveData>> userIdAndOvertimeLeaveDataMap;
        Map<Long, List<CompensatoryLeaveDataVO>> userIdAndCompensatoryLeaveDataMap;
        List<PayAttendanceData> payAttendanceDataList;
        Map<Long, BigDecimal> userIdAndEhrSalaryActualAttendance;
        List<Long> userIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(userIds)) {
            userIdList.addAll(userIds);
        }
        if (startDate.isAfter(endDate)){
            throw new ValidationException("参数异常：开始时间不能晚于结束时间");
        }
        actualUserIdMap = rosterMapper.findUserIdMap(userIds);
        if (actualUserIdMap.isEmpty()){
            return ImmutableMap.of();
        }
        payAttendanceDataList = payAttendanceDataMapper.findByDateRangeAndUserIds(startDate, endDate, userIds);
        userIdAndEhrSalaryActualAttendance = collectSalaryAccountingAttendance(payAttendanceDataList);
        attendanceTable = findAttendanceTable(payAttendanceDataList,true);
        userIdAndOvertimeLeaveDataMap =
                overtimeLeaveDataMapper.getLeaveDataListByUserIds(startDate, endDate, userIds)
                        .stream()
                        .collect(Collectors.groupingBy(OvertimeLeaveData::getOaId));
        userIdAndCompensatoryLeaveDataMap =

                compensatoryLeaveDataMapper.findByDateTimeRangeAndUserIds(startDate, endDate, userIdList,"3")
                        .stream()
                        .collect(Collectors.groupingBy(CompensatoryLeaveDataVO::getOaId));
        return Maps.transformEntries(
                actualUserIdMap,
                (uId, roster) -> Pair.of(calcAttendanceDate(
                        uId,
                        getActualDateRangesByRoster(startDate, endDate, roster),
                        userIdAndOvertimeLeaveDataMap,
                        userIdAndCompensatoryLeaveDataMap
                ), userIdAndEhrSalaryActualAttendance.getOrDefault(uId, BigDecimal.ZERO))
        );
    }

    /**
     * 从工资数据列表中收集用户的工资核算出勤天数（EHR实际出勤天数）
     * @param payAttendanceDataList 工资数据列表
     * @return 用户ID,工资核算出勤天数
     */
    private Map<Long, BigDecimal> collectSalaryAccountingAttendance(List<PayAttendanceData> payAttendanceDataList) {
        HashMap<Long, BigDecimal> result = Maps.newHashMapWithExpectedSize(payAttendanceDataList.size());

        payAttendanceDataList.forEach(payAttendanceData -> {
            Long userId = payAttendanceData.getOaId();
            BigDecimal hour = result.getOrDefault(userId, BigDecimal.ZERO);

            result.put(userId, hour.add(payAttendanceData.getCwActualAttendance()));
        });

        return result;
    }

    @Override
    public Map<Long, Pair<BigDecimal, BigDecimal>> saturationAttendance(
            LocalDate startDate, LocalDate endDate, @Nullable Collection<Long> userIds
    ) {

        Map<Long, Roster> actualUserIdMap;
        Map<Long, List<OvertimeLeaveData>> userIdAndOvertimeLeaveDataMap;
        List<PayAttendanceData> payAttendanceDataList;
        Map<Long, BigDecimal> userIdAndEhrSalaryActualAttendance;

        if (startDate.isAfter(endDate)){
            throw new ValidationException("参数异常：开始时间不能晚于结束时间");
        }
        actualUserIdMap = rosterMapper.findUserIdMap(userIds);
        if (actualUserIdMap.isEmpty()){
            return ImmutableMap.of();
        }
        payAttendanceDataList = payAttendanceDataMapper.findByDateRangeAndUserIds(startDate, endDate, userIds);
        userIdAndEhrSalaryActualAttendance = collectSalaryAccountingAttendance(payAttendanceDataList);
        userIdAndOvertimeLeaveDataMap =
                overtimeLeaveDataMapper.getLeaveDataListByUserIds(startDate, endDate, userIds)
                        .stream()
                        .collect(Collectors.groupingBy(OvertimeLeaveData::getOaId));

        return Maps.transformEntries(
                actualUserIdMap,
                (uId, roster) -> Pair.of(calcAttendance(
                        uId,
                        getActualDateRangesByRoster(startDate, endDate, roster),
                        userIdAndOvertimeLeaveDataMap
                ), userIdAndEhrSalaryActualAttendance.getOrDefault(uId, BigDecimal.ZERO))
        );
    }

    /**
     * 获取 用户ID-出勤月份-出勤天数 Table
     * @param payAttendanceDataList EHR工资表出勤数据
     * @param isActual 是否为实际考勤
     * @return 用户ID-出勤月份-出勤天数 Table
     */
    private Table<Long, LocalDate, BigDecimal> findAttendanceTable(
            List<PayAttendanceData> payAttendanceDataList, boolean isActual
    ){
        Table<Long, LocalDate, BigDecimal> result;

        if (payAttendanceDataList.isEmpty()){
            return ImmutableTable.of();
        }
        result = HashBasedTable.create();
        payAttendanceDataList.forEach(pa -> {
            Long uId = pa.getOaId();
            LocalDate payrollTime = pa.getPayrollTime().toLocalDate();
            BigDecimal attendance;

            //判断实际还是应出勤天数
            if (isActual) {
                attendance = pa.getCwActualAttendance();
            } else {
                attendance = pa.getCwDueAttendance();
            }

            BigDecimal existsAttendance;

            if (uId == null || payrollTime == null || attendance == null){
                return;
            }
            existsAttendance = ObjectUtils.defaultIfNull(result.get(uId, payrollTime), BigDecimal.ZERO);
            result.put(uId, payrollTime, existsAttendance.add(attendance));
        });

        return result;
    }

    /**
     * 根据开始、结束日期，结合用户入职、离职日期，计算出用户可计算工时的日期范围
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param roster 用户花名册信息
     * @return 可计算工时的日期范围（每月的开始日期、结束日期）
     */
    private List<Pair<LocalDate, LocalDate>> getActualDateRangesByRoster(
            LocalDate startDate, LocalDate endDate, Roster roster
    ){
        List<Pair<LocalDate, LocalDate>> result = Lists.newArrayList();
        LocalDate uStartDate = ObjectUtils.defaultIfNull(roster.getStartDate(), LocalDate.MIN);
        LocalDate uEndDate = ObjectUtils.defaultIfNull(roster.getEndDate(), LocalDate.MAX);
        LocalDate actualStartDate = uStartDate.isAfter(startDate) ? uStartDate : startDate;
        LocalDate actualEndDate = uEndDate.isBefore(endDate) ? uEndDate : endDate;
        LocalDate now = LocalDate.now();
        LocalDate nowActualEndDate = now.isBefore(actualEndDate) ? now : actualEndDate;

        int monthInstance = (nowActualEndDate.getYear() - actualStartDate.getYear()) * 12 +
                (nowActualEndDate.getMonthValue() - actualStartDate.getMonthValue());

        if (monthInstance == 0){
            result.add(Pair.of(actualStartDate, nowActualEndDate));
        } else {
            result.add(Pair.of(actualStartDate, actualStartDate.with(TemporalAdjusters.lastDayOfMonth())));
            result.add(Pair.of(nowActualEndDate.with(TemporalAdjusters.firstDayOfMonth()), nowActualEndDate));
            if (monthInstance > 1){
                IntStream.range(1, monthInstance).forEach(mi -> {
                    LocalDate nextStartDate = actualStartDate.plusMonths(mi);

                    result.add(Pair.of(
                            nextStartDate.with(TemporalAdjusters.firstDayOfMonth()),
                            nextStartDate.with(TemporalAdjusters.lastDayOfMonth())
                    ));
                });
            }
        }

        return result;
    }

    /**
     * 计算用户的实际出勤天数
     * @param dateRanges 日期范围列表
     * @return 花名册信息对应用户的实际出勤天数
     */
    private BigDecimal calcAttendance(
            Long userId,
            List<Pair<LocalDate, LocalDate>> dateRanges,
            Map<Long, List<OvertimeLeaveData>> userIdAndOvertimeLeaveDataMap
    ){
        BigDecimal resultDecimal = BigDecimal.ZERO;
        BigDecimal leaveTime;
        List<OvertimeLeaveData> leaveDataList = userIdAndOvertimeLeaveDataMap.getOrDefault(userId, ImmutableList.of());
        Map<LocalDate, BigDecimal> dateAndLeaveHourMap = Maps.newHashMapWithExpectedSize(31);
        BigDecimal hour;

        // 使用假期去预测出勤天数，扣除所有请休假时长
        if (!dateRanges.isEmpty()){
            resultDecimal = resultDecimal.add(calcAttendanceDaysByHoliday(dateRanges));
        }
        for (OvertimeLeaveData l : leaveDataList) {
            hour = dateAndLeaveHourMap.getOrDefault(l.getBelongdate(), BigDecimal.ZERO);
            if (LeaveStatusEnum.XJ.getValue().equals(l.getType())) {
                dateAndLeaveHourMap.put(l.getBelongdate(), hour.subtract(l.getHourData()));
            } else {
                dateAndLeaveHourMap.put(l.getBelongdate(), hour.add(l.getHourData()));
            }
        }
        leaveTime = dateAndLeaveHourMap.values()
                .stream()
                .map(h -> BigDecimalUtils.SEVEN_DECIMAL.compareTo(h) < 0 ? BigDecimalUtils.SEVEN_DECIMAL : h)
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO);
        if (leaveTime.compareTo(BigDecimal.ZERO) > 0) {
            resultDecimal = resultDecimal.subtract(CommonUtils.unitConversion(leaveTime));
        }

        return resultDecimal;
    }

    /**
     * 根据日期范围列表预测期间的实际出勤天数
     * @param dateRanges 日期范围列表
     * @return 预测的实际出勤天数
     */
    @Override
    public BigDecimal calcAttendanceDaysByHoliday(Collection<Pair<LocalDate, LocalDate>> dateRanges){
        BigDecimal allDays = dateRanges.stream()
                .map(dateRange -> dateRange.getFirst().until(dateRange.getSecond()).getDays() + 1)
                .reduce(Integer::sum)
                .map(BigDecimal::valueOf)
                .orElseThrow(() -> new ServiceException("根据假期预测出勤天数异常，日期范围列表：" + dateRanges));
        return allDays.subtract(BigDecimal.valueOf(holidayMapper.selectCountByDatePairs(dateRanges)));
    }


    /**
     * 计算用户的实际出勤天数
     * @param dateRanges 日期范围列表
     * @return 花名册信息对应用户的实际出勤天数
     */
    private BigDecimal calcAttendanceDate(
            Long userId,
            List<Pair<LocalDate, LocalDate>> dateRanges,
            Map<Long, List<OvertimeLeaveData>> userIdAndOvertimeLeaveDataMap,
            Map<Long, List<CompensatoryLeaveDataVO>> userIdAndCompensatoryLeaveDataMap
    ){
        BigDecimal resultDecimal = BigDecimal.ZERO;
        BigDecimal leaveTime;
        List<OvertimeLeaveData> leaveDataList = userIdAndOvertimeLeaveDataMap.getOrDefault(userId, ImmutableList.of());
        List<CompensatoryLeaveDataVO> compensatoryLeaveList = userIdAndCompensatoryLeaveDataMap.getOrDefault(userId, ImmutableList.of());

        Map<LocalDate, BigDecimal> dateAndLeaveHourMap = Maps.newHashMapWithExpectedSize(31);
        BigDecimal hour;

        // 使用假期去预测出勤天数，扣除所有请休假时长
        if (!dateRanges.isEmpty()){
            resultDecimal = resultDecimal.add(calcAttendanceDaysByHoliday(dateRanges));
        }
        
        // 处理请假数据
        for (OvertimeLeaveData l : leaveDataList) {
            // 判断请假日期是否在dateRanges范围内
            boolean isInRange = dateRanges.stream()
                    .anyMatch(range -> 
                            !l.getBelongdate().isBefore(range.getFirst()) && 
                            !l.getBelongdate().isAfter(range.getSecond()));
            
            if (isInRange) {
                hour = dateAndLeaveHourMap.getOrDefault(l.getBelongdate(), BigDecimal.ZERO);
                if (LeaveStatusEnum.XJ.getValue().equals(l.getType())) {
                    dateAndLeaveHourMap.put(l.getBelongdate(), hour.subtract(l.getHourData()));
                } else {
                    dateAndLeaveHourMap.put(l.getBelongdate(), hour.add(l.getHourData()));
                }
            }
        }
        
        leaveTime = dateAndLeaveHourMap.values()
                .stream()
                .map(h -> BigDecimalUtils.SEVEN_DECIMAL.compareTo(h) < 0 ? BigDecimalUtils.SEVEN_DECIMAL : h)
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO);
        if (leaveTime.compareTo(BigDecimal.ZERO) > 0) {
            resultDecimal = resultDecimal.subtract(CommonUtils.unitConversion(leaveTime));
        }

        // 处理调休数据
        if (CollectionUtils.isNotEmpty(compensatoryLeaveList)) {
            BigDecimal leaveHoursSum = compensatoryLeaveList.stream()
                    .filter(l -> {
                        // 判断调休日期是否在dateRanges范围内
                        return dateRanges.stream()
                                .anyMatch(range -> 
                                        !l.getBelongDate().isBefore(range.getFirst()) && 
                                        !l.getBelongDate().isAfter(range.getSecond()));
                    })
                    .map(CompensatoryLeaveDataVO::getHourData)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .setScale(2, RoundingMode.HALF_UP);
            if (leaveHoursSum.compareTo(BigDecimal.ZERO) > 0) {
                resultDecimal = resultDecimal.subtract(CommonUtils.unitConversion(leaveHoursSum));
            }
        }
        return resultDecimal;
    }
}
