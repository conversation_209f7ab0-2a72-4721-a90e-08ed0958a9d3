package com.gok.pboot.pms.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.enumeration.OrderTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 滴滴订单通用实体
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("")
public class DdCommonOrder {
    
    /**
     * 订单ID
     */
    private Long id;
    
    /**
     * 订单类型
     */
    private OrderTypeEnum orderType;
    
    /**
     * 订单编号
     */
    private String orderNo;
    
    /**
     * 企业实付金额
     */
    private BigDecimal companyActualPayment;

    /**
     * 服务费
     */
    private BigDecimal serviceFee;

    /**
     * 预订日期
     */
    private LocalDate bookingDate;

    /**
     * 预订人ID
     */
    private Long bookingUserId;

    /**
     * 预订人工号
     */
    private String bookingEmployeeNo;

    /**
     * 预订人姓名
     */
    private String bookingEmployeeName;

    /**
     * 预订人部门ID
     */
    private Long bookingDeptId;

    /**
     * 预订人部门名称
     */
    private String bookingDeptName;

    /**
     * 出差申请单号
     */
    private String businessTripApplicationNo;

    /**
     * 出差事由
     */
    private String businessTripReason;

    /**
     * 项目编码
     */
    private String projectCode;

    /**
     * 所属账期(YYYY-MM)
     */
    private String accountingPeriod;

    /**
     * 所属公司ID
     */
    private Long companyId;

    /**
     * 所属公司
     */
    private String companyName;

    /**
     * 成本中心名称
     */
    private String costCenterName;
    
    /**
     * 成本中心ID
     */
    private Long costCenterId;

    /**
     * OA部门ID
     */
    private Long oaDeptId;
    
    /**
     * 所属项目
     */
    private String projectName;
    
    /**
     * 项目ID
     */
    private Long projectId;
    
    /**
     * 项目状态
     */
    private Integer projectStatus;
    
    /**
     * 报销状态
     */
    private Integer initiationStatus;

    /**
     * 报销单号
     */
    private String expenseReportNo;
    
    /**
     * 发起日期
     */
    private LocalDate initiationDate;

    /**
     * 请求ID
     */
    private Long requestId;
    
    /**
     * 创建时间
     */
    private LocalDateTime ctime;
    
    /**
     * 更新时间
     */
    private LocalDateTime mtime;
    
    /**
     * 创建人
     */
    private String creator;
    
    /**
     * 更新人
     */
    private String modifier;

    // ==================== 机票订单特有字段 ====================
    /**
     * 乘机人ID
     */
    private Long passengerId;

    /**
     * 乘机人工号
     */
    private String passengerEmployeeNo;

    /**
     * 乘机人姓名
     */
    private String passengerName;

    /**
     * 乘机人部门ID
     */
    private Long passengerDeptId;

    /**
     * 乘机人部门名称
     */
    private String passengerDeptName;

    /**
     * 票号
     */
    private String ticketNo;

    /**
     * 票号状态
     */
    private String ticketStatus;

    /**
     * 出发地
     */
    private String departureLocation;

    /**
     * 到达地
     */
    private String arrivalLocation;

    /**
     * 航班号
     */
    private String flightNo;

    /**
     * 起飞时间
     */
    private LocalDateTime departureTime;

    /**
     * 降落时间
     */
    private LocalDateTime landingTime;

    // ==================== 酒店订单特有字段 ====================
    /**
     * 入住人ID
     */
    private Long checkinUserId;

    /**
     * 入住人工号
     */
    private String checkinEmployeeNo;

    /**
     * 入住人姓名
     */
    private String checkinPersonName;

    /**
     * 入住人部门ID
     */
    private Long checkinDeptId;

    /**
     * 入住人部门名称
     */
    private String checkinDeptName;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 房间房型
     */
    private String roomType;

    /**
     * 入住时间
     */
    private LocalDateTime checkinTime;

    /**
     * 离店时间
     */
    private LocalDateTime checkoutTime;

    /**
     * 天数
     */
    private BigDecimal numberOfDays;

    /**
     * 房间数
     */
    private BigDecimal numberOfRooms;

    /**
     * 间夜
     */
    private BigDecimal roomNights;

    /**
     * 单价
     */
    private BigDecimal unitPrice;

    /**
     * 房间差标
     */
    private BigDecimal roomStandardDifference;

    /**
     * 订单状态
     */
    private String orderStatus;

    // ==================== 用车订单特有字段 ====================
    /**
     * 乘坐时间
     */
    private LocalDateTime travelTime;

    /**
     * 到达时间
     */
    private LocalDateTime arrivalTime;

    /**
     * 用车类型
     */
    private String vehicleType;

    /**
     * 出发城市
     */
    private String departureCity;

    /**
     * 出发地址
     */
    private String departureAddress;

    /**
     * 到达城市
     */
    private String arrivalCity;

    /**
     * 到达地址
     */
    private String arrivalAddress;

    /**
     * 用车行驶距离(公里)
     */
    private BigDecimal travelDistance;

    /**
     * 支付类型
     */
    private String paymentType;
}