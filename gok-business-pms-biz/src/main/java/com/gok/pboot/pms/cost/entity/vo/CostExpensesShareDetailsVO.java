package com.gok.pboot.pms.cost.entity.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
    * 人力外包-费用分摊明细
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CostExpensesShareDetailsVO{

    /**
     * id
     */
    @ExcelIgnore
    private Long id;

    /**
     * 项目ID
     */
    @ExcelIgnore
    private Long projectId;

    /**
    * 收款人姓名
    */
    @ExcelProperty("姓名")
    @ColumnWidth(value = 30)
    private String recipientUserName;

    /**
    * 工号
    */
    @ExcelProperty("工号")
    @ColumnWidth(value = 30)
    private String workCode;

    /**
    * 所属部门id
    */
    @ExcelIgnore
    private Long departmentId;

    /**
    * 所属部门
    */
    @ExcelProperty("所属部门")
    @ColumnWidth(value = 30)
    private String departmentName;

    /**
     * 归属月份
     */
    @ExcelProperty("归属月份")
    @ColumnWidth(value = 30)
    private String belongingMonth;

    /**
     * 关联流程ID
     */
    @ExcelIgnore
    private Long requestId;

    /**
     * 报账编号
     */
    @ExcelProperty("报账编号")
    @ColumnWidth(value = 30)
    private String requestNumber;

    /**
    * 科目名称id
    */
    @ExcelIgnore
    private Long accountId;

    /**
    * 科目名称
    */
    @ExcelProperty("科目名称")
    @ColumnWidth(value = 30)
    private String accountName;

    /**
    * 费用项类别id
    */
    @ExcelIgnore
    private Long accountCategoryId;

    /**
    * 费用项类别
    */
    @ExcelProperty("费用项类别")
    @ColumnWidth(value = 30)
    private String accountCategoryName;

    /**
    * 报销金额
    */
    @ExcelProperty("报销金额")
    @ColumnWidth(value = 30)
    private BigDecimal reimburseMoney;

    /**
    * 申请人id
    */
    @ExcelIgnore
    private Long applicantId;

    /**
    * 申请人
    */
    @ExcelProperty("申请人")
    @ColumnWidth(value = 30)
    private String applicantName;

    /**
    * 申请日期
    */
    @ExcelProperty("申请日期")
    @ColumnWidth(value = 30)
    private String applicantTime;

    /**
    * 是否客户承担
    */
    @ExcelIgnore
    private Integer customerUndertakes;

    /**
     * 是否客户承担txt
     */
    @ExcelProperty("是否客户承担")
    @ColumnWidth(value = 30)
    private String customerUndertakesTxt;

    /**
     * 归档日期
     */
    @ExcelProperty("归档日期")
    @ColumnWidth(value = 30)
    private String filingTime;
}