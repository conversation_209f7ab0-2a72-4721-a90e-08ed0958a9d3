package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 预警等级枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum WarningLevelEnum implements ValueEnum<Integer> {

    /**
     * 0级
     */
    ZERO(0, "0级"),

    /**
     * 1级
     */
    ONE(1, "1级"),

    /**
     * 2级
     */
    TWO(2, "2级"),

    /**
     * 3级
     */
    THREE(3, "3级");

    private final Integer value;

    private final String name;
}
