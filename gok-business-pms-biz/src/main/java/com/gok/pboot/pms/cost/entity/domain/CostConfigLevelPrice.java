package com.gok.pboot.pms.cost.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 人员级别单价配置
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("cost_config_level_price")
public class CostConfigLevelPrice extends BeanEntity<Long> {

    private static final long serialVersionUID = 1L;

    /**
     * 版本ID
     */
    private Long versionId;

    /**
     * 职务id
     */
    private Long jobActivityId;

    /**
     * 地域
     */
    private String region;

    /**
     * 职级id
     */
    private Long personnelLevel;

    /**
     * 人员固定薪资单价（元/天）
     */
    private BigDecimal levelPrice;

    /**
     * 人员基本薪资单价（元/天）
     */
    private BigDecimal baseSalaryPrice;

    /**
     * 工资（元/天）
     */
    private BigDecimal salaryPerDay;

    /**
     * 社保（元/天）
     */
    private BigDecimal socialSecurityPerDay;

    /**
     * 公积金（元/天）
     */
    private BigDecimal housingFundPerDay;

    /**
     * 残保金（元/天）
     */
    private BigDecimal disabilityLevyPerDay;
}
