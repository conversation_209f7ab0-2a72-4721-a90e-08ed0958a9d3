package com.gok.pboot.pms.cost.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.cost.entity.domain.CostConfigLevelPrice;
import com.gok.pboot.pms.cost.entity.vo.CostConfigLevelPriceVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 人员级别单价配置 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Mapper
public interface CostConfigLevelPriceMapper extends BaseMapper<CostConfigLevelPrice> {

    /**
     * 按版本 ID 获取成本配置级别价目表
     *
     * @param versionId 版本 ID
     * @param page      页
     * @return {@link Page }<{@link CostConfigLevelPriceVO }>
     */
    Page<CostConfigLevelPriceVO> getLevelPriceListByVersionIdPage(@Param("versionId") Long versionId, @Param("page") Page<CostConfigLevelPrice> page);

    /**
     * 按版本 ID 获取成本配置级别价目表
     *
     * @param versionId 版本 ID
     * @return {@link List }<{@link CostConfigLevelPriceVO }>
     */
    List<CostConfigLevelPriceVO> getLevelPriceListByVersionIdPage(@Param("versionId") Long versionId);
}
