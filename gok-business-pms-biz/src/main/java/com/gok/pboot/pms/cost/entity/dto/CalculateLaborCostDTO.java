package com.gok.pboot.pms.cost.entity.dto;

import com.gok.pboot.pms.cost.entity.domain.CostManagePersonnelLevelDetail;
import com.gok.pboot.pms.cost.entity.vo.CostConfigLevelPriceVO;
import com.gok.pboot.pms.enumeration.CostSalaryRelateTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;


/**
 * 计算人工成本 DTO
 *
 * <AUTHOR>
 * @date 2025/04/18
 */
@Accessors(chain = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CalculateLaborCostDTO {

    /**
     * 唯一标识
     */
    private Long id;

    /**
     * 关联 ID
     */
    private Long relateId;

    /**
     * 关联类型枚举
     */
    private CostSalaryRelateTypeEnum relateTypeEnum;

    /**
     * 用户 ID
     */
    private Long userId;

    /**
     * 配置 ID
     */
    private Long configId;

    /**
     * 正常工时
     */
    private BigDecimal normalHours;

    /**
     * 工作日加班工时
     */
    private BigDecimal workOvertimeHours;

    /**
     * 休息日加班工时
     */
    private BigDecimal restOvertimeHours;

    /**
     * 节假日加班工时
     */
    private BigDecimal holidayOvertimeHours;

    private CostConfigLevelPriceVO costConfigLevelPrice;

    public static CalculateLaborCostDTO build(CostManagePersonnelLevelDetail item) {
        return new CalculateLaborCostDTO()
                .setId(item.getId())
                .setRelateId(item.getId())
                .setRelateTypeEnum(CostSalaryRelateTypeEnum.ARTIFICIAL_COST_ESTIMATION)
                .setConfigId(item.getLevelConfigId())
                .setNormalHours(BigDecimal.valueOf(item.getExpectedPersonDay() * 7L));
    }

    public void init() {
        if (normalHours == null) {
            normalHours = BigDecimal.ZERO;
        }
        if (workOvertimeHours == null) {
            workOvertimeHours = BigDecimal.ZERO;
        }
        if (restOvertimeHours == null) {
            restOvertimeHours = BigDecimal.ZERO;
        }
        if (holidayOvertimeHours == null) {
            holidayOvertimeHours = BigDecimal.ZERO;
        }
    }
}
