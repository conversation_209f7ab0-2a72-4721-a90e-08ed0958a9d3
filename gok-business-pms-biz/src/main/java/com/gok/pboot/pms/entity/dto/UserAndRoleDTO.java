package com.gok.pboot.pms.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * 根据用户id和角色id实体
 * @date 2023/5/16
 */
@Data
public class UserAndRoleDTO {

	/**
	 * 角色ID
	 */
	@ApiModelProperty(value = "角色id集合")
	private List<Long> role;

	/**
	 * 用户id集合
	 */
	@ApiModelProperty(value = "用户id集合")
	private Collection<Long> userIds;
}
