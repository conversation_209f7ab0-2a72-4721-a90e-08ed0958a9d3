package com.gok.pboot.pms.cost.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.cost.entity.domain.CostTaskCategoryManagement;
import com.gok.pboot.pms.cost.entity.dto.CostTaskCategoryManagementDTO;
import com.gok.pboot.pms.cost.entity.vo.CostTaskCategoryManagementVO;
import com.gok.pboot.pms.cost.entity.vo.CostTaskCategorySortVO;

import java.util.List;
import java.util.Map;

/**
 * 类别管理表 服务类
 *
 * <AUTHOR>
 * @date 2024/03/26
 */
public interface ICostTaskCategoryManagementService extends IService<CostTaskCategoryManagement> {

    /**
     * 获取工单类别列表
     *
     * @return {@link List}<{@link CostTaskCategoryManagementVO}>
     */
    List<CostTaskCategoryManagementVO> taskCategoryList();

    /**
     * 新增类别管理
     *
     * @param dto 类别管理DTO
     */
    void addTaskCategory(CostTaskCategoryManagementDTO dto);

    /**
     * 修改类别管理
     *
     * @param dto 类别管理DTO
     */
    void editTaskCategory(CostTaskCategoryManagementDTO dto);

    /**
     * 删除类别管理
     *
     * @param id 主键
     */
    void removeTaskCategory(Long id);

    /**
     * 获取成本任务类别Map
     *
     * @return {@link Map }<{@link Integer }, {@link CostTaskCategoryManagement }>
     */
    Map<Integer, CostTaskCategoryManagement> getCostTaskCategoryMap();

    /**
     * 更新工单类别排序
     *
     * @param taskCategoryIds 类别ID列表，按照新的排序顺序排列
     */
    void updateTaskCategorySort(List<Long> taskCategoryIds);

    /**
     * 获取工单类型字典值
     *
     * @return {@link List }<{@link CostTaskCategorySortVO }>
     */
    List<CostTaskCategorySortVO> getTaskCategoryDict();

}