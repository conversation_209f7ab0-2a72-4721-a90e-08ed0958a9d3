package com.gok.pboot.pms.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * - 绩效用户 -
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/11/16 16:15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SysUserJxVO {

    /**
     * 用户编号
     */
    private Long userId;
    /**
     * 姓名
     */
    private String name;
    /**
     * 部门编号
     */
    private Long deptId;
    /**
     * 部门全称
     */
    private String deptName;
    /**
     * 当前部门全称
     */
    private String currentDeptName;
    /**
     * 职位
     */
    private String jobName;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 直接上级ID
     */
    private Long managerId;
}
