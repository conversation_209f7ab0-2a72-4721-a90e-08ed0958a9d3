package com.gok.pboot.pms.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
 * 非工作日
 *
 * <AUTHOR>
 * @since 2022-08-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(Holiday.ALIAS)
public class Holiday  {

    public static final String ALIAS = "mhour_holiday";
    /**
    * 日期
    */
    private LocalDate dayDate;

    /**
     * 0-普通休息日 1-法定节假日
     */
    private Integer holidayType;


}
