package com.gok.pboot.pms.controller;

import com.gok.pboot.pms.service.IEhrService;
import com.gok.pboot.service.commons.base.ApiResult;
import com.gok.pboot.service.entity.position.vo.GradeNodeVo;
import com.gok.pboot.service.entity.position.vo.JobTreeNodeVo;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * ehr控制器
 *
 * <AUTHOR>
 * @date 2025/05/06
 */
@RestController
@RequestMapping("/ehr")
@AllArgsConstructor
public class EhrController {

    private final IEhrService IEhrService;

    /**
     * 职级树
     *
     * @return {@link ApiResult }<{@link List }<{@link GradeNodeVo }>>
     */
    @GetMapping("/positionGradeTree")
    ApiResult<List<GradeNodeVo>> positionGradeTree(){
        return IEhrService.positionGradeTree();
    }

    /**
     * 岗位树
     *
     * @return {@link ApiResult }<{@link List }<{@link JobTreeNodeVo }>>
     */
    @GetMapping("/jobTitlesTree")
    ApiResult<List<JobTreeNodeVo>> jobTitlesTree(){
        return IEhrService.jobTitlesTree();
    }

}
