package com.gok.pboot.pms.cost.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gok.pboot.pms.common.base.BeanEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <p>
 * 人员信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("cost_personnel_information")
public class CostPersonnelInformation extends BeanEntity<Long> {

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 流程ID
     */
    private Long requestId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 工号
     */
    private String workCode;

    /**
     * 人员属性(0: 国科人员,1: 第三方)
     */
    private Integer personnelAttribute;

    /**
     * 可用状态(0: 不可用,1: 可用)
     */
    private Integer availableStatus;

    /**
     * 所属部门ID
     */
    private Long deptId;

    /**
     * 所属部门名称
     */
    private String deptName;

    /**
     * 岗位ID
     */
    private Long jobId;

    /**
     * 岗位名称
     */
    private String jobName;

    /**
     * 职级ID
     */
    private Long positionId;

    /**
     * 职级名称
     */
    private String positionName;

    /**
     * 入场时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate entryTime;

    /**
     * 离场时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate leaveTime;

    /**
     * 状态(0: 在场,1: 已离场)
     */
    private Integer status;

    /**
     * 驻场时长(天)
     */
    private BigDecimal durationDays;

    /**
     * 驻场地点
     */
    private String domicile;

    /**
     * 报价方式(0: 人天,1: 人月,2: 固定费率)
     */
    private Integer quotationType;

    /**
     * 含税报价
     */
    private BigDecimal quotationIncludeTax;

    /**
     * 报价税率ID
     */
    private Integer quotedRateId;

    /**
     * 不含税报价
     */
    private BigDecimal quotationExcludeTax;

    /**
     * 固定费率
     */
    private BigDecimal flatRate;

    /**
     * 外采含税单价
     */
    private BigDecimal foreignPurchaseUnitPriceIncludeTax;

    /**
     * 外采税率ID
     */
    private Integer foreignPurchaseTaxRateId;

    /**
     * 外采不含税单价
     */
    private BigDecimal foreignPurchaseUnitPriceExcludeTax;

    /**
     * 人天单价
     */
    private BigDecimal humanDayPrice;

    /**
     * 人月单价
     */
    private BigDecimal humanMonthPrice;

    /**
     * 归属月份
     */
    private LocalDate belongMonth;

}