package com.gok.pboot.pms.enumeration;

/**
 * OA文件下载类型
 *
 * <AUTHOR>
 */
public enum OaFileDownLoadTypeEnum implements ValueEnum<Integer> {
    /**
     * 合同附件
     */
    HTFJ(0, "合同附件"),
    /**
     * 合同附件(已盖章)
     */
    HTFJYGZ(1, "合同附件(已盖章)"),
    /**
     * 变更合同附件(已盖章)
     */
    BGHTFJYGZ(2, "变更合同附件(已盖章)"),
    /**
     * 新合同附件(已盖章)
     */
    XHTFJYGZ(3, "新合同附件(已盖章)"),
    /**
     * 原合同附件(已盖章)
     */
    YHTFJYGZ(4, "原合同附件(已盖章)"),
    /**
     * 合同款项结算单(电子版)
     */
    HTKX_JSD_DZB(5, "合同款项结算单(电子版)"),
    /**
     * 合同款项结算单(扫描件)
     */
    HTKX_JSD_SMJ(6, "合同款项结算单(扫描件)"),
    /**
     * 验收报告附件
     */
   YSBGFJ(7, "验收报告附件"),
    /**
     * 发票附件
     */
   FPFJ(8, "发票附件"),
    /**
     * 项目设备签收单附件
     */
    XMSBQSDFJ(9, "项目设备签收单附件"),
    /**
     * 年度项目计划表
     */
    NDXMJHB(10, "年度项目计划表"),
    /**
     * 项目业务需求文档
     */
    XMYWXQWD(11, "项目业务需求文档");



    //值
    private Integer  value;
    //名称
    private String name;

    OaFileDownLoadTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
    /**
     * 获取值
     *
     * @return Integer
     */
    @Override
    public Integer getValue() {
        return value;
    }

    /**
     * 获取名称
     *
     * @return String
     */
    @Override
    public String getName() {
        return name;
    }

    public static String getNameByVal(Integer value) {
        for (OaFileDownLoadTypeEnum businessTypeEnum : OaFileDownLoadTypeEnum.values()) {
            if (businessTypeEnum.value.equals(value)) {
                return businessTypeEnum.name;
            }
        }
        return "";
    }
    public static Integer getValByName(String name) {
        for (OaFileDownLoadTypeEnum businessTypeEnum : OaFileDownLoadTypeEnum.values()) {
            if (businessTypeEnum.getName().equals(name)) {
                return businessTypeEnum.getValue();
            }
        }
        return null;
    }

    public static OaFileDownLoadTypeEnum getBusinessTypeEnum(Integer value) {
        for (OaFileDownLoadTypeEnum businessTypeEnum : OaFileDownLoadTypeEnum.values()) {
            if (businessTypeEnum.value.equals(value)) {
                return businessTypeEnum;
            }
        }
        return null;
    }
}
