package com.gok.pboot.pms.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.gok.base.admin.feign.RemoteOaService;
import com.gok.bcp.admin.entity.SysDictItem;
import com.gok.bcp.admin.feign.RemoteBcpDictService;
import com.gok.bcp.admin.vo.DictKvVo;
import com.gok.bcp.upms.feign.RemoteOutService;
import com.gok.bcp.upms.feign.RemoteRoleService;
import com.gok.bcp.upms.vo.SysDeptOutVO;
import com.gok.bcp.upms.vo.SysMenuVo;
import com.gok.components.common.user.PigxUser;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.Util.BcpLoggerUtils;
import com.gok.pboot.pms.Util.DbApiUtil;
import com.gok.pboot.pms.Util.PageUtils;
import com.gok.pboot.pms.Util.RSAUtils;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.common.base.PropertyFilters;
import com.gok.pboot.pms.common.base.R;
import com.gok.pboot.pms.common.constant.FunctionConstants;
import com.gok.pboot.pms.common.exception.ServiceException;
import com.gok.pboot.pms.entity.CustomerBusinessUnit;
import com.gok.pboot.pms.entity.domain.*;
import com.gok.pboot.pms.entity.dto.CreateRequestDTO;
import com.gok.pboot.pms.entity.dto.CustomerAttentionDTO;
import com.gok.pboot.pms.entity.dto.CustomerCommonPageDTO;
import com.gok.pboot.pms.entity.dto.CustomerPageDTO;
import com.gok.pboot.pms.entity.vo.*;
import com.gok.pboot.pms.enumeration.*;
import com.gok.pboot.pms.handler.ProjectScopeHandle;
import com.gok.pboot.pms.mapper.BusinessInfoMapper;
import com.gok.pboot.pms.mapper.CustomerInfoMapper;
import com.gok.pboot.pms.mapper.OaHrmcityMapper;
import com.gok.pboot.pms.mapper.ProjectInfoMapper;
import com.gok.pboot.pms.oa.OaUtil;
import com.gok.pboot.pms.oa.client.OaClient;
import com.gok.pboot.pms.oa.dto.OaCreateRequestDTO;
import com.gok.pboot.pms.oa.dto.OaMainParamDTO;
import com.gok.pboot.pms.service.*;
import com.google.common.collect.ImmutableList;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class CustomerInfoServiceImpl extends ServiceImpl<CustomerInfoMapper, CustomerInfo>
        implements ICustomerInfoService {

    private final RemoteOaService remoteOaService;

    private final RemoteOutService remoteOutService;

    private final RemoteBcpDictService remoteBcpDictService;

    private final RemoteRoleService remoteRoleService;

    private final OaUtil oaUtil;

    private final DbApiUtil dbApiUtil;

    private final BcpLoggerUtils bcpLoggerUtils;

    private final OaClient oaClient;
    private final OaHrmcityMapper oaHrmcityMapper;

    private final ICustomerAttentionService customerAttentionService;

    private final BusinessInfoMapper businessInfoMapper;

    private final ProjectInfoMapper projectInfoMapper;

    private final IPmsDocImageFileService pmsDocImageFileService;

    private final ICustomerBusinessPersonService customerBusinessPersonService;

    private final ICustomerBusinessUnitService customerBusinessUnitService;

    private final ProjectScopeHandle projectScopeHandle;


    @Autowired
    @Lazy
    private  ICustomerBusinessService customerBusinessService;
    public static final String PARAM_FILTER_CUSTOMER_ID = "customerId";
    public static final String PARAM_FILTER_PERMISSION = "permission";
    public static final String PARAM_FILTER_APPLICATION = "application";
    public static final String PARAM_FILTER_MENU_TYPE = "menuType";

    public static final List<String> DATA_SCOPE_PERMISSION_LIST =
            ImmutableList.of(
                    "CUSTOMER_BUSINESS_DATA",
                    "CUSTOMER_TB:BUSINESS_DATA",
                    "CUSTOMER_TB:BASE"
            );

    @Value("${oa.workflowUrl}")
    private String workflowUrl;
    /**
     * oa的url地址
     */
    @Value("${oa.url.httpUrl}")
    private String url;

    /**
     * oa的资源appId
     */
    @Value("${oa.resourcesAppId}")
    private String appId;

    /**
     * oa的spk
     */
    @Value("${oa.spk}")
    private String spk;
    @Override
    public Page<CustomerListVO> findPage(CustomerPageDTO filter) {
        filter.setUserId(SecurityUtils.getUser().getId());
        List<CustomerListVO> customerListVOList;
        if(filter.getIsAttention()){
            customerListVOList = baseMapper.findAttentionList(filter);
        }else{
            customerListVOList = baseMapper.findList(filter);
        }
        PageRequest pageRequest = new PageRequest(filter.getPageNumber(),filter.getPageSize());
        if (CollUtil.isEmpty(customerListVOList)) {
            return PageUtils.page(new ArrayList<>(),pageRequest);
        }
        List<CustomerListVO> customerListAuthorityList=getAuthorityList(customerListVOList);

        List<CustomerListVO>  customerListVOPageList=CollUtil.page(filter.getPageNumber()-1,filter.getPageSize(),
                customerListAuthorityList);

        // 对于枚举字段进行重新赋值
        if(CollUtil.isNotEmpty(customerListVOPageList)){
            Set<String> dictNameSet = new HashSet<>();
            dictNameSet.add("最终客户分级");
            Map<String, List<DictKvVo>> dictMap = remoteBcpDictService.getDictKvBatchList(dictNameSet).getData();
            //最终客户分级
            List<DictKvVo> khfjList = dictMap.get("最终客户分级");
            Map<String, String> khfjMap = khfjList.stream()
                    .collect(Collectors.toMap(DictKvVo::getValue, DictKvVo::getName, (a, b) -> a));
           //行业一二级
            Map<String, SysDictItem> khxyyjChildMap=new HashMap<>();
            Map<Long, SysDictItem> khxyyjParentMap=new HashMap<>();
            getHyfjMap(khxyyjChildMap,khxyyjParentMap);

            List<com.gok.bcp.upms.vo.SysDeptOutVO> deptList = remoteOutService.getOrgStructList().getData();
            Map<Long, com.gok.bcp.upms.vo.SysDeptOutVO> deptMap = CollUtil.isNotEmpty(deptList)
                    ? deptList.stream().collect(Collectors.toMap(SysDeptOutVO::getDeptId, d -> d))
                    : new HashMap<>();

            QueryWrapper<BusinessInfo> businessInfoQueryWrapper = new QueryWrapper<>();
            businessInfoQueryWrapper.lambda()
                    .isNotNull(BusinessInfo::getEndCustomerName)
                    .isNotNull(BusinessInfo::getContractCustomerName)
                    .eq(BusinessInfo::getBusinessStatus,0)
                    .ne(BusinessInfo::getIsNotInternalProject,1)
                    .isNotNull(BusinessInfo::getItemNo);
            Map<String, List<BusinessInfo>> businessInfoCustomerNameMap1 = businessInfoMapper.selectList(businessInfoQueryWrapper)
                    .stream().collect(Collectors.groupingBy(BusinessInfo::getEndCustomerName));

            Map<String, List<BusinessInfo>> businessInfoCustomerNameMap2 = businessInfoMapper.selectList(businessInfoQueryWrapper)
                    .stream().collect(Collectors.groupingBy(BusinessInfo::getContractCustomerName));

            QueryWrapper<ProjectInfo> projectInfoQueryWrapper = new QueryWrapper<>();
            projectInfoQueryWrapper.lambda()
                    .isNotNull(ProjectInfo::getCustomerName)
                    .isNotNull(ProjectInfo::getEndCustomer)
                    .isNotNull(ProjectInfo::getContractCustomer)
                    .in(ProjectInfo::getProjectStatus, ProjectStatusEnum.ZJ.getValue(), ProjectStatusEnum.JX.getValue())
                    .eq(ProjectInfo::getIsNotInternalProject, IsNoInternalProjectEnum.no.getValue())
                    .isNotNull(ProjectInfo::getItemNo)
                    .isNotNull(ProjectInfo::getItemName);
            List<ProjectInfo> projectInfoList = projectInfoMapper.selectList(projectInfoQueryWrapper);

            // 使用枚举替代硬编码状态值，提高代码可读性和维护性
            Map<String, List<ProjectInfo>> zjMap1 = groupProjectsByStatus(projectInfoList, ProjectStatusEnum.ZJ, ProjectInfo::getCustomerName);
            Map<String, List<ProjectInfo>> zjMap2 = groupProjectsByStatus(projectInfoList, ProjectStatusEnum.ZJ, ProjectInfo::getEndCustomer);
            Map<String, List<ProjectInfo>> zjMap3 = groupProjectsByStatus(projectInfoList, ProjectStatusEnum.ZJ, ProjectInfo::getContractCustomer);

            Map<String, List<ProjectInfo>> jxMap1 = groupProjectsByStatus(projectInfoList, ProjectStatusEnum.JX, ProjectInfo::getCustomerName);
            Map<String, List<ProjectInfo>> jxMap2 = groupProjectsByStatus(projectInfoList, ProjectStatusEnum.JX, ProjectInfo::getEndCustomer);
            Map<String, List<ProjectInfo>> jxMap3 = groupProjectsByStatus(projectInfoList, ProjectStatusEnum.JX, ProjectInfo::getContractCustomer);

            for (CustomerListVO vo: customerListVOPageList) {
                if(vo.getKhfj()!=null){
                    vo.setKhfjTxt(khfjMap.get(String.valueOf(vo.getKhfj())));
                }
                if(vo.getKhxyyj()!=null){
                    SysDictItem sysDictItem = khxyyjChildMap.get(String.valueOf(vo.getKhxyyj()));
                    if(Optional.ofNullable(sysDictItem).isPresent()){
                        SysDictItem sysDictItemParent = khxyyjParentMap.get(sysDictItem.getParentId());
                        vo.setKhxyyjTxt(sysDictItemParent!=null
                                ?sysDictItemParent.getItemName()+"/"+sysDictItem.getItemName():sysDictItem.getItemName());
                    }
                }
                if(!filter.getIsAttention()){
                    QueryWrapper<CustomerAttention> queryWrapper = new QueryWrapper<>();
                    queryWrapper.lambda().eq(CustomerAttention::getUserid,filter.getUserId());
                    List<Long> customerIds = customerAttentionService.list(queryWrapper).stream()
                            .map(CustomerAttention::getCustomerId).collect(Collectors.toList());
                    if(CollUtil.isNotEmpty(customerIds)&&customerIds.contains(vo.getId())){
                        vo.setIsAttention(true);
                    }
                }else{
                    vo.setIsAttention(true);
                }
                if(vo.getGsywbmejbm()!=null){
                    SysDeptOutVO sysDeptOutVO = deptMap.get(vo.getGsywbmejbm());
                    if(Optional.ofNullable(sysDeptOutVO).isPresent()){
                        vo.setGsywbmejbmTxt(sysDeptOutVO.getName());
                        if(Optional.ofNullable(sysDeptOutVO.getParentId()).isPresent()){
                            SysDeptOutVO sysDeptOutVOParent = deptMap.get(sysDeptOutVO.getParentId());
                            if(Optional.ofNullable(sysDeptOutVOParent).isPresent()){
                                vo.setGsywbmejbmTxt(sysDeptOutVOParent.getName()+"/"+sysDeptOutVO.getName());
                            }
                        }
                    }

                }
                //跟进商机
                List<BusinessInfo> businessInfos1 = CollUtil.isNotEmpty(businessInfoCustomerNameMap1.get(vo.getKhmc()))?
                        businessInfoCustomerNameMap1.get(vo.getKhmc()):new ArrayList<>();
                List<BusinessInfo> businessInfos2 =  CollUtil.isNotEmpty(businessInfoCustomerNameMap2.get(vo.getKhmc()))?
                        businessInfoCustomerNameMap2.get(vo.getKhmc()):new ArrayList<>();
                businessInfos1.addAll(businessInfos2);
                businessInfos1=businessInfos1.stream()
                        .collect(Collectors.collectingAndThen(
                                Collectors.toMap(
                                        BusinessInfo::getId, // key: id
                                        buttonAuthority -> buttonAuthority, // value: ButtonAuthority
                                        (existing, replacement) -> existing // 处理重复项，保留第一个
                                ),
                                map -> new ArrayList<>(map.values())
                        ));
                vo.setGjsj(businessInfos1.size());
                //在建商机
                List<ProjectInfo> zjProjectInfos1 = CollUtil.isNotEmpty(zjMap1.get(vo.getKhmc()))?
                        zjMap1.get(vo.getKhmc()):new ArrayList<>();
                List<ProjectInfo> zjProjectInfos2 = CollUtil.isNotEmpty(zjMap2.get(vo.getKhmc()))?
                        zjMap2.get(vo.getKhmc()):new ArrayList<>();
                List<ProjectInfo> zjProjectInfos3 =CollUtil.isNotEmpty(zjMap3.get(vo.getKhmc()))?
                        zjMap3.get(vo.getKhmc()):new ArrayList<>();
                zjProjectInfos1.addAll(zjProjectInfos2);
                zjProjectInfos1.addAll(zjProjectInfos3);
                zjProjectInfos1=zjProjectInfos1.stream()
                        .collect(Collectors.collectingAndThen(
                                Collectors.toMap(
                                        ProjectInfo::getId, // key: id
                                        buttonAuthority -> buttonAuthority, // value: ButtonAuthority
                                        (existing, replacement) -> existing // 处理重复项，保留第一个
                                ),
                                map -> new ArrayList<>(map.values())
                        ));
                vo.setZjsj(zjProjectInfos1.size());
                //结项商机
                List<ProjectInfo> jxProjectInfos1 =  CollUtil.isNotEmpty(jxMap1.get(vo.getKhmc()))?
                        jxMap1.get(vo.getKhmc()):new ArrayList<>();
                List<ProjectInfo> jxProjectInfos2 =   CollUtil.isNotEmpty(jxMap2.get(vo.getKhmc()))?
                        jxMap2.get(vo.getKhmc()):new ArrayList<>();
                List<ProjectInfo> jxProjectInfos3 =   CollUtil.isNotEmpty(jxMap3.get(vo.getKhmc()))?
                        jxMap3.get(vo.getKhmc()):new ArrayList<>();
                jxProjectInfos1.addAll(jxProjectInfos2);
                jxProjectInfos1.addAll(jxProjectInfos3);
                jxProjectInfos1=jxProjectInfos1.stream()
                        .collect(Collectors.collectingAndThen(
                                Collectors.toMap(
                                        ProjectInfo::getId, // key: id
                                        buttonAuthority -> buttonAuthority, // value: ButtonAuthority
                                        (existing, replacement) -> existing // 处理重复项，保留第一个
                                ),
                                map -> new ArrayList<>(map.values())
                        ));
                vo.setJxsj(jxProjectInfos1.size());

            }
        }
        Page<CustomerListVO> page = PageUtils.page(new ArrayList<>(), pageRequest);
        page.setRecords(customerListVOPageList);
        page.setSize(filter.getPageSize());
        page.setCurrent(filter.getPageNumber());
        page.setTotal(customerListAuthorityList.size());
        return page;
    }

    @Override
    public  CustomerNumVO findCustomerNum(){
        CustomerPageDTO filter = new CustomerPageDTO();
        filter.setUserId(SecurityUtils.getUser().getId());
        List<CustomerListVO> customerListVOAttentionList=getAuthorityList(baseMapper.findAttentionList(filter));
        List<CustomerListVO> customerListVOList=getAuthorityList(baseMapper.findList(filter));
        CustomerNumVO customerNumVO = new CustomerNumVO();
        customerNumVO.setAttentionCustomerNum(customerListVOAttentionList.size());
        customerNumVO.setAllCustomerNum(customerListVOList.size());
        return customerNumVO;
    }
    /**
     *  数据去重
     * @param customerListVOList  原始数据
     * @return
     */
    private  List<CustomerListVO>  dataDeduplication(List<CustomerListVO>  customerListVOList ){
       return customerListVOList.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(
                                CustomerListVO::getId, // key: id
                                buttonAuthority -> buttonAuthority, // value: ButtonAuthority
                                (existing, replacement) -> existing // 处理重复项，保留第一个
                        ),
                        map -> new ArrayList<>(map.values())
                )).stream()
                .sorted(Comparator.comparing(CustomerListVO::getCtime, Comparator.nullsLast(String::compareTo))
                        .reversed()).collect(Collectors.toList());
    }

    /**
     *  更具权限过滤数据
     * @param customerListVOList  原始数据
     * @return
     */
    private  List<CustomerListVO>  getAuthorityList(List<CustomerListVO>  customerListVOList ){
        List<CustomerListVO> customerListAuthorityList=new ArrayList<>();
        SysUserDataScopeVO dataScope = projectScopeHandle.findDataScope();
        if(!dataScope.getIsAll()){
            List<Long> authorityUserIds=dataScope.getUserIdList();
            authorityUserIds.add(SecurityUtils.getUser().getId());
            List<CustomerListVO> finalCustomerListAuthorityList = customerListAuthorityList;
            customerListVOList.forEach(c->{
                List<Long> authorityIds = new ArrayList<>();
                if(StrUtil.isNotBlank(c.getManagerIds())){
                    authorityIds.addAll(Arrays.asList(c.getManagerIds().split(",")).stream()
                            .map(Long::valueOf)
                            .collect(Collectors.toList()));
                }
                if(StrUtil.isNotBlank(c.getKhjl())){
                    authorityIds.addAll(Arrays.asList(c.getKhjl().split(",")).stream()
                            .map(Long::valueOf)
                            .collect(Collectors.toList()));
                }
                authorityIds=authorityIds.stream().distinct().collect(Collectors.toList());
                if(CollUtil.isNotEmpty(authorityUserIds)&&CollUtil.isNotEmpty(authorityIds)){
                    List<Long> temp = new ArrayList<>(authorityUserIds);
                    temp.retainAll(authorityIds);
                    if(!temp.isEmpty()){
                        finalCustomerListAuthorityList.add(c);
                    }}

            });
        }else{
           customerListAuthorityList=customerListVOList;
        }
        customerListAuthorityList= dataDeduplication(customerListAuthorityList);
      return customerListAuthorityList;
    }

    @Override
    public   List<OaHrmcityVO> getCityListByName(String name){
        return baseMapper.getCityListByName(name);
    }

    @Override
    public   CustomerHeadVO getHeadById(Long id,HttpServletRequest request){
        CustomerHeadVO customerHeadVO = new CustomerHeadVO();
        CustomerInfoVO customerInfoVO = this.findById(id);
        BeanUtil.copyProperties(customerInfoVO,customerHeadVO);
        return customerHeadVO;
    }
    @Override
    public  List<SysMenuVo> getMenuAuthority(HttpServletRequest request){
        Map<String, Object> filter = PropertyFilters.get(request, true);
        Object customerIdObj = filter.get(PARAM_FILTER_CUSTOMER_ID);
        Object permissionObj = filter.get(PARAM_FILTER_PERMISSION);
        Object menuType = filter.get(PARAM_FILTER_MENU_TYPE);
        Long application = Long.valueOf(request.getHeader(PARAM_FILTER_APPLICATION));
        // 获取普通角色菜单权限
        List<SysMenuVo> sysMenuVos = new ArrayList<>();
        com.gok.components.common.util.R<List<SysMenuVo>> funcAuthListByIdAndAppId =
                remoteOutService.getFuncAuthListByIdAndAppId(SecurityUtils.getUser().getId(), application);
        if (Optional.ofNullable(funcAuthListByIdAndAppId).isPresent() && Optional.ofNullable(permissionObj).isPresent()) {
            String permission = String.valueOf(permissionObj);
            List<SysMenuVo> allSysMenuVos = funcAuthListByIdAndAppId.getData();
            if (CollUtil.isNotEmpty(allSysMenuVos)) {
                Optional<SysMenuVo> any = allSysMenuVos.stream().filter(s -> permission.equals(s.getPermission())).findAny();
                if (any.isPresent()) {
                    SysMenuVo sysMenuVo = any.get();
                    sysMenuVos = allSysMenuVos.stream().filter(s -> sysMenuVo.getMenuId().equals(s.getParentId())).collect(Collectors.toList());
                }
            }
        }
        // 获取业务角色菜单权限
        List<SysMenuVo> buttonAuthoritiesAll = new ArrayList<>();
        Long customerId = Long.valueOf(String.valueOf(customerIdObj));
        String permission = String.valueOf(permissionObj);

        // 获取客户业务角色菜单权限
        CustomerInfo customerInfo = baseMapper.selectById(customerId);
        SysUserDataScopeVO dataScope = projectScopeHandle.findDataScope();
        List<Long> userIds = dataScope.getUserIdList();
        if(customerInfo.getKhjl()!=null&&userIds.contains(customerInfo.getKhjl())){
            List<String> businessAuthorities = new ArrayList<>();
            businessAuthorities.add(BusinessRoleCodeEnum.PROJECT_SALES_MANAGER.getName());
            if (Optional.ofNullable(menuType).isPresent() && CollUtil.isNotEmpty(businessAuthorities)) {
                buttonAuthoritiesAll = remoteRoleService.getMenuAuthListByRoleListAndClientId(application,
                        businessAuthorities.stream().distinct().collect(Collectors.toList()),
                        menuType.toString(), permission).getData();
            } else if (CollUtil.isNotEmpty(businessAuthorities)) {
                buttonAuthoritiesAll = remoteRoleService.getMenuAuthListByRoleListAndClientId(application,
                        businessAuthorities.stream().distinct().collect(Collectors.toList()), null, permission).getData();
            }
            if (CollUtil.isNotEmpty(buttonAuthoritiesAll)) {
                buttonAuthoritiesAll = buttonAuthoritiesAll.stream()
                        .filter(b -> DATA_SCOPE_PERMISSION_LIST.contains(b.getPermission())).collect(Collectors.toList());
            }
        }

        // 获取客户单元角色菜单权限
        List<SysMenuVo> buttonAuthorities = new ArrayList<>();
        List<CustomerBusinessUnit> customerBusinessUnitList =
                customerBusinessUnitService.list(new QueryWrapper<CustomerBusinessUnit>()
                        .lambda().eq(CustomerBusinessUnit::getId, customerId));
        if(CollUtil.isNotEmpty(customerBusinessUnitList)){
            customerBusinessUnitList.stream().forEach(customerBusinessUnit -> {
                buttonAuthorities.addAll(customerBusinessService.getBusinessAuthorities(customerBusinessUnit.getBusinessId(),
                        permission,menuType,application));
            });
            buttonAuthoritiesAll.addAll(buttonAuthorities);
            buttonAuthoritiesAll.stream()
                    .collect(Collectors.collectingAndThen(
                            Collectors.toMap(
                                    SysMenuVo::getMenuId, // key: id
                                    buttonAuthority -> buttonAuthority, // value: ButtonAuthority
                                    (existing, replacement) -> existing // 处理重复项，保留第一个
                            ),
                            map -> new ArrayList<>(map.values())
                    ));
        }
        buttonAuthoritiesAll.addAll(sysMenuVos);
        return buttonAuthoritiesAll.stream().collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(
                                Comparator.comparing(SysMenuVo::getPermission))), ArrayList::new))
                .stream().sorted(Comparator.comparing(SysMenuVo::getSortOrder)).collect(Collectors.toList());
    }




    @Override
    public  Page<CustomerCommonVO> findCommonPage(CustomerCommonPageDTO filter){
        Page<CustomerCommonVO> page = baseMapper.findCommonPage(
                Page.of(filter.getPageNumber(), filter.getPageSize()),
                filter
        );
        // 对于枚举字段进行重新赋值
        List<CustomerCommonVO> records = page.getRecords();
        if(CollUtil.isNotEmpty(records)){
            Set<String> dictNameSet = new HashSet<>();
            dictNameSet.add("最终客户分级");
            Map<String, List<DictKvVo>> dictMap = remoteBcpDictService.getDictKvBatchList(dictNameSet).getData();
            //最终客户分级
            List<DictKvVo> khfjList = dictMap.get("最终客户分级");
            Map<String, String> khfjMap = khfjList.stream()
                    .collect(Collectors.toMap(DictKvVo::getValue, DictKvVo::getName, (a, b) -> a));
            //行业一二级
            Map<String, SysDictItem> khxyyjChildMap=new HashMap<>();
            Map<Long, SysDictItem> khxyyjParentMap=new HashMap<>();
            getHyfjMap(khxyyjChildMap,khxyyjParentMap);

            List<com.gok.bcp.upms.vo.SysDeptOutVO> deptList = remoteOutService.getOrgStructList().getData();
            Map<Long, com.gok.bcp.upms.vo.SysDeptOutVO> deptMap = CollUtil.isNotEmpty(deptList)
                    ? deptList.stream().collect(Collectors.toMap(SysDeptOutVO::getDeptId, d -> d))
                    : new HashMap<>();


            for (CustomerCommonVO vo: records) {
                if(vo.getKhfj()!=null){
                    vo.setKhfjTxt(khfjMap.get(String.valueOf(vo.getKhfj())));
                }
                if(vo.getKhxyyj()!=null){
                    SysDictItem sysDictItem = khxyyjChildMap.get(String.valueOf(vo.getKhxyyj()));
                    if(Optional.ofNullable(sysDictItem).isPresent()){
                        SysDictItem sysDictItemParent = khxyyjParentMap.get(sysDictItem.getParentId());
                        vo.setKhxyyjTxt(sysDictItemParent!=null
                                ?sysDictItemParent.getItemName()+"/"+sysDictItem.getItemName():sysDictItem.getItemName());
                    }
                }

                if(vo.getGsywbmejbm()!=null){
                    SysDeptOutVO sysDeptOutVO = deptMap.get(vo.getGsywbmejbm());
                    if(Optional.ofNullable(sysDeptOutVO).isPresent()){
                        vo.setGsywbmejbmTxt(sysDeptOutVO.getName());
                        if(Optional.ofNullable(sysDeptOutVO.getParentId()).isPresent()){
                            SysDeptOutVO sysDeptOutVOParent = deptMap.get(sysDeptOutVO.getParentId());
                            if(Optional.ofNullable(sysDeptOutVOParent).isPresent()){
                                vo.setGsywbmejbmTxt(sysDeptOutVOParent.getName()+"/"+sysDeptOutVO.getName());
                            }
                        }
                    }

                }

            }

        }

        return page;
    }

    @Override
    public  List<CustomerCommonVO> findByIds(List<Long> ids){
        List<CustomerInfo> customerInfoList = baseMapper.selectBatchIds(ids);
        List<CustomerCommonVO> customerCommonVOList = BeanUtil.copyToList(customerInfoList, CustomerCommonVO.class);
        if(CollUtil.isNotEmpty(customerCommonVOList)) {
            Set<String> dictNameSet = new HashSet<>();
            dictNameSet.add("最终客户分级");
            Map<String, List<DictKvVo>> dictMap = remoteBcpDictService.getDictKvBatchList(dictNameSet).getData();
            //最终客户分级
            List<DictKvVo> khfjList = dictMap.get("最终客户分级");
            Map<String, String> khfjMap = khfjList.stream()
                    .collect(Collectors.toMap(DictKvVo::getValue, DictKvVo::getName, (a, b) -> a));

            //行业一二级
            Map<String, SysDictItem> khxyyjChildMap=new HashMap<>();
            Map<Long, SysDictItem> khxyyjParentMap=new HashMap<>();
            getHyfjMap(khxyyjChildMap,khxyyjParentMap);

            List<com.gok.bcp.upms.vo.SysDeptOutVO> deptList = remoteOutService.getOrgStructList().getData();
            Map<Long, com.gok.bcp.upms.vo.SysDeptOutVO> deptMap = CollUtil.isNotEmpty(deptList)
                        ? deptList.stream().collect(Collectors.toMap(SysDeptOutVO::getDeptId, d -> d))
                    : new HashMap<>();
            for (CustomerCommonVO vo : customerCommonVOList) {
                if (vo.getKhfj() != null) {
                    vo.setKhfjTxt(khfjMap.get(String.valueOf(vo.getKhfj())));
                }
                if(vo.getKhxyyj()!=null){
                    SysDictItem sysDictItem = khxyyjChildMap.get(String.valueOf(vo.getKhxyyj()));
                    if(Optional.ofNullable(sysDictItem).isPresent()){
                        SysDictItem sysDictItemParent = khxyyjParentMap.get(sysDictItem.getParentId());
                        vo.setKhxyyjTxt(sysDictItemParent!=null
                                ?sysDictItemParent.getItemName()+"/"+sysDictItem.getItemName():sysDictItem.getItemName());
                    }
                }

                if (vo.getGsywbmejbm() != null) {
                    SysDeptOutVO sysDeptOutVO = deptMap.get(vo.getGsywbmejbm());
                    if (Optional.ofNullable(sysDeptOutVO).isPresent()) {
                        vo.setGsywbmejbmTxt(sysDeptOutVO.getName());
                        if (Optional.ofNullable(sysDeptOutVO.getParentId()).isPresent()) {
                            SysDeptOutVO sysDeptOutVOParent = deptMap.get(sysDeptOutVO.getParentId());
                            if (Optional.ofNullable(sysDeptOutVOParent).isPresent()) {
                                vo.setGsywbmejbmTxt(sysDeptOutVOParent.getName() + "/" + sysDeptOutVO.getName());
                            }
                        }
                    }

                }

            }
        }
        return customerCommonVOList;
    }


    @Override
    public  R<String> doCreateRequest(CreateRequestDTO dto){
        //获得当前用户免登录token
        String redirectOaToken;
        Long currentUserId;
        try {
            PigxUser user = SecurityUtils.getUser();
            currentUserId=user.getId();
            redirectOaToken = remoteOaService.getOaSsoTokenByUserId(currentUserId).getData();
            log.info("获得当前用户免登录token:" + redirectOaToken);
        } catch (Exception e) {
            log.error("获取跳转token失败:" + e.getMessage());
            return R.failed("获取跳转token失败" + e.getMessage());
        }
        if(StrUtil.isBlank(redirectOaToken)){
            log.error("获取跳转token失败:token为空" );
            return R.failed("获取跳转token失败:token为空");
        }
        OAFormTypeEnum oaFormTypeEnum = OAFormTypeEnum.getBusinessTypeEnum(dto.getFormType());
        if (oaFormTypeEnum==null) {
            throw new ServiceException("发起流程失败，原因:未找到对应表单~");
        }
        String formTypeName =oaFormTypeEnum.getName();
                OaAccountVO oaAccountVO = dbApiUtil.getOaAccountInfoByUserId(currentUserId);
        log.info("OA用户:"+oaAccountVO);
        if(!Optional.ofNullable(oaAccountVO).isPresent()){
            return R.failed("发起"+formTypeName+"流程失败，原因:未查询到当前用户" );
        }else{
            if(!Optional.ofNullable(oaAccountVO.getOaId()).isPresent()){
                return R.failed("发起"+formTypeName+"流程失败，原因:oaId为空" );
            }
        }
        //客户台账信息
        CustomerInfo customerInfo = baseMapper.selectById(dto.getCustomerId());
        Integer requestId;
        if (Optional.ofNullable(customerInfo).isPresent()||dto.getFormType().equals(OAFormTypeEnum.KHBA.getValue())) {
            try {
                //发起录用流程
                OaCreateRequestDTO oaCreateRequestDTO = new OaCreateRequestDTO();
                //请求参数
                List<OaMainParamDTO> mainDataList = new ArrayList<>();

                //流程id
                OAFormTypeEnum businessTypeEnum = OAFormTypeEnum.getBusinessTypeEnum(dto.getFormType());
                oaCreateRequestDTO.setWorkflowId(dbApiUtil.getWorkflowIdByName(businessTypeEnum.getName()));


                OaMainParamDTO oaMainParamDTO1 =
                        OaMainParamDTO.builder().fieldName("ApplicantID")
                                .fieldValue(String.valueOf(oaAccountVO.getOaId())).build();
                mainDataList.add(oaMainParamDTO1);

                OaMainParamDTO oaMainParamDTO2 =
                        OaMainParamDTO.builder().fieldName("bh")
                                .fieldValue(String.valueOf(oaAccountVO.getWorkcode())).build();
                mainDataList.add(oaMainParamDTO2);

                OaMainParamDTO oaMainParamDTO3 =
                        OaMainParamDTO.builder().fieldName("ApplicantDeptID")
                                .fieldValue(oaAccountVO.getDepartmentid()).build();
                mainDataList.add(oaMainParamDTO3);

                OaMainParamDTO oaMainParamDTO4 =
                        OaMainParamDTO.builder().fieldName("ApplicantTel")
                                .fieldValue(oaAccountVO.getMobile()).build();
                mainDataList.add(oaMainParamDTO4);

                //岗位
                OaMainParamDTO oaMainParamDTO5 =
                        OaMainParamDTO.builder().fieldName("ApplicantJobID")
                                .fieldValue(oaAccountVO.getJobtitle()).build();
                mainDataList.add(oaMainParamDTO5);

                String  oaTitle=StrUtil.format(formTypeName+"-{}-{}"
                        , SecurityUtils.getUser().getName()
                        , LocalDate.now()
                );
                if(dto.getFormType().equals(OAFormTypeEnum.KHGTJL.getValue())){
                    //客户名称
                    OaMainParamDTO oaMainParamDTO13 =
                            OaMainParamDTO.builder().fieldName("khmc")
                                    .fieldValue(customerInfo.getId().toString()).build();
                    mainDataList.add(oaMainParamDTO13);

                } /*else  if( dto.getFormType().equals(OAFormTypeEnum.KHYJ.getValue())){
                    //客户名称
                    OaMainParamDTO oaMainParamDTO13 =
                            OaMainParamDTO.builder().fieldName("khmc")
                                    .fieldValue(customerInfo.getId().toString()).build();
                    mainDataList.add(oaMainParamDTO13);

                } */else  if(dto.getFormType().equals(OAFormTypeEnum.SJBB.getValue())){
                    //签约客户
                    OaMainParamDTO oaMainParamDTO13 =
                            OaMainParamDTO.builder().fieldName("qykh")
                                    .fieldValue(customerInfo.getId().toString()).build();
                    mainDataList.add(oaMainParamDTO13);
                    //最终客户
                    OaMainParamDTO oaMainParamDTO14 =
                            OaMainParamDTO.builder().fieldName("zzkh")
                                    .fieldValue(customerInfo.getId().toString()).build();
                    mainDataList.add(oaMainParamDTO14);
                }
                oaCreateRequestDTO.setRequestName(oaTitle);
                oaCreateRequestDTO.setMainData(mainDataList);

                Map<String, Object> otherParamsMap = new HashMap<>();
                //是否主动提交流程
                otherParamsMap.put("isnextflow", "0");
                oaCreateRequestDTO.setOtherParams(otherParamsMap);
                String OtherParamsStr = JSONUtil.toJsonStr(otherParamsMap);
                String mianParamStr = new ObjectMapper().writeValueAsString(mainDataList);
                // 获取token
                String token = oaUtil.getToken();
                if (oaAccountVO.getOaId() == null) {
                    throw new ServiceException("必须传入userID");
                }
                // 对userID进行加密
                String encryptUserId = RSAUtils.encrypt(String.valueOf(oaAccountVO.getOaId()), spk);

                Map<String, Object> map = oaClient.doCreateRequest(token,appId,encryptUserId,url,oaCreateRequestDTO.getWorkflowId().toString(),
                        oaCreateRequestDTO.getRequestName(),
                        mianParamStr,
                        OtherParamsStr,
                        new ObjectMapper().writeValueAsString(new ArrayList<>()));
                String code = (String) map.get("code");
                if (!"SUCCESS".equals(code)) {
                    log.error("发起OA流程失败1，原因:" + map);
                    return R.failed("发起OA流程失败1，原因:" + map);
                }
                ObjectMapper objectMapper = new ObjectMapper();
                Map<String, Object> dataMap = objectMapper.readValue(map.get("data").toString(), Map.class);
                //流程id
                requestId = (Integer)dataMap.get("requestid");
                switch (oaFormTypeEnum ) {
                    case SJBB:
                        //日志
                        bcpLoggerUtils.log(FunctionConstants.CUSTOMER_LEDGER, LogContentEnum.OPEN_THE_BUSINESS_OPPORTUNITY_REPORT,
                                SecurityUtils.getUser().getName(), customerInfo.getKhmc());
                        break;
                    case KHBA:
                        //日志
                        bcpLoggerUtils.log(FunctionConstants.CUSTOMER_LEDGER, LogContentEnum.OPEN_CUSTOMER_REPORT,
                                SecurityUtils.getUser().getName());
                        break;
                    case KHYJ:
                        //日志
                        bcpLoggerUtils.log(FunctionConstants.CUSTOMER_LEDGER, LogContentEnum.OPEN_CUSTOMER_HANDOVER,
                                SecurityUtils.getUser().getName(), customerInfo.getKhmc());
                        break;
                    case KHGTJL:
                        //日志
                        bcpLoggerUtils.log(FunctionConstants.CUSTOMER_LEDGER, LogContentEnum.OPEN_CUSTOMER_COMMUNICATION_RECORDS,
                                SecurityUtils.getUser().getName(), customerInfo.getKhmc());
                        break;
                    default:
                        log.info("未知的OA流程类型");
                }


            } catch (Exception e) {
                log.error("发起OA流程失败2:" +e);
                return R.failed("发起OA流程失败2:" + e.getMessage());
            }
        } else {
            log.error("根据id:" + dto.getProjectId() + ",没有找到对应数据");
            return R.failed("根据id:" + dto.getProjectId()  + ",没有找到对应数据");
        }
        return R.ok(StrUtil.format(workflowUrl, redirectOaToken, requestId), "请求成功");
    }


    @Override
    public CustomerInfoVO findById(Long id) {
        CustomerInfoVO customerInfoVO = new CustomerInfoVO();
        CustomerInfo customerInfo = baseMapper.selectById(id);
        if(Optional.ofNullable(customerInfo).isPresent()){
            BeanUtil.copyProperties(customerInfo,customerInfoVO);
            List<ContactVO> lxrList=new ArrayList<>();

            Set<String> dictNameSet = new HashSet<>();
            dictNameSet.add("最终客户分级");
            dictNameSet.add("联系人角色");
            dictNameSet.add("是否");
            Map<String, List<DictKvVo>> dictMap = remoteBcpDictService.getDictKvBatchList(dictNameSet).getData();

            //行业一二级
            Map<String, SysDictItem> khxyyjChildMap=new HashMap<>();
            Map<Long, SysDictItem> khxyyjParentMap=new HashMap<>();
            getHyfjMap(khxyyjChildMap,khxyyjParentMap);


            //最终客户分级
            List<DictKvVo> khfjList = dictMap.get("最终客户分级");
            Map<String, String> khfjMap = khfjList.stream()
                    .collect(Collectors.toMap(DictKvVo::getValue, DictKvVo::getName, (a, b) -> a));

            ////是否
            List<DictKvVo> sfjtlkhList = dictMap.get("是否");
            Map<String, String> sfjtlkhMap = sfjtlkhList.stream()
                    .collect(Collectors.toMap(DictKvVo::getValue, DictKvVo::getName, (a, b) -> a));

            ////联系人角色
            List<DictKvVo> lxrjsList = dictMap.get("联系人角色");
            Map<String, String> lxrjsMap = lxrjsList.stream()
                    .collect(Collectors.toMap(DictKvVo::getValue, DictKvVo::getName, (a, b) -> a));

            //部门
            List<com.gok.bcp.upms.vo.SysDeptOutVO> deptList = remoteOutService.getOrgStructList().getData();
            Map<Long, com.gok.bcp.upms.vo.SysDeptOutVO> deptMap = CollUtil.isNotEmpty(deptList)
                    ? deptList.stream().collect(Collectors.toMap(SysDeptOutVO::getDeptId, d -> d))
                    : new HashMap<>();

            if(StrUtil.isNotBlank(customerInfo.getLxr1())){
                lxrList.add(ContactVO.builder().lxr(customerInfo.getLxr1()).lxrjs(lxrjsMap.get(customerInfo.getLxrjs1()))
                        .lxdh(customerInfo.getLxdh1()).sqsj(customerInfo.getSqsj1()).build());
            }
            if(StrUtil.isNotBlank(customerInfo.getLxr2())){
                lxrList.add(ContactVO.builder().lxr(customerInfo.getLxr2()).lxrjs(lxrjsMap.get(customerInfo.getLxrjs2()))
                        .lxdh(customerInfo.getLxdh2()).sqsj(customerInfo.getSqsj2()).build());
            }
            if(StrUtil.isNotBlank(customerInfo.getLxr3())){
                lxrList.add(ContactVO.builder().lxr(customerInfo.getLxr3()).lxrjs(lxrjsMap.get(customerInfo.getLxrjs3()))
                        .lxdh(customerInfo.getLxdh3()).sqsj(customerInfo.getSqsj3()).build());
            }
            customerInfoVO.setLxrList(lxrList);
                if(customerInfoVO.getKhfj()!=null){
                    customerInfoVO.setKhfjTxt(khfjMap.get(String.valueOf(customerInfoVO.getKhfj())));
                }
                if(customerInfoVO.getKhxyyj()!=null){
                    SysDictItem sysDictItem = khxyyjChildMap.get(String.valueOf(customerInfoVO.getKhxyyj()));
                    if(Optional.ofNullable(sysDictItem).isPresent()){
                        SysDictItem sysDictItemParent = khxyyjParentMap.get(sysDictItem.getParentId());
                        customerInfoVO.setKhxyyjTxt(sysDictItemParent!=null
                                ?sysDictItemParent.getItemName()+"/"+sysDictItem.getItemName():sysDictItem.getItemName());
                    }
                }
            if(customerInfoVO.getSfjtlkh()!=null){
                customerInfoVO.setSfjtlkhTxt(sfjtlkhMap.get(String.valueOf(customerInfoVO.getSfjtlkh())));
            }

             if(customerInfo.getKhszd()!=null){
                 OaHrmcity oaHrmcity = oaHrmcityMapper.selectById(customerInfo.getKhszd());
                 customerInfoVO.setKhszdTxt(oaHrmcity.getCityname());
             }

            if (StrUtil.isNotBlank(customerInfo.getKhbalcRequestid())&&StrUtil.isNotBlank(customerInfo.getKhbalcRelateid())) {
                List<OaFileVo> resourcesData = oaUtil.getResourcesData(String.valueOf(customerInfo.getKhbalcRequestid()),
                        String.valueOf(customerInfo.getKhbalcRelateid()));
                OaFileInfoVo xmsbqsdfjOaFileInfoVo = new OaFileInfoVo();
                xmsbqsdfjOaFileInfoVo.setRequestId(String.valueOf(customerInfo.getKhbalcRequestid()));
                xmsbqsdfjOaFileInfoVo.setFileId(customerInfo.getKpfj());
                customerInfoVO.setKpfjList(pmsDocImageFileService.getOaOaFileInfoList(xmsbqsdfjOaFileInfoVo
                        , resourcesData));
            }
            if (customerInfoVO.getGsywbmejbm() != null) {
                SysDeptOutVO sysDeptOutVO = deptMap.get(customerInfoVO.getGsywbmejbm());
                if (Optional.ofNullable(sysDeptOutVO).isPresent()) {
                    customerInfoVO.setGsywbmejbmTxt(sysDeptOutVO.getName());
                    if (Optional.ofNullable(sysDeptOutVO.getParentId()).isPresent()) {
                        SysDeptOutVO sysDeptOutVOParent = deptMap.get(sysDeptOutVO.getParentId());
                        if (Optional.ofNullable(sysDeptOutVOParent).isPresent()) {
                            customerInfoVO.setGsywbmejbmTxt(sysDeptOutVOParent.getName() + "/" + sysDeptOutVO.getName());
                        }
                    }
                }

            }
            QueryWrapper<CustomerAttention> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(CustomerAttention::getUserid,SecurityUtils.getUser().getId());
            List<Long> customerIds = customerAttentionService.list(queryWrapper).stream()
                    .map(CustomerAttention::getCustomerId).collect(Collectors.toList());
            if(CollUtil.isNotEmpty(customerIds)&&customerIds.contains(customerInfo.getId())){
                customerInfoVO.setIsAttention(true);

            }
        }
        return customerInfoVO;
    }


    @Override
    public CustomerInfoVO simplyFindById(Long id) {
        CustomerInfoVO customerInfoVO = new CustomerInfoVO();
        CustomerInfo customerInfo = baseMapper.selectById(id);
        if(Optional.ofNullable(customerInfo).isPresent()){
            BeanUtil.copyProperties(customerInfo,customerInfoVO);
            Set<String> dictNameSet = new HashSet<>();
            dictNameSet.add("最终客户分级");
            dictNameSet.add("联系人角色");
            dictNameSet.add("是否");
            Map<String, List<DictKvVo>> dictMap = remoteBcpDictService.getDictKvBatchList(dictNameSet).getData();
            //行业一二级
            Map<String, SysDictItem> khxyyjChildMap=new HashMap<>();
            Map<Long, SysDictItem> khxyyjParentMap=new HashMap<>();
            getHyfjMap(khxyyjChildMap,khxyyjParentMap);
            //最终客户分级
            List<DictKvVo> khfjList = dictMap.get("最终客户分级");
            Map<String, String> khfjMap = khfjList.stream()
                    .collect(Collectors.toMap(DictKvVo::getValue, DictKvVo::getName, (a, b) -> a));
            //部门
            List<com.gok.bcp.upms.vo.SysDeptOutVO> deptList = remoteOutService.getOrgStructList().getData();
            Map<Long, com.gok.bcp.upms.vo.SysDeptOutVO> deptMap = CollUtil.isNotEmpty(deptList)
                    ? deptList.stream().collect(Collectors.toMap(SysDeptOutVO::getDeptId, d -> d))
                    : new HashMap<>();
            if(customerInfoVO.getKhfj()!=null){
                //客户分级Txt
                customerInfoVO.setKhfjTxt(khfjMap.get(String.valueOf(customerInfoVO.getKhfj())));
            }
            if(customerInfoVO.getKhxyyj()!=null){
                SysDictItem sysDictItem = khxyyjChildMap.get(String.valueOf(customerInfoVO.getKhxyyj()));
                if(Optional.ofNullable(sysDictItem).isPresent()){
                    SysDictItem sysDictItemParent = khxyyjParentMap.get(sysDictItem.getParentId());
                    //客户行业Txt
                    customerInfoVO.setKhxyyjTxt(sysDictItemParent!=null
                            ?sysDictItemParent.getItemName()+"/"+sysDictItem.getItemName():sysDictItem.getItemName());
                }
            }
            if(customerInfo.getKhszd()!=null){
                OaHrmcity oaHrmcity = oaHrmcityMapper.selectById(customerInfo.getKhszd());
                //客户所在地TXT
                customerInfoVO.setKhszdTxt(oaHrmcity.getCityname());
            }
            if (customerInfoVO.getGsywbmejbm() != null) {
                SysDeptOutVO sysDeptOutVO = deptMap.get(customerInfoVO.getGsywbmejbm());
                if (Optional.ofNullable(sysDeptOutVO).isPresent()) {
                    //业务归属部门Txt
                    customerInfoVO.setGsywbmejbmTxt(sysDeptOutVO.getName());
                    if (Optional.ofNullable(sysDeptOutVO.getParentId()).isPresent()) {
                        SysDeptOutVO sysDeptOutVOParent = deptMap.get(sysDeptOutVO.getParentId());
                        if (Optional.ofNullable(sysDeptOutVOParent).isPresent()) {
                            customerInfoVO.setGsywbmejbmTxt(sysDeptOutVOParent.getName() + "/" + sysDeptOutVO.getName());
                        }
                    }
                }
            }
        }
        return customerInfoVO;
    }

    private void getHyfjMap(Map<String, SysDictItem> khxyyjChildMap,Map<Long, SysDictItem> khxyyjParentMap){
        //行业一二级
        Set<String> dictLevelNameSet = new HashSet<>();
        dictLevelNameSet.add("行业一二级");
        Map<String, List<SysDictItem>> dictLeverMap = remoteBcpDictService.batchLeverList(dictLevelNameSet).getData();

        List<SysDictItem>  hyfjDictItemList = dictLeverMap.get("行业一二级");
        khxyyjChildMap.putAll(hyfjDictItemList.stream().filter(s->s.getParentId()!=1000015516)
                .collect(Collectors.toMap(SysDictItem::getItemValue,d->d)));
        khxyyjParentMap.putAll(hyfjDictItemList.stream().filter(s->s.getParentId()==1000015516)
                .collect(Collectors.toMap(SysDictItem::getItemId,d->d)));
    }

    @Override
    public String attention(CustomerAttentionDTO dto){
        CustomerAttention customerAttention = new CustomerAttention();
        customerAttention.setUserid(SecurityUtils.getUser().getId());
        customerAttention.setCustomerId(dto.getCustomerId());
        CustomerInfo customerInfo = baseMapper.selectById(dto.getCustomerId());
        if(dto.getIsAttention()){
            customerAttentionService.save(customerAttention);
            //日志
            bcpLoggerUtils.log(FunctionConstants.CUSTOMER_LEDGER, LogContentEnum.PAY_ATTENTION_TO_CUSTOMERS,
                    SecurityUtils.getUser().getName(), customerInfo.getKhmc());
            return "关注成功";
        }else{
            QueryWrapper<CustomerAttention> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(CustomerAttention::getCustomerId, customerAttention.getCustomerId())
                            .eq(CustomerAttention::getUserid,customerAttention.getUserid());
            customerAttentionService.remove(queryWrapper);
            //日志
            bcpLoggerUtils.log(FunctionConstants.CUSTOMER_LEDGER, LogContentEnum.CANCEL_FOLLOWING_CUSTOMERS,
                    SecurityUtils.getUser().getName(), customerInfo.getKhmc());
            return "取消关注成功";
        }

    }

    /**
     * 根据项目状态对项目信息进行分组
     *
     * @param projectInfoList 项目信息列表
     * @param statusEnum 项目状态枚举
     * @param keyExtractor 分组键提取函数
     * @return 按指定键分组的项目信息映射
     */
    private Map<String, List<ProjectInfo>> groupProjectsByStatus(
            List<ProjectInfo> projectInfoList,
            ProjectStatusEnum statusEnum,
            java.util.function.Function<ProjectInfo, String> keyExtractor) {

        return projectInfoList.stream()
                .filter(project -> statusEnum.getStrValue().equals(project.getProjectStatus()))
                .collect(Collectors.groupingBy(keyExtractor));
    }
}
