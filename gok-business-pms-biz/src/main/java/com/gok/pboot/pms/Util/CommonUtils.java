package com.gok.pboot.pms.Util;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.experimental.UtilityClass;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/2/23
 * 基础常用工具
 */
@UtilityClass
public class CommonUtils {
    /**
     * 手动分页
     * @param currentPage 当前页
     * @param pageSize 每页大小
     * @param list 数据列表
     * @return
     * @param <T>
     */
    public static <T> Page<T> getPages(Integer currentPage, Integer pageSize, List<T> list) {

        Page<T> page = new Page<>();

        int size = list.size();
        if (size == 0){
            return page;
        }

        //求出最大页数，防止currentPage越界
        int maxPage = size % pageSize == 0 ? size / pageSize : size / pageSize + 1;

        if (currentPage > maxPage) {
            currentPage = maxPage;
        }

        //当前页第一条数据下标
        int curIds = currentPage > 1 ? (currentPage - 1) * pageSize : 0;

        List<T> pageList = new ArrayList<>();

        //将当前页的数据放进pageList
        for (int i = 0; i < pageSize && curIds + i < size; i++) {
            pageList.add(list.get(curIds + i));
        }

        page.setCurrent(currentPage).setSize(pageSize).setTotal(list.size()).setRecords(pageList);

        return page;

    }

    /**
     * 计算应出勤天数
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param holidays 假期数量
     * @return 应出勤天数
     */
    public int attendanceDays(LocalDate startTime, LocalDate endTime, Integer holidays) {
        return (int) Math.max((startTime.until(endTime, ChronoUnit.DAYS) - holidays + 1), 0);
    }

    /**
     * 将工时换算成人天
     * 人天单位的工时 = 填写的工时 / 7
     *
     * @param hours 工时
     * @return 人天单位的工时
     */
    public Double unitConversion(Double hours) {
        BigDecimal unit = new BigDecimal(7);
        BigDecimal item = BigDecimal.valueOf(hours);

        BigDecimal result = item.divide(unit, 2, RoundingMode.HALF_UP);
        return result.doubleValue();
    }

    /**
     * 将工时换算成人天
     * 人天单位的工时 = 填写的工时 / 7
     *
     * @param hours 工时
     * @return 人天单位的工时
     */
    public BigDecimal unitConversion(BigDecimal hours) {
        if (ObjectUtil.isNull(hours) || BigDecimal.ZERO.equals(hours)){
            return BigDecimal.ZERO.setScale(0, RoundingMode.HALF_UP);
        }

        BigDecimal unit = new BigDecimal(7);
        return hours.divide(unit, 2, RoundingMode.HALF_UP);
    }

    /**
     * 将人天转换为工时时  传入工时保留1位小数
     *
     * @param hours 工时
     * @return 小时单位的工时
     */
    public BigDecimal roundOnePoint(BigDecimal hours){
        if (ObjectUtil.isNull(hours) || BigDecimal.ZERO.equals(hours)){
            return BigDecimal.ZERO.setScale(0, RoundingMode.HALF_UP);
        }
        return hours.setScale(1,RoundingMode.HALF_UP);
    }

    /**
     * 将数字转为保留两位小数的百分比
     *
     * @param num 需要转换的数字
     **/
    public Double calculationPercentByTwoPreserves(Double num) {
        BigDecimal convertNum = BigDecimal.valueOf(num);
        BigDecimal hundred = new BigDecimal(100);
        BigDecimal result = convertNum.setScale(4, RoundingMode.HALF_UP).multiply(hundred);
        return result.doubleValue();
    }
    /**
     * 将数字转为两位小数
     *
     * @param num 需要转换的数字
     **/
    public Double calculationByTwoPreserves(Double num) {
        BigDecimal convertNum = BigDecimal.valueOf(num);
        BigDecimal result = convertNum.setScale(4, RoundingMode.HALF_UP);
        return result.doubleValue();
    }

    /**
     * 计算工时饱和度
     * 工时饱和度计算 = 项目耗用工时（人天）/应出勤天数
     *
     * @param projectHours   项目耗用工时（人天）
     * @param attendanceDays 应出勤天数
     * @return 工时饱和度
     */
    public BigDecimal calculationHourSaturation(BigDecimal projectHours, BigDecimal attendanceDays) {
        if (BigDecimal.ZERO.compareTo(attendanceDays) == 0) {
            return BigDecimal.ZERO;
        }

        BigDecimal result = projectHours.divide(attendanceDays, 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
        return result.setScale(2, RoundingMode.HALF_UP);
    }
}
