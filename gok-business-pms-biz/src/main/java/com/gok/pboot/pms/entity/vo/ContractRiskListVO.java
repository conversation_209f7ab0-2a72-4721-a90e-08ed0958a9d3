package com.gok.pboot.pms.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
    * 合同台账相关风险信息vo
    * <AUTHOR>
*/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ContractRiskListVO {

    /**
    * 风险类型名
    */
    private String riskTypeName;

    /**
     * 流程信息
     */
    private List<ContractRiskInfoVO> infoVOList;

}