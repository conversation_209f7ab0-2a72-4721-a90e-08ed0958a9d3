/*
 *
 *      Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the pig4cloud.com developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: lengleng (<EMAIL>)
 *
 */

package com.gok.pboot.pms.entity.vo;

import com.gok.bcp.upms.vo.SysDeptOutVO;
import com.gok.bcp.upms.vo.SysUserOutVO;
import com.gok.pboot.pms.Util.SysDeptUtils;
import com.gok.pboot.pms.entity.SysDept;
import lombok.Data;
import org.apache.commons.collections4.MapUtils;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2017-10-29
 */
@Data
public class SysUserPmsCqVO {

    /**
     * 用户编号
     */
    private Long userId;
    /**
     * 姓名
     */
    private String name;
    /**
     * 部门编号
     */
    private Long deptId;
    /**
     * 部门全称
     */
    private String deptName;

    /**
     * 当前部门全称
     */
    private String currentDeptName;
    /**
     * 人员状态
     */
    private Integer personnelStatus;
    /**
     * 人员状态名称
     */
    private String personnelStatusName;
    /**
     * 职位
     */
    private String jobName;


    public static SysUserPmsCqVO of(SysUserOutVO request, Map<Long, SysDept> sysDeptMap) {
        Long deptId = null;
        SysUserPmsCqVO result = new SysUserPmsCqVO();
        Map<Long, List<SysDeptOutVO>> deptMap = request.getDeptMap();
        if (MapUtils.isNotEmpty(deptMap)) {
            deptId = deptMap.keySet().iterator().next();
        }

        result.setUserId(request.getUserId());
        result.setName(request.getUsername());
        result.setDeptId(deptId);
        if (sysDeptMap != null){
            result.setDeptName("");
        }
        if (sysDeptMap != null && deptId != null) {
            SysDept sysDept = sysDeptMap.get(deptId);
            if (sysDept != null) {
                result.setCurrentDeptName(sysDept.getName());
                result.setDeptName(SysDeptUtils.collectFullName(sysDeptMap, deptId));
            }
        }

        return result;
    }
}
