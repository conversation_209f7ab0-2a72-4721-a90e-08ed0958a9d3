package com.gok.pboot.pms.cost.enums;

import com.gok.pboot.pms.enumeration.ValueEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 计算方式枚举
 *
 * <AUTHOR>
 * @date 2025/01/07
 */
@AllArgsConstructor
@Getter
public enum CalculationMethodEnum implements ValueEnum<Integer> {

    /**
     * 按预计人天计算
     */
    BY_ESTIMATED_PERSON_DAY(0, "按预计人天计算"),

    /**
     * 按出差人天计算
     */
    BY_TRAVEL_PERSON_DAY(1, "按出差人天计算");


    private final Integer value;

    private final String name;
}
