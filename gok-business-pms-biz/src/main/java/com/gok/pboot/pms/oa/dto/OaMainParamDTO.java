package com.gok.pboot.pms.oa.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * - 创建流程主要参数 -
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OaMainParamDTO {

    /**
     * 主表字段名
     */
    private String fieldName;
    /**
     * 主表字段值
     */
    private String fieldValue;






}
