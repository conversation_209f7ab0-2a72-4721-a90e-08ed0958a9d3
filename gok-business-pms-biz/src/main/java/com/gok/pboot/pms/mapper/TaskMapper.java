package com.gok.pboot.pms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.Util.PageAdapter;
import com.gok.pboot.pms.common.join.TaskInDailyPaperEntry;
import com.gok.pboot.pms.entity.domain.ProjectStakeholderMember;
import com.gok.pboot.pms.entity.domain.Task;
import com.gok.pboot.pms.entity.vo.TaskInDailyPaperEntryListVo;
import com.gok.pboot.pms.entity.vo.TaskInfoVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 任务 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-19
 */
@Mapper
public interface TaskMapper extends BaseMapper<Task> {


    /**
     * 逻辑删除
     *
     * @param id 唯一标识
     * @return
     */
    int deleteByLogic(@Param("id") Long id);

    /**
     * 批量插入
     *
     * @param poList  实体集合
     */
    void batchSave(@Param("poList") List<Task> poList);



    /**
     * 批量修改
     *
     * @param list 实体集合
     */
    void batchUpdate(@Param("list") List<Task> list);

    /**
    * 批量逻辑删除
    *
    * @param list id集合
    */
    void batchDel(@Param("list") List<Long> list);

    /**
    * 批量修改不为空字段
    *
    * @param list id集合
    */
    void updateBatch(@Param("list") List<Long> list);

    /**
     * 项目下的任务详情
     * @param adapter
     * @param id 项目id
     * @param filter
     * @return
     */
    List<TaskInfoVO> selectListVo(@Param("adapter") PageAdapter adapter, @Param("id")Long id, @Param("filter") Map<String, Object> filter);

    Map<String, Object> totalMap(Long id);

    Long selectTaskCount(@Param("id")Long id,@Param("filter") Map<String, Object> filter);
    Long selectTaskCountMr(@Param("id")Long id);
    /**
     * 任务详情
     * @param id 任务id
     * @return
     */
    TaskInfoVO getTaskInfo(@Param("id")Long id, @Param("approvalStatus")Integer integer);

    /**
     * ~ 根据用户ID查询目标用户可用的任务 ~
     * @param userId 用户ID
     * @param projectId 项目ID
     * @return java.util.List<com.gok.pboot.pms.common.join.TaskInDailyPaperEntry>
     * <AUTHOR>
     * @date 2022/8/26 9:19
     */
    List<TaskInDailyPaperEntry> findByUserIdAndProjectIdForEntry(
            @Param("userId") Long userId,
            @Param("projectId") Long projectId
    );

    /**
     * 批量根据用户ID查询目标用户可用的任务
     *
     * @param list
     * @return {@link List}<{@link TaskInDailyPaperEntryListVo}>
     */
    List<TaskInDailyPaperEntryListVo> findByUserIdAndProjectIdForEntryVos(
            @Param("userId") Long userId,
            @Param("list") List<Long> list
    );

    /**
     * ~ 根据项目ID获取 ~
     * @param projectId 项目ID
     * @return java.util.List<com.gok.pboot.pms.entity.domain.Task>
     * <AUTHOR>
     * @date 2022/9/16 11:14
     */
    List<Task> findByProjectId(Long projectId);

    /**
     * 通过项目id集合获取任务及相关人员
     * @param prjs
     * @return
     */
    List<TaskInfoVO> selectDefaultPersonnel(@Param("projectIds")List<Long> prjs);

    /**
     * 获取全部任务
     *
     * @return {@link List}<{@link Task}>
     */
    List<Task> findAll();

    List<ProjectStakeholderMember> findToSyncMember();

}
