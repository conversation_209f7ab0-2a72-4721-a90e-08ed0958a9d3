package com.gok.pboot.pms.entity.vo;

import com.gok.pboot.pms.common.join.ProjectInDailyPaperEntry;
import com.gok.pboot.pms.entity.DailyPaper;
import com.gok.pboot.pms.entity.DailyPaperEntry;
import com.gok.pboot.pms.entity.Holiday;
import com.gok.pboot.pms.entity.domain.TomorrowPlanPaperEntry;
import lombok.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * - 日报详情VO -
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/8/24 16:46
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@ToString
public class DailyPaperDetailsVO {
    /**
     * 日报信息
     */
    private DailyPaperVO dailyPaper;

    /**
     * 异常情况
     */
    private String abnormalDesc;

    /**
     * 日报条目信息
     */
    private List<DailyPaperEntryVO> entries;

    private List<TomorrowPlanPaperEntryVO> tomorrowPlanPaperEntries;

    public DailyPaperDetailsVO(
            DailyPaper dailyPaper,
            List<DailyPaperEntry> entries,
            List<TomorrowPlanPaperEntry> tomorrowPlanPaperEntries,
            Map<Long, ProjectInDailyPaperEntry> projects
    ) {
        this.tomorrowPlanPaperEntries = tomorrowPlanPaperEntries.stream()
                .map(tomorrowPlanPaperEntry -> new TomorrowPlanPaperEntryVO(tomorrowPlanPaperEntry, projects.get(tomorrowPlanPaperEntry.getProjectId())))
                .collect(Collectors.toList());
        this.dailyPaper = new DailyPaperVO(dailyPaper);
        this.entries = entries.stream()
                .map(entry -> new DailyPaperEntryVO(entry, projects.get(entry.getProjectId())))
                .collect(Collectors.toList());
    }

    public DailyPaperDetailsVO(
            DailyPaper dailyPaper,
            List<DailyPaperEntry> entries,
            List<TomorrowPlanPaperEntry> tomorrowPlanPaperEntries,
            Map<Long, ProjectInDailyPaperEntry> projects,
            String userRealName,
            String abnormalDesc,
            BigDecimal leaveHour,
            BigDecimal compensatoryLeave,
            Holiday holiday
    ) {
        this.tomorrowPlanPaperEntries = tomorrowPlanPaperEntries.stream()
                .map(tomorrowPlanPaperEntry -> new TomorrowPlanPaperEntryVO(tomorrowPlanPaperEntry, projects.get(tomorrowPlanPaperEntry.getProjectId())))
                .collect(Collectors.toList());
        this.dailyPaper = new DailyPaperVO(dailyPaper, userRealName, leaveHour,compensatoryLeave,holiday);
        this.entries = entries.stream()
                .map(entry -> new DailyPaperEntryVO(entry, projects.get(entry.getProjectId())))
                .collect(Collectors.toList());
        this.abnormalDesc = abnormalDesc;
    }
}
