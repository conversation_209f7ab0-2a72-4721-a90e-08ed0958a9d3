package com.gok.pboot.pms.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 商机信息表（OA同步）(BusinessInfo)表实体类
 *
 * <AUTHOR>
 * @since 2023-11-21 14:18:07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@TableName("business_info")
public class BusinessInfo extends BeanEntity<Long> {

    /**
     * 商机名称(项目名称)
     */
    private String businessName;

    /**
     * 项目编号
     */
    private String itemNo;

    /**
     * 业务归属一级部门id
     */
    private Long firstLevelDepartmentId;

    /**
     * 业务归属一级部门
     */
    private String firstLevelDepartment;

    /**
     * 业务归属部门id
     */
    private Long departmentId;

    /**
     * 业务归属部门
     */
    private String department;

    /**
     * 是否内部项目
     */
    private Long isNotInternalProject;

    /**
     * 项目所在地
     */
    private String projectLocation;

    /**
     * 最终客户名称
     */
    private String endCustomerName;

    /**
     * 最终客户分级
     */
    private String endCustomerGrade;

    /**
     * 最终客户行业
     */
    private String endCustomerIndustry;

    /**
     * 签约客户名称
     */
    private String contractCustomerName;

    /**
     * 签约客户分级
     */
    private String contractCustomerGrade;

    /**
     * 是否需要招投标
     */
    private Long isNotNeedBidding;

    /**
     * 招标方式
     */
    private String biddingMethod;

    /**
     * 项目签约主体
     */
    private String contractEntity;

    /**
     * 业务板块
     */
    private String businessModule;

    /**
     * 收入类型
     */
    private String incomeType;

    /**
     * 技术类型
     */
    private String technologyType;

    /**
     * 结算方式
     */
    private String settlementMethod;

    /**
     * 交付形式
     */
    private String deliveryMethod;

    /**
     * 关键决策链
     */
    private String keyDecisionChain;

    /**
     * 关键决策人支持情况
     */
    private String supportFromKeyDecision;

    /**
     * 项目需求是否明确
     */
    private String projectRequirementClear;

    /**
     * 项目需求
     */
    private String projectRequirement;

    /**
     * 预算情况
     */
    private String budgetSituation;

    /**
     * 预计签单金额
     */
    private BigDecimal expectedOrderAmount;

    /**
     * 预估毛利率
     */
    private String ygmll;

    /**
     * 预计签单时间
     */
    private String expectedCompleteTime;

    /**
     * 竞争情况
     */
    private String competitionSituation;

    /**
     * 商机阶段
     */
    private String businessStage;

    /**
     * 项目里程碑
     */
    private String businessMilestone;

    /**
     * 内部项目类别
     */
    private String internalProjectType;

    /**
     * 是否涉及外采
     */
    private String isExternalProcurement;

    /**
     * 采购类别
     */
    private String purchasingCategories;

    /**
     * 商机状态
     */
    private String businessStatus;

    /**
     * 售前工时
     */
    private String preSaleHours;

    /**
     * 售后工时
     */
    private String afterSaleHours;

    /**
     * 学员工时
     */
    private String studentHours;

    /**
     * 售前投入金额
     */
    private BigDecimal preSaleInvestAmount;

    /**
     * 售后投入金额
     */
    private BigDecimal afterSaleInvestAmount;

    /**
     * 项目销售人员ID(客户经理ID)
     */
    private Long salesmanUserId;

    /**
     * 项目销售人员(客户经理姓名)
     */
    private String projectSalesperson;

    /**
     * 售前人员ID(售前经理ID)
     */
    private Long preSaleUserId;

    /**
     * 售前人员姓名(售前经理姓名)
     */
    private String preSaleUserName;

    /**
     * 项目经理人员ID
     */
    private Long managerUserId;

    /**
     * 项目经理人员姓名
     */
    private String managerUserName;

    /**
     * 商机创建时间
     */
    private String businessCtime;

    /**
     * 商机更新时间
     */
    private String businessMtime;

    /**
     * 成交时间/终止时间
     */
    private LocalDate transactionDate;
}

