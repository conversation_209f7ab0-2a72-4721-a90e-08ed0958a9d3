package com.gok.pboot.pms.entity.bo;

import com.gok.wx.entity.dto.WxCpUserSimpleDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * - 带有部门名称的用户对象 -
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WxCpUserSimpleWithDeptNameBO {

    private WxCpUserSimpleDTO user;

    private String deptName;

    public static WxCpUserSimpleWithDeptNameBO from(WxCpUserSimpleDTO user, String deptName){
        return new WxCpUserSimpleWithDeptNameBO(user, deptName);
    }
}
