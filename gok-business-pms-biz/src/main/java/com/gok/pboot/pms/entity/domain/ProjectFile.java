package com.gok.pboot.pms.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 项目附件表
 *
 * <AUTHOR>
 * @date 2023/11/22
 */
@Data
@TableName("project_file")
@EqualsAndHashCode(callSuper = false)
public class ProjectFile {

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 文件名
     */
    private String docName;

    /**
     * 文件url-oa地址
     */
    private String docUrl;

    /**
     * 所属类型（0 项目附件 1 商机进展记录 2 客户沟通记录）
     */
    private Integer associationType;

    /**
     * 创建时间
     */
    private LocalDateTime ctime;

}
