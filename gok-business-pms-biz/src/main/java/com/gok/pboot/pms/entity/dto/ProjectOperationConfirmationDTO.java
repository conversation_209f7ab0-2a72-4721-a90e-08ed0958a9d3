package com.gok.pboot.pms.entity.dto;

import com.gok.pboot.pms.enumeration.ProjectOperationRoleEnum;
import com.gok.pboot.pms.enumeration.ProjectOperationTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2025/07/03
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectOperationConfirmationDTO {

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 操作用户ID
     */
    private Long userId;

    /**
     * 操用用户名称
     */
    private String userName;

    /**
     * 模块 0-质保 1-关闭
     */
    @NotNull
    @Min(0)
    @Max(1)
    private Integer module;

    /**
     * 操作类型 0-否 1-确认 2-之后提醒 3-知悉
     * {@link ProjectOperationTypeEnum#getValue()}
     */
    @NotNull
    @Min(0)
    @Max(3)
    private Integer operationType;

    /**
     * 提醒日期
     */
    private LocalDate reminderDate;

    /**
     * 角色 0-项目经理 1-客户经理 2-项目经理上级 3-客户经理上级 4-PMO
     * {@link ProjectOperationRoleEnum#getValue}
     */
    private List<Integer> role;

}
