package com.gok.pboot.pms.service;



import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.entity.domain.OaHrmcity;
import com.gok.pboot.pms.entity.vo.OaHrmcityVO;

import java.util.List;


/**
 * Oa所在城市
 *
 * <AUTHOR>
 * @date 2024-02-26 14:58:58
 */
public interface IOaHrmcityService extends IService<OaHrmcity> {
    /**
     * 所在城市集合
     *
     * @return {@link ApiResult}<{@link List}<{@link OaHrmcityVO}>
     */
    List<OaHrmcityVO> getListByName( String name);

    /**
     * 获取所有城市列表
     *
     * @return {@link List }<{@link OaHrmcityVO }>
     */
    List<OaHrmcityVO> getAllCityList();
}

