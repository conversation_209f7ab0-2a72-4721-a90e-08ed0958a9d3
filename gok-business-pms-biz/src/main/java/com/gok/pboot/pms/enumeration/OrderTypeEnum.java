package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 订单类型枚举
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@AllArgsConstructor
@Getter
public enum OrderTypeEnum {

    /**
     * 机票订单
     */
    FLIGHT_TICKET("FLIGHT_TICKET", "机票订单", "【账期】滴滴商旅平台差旅费用-交通费"),

    /**
     * 酒店订单
     */
    HOTEL("HOTEL", "酒店订单", "【账期】滴滴商旅平台差旅费用-住宿费"),

    /**
     * 用车订单
     */
    VEHICLE("VEHICLE", "用车订单", "【账期】滴滴商旅平台差旅费用-市内交通费");

    private final String code;
    private final String name;
    //费用摘要
    private final String expenseSummary;

    /**
     * 根据代码获取枚举
     *
     * @param code 代码
     * @return 枚举
     */
    public static OrderTypeEnum getByCode(String code) {
        for (OrderTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
} 