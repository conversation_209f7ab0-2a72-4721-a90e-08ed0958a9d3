package com.gok.pboot.pms.controller;

import com.gok.pboot.common.security.annotation.Inner;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.BaseController;
import com.gok.pboot.pms.entity.dto.DailyPaperAddOrUpdateDTO;
import com.gok.pboot.pms.entity.dto.DailyPaperMonthlyDTO;
import com.gok.pboot.pms.entity.vo.CompensatoryLeaveDataVO;
import com.gok.pboot.pms.entity.vo.DailyPaperDetailsVO;
import com.gok.pboot.pms.entity.vo.DailyPaperVO;
import com.gok.pboot.pms.entity.vo.DailyPapersStatisticMonthlyVO;
import com.gok.pboot.pms.service.ICompensatoryLeaveDataService;
import com.gok.pboot.pms.service.IDailyPaperService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDate;
import java.util.List;

/**
 * 日报控制器
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/8/23 16:28
 * @menu PMS业务模块-日报
 */
@Slf4j
@RestController
@RequestMapping("/dailyPaper")
@RequiredArgsConstructor
public class DailyPaperController extends BaseController {

    private final IDailyPaperService service;

    private final ICompensatoryLeaveDataService overtimeLeaveDataService;


    /**
     * 添加/编辑 日报
     *
     * @param request 请求实体
     * @return com.gok.pboot.pms.common.base.ApiResult<java.lang.Void>
     * <AUTHOR>
     * @date 2022/8/23 16:37
     */
    @PostMapping("/addOrUpdate")
    public ApiResult<Void> addOrUpdate(@Valid @RequestBody DailyPaperAddOrUpdateDTO request) {
        request.setUserId(SecurityUtils.getUser().getId());
        service.newAddOrUpdate(request);
        return ApiResult.success(null);
    }

    /**
     * 根据ID获取绩效报表详情
     *
     * @param id 报表ID
     * @return com.gok.pboot.pms.common.base.ApiResult<com.gok.pboot.pms.entity.vo.DailyPaperDetailsVO>
     * <AUTHOR>
     * @date 2022/8/25 9:45
     */
    @GetMapping("/details/{id}")
    public ApiResult<DailyPaperDetailsVO> details(@PathVariable("id") Long id) {
        return ApiResult.success(service.getDetailsById(id));
    }

    @GetMapping("/statisticMonthly")
    public ApiResult<DailyPapersStatisticMonthlyVO> statisticMonthly(DailyPaperMonthlyDTO request) {

        return ApiResult.success(service.getStatisticMonthly(request));
    }

    /**
     * 查询日报
     *
     * @return com.gok.pboot.pms.common.base.ApiResult<com.gok.pboot.pms.entity.vo.DailyPaperVO>
     * <AUTHOR>
     * @date 2022/8/26 14:30
     */
    @PostMapping("/listMonthly")
    public ApiResult<List<DailyPaperVO>> listMonthly(@Valid @RequestBody DailyPaperMonthlyDTO request) {
        return ApiResult.success(service.listDailyPaperMonthly(request));
    }

    /**
     * ~ 根据日期查找离其最近的日报详情 ~
     *
     * @param date date
     * @return com.gok.pboot.pms.common.base.ApiResult<com.gok.pboot.pms.entity.vo.DailyPaperDetailsVO>
     * <AUTHOR>
     * @date 2022/9/5 11:20
     */
    @GetMapping("/detailsForBackFill")
    public ApiResult<DailyPaperDetailsVO> detailsForBackFill(LocalDate date) {
        if (date == null) {
            return ApiResult.failure("请传入日期");
        }

        return ApiResult.success(service.getDetailsByDateForCurrentUser(date));
    }

    /**
     * 根据日期查询日报信息
     *
     * @param date 日期
     * @return {@link ApiResult<DailyPaperDetailsVO>}
     */
    @GetMapping("/detailsByDate")
    public ApiResult<DailyPaperDetailsVO> detailsBySubmissionDate(LocalDate date) {
        if (date == null) {
            return ApiResult.failure("请传入日期");
        }

        return ApiResult.success(service.getDetailsByDateForCurrentUserAccurate(date));
    }


    /**
     * 定时器
     * ~ 自动提交前一日日报定时任务调用方法 ~    0 0 14 * * ? *
     *
     * @return com.gok.pboot.pms.common.base.ApiResult<java.lang.Void>
     * <AUTHOR>
     * @date 2022/9/16 14:38
     */
    @Inner(value = false)
    @GetMapping("/autoSubmitJob")
    public ApiResult<Void> autoSubmitJob(@RequestParam("para") String para) {
        log.info("定时任务触发, 每天14:00自动提交指定日期前一天的日报，输入参数 {}，执行开始", para);
        service.autoSubmitJob(para);
        log.info("定时任务触发, 每天14:00自动提交指定日期前一天的日报，输入参数 {}，执行结束", para);
        return ApiResult.success(null);
    }

    /**
     * 查询日报
     *
     * @return com.gok.pboot.pms.common.base.ApiResult<com.gok.pboot.pms.entity.vo.DailyPaperVO>
     * <AUTHOR>
     * @date 2022/8/26 14:30
     */
    @GetMapping("/test")
    public ApiResult<List<CompensatoryLeaveDataVO>> test() {

        List<CompensatoryLeaveDataVO> byDateTimeRangeAndUserId =
                overtimeLeaveDataService.findByDateTimeRangeAndUserId(LocalDate.of(2024, 4, 16),
                      null, 1219L);
        return ApiResult.success(byDateTimeRangeAndUserId);
    }
}
