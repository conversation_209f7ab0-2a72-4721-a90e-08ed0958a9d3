package com.gok.pboot.pms.cost.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.cost.entity.vo.CostBaselineVersionRecordVO;
import com.gok.pboot.pms.cost.service.ICostBaselineVersionRecordService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 成本基准版本记录控制器
 *
 * <AUTHOR>
 * @date 2025/01/15
 */
@RestController
@AllArgsConstructor
@RequestMapping("/costBaselineVersionRecord")
public class CostBaselineVersionRecordController {

    private final ICostBaselineVersionRecordService costBaselineVersionRecordService;

    /**
     * 获取版本记录分页列表
     *
     * @param pageRequest 页面请求
     * @return {@link ApiResult }<{@link Page }<{@link CostBaselineVersionRecordVO }>>
     */
    @GetMapping("/getPage/{projectId}")
    public ApiResult<Page<CostBaselineVersionRecordVO>> getVersionRecordPage(@PathVariable("projectId") Long projectId, PageRequest pageRequest) {
        return ApiResult.success(costBaselineVersionRecordService.getVersionRecordPage(projectId, pageRequest), "获取成功");
    }

}
