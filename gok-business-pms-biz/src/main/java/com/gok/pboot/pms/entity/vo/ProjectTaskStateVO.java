package com.gok.pboot.pms.entity.vo;

import cn.hutool.core.util.NumberUtil;
import com.gok.pboot.pms.entity.domain.ProjectTask;
import com.gok.pboot.pms.enumeration.TaskWorkingState;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 项目下任务状态统计vo
 *
 * <AUTHOR>
 * @date 2023/8/24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectTaskStateVO {

    /**
     * 任务总数
     */
    private Integer total;

    /**
     * 已完成
     */
    private Integer finished;

    /**
     * 进行中
     */
    private Integer working;

    /**
     * 未开始
     */
    private Integer notStart;

    /**
     * 延期任务
     */
    private Integer delay;

    /**
     * 今日到期
     */
    private Integer deadLine;

    /**
     * 完成率
     */
    private String finishedRate;

    /**
     * 延期率
     */
    private String delayRate;


    public static ProjectTaskStateVO of(List<ProjectTask> taskList) {
        ProjectTaskStateVO result = new ProjectTaskStateVO();
        double total = 0;
        double finished = 0;
        int working = 0;
        int notStart = 0;
        double delay = 0;
        int deadLine = 0;
        String finishedRate = "";
        String delayRate = "";

        for (ProjectTask task : taskList) {
            total++;
            // 重新判断基本状态类型
            if (task.getState().equals(TaskWorkingState.FINISHED.getValue())) {
                finished++;
            }
            if (task.getState().equals(TaskWorkingState.WORKING.getValue())) {
                working++;
            }
            if (task.getState().equals(TaskWorkingState.NOT_START.getValue())) {
                notStart++;
            }
            if (task.getState().equals(TaskWorkingState.DELAY.getValue())) {
                delay++;
            }
            // 判断是否今日到期
            LocalDate today = LocalDate.now();
            LocalDateTime actualEndTime = task.getActualEndTime();
            LocalDateTime expectedEndTime = task.getExpectedEndTime();
            LocalDate expectedEndDate = expectedEndTime.toLocalDate();
            if (expectedEndDate.equals(today) && actualEndTime == null) {
                deadLine++;
            }
        }

        // 计算完成率和延期率
        finishedRate = NumberUtil.round(finished/total*100,0) + "%";
        delayRate = NumberUtil.round(delay/total*100,0) + "%";

        result.setTotal((int)total);
        result.setFinished((int) finished);
        result.setWorking(working);
        result.setNotStart(notStart);
        result.setDelay((int) delay);
        result.setDeadLine(deadLine);
        result.setFinishedRate(finishedRate);
        result.setDelayRate(delayRate);

        return result;
    }
}
