package com.gok.pboot.pms.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 销售收款计划
 * @TableName sales_receipt
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName(value ="sales_receipt")
@ApiModel("销售收款计划")
public class SalesReceipt extends Model<SalesReceipt> {
    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键id")
    private Long id;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private Long projectId;

    /**
     * 项目编号
     */
    @ApiModelProperty(value = "项目编号")
    private String projectNo;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 项目状态
     */
    @ApiModelProperty(value = "项目状态")
    private String projectStatus;

    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
    private Long contractId;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    private String contractName;

    /**
     * 合同编码
     */
    @ApiModelProperty(value = "合同编码")
    private String contractCode;

    /**
     * 合同签订日期
     */
    @ApiModelProperty(value = "合同签订日期")
    private String signingDate;

    /**
     * 预计收款日期
     */
    @ApiModelProperty(value = "预计收款日期")
    private String expectedDate;

    /**
     * 合同金额
     */
    @ApiModelProperty(value = "合同金额")
    private BigDecimal contractMoney;

    /**
     * 累计收款金额（含税）
     */
    @ApiModelProperty(value = "累计收款金额（含税）")
    private BigDecimal accumulatedAmount;

    /**
     * 累计收款比例
     */
    @ApiModelProperty(value = "累计收款比例")
    private String collectionRatio;

    /**
     * 当前款项名称
     */
    @ApiModelProperty(value = "当前款项金额")
    private BigDecimal currentPaymentMoney;

    /**
     * 当前款项名称
     */
    @ApiModelProperty(value = "当前款项名称")
    private String currentPaymentName;

    /**
     * 收款滞后天数
     */
    @ApiModelProperty(value = "收款滞后天数")
    private String collectionDelayDays;

    /**
     * 预警等级
     */
    @ApiModelProperty(value = "预警等级")
    private String warningLevel;

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
    private Long customerId;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String customerName;

    /**
     * 项目销售人员ID
     */
    @ApiModelProperty(value = "项目销售人员ID")
    private Long salesmanUserId;

    /**
     * 项目销售人员
     */
    @ApiModelProperty(value = "项目销售人员")
    private String salesmanUserName;

    /**
     * 项目销售人员上级ID
     */
    @ApiModelProperty(value = "项目销售人员上级ID")
    private Long salesmanLeaderUserId;

    /**
     * 项目经理人员ID
     */
    @ApiModelProperty(value = "项目经理人员ID")
    private Long managerUserId;

    /**
     * 项目经理人员姓名
     */
    @ApiModelProperty(value = "项目经理人员姓名")
    private String managerUserName;

    /**
     * 项目经理人员上级ID
     */
    @ApiModelProperty(value = "项目经理人员上级ID")
    private Long managerLeaderUserId;

    /**
     * 售前经理ID
     */
    @ApiModelProperty(value = "售前经理ID")
    private Long preSaleUserId;

    /**
     * 售前经理上级ID
     */
    @ApiModelProperty(value = "售前经理上级ID")
    private Long preSaleLeaderUserId;

    /**
     * 项目区域主管ID
     */
    @ApiModelProperty(value = "项目区域主管ID")
    private Long headUserId;

    /**
     * 项目区域主管姓名
     */
    @ApiModelProperty(value = "项目区域主管姓名")
    private String headUserName;

    /**
     * 项目市场总监ID
     */
    @ApiModelProperty(value = "项目市场总监ID")
    private Long commissionerUserId;

    /**
     * 项目市场总监姓名
     */
    @ApiModelProperty(value = "项目市场总监姓名")
    private String commissionerUserName;

    /**
     * 归属主体/主体名称
     */
    @ApiModelProperty(value = "归属主体/主体名称")
    private String attributableSubject;

    /**
     * 一级部门
     */
    @ApiModelProperty(value = "一级部门")
    private Long firstDepartmentId;

    /**
     * 一级部门
     */
    @ApiModelProperty(value = "一级部门")
    private String firstDepartment;

    /**
     * 二级部门
     */
    @ApiModelProperty(value = "二级部门")
    private Long secondDepartmentId;

    /**
     * 二级部门
     */
    @ApiModelProperty(value = "二级部门")
    private String secondDepartment;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remarks;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * delFlag
     */
    @TableLogic(value = "0", delval = "1")
    @ApiModelProperty(value = "delFlag")
    private String delFlag;

    /**
     * 所属租户
     */
    @ApiModelProperty(value = "所属租户", hidden = true)
    private Long tenantId;
}