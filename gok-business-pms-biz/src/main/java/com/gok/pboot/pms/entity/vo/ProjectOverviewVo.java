package com.gok.pboot.pms.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * 项目概览Vo
 *
 * <AUTHOR>
 * @date 2024/01/23
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProjectOverviewVo {

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 是否是内部项目
     */
    private Boolean internalFlag;

    /**
     * 项目状态
     */
    private Integer projectStatus;

    /**
     * 项目状态txt
     */
    private String projectStatusTxt;

    /**
     * 预估总人天
     */
    private String estimatedTotalManDays;

    /**
     * 总任务数量
     */
    private Integer totalTaskNumber;

    /**
     * 总人天数
     */
    private String totalManDays;

    /**
     * 创建时间
     */
    private LocalDate ctime;

}
