package com.gok.pboot.pms.entity.dto;

import lombok.Data;

import java.time.LocalDate;

/**
 * @menu
 * @author: suzy
 * @create date: 2023/4/4
 * @Description:日报审核（项目维度）-查看日报-统计 请求实体
 **/
@Data
public class DimensionViewTotalDto {
    /**
     * 项目id
     */
    private Long projectId;
    /**
     * 任务名称
     */
    private String taskName;
    /**
     * 人员名称
     */
    private String userName;
    /**
     * 审批状态
     */
    private Integer auditStatus;

    /**
     * 日期开始时间
     */
    private LocalDate startTime;

    /**
     * 日期结束时间
     */
    private LocalDate endTime;
    /**
     * 是否查询归档数据（0：不查询、1：查询）
     */
    private Integer filing;
}
