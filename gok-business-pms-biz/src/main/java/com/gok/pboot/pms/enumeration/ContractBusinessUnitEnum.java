package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 业务板块枚举
 *
 * <AUTHOR>
 * @date 13/12/2023
 */
@Getter
@AllArgsConstructor
public enum ContractBusinessUnitEnum implements ValueEnum<Integer> {

    /**
     * 人力结算（订单结算）
     */
    GENERAL_ENTERPRISE_MANAGEMENT_DIGITALIZATION(0, "通用企业管理数字化"),

    /**
     * 项目结算（固定总价）
     */
    PROFESSIONAL_MAIN_BUSINESS_DIGITAL(1, "专业主营业务数字化"),

    /**
     * 项目结算（固定总价）
     */
    EDUCATION_AND_TRAINING(2, "教育培训");

    private final Integer value;

    private final String name;
}
