package com.gok.pboot.pms.entity.dto;

import com.gok.pboot.pms.common.base.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @desc 日报审核 分页DTO
 * @createTime 2023/2/15 10:31
 */
@Deprecated
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@ApiModel("日报审核（项目维度）分页DTO")
public class DailyReviewPageDTO extends PageRequest {
    /**
     * 任务id
     */
    @ApiModelProperty("任务id")
    private Long projectId;
    /**
     * 项目名称
     */
    @ApiModelProperty("项目名称")
    private String projectName;
    /**
     * 开始时间
     */
    @ApiModelProperty("开始时间")
    private String startTime;
    /**
     * 结束时间
     */
    @ApiModelProperty("结束时间")
    private String endTime;
    /**
     * 任务名称
     */
    @ApiModelProperty("任务名称")
    private String taskName;
    /**
     * 审批状态 0待审批 1已审批
     */
    @ApiModelProperty("审批状态")
    private Integer auditStatus;
    /**
     * 人员名称
     */
    @ApiModelProperty("人员名称")
    private String username;
    /**
     * 用户id
     */
    @ApiModelProperty("用户id")
    private Long userId;
    /**
     * 用户id集合
     */
    @ApiModelProperty("用户id集合")
    private List<Long> userIds;
    /**
     * 是否查询归档数据（0：不查询、1：查询）
     */
    private Integer filing;
}
