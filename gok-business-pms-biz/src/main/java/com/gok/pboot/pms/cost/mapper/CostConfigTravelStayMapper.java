package com.gok.pboot.pms.cost.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.cost.entity.domain.CostConfigTravelStay;
import com.gok.pboot.pms.cost.entity.vo.CostConfigTravelStayVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 差旅住宿标准配置 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Mapper
public interface CostConfigTravelStayMapper extends BaseMapper<CostConfigTravelStay> {

    /**
     * 按最大版本编号获取差旅住宿标准配置
     *
     * @param versionId 版本 ID
     * @return {@link List }<{@link CostConfigTravelStayVO }>
     */
    List<CostConfigTravelStayVO> getTravelStaysByVersionId(@Param("versionId") Long versionId);

}
