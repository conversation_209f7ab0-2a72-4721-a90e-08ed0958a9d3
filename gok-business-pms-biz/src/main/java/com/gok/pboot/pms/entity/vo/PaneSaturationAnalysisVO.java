package com.gok.pboot.pms.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @desc 工时饱和度统计
 * @createTime 2023/2/21 14:56
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PaneSaturationAnalysisVO {
    /**
     * 审核工时
     */
    @ApiModelProperty(value = "审核工时，人/天")
    private BigDecimal auditTime;
    /**
     * 滞后提交人天总数
     */
    @ApiModelProperty(value = "滞后提交人天总数，人/天")
    private BigDecimal delaySubmit;

    private final static PaneSaturationAnalysisVO EMPTY =
            new PaneSaturationAnalysisVO(BigDecimal.ZERO, BigDecimal.ZERO);

    public static PaneSaturationAnalysisVO empty() {
        return EMPTY;
    }
}
