package com.gok.pboot.pms.service.impl;

import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.entity.domain.ProjectStakeholderMember;
import com.gok.pboot.pms.entity.domain.ProjectTaskeUser;
import com.gok.pboot.pms.entity.vo.PmsInfoVo;
import com.gok.pboot.pms.enumeration.ProjectTaskRoleEnum;
import com.gok.pboot.pms.enumeration.RoleTypeEnum;
import com.gok.pboot.pms.mapper.ProjectStakeholderMemberMapper;
import com.gok.pboot.pms.mapper.ProjectTaskeUserMapper;
import com.gok.pboot.pms.service.PmsService;
import com.google.common.collect.ImmutableList;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * PMS相关服务
 *
 * <AUTHOR>
 * @version 1.3.2
 */
@Service
@RequiredArgsConstructor
public class PmsServiceImpl implements PmsService {

    private final ProjectTaskeUserMapper projectTaskeUserMapper;

    private final ProjectStakeholderMemberMapper projectStakeholderMemberMapper;

    @Override
    public PmsInfoVo getInfo() {
        Set<Long> memberProjectIds, chargeProjectIds, memberTaskIds, chargeTaskIds;
        Long userId = SecurityUtils.getUser().getId();
        Map<Integer, List<ProjectStakeholderMember>> roleTypeAndProjectMemberMap =
                projectStakeholderMemberMapper.findByMemberId(userId)
                        .stream()
                        .collect(Collectors.groupingBy(ProjectStakeholderMember::getRoleType));
        Map<Integer, List<ProjectTaskeUser>> taskRoleAndProjectTaskUserMap =
                projectTaskeUserMapper.findByUserId(userId)
                        .stream()
                        .collect(Collectors.groupingBy(ProjectTaskeUser::getTaskRole));

        if (roleTypeAndProjectMemberMap.isEmpty() || taskRoleAndProjectTaskUserMap.isEmpty()) {
            return PmsInfoVo.EMPTY;
        }
        chargeProjectIds = Stream.of(
                roleTypeAndProjectMemberMap.getOrDefault(
                        RoleTypeEnum.PROJECT_SALES_MANAGER.getValue(), ImmutableList.of()
                ),
                roleTypeAndProjectMemberMap.getOrDefault(
                        RoleTypeEnum.PROJECT_PRE_SALES_MANAGER.getValue(), ImmutableList.of()
                ),
                roleTypeAndProjectMemberMap.getOrDefault(
                        RoleTypeEnum.PROJECT_MANAGER.getValue(), ImmutableList.of()
                )
        ).flatMap(Collection::stream).map(ProjectStakeholderMember::getProjectId).collect(Collectors.toSet());
        memberProjectIds = Stream.of(
                roleTypeAndProjectMemberMap.getOrDefault(
                        RoleTypeEnum.PROJECT_MEMBER.getValue(), ImmutableList.of()
                ),
                roleTypeAndProjectMemberMap.getOrDefault(
                        RoleTypeEnum.PROJECT_OPERATIONS_ASSISTANT.getValue(), ImmutableList.of()
                )
        ).flatMap(Collection::stream).map(ProjectStakeholderMember::getProjectId).collect(Collectors.toSet());
        memberProjectIds.addAll(chargeProjectIds);
        chargeTaskIds = taskRoleAndProjectTaskUserMap.getOrDefault(
                ProjectTaskRoleEnum.LEADER.getValue(), ImmutableList.of()
        ).stream().map(ProjectTaskeUser::getTaskId).collect(Collectors.toSet());
        memberTaskIds = taskRoleAndProjectTaskUserMap.getOrDefault(
                ProjectTaskRoleEnum.MEMBER.getValue(), ImmutableList.of()
        ).stream().map(ProjectTaskeUser::getTaskId).collect(Collectors.toSet());
        chargeTaskIds.addAll(memberTaskIds);

        return PmsInfoVo.of(memberProjectIds, chargeProjectIds, memberTaskIds, chargeTaskIds);
    }
}
