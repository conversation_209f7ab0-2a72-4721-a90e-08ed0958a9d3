package com.gok.pboot.pms.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.bcp.upms.vo.SysMenuVo;
import com.gok.pboot.common.security.annotation.Inner;
import com.gok.pboot.pms.common.base.*;
import com.gok.pboot.pms.entity.dto.RoleProjectPageDto;
import com.gok.pboot.pms.entity.vo.*;
import com.gok.pboot.pms.service.IProjectService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @description 项目 前端控制器
 * @menu PMS业务模块-项目
 * @since 2022-08-19
 */
@Slf4j
@RestController
@RequestMapping("/project")
public class ProjectController extends BaseController {

    private final IProjectService service;

    @Autowired
    public ProjectController(IProjectService service) {
        this.service = service;
    }

    /**
     * @create by yzs at 2023/4/20
     * @description:分页 查询项目
     * @param: pageRequest
     * @param: request
     * @return: com.gok.pboot.pms.common.base.ApiResult<com.baomidou.mybatisplus.extension.plugins.pagination.Page < com.gok.pboot.pms.entity.vo.ProjectVO>>
     */
    @Deprecated
    @PreAuthorize("@pms.hasPermission('PROJECT_MANAGE_LIST')")
    @GetMapping("/findPage")
    public ApiResult<Page<ProjectVO>> findPage(PageRequest pageRequest, HttpServletRequest request) {
        return success(service.findPage2(pageRequest, PropertyFilters.get(request)));
    }


    /**
     * 查询项目-总计
     *
     * @param id 项目id
     * @return {@link ApiResult}
     */
    @GetMapping("/count/{id}")
    public ApiResult<ProjectInfoVO> getByIdCount(@PathVariable("id") Long id) {
        return service.getByIdCount(id);
    }

    /**
     * 工时填报查询所有当前用户可用的所有项目
     *
     * @return com.gok.pboot.pms.common.base.ApiResult<java.util.List < com.gok.pboot.pms.entity.vo.ProjectInDailyPaperEntryVO>>
     * <AUTHOR>
     * @date 2022/8/25 11:44
     */
    @Deprecated
    @GetMapping("/currentForDailyPaperEntry")
    public ApiResult<List<ProjectWithTasksInDailyPaperEntryVO>> currentForDailyPaperEntry(LocalDate date) {
        return ApiResult.success(service.findByCurrUserIdForDailyPaperEntry(date));
    }

    /**
     * 工时填报查询所有当前用户可用的所有项目（新、旧任务）
     *
     * @return com.gok.pboot.pms.common.base.ApiResult<java.util.List < com.gok.pboot.pms.entity.vo.ProjectInDailyPaperEntryVO>>
     * <AUTHOR>
     * @date 2022/8/25 11:44
     */
    @Deprecated
    @GetMapping("/currentForDailyPaperEntryMix")
    public ApiResult<List<ProjectWithTasksInDailyPaperEntryVO>> currentForDailyPaperEntryMix(LocalDate date) {
        return ApiResult.success(service.findByCurrUserIdForDailyPaperEntryMix(date));
    }

    /**
     * 工时填报查询所有当前用户可用的所有项目（新任务）
     *
     * @return com.gok.pboot.pms.common.base.ApiResult<java.util.List < com.gok.pboot.pms.entity.vo.ProjectInDailyPaperEntryVO>>
     * <AUTHOR>
     * @date 2022/8/25 11:44
     */
    @Deprecated
    @GetMapping("/currentForDailyPaperEntryNew")
    public ApiResult<List<ProjectWithTasksInDailyPaperEntryVO>> currentForDailyPaperEntryNew(LocalDate date) {
        return ApiResult.success(service.findByCurrUserIdForDailyPaperEntryNew(date));
    }

    /**
     * 工时填报查询所有当前用户可用的所有项目（新旧任务统一，去除旧任务标识）
     *
     * @param date 填报日期
     * @return {@link ApiResult}<{@link List}<{@link ProjectWithTasksInDailyPaperEntryUnifyVO}>>
     */
    @GetMapping("/currentForDailyPaperEntryUnify")
    public ApiResult<List<ProjectWithTasksInDailyPaperEntryUnifyVO>> currentForDailyPaperEntryUnify(LocalDate date) {
        return ApiResult.success(service.findCurrentForDailyPaperEntryUnify(date));
    }

    /**
     * 每10分钟更新更新了数据的项目成员
     *
     * @return {@link ApiResult}
     * @para 0:更新近期数据，1：全量更新
     */
    @Inner
    @GetMapping("/projectTaskPersonnel/job")
    public ApiResult<Void> filingTask(String para) {
        service.projectTaskPersonnel(para);

        return ApiResult.success(null);
    }

    /**
     * @create by yzs at 2023/4/20
     * @description:子任务管理，项目工时汇总通过不通权限查询项目
     * @param: page
     * @return: com.gok.pboot.pms.common.base.ApiResult<com.baomidou.mybatisplus.extension.plugins.pagination.Page < com.gok.pboot.pms.entity.vo.ProjectVO>>
     */
    @PreAuthorize("@pms.hasPermission('PROJECT_MANAGE_LIST')")
    @PostMapping("/findByPermissions")
    public ApiResult<Page<ProjectVO>> findByPermissions(Page page, @RequestBody RoleProjectPageDto dto) throws ClassNotFoundException, IllegalAccessException, InstantiationException {
        return success(service.findByPermissions(page, dto));
    }

    /**
     * 每隔10分钟更新项目的销售人员赋予销售角色
     *
     * @return {@link ApiResult}
     * @para 0:更新近期数据，1：全量更新
     */
    @GetMapping("/syncOaSalesTask/job")
    @Inner
    public R syncOaSalesTask(String para) {
        return service.syncOaSalesTask(para);
    }

    /**
     * 查询已关注项目
     *
     * @param
     * @return {@link ApiResult<List<ProjectAttentionVO>>}
     */
    @GetMapping("/findAttentionProject")
    public ApiResult<Page<ProjectAttentionVO>> findAttentionProject(PageRequest pageRequest, HttpServletRequest request) {
        return ApiResult.success(service.findAttentionProject(pageRequest, PropertyFilters.get(request)));
    }

    /**
     * 新增关注项目（先清空之前关注的，在批量关注）
     *
     * @param projectIds 项目id
     * @return {@link ApiResult<Long>} 用户id
     */
    @PostMapping("/addAttentionProject")
    public ApiResult<Long> attentionProject(@RequestBody List<Long> projectIds) {
        return ApiResult.success(service.attentionProject(projectIds));
    }

    /**
     * 关注目标项目
     *
     * @param projectId 项目id
     * @return {@link ApiResult<Boolean>} 关注结果
     */
    @PostMapping("/addAttentionProject/{projectId}")
    public ApiResult<Boolean> attentionProject(@PathVariable("projectId") Long projectId) {
        return ApiResult.success(service.attentionProject(projectId));
    }

    /**
     * 取消关注项目
     *
     * @param projectId 项目id
     * @return {@link ApiResult<Long>} 用户id
     */
    @DeleteMapping("/removeAttentionProject/{projectId}")
    public ApiResult removeAttentionProject(@PathVariable("projectId") Long projectId) {
        service.removeAttentionProject(projectId);
        return ApiResult.success("请求成功");
    }


    /**
     * 查询已关注项目
     *
     * @return {@link ApiResult<List<ProjectVO>>}
     */
    @GetMapping("/getAttentionProjectList")
    public ApiResult<List<ProjectVO>> getAttentionProjectList() {
        return ApiResult.success(service.getAttentionProjectList());
    }


    /**
     * 通过Id 查询项目基础信息
     *
     * @param id 项目id
     * @return {@link R}<{@link ProjectBaseInfoVO}>
     */
    @PreAuthorize("@pms.hasPermission('PROJECT_MANAGE_LIST')")
    @GetMapping("/baseInfo/{id}")
    public ApiResult<ProjectBaseInfoVO> getProjectBaseInfoById(@PathVariable("id") Long id) {
        return service.getProjectBaseInfoById(id);
    }

    /**
     * 通过Id 查询项目基础信息
     *
     * @param id 项目id
     * @return {@link R}<{@link ProjectBaseInfoVO}>
     */
    @PreAuthorize("@pms.hasPermission('PROJECT_MANAGE_LIST_INSIDE')")
    @GetMapping("/inner/baseInfo/{id}")
    public ApiResult<ProjectBaseInfoVO> getProjectBaseInfoInnerById(@PathVariable("id") Long id) {
        return service.getProjectBaseInfoById(id);
    }

    /**
     * 获取外部项目菜单权限
     *
     * @param request 请求对象
     * @return {@link ApiResult}<{@link List}<{@link SysMenuVo}>>
     * customParam filter_L_projectId 传入的项目id
     * customParam filter_S_permission 传入的权限标识
     * customParam filter_S_menuType 传入的菜单类型（0菜单 1按钮，9系统）
     */
    @GetMapping("/getMenuAuthority")
    @PreAuthorize("@pms.hasPermission('PROJECT_MANAGE_DETAIL')")
    public ApiResult<List<SysMenuVo>> getMenuAuthority(HttpServletRequest request) {
        return ApiResult.success(service.getMenuAuthority(request));
    }

    /**
     * 获取内部部项目菜单权限
     *
     * @param request 请求对象
     * @return {@link ApiResult}<{@link List}<{@link SysMenuVo}>>
     * customParam filter_L_projectId 传入的项目id
     * customParam filter_S_permission 传入的权限标识
     * customParam filter_S_menuType 传入的菜单类型（0菜单 1按钮，9系统）
     */
    @GetMapping("/inner/getMenuAuthority")
    @PreAuthorize("@pms.hasPermission('PROJECT_MANAGE_DETAIL_INSIDE')")
    public ApiResult<List<SysMenuVo>> getMenuAuthorityInner(HttpServletRequest request) {
        return ApiResult.success(service.getMenuAuthority(request));
    }

}