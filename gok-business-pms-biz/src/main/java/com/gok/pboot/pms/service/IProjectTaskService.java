package com.gok.pboot.pms.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.entity.dto.ProjectTaskDTO;
import com.gok.pboot.pms.entity.dto.ProjectTaskTimeDTO;
import com.gok.pboot.pms.entity.vo.*;

import java.util.List;
import java.util.Map;

/**
 * 项目任务管理服务
 *
 * <AUTHOR>
 * @date 2023/8/19
 */
public interface IProjectTaskService {
    /**
     * 查询列表
     * @param filter 请求参数
     * @customParam filter_L_projectId 传入的项目id
     * @customParam filter_L_state 传入的状态参数，不传默认查所有
     * @customParam filter_S_title 模糊查询的任务标题
     * @customParam filter_S_managerUserName 模糊查询的负责人姓名
     * @return {@link List<ProjectTaskVO>}
     */
    List<ProjectTaskVO> findList(Map<String, Object> filter);

    /**
     * 创建任务
     * @param request 请求参数
     * @return {@link Long}
     */
    Long save(ProjectTaskDTO request);

    /**
     * 编辑任务
     * @param request 请求参数
     * @return {@link Long}
     */
    Long update(ProjectTaskDTO request);

    /**
     * 删除任务
     * @param id 传入的任务id
     * @return {@link Boolean}
     */
    Boolean delete(Long id);

    /**
     * 根据ID查询详情
     * @param id 任务ID
     * @return details
     */
    ProjectTaskDetailsVO findDetailsById(Long id);

    /**
     * 根据任务id查询单个任务详情（编辑任务时使用）
     * @param id 任务id
     * @return {@link ProjectTaskEditVo}
     */
    ProjectTaskEditVo findOne(Long id);

    /**
     * 项目下：任务状态统计接口
     * @param filter 请求参数
     * @customParam filter_L_projectId 传入的项目id
     * @customParam filter_S_title 模糊查询的任务标题
     * @customParam filter_S_managerUserName 模糊查询的负责人姓名
     * @return {@link ProjectTaskStateVO} 任务状态统计信息
     */
    ProjectTaskStateVO findStateCount(Map<String, Object> filter);

    /**
     * 项目下：任务列表分页接口（树结构）
     * @param pageRequest 分页请求参数
     * @param filter 请求参数
     * @return {@link Page<ProjectTaskPageVO>}
     */
    Page<ProjectTaskPageVO> findPage(PageRequest pageRequest, Map<String, Object> filter);

    /**
     * 查询负责人的项目数量
     * @return {@link ApiResult<Integer>}
     */
    ApiResult<Integer> findChargeTaskNum();

    Page<ProjectTaskOfChargeVO> findPageOfCharge(PageRequest pageRequest, Map<String, Object> filter);

    Long updateTaskTime(ProjectTaskTimeDTO dto);

    Long rollbackEndTime(Long id);

    /**
     * 任务即将结束通知
     */
    void deadLineNotify();

    /**
     * 全量判断并更新任务状态（未开始->已逾期）
     */
    void updateStatusToDelay();
}
