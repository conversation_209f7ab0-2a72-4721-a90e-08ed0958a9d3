package com.gok.pboot.pms.entity.dto;

import com.gok.pboot.pms.common.base.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @desc 复用交付工时审核分页DTO
 * @createTime 2023/2/15 10:40
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@ApiModel("复用交付工时审核分页DTO")
public class ReuseDeliveryTimeReviewPageDTO extends PageRequest {
    /**
     * 任务名称
     */
    @ApiModelProperty("任务名称")
    private String projectName;
    /**
     * 开始月份
     */
    @ApiModelProperty("开始月份")
    private Integer startMonth;
    /**
     * 结束月份
     */
    @ApiModelProperty("结束月份")
    private Integer endMonth;
    /**
     * 人员名称
     */
    @ApiModelProperty("人员名称")
    private String personName;

}
