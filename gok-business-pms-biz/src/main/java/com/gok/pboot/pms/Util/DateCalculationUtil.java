package com.gok.pboot.pms.Util;

import java.util.Date;

/**
 * 日期计算相关工具
 *
 * <AUTHOR>
 * @date 2023/11/22
 */
public class DateCalculationUtil {

    /**
     * 传入日期与当前日期，计算差值
     * @param date 日期
     * @return 0天显示为今天，1天显示为昨天，2天显示为前天，>=3天显示为xx天前
     */
    public static String calculateDateDiff(Date date) {
        Date currentDate = new Date();
        long diffInMillies = Math.abs(currentDate.getTime() - date.getTime());
        long diffInDays = diffInMillies / (1000 * 60 * 60 * 24);

        if (diffInDays == 0) {
            return "今天";
        } else if (diffInDays == 1) {
            return "昨天";
        } else if (diffInDays == 2) {
            return "前天";
        } else {
            return diffInDays + "天前";
        }
    }
}
