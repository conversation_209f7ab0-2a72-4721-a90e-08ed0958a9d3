/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 */

package com.gok.pboot.pms.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 任务-用户关联表
 *
 * <AUTHOR> generator
 * @date 2023-08-18 10:07:30
 */
@Data
@TableName("project_task_user")
@EqualsAndHashCode(callSuper = true)
public class ProjectTaskUser extends BeanEntity<Long> {

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户姓名
     */
    private String userName;

    public static ProjectTaskUser save(Long userId,String userName,Long taskId){
        ProjectTaskUser result = new ProjectTaskUser();

        result.setTaskId(taskId);
        result.setUserId(userId);
        // 用户名
        result.setUserName(userName);
        return result;
    }

}
