package com.gok.pboot.pms.common.base;

import cn.hutool.core.lang.tree.TreeNode;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.HashMap;
import java.util.Map;

/**
 * - 树结构实体 -
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public abstract class TreeEntity<T> extends BaseEntity<T>{

    @JsonSerialize(using = ToStringSerializer.class)
    private T parentId;

    private String name;

    public TreeEntity() {
    }

    public TreeEntity(T parentId, String name) {
        this.parentId = parentId;
        this.name = name;
    }

    public T getParentId() {
        return parentId;
    }

    public void setParentId(T parentId) {
        this.parentId = parentId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public TreeNode<T> toTreeNode(){
        return toTreeNode(new HashMap<>());
    }

    public TreeNode<T> toTreeNode(Map<String, Object> extra){
        TreeNode<T> treeNode = new TreeNode<>(id, parentId, name, null);

        treeNode.setExtra(extra);

        return treeNode;
    }
}
