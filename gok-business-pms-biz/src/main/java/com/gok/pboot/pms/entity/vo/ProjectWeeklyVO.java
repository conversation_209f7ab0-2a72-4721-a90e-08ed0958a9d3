package com.gok.pboot.pms.entity.vo;


import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 项目周报表Vo
 *
 * <AUTHOR>
 * @LocalDateTime 2023-07-11 17:05:26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectWeeklyVO {

    /**
     * ID id
     */
    @ExcelIgnore
    private Long id;
    /**
     * 项目id
     */
    @ExcelIgnore
    private Long projectId;

    /**
     * 项目编号
     */
    @ExcelIgnore
    private String itemNo;

    /**
     * 立项日期
     */
    @ExcelIgnore
    private String projectDate;

    /**
     * 业务归属一级部门id
     */
    @ExcelIgnore
    private Long firstLevelDepartmentId;

    /**
     * 业务归属一级部门
     */
    @ExcelIgnore
    private String firstLevelDepartment;

    /**
     * 项目状态
     */
    @ExcelIgnore
    private String projectStatus;
    /**
     * 项目状态值
     */
    @ExcelIgnore
    private String projectStatusName;

    /**
     * 汇报周期
     */
    @ExcelProperty({"汇报周期"})
    @ColumnWidth(30)
    private String reportStartEnd;
    /**
     * '项目名称'
     */
    @ExcelIgnore
    private String projectName;
    /**
     * '业务归属部门'
     */
    @ExcelIgnore
    private String projectDepartment;
    /**
     * 汇报周期-开始
     */
    @ExcelIgnore
    private LocalDate reportStart;
    /**
     * 汇报周期-结束
     */
    @ExcelIgnore
    private LocalDate reportEnd;

    /**
     * 汇报人id
     */
    @ExcelIgnore
    private Long reportUserId;
    /**
     * 汇报人
     */
    @ExcelProperty({"汇报人"})
    @ColumnWidth(20)
    private String reportUser;
    /**
     * 本周进展情况
     */
    @ExcelProperty({"本周进展情况"})
//    @ContentStyle(wrapped = true)
    @ColumnWidth(50)
    private String currentWorkProgress;
    /**
     * 当前项目进度
     */
    @ExcelProperty({"当前进度"})
    @ColumnWidth(20)
    private String currentProgress;
    /**
     * 当期新增工时（人天）
     */
    @ExcelProperty({"当期新增工时（人天）"})
    @ColumnWidth(20)
    private BigDecimal currentHours;
    /**
     * 累计工时（人天）
     */
    @ExcelProperty({"累计工时（人天）"})
    @ColumnWidth(20)
    private BigDecimal totalHours;
    /**
     * 下周工作计划
     */
    @ExcelProperty({"下周工作计划"})
//    @ContentStyle(wrapped = true)
    @ColumnWidth(50)
    private String nextWorkPlan;
    /**
     * 需配合支撑事项
     */
    @ExcelProperty({"需配合支撑事项"})
//    @ContentStyle(wrapped = true)
    @ColumnWidth(50)
    private String needSupportItem;
    /**
     * 文件id集合
     */
    @ExcelIgnore
    private String docIds;
    /**
     * 文件名集合
     */
    @ExcelIgnore
    private String docNames;

    /**
     * 项目风险
     */
    @ExcelProperty({"项目风险"})
//    @ContentStyle(wrapped = true)
    @ColumnWidth(50)
    private String weeklyRisk;

    /**
     * 周报风险id集合
     */
    @ExcelIgnore
    private String weeklyRiskIds;

    /**
     * 项目风险基本信息集合
     */
    @ExcelIgnore
    private List<ProjectRiskBaseVo> projectRiskBaseList;

    /**
     * 备 注
     */
    @ExcelProperty({"备注"})
//    @ContentStyle(wrapped = true)
    @ColumnWidth(50)
    private String remark;

    /**
     * 是否已读（1已读，0未读）
     */
    @ExcelIgnore
    private Integer isRead;

    /**
     * 创建时间
     */
    @ExcelIgnore
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String ctime;

    /**
     * 修改时间
     */
    @ExcelIgnore
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String mtime;

    /**
     * 是否内部项目（1=是，2=否，""）
     *
     * @see com.gok.pboot.pms.enumeration.IsNoInternalProjectEnum
     */
    @ExcelIgnore
    private Integer isNotInternalProject;

}
