package com.gok.pboot.pms.cost.entity.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 异常工时列表VO
 *
 * <AUTHOR>
 * @date 2024/04/16
 */
@Data
public class CostTaskDailyPaperAbnormalVO {
    
    /**
     * 工单ID
     */
    @ExcelIgnore
    private Long taskId;

    /**
     * 工单名称
     */
    @ExcelProperty("工单名称")
    private String taskName;

    /**
     * 上级工单id
     */
    @ExcelIgnore
    private Long parentId;

    /**
     * 工单级别
     */
    @ExcelIgnore
    private Integer taskLevel;

    /**
     * 项目ID
     */
    @ExcelIgnore
    private Long projectId;
    
    /**
     * 项目名称
     */
    @ExcelProperty("项目名称")
    private String projectName;
    
    /**
     * 工单类别
     */
    @ExcelIgnore
    private Integer taskCategory;
    
    /**
     * 工单类别名称
     */
    @ExcelProperty("工单类别")
    private String taskCategoryTxt;
    
    /**
     * 描述
     */
    @ExcelProperty("描述")
    private String taskDesc;
    
    /**
     * 工单负责人ID
     */
    @ExcelIgnore
    private Long managerId;
    
    /**
     * 工单负责人
     */
    @ExcelProperty("工单负责人")
    private String managerName;
    
    /**
     * 工单状态
     */
    @ExcelIgnore
    private Integer taskStatus;
    
    /**
     * 工单状态名称
     */
    @ExcelProperty("工单状态")
    private String taskStatusTxt;
    
    /**
     * 待审核工时
     */
    @ExcelProperty("待审核工时")
    private BigDecimal pendingReviewHours;

    /**
     * 最早提交时间
     */

    @ExcelProperty("最早提交时间")
    private LocalDateTime earliestSubmissionDate;


    /**
     * 审核人ID
     */
    @ExcelIgnore
    private Long reviewerId;
    
    /**
     * 审核人
     */
    @ExcelProperty("审核人")
    private String reviewer;
    
    /**
     * 售前经理ID
     */
    @ExcelIgnore
    private Long preSalesManagerId;
    
    /**
     * 售前经理
     */
    @ExcelProperty("售前经理")
    private String preSalesManager;

    @ExcelIgnore
    private Integer abnormalType;


    /**
     * 异常类型
     */
    @ExcelProperty("异常类型")
    private String abnormalTypeTxt;
} 