package com.gok.pboot.pms.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.entity.domain.ProjectBusinessMilestones;
import com.gok.pboot.pms.entity.vo.ProjectDataConfirmedVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 项目商务里程碑（OA同步）
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-02-23 10:46:40
 */
@Mapper
public interface ProjectBusinessMilestonesMapper extends BaseMapper<ProjectBusinessMilestones> {

    /**
     * 查询项目评价所需里程碑数据
     *
     * @param projectIds 项目ID集合[]
     * @return
     */
    List<ProjectBusinessMilestones> selEvalProjectData(@Param("projectIds") List<Long> projectIds);

    /**
     * 查询项目确认数据
     *
     * @param projectId 项目ID
     * @return
     */
    ProjectDataConfirmedVO selConfirmedData(@Param("projectId") Long projectId);

}
