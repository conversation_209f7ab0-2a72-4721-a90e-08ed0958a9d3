package com.gok.pboot.pms.entity.dto;

import com.gok.pboot.pms.enumeration.FilingTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @desc 日报对应的归档信息DTO
 * @createTime 2023/2/24 14:09
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class DailyPaperEntryFilingDTO {
    /**
     * 日报条目id
     */
    private Long dailyPaperEntryId;
    /**
     * 日报id
     */
    private Long dailyPaperId;
    /**
     * 填报日期
     */
    private LocalDate submissionDate;
    /**
     * 归档状态（0=未归档，1=已归档）
     * {@link FilingTypeEnum}
     */
    private Integer filed;

    /**
     * 正常工时
     */
    private BigDecimal normalHours;

    /**
     * 加班工时
     */
    private BigDecimal addedHours;
}
