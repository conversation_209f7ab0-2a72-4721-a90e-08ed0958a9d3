package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 流程类型枚举
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Getter
@AllArgsConstructor
public enum ProcessTypeEnum implements ValueEnum<Integer> {

    /**
     * XM-05 项目毛利测算
     */
    XM_FIVE(1, "XM-05 项目毛利测算"),

    /**
     * 项目毛利测算变更
     */
    XM_SIX(2, "XM-06 项目毛利测算变更"),

    /**
     * XM-07 合同会签
     */
    XM_SEVEN(3, "XM-07 合同会签"),

    /**
     * XM-08 合同变更/补充
     */
    XM_EIGHT(4, "XM-08 合同变更/补充"),

    /**
     * XM-32 人力外包结算审批
     */
    XM_THIRTY_TWO(5, "XM-32 人力外包结算审批"),

    /**
     * XM-09 开票申请
     */
    XM_NINE(6, "XM-09 开票申请"),

    /**
     * XM-10 发票作废
     */
    XM_TEN(7, "XM-10 发票作废"),

    /**
     * XM-17 项目结项及收入确认申请
     */
    XM_SEVENTEEN(8, "XM-17 项目结项及收入确认申请");

    private final Integer value;

    private final String name;


}
