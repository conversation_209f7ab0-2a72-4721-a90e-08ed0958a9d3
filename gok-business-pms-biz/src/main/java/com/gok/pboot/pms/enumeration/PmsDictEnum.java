package com.gok.pboot.pms.enumeration;

import lombok.Getter;

/**
 * pms字典表枚举
 *
 * <AUTHOR>
 * @date 2023/11/21
 */
@Getter
public enum PmsDictEnum implements ValueEnum<String> {

    /**
     * "商机阶段0、商机阶段1、商机阶段2、商机阶段3、商机阶段4"
     */
    BUSINESS_STAGE("business_stage", "商机阶段"),
    /**
     * 项目未启动、需求方案编制、可研设计、招投标
     */
    BUSINESS_MILESTONE("business_milestone", "项目里程碑"),
    /**
     * 战略部署类、积极开拓类、潜力探索类、被动展业类、其他
     */
    CUSTOMER_GRADE("customer_grade", "客户分级"),
    /**
     * 客户行业，具有一级、二级字典
     */
    CUSTOMER_INDUSTRY("customer_industry", "客户行业"),
    /**
     * 内部项目、外部项目
     */
    IS_NOT_INTERNAL_PROJECT("is_not_internal_project", "是否内部项目"),
    /**
     * 通用企业管理数字化、专业主营业务数字化、教育培训
     */
    BUSINESS_MODULE("business_module", "业务板块"),
    /**
     * 需要招投标、不需要招投标
     */
    IS_NOT_NEED_BIDDING("is_not_need_bidding", "是否需要招投标"),
    /**
     * 国科母公司及所有子公司名称
     */
    CONTRACT_ENTITY("contract_entity", "签约主体"),
    /**
     * 产业服务、教育服务
     */
    INCOME_TYPE("income_type", "收入类型"),
    /**
     * ICT集成、综合运维、安全服务、软件开发、ERP交付、数据治理、其他
     */
    TECHNOLOGY_TYPE("technology_type", "技术类型"),
    /**
     * 人力结算、项目结算
     */
    SETTLEMENT_METHOD("settlement_method", "结算方式"),
    /**
     * 劳务派遣、劳务外包、项目外包
     */
    DELIVERY_METHOD("delivery_method", "交付形式"),
    /**
     * 未接触到关键决策人、部分联系且具备合作意愿、1个关键决策人支持、2个或以上关键决策人支持
     */
    SUPPORT_FROM_KEY_DECISION("support_from_key_decision", "关键决策人支持情况"),
    /**
     * 不了解具体需求、需求初步了解、需求基本明确、需求细节清晰
     */
    PROJECT_REQUIREMENT_CLEAR("project_requirement_clear", "项目需求是否明确"),
    /**
     * 不了解预算、了解预算概况、预算已申报、预算已批复
     */
    BUDGET_SITUATION("budget_situation", "预算情况"),
    /**
     * 商机、商机终止、已成交
     */
    BUSINESS_STATUS("business_status", "商机状态"),
    /**
     * 交流方式：面谈、会议、非正式交流
     */
    COMMUNICATION_METHOD("communication_method", "交流方式"),
    /**
     * 时间段：上午、下午、晚上
     */
    TIME_SLOT("time_slot", "时间段"),
    /**
     * 联系人角色：使用负责人、技术负责人、采购负责人、最终决策者、项目经办、其他
     */
    CONTACT_ROLE("contact_role", "联系人角色"),
    /**
     * 内部项目类别：内部管理、内部研发、内部关联交易
     */
    INTERNAL_PROJECT_TYPE("internal_project_type", "内部项目类别"),
    /**
     * 是否涉及外采：是、否
     */
    IS_EXTERNAL_PROCUREMENT("is_external_procurement", "是否涉及外采"),
    /**
     * 采购类别：硬件标品、软件标品、服务采购、工程采购
     */
    PURCHASING_CATEGORIES("purchasing_categories", "采购类别"),
    /**
     * 是否要提前投入
     */
    ADVANCE_INVESTMENT("advance_investment", "是否要提前投入"),
    /**
     * 客户集
     */
    CUSTOMER_COLLECTION("customer_collection", "客户集");

    /**
     * 值
     */
    private final String value;

    /**
     * 名称
     */
    private final String name;

    PmsDictEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

}
