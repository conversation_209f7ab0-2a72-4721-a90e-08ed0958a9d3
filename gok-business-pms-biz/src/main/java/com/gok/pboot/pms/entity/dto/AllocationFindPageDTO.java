package com.gok.pboot.pms.entity.dto;

import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.common.validate.constraint.SelectMonth;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * PMS项目工时分摊表
 *
 * @Auther chenhc
 * @Date 2022-08-24 11:24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AllocationFindPageDTO extends PageRequest {

    /**
     * 用户ID
     */
    private List<Long> userIds;

    /**
     * 用户名字
     */
    private String userName;

    /**
     * 所属项目ID
     */
    private Long projectId;

    /**
     * 选择月份
     */
    @NotBlank(message = "选择月份不能为空")
    @SelectMonth(message = "选择月份格式:【yyyy-MM】")
    private String selectMonth;

    /**
     * 人员状态
     */
    private Integer personnelStatus;

    /**
     * 内外部项目(-1:全部,1:内部项目，2：外部项目)
     */
    private Integer isNotInternalProject;

    /**
     * 工作量单位（1：按天统计，2：按小时统计）
     */
    private Integer workloadType;

    /**
     * 内部项目类型
     */
    private List<Integer> projectTypeList;

    /**
     * 部门条件
     */
    private List<Long> deptIds;

    /**
     * 用户ID集合实习
     */
    private List<Long> userIdsSX;

    /**
     * 用户ID集合正式
     */
    private List<Long> userIdsZS;


}
