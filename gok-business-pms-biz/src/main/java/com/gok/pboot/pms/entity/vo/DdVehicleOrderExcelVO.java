package com.gok.pboot.pms.entity.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 滴滴用车订单Excel导出VO
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
public class DdVehicleOrderExcelVO {

    @ExcelProperty("订单号")
    private String orderNo;

    @ExcelProperty("乘车人工号")
    private String passengerEmployeeNo;

    @ExcelProperty("乘车人姓名")
    private String passengerName;

    @ExcelProperty("乘车人部门名称")
    private String passengerDeptName;

    @ExcelProperty("乘坐时间")
    private LocalDateTime travelTime;

    @ExcelProperty("到达时间")
    private LocalDateTime arrivalTime;

    @ExcelProperty("用车类型")
    private String vehicleType;

    @ExcelProperty("出发城市")
    private String departureCity;

    @ExcelProperty("出发地址")
    private String departureAddress;

    @ExcelProperty("到达城市")
    private String arrivalCity;

    @ExcelProperty("到达地址")
    private String arrivalAddress;

    @ExcelProperty("用车行驶距离(公里)")
    private BigDecimal travelDistance;

    @ExcelProperty("实付金额")
    private BigDecimal companyActualPayment;

    @ExcelProperty("服务费")
    private BigDecimal serviceFee;

    @ExcelProperty("支付类型")
    private String paymentType;

    @ExcelProperty("预订日期")
    private LocalDate bookingDate;

    @ExcelProperty("预订人工号")
    private String bookingEmployeeNo;

    @ExcelProperty("预订人姓名")
    private String bookingEmployeeName;

    @ExcelProperty("预订人部门名称")
    private String bookingDeptName;

    @ExcelProperty("出差申请单号")
    private String businessTripApplicationNo;

    @ExcelProperty("出差事由")
    private String businessTripReason;

    @ExcelProperty("成本中心名称")
    private String costCenterName;

    @ExcelProperty("所属项目名称")
    private String projectName;

    @ExcelProperty("项目编码")
    private String projectCode;

    @ExcelProperty("所属公司名称")
    private String companyName;

    @ExcelProperty("所属账期")
    private String accountingPeriod;
}
