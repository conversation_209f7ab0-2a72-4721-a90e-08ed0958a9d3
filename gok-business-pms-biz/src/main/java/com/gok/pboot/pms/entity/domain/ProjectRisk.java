package com.gok.pboot.pms.entity.domain;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.common.base.BeanEntity;
import com.gok.pboot.pms.entity.dto.ProjectRiskDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Optional;

/**
 * 项目风险表
 *
 * <AUTHOR>
 * @since 2023-07-12
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(ProjectRisk.ALIAS)
public class ProjectRisk extends BeanEntity<Long> {

    public static final String ALIAS = "project_risk";

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 风险描述
     */
    private String description;

    /**
     * 发生概率 (%)
     */
    private BigDecimal probability;

    /**
     * 影响程度  极大、大、中、小（字典设置）
     * {@link com.gok.pboot.pms.enumeration.InfluenceDegreeEnum}
     */
    private Integer influenceDegree;

    /**
     * 风险等级 高 中 低
     * 高风险： >60%发生风险的可能性    中风险： 30-60%发生风险的可能性    低风险：<30%发生防线的可能性
     */
    private Integer level;

    /**
     * 风险应对计划
     */
    private String responsePlan;

    /**
     * 责任人id
     */
    private Long chargeUserId;

    /**
     * 责任人
     */
    private String chargeUser;

    /**
     * 状态 开放0、关闭1
     */
    private Integer status;


    /**
     * 请求对象赋值为实体类
     *
     * @param request
     * @return
     */
    public static ProjectRisk buildSave(ProjectRiskDTO request) {
        ProjectRisk entity = new ProjectRisk();
        BeanUtil.copyProperties(request, entity);
        if (!Optional.ofNullable(request.getId()).isPresent()) {
            BaseBuildEntityUtil.buildInsert(entity);
        }
        return entity;
    }

}
