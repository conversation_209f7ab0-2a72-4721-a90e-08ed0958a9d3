package com.gok.pboot.pms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.entity.domain.SalesReceiptDetails;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 销售收款明细Mapper
 *
 * <AUTHOR>
 * @description 针对表【sales_receipt_details(销售收款明细)】的数据库操作Mapper
 * @createDate 2023-10-07 11:24:50
 * @Entity com.gok.pboot.financial.db.entity.SalesReceiptDetails
 */
@Mapper
public interface SalesReceiptDetailsMapper extends BaseMapper<SalesReceiptDetails> {

    /**
     * 通过mainId查询
     *
     * @param mainId 主题Id
     * @return 实体类
     */
    List<SalesReceiptDetails> selectByMainId(@Param("mainId") Long mainId);
}




