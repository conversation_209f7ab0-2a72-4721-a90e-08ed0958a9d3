package com.gok.pboot.pms.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.data.datascope.BusinessDataScope;
import com.gok.pboot.pms.entity.domain.ContractLedger;
import com.gok.pboot.pms.entity.domain.ProjectBusinessMilestones;
import com.gok.pboot.pms.entity.dto.ContractLedgerListDTO;
import com.gok.pboot.pms.entity.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 合同台账数据（OA同步）
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-02-23 10:46:40
 */
@Mapper
public interface ContractLedgerMapper extends BaseMapper<ContractLedger> {

    /**
     * 查询合同台账列表
     *
     * @param page
     * @param dto
     * @return
     */
    Page<ContractListVo> findPage(Page<ContractLedgerListVo> page, @Param("dto") ContractLedgerListDTO dto);

    /**
     * 合同台账列表汇总数据
     *
     * @param dto
     * @return
     */
    @BusinessDataScope(deptOrUser = "user", scopeUserNameList = {"xmfzr", "xmjl", "xmxsry", "xmxsry2"})
    List<ContractLedgerSummaryVo> selSummary(@Param("dto") ContractLedgerListDTO dto);

    /**
     * 根据合同id查询合同头部信息
     *
     * @param id
     * @return
     */
    @BusinessDataScope(deptOrUser = "user", scopeUserNameList = {"xmfzr", "xmjl", "xmxsry", "xmxsry2"})
    ContractBaseHeadVo selHeadInfoById(@Param("id") Long id);

    /**
     * 根据合同id查询合同概览信息
     *
     * @param id
     * @return
     */
    ContractOverviewInfoVo selOverviewInfoById(@Param("id") Long id);


    /**
     * 查询列表数据
     *
     * @param dto
     * @return
     */
    @BusinessDataScope(deptOrUser = "user", scopeUserNameList = {"xmfzr", "xmjl", "xmxsry", "xmxsry2"})
    List<ContractListVo> selList(@Param("dto") ContractLedgerListDTO dto);

    /**
     * 查询列表数据汇总
     *
     * @param dto
     * @return
     */
    @BusinessDataScope(deptOrUser = "user", scopeUserNameList = {"xmfzr", "xmjl", "xmxsry", "xmxsry2"})
    List<ContractListVo> selListCount(@Param("dto") ContractLedgerListDTO dto);


    /**
     * 查询项目评价所需合同数据
     *
     * @param projectIds 项目ID集合[]
     * @return
     */
    List<ContractLedger> selEvalProjectData(@Param("projectIds") List<Long> projectIds);

}
