package com.gok.pboot.pms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.entity.domain.ProjectMeeting;
import com.gok.pboot.pms.entity.vo.ProjectMeetingExportExcelVO;
import com.gok.pboot.pms.entity.vo.ProjectMeetingFindPageVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 项目会议纪要 Mapper接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-13
 **/
@Mapper
public interface ProjectMeetingMapper extends BaseMapper<ProjectMeeting> {

    /**
     * 分页查询项目会议纪要
     *
     * @param page   分页请求实体
     * @param filter 请求参数
     * @return
     */
    Page<ProjectMeetingFindPageVO> findListPage(Page<ProjectMeetingFindPageVO> page,
                                                @Param("filter") Map<String, Object> filter);

    /**
     * 分页查询项目会议纪要导出所需数据
     *
     * @param page   分页请求实体
     * @param filter 请求参数
     * @return
     */
    Page<ProjectMeetingExportExcelVO> findExportPage(Page<ProjectMeetingExportExcelVO> page,
                                                     @Param("filter") Map<String, Object> filter);

    /**
     * 根据id查询实体类
     * 过滤逻辑删除数据
     *
     * @param id 项目会议纪要id
     * @return 实体类数据
     */
    ProjectMeeting selectById(@Param("id") Long id);

    /**
     * 批量逻辑删除
     *
     * @param list id集合
     */
    void batchDel(@Param("list") List<Long> list);

}
