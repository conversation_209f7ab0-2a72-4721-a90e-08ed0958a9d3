package com.gok.pboot.pms.entity.dto;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 任务编辑
 *
 * <AUTHOR>
 * @since 2022-08-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class TaskUpdateDTO {

    /**
     * 任务id
     */
    private Long id;
    /**
    * 任务名称
    */
    private String taskName;
    /**
    * 任务状态（0=正常，1=关闭）
    */
    private Integer taskStatus;
    /**
     * 人员id
     */
    private List<Long> taskUserIds;


}
