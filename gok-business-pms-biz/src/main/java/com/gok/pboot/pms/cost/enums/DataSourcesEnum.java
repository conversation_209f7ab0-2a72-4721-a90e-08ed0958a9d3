package com.gok.pboot.pms.cost.enums;

import com.gok.pboot.pms.enumeration.ValueEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 数据来源枚举类
 *
 * <AUTHOR>
 * @create 2025/01/24
 **/
@Getter
@AllArgsConstructor
public enum DataSourcesEnum implements ValueEnum<Integer> {

    /**
     * 测算生成
     */
    CALCULATE_GENERATE(0, "测算生成"),

    /**
     * 手动添加
     */
    MANUALLY_ADD(1, "手动添加");

    private final Integer value;

    private final String name;

}
