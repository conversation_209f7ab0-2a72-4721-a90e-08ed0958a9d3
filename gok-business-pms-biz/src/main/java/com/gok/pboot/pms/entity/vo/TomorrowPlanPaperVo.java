package com.gok.pboot.pms.entity.vo;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.gok.pboot.pms.common.base.BeanEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TomorrowPlanPaperVo extends BeanEntity<Long> {
    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 日报ID
     */
    private Long dailyPaperId;

    /**
     * 审核状态（0=未提交，1=已退回，2=待审核，3=不通过，4=已通过）
     */
    private Integer approvalStatus;

    /**
     * 提交日期
     */
    private LocalDate submissionDate;

    /**
     * 工时类型（0=售前支撑，1=售后交付）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer workType;

    /**
     * 描述
     */
    private String description;

    /**
     * 审核不通过原因
     */
    private String approvalReason;

    /**
     * 提交人ID
     */
    private Long userId;

    /**
     * 提交人姓名
     */
    private String userRealName;

    /**
     * 提交人所属部门ID
     */
    private Long userDeptId;

    /**
     * 旧任务标识
     */
    private Integer oldTaskFlag;

    /**
     * 查询日期
     */
    private LocalDate selectDate;
}
