package com.gok.pboot.pms.cost.enums;

import com.gok.pboot.pms.enumeration.ValueEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum AvailableStatusTypeEnum implements ValueEnum<Integer> {

    BKY(0, "不可用"),
    KY(1, "可用");

    /**
     * 值
     */
    private final Integer value;

    /**
     * 名称
     */
    private final String name;

}