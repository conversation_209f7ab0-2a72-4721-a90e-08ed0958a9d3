package com.gok.pboot.pms.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 项目回款跟踪编辑详情展示
 *
 * <AUTHOR>
 * @since 2023-09-28
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProjectPaymentClaimVO {

    /**
     * id
     */
    private Long id;

    // 回款信息

    /**
     * 收款公司code
     * {@link com.gok.pboot.pms.enumeration.AttributableSubjectEnum}
     */
    private String paymentCompany;

    /**
     * 收款公司value
     * {@link com.gok.pboot.pms.enumeration.AttributableSubjectEnum}
     */
    private String paymentCompanyTxt;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 企业名称
     */
    private String enterpriseName;

    /**
     * 收款日期
     */
    private String paymentDate;

    /**
     * 收款金额
     */
    private String paymentAmount;

    /**
     * 收款平台code
     */
    private Integer paymentPlatform;

    /**
     * 收款平台value
     */
    private String paymentPlatformTxt;

    /**
     * 凭证编号
     */
    private String voucherNumber;

    /**
     * 预算回款金额
     */
    private String budgetCollectionAmount;

    /**
     * 备注
     */
    private String paymentNote;

    /**
     * 银行账号
     */
    private String bankAccount;

    // 认领信息

    /**
     * 销售负责
     */
    private String salesmanUserName;

    /**
     * 归属业务线code
     * {@link com.gok.pboot.pms.enumeration.BusinessLineEnum}
     */
    private Integer businessLine;

    /**
     * 归属业务线value
     * {@link com.gok.pboot.pms.enumeration.BusinessLineEnum}
     */
    private String businessLineTxt;

    /**
     * 项目回款
     * {@link com.gok.pboot.pms.enumeration.ProjectCollectionEnum}
     */
    private Integer projectCollection;

    /**
     * 项目回款
     * {@link com.gok.pboot.pms.enumeration.ProjectCollectionEnum}
     */
    private String projectCollectionTxt;

    /**
     * 归属合同收款明细
     */
    private String contractPayment;

    /**
     * 合同名称
     */
    private String contractName;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 回款一级部门
     */
    private String paymentDept;

    /**
     * 回款二级部门
     */
    private String paymentSecondaryDept;

    /**
     * 预算内回款
     * {@link com.gok.pboot.pms.enumeration.CollectionWithinBudgetEnum}
     */
    private Integer collectionWithinBudget;

    /**
     * 预算内回款
     * {@link com.gok.pboot.pms.enumeration.CollectionWithinBudgetEnum}
     */
    private String collectionWithinBudgetTxt;

    /**
     * 归属区域
     */
    private String belongingArea;

}
