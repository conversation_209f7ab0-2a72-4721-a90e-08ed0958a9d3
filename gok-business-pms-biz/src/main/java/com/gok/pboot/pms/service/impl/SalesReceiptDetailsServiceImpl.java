package com.gok.pboot.pms.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.pboot.pms.Util.DbApiUtil;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.Util.MoneyUtil;
import com.gok.pboot.pms.common.constant.DictConstants;
import com.gok.pboot.pms.entity.domain.SalesReceipt;
import com.gok.pboot.pms.entity.domain.SalesReceiptDetails;
import com.gok.pboot.pms.entity.vo.ProjectDictVo;
import com.gok.pboot.pms.entity.vo.SalesReceiptDetailsTableVO;
import com.gok.pboot.pms.entity.vo.SalesReceiptDetailsVO;
import com.gok.pboot.pms.entity.vo.SalesReceiptInDetailVO;
import com.gok.pboot.pms.enumeration.AttributableSubjectEnum;
import com.gok.pboot.pms.enumeration.InvoicingStatusEnum;
import com.gok.pboot.pms.enumeration.PaymentStatusEnum;
import com.gok.pboot.pms.enumeration.ProjectStatusEnum;
import com.gok.pboot.pms.mapper.SalesReceiptDetailsMapper;
import com.gok.pboot.pms.mapper.SalesReceiptMapper;
import com.gok.pboot.pms.service.ISalesReceiptDetailsService;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 销售收款明细ServiceImpl
 *
 * <AUTHOR>
 * @description 针对表【sales_receipt_details(销售收款明细)】的数据库操作Service实现
 * @createDate 2023-10-07 11:24:50
 */
@Service
public class SalesReceiptDetailsServiceImpl extends ServiceImpl<SalesReceiptDetailsMapper, SalesReceiptDetails>
        implements ISalesReceiptDetailsService {

    @Resource
    private SalesReceiptMapper salesReceiptMapper;

    @Resource
    private DbApiUtil dbApi;

    @Override
    public SalesReceiptDetailsVO salesReceiptDetailList(Long id, Long contractId) {
        MoneyUtil moneyUtils = MoneyUtil.getInstance();

        //1、查询销售收款明细数据
        List<SalesReceiptDetails> salesReceiptDetailsList = baseMapper.selectByMainId(contractId);
        List<SalesReceiptDetailsTableVO> voList = new ArrayList<>();
        for (SalesReceiptDetails details : salesReceiptDetailsList) {
            SalesReceiptDetailsTableVO vo = configVo(details);
            voList.add(vo);
        }

        //2、查询销售收款数据
        SalesReceiptInDetailVO salesReceiptInDetailVO = new SalesReceiptInDetailVO();
        SalesReceipt salesReceipt = salesReceiptMapper.selectById(id);
        BeanUtils.copyProperties(salesReceipt, salesReceiptInDetailVO);
        salesReceiptInDetailVO.setAttributableSubject(EnumUtils.getNameByValue(AttributableSubjectEnum.class, salesReceiptInDetailVO.getAttributableSubject()));
        salesReceiptInDetailVO.setProjectStatus(ProjectStatusEnum.getNameByStrVal( salesReceiptInDetailVO.getProjectStatus()));
        salesReceiptInDetailVO.setContractMoney(moneyUtils.transType(salesReceipt.getContractMoney()));

        //3、数据组装
        return SalesReceiptDetailsVO.builder()
                .salesReceiptDetailsList(voList)
                .salesReceiptInDetailVO(salesReceiptInDetailVO).build();
    }

    private SalesReceiptDetailsTableVO configVo(SalesReceiptDetails details) {
        SalesReceiptDetailsTableVO vo = new SalesReceiptDetailsTableVO();
        //复制基础信息
        BeanUtils.copyProperties(details, vo);
        MoneyUtil moneyUtils = MoneyUtil.getInstance();
        //设置比例
        if (details.getProportionFunds() != null) {
            BigDecimal ratio = new BigDecimal(details.getProportionFunds()).multiply(new BigDecimal("100")).setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP);
            vo.setProportionFunds(ratio + "%");
        }
        //设置枚举
        vo.setInvoiceStatusTxt(EnumUtils.getNameByValue(InvoicingStatusEnum.class, details.getInvoiceStatus()));
        vo.setPaymentStatusTxt(EnumUtils.getNameByValue(PaymentStatusEnum.class, String.valueOf(details.getPaymentStatus())));
        //金额类型转换
        if (details.getFundsAmount() != null) {
            vo.setFundsAmount(moneyUtils.transType(details.getFundsAmount()));
        }
        if (details.getPaymentAmountIncludingTax() != null) {
            vo.setPaymentAmountIncludingTax(moneyUtils.transType(details.getPaymentAmountIncludingTax()));
        }
        if (details.getPaymentAmount() != null) {
            vo.setPaymentAmount(moneyUtils.transType(details.getPaymentAmount()));
        }
        //税率转换
        if (details.getTaxRate() != null) {
            Map<Integer, String> projectMap = dbApi.projectDict(DictConstants.SALES_TAX_RATE_ID)
                    .stream()
                    .collect(Collectors.toMap(ProjectDictVo::getSelectvalue, ProjectDictVo::getSelectname));
            vo.setTaxRate(projectMap.get(details.getTaxRate()));
        }
        return vo;
    }


}




