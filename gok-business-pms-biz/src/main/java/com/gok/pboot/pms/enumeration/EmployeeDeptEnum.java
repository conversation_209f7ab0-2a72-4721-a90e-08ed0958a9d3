package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;

/**
 * - 孤立的人员部门枚举 -
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/9/23 10:53
 */
@AllArgsConstructor
public enum EmployeeDeptEnum implements ValueEnum<Long> {
    // 注释详见name属性
    OUTSIDE(90000001L, "临时");

    private final Long value;

    private final String name;

    @Override
    public Long getValue() {
        return value;
    }

    @Override
    public String getName() {
        return name;
    }
}
