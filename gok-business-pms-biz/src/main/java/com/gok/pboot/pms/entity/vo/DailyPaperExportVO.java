package com.gok.pboot.pms.entity.vo;

import lombok.*;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 日报提交一览表 日报集合VO类
 *
 * <AUTHOR>
 * @since 2023-08-22
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class DailyPaperExportVO {

    /**
     * 提交日期
     */
    private LocalDate submissionDate;

    /**
     * 日常工时数量
     */
    private BigDecimal dailyHourCount;

    /**
     * 加班工时数量
     */
    private BigDecimal addedHourCount;

    /**
     * 工作时长字符串
     * 日常工时加班工时 eg:7+0
     */
    private String workHourStr;

    /**
     * 是否工作日（0=否，1=是）
     */
    private Integer workday;


}
