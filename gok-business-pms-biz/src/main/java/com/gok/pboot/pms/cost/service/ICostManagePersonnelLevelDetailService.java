package com.gok.pboot.pms.cost.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.cost.entity.domain.CostManagePersonnelLevelDetail;
import com.gok.pboot.pms.cost.entity.vo.CostManagePersonnelLevelDetailVO;

import java.util.List;

/**
 * <p>
 * 成本管理人员级别测算明细 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
public interface ICostManagePersonnelLevelDetailService extends IService<CostManagePersonnelLevelDetail> {


    /**
     * 根据estimationResultsId查询预算明细
     * @param estimationResultsId
     * @return
     */
    List<CostManagePersonnelLevelDetailVO> getByEstimationResultsId(Long estimationResultsId);

}
