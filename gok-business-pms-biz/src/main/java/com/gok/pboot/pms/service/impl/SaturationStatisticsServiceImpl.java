package com.gok.pboot.pms.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.bcp.upms.dto.DeptCacheDto;
import com.gok.bcp.upms.dto.DeptDto;
import com.gok.bcp.upms.feign.RemoteDeptService;
import com.gok.pboot.pms.Util.*;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.BaseConstants;
import com.gok.pboot.pms.common.base.BaseEntity;
import com.gok.pboot.pms.common.base.R;
import com.gok.pboot.pms.common.constant.EntitySign;
import com.gok.pboot.pms.common.constant.FunctionConstants;
import com.gok.pboot.pms.common.exception.ServiceException;
import com.gok.pboot.pms.entity.SysDept;
import com.gok.pboot.pms.entity.domain.ProjectInfo;
import com.gok.pboot.pms.entity.domain.TomorrowPlanPaperEntry;
import com.gok.pboot.pms.entity.dto.DailyPaperAnalysisDTO;
import com.gok.pboot.pms.entity.dto.SaturationStatisticsDTO;
import com.gok.pboot.pms.entity.vo.*;
import com.gok.pboot.pms.enumeration.ApprovalStatusEnum;
import com.gok.pboot.pms.enumeration.LogContentEnum;
import com.gok.pboot.pms.enumeration.ProjectIncomeTypeEnum;
import com.gok.pboot.pms.enumeration.YesOrNoEnum;
import com.gok.pboot.pms.handler.ProjectScopeHandle;
import com.gok.pboot.pms.mapper.*;
import com.gok.pboot.pms.service.ISaturationStatisticsService;
import com.gok.pboot.pms.service.processor.SaturationCalculator;
import com.google.common.base.Strings;
import com.google.common.collect.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.data.util.CastUtils;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2023/2/22
 */
@Slf4j
@Service
@AllArgsConstructor
public class SaturationStatisticsServiceImpl implements ISaturationStatisticsService {

    private final DailyPaperEntryMapper dailyPaperEntryMapper;
    private final DailyPaperMapper dailyPaperMapper;
    private final ProjectInfoMapper projectInfoMapper;
    private final SaturationCalculator calculator;
    private final RosterMapper rosterMapper;
    private final TomorrowPlanPaperEntryMapper tomorrowPlanPaperEntryMapper;
    private final EntityOptionMapper entityOptionMapper;

    private final RemoteDeptService remoteDeptService;
    private final ProjectScopeHandle projectScopeHandle;
    private final BcpLoggerUtils bcpLoggerUtils;
    private final CompensatoryLeaveDataMapper compensatoryLeaveDataMapper;

    /**
     * 项目占比情况
     *
     * @param saturationStatisticsDTO 查询实体
     * @return {@link ApiResult <Map<String, BigDecimal>>}
     */
    @Override
    public List<ProjectPercentVO> projectPercentage(SaturationStatisticsDTO saturationStatisticsDTO) {
        //部门搜索条件为空，返回空数据
        if(saturationStatisticsDTO.getDeptIds().isEmpty()){
            return new ArrayList<>(0);
        }
        List<ProjectPercentVO> percentVOs = new ArrayList<>();
        List<Long> projectIds = findProjectIdsByProjectIncomeTypes(saturationStatisticsDTO.getProjectIncomeTypes());
        if (projectIds != null && projectIds.isEmpty()) {
            return percentVOs;
        }
        // 项目id -> 项目耗用工时
        Map<Object, BigDecimal> data = new HashMap<>();
        BigDecimal all = this.calculateAllTime(
                saturationStatisticsDTO, projectIds, data, DailyPaperEntryVO::getProjectId, false,true
        );
        // key 是不重复的项目id，以此查出涉及到的项目
        List<Long> projectIdsInResult = new ArrayList<>(CastUtils.cast(data.keySet()));
        if (CollectionUtil.isEmpty(projectIdsInResult)) {
            return percentVOs;
        }
        List<ProjectInfo> projects = projectInfoMapper.selectBatchIds(projectIdsInResult);

        Map<Long, String> projectMap = projects.stream().collect(Collectors.toMap(BaseEntity::getId, ProjectInfo::getItemName, (a, b) -> a));
        percentVOs = this.calculateAllTime(saturationStatisticsDTO, data, projectMap, all, true);

        return percentVOs;
    }

    /**
     * 计算对应时间，对应部门 的 耗用工时  并计算总工时
     *
     * @param saturationStatisticsDTO 查询体
     * @param data                    计算完的实体
     * @param function                方法  -> 按object组合成map
     * @param isCalculatedDay         是否计算人天
     * @param isProject                是否项目占比  、排行榜
     * @return 总工时
     */
    private BigDecimal calculateAllTime(
            SaturationStatisticsDTO saturationStatisticsDTO,
            List<Long> projectIds,
            Map<Object, BigDecimal> data,
            Function<DailyPaperEntryVO, Object> function,
            boolean isCalculatedDay,
            boolean isProject
    ) {
        // 总工时
        BigDecimal all = BigDecimal.ZERO;
        // 获取子集部门
        Pair<List<Long>, List<Long>> deptIdsAndUserIdsAvailable = findDeptIdsAndUserIdsAvailable(
                saturationStatisticsDTO.getDeptIds(),
                projectIds,
                LocalDateTimeUtil.parseDate(saturationStatisticsDTO.getStartTime()),
                LocalDateTimeUtil.parseDate(saturationStatisticsDTO.getEndTime()),
                saturationStatisticsDTO.getOnlySelectedDeptFlag()
        );
        if (deptIdsAndUserIdsAvailable == null) {
            return BigDecimal.ZERO;
        }
        List<Long> allDeptIds = deptIdsAndUserIdsAvailable.getFirst();
        // 项目id -> 项目耗用工时
        List<DailyPaperEntryVO> byProjectIdsAndSubmissionDateBetween = dailyPaperEntryMapper.findByProjectIdsAndSubmissionDateBetween(
                allDeptIds,
                deptIdsAndUserIdsAvailable.getSecond(),
                saturationStatisticsDTO.getStartTime(),
                saturationStatisticsDTO.getEndTime(),
                ApprovalStatusEnum.YTG.getValue(),
                projectIds
        );
        // 计算对应时间，对应部门 的 耗用工时  并计算总工时
/*        for (DailyPaperEntryVO dailyPaperEntry : byProjectIdsAndSubmissionDateBetween) {
            BigDecimal normalHours = dailyPaperEntry.getNormalHours();
            BigDecimal addedHours = dailyPaperEntry.getAddedHours();
            BigDecimal sum = normalHours.add(addedHours);
            all = all.add(sum);
            if (isCalculatedDay) {
                sum = CommonUtils.unitConversion(sum);
            }
            data.putIfAbsent(function.apply(dailyPaperEntry), BigDecimal.ZERO);
            BigDecimal add = data.get(function.apply(dailyPaperEntry)).add(sum);
            data.put(function.apply(dailyPaperEntry), add);
        }*/
        if(isProject){
            Map<Long, List<DailyPaperEntryVO>> projectIdsMap = byProjectIdsAndSubmissionDateBetween.stream().collect(Collectors.groupingBy(DailyPaperEntryVO::getProjectId));
            for (Long projectId : projectIdsMap.keySet()){
                List<DailyPaperEntryVO> detailList = projectIdsMap.get(projectId);
                DailyPaperEntryVO dailyPaperEntry = detailList.get(0);
                BigDecimal normalHours = detailList.stream().map(DailyPaperEntryVO::getNormalHours).reduce(BigDecimal.ZERO,BigDecimal::add);
                BigDecimal addedHours = detailList.stream().map(DailyPaperEntryVO::getAddedHours).reduce(BigDecimal.ZERO,BigDecimal::add);
                BigDecimal sum = normalHours.add(addedHours);
                all = all.add(sum);
                if (isCalculatedDay) {
                    sum = CommonUtils.unitConversion(sum);
                }
                data.putIfAbsent(function.apply(dailyPaperEntry), BigDecimal.ZERO);
                BigDecimal add = data.get(function.apply(dailyPaperEntry)).add(sum);
                data.put(function.apply(dailyPaperEntry), add);
            }
        }else{
            //工时转换人天  先处理累加再进行计算处理人天
            Map<Long, List<DailyPaperEntryVO>> byProjectIdsAndSubmissionDateBetweenMap = byProjectIdsAndSubmissionDateBetween.stream().collect(Collectors.groupingBy(DailyPaperEntryVO::getUserId));
            for (Long userId : byProjectIdsAndSubmissionDateBetweenMap.keySet()){
                List<DailyPaperEntryVO> detailList = byProjectIdsAndSubmissionDateBetweenMap.get(userId);
                DailyPaperEntryVO dailyPaperEntry = detailList.get(0);
                BigDecimal normalHours = detailList.stream().map(DailyPaperEntryVO::getNormalHours).reduce(BigDecimal.ZERO,BigDecimal::add);
                BigDecimal addedHours = detailList.stream().map(DailyPaperEntryVO::getAddedHours).reduce(BigDecimal.ZERO,BigDecimal::add);
                BigDecimal sum = normalHours.add(addedHours);
                all = all.add(sum);
                if (isCalculatedDay) {
                    sum = CommonUtils.unitConversion(sum);
                }
                data.putIfAbsent(function.apply(dailyPaperEntry), BigDecimal.ZERO);
                BigDecimal add = data.get(function.apply(dailyPaperEntry)).add(sum);
                data.put(function.apply(dailyPaperEntry), add);
            }
        }
        return all;
    }

    /**
     * @param saturationStatisticsDTO 查询体
     * @param data                    计算完的实体
     * @param nameMap                 名称映射
     * @param all                     总工时 / 出勤天数
     * @param flag                    是否在列表最后增加其他
     * @return
     */
    private List<ProjectPercentVO> calculateAllTime(SaturationStatisticsDTO saturationStatisticsDTO, Map<Object, BigDecimal> data,
                                                    Map<Long, String> nameMap, BigDecimal all, boolean flag) {
        List<ProjectPercentVO> percentVOs = new ArrayList<>();
        // 按照value - >耗用工时进行排序
        List<Map.Entry<Object, BigDecimal>> dataSorts = new ArrayList<>(data.entrySet());
        dataSorts.sort((o1, o2) -> (o2.getValue().subtract(o1.getValue()).intValue()));
        // 前几名加起来的总百分比
        BigDecimal totalPercent = BigDecimal.ZERO;
        Integer defaultShowNum = saturationStatisticsDTO.getDefaultShowNum();
        BigDecimal hundred = new BigDecimal("100");
        // 计算耗用工时前几的百分比
        for (Map.Entry<Object, BigDecimal> entry : dataSorts) {
            if (defaultShowNum <= percentVOs.size()) {
                ProjectPercentVO projectPercentVO = new ProjectPercentVO("其他", BigDecimal.ONE.subtract(totalPercent));
                percentVOs.add(projectPercentVO);
                return percentVOs;
            }

            // 应出勤天数0时，工时饱和度对应展示为0
            BigDecimal percent = BigDecimal.ZERO.equals(all) ? BigDecimal.ZERO : entry.getValue().divide(all, 4, RoundingMode.HALF_UP);
            if (CollectionUtil.isEmpty(nameMap)) {
                // top10排行的
                totalPercent = totalPercent.add(percent);
                ProjectPercentVO projectPercentVO = new ProjectPercentVO(entry.getKey().toString(), percent.multiply(hundred));
                percentVOs.add(projectPercentVO);
            } else if (CollectionUtil.isNotEmpty(nameMap) && nameMap.containsKey(entry.getKey())) {
                // 项目的
                totalPercent = totalPercent.add(percent);
                String name = nameMap.get(entry.getKey());
                ProjectPercentVO projectPercentVO = new ProjectPercentVO(name, percent);
                percentVOs.add(projectPercentVO);
            }
        }
        if (ObjectUtil.isEmpty(nameMap)) {
            return percentVOs;
        }
        if (flag) {
            ProjectPercentVO projectPercentVO = new ProjectPercentVO("其他", BigDecimal.ONE.subtract(totalPercent));
            percentVOs.add(projectPercentVO);
        }
        return percentVOs;

    }

    @Override
    public List<ProjectPercentVO> top10Ranking(SaturationStatisticsDTO saturationStatisticsDTO) {
        //部门搜索条件为空，返回空数据
        if(saturationStatisticsDTO.getDeptIds().isEmpty()){
            return new ArrayList<>();
        }
        String startTime = saturationStatisticsDTO.getStartTime();
        String endTime = saturationStatisticsDTO.getEndTime();
        Map<Long, Pair<BigDecimal, BigDecimal>> userAttendanceMap = calculator.calcAttendance(
                LocalDateTimeUtil.parseDate(startTime),
                LocalDateTimeUtil.parseDate(endTime),
                null
        );
        List<ProjectPercentVO> percentVOs = new ArrayList<>();
        // 名字id -> 项目耗用工时
        Map<Object, BigDecimal> data = new HashMap<>();
        Function<DailyPaperEntryVO, Object> userNameSplitId = dailyPaperEntryVO -> StringUtils.join(dailyPaperEntryVO.getUserRealName(), BaseConstants.SPLIT_STR, dailyPaperEntryVO.getUserId());
        List<Long> projectIds = findProjectIdsByProjectIncomeTypes(saturationStatisticsDTO.getProjectIncomeTypes());
        if (projectIds != null && projectIds.isEmpty()) {
            return percentVOs;
        }
        this.calculateAllTime(saturationStatisticsDTO, projectIds, data, userNameSplitId, true,false);
        BigDecimal hundred = new BigDecimal("100");
        Integer defaultShowNum = saturationStatisticsDTO.getDefaultShowNum();
        Pair<BigDecimal, BigDecimal> fallbackAttendanceDays = Pair.of(BigDecimal.ZERO, BigDecimal.ZERO);
        for (Map.Entry<Object, BigDecimal> entry : data.entrySet()) {
            String[] split = entry.getKey().toString().split(BaseConstants.SPLIT_STR);
            String name = split[NumberUtils.INTEGER_ZERO];
            Long userId = Long.valueOf(split[NumberUtils.INTEGER_ONE]);
            Pair<BigDecimal, BigDecimal> payAttendanceDays = userAttendanceMap.getOrDefault(userId, fallbackAttendanceDays);
            BigDecimal percent = NumberUtils.INTEGER_ZERO.equals(payAttendanceDays.getFirst().compareTo(BigDecimal.ZERO)) ?
                    BigDecimal.ZERO
                    :
                    entry.getValue().divide(payAttendanceDays.getFirst(), 4, RoundingMode.HALF_UP);
            //默认0存储姓名
            ProjectPercentVO projectPercentVO = new ProjectPercentVO(name, percent.multiply(hundred));
            percentVOs.add(projectPercentVO);
        }
        percentVOs = percentVOs.stream()
                .sorted(Comparator.comparing(ProjectPercentVO::getPercent, Comparator.reverseOrder()))
                .limit(defaultShowNum)
                .collect(Collectors.toList());

        return percentVOs;
    }

    @Override
    public SaturationAndTotalVO getDeptDetailData(SaturationStatisticsDTO saturationStatisticsDTO) {
        //部门搜索条件为空，返回空数据
        if(saturationStatisticsDTO.getDeptIds().isEmpty()){
            return new SaturationAndTotalVO();
        }
        LocalDate startDate = LocalDateTimeUtil.parseDate(saturationStatisticsDTO.getStartTime());
        LocalDate endDate = LocalDateTimeUtil.parseDate(saturationStatisticsDTO.getEndTime());
        // 标识为部门
        saturationStatisticsDTO.setFlag(YesOrNoEnum.YES.getValue());
        List<Long> selectDeptIds = saturationStatisticsDTO.getDeptIds();
        // 获取可用项目
        List<Long> projectIds = findProjectIdsByProjectIncomeTypes(saturationStatisticsDTO.getProjectIncomeTypes());
        if (projectIds != null && projectIds.isEmpty()) {
            return new SaturationAndTotalVO();
        }
        // 获取可用部门和人员
        Pair<List<Long>, List<Long>> deptIdsAndUserIds = findDeptIdsAndUserIdsAvailable(
                selectDeptIds,
                projectIds,
                startDate,
                endDate,
                saturationStatisticsDTO.getOnlySelectedDeptFlag()
        );
        if (deptIdsAndUserIds == null) {
            return new SaturationAndTotalVO();
        }
        // 获取部门信息
        List<Long> deptIds = deptIdsAndUserIds.getFirst();
        Set<Long> deptIdsForGroupingBy;
        Multimap<Long, Long> deptIdAndChildrenIdMap;
        List<Long> userIds = deptIdsAndUserIds.getSecond();
        Map<Long, Pair<BigDecimal, BigDecimal>> userAttendanceMap = calculator.calcAttendance(
                startDate,
                endDate,
                userIds
        );
        SaturationAndTotalVO saturationAndTotal = new SaturationAndTotalVO();
        List<SituationAnalysisDeptVO> list = new ArrayList<>();
        Map<Long, SysDept> deptIdMap = remoteDeptService.getAllDeptList(false)
                .stream()
                .map(SysDeptUtils::mapToSysDept)
                .collect(Collectors.toMap(SysDept::getDeptId, d -> d, (a, b) -> a));
        BigDecimal totalSalaryAttendance = BigDecimal.ZERO;

        if (CollUtil.isEmpty(deptIdMap)) {
            throw new ServiceException("获取中台部门信息异常");
        }
        deptIdsForGroupingBy = Sets.newHashSet(deptIds);
        deptIdAndChildrenIdMap = HashMultimap.create();
        // 聚合部门信息，将子集全部移除得到set，同时收集这些父子关系以便聚合查询结果
        deptIds.forEach(dId -> {
            if (!deptIdMap.containsKey(dId)) {
                return;
            }
            SysDeptUtils.findAllChildrenDept(deptIdMap.values(), dId)
                    .forEach(d -> {
                        Long deptId = d.getDeptId();
                        //不过滤部门，选择的部门都展示
                        //if (deptIdsForGroupingBy.remove(deptId)) {
                            deptIdAndChildrenIdMap.put(dId, deptId);
                        //}
                    });
            deptIdAndChildrenIdMap.put(dId, dId);
        });
        //处理排序  按照传入的部门排序
        ArrayList<Long> longs = new ArrayList<>(deptIdsForGroupingBy);
        List<Long> orders = saturationStatisticsDTO.getDeptIds();
        Collections.sort(longs, new Comparator<Long>() {
            @Override
            public int compare(Long o1, Long o2) {
                int index1 = orders.indexOf(o1);
                int index2 = orders.indexOf(o2);
                return Integer.compare(index1, index2);
            }
        });

        for (Long selectDeptId : longs) {
            SysDept dept = deptIdMap.get(selectDeptId);
            if (dept == null) {
                continue;
            }
            String deptName = dept.getName();
            SituationAnalysisDeptVO panelProjectSituationAnalysisVO = new SituationAnalysisDeptVO();
            panelProjectSituationAnalysisVO.setStartTime(saturationStatisticsDTO.getStartTime());
            panelProjectSituationAnalysisVO.setEndTime(saturationStatisticsDTO.getEndTime());
            // 先获取子集部门
            //List<Long> allDeptIds = ImmutableList.copyOf(deptIdAndChildrenIdMap.get(selectDeptId));
            List<Long> allDeptIds = Lists.newArrayList(selectDeptId);

            saturationStatisticsDTO.setDeptIds(allDeptIds);
            if (CollUtil.isEmpty(allDeptIds)) {
                continue;
            }
            // 查库,部门与下面所有部门的集合
            saturationStatisticsDTO.setApprovalStatus(ApprovalStatusEnum.YTG.getValue());

            List<CompensatoryLeaveDeptSumVO> leaveDeptSumProjects = compensatoryLeaveDataMapper.sumLeaveDeptByDateTimeRangeAndUserIds(startDate, endDate, userIds, projectIds, "3");
            List<CompensatoryLeaveDeptSumVO> leaveDeptSumAll = compensatoryLeaveDataMapper.sumLeaveDeptByDateTimeRangeAndUserIds(startDate, endDate, userIds, null, "3");
            List<SituationAnalysisDeptVO> deptChildEntry =
                    dailyPaperEntryMapper.selectListSituationAnalysis(
                            saturationStatisticsDTO,
                            userIds,
                            projectIds
                    );
            /*List<Long> users = dailyPaperEntryMapper.findUserIds(
                    startDate,
                    endDate,
                    ApprovalStatusEnum.YTG.getValue(),
                    allDeptIds,
                    projectIds
            );*/
            List<Long> usersAll = dailyPaperEntryMapper.findUserIds(
                    startDate,
                    endDate,
                    ApprovalStatusEnum.YTG.getValue(),
                    allDeptIds,
                    null
            );

            if (CollectionUtils.isEmpty(deptChildEntry)) {
                continue;
            }
            BigDecimal attendanceDays = BigDecimal.ZERO;
            BigDecimal salaryAttendanceDays = BigDecimal.ZERO;
            Pair<BigDecimal, BigDecimal> fallbackAttendanceDays = Pair.of(BigDecimal.ZERO, BigDecimal.ZERO);

            for (Long user : usersAll) {
                Pair<BigDecimal, BigDecimal> pair = userAttendanceMap.getOrDefault(user, fallbackAttendanceDays);

                attendanceDays = attendanceDays.add(pair.getFirst());
                salaryAttendanceDays = salaryAttendanceDays.add(pair.getSecond());
            }
            // 计算部门下的合计 出勤天数 = 该部门下的所有人 * 每个人应出勤天数
            this.calculTotal(
                    leaveDeptSumProjects,
                    leaveDeptSumAll,
                    deptChildEntry,
                    null,
                    panelProjectSituationAnalysisVO,
                    attendanceDays,
                    salaryAttendanceDays
            );
            panelProjectSituationAnalysisVO.setDeptName(deptName);
            list.add(panelProjectSituationAnalysisVO);
            panelProjectSituationAnalysisVO.setSalaryAttendanceDays(salaryAttendanceDays);
            totalSalaryAttendance = totalSalaryAttendance.add(salaryAttendanceDays);
        }
        if (CollectionUtils.isEmpty(list)) {
            return saturationAndTotal;
        }
        // 计算合计
        this.calculTotal(null,null,list, saturationAndTotal, null, null, totalSalaryAttendance);
        saturationAndTotal.setList(list);

        return saturationAndTotal;
    }

    /**
     * 合计计算
     *
     * @param list                            集合
     * @param saturationAndTotalVO            填充的实体1
     * @param panelProjectSituationAnalysisVO 填充的实体2
     */
    private void calculTotal(
            List<CompensatoryLeaveDeptSumVO> leaveSumProjects,
            List<CompensatoryLeaveDeptSumVO> leaveSumAll,
            List<SituationAnalysisDeptVO> list,
            SaturationAndTotalVO saturationAndTotalVO,
            SituationAnalysisDeptVO panelProjectSituationAnalysisVO,
            BigDecimal attendanceDays,
            BigDecimal salaryAttendanceDays
    ) {
        // 计算所有的合计
        BigDecimal totalNormalHours = BigDecimal.ZERO;
        BigDecimal totalAddedHours = BigDecimal.ZERO;
        BigDecimal totalWorkAddedHours = BigDecimal.ZERO;
        BigDecimal totalRestAddedHours = BigDecimal.ZERO;
        BigDecimal totalHolidayAddedHours = BigDecimal.ZERO;
        BigDecimal totalLeaveHours = BigDecimal.ZERO;
        BigDecimal totalProjectHours = BigDecimal.ZERO;
        BigDecimal totalProjectShareHours = BigDecimal.ZERO;
        BigDecimal totalManagementHours = BigDecimal.ZERO;
        BigDecimal totalAttendanceDays = BigDecimal.ZERO;
        BigDecimal totalSalaryAttendanceDays = BigDecimal.ZERO;
        BigDecimal totalHourSaturation;
        BigDecimal sumNormalHours = BigDecimal.ZERO;
        BigDecimal sumAddedHours = BigDecimal.ZERO;
        BigDecimal sumWorkAddedHours = BigDecimal.ZERO;
        BigDecimal sumRestAddedHours = BigDecimal.ZERO;
        BigDecimal sumHolidayAddedHours = BigDecimal.ZERO;
        BigDecimal sumLeaveHours = BigDecimal.ZERO;
        BigDecimal sumManagementHours = BigDecimal.ZERO;
        BigDecimal sumProjectHours = BigDecimal.ZERO;
        BigDecimal sumProjectShareHours = BigDecimal.ZERO;
        Map<Long,BigDecimal> leaveSumProjectsMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(leaveSumProjects)){
            leaveSumProjectsMap = leaveSumProjects.stream().collect(Collectors.toMap(CompensatoryLeaveDeptSumVO::getDeptId, CompensatoryLeaveDeptSumVO::getLeaveSum));
        }
        // 换算为人天，并计算项目耗用工时
        for (SituationAnalysisDeptVO record : list) {
            BigDecimal normalHours = ObjectUtils.defaultIfNull(record.getNormalHours(), BigDecimal.ZERO);
            BigDecimal addedHours = ObjectUtils.defaultIfNull(record.getAddedHours(), BigDecimal.ZERO);
            BigDecimal addedWorkHours = ObjectUtils.defaultIfNull(record.getWorkOvertimeHours(), BigDecimal.ZERO);
            BigDecimal addedRestHours = ObjectUtils.defaultIfNull(record.getRestOvertimeHours(), BigDecimal.ZERO);
            BigDecimal addedHolidayHours = ObjectUtils.defaultIfNull(record.getHolidayOvertimeHours(), BigDecimal.ZERO);
            BigDecimal leaveHours = leaveSumProjectsMap.getOrDefault(record.getDeptId(), BigDecimal.ZERO);
            BigDecimal managementHours = ObjectUtils.defaultIfNull(record.getManagementHours(), BigDecimal.ZERO);

            BigDecimal projectHours = BigDecimal.ZERO;
            BigDecimal projectShareHours = BigDecimal.ZERO;

            if (ObjectUtil.isNotNull(saturationAndTotalVO)) {
                totalAttendanceDays =
                        totalAttendanceDays.add(ObjectUtils.defaultIfNull(record.getAttendanceDays(), BigDecimal.ZERO));
                leaveHours = ObjectUtils.defaultIfNull(record.getLeaveHours(), BigDecimal.ZERO);
                totalSalaryAttendanceDays = salaryAttendanceDays;
                projectHours = record.getProjectHours();
                projectShareHours = record.getProjectShareHours();
            } else {
                // 不是合计不用计算
                totalAttendanceDays = attendanceDays;
                totalSalaryAttendanceDays = salaryAttendanceDays;
                sumNormalHours = sumNormalHours.add(normalHours);
                sumAddedHours = sumAddedHours.add(addedHours);
                sumWorkAddedHours = sumWorkAddedHours.add(addedWorkHours);
                sumRestAddedHours = sumRestAddedHours.add(addedRestHours);
                sumHolidayAddedHours = sumHolidayAddedHours.add(addedHolidayHours);
                sumLeaveHours = sumLeaveHours.add(leaveHours);
                sumManagementHours = sumManagementHours.add(managementHours);
                sumProjectHours = sumNormalHours.add(sumAddedHours);
                sumProjectShareHours = sumNormalHours.add(sumRestAddedHours).add(sumHolidayAddedHours).add(sumLeaveHours);
            }
            totalNormalHours = totalNormalHours.add(normalHours);
            totalAddedHours = totalAddedHours.add(addedHours);
            totalWorkAddedHours = totalWorkAddedHours.add(addedWorkHours);
            totalRestAddedHours = totalRestAddedHours.add(addedRestHours);
            totalHolidayAddedHours = totalHolidayAddedHours.add(addedHolidayHours);
            totalLeaveHours = totalLeaveHours.add(leaveHours);
            totalManagementHours = totalManagementHours.add(managementHours);
            totalProjectHours = totalProjectHours.add(projectHours);
            totalProjectShareHours = totalProjectShareHours.add(projectShareHours);
        }
        totalHourSaturation = CommonUtils.calculationHourSaturation(CommonUtils.unitConversion(sumProjectHours), totalAttendanceDays);
        //  合计
        if (ObjectUtil.isNotNull(saturationAndTotalVO)) {
            saturationAndTotalVO.setTotalHourSaturation(
                    totalAttendanceDays.compareTo(BigDecimal.ZERO) <= 0 ? BigDecimal.ZERO :
                    totalProjectHours
                            .multiply(BigDecimalUtils.HUNDRED_DECIMAL)
                            .divide(totalAttendanceDays.setScale(2, RoundingMode.HALF_UP), RoundingMode.HALF_UP)
                            .setScale(2, RoundingMode.HALF_UP)
            );
            saturationAndTotalVO.setTotalNormalHours(totalNormalHours);
            saturationAndTotalVO.setTotalAddedHours(totalAddedHours);
            saturationAndTotalVO.setTotalWorkOvertimeHours(totalWorkAddedHours);
            saturationAndTotalVO.setTotalRestOvertimeHours(totalRestAddedHours);
            saturationAndTotalVO.setTotalHolidayOvertimeHours(totalHolidayAddedHours);
            saturationAndTotalVO.setTotalLeaveHours(totalLeaveHours);
            saturationAndTotalVO.setTotalManagementHours(totalManagementHours);
            saturationAndTotalVO.setTotalProjectHours(totalProjectHours);
            saturationAndTotalVO.setTotalProjectShareHours(totalProjectShareHours);
            saturationAndTotalVO.setTotalAttendanceDays(totalAttendanceDays);
            saturationAndTotalVO.setTotalSalaryAttendanceDays(totalSalaryAttendanceDays);
        }
        // 需要计算人天等
        if (ObjectUtil.isNotNull(panelProjectSituationAnalysisVO)) {
            panelProjectSituationAnalysisVO.setHourSaturation(totalHourSaturation);
            panelProjectSituationAnalysisVO.setNormalHours(CommonUtils.unitConversion(sumNormalHours));
            panelProjectSituationAnalysisVO.setManagementHours(CommonUtils.unitConversion(sumManagementHours));
            panelProjectSituationAnalysisVO.setAddedHours(CommonUtils.unitConversion(sumAddedHours));
            panelProjectSituationAnalysisVO.setWorkOvertimeHours(CommonUtils.unitConversion(sumWorkAddedHours));
            panelProjectSituationAnalysisVO.setRestOvertimeHours(CommonUtils.unitConversion(sumRestAddedHours));
            panelProjectSituationAnalysisVO.setHolidayOvertimeHours(CommonUtils.unitConversion(sumHolidayAddedHours));
            panelProjectSituationAnalysisVO.setLeaveHours(CommonUtils.unitConversion(sumLeaveHours));
            panelProjectSituationAnalysisVO.setProjectHours(CommonUtils.unitConversion(sumProjectHours));
            panelProjectSituationAnalysisVO.setProjectShareHours(CommonUtils.unitConversion(sumProjectShareHours));
            panelProjectSituationAnalysisVO.setAttendanceDays(totalAttendanceDays);
        }
    }

    @Override
    public Page<SituationAnalysisVO> getUserDetailData(SaturationStatisticsDTO saturationStatisticsDTO) {
        //部门搜索条件为空，返回空数据
        if(saturationStatisticsDTO.getDeptIds().isEmpty()){
            return Page.of(saturationStatisticsDTO.getPageNumber(), saturationStatisticsDTO.getPageSize());
        }
        Boolean onlySelectedDeptFlag = BooleanUtils.toBoolean(saturationStatisticsDTO.getOnlySelectedDeptFlag());
        // 获取可查项目
        List<Long> projectIds = findProjectIdsByProjectIncomeTypes(saturationStatisticsDTO.getProjectIncomeTypes());
        LocalDate startDate;
        LocalDate endDate;
        Pair<List<Long>, List<Long>> deptIdsAndUserIds;
        Page<SituationAnalysisVO> page = Page.of(
                saturationStatisticsDTO.getPageNumber(), saturationStatisticsDTO.getPageSize()
        );
        boolean checkUserDetails = BooleanUtils.toBoolean(saturationStatisticsDTO.getCheckUserDetailsFlag());
        List<SituationAnalysisVO> records;
        Map<Long, SituationAnalysisVO> userIdAndRecordMap;
        Set<Long> userIds;
        String startTime;
        String endTime;
        Map<Long, Pair<BigDecimal, BigDecimal>> userAttendanceMap;
        List<DeptCacheDto> deptList;
        Map<Long, DeptCacheDto> deptIdMap;
        long userId;
        Pair<BigDecimal, BigDecimal> attendance;
        Pair<BigDecimal, BigDecimal> fallbackPair;
        DeptCacheDto dept;
        List<Long> deptIds;
        Long userIdParam;
        String deptName;

        if (projectIds != null && projectIds.isEmpty()) {
            return page;
        }
        startDate = LocalDateTimeUtil.parseDate(saturationStatisticsDTO.getStartTime());
        endDate = LocalDateTimeUtil.parseDate(saturationStatisticsDTO.getEndTime());
        if (checkUserDetails) {
            // 如果是根据用户查询某人的详情，直接进行查询得到page
            userIdParam = saturationStatisticsDTO.getUserId();
            if (userIdParam == null) {
                return page;
            }
            deptIds = filterDeptIdsByDataScope(saturationStatisticsDTO.getDeptIds(), false);
            if (deptIds.isEmpty()) {
                return page;
            }
            saturationStatisticsDTO.setDeptIds(deptIds);
            page = dailyPaperEntryMapper.selectPageSituationAnalysis(
                    saturationStatisticsDTO, ImmutableList.of(userIdParam), projectIds, page
            );
        } else {
            // 获取可用部门和人员，然后查询得到page
            deptIdsAndUserIds = findDeptIdsAndUserIdsAvailable(
                    saturationStatisticsDTO.getDeptIds(),
                    projectIds,
                    startDate,
                    endDate,
                    onlySelectedDeptFlag
            );
            if (deptIdsAndUserIds == null) {
                return page;
            }
            saturationStatisticsDTO.setDeptIds(deptIdsAndUserIds.getFirst());
            // 查库
            saturationStatisticsDTO.setApprovalStatus(ApprovalStatusEnum.YTG.getValue());
            page = dailyPaperEntryMapper.selectPageSituationAnalysis(
                    saturationStatisticsDTO, deptIdsAndUserIds.getSecond(), projectIds, page
            );
        }
        records = page.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return page;
        }
        userIds = records.stream()
                .map(sa -> NumberUtils.toLong(sa.getUserId(), -1L))
                .collect(Collectors.toSet());
        startTime = saturationStatisticsDTO.getStartTime();
        endTime = saturationStatisticsDTO.getEndTime();
        // 获取应出勤天数
        userAttendanceMap = calculator.calcAttendance(
                LocalDateTimeUtil.parseDate(startTime),
                LocalDateTimeUtil.parseDate(endTime),
                userIds
        );
        //查询人员调休数据
        Map<Long, BigDecimal> leaveSumMap  = new HashMap<>();
        Map<Long, BigDecimal> leaveProjectUserMap  = new HashMap<>();
        List<CompensatoryLeaveSumVO> leaveSumUser = compensatoryLeaveDataMapper.sumLeaveByDateTimeRangeAndUserIds(startDate, endDate, new ArrayList<>(userIds), null,"3");
        List<CompensatoryLeaveSumVO> leaveProjectUser = compensatoryLeaveDataMapper.sumLeaveByDateTimeRangeAndUserIds(startDate, endDate, new ArrayList<>(userIds), projectIds,"3");
        leaveSumMap = leaveSumUser.stream().collect(Collectors.toMap(CompensatoryLeaveSumVO::getUserId, CompensatoryLeaveSumVO::getLeaveSum));
        leaveProjectUserMap = leaveProjectUser.stream().collect(Collectors.toMap(CompensatoryLeaveSumVO::getUserId, CompensatoryLeaveSumVO::getLeaveSum));
        List<CompensatoryLeaveProjectSumVO> leaveProjectUserDetails = compensatoryLeaveDataMapper.projectLeaveByDateTimeRangeAndUserIds(LocalDateTimeUtil.parseDate(startTime), LocalDateTimeUtil.parseDate(endTime),
                new ArrayList<>(userIds), projectIds,"3");
        // 获取部门信息
        deptList = remoteDeptService.getAllDeptList(false);
        deptIdMap = CollectionUtils.isEmpty(deptList) ? ImmutableMap.of() : deptList.stream()
                .collect(Collectors.toMap(DeptCacheDto::getDeptId, d -> d));
        // 换算为人天，并计算项目耗用工时
        fallbackPair = Pair.of(BigDecimal.ZERO, BigDecimal.ZERO);
        userIdAndRecordMap = Maps.newHashMapWithExpectedSize(records.size());
        for (SituationAnalysisVO record : records) {
            userId = Long.parseLong(record.getUserId());
            // 实际出勤天数和工资考核出勤天数
            attendance = userAttendanceMap.getOrDefault(userId, fallbackPair);
            if (
                    org.apache.commons.collections4.CollectionUtils.size(record.getMultiGroupByDept()) == 1
            ) {
                // 只筛选选中的部门（或者该用户只查询到了一个部门的工时），直接设置部门名称，并将分组列表属性设为空列表
                dept = deptIdMap.get(record.getDeptId());
                deptName = dept == null ? StringUtils.EMPTY : dept.getName();
                record.setMultiGroupByDept(ImmutableList.of());
            } else {
                // 筛选所有具有工时的部门，此结果为聚合结果，设置空部门名称，并拼接随机ID
                deptName = StringUtils.EMPTY;
                record.setId(record.getId() + RandomStringUtils.random(6));
            }
            if(YesOrNoEnum.NO.getValue().equals(saturationStatisticsDTO.getFlag())){
                CompensatoryLeaveProjectSumVO leaveProjectSumVO = leaveProjectUserDetails.stream().filter(e -> record.getUserId().equals(e.getUserId().toString()) && record.getProjectId().equals(e.getProjectId())).findFirst().orElse(null);
                record.setLeaveHours(Optional.ofNullable(leaveProjectSumVO).isPresent()?leaveProjectSumVO.getLeaveSum():BigDecimal.ZERO);
            }else{
                record.setLeaveHours(leaveProjectUserMap.getOrDefault(userId,BigDecimal.ZERO));
            }
            buildUserSituationAnalysisVO(leaveSumMap,record, attendance, deptName, onlySelectedDeptFlag);
            userIdAndRecordMap.put(userId, record);
        }
        if (checkUserDetails) {
            // 如果是查看某个用户在某个部门下的详情，那么到此为止，不需要考虑其他部门
            // 如果只筛选了单一部门，同上
            return page;
        }
        // 如果非查看某个用户在某个部门下的详情，则还需要分别查询每个人的每个部门的工时饱和度情况
        Map<Long, BigDecimal> finalLeaveSumMap = leaveSumMap;
        dailyPaperEntryMapper.selectSituationAnalysisMultiDept(
                saturationStatisticsDTO,
                userIdAndRecordMap.keySet(),
                projectIds
        ).forEach(r -> {
            SituationAnalysisVO data = userIdAndRecordMap.get(NumberUtils.toLong(r.getUserId()));
            List<SituationAnalysisVO> multiDeptDataList;
            DeptCacheDto d;
            Long deptId;
            Pair<BigDecimal, BigDecimal> attendancePair;

            if (data == null) {
                return;
            }
            deptId = r.getDeptId();
            d = deptIdMap.get(deptId);
            multiDeptDataList = data.getMultiGroupByDept();
            if (multiDeptDataList == null || multiDeptDataList.size() == 0) {
                multiDeptDataList = Lists.newArrayList();
                data.setMultiGroupByDept(multiDeptDataList);
            }
            attendancePair = userAttendanceMap.getOrDefault(Long.parseLong(r.getUserId()), fallbackPair);
            buildUserSituationAnalysisVO(finalLeaveSumMap,
                    r,
                    attendancePair,
                    d == null ? StringUtils.EMPTY : d.getName(),
                    onlySelectedDeptFlag
            );
            // 子结点不需要用户姓名，置空
            r.setUserName(StringUtils.EMPTY);
            multiDeptDataList.add(r);
        });
        // 最后遍历一遍，将只有一个子结点的父级打平
        records.forEach(r -> {
            DeptCacheDto d;

            if (org.apache.commons.collections4.CollectionUtils.size(r.getMultiGroupByDept()) == 1) {
                d = deptIdMap.get(r.getDeptId());
                r.setDeptName(d == null ? StringUtils.EMPTY : d.getName());
                r.setMultiGroupByDept(ImmutableList.of());
            }
        });

        return page;
    }

    /**
     * 构建人员饱和度VO对象
     * @param leaveSumMap 总调休不过滤项目   对象
     * @param vo vo对象
     * @param attendanceDaysAndSalaryAttendanceDays 实际出勤天数和工资考核出勤天数
     * @param deptName 部门名称
     * @param onlySelectedDeptFlag 是否只看筛选条件对应的部门数据
     */
    private void buildUserSituationAnalysisVO(Map<Long, BigDecimal> leaveSumMap,
            SituationAnalysisVO vo,
            Pair<BigDecimal, BigDecimal> attendanceDaysAndSalaryAttendanceDays,
            String deptName,
            Boolean onlySelectedDeptFlag
    ) {
        BigDecimal attendanceDays = attendanceDaysAndSalaryAttendanceDays.getFirst();
        BigDecimal leaveHours = vo.getLeaveHours();
        //实际出勤天数
        vo.setNormalHours(CommonUtils.unitConversion(vo.getNormalHours()));
        vo.setAddedHours(CommonUtils.unitConversion(vo.getAddedHours()));
        vo.setWorkOvertimeHours(CommonUtils.unitConversion(vo.getWorkOvertimeHours()));
        vo.setRestOvertimeHours(CommonUtils.unitConversion(vo.getRestOvertimeHours()));
        vo.setHolidayOvertimeHours(CommonUtils.unitConversion(vo.getHolidayOvertimeHours()));
        vo.setLeaveHours(CommonUtils.unitConversion(leaveHours));
        vo.setProjectHours(vo.getNormalHours().add(
                vo.getAddedHours()).setScale(4, RoundingMode.HALF_UP)
        );
        vo.setProjectShareHours(vo.getNormalHours().add(vo.getRestOvertimeHours()).add(vo.getHolidayOvertimeHours()).add(vo.getLeaveHours()).setScale(4, RoundingMode.HALF_UP));
        vo.setAttendanceDays(attendanceDays);
        vo.setSalaryAttendanceDays(attendanceDaysAndSalaryAttendanceDays.getSecond());
        // 计算工时饱和度
        vo.setHourSaturation(CommonUtils.calculationHourSaturation(vo.getProjectHours(), attendanceDays));
        vo.setDeptName(deptName);
        vo.setOnlySelectedDeptFlag(onlySelectedDeptFlag);
    }

    /**
     * 导出
     *
     * @param saturationStatisticsDTO 查询参数
     * @param response 响应对象
     * @throws IOException IO异常
     */
    @Override
    public void export(SaturationStatisticsDTO saturationStatisticsDTO, HttpServletResponse response) throws IOException {
        //部门搜索条件为空，返回空数据
        if(saturationStatisticsDTO.getDeptIds().isEmpty()){
            return ;
        }
        String startTime = saturationStatisticsDTO.getStartTime();
        String endTime = saturationStatisticsDTO.getEndTime();
        List<Long> deptIds = saturationStatisticsDTO.getDeptIds();
        Map<Long, Pair<BigDecimal, BigDecimal>> userAttendanceMap = calculator.calcAttendance(
                LocalDateTimeUtil.parseDate(startTime),
                LocalDateTimeUtil.parseDate(endTime)
        );
        Pair<BigDecimal, BigDecimal> attendancePair;

        saturationStatisticsDTO.setFlag(YesOrNoEnum.NO.getValue());
        SaturationAndTotalVO deptDetailData = this.getDeptDetailData(saturationStatisticsDTO);
        List<SituationAnalysisDeptVO> list2 = deptDetailData.getList();
        // 获取所有可用项目
        List<Long> projectIds = findProjectIdsByProjectIncomeTypes(saturationStatisticsDTO.getProjectIncomeTypes());
        if (projectIds != null && projectIds.isEmpty()) {
            return;
        }
        // 先获取子集部门
        Pair<List<Long>, List<Long>> deptIdsAndUserIdsAvailable = findDeptIdsAndUserIdsAvailable(
                deptIds,
                projectIds,
                LocalDateTimeUtil.parseDate(saturationStatisticsDTO.getStartTime()),
                LocalDateTimeUtil.parseDate(saturationStatisticsDTO.getEndTime()),
                saturationStatisticsDTO.getOnlySelectedDeptFlag()
        );
        if (deptIdsAndUserIdsAvailable == null) {
            return;
        }
        List<Long> allDeptIds = deptIdsAndUserIdsAvailable.getFirst();
        saturationStatisticsDTO.setDeptIds(allDeptIds);
        // 查库
        saturationStatisticsDTO.setFlag(YesOrNoEnum.NO.getValue());
        saturationStatisticsDTO.setApprovalStatus(ApprovalStatusEnum.YTG.getValue());
        List<SituationAnalysisVO> list = dailyPaperEntryMapper.selectPageSituationAnalysis(
                saturationStatisticsDTO,
                deptIdsAndUserIdsAvailable.getSecond(),
                projectIds
        );
        List<Long> userIds = list.stream().map(e->Long.parseLong(e.getUserId())).collect(Collectors.toList());
        List<CompensatoryLeaveProjectSumVO> leaveProjectUser = compensatoryLeaveDataMapper.projectLeaveByDateTimeRangeAndUserIds(LocalDateTimeUtil.parseDate(startTime), LocalDateTimeUtil.parseDate(endTime),
                userIds, projectIds,"3");

        // 换算为人天，并计算项目耗用工时
        Pair<BigDecimal, BigDecimal> fallbackPair = Pair.of(BigDecimal.ZERO, BigDecimal.ZERO);
        for (SituationAnalysisVO record : list) {
            CompensatoryLeaveProjectSumVO leaveProjectSumVO = leaveProjectUser.stream().filter(e -> record.getUserId().equals(e.getUserId().toString()) && record.getProjectId().equals(e.getProjectId())).findFirst().orElse(null);
            attendancePair = userAttendanceMap.getOrDefault(Long.parseLong(record.getUserId()), fallbackPair);
            record.setNormalHours(CommonUtils.unitConversion(record.getNormalHours()));
            record.setAddedHours(CommonUtils.unitConversion(record.getAddedHours()));
            record.setWorkOvertimeHours(CommonUtils.unitConversion(record.getWorkOvertimeHours()));
            record.setRestOvertimeHours(CommonUtils.unitConversion(record.getRestOvertimeHours()));
            record.setHolidayOvertimeHours(CommonUtils.unitConversion(record.getHolidayOvertimeHours()));
            record.setProjectHours(record.getNormalHours()
                    .add(record.getAddedHours())
                    .setScale(4, RoundingMode.HALF_UP)
            );
            record.setLeaveHours(CommonUtils.unitConversion(Optional.ofNullable(leaveProjectSumVO).isPresent()?leaveProjectSumVO.getLeaveSum():BigDecimal.ZERO));
            record.setProjectShareHours(record.getNormalHours()
                    .add(record.getRestOvertimeHours()).add(record.getHolidayOvertimeHours()).add(record.getLeaveHours())
                    .setScale(4, RoundingMode.HALF_UP)
            );
            record.setAttendanceDays(attendancePair.getFirst());
            record.setSalaryAttendanceDays(attendancePair.getSecond());
            // 计算工时饱和度
            record.setHourSaturation(
                    CommonUtils.calculationHourSaturation(record.getProjectHours(), attendancePair.getFirst())
            );
        }
        //导出【数量】
        bcpLoggerUtils.log(FunctionConstants.HOUR_SATURATION, LogContentEnum.EXPORT_DATA,
                list2.size());
        OutputStream os = null;
        ExcelWriter excelWriter = null;
        try {
            // 设置文件名
            response.setHeader("content-Type", "application/ms-excel");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("企业信息.xlsx", "UTF-8"));
            // excel写入对象
            os = response.getOutputStream();
            excelWriter = EasyExcel.write(os).build();
            WriteSheet mainSheet2 = EasyExcel.writerSheet(0, "工时饱和度数据导出").head(SituationAnalysisDeptVO.class).build();
            excelWriter.write(list2, mainSheet2);

            WriteSheet mainSheet = EasyExcel.writerSheet(1, "详细数据导出").head(SituationAnalysisVO.class).build();
            excelWriter.write(list, mainSheet);

            response.setCharacterEncoding("UTF-8");

        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
            if (os != null) {
                os.flush();
                os.close();
            }
        }
    }

    /**
     * 通过数据权限，对传入的部门id列表进行过滤
     * @param selectDeptIds 部门id列表
     * @param returnAllIfNull 如果传入的部门列表为空，自动返回权限范围内的所有部门
     * @return 被过滤后的部门ID列表
     */
    private List<Long> filterDeptIdsByDataScope(List<Long> selectDeptIds, boolean returnAllIfNull) {
        List<Long> deptIdsAvailable;
        SysUserDataScopeVO dataScope = projectScopeHandle.findDataScope();
        log.info("selectDeptIds:{}", selectDeptIds);

        log.info("dataScope:{},{},{}", dataScope.getIsAll(), dataScope.getUserIdList(), dataScope.getDeptIdList());

        if (dataScope.getIsAll()) {
            if (CollectionUtils.isEmpty(selectDeptIds)) {
                if (returnAllIfNull) {
                    deptIdsAvailable = ObjectUtils.<List<DeptCacheDto>>defaultIfNull(
                            remoteDeptService.getAllDeptList(false), ImmutableList.of()
                    ).stream().map(DeptDto::getDeptId).collect(Collectors.toList());
                } else {
                    deptIdsAvailable = selectDeptIds;
                }
            } else {
                deptIdsAvailable = selectDeptIds;
            }
        } else {
            if (CollectionUtils.isEmpty(selectDeptIds)) {
                deptIdsAvailable = returnAllIfNull ? dataScope.getDeptIdList() : ImmutableList.of();
            } else {
                deptIdsAvailable = selectDeptIds.stream()
                        .filter(deptId -> dataScope.getDeptIdList().contains(deptId))
                        .distinct()
                        .collect(Collectors.toList());
            }
        }

        log.info("deptIdsAvailable:{}", deptIdsAvailable);

        return deptIdsAvailable;
    }


    @Override
    public R<PaneSaturationAnalysisVO> analysisTotal(DailyPaperAnalysisDTO dto) {
        //部门搜索条件为空，返回空数据
        List<Long> deptIds = dto.getDeptIds();
        if(deptIds.isEmpty()){
            return R.ok(PaneSaturationAnalysisVO.empty());
        }
        List<Long> projectIds;
        Pair<List<Long>, List<Long>> deptIdsAndUserIds;
        List<Long> userIds;
        LocalDate startDate;
        LocalDate endDate;
        BigDecimal delaySubmitHours;
        BigDecimal auditHours;
        PaneSaturationAnalysisVO result;

        // 筛选出所有可查询项目
        projectIds = findProjectIdsByProjectIncomeTypes(dto.getProjectIncomeTypes());
        if (projectIds != null && projectIds.isEmpty()) {
            return R.ok(PaneSaturationAnalysisVO.empty());
        }
        // 根据数据权限过滤出可查询部门和人员
        startDate = LocalDateTimeUtil.parseDate(dto.getStartTime());
        endDate = LocalDateTimeUtil.parseDate(dto.getEndTime());
        deptIdsAndUserIds = findDeptIdsAndUserIdsAvailable(
                deptIds, projectIds, startDate, endDate, dto.getOnlySelectedDeptFlag()
        );
        if (deptIdsAndUserIds == null) {
            return R.ok(PaneSaturationAnalysisVO.empty());
        }
        // 查询并包装最终结果
        result = new PaneSaturationAnalysisVO();
        deptIds = deptIdsAndUserIds.getFirst();
        userIds = deptIdsAndUserIds.getSecond();
        delaySubmitHours =
                dailyPaperMapper.saturationAnalysisBySubmissionDateRangeAndDeptIdsAndUserIdsAndProjectIds(
                        startDate, endDate, deptIds, userIds, projectIds
                );
        auditHours = dailyPaperEntryMapper.saturationAnalysisBySubmissionDateRangeAndDeptIdsAndUserIdsAndProjectIds(
                startDate, endDate, deptIds, userIds, projectIds
        );
        result.setDelaySubmit(CommonUtils.unitConversion(delaySubmitHours));
        result.setAuditTime(CommonUtils.unitConversion(auditHours));

        return R.ok(result);
    }

    /**
     * 根据项目收入类型参数查询项目ID列表
     * @param projectIncomeTypes 项目收入类型列表
     * @return 项目ID列表（如果返回null，说明未筛选参数；如果返回空列表，说明未查询到可用项目）
     */
    @Nullable
    private List<Long> findProjectIdsByProjectIncomeTypes(@Nullable List<Integer> projectIncomeTypes) {
        List<Integer> insideProjectParams;
        boolean includeSpecial = false;
        Set<Long> specialProjectIds;
        List<Long> projectIds;

        if (CollectionUtils.isEmpty(projectIncomeTypes)) {
            return null;
        }
        insideProjectParams = Lists.newArrayListWithCapacity(2);
        for (Integer type : projectIncomeTypes) {
            if (EnumUtils.valueEquals(type, ProjectIncomeTypeEnum.INSIDE_SPECIAL)) {
                includeSpecial = true;
            } else {
                insideProjectParams.add(type);
                if(EnumUtils.valueEquals(type, ProjectIncomeTypeEnum.OUTSIDE)) {
                    // 部分外部项目枚举值不规范，为2，也要考虑在内
                    insideProjectParams.add(2);
                }
            }
        }
        specialProjectIds = entityOptionMapper.findIdSetBySign(EntitySign.SPECIAL_PROJECT);
        if(insideProjectParams.isEmpty()){
            projectIds = new ArrayList<>();
        }else{
            projectIds = projectInfoMapper.findIdByInsideProjects(insideProjectParams);
        }

        if (includeSpecial) {
            //包含特殊项目
            projectIds.addAll(specialProjectIds);
        } else {
            //不包含特殊项目
            projectIds = projectIds.stream().filter(e->!specialProjectIds.contains(e)).collect(Collectors.toList());
        }
        return projectIds;
    }

    /**
     * 根据参数部门ID列表和项目ID列表，获取所有可查询部门ID列表和用户ID列表
     * @param deptIds 部门ID列表
     * @param projectIds 项目ID列表
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 部门ID列表+用户ID列表（如果返回null，说明没有可查询的数据）
     */
    private Pair<List<Long>, List<Long>> findDeptIdsAndUserIdsAvailable(
            List<Long> deptIds,
            @Nullable List<Long> projectIds,
            LocalDate startDate,
            LocalDate endDate,
            @Nullable Boolean onlySelectedDeptFlag
    ) {
        // 根据部门权限过滤
        List<Long> deptIdsInDataScope = filterDeptIdsByDataScope(deptIds, true);
        List<Long> userIds;
        List<Long> deptIdsAvailable;

        if (deptIdsInDataScope.isEmpty()) {
            return null;
        }
        // 查询用户ID列表
        userIds = rosterMapper.findIdByDeptIds(deptIdsInDataScope);
        if (userIds.isEmpty()) {
            return null;
        }
        // 查询可用项目列表
        // 根据标志，根据用户ID列表反查最终部门列表（一些在当前部门的用户，可能是从其他部门调过来的，因此最终可查部门可能比当前部门多）
        List<org.apache.commons.lang3.tuple.Pair<Long, Long>> userDeptIds = dailyPaperEntryMapper.findDeptIdsBySubmissionDateRangeAndApprovalStatusAndUserIdsAndProjectIds(
                startDate, endDate, ApprovalStatusEnum.YTG.getValue(), userIds, projectIds
        );
        //过滤用户所有部门不在查询部门范围内的数据
        List<Long> userIdsAvailable = userDeptIds.stream().filter(e -> deptIds.contains(e.getRight())).map(e -> e.getLeft()).collect(Collectors.toList());
        deptIdsAvailable = BooleanUtils.toBoolean(onlySelectedDeptFlag) ?
                deptIdsInDataScope :
                userDeptIds.stream().filter(e -> userIdsAvailable.contains(e.getLeft())).map(e -> e.getRight()).collect(Collectors.toList());
        if (deptIdsAvailable.isEmpty()) {
            return null;
        }
        deptIdsAvailable = deptIdsAvailable.stream().distinct().collect(Collectors.toList());
        userIds = userIds.stream().distinct().collect(Collectors.toList());

        return Pair.of(deptIdsAvailable, userIds);
    }

    @Override
    public Page<DailyReviewProjectAuditPageVO> findBySaturation(
            Page<DailyReviewProjectAuditPageVO> page, DailyPaperAnalysisDTO dto
    ) {
        List<Long> deptIds = dto.getDeptIds();
        List<Long> projectIds;
        Pair<List<Long>, List<Long>> deptIdsAndUserIdsAvailable;
        Page<DailyReviewProjectAuditPageVO> bySaturation;
        List<DailyReviewProjectAuditPageVO> records;
        Set<Long> userIds;
        Table<Long, LocalDate, List<TomorrowPlanPaperEntry>> userIdAndDateAndLastDayEntryTable;
        List<TomorrowPlanPaperEntry> tomorrowPlanPaperEntry;
        LocalDate startDate;
        LocalDate endDate;

        if (CollUtil.isEmpty(deptIds)) {
            return page;
        }
        projectIds = findProjectIdsByProjectIncomeTypes(dto.getProjectIncomeTypes());
        startDate = LocalDateTimeUtil.parseDate(dto.getStartTime());
        endDate = LocalDateTimeUtil.parseDate(dto.getEndTime());
        deptIdsAndUserIdsAvailable = findDeptIdsAndUserIdsAvailable(
                deptIds,
                projectIds,
                startDate,
                endDate,
                dto.getOnlySelectedDeptFlag()
        );
        if (deptIdsAndUserIdsAvailable == null){
            return page;
        }
        dto.setDeptIds(deptIdsAndUserIdsAvailable.getFirst());
        bySaturation = dailyPaperEntryMapper.findBySaturation(Page.of(dto.getPageNumber(), dto.getPageSize()), dto, projectIds);
        records = bySaturation.getRecords();
        if (!records.isEmpty()) {
            userIds = Sets.newHashSet();
            for (DailyReviewProjectAuditPageVO record : records) {
                record.setAddedHours(CommonUtils.unitConversion(record.getAddedHours()));
                record.setNormalHours(CommonUtils.unitConversion(record.getNormalHours()));
                userIds.add(record.getUserId());
            }
            userIdAndDateAndLastDayEntryTable = findLastDayPlanEntries(startDate, endDate, userIds);
            for (DailyReviewProjectAuditPageVO record : records) {
                tomorrowPlanPaperEntry = userIdAndDateAndLastDayEntryTable.get(
                        record.getUserId(), record.getSubmissionDate()
                );
                record.setYesterdayPlan(StringUtils.EMPTY);
                if (tomorrowPlanPaperEntry == null) {
                    continue;
                }
                tomorrowPlanPaperEntry.stream()
                        .filter(t -> Objects.equals(t.getTaskId(), record.getTaskId()))
                        .findFirst()
                        .ifPresent(t -> record.setYesterdayPlan(Strings.nullToEmpty(t.getDescription())));
            }
        }

        return bySaturation;
    }

    @Override
    public List<SaturationExportVO> exportBySaturation(DailyPaperAnalysisDTO dto) {
        List<Long> deptIds = dto.getDeptIds();
        List<Long> projectIds;
        Pair<List<Long>, List<Long>> deptIdsAndUserIdsAvailable;
        List<SaturationExportVO> records;
        Set<Long> userIds;
        Table<Long, LocalDate, List<TomorrowPlanPaperEntry>> userIdAndDateAndLastDayEntryTable;
        List<TomorrowPlanPaperEntry> tomorrowPlanPaperEntry;
        LocalDate startDate;
        LocalDate endDate;

        if (CollUtil.isEmpty(deptIds)) {
            return ImmutableList.of();
        }
        projectIds = findProjectIdsByProjectIncomeTypes(dto.getProjectIncomeTypes());
        startDate = LocalDateTimeUtil.parseDate(dto.getStartTime());
        endDate = LocalDateTimeUtil.parseDate(dto.getEndTime());
        deptIdsAndUserIdsAvailable = findDeptIdsAndUserIdsAvailable(
                deptIds,
                projectIds,
                startDate,
                endDate,
                dto.getOnlySelectedDeptFlag()
        );
        if (deptIdsAndUserIdsAvailable == null){
            return ImmutableList.of();
        }
        dto.setDeptIds(deptIdsAndUserIdsAvailable.getFirst());
        records = dailyPaperEntryMapper.exportBySaturation(dto, projectIds);
        if (!records.isEmpty()) {
            userIds = Sets.newHashSet();
            for (SaturationExportVO record : records) {
                record.setAddedHours(CommonUtils.unitConversion(record.getAddedHours()));
                record.setNormalHours(CommonUtils.unitConversion(record.getNormalHours()));
                userIds.add(record.getUserId());
            }
            userIdAndDateAndLastDayEntryTable = findLastDayPlanEntries(startDate, endDate, userIds);
            for (SaturationExportVO record : records) {
                tomorrowPlanPaperEntry = userIdAndDateAndLastDayEntryTable.get(
                        record.getUserId(), record.getSubmissionDate()
                );
                record.setYesterdayPlan(StringUtils.EMPTY);
                if (tomorrowPlanPaperEntry == null) {
                    continue;
                }
                tomorrowPlanPaperEntry.stream()
                        .filter(t -> Objects.equals(t.getTaskId(), record.getTaskId()))
                        .findFirst()
                        .ifPresent(t -> record.setYesterdayPlan(Strings.nullToEmpty(t.getDescription())));
            }
        }

        return records;
    }

    /**
     * 查询昨日计划
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param userIds 用户ID列表
     * @return 用户id-日报条目提交日期-昨日计划
     */
    private Table<Long, LocalDate, List<TomorrowPlanPaperEntry>> findLastDayPlanEntries(
            LocalDate startDate,
            LocalDate endDate,
            Collection<Long> userIds
    ) {
        List<TomorrowPlanPaperEntry> resultList;
        Table<Long, LocalDate, List<TomorrowPlanPaperEntry>> result;

        result = HashBasedTable.create();
        resultList = tomorrowPlanPaperEntryMapper.findBySubmissionDateRangeAndUserIds(
                startDate, endDate, userIds
        );
        resultList.forEach(e -> {
            Long userId = e.getUserId();
            LocalDate submissionDate = e.getSubmissionDate();
            List<TomorrowPlanPaperEntry> list = result.get(userId, submissionDate);

            if (list == null) {
                list = Lists.newArrayList();
                result.put(userId, submissionDate, list);
            }
            list.add(e);
        });

        return result;
    }

}
