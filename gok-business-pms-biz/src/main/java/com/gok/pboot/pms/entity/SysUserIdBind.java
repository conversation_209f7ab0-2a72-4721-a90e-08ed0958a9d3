package com.gok.pboot.pms.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.gok.pboot.pms.common.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 三方登录用户ID关联关系
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("三方登录用户ID关联关系")
public class SysUserIdBind extends BaseEntity<Long> {

    @ApiModelProperty(value = "用户ID")
    private Long userId;

    @ApiModelProperty(value = "账号")
    private String username;

    @ApiModelProperty(value = "微信OpenId")
    private String wxOpenId;

    @TableField(fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @TableField(fill = FieldFill.UPDATE)
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    public static SysUserIdBind of(Long userId, String username, String wxOpenId){
        SysUserIdBind result = new SysUserIdBind();

        result.setUserId(userId);
        result.setUsername(username);
        result.setWxOpenId(wxOpenId);

        return result;
    }
}
