package com.gok.pboot.pms.entity.vo;


import com.gok.pboot.pms.entity.OvertimeLeaveData;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 加班、请假、销假数据同步
 *
 * <AUTHOR>
 * @since 2022-10-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class OvertimeLeaveDataVO {

    /**
     * 用户编号
     */
    private Long userId;

    /**
     * 请假集合/每天
     */
    private List<OvertimeLeaveData> overtimeLeaveDatas;


}
