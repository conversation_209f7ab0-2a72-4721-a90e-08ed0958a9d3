package com.gok.pboot.pms.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 商机进展记录表（OA同步）(BusinessProgressInfo)表实体类
 *
 * <AUTHOR>
 * @since 2023-11-24 09:20:52
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("business_progress_info")
public class BusinessProgressInfo extends BeanEntity<Long> {

    /**
     * 商机ID(项目ID)
     */
    private Long businessId;

    /**
     * 商机名称(项目名称)
     */
    private String businessName;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 商机进展
     */
    private String businessProgress;

    /**
     * 下一步工作计划
     */
    private String nextStepForwardPlan;

    /**
     * 下一步计划完成时间
     */
    private String nextStepPlanFtime;

    /**
     * 遇到的问题及所需支持(存在问题及需要协同)
     */
    private String yddwtjsxdzc;

    /**
     * 关键决策人支持情况
     */
    private String supportFromKeyDecision;

    /**
     * 项目需求是否明确
     */
    private String projectRequirementClear;

    /**
     * 预算情况
     */
    private String budgetSituation;

    /**
     * 预计签单金额
     */
    private BigDecimal expectedOrderAmount;

    /**
     * 预计签单时间
     */
    private String expectedCompleteTime;

    /**
     * 商机阶段
     */
    private String businessStage;

    /**
     * 项目里程碑
     */
    private String businessMilestone;

    /**
     * 商机状态
     */
    private String businessStatus;

    /**
     * 项目销售人员ID(客户经理ID)
     */
    private Long salesmanUserId;

    /**
     * 项目销售人员(客户经理姓名)
     */
    private String projectSalesperson;

    /**
     * 提交人ID
     */
    private Long submitUserId;

    /**
     * 提交人
     */
    private String submitUser;

    /**
     * 提交人头像
     */
    private String submitUserAvatar;

    /**
     * 提交人所在部门id
     */
    private Long submitUserDeptId;

    /**
     * 提交人所在部门
     */
    private String submitUserDept;

    /**
     * 提交时间
     */
    private String submitTime;

}

