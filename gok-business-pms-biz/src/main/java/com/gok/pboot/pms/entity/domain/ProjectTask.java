/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 */

package com.gok.pboot.pms.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import com.gok.pboot.pms.entity.dto.ProjectTaskDTO;
import com.gok.pboot.pms.entity.dto.ProjectTaskTimeDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 项目任务
 *
 * <AUTHOR> generator
 * @date 2023-08-18 10:07:28
 */
@Data
@TableName("project_task")
@EqualsAndHashCode(callSuper = true)
public class ProjectTask extends BeanEntity<Long> {

    ///**
    // * 主键ID
    // */
    //@TableId(type = IdType.ASSIGN_ID)
    //private Long id;

    /**
     * 父级ID
     */
    private Long parentId;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 层级
     */
    private Integer treeLevel;

    /**
     * 父级到子级的全路径
     */
    private String treePathIds;

    /**
     * 任务标题
     */
    private String title;

    /**
     * 负责人ID
     */
    private Long managerUserId;

    /**
     * 负责人姓名
     */
    private String managerUserName;

    /**
     * 状态（0=未开始，1=进行中，2=已完成）
     * @see com.gok.pboot.pms.enumeration.TaskWorkingState
     */
    private Integer state;

    /**
     * 计划开始时间
     */
    private LocalDateTime expectedStartTime;

    /**
     * 计划结束时间
     */
    private LocalDateTime expectedEndTime;

    /**
     * 实际开始时间
     */
    private LocalDateTime actualStartTime;

    /**
     * 实际结束时间
     */
    private LocalDateTime actualEndTime;

    /**
     * 是否里程碑
     */
    private Boolean milestone;

    /**
     * 详细描述
     */
    private String content;

    /**
     * 分组ID
     */
    private Long groupId;

    public static ProjectTask save(ProjectTaskDTO request){
        ProjectTask result = new ProjectTask();

        // 父级id
        if(request.getParentId() != null){
            result.setParentId(request.getParentId());
        }

        // 项目id
        result.setProjectId(request.getProjectId());
        // 标题
        result.setTitle(request.getTitle());
        // 负责人id
        result.setManagerUserId(request.getManagerUserId());
        // 负责人姓名
        result.setManagerUserName(request.getManagerUserName());
        // 分组ID
        result.setGroupId(0L);
        // 计划开始时间
        result.setExpectedStartTime(request.getExpectedStartTime());
        // 计划结束时间
        result.setExpectedEndTime(request.getExpectedEndTime());
        // 实际开始时间
        if(request.getActualStartTime() != null){
            result.setActualStartTime(request.getActualStartTime());
        }
        // 实际结束时间
        if (request.getActualEndTime() != null){
            result.setActualEndTime(request.getActualEndTime());
        }
        // 是否里程碑
        result.setMilestone(request.getMilestone());
        // 详细描述
        result.setContent(request.getContent());

        return result;
    }

    public static ProjectTask update(ProjectTaskDTO request){
        ProjectTask result = new ProjectTask();
        //Long projectId = request.getProjectId();
        String title = request.getTitle();
        Long managerUserId = request.getManagerUserId();
        String managerUserName = request.getManagerUserName();
        LocalDateTime expectedStartTime = request.getExpectedStartTime();
        LocalDateTime expectedEndTime = request.getExpectedEndTime();
        LocalDateTime actualStartTime = request.getActualStartTime();
        LocalDateTime actualEndTime = request.getActualEndTime();
        Boolean milestone = request.getMilestone();
        String content = request.getContent();

        result.setId(request.getTaskId());
        //result.setProjectId(projectId);
        if (title != null) {
            result.setTitle(title);
        }
        if (managerUserId != null) {
            // 写入负责人姓名
            result.setManagerUserId(managerUserId);
            result.setManagerUserName(managerUserName);
        }
        if (expectedStartTime != null) {
            result.setExpectedStartTime(expectedStartTime);
        }
        if (expectedEndTime != null) {
            result.setExpectedEndTime(expectedEndTime);
        }
        if (actualStartTime != null) {
            result.setActualStartTime(actualStartTime);
        }
        if (actualEndTime != null) {
            result.setActualEndTime(actualEndTime);
        }
        if (milestone != null) {
            result.setMilestone(milestone);
        }
        if (content != null) {
            result.setContent(content);
        }
        return result;
    }

    public static ProjectTask updateTime(ProjectTaskTimeDTO request) {
        ProjectTask result = new ProjectTask();
        LocalDateTime actualStartTime = request.getActualStartTime();
        LocalDateTime actualEndTime = request.getActualEndTime();

        result.setId(request.getTaskId());
        result.setActualStartTime(actualStartTime);
        result.setActualEndTime(actualEndTime);
        return result;
    }
}
