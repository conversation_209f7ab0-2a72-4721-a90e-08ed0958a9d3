package com.gok.pboot.pms.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.module.excel.api.annotation.ResponseExcel;
import com.gok.module.excel.api.enums.ExcelDateEnum;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.entity.dto.CustomerCommunicationRecordDTO;
import com.gok.pboot.pms.entity.vo.CCRExportExcelVO;
import com.gok.pboot.pms.entity.vo.CustomerCommunicationRecordVO;
import com.gok.pboot.pms.service.CustomerCommunicationRecordService;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 客户沟通记录
 *
 * <AUTHOR>
 * @date 2023/11/20
 * @menu 客户沟通记录
 */
@RestController
@RequestMapping("/customerCommunicationRecord")
@AllArgsConstructor
public class CustomerCommunicationRecordController {

    private final CustomerCommunicationRecordService service;

    /**
     * 获取客户沟通记录-分页
     *
     * @param pageRequest 分页请求对象
     * @param filter 客户沟通记录查询DTO
     * @return {@link ApiResult}<{@link Page}<{@link CustomerCommunicationRecordVO}>>
     */
    @PreAuthorize("@pms.hasPermission('COMMUNICAE_RECORDS')")
    @GetMapping("/findPage")
    public ApiResult<Page<CustomerCommunicationRecordVO>> findPage(PageRequest pageRequest, CustomerCommunicationRecordDTO filter) {
        return ApiResult.success(service.findPage(pageRequest, filter));
    }

    /**
     * 通过id获取客户沟通记录详情
     *
     * @param id 客户沟通记录id
     * @return {@link ApiResult}<{@link CustomerCommunicationRecordVO}>
     */
    @GetMapping("/{id}")
    public ApiResult<CustomerCommunicationRecordVO> findById(@PathVariable("id") Long id) {
        return ApiResult.success(service.findById(id));
    }

    /**
     * 导出客户沟通记录列表数据
     *
     * @param pageRequest 分页请求对象
     * @param filter 客户沟通记录查询DTO
     * @return {@link List}<{@link CCRExportExcelVO}>
     */
    @PreAuthorize("@pms.hasPermission('COMMUNICAE_RECORDS')")
    @GetMapping("/export")
    @ApiOperation(value = "导出客户沟通记录列表数据", notes = "导出客户沟通记录列表数据")
    @ResponseExcel(name = "客户沟通记录", nameWithDate = ExcelDateEnum.DATE, dateFormat = "yyyy-MM-dd")
    public List<CCRExportExcelVO> export(PageRequest pageRequest, CustomerCommunicationRecordDTO filter) {
        return service.export(pageRequest, filter);
    }

    /**
     * 客户沟通记录文件接口
     * @param requestId 请求id
     * @return {@link ApiResult}<{@link Object}>
     */
    @GetMapping("/getFile/{requestId}")
    public ApiResult<Object> getFile(@PathVariable("requestId") Long requestId){
        return service.getFile(requestId);
    }
}
