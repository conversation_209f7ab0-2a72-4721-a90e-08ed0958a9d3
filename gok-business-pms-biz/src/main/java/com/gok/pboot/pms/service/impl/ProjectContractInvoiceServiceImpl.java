package com.gok.pboot.pms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.pboot.pms.Util.DecimalFormatUtil;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.entity.domain.ContractLedger;
import com.gok.pboot.pms.entity.domain.ContractLedgerDetail;
import com.gok.pboot.pms.entity.domain.PmsDocImageFile;
import com.gok.pboot.pms.entity.domain.ProjectContractInvoice;
import com.gok.pboot.pms.entity.vo.ContractInvoiceVo;
import com.gok.pboot.pms.entity.vo.OaFileInfoVo;
import com.gok.pboot.pms.enumeration.InvoiceStatusEnum;
import com.gok.pboot.pms.enumeration.InvoiceTaxRateEnum;
import com.gok.pboot.pms.enumeration.InvoiceTypeEnum;
import com.gok.pboot.pms.enumeration.InvoicingStatusEnum;
import com.gok.pboot.pms.mapper.ProjectContractInvoiceMapper;
import com.gok.pboot.pms.oa.OaUtil;
import com.gok.pboot.pms.service.IContractLedgerDetailService;
import com.gok.pboot.pms.service.IContractLedgerService;
import com.gok.pboot.pms.service.IPmsDocImageFileService;
import com.gok.pboot.pms.service.IProjectContractInvoiceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Slf4j
@Service
@RequiredArgsConstructor
public class ProjectContractInvoiceServiceImpl
        extends ServiceImpl<ProjectContractInvoiceMapper, ProjectContractInvoice>
        implements IProjectContractInvoiceService {

    private final IPmsDocImageFileService pmsDocImageFileService;

    private final IContractLedgerService contractLedgerService;

    private final IContractLedgerDetailService contractLedgerDetailService;

    private final OaUtil oaUtil;

    @Override
    public List<ContractInvoiceVo> getContractInvoiceVoList(Long id) {
        List<ContractInvoiceVo> contractInvoiceVos = new ArrayList<>();
        ContractLedger contractLedger = contractLedgerService.getById(id);
        if(Optional.ofNullable(contractLedger).isPresent()){
            QueryWrapper<ContractLedgerDetail> contractLedgerDetailQueryWrapper = new QueryWrapper<>();
            contractLedgerDetailQueryWrapper.lambda().eq(ContractLedgerDetail::getMainid, id)
                    .eq(ContractLedgerDetail::getFpkjzt, InvoicingStatusEnum.YES.getValue());
            List<ContractLedgerDetail> contractLedgerDetailList
                    = contractLedgerDetailService.list(contractLedgerDetailQueryWrapper);
           if(CollectionUtil.isNotEmpty(contractLedgerDetailList)){
               List<Long> ids = contractLedgerDetailList.stream().map(d -> d.getId()).collect(Collectors.toList());
               QueryWrapper<ProjectContractInvoice> queryWrapper = new QueryWrapper<>();
               queryWrapper.lambda().in(ProjectContractInvoice::getLlhtmxskbh, ids)
                       .or()
                       .eq(ProjectContractInvoice::getHtbh, contractLedger.getHtbh())
                       .orderByDesc(ProjectContractInvoice::getFprq);
               List<ProjectContractInvoice> projectContractInvoiceList = baseMapper.selectList(queryWrapper);
               if (CollectionUtil.isNotEmpty(projectContractInvoiceList)) {
                   contractInvoiceVos = BeanUtil.copyToList(projectContractInvoiceList, ContractInvoiceVo.class);
                   List<Long> docIds = new ArrayList<>();
                   contractInvoiceVos.stream().forEach(s->{
                       if(StringUtils.isNotBlank(s.getFpsmj())){
                           String[] split = StringUtils.split(s.getFpsmj(),",");
                           List<String> list = Arrays.asList(split);
                           list.stream().forEach(l->docIds.add(Long.parseLong(l)));
                       }
                   });
                   Map<Long, PmsDocImageFile> pmsDocImageFileMap=new HashMap<>();
                   if(CollUtil.isNotEmpty(docIds)){
                       List<PmsDocImageFile> pmsDocImageFileList = pmsDocImageFileService.getByDocIds(docIds);
                        pmsDocImageFileMap =
                               pmsDocImageFileList.stream().collect(Collectors.toMap(PmsDocImageFile::getDocId, Function.identity()));
                   }
                   for (int i = 0; i < contractInvoiceVos.size(); i++) {
                       ContractInvoiceVo c = contractInvoiceVos.get(i);

                       String kpsl = EnumUtils.getNameByValue(InvoiceTaxRateEnum.class, c.getKpsl());
                       c.setKpsl(StringUtils.isBlank(kpsl)?new Integer(0):Integer.parseInt(kpsl));
                       //发票金额（不含税）= 发票金额*（1-税率）。千分位分隔，末位保留2位小数四舍五入
                       //税费= 发票金额（含税）* 税率。千分位分隔，末位保留2位小数四舍五入
                       if(Optional.ofNullable(c.getFpje()).isPresent()&&Optional.ofNullable(c.getKpsl()).isPresent()){
                           c.setFpjebhs(c.getFpje().multiply(BigDecimal.valueOf(1-c.getKpsl()*0.01))
                                   .setScale(2,BigDecimal.ROUND_HALF_DOWN));
                           c.setFpsf(c.getFpje().multiply(BigDecimal.valueOf(c.getKpsl()*0.01))
                                   .setScale(2,BigDecimal.ROUND_HALF_DOWN));
                       }
                       c.setKplxTxt(EnumUtils.getNameByValue(InvoiceTypeEnum.class,c.getKplx()));
                       c.setFpztTxt(EnumUtils.getNameByValue(InvoiceStatusEnum.class,c.getFpzt()));
                       c.setKpslTxt(DecimalFormatUtil
                               .setThousandthAndTwoDecimal(new BigDecimal(c.getKpsl()),DecimalFormatUtil.ZERO)+"%");
                       c.setFpjeTxt(DecimalFormatUtil.setThousandthAndTwoDecimal(c.getFpje(),DecimalFormatUtil.ZERO));
                       c.setFpjebhsTxt(DecimalFormatUtil.setThousandthAndTwoDecimal(c.getFpjebhs(),DecimalFormatUtil.ZERO));
                       c.setFpsfTxt(DecimalFormatUtil.setThousandthAndTwoDecimal(c.getFpsf(),DecimalFormatUtil.ZERO));
                       if(CollUtil.isNotEmpty(pmsDocImageFileMap)){
                           List<OaFileInfoVo> fpsmjName=new ArrayList<>();
                           if(StringUtils.isNotBlank(c.getFpsmj())){
                               String[] split = StringUtils.split(c.getFpsmj(),",");
                               List<String> list = Arrays.asList(split);
                               for (String s: list) {
                                   PmsDocImageFile pmsDocImageFile = pmsDocImageFileMap.get(Long.parseLong(s));
                                   OaFileInfoVo oaFileInfoVo = new OaFileInfoVo();
                                   oaFileInfoVo.setFileId(pmsDocImageFile.getDocId().toString());
                                   oaFileInfoVo.setFilename(pmsDocImageFile.getImageFileName());
                                   fpsmjName.add(oaFileInfoVo);
                               }
                           }
                           c.setFpsmjName(fpsmjName);
                       }

                   }

               }
           }

        }

        return contractInvoiceVos;
    }
    @Override
   public Map<Long,List<ContractInvoiceVo>> getContractInvoiceVoListMap(List<Long> ids){
        List<ContractInvoiceVo> projectContractInvoiceList=baseMapper.getContractInvoiceVoListByContractIds(ids);
        return projectContractInvoiceList.stream().collect(Collectors.groupingBy(ContractInvoiceVo::getMainId));
    }


}