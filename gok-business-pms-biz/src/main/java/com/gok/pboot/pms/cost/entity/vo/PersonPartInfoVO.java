package com.gok.pboot.pms.cost.entity.vo;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class PersonPartInfoVO {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 工号
     */
    private String workCode;

    /**
     * 所属部门ID
     */
    private Long deptId;

    /**
     * 所属部门名称
     */
    private String deptName;

    /**
     * 岗位ID
     */
    private Long jobId;

    /**
     * 岗位名称
     */
    private String jobName;

    /**
     * 职级ID
     */
    private Long positionId;

    /**
     * 职级名称
     */
    private String positionName;

    /**
     * 状态(0: 在场,1: 已离场)
     */
    private Integer status;

    /**
     * 状态名称
     */
    private String statusName;

}