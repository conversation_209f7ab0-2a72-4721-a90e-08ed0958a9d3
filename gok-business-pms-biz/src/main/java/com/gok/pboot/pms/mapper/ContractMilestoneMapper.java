package com.gok.pboot.pms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.entity.domain.ContractMilestone;
import com.gok.pboot.pms.entity.vo.ContractMilestoneVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2025/03/18
 **/
@Mapper
public interface ContractMilestoneMapper extends BaseMapper<ContractMilestone> {

    /**
     * 根据合同ID查询对应的合同里程碑信息
     *
     * @param contractIds 合同ID集合[]
     * @return
     */
    List<ContractMilestoneVO> findByContractIds(@Param("contractIds") List<Long> contractIds);

}
