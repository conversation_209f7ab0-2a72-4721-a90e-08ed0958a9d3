package com.gok.pboot.pms.entity.vo;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.Util.DecimalFormatUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.RoundingMode;
import java.text.DecimalFormat;

/**
 * 财务数据-追加预算总成本
 *
 * <AUTHOR>
 * @date 2023/08/23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class AdditionalCostsVo {

    /**
     * 预算金额合计（含税）
     */
    private String totalBudgetAmount;

    /**
     * 预算金额合计（不含税）
     */
    private String totalBudgetAmountBhs;

    /**
     * 财务数据-追加预算总成本 分页数据
     */
    private Page<AdditionalCostsPageVo> additionalCostsPageVoPage;

    /**
     * 设置小数位和舍进规则
     *
     * @param newScale      小数保留位数
     * @param roundingMode  舍进规则
     * @param decimalFormat 小数
     */
    public void setScale(int newScale, RoundingMode roundingMode, DecimalFormat decimalFormat) {
        this.totalBudgetAmount = DecimalFormatUtil.setAndValidate(totalBudgetAmount, newScale, roundingMode, decimalFormat);
        this.totalBudgetAmountBhs = DecimalFormatUtil.setAndValidate(totalBudgetAmountBhs, newScale, roundingMode, decimalFormat);
    }

}
