package com.gok.pboot.pms.cost.entity.vo;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 测算汇总导出VO类
 *
 * <AUTHOR>
 * @create 2025/03/03
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ColumnWidth(15)
public class CostIncomeCalculationExportVO {

    /**
     * 归属月份
     */
    @ExcelProperty("归属月份")
    private String belongMonth;

    /**
     * 结算人天
     */
    @ExcelProperty("结算人天")
    private String settlementHours;

    /**
     * 结算单价
     */
    @ExcelProperty("结算单价")
    private String settlementUnitPrice;

    /**
     * 客户承担费用
     */
    @ColumnWidth(30)
    @ExcelProperty("客户承担费用")
    private String customerBearingAmount;

    /**
     * 测算含税金额
     */
    @ColumnWidth(30)
    @ExcelProperty("测算含税金额")
    private String estimatedInclusiveAmountTax;

    /**
     * 确认状态
     */
    @ExcelProperty("确认状态")
    private String confirmStatusTxt;

    /**
     * 确认日期
     */
    @ExcelProperty("确认日期")
    private String confirmDate;

    /**
     * 结算状态
     */
    @ExcelProperty("结算状态")
    private String settlementStatusTxt;

    /**
     * 操作结算日期
     */
    @ColumnWidth(30)
    @ExcelProperty("操作结算日期")
    private String operationSettlementDate;

    /**
     * 结算单编号
     */
    @ColumnWidth(30)
    @ExcelProperty("结算单编号")
    private String settlementNumber;

    public static List<CostIncomeCalculationExportVO> fromVoList(List<CostIncomeCalculationVO> voList) {
        if (CollUtil.isEmpty(voList)) {
            return ListUtil.empty();
        }
        List<CostIncomeCalculationExportVO> exportList = new ArrayList<>();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM");
        voList.forEach(vo -> {
            List<CostIncomeSettlementDetailVO> settlementList = vo.getSettlementList();
            if (CollUtil.isEmpty(settlementList)) {
                CostIncomeCalculationExportVO baseVo = BeanUtil.copyProperties(vo, CostIncomeCalculationExportVO.class);
                baseVo.setBelongMonth(vo.getBelongMonth().format(dateTimeFormatter));
                exportList.add(baseVo);
            } else {
                settlementList.forEach(settlement -> {
                    CostIncomeCalculationExportVO item = BeanUtil.copyProperties(vo, CostIncomeCalculationExportVO.class);
                    item.setBelongMonth(vo.getBelongMonth().format(dateTimeFormatter));
                    item.setSettlementNumber(settlement.getSettlementNumber());
                    item.setOperationSettlementDate(settlement.getOperationSettlementDate());
                    exportList.add(item);
                });
            }
        });
        return exportList;
    }

}
