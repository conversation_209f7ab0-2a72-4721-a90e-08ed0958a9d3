package com.gok.pboot.pms.eval.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 项目人员工单评价分布明细VO
 * <AUTHOR>
 * @create 2025/5/12
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class EvalTaskDistributionDetailVO {

    /**
     * 工单id
     */
    private Long taskId;

    /**
     * 工单编号
     */
    private String taskNo;

    /**
     * 工单名称
     */
    private String taskName;

    /**
     * 工单综合评分
     */
    private BigDecimal comprehensiveScore;

    /**
     * 工单状态
     */
    private Integer taskStatus;

    /**
     * 工单状态Txt
     */
    private String taskStatusTxt;
}
