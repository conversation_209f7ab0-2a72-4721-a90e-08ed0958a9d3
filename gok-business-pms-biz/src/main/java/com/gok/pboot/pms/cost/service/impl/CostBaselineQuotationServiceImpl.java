//package com.gok.pboot.pms.cost.service.impl;
//
//import cn.hutool.core.bean.BeanUtil;
//import cn.hutool.core.collection.CollUtil;
//import cn.hutool.core.util.ObjectUtil;
//import cn.hutool.core.util.StrUtil;
//import com.baomidou.mybatisplus.core.toolkit.IdWorker;
//import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
//import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
//import com.gok.bcp.message.entity.enums.ChannelEnum;
//import com.gok.bcp.message.entity.enums.MsgTypeEnum;
//import com.gok.bcp.message.entity.enums.TargetTypeEnum;
//import com.gok.components.common.str.StrUtils;
//import com.gok.components.common.user.PigxUser;
//import com.gok.pboot.common.security.util.SecurityUtils;
//import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
//import com.gok.pboot.pms.Util.EnumUtils;
//import com.gok.pboot.pms.Util.SysDeptUtils;
//import com.gok.pboot.pms.common.base.PageRequest;
//import com.gok.pboot.pms.common.exception.ServiceException;
//import com.gok.pboot.pms.cost.entity.domain.CostBaselineQuotation;
//import com.gok.pboot.pms.cost.entity.domain.CostManageVersion;
//import com.gok.pboot.pms.cost.entity.dto.ProjectBusinessInfoDTO;
//import com.gok.pboot.pms.cost.entity.dto.VersionAuditDTO;
//import com.gok.pboot.pms.cost.entity.vo.CostBaselineQuotationHistoryVersionVO;
//import com.gok.pboot.pms.cost.entity.vo.CostBaselineQuotationVO;
//import com.gok.pboot.pms.cost.entity.vo.CostManageEstimationResultsVO;
//import com.gok.pboot.pms.cost.enums.AuditStatusTypeEnum;
//import com.gok.pboot.pms.cost.enums.CostManageVersionEnum;
//import com.gok.pboot.pms.cost.enums.VersionStatusEnum;
//import com.gok.pboot.pms.cost.mapper.CostBaselineQuotationMapper;
//import com.gok.pboot.pms.cost.service.ICostBaselineQuotationService;
//import com.gok.pboot.pms.cost.service.ICostBaselineVersionRecordService;
//import com.gok.pboot.pms.cost.service.ICostManageEstimationResultsService;
//import com.gok.pboot.pms.cost.service.ICostManageVersionService;
//import com.gok.pboot.pms.entity.SysDept;
//import com.gok.pboot.pms.entity.domain.ProjectData;
//import com.gok.pboot.pms.entity.domain.ProjectInfo;
//import com.gok.pboot.pms.entity.dto.BaseSendMsgDTO;
//import com.gok.pboot.pms.enumeration.RoleTypeEnum;
//import com.gok.pboot.pms.handler.BcpDataHandler;
//import com.gok.pboot.pms.mapper.ProjectDataMapper;
//import com.gok.pboot.pms.mapper.ProjectInfoMapper;
//import com.gok.pboot.pms.mapper.ProjectStakeholderMemberMapper;
//import com.gok.pboot.pms.service.BcpMessageService;
//import lombok.RequiredArgsConstructor;
//import org.apache.commons.lang3.ObjectUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.math.BigDecimal;
//import java.math.RoundingMode;
//import java.sql.Timestamp;
//import java.text.NumberFormat;
//import java.util.*;
//import java.util.stream.Collectors;
//
///**
// * <AUTHOR>
// */
//@Service
//@RequiredArgsConstructor
//public class CostBaselineQuotationServiceImpl extends ServiceImpl<CostBaselineQuotationMapper, CostBaselineQuotation> implements ICostBaselineQuotationService {
//
//    private final CostBaselineQuotationMapper costBaselineQuotationMapper;
//    private final ProjectStakeholderMemberMapper projectStakeholderMemberMapper;
//    private final ProjectInfoMapper projectInfoMapper;
//    private final ProjectDataMapper projectDataMapper;
//
//    private final ICostManageEstimationResultsService costManageEstimationResultsService;
//    private final ICostManageVersionService costManageVersionService;
//    private final BcpMessageService bcpMessageService;
//    private final BcpDataHandler bcpDataHandler;
//    private final ICostBaselineVersionRecordService baselineVersionRecordService;
//
//    /**
//     * 审核消息 目标 ID（1.5.1版本暂时定死，后期做成配置）
//     */
//    private final static long AUDIT_MSG_TARGET_ID = 149L;
//
//    /**
//     * 审核消息 目标名称
//     */
//    private final static String AUDIT_MSG_TARGET_NAME = "吴顺强";
//
//    /**
//     * 同步商业论证B表报价与毛利测算归档信息
//     *
//     * @return {@link Boolean}
//     */
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public boolean insertGrossProfitMeasurementAndVersionInfo() {
//        // 版本信息
//        CostManageVersion costManageVersion;
//        // 存储更班版本信息
//        List<CostManageVersion> costManageVersionUpdateList = new ArrayList<>();
//        // 存储版本信息
//        List<CostManageVersion> costManageVersionList = new ArrayList<>();
//        // 存储报价与毛利测算信息
//        List<CostBaselineQuotation> costBaselineQuotationList = new ArrayList<>();
//        // 获取当前报价与毛利测算已发起B表流程项目ID【去重】
//        Set<Long> launchProjectIds = lambdaQuery().eq(CostBaselineQuotation::getAuditStatus, AuditStatusTypeEnum.BBLCYFQ.getValue()).list()
//                .stream().map(CostBaselineQuotation::getProjectId).collect(Collectors.toSet());
//        if (ObjectUtil.isEmpty(launchProjectIds)) {
//            return false;
//        }
//        // 获取当前报价与毛利测算已归档B表流程项目ID【去重】
//        Set<Long> filingProjectIds = lambdaQuery().eq(CostBaselineQuotation::getAuditStatus, AuditStatusTypeEnum.BBLCYGD.getValue()).list()
//                .stream().map(CostBaselineQuotation::getProjectId).collect(Collectors.toSet());
//        // 移除已发起中包含已归档的项目ID
//        launchProjectIds.removeAll(filingProjectIds);
//        if (ObjectUtil.isEmpty(launchProjectIds)) {
//            return false;
//        }
//        // 获取项目核心数据对应项目ID数据
//        List<ProjectData> projectDataList = projectDataMapper.selectBatchIds(launchProjectIds);
//        if (ObjectUtil.isEmpty(projectDataList)) {
//            return false;
//        }
//        // 数据处理
//        for (ProjectData project : projectDataList) {
//            // 获取当前最新版本
//            CostManageVersion currentCostManageVersion = costManageVersionService.getCurrentCostManageVersion(CostManageVersionEnum.BJYMLCS, project.getId());
//            // 获取版本名称
//            String versionName = Objects.nonNull(currentCostManageVersion) ? currentCostManageVersion.getVersionName() : StrUtils.EMPTY;
//            // 更新为历史版本
//            if (StrUtil.isNotBlank(versionName)) {
//                currentCostManageVersion.setVersionStatus(VersionStatusEnum.HISTORY.getValue());
//                costManageVersionUpdateList.add(currentCostManageVersion);
//            }
//            // 获取版本信息
//            costManageVersion = CostManageVersion.convert(new ProjectBusinessInfoDTO().setProjectId(project.getId())
//                    .setRequestId(project.getRequestId()).setRequestName(project.getRequestName()), versionName);
//            costManageVersion.setCreator("B表流程同步");
//            costManageVersion.setCtime(new Timestamp(System.currentTimeMillis()));
//            costManageVersionList.add(costManageVersion);
//            // 获取报价与毛利测算信息
//            CostBaselineQuotation costBaselineQuotation = new CostBaselineQuotation();
//            costBaselineQuotation.setProjectId(project.getId())
//                    .setVersionId(costManageVersion.getId())
//                    .setAuditStatus(AuditStatusTypeEnum.BBLCYGD.getValue())
//                    .setIncomeAmountIncludedTax(project.getTotalBudgetRevenueIncludeTax())
//                    .setIncomeAmountExcludingTax(project.getTotalBudgetRevenue())
//                    .setCreator("B表流程同步");
//            costBaselineQuotation.setId(IdWorker.getId());
//            costBaselineQuotation.setCtime(new Timestamp(System.currentTimeMillis()));
//            costBaselineQuotationList.add(costBaselineQuotation);
//        }
//        costManageVersionService.saveOrUpdateBatch(costManageVersionUpdateList);
//        costManageVersionService.saveBatch(costManageVersionList);
//        this.saveBatch(costBaselineQuotationList);
//        return true;
//    }
//
//    /**
//     * 获取报价与毛利测算版本信息
//     *
//     * @param projectId 项目ID
//     * @param versionId 版本ID
//     * @return {@link CostBaselineQuotationVO}
//     */
//    @Override
//    public CostBaselineQuotationVO getGrossProfitMeasurementVersionInfo(Long projectId, Long versionId) {
//        // 获取报价与毛利测算版本信息
//        CostBaselineQuotationVO costBaselineQuotation = costBaselineQuotationMapper.getGrossProfitMeasurementVersionInfo(projectId, versionId, null);
//        if (ObjectUtils.isEmpty(costBaselineQuotation)) {
//            return costBaselineQuotation;
//        }
//        //处理毛利测算详情
//        setGrossProfitMeasurementDetail(projectId, costBaselineQuotation);
//        // 预计毛利率(不含税)【%格式化】
//        NumberFormat percentInstance = NumberFormat.getPercentInstance();
//        percentInstance.setMaximumFractionDigits(2);
//        costBaselineQuotation.setExpectedGrossProfitMargin(percentInstance.format(new BigDecimal(costBaselineQuotation.getExpectedGrossProfitMargin())));
//        return costBaselineQuotation;
//    }
//
//
//    /**
//     * 处理毛利测算详情
//     *
//     * @param projectId             项目ID
//     * @param costBaselineQuotation info
//     */
//    @Override
//    public void setGrossProfitMeasurementDetail(Long projectId, CostBaselineQuotationVO costBaselineQuotation) {
//        // 预算成本总额(不含税)
//        BigDecimal budgetAmountExcludingTax = BigDecimal.ZERO;
//        List<CostManageEstimationResultsVO> latestConfirmedCost = costManageEstimationResultsService.findLatestConfirmedCost(projectId);
//        if (CollUtil.isNotEmpty(latestConfirmedCost)) {
//            budgetAmountExcludingTax = latestConfirmedCost.stream()
//                    .map(CostManageEstimationResultsVO::getBudgetAmountExcludingTax)
//                    .filter(Objects::nonNull)
//                    .reduce(BigDecimal.ZERO, BigDecimal::add);
//        }
//        costBaselineQuotation.setBudgetAmountExcludingTax(String.valueOf(budgetAmountExcludingTax));
//        // 收入总额(含税)
//        costBaselineQuotation.setIncomeAmountIncludedTax(StringUtils.isNotEmpty(costBaselineQuotation.getIncomeAmountIncludedTax()) ?
//                costBaselineQuotation.getIncomeAmountIncludedTax() : "0.00");
//        // 收入总额(不含税)
//        BigDecimal incomeAmountExcludingTax = StringUtils.isNotEmpty(costBaselineQuotation.getIncomeAmountExcludingTax()) ?
//                new BigDecimal(costBaselineQuotation.getIncomeAmountExcludingTax()) : BigDecimal.ZERO;
//        costBaselineQuotation.setIncomeAmountExcludingTax(incomeAmountExcludingTax.compareTo(BigDecimal.ZERO) == 0 ? "0.00" : String.valueOf(incomeAmountExcludingTax));
//        // 预计毛利(不含税)
//        BigDecimal expectedGrossProfit = incomeAmountExcludingTax.subtract(budgetAmountExcludingTax);
//        costBaselineQuotation.setExpectedGrossProfit(String.valueOf(expectedGrossProfit));
//        // 预计毛利率(不含税)
//        costBaselineQuotation.setExpectedGrossProfitMargin(String.valueOf(incomeAmountExcludingTax.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
//                expectedGrossProfit.divide(incomeAmountExcludingTax, 4, RoundingMode.HALF_UP)));
//        // 预计提前投入成本(万元)
//        costBaselineQuotation.setExpectedEarlyInvestmentCost(StringUtils.isNotEmpty(costBaselineQuotation.getExpectedEarlyInvestmentCost()) ?
//                costBaselineQuotation.getExpectedEarlyInvestmentCost() : "0.00");
//        // 设置审核状态
//        Optional.of(costBaselineQuotation).ifPresent(c ->
//                c.setAuditStatusName(EnumUtils.getNameByValue(AuditStatusTypeEnum.class, c.getAuditStatus()))
//        );
//    }
//
//
//    /**
//     * 获取报价与毛利测算历史版本信息
//     *
//     * @param pageRequest 分页参数
//     * @param projectId   项目ID
//     * @return {@link Page}<{@link  CostBaselineQuotationHistoryVersionVO}>
//     */
//    @Override
//    public Page<CostBaselineQuotationHistoryVersionVO> getGrossProfitMeasurementHistoryVersionInfo(PageRequest pageRequest, Long projectId) {
//        // 获取报价与毛利测算历史版本信息
//        Page<CostBaselineQuotationHistoryVersionVO> page =
//                costBaselineQuotationMapper.getGrossProfitMeasurementHistoryVersionInfo(Page.of(pageRequest.getPageNumber(), pageRequest.getPageSize()), projectId);
//        // 设置审核状态
//        page.getRecords().forEach(info -> info.setAuditStatusName(EnumUtils.getNameByValue(AuditStatusTypeEnum.class, info.getAuditStatus())));
//        return page;
//    }
//
//    /**
//     * 更新报价与毛利测算信息
//     *
//     * @param info dto
//     */
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public void insertGrossProfitMeasurementInfo(ProjectBusinessInfoDTO info) {
//        // 角色类型
//        Integer roleType;
//        // 版本信息
//        CostManageVersion costManageVersion;
//        // 报价与毛利测算信息
//        CostBaselineQuotation costBaselineQuotation;
//        // 获取部门MAP
//        Map<Long, SysDept> deptIdMap = bcpDataHandler.getAllSysDeptMap();
//        // 获取当前最新版本
//        CostManageVersion currentCostManageVersion = costManageVersionService.getCurrentCostManageVersion(CostManageVersionEnum.BJYMLCS, info.getProjectId());
//        // 获取版本名称
//        String versionName = Objects.nonNull(currentCostManageVersion) ? currentCostManageVersion.getVersionName() : StrUtils.EMPTY;
//        // 更新为历史版本
//        if (StrUtil.isNotBlank(versionName)) {
//            currentCostManageVersion.setVersionStatus(VersionStatusEnum.HISTORY.getValue());
//            costManageVersionService.updateById(currentCostManageVersion);
//        }
//        // 补充内容
//        info.setRequestId(currentCostManageVersion.getRequestId());
//        info.setRequestName(currentCostManageVersion.getRequestName());
//        // 获取版本信息
//        costManageVersion = CostManageVersion.convert(info, versionName);
//        // 获取当前用户信息
//        PigxUser user = SecurityUtils.getUser();
//        if (Objects.nonNull(user)) {
//            // 获取部门名称
//            String deptName = SysDeptUtils.collectFullName(deptIdMap, user.getDeptId());
//            // 获取当前操作人角色
//            roleType = projectStakeholderMemberMapper.getOperatorRole(costManageVersion.getProjectId(), user.getId());
//            // 设置版本信息
//            costManageVersion.setOperatorId(user.getId())
//                    .setOperatorName(user.getName())
//                    .setOperatorDeptId(user.getDeptId())
//                    .setOperatorDeptName(deptName)
//                    .setOperatorRole(EnumUtils.getNameByValue(RoleTypeEnum.class, roleType));
//        }
//        // 设置、保存版本信息
//        BaseBuildEntityUtil.buildInsertNoId(costManageVersion);
//        costManageVersionService.save(costManageVersion);
//        // 获取报价与毛利测算信息
//        costBaselineQuotation = BeanUtil.copyProperties(info, CostBaselineQuotation.class);
//        // 设置、保存报价与毛利测算信息
//        costBaselineQuotation.setAuditStatus(AuditStatusTypeEnum.WSH.getValue()).setVersionId(costManageVersion.getId());
//        BaseBuildEntityUtil.buildInsert(costBaselineQuotation);
//        this.save(costBaselineQuotation);
//
//        // 同步基线版本记录
//        baselineVersionRecordService.syncCostBaselineVersionRecord(costManageVersion.getProjectId(), StrUtil.EMPTY);
//
//        // 消息推送
//        ProjectInfo projectInfo = projectInfoMapper.selectById(costManageVersion.getProjectId());
//        final String title = "报价与毛利审核";
//        final String content = user.getName() + "编辑了【" + projectInfo.getItemName() + "】的报价与毛利测算，请及时进行审核。";
//        BaseSendMsgDTO sendMsgDTO = new BaseSendMsgDTO()
//                .setTitle(title)
//                .setContent(content)
//                .setMsgTypeEnum(MsgTypeEnum.NOTICE_MSG)
//                .setTargetTypeEnum(TargetTypeEnum.USERS)
//                .setChannelEnums(CollUtil.newLinkedHashSet(ChannelEnum.WECOM, ChannelEnum.MAIL))
//                .setPath("/view-businesses/project-manage/project-manage-list?projectId=" + costManageVersion.getProjectId() + "&subTab=PROJECT_BASELINE")
//                .populateSender(user.getId(), user.getName())
//                .toOneTarget(AUDIT_MSG_TARGET_ID, AUDIT_MSG_TARGET_NAME);
//        bcpMessageService.sendMsg(sendMsgDTO);
//    }
//
//    /**
//     * 获取收入总额(不含税)
//     *
//     * @param projectId 项目ID
//     * @return {@link BigDecimal}
//     */
//    @Override
//    public BigDecimal getIncomeAmountExcludingTax(Long projectId) {
//        // 获取最新A表流程同步、已审核数据
//        CostBaselineQuotationVO costBaselineQuotation = costBaselineQuotationMapper.getCostBaselineQuotationLast(projectId);
//        if (ObjectUtils.isNotEmpty(costBaselineQuotation) && ObjectUtils.isNotEmpty(costBaselineQuotation.getIncomeAmountExcludingTax())) {
//            return new BigDecimal(costBaselineQuotation.getIncomeAmountExcludingTax());
//        }
//        // 否则获取最新版本数据
//        costBaselineQuotation = costBaselineQuotationMapper.getGrossProfitMeasurementVersionInfo(projectId, null, null);
//        if (ObjectUtils.isNotEmpty(costBaselineQuotation) && ObjectUtils.isNotEmpty(costBaselineQuotation.getIncomeAmountExcludingTax())) {
//            return new BigDecimal(costBaselineQuotation.getIncomeAmountExcludingTax());
//        }
//        return BigDecimal.ZERO;
//    }
//
//    /**
//     * 更新当前版本审核状态
//     *
//     * @param auditInfo dto
//     */
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public void updateAuditStatusInfo(VersionAuditDTO auditInfo) {
//        Long projectId = auditInfo.getProjectId();
//        Long versionId = auditInfo.getVersionId();
//        // 获取部门MAP
//        Map<Long, SysDept> deptIdMap = bcpDataHandler.getAllSysDeptMap();
//        // 获取版本信息
//        CostManageVersion costManageVersion = costManageVersionService.getById(versionId);
//        // 获取报价与毛利测算信息
//        CostBaselineQuotation costBaselineQuotation = lambdaQuery()
//                .eq(CostBaselineQuotation::getProjectId, projectId)
//                .eq(CostBaselineQuotation::getVersionId, versionId).one();
//        AuditStatusTypeEnum auditStatusTypeEnum = EnumUtils.getEnumByValue(AuditStatusTypeEnum.class, auditInfo.getAuditStatus());
//        if (auditStatusTypeEnum == null) {
//            throw new ServiceException("审核状态参数错误~");
//        }
//        final Long originalOperatorId = costManageVersion.getOperatorId();
//        final String originalOperatorName = costManageVersion.getOperatorName();
//        switch (auditStatusTypeEnum) {
//            // 已通过
//            case YSH:
//                costBaselineQuotation.setAuditStatus(AuditStatusTypeEnum.YSH.getValue());
//                break;
//            // 未通过
//            case WTG:
//                costManageVersion.setRefuseReason(auditInfo.getRefuseReason());
//                costBaselineQuotation.setAuditStatus(AuditStatusTypeEnum.WTG.getValue());
//                break;
//            default:
//                throw new ServiceException("审核状态参数错误~");
//        }
//        // 获取当前用户信息
//        PigxUser user = SecurityUtils.getUser();
//        if (Objects.nonNull(user)) {
//            // 获取部门名称
//            String deptName = SysDeptUtils.collectFullName(deptIdMap, user.getDeptId());
//            // 获取当前操作人角色
//            Integer roleType = projectStakeholderMemberMapper.getOperatorRole(projectId, user.getId());
//            // 设置版本信息
//            costManageVersion.setOperatorId(user.getId())
//                    .setOperatorName(user.getName())
//                    .setOperatorDeptId(user.getDeptId())
//                    .setOperatorDeptName(deptName)
//                    .setOperatorRole(EnumUtils.getNameByValue(RoleTypeEnum.class, roleType));
//        }
//        BaseBuildEntityUtil.buildUpdate(costManageVersion);
//        BaseBuildEntityUtil.buildUpdate(costBaselineQuotation);
//        // 更新数据
//        costManageVersionService.updateById(costManageVersion);
//        this.updateById(costBaselineQuotation);
//        // 消息推送
//        ProjectInfo projectInfo = projectInfoMapper.selectById(costManageVersion.getProjectId());
//        final String title = AuditStatusTypeEnum.WTG.equals(auditStatusTypeEnum) ? "报价与毛利审核不通过" : "报价与毛利审核通过";
//        final String content = user.getName() + "审核" + (AuditStatusTypeEnum.WTG.equals(auditStatusTypeEnum) ? "不通过" : "通过了")
//                + "【" + projectInfo.getItemName() + "】的报价与毛利测算。";
//        BaseSendMsgDTO sendMsgDTO = new BaseSendMsgDTO()
//                .setTitle(title)
//                .setContent(content)
//                .setMsgTypeEnum(MsgTypeEnum.NOTICE_MSG)
//                .setTargetTypeEnum(TargetTypeEnum.USERS)
//                .setChannelEnums(CollUtil.newLinkedHashSet(ChannelEnum.WECOM, ChannelEnum.MAIL))
//                .setPath("/view-businesses/project-manage/project-manage-list?projectId=" + projectId + "&subTab=PROJECT_BASELINE")
//                .populateSender(user.getId(), user.getName())
//                .toOneTarget(originalOperatorId, originalOperatorName);
//        bcpMessageService.sendMsg(sendMsgDTO);
//    }
//
//}
