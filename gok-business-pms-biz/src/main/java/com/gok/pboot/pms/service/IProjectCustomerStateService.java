package com.gok.pboot.pms.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.entity.domain.ProjectCustomerState;
import com.gok.pboot.pms.entity.vo.ProjectCustomerStateVO;

import java.util.Map;

/**
 * 项目客情状态表（数仓同步）
 *
 * <AUTHOR>
 * @date 2023-07-11 17:05:26
 */
public interface IProjectCustomerStateService extends IService<ProjectCustomerState> {

    /**
     * 查询项目客情状态表
     *
     * @param pageRequest 分页请求实体
     * @param filter      查询参数
     * @return {@link Page}{@link ProjectCustomerStateVO}
     */
    Page<ProjectCustomerStateVO> findPage(PageRequest pageRequest, Map<String, Object> filter);

}

