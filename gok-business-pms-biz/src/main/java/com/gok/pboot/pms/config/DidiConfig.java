package com.gok.pboot.pms.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 滴滴配置类
 *
 * <AUTHOR>
 * @date 2025/01/27
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "didi")
public class DidiConfig {

    /**
     * 客户端配置
     */
    private Client client = new Client();

    /**
     * API配置
     */
    private Api api = new Api();

    /**
     * 同步配置
     */
    private Sync sync = new Sync();

    @Data
    public static class Client {
        /**
         * 客户端ID
         */
        private String id;

        /**
         * 客户端密钥
         */
        private String secret;

        /**
         * 授权类型
         */
        private String grantType = "client_credentials";
    }

    @Data
    public static class Api {
        /**
         * 基础URL
         */
        private String baseUrl = "https://api.es.xiaojukeji.com";

        /**
         * 超时时间（毫秒）
         */
        private Integer timeout = 30000;

        /**
         * 重试次数
         */
        private Integer retryCount = 3;
    }

    @Data
    public static class Sync {
        /**
         * 是否启用自动同步
         */
        private Boolean enabled = false;

        /**
         * 同步间隔（分钟）
         */
        private Integer interval = 30;

        /**
         * 批量同步大小
         */
        private Integer batchSize = 100;

        /**
         * 是否启用异步同步
         */
        private Boolean async = true;
    }
} 