package com.gok.pboot.pms.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.entity.vo.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

public interface ProjectDetailsService {

    /**
     * 财务数据-追加预算总收入
     *
     * @param pageRequest     页面请求
     * @param stringObjectMap 字符串对象映射
     * @return {@link ApiResult}<{@link AdditionalIncomePageVo}>
     */
    ApiResult<AdditionalIncomeVo> additionalIncome(PageRequest pageRequest, Map<String, Object> stringObjectMap);

    /**
     * 财务数据-已有预算总收入
     *
     * @param pageRequest     页面请求
     * @param stringObjectMap 字符串对象映射
     * @return {@link ApiResult}<{@link AdditionalIncomePageVo}>
     */
    ApiResult<AdditionalIncomeVo> existingIncome(PageRequest pageRequest, Map<String, Object> stringObjectMap);

    /**
     * 财务数据-追加预算总成本
     *
     * @param pageRequest     页面请求
     * @param stringObjectMap 字符串对象映射
     * @return {@link ApiResult}<{@link AdditionalCostsVo}>
     */
    ApiResult<AdditionalCostsVo> additionalCosts(PageRequest pageRequest, Map<String, Object> stringObjectMap);

    /**
     * 财务数据-已有预算总成本
     *
     * @param pageRequest     页面请求
     * @param stringObjectMap 字符串对象映射
     * @return {@link ApiResult}<{@link AdditionalCostsVo}>
     */
    ApiResult<AdditionalCostsVo> existingBudget(PageRequest pageRequest, Map<String, Object> stringObjectMap);

    /**
     * 项目管理-项目费用报销明细-dbApi调用
     *
     * @param stringObjectMap
     * @return
     */
    ApiResult<Page<ReimburseDetailsVO>> reimburseDetails(PageRequest pageRequest, Map<String, Object> stringObjectMap);

    /**
     * 获取销售合同
     *
     * @param pageRequest     分页请求
     * @param stringObjectMap 字符串对象映射
     * @return {@link ApiResult}<{@link Page}<{@link ProjectContractPageVo}>>
     */
    ApiResult<ProjectContractVo> projectContract(PageRequest pageRequest, Map<String, Object> stringObjectMap);

    /**
     * 获取财务数据
     *
     * @param stringObjectMap 字符串对象映射
     * @return {@link ApiResult}<{@link FinancialDataVo}>
     */
    ApiResult<FinancialDataVo> financialData(Map<String, Object> stringObjectMap);

    /**
     * 项目管理-人天明细
     *
     * @param pageRequest     页面请求
     * @param stringObjectMap 字符串对象映射
     * @return {@link ApiResult}<{@link Page}<{@link ManDayDetailsPageVo}>>
     */
    ApiResult<Page<ManDayDetailsPageVo>> manDayDetails(PageRequest pageRequest, Map<String, Object> stringObjectMap);

    /**
     * 项目管理-查询直接人工（项目分摊）明细
     *
     * @param pageRequest     页面请求
     * @param stringObjectMap 字符串对象映射
     * @return {@link ApiResult}<{@link Page}<{@link TotalCostDetailsPageVo}>>
     */
    ApiResult<Page<TotalCostDetailsPageVo>> totalCostDetails(PageRequest pageRequest, Map<String, Object> stringObjectMap);

    /**
     * 项目采购付款明细
     *
     * @param pageRequest     页面请求
     * @param stringObjectMap 字符串对象映射
     * @return {@link ApiResult}<{@link Page}<{@link ProcureDetailsPageVo}>>
     */
    ApiResult<Page<ProcureDetailsPageVo>> procureDetails(PageRequest pageRequest, Map<String, Object> stringObjectMap);

    /**
     * 项目管理-查询合同明细
     *
     * @param pageRequest     页面请求
     * @param stringObjectMap 字符串对象映射
     * @return {@link ApiResult}<{@link Page}<{@link ContractDetailsPageVo}>>
     */
    ApiResult<Page<ContractDetailsPageVo>> contractDetails(PageRequest pageRequest, Map<String, Object> stringObjectMap);

    /**
     * @param fileName
     * @param fileUrl
     * @param response
     * @return {@link ApiResult}<{@link String}>
     */
    void install(String fileName, String fileUrl, HttpServletResponse response);

    /**
     * 财务数据-预算总成本-内部项目OA数据
     *
     * @param pageRequest 页面请求
     * @param filter      字符串对象映射
     * @return {@link ApiResult}<{@link AdditionalCostsVo}>
     * @version 1.3.6
     */
    ApiResult<AdditionalCostsVo> internalBudget(PageRequest pageRequest, Map<String, Object> filter);

}
