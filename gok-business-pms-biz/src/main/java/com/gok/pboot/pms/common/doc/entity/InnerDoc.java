package com.gok.pboot.pms.common.doc.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gok.pboot.pms.common.base.TenantBeanEntity;
import lombok.Data;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 */
@Data
public class InnerDoc extends TenantBeanEntity<Long> {
    private String fileName;
    private byte[] body;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp uploadTime;
}
