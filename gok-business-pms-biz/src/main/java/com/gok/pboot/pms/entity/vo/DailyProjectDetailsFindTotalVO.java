package com.gok.pboot.pms.entity.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 日期维度审核统计vo
 *
 * <AUTHOR>
 * @date 2023/11/29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class DailyProjectDetailsFindTotalVO {

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 提交日期
     */
    private LocalDate submissionDate;

    /**
     * 审核状态（0=未提交，1=已退回，2=待审核，3=不通过，4=已通过）
     */
    private Integer approvalStatus;

    /**
     * 审核状态（0=未提交，1=已退回，2=待审核，3=不通过，4=已通过）
     */
    private String approvalStatusName;

    /**
     * 项目总工时
     */
    private BigDecimal totalHours;

    /**
     * 项目正常工时
     */
    private BigDecimal normalHours;

    /**
     * 项目加班工时
     */
    private BigDecimal addedHours;

    /**
     * 待操作人数
     */
    private Long number;

    /**
     * 需操作总人数
     */
    private Long numberTotal;

}
