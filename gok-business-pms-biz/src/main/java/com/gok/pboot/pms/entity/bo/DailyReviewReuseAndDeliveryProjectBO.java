package com.gok.pboot.pms.entity.bo;

import lombok.*;

/**
 * <AUTHOR>
 * @version 1.3.2
 */
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class DailyReviewReuseAndDeliveryProjectBO {

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 审核人数
     */
    private Integer approvalNum;

    /**
     * 汇总工时（人天）
     */
    private Double aggregatedDays;
}
