package com.gok.pboot.pms.cost.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.cost.entity.domain.CostManageTarget;
import com.gok.pboot.pms.cost.entity.vo.CostManageTargetVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 成本目标管理 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Mapper
public interface CostManageTargetMapper extends BaseMapper<CostManageTarget> {

    CostManageTargetVO getCostManageTargetInfo(@Param("versionId") Long versionId, @Param("projectId") Long projectId);
}
