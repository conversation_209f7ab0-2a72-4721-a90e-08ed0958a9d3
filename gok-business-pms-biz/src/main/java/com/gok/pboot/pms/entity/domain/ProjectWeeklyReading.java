package com.gok.pboot.pms.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 项目周报已读表
 *
 * <AUTHOR>
 * @date 2023/9/4
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("project_weekly_reading")
public class ProjectWeeklyReading extends BeanEntity<Long> {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 周报id
     */
    private Long pwId;

    public static ProjectWeeklyReading of(Long userId, Long pwId) {
        ProjectWeeklyReading result = new ProjectWeeklyReading();
        result.setUserId(userId);
        result.setPwId(pwId);
        return result;
    }
}
