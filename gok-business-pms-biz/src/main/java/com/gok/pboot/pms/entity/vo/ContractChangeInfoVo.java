package com.gok.pboot.pms.entity.vo;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;


/**
 * 合同变更信息vo
 *
 * <AUTHOR>
 * @date 2024/2/22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class ContractChangeInfoVo {

    /**
     * 流程id
     */
    private Long requestId;

    /**
     * 归档时间
     */
    private String filingDateTime;
    /**
     * 变更类型（合同变更
     * 合同内容变更
     * 合同解除/终止
     * 原合同作废新合同成立）
     */
    private Integer bglx;

    /**
     * 变更类型txt
     */
    private String bglxTxt;


    /**
     * 新实际合同签订时间
     */
    private String xsjhtqdsj;


    /**
     * 变更协议原件数量
     */
    private Integer bgxyyjsl;

    /**
     * 原合同附件集合
     */
    List<OaFileInfoVo> htfjList;

    /**
     * 新合同附件集合
     */
    List<OaFileInfoVo> xhtfjList;

    /**
     * 新合同附件（已盖章）集合
     */
    List<OaFileInfoVo> htfjygzList;

    /**
     * 变更/补充/解除/终止协议附件（已盖章）集合
     */
    List<OaFileInfoVo> bgbcjczzxyfjygzList;

    /**
     * 合同变更/补充流程编号
     */
    private String flowno;

    /**
     * 新原件数量
     */
    private Integer xyjsl;

    /**
     * 新实际合同编号
     */
    private String sjhtbh;

    /**
     * 变更协议签订日期
     */
    private String bgxyqdrq;

    /**
     * 变更原因
     */
    private String bgyy;

    /**
     * 变更内容详情及说明
     */
    private String bgnrxqjsm;

    /**
     * 备注
     */
    private String remark;

    /**
     * 流程相关人id
     */
    private String nodeoperator;

}
