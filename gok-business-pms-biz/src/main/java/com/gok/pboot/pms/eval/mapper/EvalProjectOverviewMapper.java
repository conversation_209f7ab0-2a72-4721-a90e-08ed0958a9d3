package com.gok.pboot.pms.eval.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.entity.domain.ProjectInfo;
import com.gok.pboot.pms.eval.entity.domain.EvalProjectOverview;
import com.gok.pboot.pms.eval.entity.dto.EvalProjectOverviewDTO;
import com.gok.pboot.pms.eval.entity.vo.EvalProjectOverviewVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2025/05/08
 **/
@Mapper
public interface EvalProjectOverviewMapper extends BaseMapper<EvalProjectOverview> {

    /**
     * 根据项目ID获取
     *
     * @param projectId 项目ID
     * @return
     */
    EvalProjectOverview getByProjectId(@Param("projectId") Long projectId);

    /**
     * 根据ID获取
     *
     * @param id
     * @return
     */
    EvalProjectOverviewVO getById(@Param("id") Long id);

    /**
     * 获取所有已结项未确认的项目信息集合
     * 目前仅查询交付类型为项目外包的项目
     *
     * @return
     */
    List<EvalProjectOverviewVO> getUnSaveProjectInfoList();

    /**
     * 项目整体评价列表分页查询
     *
     * @param query 查询请求
     * @return
     */
    List<EvalProjectOverviewVO> findList(@Param("query") EvalProjectOverviewDTO query);

}
