package com.gok.pboot.pms.cost.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.bcp.message.entity.enums.ChannelEnum;
import com.gok.bcp.message.entity.enums.MsgTypeEnum;
import com.gok.bcp.message.entity.enums.TargetTypeEnum;
import com.gok.bcp.upms.dto.DeptCacheDto;
import com.gok.bcp.upms.feign.RemoteDeptService;
import com.gok.components.common.user.PigxUser;
import com.gok.components.common.user.UserUtils;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.Util.DbApiUtil;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.Util.SysDeptUtils;
import com.gok.pboot.pms.common.base.BaseConstants;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.common.exception.ServiceException;
import com.gok.pboot.pms.cost.entity.domain.CostManageEstimationResults;
import com.gok.pboot.pms.cost.entity.domain.CostManagePersonnelCustomDetail;
import com.gok.pboot.pms.cost.entity.domain.CostManagePersonnelLevelDetail;
import com.gok.pboot.pms.cost.entity.domain.CostManageVersion;
import com.gok.pboot.pms.cost.entity.dto.CostConfirmDTO;
import com.gok.pboot.pms.cost.entity.dto.CostManageVersionDTO;
import com.gok.pboot.pms.cost.entity.vo.*;
import com.gok.pboot.pms.cost.enums.*;
import com.gok.pboot.pms.cost.mapper.CostManagePersonnelCustomDetailMapper;
import com.gok.pboot.pms.cost.mapper.CostManagePersonnelLevelDetailMapper;
import com.gok.pboot.pms.cost.mapper.CostManageTargetMapper;
import com.gok.pboot.pms.cost.mapper.CostManageVersionMapper;
import com.gok.pboot.pms.cost.service.ICostBaselineVersionRecordService;
import com.gok.pboot.pms.cost.service.ICostCashPlanService;
import com.gok.pboot.pms.cost.service.ICostManageVersionService;
import com.gok.pboot.pms.entity.SysDept;
import com.gok.pboot.pms.entity.domain.ProjectInfo;
import com.gok.pboot.pms.entity.dto.BaseSendMsgDTO;
import com.gok.pboot.pms.entity.vo.ProjectStakeholderMemberVO;
import com.gok.pboot.pms.enumeration.CostManageStatusEnum;
import com.gok.pboot.pms.enumeration.RoleTypeEnum;
import com.gok.pboot.pms.mapper.ProjectInfoMapper;
import com.gok.pboot.pms.service.BcpMessageService;
import com.gok.pboot.pms.service.IProjectStakeholderMemberService;
import com.gok.pboot.pms.service.IProjectTaskeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 成本管理版本记录 服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Service
@Log4j2
@RequiredArgsConstructor
public class CostManageVersionServiceImpl extends ServiceImpl<CostManageVersionMapper, CostManageVersion> implements ICostManageVersionService {

    @Resource
    private BcpMessageService bcpMessageService;

    @Resource
    private ProjectInfoMapper projectInfoMapper;
    @Resource
    private IProjectStakeholderMemberService projectStakeholderService;
    @Lazy
    @Resource
    private ICostCashPlanService costCashPlanService;
    @Resource
    private RemoteDeptService remoteDeptService;
    private final DbApiUtil dbApiUtil;
    @Resource
    private IProjectTaskeService projectTaskeService;
    private final CostManageTargetMapper costManageTargetMapper;
    private final CostManagePersonnelLevelDetailMapper costManagePersonnelLevelDetailMapper;
    private final CostManagePersonnelCustomDetailMapper costManagePersonnelCustomDetailMapper;
    @Lazy
    @Resource
    private CostManageEstimationResultsServiceImpl costManageEstimationResultsService;
    @Lazy
    @Resource
    private ICostBaselineVersionRecordService baselineVersionRecordService;

    @Value("${oa.xsfyOaId:62}")
    private Long xsfyOaId;

    @Value("${oa.xmglfysqrgOaId:8102}")
    private Long xmglfysqrgOaId;

    /**
     * 售前成本确认
     *
     * @param costConfirmDTO 成本确认 DTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void preSaleCostConfirm(CostConfirmDTO costConfirmDTO) {
        CostManageVersion costManageVersion = getById(costConfirmDTO.getCostManageVersionId());
        Boolean operate = costConfirmDTO.getOperate();
        if (costManageVersion == null) {
            throw new ServiceException("数据不存在");
        }
        ProjectInfo projectInfo = projectInfoMapper.selectById(costManageVersion.getProjectId());
        // 客户经理Id
        Long salesmanUserId = projectInfo.getSalesmanUserId();
        PigxUser user = UserUtils.getUser();
        if (user == null || !Objects.equals(user.getId(), salesmanUserId)) {
            throw new ServiceException("权限不足,无法操作");
        }

        CostManageStatusEnum costManageStatusEnum = CostManageStatusEnum.getEnumByBool(operate);
        // 如果状态是未确认，则不发送消息
        if (CostManageStatusEnum.UNCONFIRMED.equals(costManageStatusEnum)) {
            return;
        }

        // 成本估算操作人信息
        Long costEstimateOperatorId = costManageVersion.getOperatorId();
        String costEstimateOperatorName = costManageVersion.getOperatorName();

        // 获取部门信息
        List<DeptCacheDto> deptList = remoteDeptService.getAllDeptList(false);
        Map<Long, SysDept> deptIdMap = SysDeptUtils.getDeptIdMap(deptList);
        Long deptId = user.getDeptId();
        String fullDeptName = SysDeptUtils.collectFullName(deptIdMap, user.getDeptId());

        // 设置更新信息
        costManageVersion.setStatus(costManageStatusEnum.getValue());
//        if (CostManageStatusEnum.REFUSED.equals(costManageStatusEnum)) {
//            costManageVersion.setRefuseReason(costConfirmDTO.getRefuseReason());
//        }
        costManageVersion.setOperatorId(user.getId());
        costManageVersion.setOperatorDeptId(deptId);
        costManageVersion.setOperatorDeptName(fullDeptName);
        costManageVersion.setOperatorName(user.getName());
        costManageVersion.setOperatorRole(RoleTypeEnum.PROJECT_SALES_MANAGER.getName());

        // 更新状态
        updateById(BaseBuildEntityUtil.buildUpdate(costManageVersion));
        // 发送消息通知
        BaseSendMsgDTO baseSendMsgDTO = new BaseSendMsgDTO();
        final String title = "售前成本预算" + (Boolean.TRUE.equals(operate) ? "确认通过" : "未通过");
        final String content = user.getName() + (Boolean.TRUE.equals(operate) ? "通过了" : "未通过")
                + "【" + projectInfo.getItemName() + "】的售前成本预算，"
                + (Boolean.TRUE.equals(operate) ? "您现在可以进行任务分配了" : "请重新修改售前成本预算") + "。";
        baseSendMsgDTO.setTitle(title)
                .setContent(content)
                .setMsgTypeEnum(MsgTypeEnum.NOTICE_MSG)
                .setTargetTypeEnum(TargetTypeEnum.USERS)
                .setChannelEnums(CollUtil.newLinkedHashSet(ChannelEnum.WECOM, ChannelEnum.MAIL))
                .setPath("/view-businesses/project-manage/project-manage-list?projectId=" + costManageVersion.getProjectId() + "&subTab=PROJECT_COST_MANAGEMENT")
                .populateSender()
                .toOneTarget(costEstimateOperatorId, costEstimateOperatorName);
        bcpMessageService.sendMsg(baseSendMsgDTO);
    }


    /**
     * 售前成本估算消息推送
     *
     * @param costManageVersionId 成本管理版本 ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void preSaleEstimateMessagePush(Long costManageVersionId) {
        CostManageVersion costManageVersion = getById(costManageVersionId);
        if (costManageVersion == null) {
            throw new ServiceException("数据不存在");
        }
        // 如果状态不是未确认，则不发送消息
        if (!EnumUtils.valueEquals(costManageVersion.getStatus(), CostManageStatusEnum.UNCONFIRMED)) {
            log.warn("成本管理版本状态不是未确认，不发送消息");
            return;
        }
        // 如果不是成本管理 或 不是售前成本，则不发送消息
        if (!EnumUtils.valueEquals(costManageVersion.getCostBudgetType(), CostBudgetTypeEnum.SQCB)
                || !EnumUtils.valueEquals(costManageVersion.getVersionType(), CostManageVersionEnum.CBGL)) {
            log.warn("类型不是成本管理或不是售前成本，不发送消息");
            return;
        }
        ProjectInfo projectInfo = projectInfoMapper.selectById(costManageVersion.getProjectId());
        // 客户经理Id
        Long salesmanUserId = projectInfo.getSalesmanUserId();
        // 客户经理名称
        String salesmanUser = projectInfo.getProjectSalesperson();
        // 操作人名称
        String operatorName = costManageVersion.getOperatorName();
        // 发送消息通知
        BaseSendMsgDTO baseSendMsgDTO = new BaseSendMsgDTO();
        final String title = "售前成本预算确认";
        final String content = operatorName + "发起了【" + projectInfo.getItemName() + "】的售前成本预算确认，请及时进行确认，否则售前无法进行任务分配。";
        baseSendMsgDTO.setTitle(title)
                .setContent(content)
                .setMsgTypeEnum(MsgTypeEnum.NOTICE_MSG)
                .setTargetTypeEnum(TargetTypeEnum.USERS)
                .setPath("/view-businesses/project-manage/project-manage-list?projectId=" + costManageVersion.getProjectId() + "&subTab=PROJECT_COST_MANAGEMENT")
                .setChannelEnums(CollUtil.newLinkedHashSet(ChannelEnum.WECOM, ChannelEnum.MAIL))
                .toOneTarget(salesmanUserId, salesmanUser)
                .populateSender();
        bcpMessageService.sendMsg(baseSendMsgDTO);
    }

    @Override
    public PreCostConfirmVO queryPreCostConfirm(Long projectId) {
        PreCostConfirmVO preCostConfirmVO = new PreCostConfirmVO();
        //查询成本管理最新版本
        CostManageVersion costManageVersion = getCurrentCostManageVersion(CostManageVersionEnum.CBGL, projectId);
        if (costManageVersion == null) {
            return null;
        }
        preCostConfirmVO.setStatus(costManageVersion.getStatus());
        //查询OA流程状态
        if (null != costManageVersion.getRequestId()) {
            List<CostManageVersionVO> costManageVersionList = dbApiUtil.getOaRequestStatusToObj(Arrays.asList(costManageVersion.getRequestId()), CostManageVersionVO.class);
            Integer currentNodeType = ObjectUtil.isEmpty(costManageVersionList) ? null : costManageVersionList.get(0).getRequestStatus();
            // 如果是预算总成本，并且流程已通过状态设置为已确认
            List<Integer> budgetTypeList = Arrays.asList(CostBudgetTypeEnum.ABCB.getValue(), CostBudgetTypeEnum.BBCB.getValue());
            if (budgetTypeList.contains(costManageVersion.getCostBudgetType()) && CostRequestStatusEnum.FINISH.getCurrentNodeType().equals(currentNodeType)) {
                preCostConfirmVO.setStatus(CostManageStatusEnum.CONFIRMED.getValue());
            }
        }
        preCostConfirmVO.setStatusName(EnumUtils.getNameByValue(CostManageStatusEnum.class, costManageVersion.getStatus()));
        //提交售前成本的时间：创建时间
        preCostConfirmVO.setCtime(costManageVersion.getCtime());
        //确认售前成本的时间：修改时间
        preCostConfirmVO.setMtime(costManageVersion.getMtime());
        preCostConfirmVO.setRefuseReason(costManageVersion.getRefuseReason());
        preCostConfirmVO.setId(costManageVersion.getId());
        //提交售前成本的人：创建人
        preCostConfirmVO.setCreator(costManageVersion.getCreator());
        //查询项目表获取客户经理
        ProjectInfo projectInfo = projectInfoMapper.selectById(costManageVersion.getProjectId());

        // 客户经理名称
        String salesmanUser = projectInfo.getProjectSalesperson();
        if (CostManageStatusEnum.UNCONFIRMED.getValue().equals(costManageVersion.getStatus())) {
            preCostConfirmVO.setOperatorName(salesmanUser);
        } else {
            preCostConfirmVO.setOperatorName(costManageVersion.getOperatorName());
        }
        //当前用户id
        PigxUser user = UserUtils.getUser();
        preCostConfirmVO.setUserId(user.getId());
        // 客户经理Id
        Long salesmanUserId = projectInfo.getSalesmanUserId();
        preCostConfirmVO.setSalesmanUserId(salesmanUserId);
        return preCostConfirmVO;
    }

    /**
     * 获取成本管理历史记录版本
     *
     * @param pageRequest 页面请求
     * @return {@link Page }<{@link VersionHistoryVO }>
     */
    @Override
    public Page<CostManageVersionVO> getHistoryVersions(PageRequest pageRequest, CostManageVersionDTO request) {
        request.setVersionType(CostManageVersionEnum.CBGL.getValue());
        Page<CostManageVersionVO> page =
                baseMapper.findPage(Page.of(pageRequest.getPageNumber(), pageRequest.getPageSize()), request);
        // 查询OA流程状态
        List<Long> requestIds = page.getRecords().stream()
                .filter(version -> version != null && version.getRequestId() != null)
                .map(CostManageVersionVO::getRequestId)
                .collect(Collectors.toList());
        Map<Long, CostManageStatusEnum> requestStatusMap = dbApiUtil.getOaRequestStatusToObj(requestIds, CostManageVersionVO.class).stream()
                .collect(Collectors.toMap(CostManageVersionVO::getRequestId,
                        e -> costManageEstimationResultsService.getCostManageStatusByCurrentNodeType(e.getRequestStatus()),
                        (a, b) -> a));
        // 角色Map
        Map<Long, List<ProjectStakeholderMemberVO>> memberVOMap =
                CollUtil.emptyIfNull(projectStakeholderService.getMemberByProjectId(request.getProjectId(), null).getData()).stream()
                        .collect(Collectors.groupingBy(ProjectStakeholderMemberVO::getMemberId));
        page.getRecords().forEach(e -> {
            e.setVersionStatusName(EnumUtils.getNameByValue(VersionStatusEnum.class, e.getVersionStatus()));
            CostManageStatusEnum costManageStatusEnum = requestStatusMap.get(e.getRequestId());
            e.setRequestId(null != costManageStatusEnum ? e.getRequestId() : null);
            e.setRequestName(null != costManageStatusEnum ? e.getRequestName() : null);
            e.setStatus(null == e.getRequestId()
                    ? CostManageStatusEnum.DRAFT.getValue()
                    : null != costManageStatusEnum ? costManageStatusEnum.getValue() : CostManageStatusEnum.DRAFT.getValue());
            e.setStatusName(EnumUtils.getNameByValue(CostManageStatusEnum.class, e.getStatus()));
            if (null != e.getCostBudgetType()) {
                e.setCostBudgetTypeName(EnumUtils.getNameByValue(CostBudgetTypeEnum.class, e.getCostBudgetType()));
            }
            //创建人角色设置为当前在项目中的角色
            List<Integer> roleTypes = projectTaskeService.isIronTriangle(request.getProjectId(), e.getCreatorId());
            List<String> roleNames = new ArrayList<>();
            if (CollUtil.isNotEmpty(roleTypes)) {
                roleTypes.forEach(r -> roleNames.add(EnumUtils.getNameByValue(RoleTypeEnum.class, r)));
            }
            List<Integer> memberRoleTypes = memberVOMap.getOrDefault(e.getCreatorId(), new ArrayList<>()).stream()
                    .map(ProjectStakeholderMemberVO::getRoleType)
                    .collect(Collectors.toList());
            roleTypes.addAll(memberRoleTypes);
            e.setOperatorRole(roleNames.stream().distinct().sorted().collect(Collectors.joining(",")));
        });
        return page;
    }

    /**
     * 获取当前成本管理版本记录
     *
     * @param versionEnum version 类型 enum
     * @return {@link CostManageVersion }
     */
    @Override
    public CostManageVersion getCurrentCostManageVersion(CostManageVersionEnum versionEnum, Long projectId) {
        return lambdaQuery().eq(CostManageVersion::getVersionType, versionEnum.getValue())
                .eq(CostManageVersion::getVersionStatus, VersionStatusEnum.CURRENT.getValue())
                .eq(CostManageVersion::getProjectId, projectId).one();
    }

    @Override
    public List<CostManageVersionVO> queryProjectVersion(Long projectId) {
        List<CostManageVersionVO> voList = baseMapper.queryProjectVersionByProjectId(projectId, VersionStatusEnum.CURRENT.getValue());
        if (CollUtil.isEmpty(voList)) {
            voList = new ArrayList<>();
            for (CostManageVersionEnum versionEnum : CostManageVersionEnum.values()) {
                CostManageVersionVO costManageVersionVO = new CostManageVersionVO();
                costManageVersionVO.setVersionType(versionEnum.getValue());
                costManageVersionVO.setVersionTypeName(versionEnum.getName());
                costManageVersionVO.setProjectId(projectId);
                voList.add(costManageVersionVO);
            }
        } else {
            for (CostManageVersionVO vo : voList) {
                vo.setVersionTypeName(EnumUtils.getNameByValue(CostManageVersionEnum.class, vo.getVersionType()));
            }
        }
        return voList;
    }

    @Override
    public Map<Integer, Boolean> checkCostBudgetTypeExist(Long projectId, boolean isDraft) {
        Map<Integer, Boolean> resultMap = new HashMap<>(3);
        if (projectId == null) {
            return resultMap;
        }

        // 初始化返回结果，默认所有类型都不存在
        resultMap.put(ChangeContentEnum.SQCB.getValue(), false);  // 售前成本
        resultMap.put(ChangeContentEnum.ABCB.getValue(), false);  // A表成本
        resultMap.put(ChangeContentEnum.BBCB.getValue(), false);  // B表成本
        resultMap.put(ChangeContentEnum.CASH.getValue(), false);

        // 查询项目的所有成本估算版本
        List<CostManageVersion> versionList = CollUtil.emptyIfNull(this.lambdaQuery()
                .eq(CostManageVersion::getProjectId, projectId)
                .eq(CostManageVersion::getVersionType, CostManageVersionEnum.CBGL.getValue())
                .eq(CostManageVersion::getDelFlag, 0)
                .list());

        Map<Integer, CostManageVersion> latestVersionByType = versionList.stream()
                .filter(version -> version.getCostBudgetType() != null)
                // 按costBudgetType分组
                .collect(Collectors.groupingBy(
                        CostManageVersion::getCostBudgetType,
                        // 获取每组中ctime最新的记录
                        Collectors.collectingAndThen(
                                Collectors.maxBy(Comparator.comparing(CostManageVersion::getCtime)),
                                optional -> optional.orElse(null)
                        )
                ));

        //查询项目目标信息
        CostManageTargetVO costManageTarget = costManageTargetMapper.getCostManageTargetInfo(null, projectId);

        // 获取成本对应流程id
        List<Long> requestIds = latestVersionByType.values().stream()
                .map(CostManageVersion::getRequestId).filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (null != costManageTarget) {
            requestIds.add(costManageTarget.getRequestId());
        }
        Map<Long, CostManageStatusEnum> statusEnumMap = dbApiUtil.getOaRequestStatusToObj(requestIds, CostManageVersionVO.class).stream()
                .collect(Collectors.toMap(CostManageVersionVO::getRequestId, e -> CostManageEstimationResultsServiceImpl.getCostManageStatusByCurrentNodeType(e.getRequestStatus()), (a, b) -> a));

        // 遍历所有版本，标记存在的成本类型
        latestVersionByType.forEach((costBudgetType, v) -> {
            if (costBudgetType != null) {
                if (isDraft && (null == v.getRequestId()
                        || null == statusEnumMap.get(v.getRequestId())
                        || CostManageStatusEnum.DRAFT.equals(statusEnumMap.get(v.getRequestId())))) {
                    resultMap.put(costBudgetType, true);
                } else if (!isDraft) {
                    resultMap.put(costBudgetType, true);
                }
            }
        });


        // 非已确认项目目标允许携带
        resultMap.put(ChangeContentEnum.TARGET.getValue(), null != costManageTarget && !CostManageStatusEnum.CONFIRMED.equals(statusEnumMap.get(costManageTarget.getRequestId())));

        // 现金流版本
        CostCashPlanVersionVO currentVersionVo = costCashPlanService.getCurrentVersion(projectId);
        if (EnumUtils.valueEquals(currentVersionVo.getStatus(), CostManageStatusEnum.DRAFT)) {
            resultMap.put(ChangeContentEnum.CASH.getValue(), true);
        }
        return resultMap;
    }

    @Override
    @Transactional
    public List<Long> saveByPreSalesCost() {
        // 获取所有已确认的售前成本集合
        List<CostManageVersion> preSalesCostList = CollUtil.emptyIfNull(baseMapper.getLatestPreSalesCost());
        if (CollUtil.isEmpty(preSalesCostList)) {
            return ListUtil.empty();
        }
        // 获取对应的状态
        List<CostManageVersion> needGeneratePreSalesVersions = new ArrayList<>();
        List<List<CostManageVersion>> splitList = CollUtil.split(preSalesCostList, 100);
        for (List<CostManageVersion> costManageVersions : splitList) {
            List<Long> requestIds = costManageVersions.stream().map(CostManageVersion::getRequestId).collect(Collectors.toList());
            Map<Long, CostManageStatusEnum> statusEnumMap = dbApiUtil.getOaRequestStatusToObj(requestIds, CostManageVersionVO.class).stream()
                    .collect(Collectors.toMap(CostManageVersionVO::getRequestId, e -> costManageEstimationResultsService.getCostManageStatusByCurrentNodeType(e.getRequestStatus()), (a, b) -> a));
            List<CostManageVersion> subConfirmedVersions = costManageVersions.stream()
                    .filter(version -> version.getRequestId() != null
                            && CostManageStatusEnum.CONFIRMED.equals(statusEnumMap.get(version.getRequestId())))
                    .collect(Collectors.groupingBy(CostManageVersion::getProjectId))
                    .values().stream()
                    .map(versions -> versions.stream()
                            .sorted(Comparator.comparing(CostManageVersion::getCtime).reversed())
                            .findFirst()
                            .orElse(null))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            CollUtil.addAll(needGeneratePreSalesVersions, subConfirmedVersions);
            ThreadUtil.safeSleep(1000);
        }
        if (CollUtil.isEmpty(needGeneratePreSalesVersions)) {
            log.info("未查询到需要创建项目总成本的已确认售前成本，本次操作结束！");
            return ListUtil.empty();
        }

        // 获取对应的所有项目总成本
        List<Long> projectIds = needGeneratePreSalesVersions.stream().map(CostManageVersion::getProjectId).collect(Collectors.toList());
        List<CostManageVersion> totalCostList = Optional.ofNullable(baseMapper.getLatestTotalCost(projectIds)).orElse(ListUtil.empty());
        // key-projectId value-对应项目总成本
        Map<Long, CostManageVersion> totalCostMap = totalCostList.stream()
                .collect(Collectors.toMap(CostManageVersion::getProjectId, Function.identity(), (a, b) -> a));

        // 获取所有售前成本明细
        Map<Long, List<CostManageEstimationResultsVO>> preSalesCostResultsMap =
                costManageEstimationResultsService.findByVersionId(needGeneratePreSalesVersions.stream().map(CostManageVersion::getId).collect(Collectors.toList()))
                        .stream()
                        .filter(e -> xsfyOaId.equals(e.getAccountOaId()) || xmglfysqrgOaId.equals(e.getAccountOaId()))
                        .collect(Collectors.groupingBy(CostManageEstimationResultsVO::getVersionId));

        // 获取旧版本项目总成本明细数据
        List<CostManageEstimationResultsVO> totalCostResultsList =
                CollUtil.emptyIfNull(costManageEstimationResultsService.findByVersionId(totalCostList.stream().map(CostManageVersion::getId).collect(Collectors.toList())));
        Map<Long, List<CostManageEstimationResultsVO>> totalCostResultsMap = totalCostResultsList.stream()
                .filter(e -> !xsfyOaId.equals(e.getAccountOaId()) && !xmglfysqrgOaId.equals(e.getAccountOaId()))
                .collect(Collectors.groupingBy(CostManageEstimationResultsVO::getVersionId));

        // 获取所有的关联明细数据
        List<Long> totalCostResultsIds = totalCostResultsList.stream().map(CostManageEstimationResultsVO::getId).collect(Collectors.toList());
        // key-成本估算结果id value-人员级别信息集合
        Map<Long, List<CostManagePersonnelLevelDetail>> personnelLevelMap = CollUtil.isNotEmpty(totalCostResultsIds)
                ? CollUtil.emptyIfNull(costManagePersonnelLevelDetailMapper.selectList(Wrappers.<CostManagePersonnelLevelDetail>lambdaQuery()
                        .in(CostManagePersonnelLevelDetail::getEstimationResultsId, totalCostResultsIds))).stream()
                .collect(Collectors.groupingBy(CostManagePersonnelLevelDetail::getEstimationResultsId))
                : new HashMap<>();
        Map<Long, List<CostManagePersonnelCustomDetail>> personnelCustomMap = CollUtil.isNotEmpty(totalCostResultsIds)
                ? CollUtil.emptyIfNull(costManagePersonnelCustomDetailMapper.selectList(Wrappers.<CostManagePersonnelCustomDetail>lambdaQuery()
                        .in(CostManagePersonnelCustomDetail::getEstimationResultsId, totalCostResultsIds))).stream()
                .collect(Collectors.groupingBy(CostManagePersonnelCustomDetail::getEstimationResultsId))
                : new HashMap<>();

        // 已确认的售前成本，同步创建项目总成本
        List<CostManageVersion> newTotalCostList = new ArrayList<>(needGeneratePreSalesVersions.size());
        List<CostManageEstimationResults> newResultsList = new ArrayList<>();
        List<CostManagePersonnelLevelDetail> newPersonnelLevelDetailList = new ArrayList<>();
        List<CostManagePersonnelCustomDetail> newPersonnelCustomDetailList = new ArrayList<>();
        for (CostManageVersion needGeneratePreSalesVersion : needGeneratePreSalesVersions) {
            // 创建新的项目总成本
            CostManageVersion oldTotalCost = totalCostMap.getOrDefault(needGeneratePreSalesVersion.getProjectId(), null);
            if (null == oldTotalCost) {
                continue;
            }
            Long newVersionId = IdWorker.getId();
            Long projectId = needGeneratePreSalesVersion.getProjectId();
            String newVersionName = CostConfigVersionServiceImpl.getVersionName(oldTotalCost.getVersionName());
            CostManageVersion newTotalCost = BeanUtil.copyProperties(oldTotalCost, CostManageVersion.class);
            newTotalCost.setId(newVersionId);
            newTotalCost.setVersionName(newVersionName);
            newTotalCost.setCtime(new Timestamp(System.currentTimeMillis()));
            newTotalCost.setPreSaleVersionId(needGeneratePreSalesVersion.getId());
            newTotalCost.setPreSaleVersionName(needGeneratePreSalesVersion.getVersionName());
            newTotalCost.setDelFlag(BaseConstants.NO);
            // 修改旧项目总成本状态
            oldTotalCost.setVersionStatus(VersionStatusEnum.HISTORY.getValue());
            BaseBuildEntityUtil.buildUpdate(oldTotalCost);

            // 获取旧项目总成本成本明细
            List<CostManageEstimationResultsVO> oldResults = totalCostResultsMap.getOrDefault(oldTotalCost.getId(), ListUtil.empty());
            for (CostManageEstimationResultsVO oldResult : oldResults) {
                CostManageEstimationResults newResult = BeanUtil.copyProperties(oldResult, CostManageEstimationResults.class);
                newResult.setVersionId(newVersionId);
                newResult.setProjectId(projectId);
                BaseBuildEntityUtil.buildInsert(newResult);
                newResultsList.add(newResult);

                List<CostManagePersonnelLevelDetail> oldPersonnelLevels =
                        personnelLevelMap.getOrDefault(oldResult.getId(), ListUtil.empty());
                if (CollUtil.isNotEmpty(oldPersonnelLevels)) {
                    oldPersonnelLevels.forEach(e -> {
                        CostManagePersonnelLevelDetail newCostManagePersonnelLevelDetail =
                                BeanUtil.copyProperties(e, CostManagePersonnelLevelDetail.class);
                        newCostManagePersonnelLevelDetail.setVersionId(newVersionId);
                        newCostManagePersonnelLevelDetail.setEstimationResultsId(newResult.getId());
                        BaseBuildEntityUtil.buildInsert(newCostManagePersonnelLevelDetail);
                        newPersonnelLevelDetailList.add(newCostManagePersonnelLevelDetail);
                    });
                }
                List<CostManagePersonnelCustomDetail> oldCustomDetails =
                        personnelCustomMap.getOrDefault(oldResult.getId(), ListUtil.empty());
                if (CollUtil.isNotEmpty(oldPersonnelLevels)) {
                    oldCustomDetails.forEach(e -> {
                        CostManagePersonnelCustomDetail newCostManagePersonnelCustomDetail =
                                BeanUtil.copyProperties(e, CostManagePersonnelCustomDetail.class);
                        newCostManagePersonnelCustomDetail.setVersionId(newVersionId);
                        newCostManagePersonnelCustomDetail.setEstimationResultsId(newResult.getId());
                        BaseBuildEntityUtil.buildInsert(newCostManagePersonnelCustomDetail);
                        newPersonnelCustomDetailList.add(newCostManagePersonnelCustomDetail);
                    });
                }
            }
            newTotalCostList.add(newTotalCost);

            baselineVersionRecordService.syncCostBaselineVersionRecord(projectId, StrUtil.EMPTY, newTotalCost.getCostBudgetType());

            // 修改售前成本标记位
            needGeneratePreSalesVersion.setGenerateTotalCost(0);
            BaseBuildEntityUtil.buildUpdate(needGeneratePreSalesVersion);

            // 添加售前成本科目明细
            List<CostManageEstimationResultsVO> preSaleResults = preSalesCostResultsMap.getOrDefault(needGeneratePreSalesVersion.getId(), ListUtil.empty());
            for (CostManageEstimationResultsVO preSaleResult : preSaleResults) {
                CostManageEstimationResults newResult = BeanUtil.copyProperties(preSaleResult, CostManageEstimationResults.class);
                newResult.setVersionId(newVersionId);
                newResult.setProjectId(projectId);
                BaseBuildEntityUtil.buildInsert(newResult);
                newResultsList.add(newResult);
            }
        }

        this.saveBatch(newTotalCostList);
        this.updateBatchById(totalCostMap.values());
        this.updateBatchById(needGeneratePreSalesVersions);
        costManageEstimationResultsService.saveBatch(newResultsList);
        if (CollUtil.isNotEmpty(newPersonnelLevelDetailList)) {
            costManagePersonnelLevelDetailMapper.batchSave(newPersonnelLevelDetailList);
        }
        if (CollUtil.isNotEmpty(newPersonnelCustomDetailList)) {
            costManagePersonnelCustomDetailMapper.batchSave(newPersonnelCustomDetailList);
        }

        return newTotalCostList.stream().map(CostManageVersion::getId).collect(Collectors.toList());
    }

    @Override
    public CostManageVersion getLatestCostManageVersion(CostManageVersionDTO request) {
        return baseMapper.getLatestCostManageEstimation(request);
    }

}
