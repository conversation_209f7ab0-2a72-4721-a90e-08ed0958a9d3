package com.gok.pboot.pms.enumeration;

import lombok.Getter;

/**
 * 锁定状态 Enum
 *
 * <AUTHOR>
 * @since 2023-09-26
 */
@Getter
public enum LockStatusEnum implements ValueEnum<Integer> {

    /**
     * 已锁定
     */
    YES(0, "已锁定"),

    /**
     * 待锁定
     */
    NO(1, "待锁定");

    private final Integer value;

    private final String name;

    LockStatusEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
}
