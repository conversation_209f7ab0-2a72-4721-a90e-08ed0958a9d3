package com.gok.pboot.pms.entity.vo;

import com.gok.pboot.pms.entity.domain.ProjectInfo;
import com.google.common.base.Strings;
import lombok.*;

import java.math.BigDecimal;

/**
 * 工时审核项目维度详情统计
 *
 * <AUTHOR>
 * @version 1.3.2
 */
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class DailyReviewProjectViewTotalVO {

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 审核数
     */
    private Integer approvalNum;

    /**
     * 总工时合计
     */
    private BigDecimal totalHours;

    /**
     * 正常工时合计
     */
    private BigDecimal normalHours;

    /**
     * 加班工时合计
     */
    private BigDecimal addedHours;

    /**
     * 工作日加班工时合计
     */
    private String workOvertimeHours;

    /**
     * 休息日加班工时合计
     */
    private String restOvertimeHours;

    /**
     * 节假日加班工时合计
     */
    private String holidayOvertimeHours;


    public static DailyReviewProjectViewTotalVO empty(ProjectInfo project) {
        return new DailyReviewProjectViewTotalVO(
                String.valueOf(project.getId()), Strings.nullToEmpty(project.getItemName()),
                0, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO,
               "0.00","0.00", "0.00"
        );
    }
}
