package com.gok.pboot.pms.cost.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class PersonInfoConditionDTO {

    /**
     * 是否有全部权限
     */
    private Boolean isAll;

    /**
     * 状态(true: 模板)
     */
    private Boolean exportStatus;

    /**
     * 可用状态(0: 不可用,1: 可用)
     */
    private Integer availableStatus;

    /**
     * 主键ID
     */
    private List<Long> ids;

    /**
     * 项目ID
     */
    private List<Long> projectIds;

    /**
     * 在场/已离场(0: 在场,1: 已离场)
     */
    private Integer status;

    /**
     * 姓名或工号
     */
    private String nameOrWorkCode;

    /**
     * 归属月份集合
     * 目前仅收入测算使用
     */
    private List<String> belongMonth;

    /**
     * 数据权限
     */
    private List<Long> userIds;

    /**
     * 人员属性(0: 国科人员,1: 第三方)
     */
    private Integer personnelAttribute;

}