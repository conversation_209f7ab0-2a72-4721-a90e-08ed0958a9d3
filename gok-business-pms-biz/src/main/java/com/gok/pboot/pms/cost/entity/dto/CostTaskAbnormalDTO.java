package com.gok.pboot.pms.cost.entity.dto;

import com.gok.pboot.pms.cost.enums.TaskAbnormalTypeEnum;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * 异常工单查询DTO
 * <AUTHOR>
 */
@Data
public class CostTaskAbnormalDTO {
    
    /**
     * 工单起止日期范围-开始日期
     */
    private LocalDate startDate;
    
    /**
     * 工单起止日期范围-结束日期
     */
    private LocalDate endDate;
    
    /**
     * 工单负责人ID列表
     */
    private List<Long> taskOwnerIds;
    
    /**
     * 审核人ID列表
     */
    private List<Long> reviewerIds;
    
    /**
     * 项目名称
     */
    private String projectName;
    
    /**
     * 工单提交日期范围-开始日期
     */
    private LocalDate submitStartDate;
    
    /**
     * 工单提交日期范围-结束日期
     */
    private LocalDate submitEndDate;
    
    /**
     * 异常类型（1未提交、2未审核、3未拆解、4未评价）
     * @see TaskAbnormalTypeEnum#getValue()
     */
    private Integer abnormalType;
} 