package com.gok.pboot.pms.entity.domain;


import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目客情状态表（数仓同步）
 *
 * <AUTHOR>
 * @date 2023-07-11 17:05:26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("project_customer_state")
public class ProjectCustomerState extends BeanEntity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 项目id
     */
    private Long projectId;
    /**
     * 联系人
     */
    private String contact;
    /**
     * 联系方式
     */
    private String contactPhone;
    /**
     * 角色
     */
    private String role;
    /**
     * 状态
     */
    private String status;
    /**
     * 评判依据
     */
    private String judgeBasis;
    /**
     * 支持度
     */
    private String supportDegree;


}
