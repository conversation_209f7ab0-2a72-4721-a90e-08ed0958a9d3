package com.gok.pboot.pms.eval.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.common.exception.ServiceException;
import com.gok.pboot.pms.enumeration.YesOrNoEnum;
import com.gok.pboot.pms.eval.entity.domain.EvalProjectManager;
import com.gok.pboot.pms.eval.entity.domain.EvalProjectOverview;
import com.gok.pboot.pms.eval.entity.dto.EvalProjectManagerDTO;
import com.gok.pboot.pms.eval.entity.vo.EvalProjectManagerVO;
import com.gok.pboot.pms.eval.entity.vo.EvalProjectOverviewVO;
import com.gok.pboot.pms.eval.entity.vo.EvalUserRoleVO;
import com.gok.pboot.pms.eval.enums.*;
import com.gok.pboot.pms.eval.mapper.EvalProjectManagerMapper;
import com.gok.pboot.pms.eval.mapper.EvalProjectOverviewMapper;
import com.gok.pboot.pms.eval.service.IEvalProjectManagerService;
import com.gok.pboot.pms.eval.service.IEvalUserRoleService;
import com.google.common.collect.ImmutableMap;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 项目经理评价表服务实现类
 *
 * <AUTHOR>
 * @create 2025/05/08
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class EvalProjectManagerServiceImpl extends ServiceImpl<EvalProjectManagerMapper, EvalProjectManager> implements IEvalProjectManagerService {

    private final EvalProjectOverviewMapper evalProjectOverviewMapper;

    private final IEvalUserRoleService evalUserRoleService;

    @Override
    @Transactional
    public List<Long> autoSaveEvalProjectManager(List<EvalProjectOverview> evalProjectOverviews) {
        if (CollUtil.isEmpty(evalProjectOverviews)) {
            log.info("项目整体评价集合为空，操作结束！");
            return ListUtil.empty();
        }

        List<EvalProjectManager> saveEntries = new ArrayList<>();
        evalProjectOverviews.forEach(e -> {
            // 封装项目工作指标
            saveEntries.addAll(assembleProjectWorkIndex(e));
            // 封装职业行为指标
            saveEntries.addAll(assembleProfessionalBehaviorIndex(e));
        });

        if (CollUtil.isNotEmpty(saveEntries)) {
            saveEntries.forEach(e -> BaseBuildEntityUtil.buildInsertNoUser(e, "admin"));
            this.saveBatch(saveEntries);
        }
        return saveEntries.stream().map(EvalProjectManager::getId).collect(Collectors.toList());
    }

    /**
     * 组装项目工作指标集合
     */
    private List<EvalProjectManager> assembleProjectWorkIndex(EvalProjectOverview evalProjectOverview) {
        List<EvalProjectManager> projectWorkList = new ArrayList<>();
        if (null == evalProjectOverview) {
            return projectWorkList;
        }

        // 完成及时性
        EvalProjectManager timelinessItem = EvalProjectManager.builder()
                .projectId(evalProjectOverview.getProjectId())
                .overviewId(evalProjectOverview.getId())
                .deliverType(evalProjectOverview.getDeliverType())
                .indexType(EvalIndexTypeEnum.PROJECT_WORK.getValue())
                .assessmentProject(AssessmentProjectEnum.COMPLETION_TIMELINESS.getValue())
                .weight(AssessmentProjectEnum.COMPLETION_TIMELINESS.getWeight())
                .score(BigDecimal.ZERO)
                .build();
        projectWorkList.add(timelinessItem);

        // 完成质量
        EvalProjectManager qualityItem = EvalProjectManager.builder()
                .projectId(evalProjectOverview.getProjectId())
                .overviewId(evalProjectOverview.getId())
                .deliverType(evalProjectOverview.getDeliverType())
                .indexType(EvalIndexTypeEnum.PROJECT_WORK.getValue())
                .assessmentProject(AssessmentProjectEnum.COMPLETION_QUALITY.getValue())
                .weight(AssessmentProjectEnum.COMPLETION_QUALITY.getWeight())
                .score(BigDecimal.ZERO)
                .build();
        projectWorkList.add(qualityItem);

        // 合规性
        EvalProjectManager complianceItem = EvalProjectManager.builder()
                .projectId(evalProjectOverview.getProjectId())
                .overviewId(evalProjectOverview.getId())
                .deliverType(evalProjectOverview.getDeliverType())
                .indexType(EvalIndexTypeEnum.PROJECT_WORK.getValue())
                .assessmentProject(AssessmentProjectEnum.COMPLIANCE.getValue())
                .weight(AssessmentProjectEnum.COMPLIANCE.getWeight())
                .score(BigDecimal.ZERO)
                .build();
        projectWorkList.add(complianceItem);

        return projectWorkList;
    }

    /**
     * 组装职业行为指标集合
     */
    private List<EvalProjectManager> assembleProfessionalBehaviorIndex(EvalProjectOverview evalProjectOverview) {
        List<EvalProjectManager> behaviorList = new ArrayList<>();
        if (null == evalProjectOverview) {
            return behaviorList;
        }

        // 协作沟通
        EvalProjectManager collaborationItem = EvalProjectManager.builder()
                .projectId(evalProjectOverview.getProjectId())
                .overviewId(evalProjectOverview.getId())
                .deliverType(evalProjectOverview.getDeliverType())
                .indexType(EvalIndexTypeEnum.PROFESSIONAL_BEHAVIOR.getValue())
                .assessmentProject(AssessmentProjectEnum.COLLABORATION.getValue())
                .weight(AssessmentProjectEnum.COLLABORATION.getWeight())
                .score(BigDecimal.ZERO)
                .build();
        behaviorList.add(collaborationItem);

        // 工作态度
        EvalProjectManager attitudeItem = EvalProjectManager.builder()
                .projectId(evalProjectOverview.getProjectId())
                .overviewId(evalProjectOverview.getId())
                .deliverType(evalProjectOverview.getDeliverType())
                .indexType(EvalIndexTypeEnum.PROFESSIONAL_BEHAVIOR.getValue())
                .assessmentProject(AssessmentProjectEnum.WORK_ATTITUDE.getValue())
                .weight(AssessmentProjectEnum.WORK_ATTITUDE.getWeight())
                .score(BigDecimal.ZERO)
                .build();
        behaviorList.add(attitudeItem);

        return behaviorList;
    }

    @Override
    public Map<Long, List<EvalProjectManagerVO>> findEvalProjectManagerVOList(List<EvalProjectOverviewVO> overviewVOList) {
        if (CollUtil.isEmpty(overviewVOList)) {
            return ImmutableMap.of();
        }

        // 获取所有项目评价ID
        List<Long> overviewIds = overviewVOList.stream()
                .map(EvalProjectOverviewVO::getId)
                .collect(Collectors.toList());

        // 查询项目经理评价详情
        LambdaQueryWrapper<EvalProjectManager> queryWrapper = Wrappers.<EvalProjectManager>lambdaQuery()
                .in(EvalProjectManager::getOverviewId, overviewIds);
        List<EvalProjectManager> managerList = this.list(queryWrapper);

        if (CollUtil.isEmpty(managerList)) {
            return ImmutableMap.of();
        }

        // 转换为VO并计算得分
        Map<Long, EvalProjectOverviewVO> projectOverviewMap = overviewVOList.stream()
                .collect(Collectors.toMap(EvalProjectOverviewVO::getId, v -> v));

        Map<Long, List<EvalProjectManagerVO>> resultMap = new HashMap<>();

        // 按项目整体评价ID分组
        Map<Long, List<EvalProjectManager>> groupedManagers = managerList.stream()
                .collect(Collectors.groupingBy(EvalProjectManager::getOverviewId));

        for (Map.Entry<Long, List<EvalProjectManager>> entry : groupedManagers.entrySet()) {
            Long overviewId = entry.getKey();
            List<EvalProjectManager> managers = entry.getValue();
            EvalProjectOverviewVO overviewVO = projectOverviewMap.get(overviewId);

            if (overviewVO == null) {
                continue;
            }

            List<EvalProjectManagerVO> managerVOList = new ArrayList<>();
            if (EvalStatusEnum.ARCHIVE.getValue().equals(overviewVO.getEvalStatus())) {
                managerVOList = managers.stream()
                        .map(e -> {
                            EvalProjectManagerVO managerVO = convertToVO(e);
                            managerVO.setScore(e.getScore());
                            managerVO.setManagerScore(e.getManagerScore());
                            managerVO.setPmoScore(e.getPmoScore());
                            return managerVO;
                        })
                        .collect(Collectors.toList());
            } else {

                // 转换为VO并计算每个指标的得分
                for (EvalProjectManager manager : managers) {
                    EvalProjectManagerVO managerVO = convertToVO(manager);

                    // 计算单项得分（managerScore和pmoScore的平均值）
                    BigDecimal score = calculateScore(manager.getManagerScore(), manager.getPmoScore());
                    managerVO.setScore(score);
                    managerVO.setManagerScore(manager.getManagerScore());
                    managerVO.setPmoScore(manager.getPmoScore());
                    managerVOList.add(managerVO);
                }
            }
            overviewVO.setEvalProjectManagerList(managerVOList);
            resultMap.put(overviewId, managerVOList);
        }

        return resultMap;
    }

    /**
     * 将实体转换为VO
     */
    private EvalProjectManagerVO convertToVO(EvalProjectManager manager) {
        EvalProjectManagerVO vo = new EvalProjectManagerVO();
        vo.setId(manager.getId())
                .setProjectId(manager.getProjectId())
                .setOverviewId(manager.getOverviewId())
                .setIndexType(manager.getIndexType())
                .setIndexTypeName(EnumUtils.getNameByValue(EvalIndexTypeEnum.class, manager.getIndexType()))
                .setAssessmentProject(manager.getAssessmentProject())
                .setAssessmentProjectName(EnumUtils.getNameByValue(AssessmentProjectEnum.class, manager.getAssessmentProject()))
                .setWeight(manager.getWeight());
        return vo;
    }

    /**
     * 计算单项得分（managerScore和pmoScore的平均值）
     */
    private BigDecimal calculateScore(BigDecimal managerScore, BigDecimal pmoScore) {
        if (managerScore == null && pmoScore == null) {
            return BigDecimal.ZERO;
        }
        if (managerScore == null) {
            return pmoScore;
        }
        if (pmoScore == null) {
            return managerScore;
        }
        return managerScore.add(pmoScore).divide(new BigDecimal("2"), 2, RoundingMode.HALF_UP);
    }

    /**
     * 计算平均得分
     */
    private BigDecimal calculateAverageScore(List<BigDecimal> scores) {
        if (CollUtil.isEmpty(scores)) {
            return BigDecimal.ZERO;
        }
        BigDecimal sum = scores.stream()
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        return sum.divide(new BigDecimal(scores.size()), 2, RoundingMode.HALF_UP);
    }

    /**
     * 计算项目经理总得分
     */
    private BigDecimal calculateManagerTotalScore(BigDecimal projectWorkScore, BigDecimal professionalBehaviorScore) {
        // 获取项目工作和职业行为的权重总和
        BigDecimal projectWorkWeight = AssessmentProjectEnum.COMPLETION_TIMELINESS.getWeight()
                .add(AssessmentProjectEnum.COMPLETION_QUALITY.getWeight())
                .add(AssessmentProjectEnum.COMPLIANCE.getWeight());

        BigDecimal professionalBehaviorWeight = AssessmentProjectEnum.COLLABORATION.getWeight()
                .add(AssessmentProjectEnum.WORK_ATTITUDE.getWeight());

        // 计算总得分
        return projectWorkScore.multiply(projectWorkWeight)
                .add(professionalBehaviorScore.multiply(professionalBehaviorWeight))
                .setScale(2, RoundingMode.HALF_UP);
    }

    @Override
    @Transactional
    public void score(List<EvalProjectManagerDTO> requestList) {
        if (CollUtil.isEmpty(requestList)) {
            return;
        }
        Long overviewId = requestList.get(0).getOverviewId();

        // 获取当前用户ID
        Long currentUserId = SecurityUtils.getUser().getId();

        // 获取所有用户角色
        List<EvalUserRoleVO> allRoles = CollUtil.emptyIfNull(evalUserRoleService.findAll());
        // 判断当前用户角色是否PMO
        boolean isPmo = allRoles.stream().anyMatch(role -> role.getUserId().equals(currentUserId)
                && EvalUserRoleEnum.PMO.getValue().equals(role.getRole()));
        // 获取项目整体评价信息，用于判断总支撑官/职能领导
        EvalProjectOverviewVO overview = evalProjectOverviewMapper.getById(overviewId);
        boolean isSuperManager = null != overview && currentUserId.equals(overview.getSupportManagerId());
        if (!isPmo && !isSuperManager) {
            log.warn("当前用户无评分权限");
            return;
        }
        if (EvalStatusEnum.ARCHIVE.getValue().equals(overview.getEvalStatus())) {
            log.warn("已归档评价无法评分");
            return;
        }

        // 获取项目经理评价列表
        LambdaQueryWrapper<EvalProjectManager> queryWrapper = Wrappers.<EvalProjectManager>lambdaQuery()
                .eq(EvalProjectManager::getOverviewId, overviewId)
                .eq(EvalProjectManager::getDelFlag, YesOrNoEnum.NO.getValue());
        List<EvalProjectManager> scoreEntries = this.list(queryWrapper);
        if (CollUtil.isEmpty(scoreEntries)) {
            return;
        }

        Map<Integer, EvalProjectManagerDTO> scoreMap = requestList.stream()
                .collect(Collectors.toMap(EvalProjectManagerDTO::getAssessmentProject, Function.identity(), (v1, v2) -> v1));

        if (isSuperManager) {
            for (EvalProjectManager evalProjectManager : scoreEntries) {
                EvalProjectManagerDTO requestScore = scoreMap.get(evalProjectManager.getAssessmentProject());
                BigDecimal managerScore = requestScore.getManagerScore();
                // 总支撑官/职能领导评分
                if (evalProjectManager.getManagerScore() != null || null == managerScore) {
                    continue;
                }
                if (managerScore.compareTo(BigDecimal.ZERO) < 0 || managerScore.compareTo(new BigDecimal("5")) > 0) {
                    throw new ServiceException("总支撑官/职能领导评分非法");
                }
                evalProjectManager.setManagerScore(managerScore);
            }
        }

        if (isPmo) {
            for (EvalProjectManager evalProjectManager : scoreEntries) {
                EvalProjectManagerDTO requestScore = scoreMap.get(evalProjectManager.getAssessmentProject());
                BigDecimal pmoScore = requestScore.getPmoScore();
                // PMO评分
                if (evalProjectManager.getPmoScore() != null || null == pmoScore) {
                    continue;
                }
                if (pmoScore.compareTo(BigDecimal.ZERO) < 0 || pmoScore.compareTo(new BigDecimal("5")) > 0) {
                    throw new ServiceException("PMO评分非法");
                }
                evalProjectManager.setPmoScore(pmoScore);
            }
        }

        for (EvalProjectManager evalProjectManager : scoreEntries) {
            // 重新计算平均分
            evalProjectManager.setScore(calculateScore(evalProjectManager.getManagerScore(), evalProjectManager.getPmoScore()));
            BaseBuildEntityUtil.buildUpdate(evalProjectManager);
        }

        // 批量更新项目经理得分
        baseMapper.batchScore(scoreEntries);

        // 项目评价同步变更
        EvalProjectOverview evalProjectOverview = evalProjectOverviewMapper.selectById(overviewId);
        evalProjectOverview.setManagerEvalStatus(getManagerEvalStatus(scoreEntries).getValue());
        BaseBuildEntityUtil.buildUpdate(evalProjectOverview);
        evalProjectOverviewMapper.updateById(evalProjectOverview);
    }

    private ManagerEvalStatusEnum getManagerEvalStatus(List<EvalProjectManager> evalProjectManagerList) {
        AtomicBoolean isPmoSore = new AtomicBoolean(false);
        AtomicBoolean isManagerSore = new AtomicBoolean(false);
        evalProjectManagerList.forEach(e -> {
            if (null != e.getPmoScore()) {
                isPmoSore.set(true);
            }
            if (null != e.getManagerScore()) {
                isManagerSore.set(true);
            }
        });
        if (isPmoSore.get() && isManagerSore.get()) {
            return ManagerEvalStatusEnum.ALL;
        } else if (isPmoSore.get() && !isManagerSore.get()) {
            return ManagerEvalStatusEnum.PMO;
        } else if (!isPmoSore.get() && isManagerSore.get()) {
            return ManagerEvalStatusEnum.MANAGER;
        } else {
            return ManagerEvalStatusEnum.UNEVALUATED;
        }
    }

}
