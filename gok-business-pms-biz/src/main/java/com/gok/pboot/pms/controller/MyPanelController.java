package com.gok.pboot.pms.controller;

import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.entity.vo.MyBaseInfoVO;
import com.gok.pboot.pms.service.MyPanelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 个人面板controller
 *
 * <AUTHOR>
 * @date 2023/8/27
 */
@RestController()
@RequestMapping("myPanel")
public class MyPanelController {

    @Autowired
    private MyPanelService service;

    /**
     * 获取个人模板基本信息
     * @return {@link ApiResult<MyBaseInfoVO>}
     */
    @GetMapping("/findMyBaseInfo")
    public ApiResult<MyBaseInfoVO> findMyBaseInfo(){
        return ApiResult.success(service.findMyBaseInfo());
    }

    /**
     * 获取 本月待填日报数
     * @return {@link ApiResult}<{@link Integer}>
     */
    @GetMapping("/getNoFillDailyPaperCount")
    public ApiResult<Integer> getNoFillDailyPaperCount(){
        return ApiResult.success(service.getNoFillDailyPaperCount());
    }

    /**
     * 获取当前用户待审核工时数量
     * @return {@link ApiResult}<{@link Integer}>
     */
    @GetMapping("/getPendingApprovalCount")
    public ApiResult<Integer> getPendingApprovalCount(){
        return ApiResult.success(service.getPendingApprovalCount());
    }
}
