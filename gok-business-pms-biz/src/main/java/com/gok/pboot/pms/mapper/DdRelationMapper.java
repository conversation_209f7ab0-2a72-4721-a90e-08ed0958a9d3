package com.gok.pboot.pms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.entity.domain.DdRelation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 滴滴关联表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Mapper
public interface DdRelationMapper extends BaseMapper<DdRelation> {

    /**
     * 根据关联ID和类型查询关联关系
     *
     * @param relateId   关联ID
     * @param relateType 关联类型
     * @return 关联关系
     */
    DdRelation selectByRelateIdAndType(@Param("relateId") Long relateId, @Param("relateType") Integer relateType);



    /**
     * 根据滴滴项目ID和关联类型查询关联关系
     *
     * @param didiProjectId 滴滴项目ID
     * @param relateType    关联类型
     * @return 关联关系
     */
    DdRelation selectByDidiIdAndType(@Param("didiProjectId") String didiProjectId, @Param("relateType") Integer relateType);

    /**
     * 更新同步时间
     *
     * @param relateId   关联ID
     * @param relateType 关联类型
     * @return 更新行数
     */
    int updateSyncTimeByRelateIdAndType(@Param("relateId") Long relateId, @Param("relateType") Integer relateType);


}
