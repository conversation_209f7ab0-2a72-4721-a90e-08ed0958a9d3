package com.gok.pboot.pms.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.common.user.PigxUser;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.entity.SysDept;
import com.gok.pboot.pms.entity.dto.*;
import com.gok.pboot.pms.entity.vo.*;

import javax.annotation.Nullable;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 统计查询
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-24
 */
public interface ICountQueryService {



    /**
     * 查询日报提交一览表
     * @param pageRequest 分页参数
     * @param dailyFindPageDTO 查询参数
     * @return 列表数据
     */
    ApiResult<Page<DailyFindPageVO>> dailyFindPage(
            PageRequest pageRequest,
            DailyFindPageDTO dailyFindPageDTO
    );

    /**
     * 查询异常日报统计信息（日报提交一览表页）
     * @param dailyFindPageDTO 查询参数
     * @return 统计信息
     */
    AbnormalStatisticsVO findAbnormalStatistics(DailyFindPageDTO dailyFindPageDTO);

    /**
     * 查询异常日报
     * @param dailyFindPageDTO 查询参数
     * @param abnormalType 异常类型
     * @return 异常日报列表
     */
    List<AbnormalDailyPaperExcelVO> findAbnormal(DailyFindPageDTO dailyFindPageDTO, Integer abnormalType);

    /**
     * 查询异常日报
     * @param dailyFindPageDTO 查询参数
     * @return 异常日报列表
     */
    List<AbnormalDailyPaperExcelVO> findAbnormal(DailyFindPageDTO dailyFindPageDTO);

    /**
     * 获取异常日报数据
     *
     * @param pageRequest
     * @param dailyFindPageDTO
     * @param isDataScope      是否需要权限过滤  true：是   false：否
     * @return
     */
    ApiResult<AbnormalDailyPaperFindPageVO> findAbnormalPage(PageRequest pageRequest, DailyFindPageDTO dailyFindPageDTO, boolean isDataScope);

    void sendWeiXinCpMessageForAbnormal(DailyFindPageDTO dailyFindPageDTO, List<AbnormalDailyPaperExcelVO> list ,PigxUser user);

    void sendWeiXinCpMsgForAuditor(DailyFindPageDTO dailyFindPageDTO, boolean manual, PigxUser user);

    List<UnReviewedDailyPaperEntryExcelVO> exportUnreviewed(ProjectHourSumFindPageDTO dailyFindPageDTO) throws ClassNotFoundException, IllegalAccessException, InstantiationException;

    List<UnReviewedDailyPaperEntryExcelVO> exportReviewed(ProjectHourSumFindPageDTO dailyFindPageDTO) throws ClassNotFoundException, IllegalAccessException, InstantiationException;

    List<UnReviewedDailyPaperEntryExcelVO> exportAll(ProjectHourSumFindPageDTO dailyFindPageDTO) throws ClassNotFoundException, IllegalAccessException, InstantiationException;

    ApiResult<Page<AllocationFindPageVO>> allocationFindPage(Boolean ifExport, AllocationFindPageDTO allocationFindPageDTO);

    /**
     * 获取工时分摊表橄榄
     *
     * @param allocationFindPageDTO 请求对象
     * @return {@link AllocationOverviewVO}
     */
    AllocationOverviewVO findOverview(AllocationFindPageDTO allocationFindPageDTO);

    void allocationExport(HttpServletResponse response, AllocationFindPageDTO allocationFindPageDTO);

    ApiResult<Page<ProjectHourSumFindPageVO>> projectHourSumFindPage(
            PageRequest pageRequest,
            ProjectHourSumFindPageDTO projectHourSumFindPageDTO,
            @Nullable Boolean showUnreviewed
    );

    void projectHourSumExport(HttpServletResponse response, ProjectHourSumFindPageDTO projectHourSumFindPageDTO);

    ApiResult<Page<PersonnelReuseFindPageVO>> personnelReuseFindPage(PageRequest pageRequest, PersonnelReuseFindPageDTO personnelReuseFindPageDTO);

    void personnelReuseExport(HttpServletResponse response, PersonnelReuseFindPageDTO personnelReuseFindPageDTO);

    String getResultUnitName(Long unitId, Map<Long, SysDept> orgMap, String resultName);

    WorkHourStatisticsFindPageVO workHourStatisticsFindPage(WorkHourStatisticsFindPageDTO dto);

    /**
     * 项目工时明细分页
     *
     * @param dto 搜索条件
     * @return {@link ApiResult<Page<ProjectHourDetailsFindPageVO>>}
     */
    ApiResult<Page<ProjectHourDetailsFindPageVO>> projectHourDetailsFindPage(ProjectHourDetailsFindPageDTO dto);

    /**
     * 项目工时明细导出
     *
     * @param dto 搜索条件
     * @return {@link List<ProjectHourDetailsFindPageVO>}
     */
    List<ProjectHourDetailsFindPageVO> projectHourDetailsExport(ProjectHourDetailsFindPageDTO dto);

    /**
     * 分页查询交付人员
     *
     * @param dto 查询条件
     * @return {@link ApiResult<Page<DelivererFindPageVO>>}
     */
    ApiResult<Page<DelivererFindPageVO>> delivererFindPage(DelivererFindPageDTO dto);

    /**
     * 导出交付人员
     *
     * @param dto 查询条件
     * @return {@link List<DelivererFindPageVO>}
     */
    List<DelivererFindPageVO> delivererExport(DelivererFindPageDTO dto);

    /**
     * 项目工时明细分页(新)
     *
     * @param pageRequest
     * @param projectHourSumFindPageDTO
     * @param showUnreviewed
     * @return
     */
    ApiResult<Page<ProjectHourSumFindPageVO>> projectHourSumFindPageV2(PageRequest pageRequest, ProjectHourSumFindPageDTO projectHourSumFindPageDTO, Boolean showUnreviewed) throws ClassNotFoundException, IllegalAccessException, InstantiationException;

    /**
     * 导出 - 日报提交一览表
     *
     * @param dto 日报提交一览表查询请求
     * @return 日报提交一览表导出Vo类集合[]
     */
    @Deprecated
    void exportDaily(DailyFindPageDTO dto, HttpServletResponse response);

    List<ProjectHourSumFindPageVO> projectHourSumExport2(ProjectHourSumFindPageDTO projectHourSumFindPageDTO,
                                                          Boolean showUnreviewed) throws ClassNotFoundException, IllegalAccessException, InstantiationException;;

    void exportReviewedAndUnreviewed(HttpServletResponse response, ProjectHourSumFindPageDTO projectHourSumFindPageDTO);
    /**
     * 分页 查询日报提交一览表导出
     *
     * @param dailyFindPageDTO 日报查找分页对象
     * @return {@link ApiResult<Page<DailyFindPageVO>>}
     * @customParam name 姓名
     * @customParam deptId 部门编号
     * @customParam personnelStatus 人员状态
     * @customParam startTime 日期开始时间
     * @customParam endTime 日期结束时间
     */
    List<DailyExcelExportVO> dailyExport(DailyFindPageDTO dailyFindPageDTO);

}
