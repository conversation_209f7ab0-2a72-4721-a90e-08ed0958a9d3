package com.gok.pboot.pms.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.bcp.upms.vo.SysMenuVo;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.R;
import com.gok.pboot.pms.entity.domain.CustomerInfo;
import com.gok.pboot.pms.entity.dto.CreateRequestDTO;
import com.gok.pboot.pms.entity.dto.CustomerAttentionDTO;
import com.gok.pboot.pms.entity.dto.CustomerCommonPageDTO;
import com.gok.pboot.pms.entity.dto.CustomerPageDTO;
import com.gok.pboot.pms.entity.vo.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 合同台账明细表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-02-23 10:46:40
 */
public interface ICustomerInfoService extends IService<CustomerInfo> {


    /**
     * 获取客户台账-分页
     *
     * @param filter 客户台账查询DTO
     * @return {@link ApiResult}<{@link Page}<{@link CustomerListVO}>>
     */
    Page<CustomerListVO> findPage(CustomerPageDTO filter);

    /**
     * 根据类型发起流程
     *
     * @param dto 项目里程碑发起流程Dto
     * @return String
     */
    R<String> doCreateRequest(CreateRequestDTO dto);

    /**
     * 通过id获取客户台账详情
     *
     * @param id 客户台账id
     * @return {@link ApiResult}<{@link CustomerInfoVO}>
     */
    CustomerInfoVO findById(Long id);

    /**
     * 通过id获取简单客户台账详情
     *
     * @param id 客户台账id
     * @return {@link ApiResult}<{@link CustomerInfoVO}>
     */
    CustomerInfoVO simplyFindById(Long id);

    /**
     * 关注/取消关注
     *
     * @param dto 客户台账id
     * @return {@link ApiResult}<{@link String}>
     */
    String attention(CustomerAttentionDTO dto);

    /**
     * 获取客户台账-分页（通用组件）
     *
     * @param filter 客户台账查询DTO
     * @return {@link ApiResult}<{@link Page}<{@link CustomerCommonVO}>>
     */
    Page<CustomerCommonVO> findCommonPage(CustomerCommonPageDTO filter);


    /**
     * 通过ids获取客户台账详情（通用组件）
     *
     * @param ids 客户台账ids
     * @return {@link ApiResult}<{@link List}<{@link CustomerCommonVO}>
     */
    List<CustomerCommonVO> findByIds(List<Long> ids);

    /**
     * 获取客户台账数量
     *
     * @return {@link ApiResult}<{@link Integer}>
     */
    CustomerNumVO findCustomerNum();

    /**
     * 模糊查询所在城市（参数没传值，默认前50个）
     *
     * @return {@link ApiResult}<{@link List}<{@link OaHrmcityVO}>
     */
    List<OaHrmcityVO> getCityListByName(String name);

    /**
     * 通过id获取客户台账详情头部
     *
     * @param id 客户台账id
     * @return {@link ApiResult}<{@link CustomerInfoVO}>
     */
    CustomerHeadVO getHeadById(Long id, HttpServletRequest request);

    /**
     * 获取菜单权限
     *
     * @param request 请求对象
     * @return {@link ApiResult}<{@link List}<{@link SysMenuVo}>>
     * customParam filter_L_customerId 传入的客户id
     * customParam filter_S_permission 传入的权限标识
     * customParam filter_S_menuType 传入的菜单类型（0菜单 1按钮，9系统）
     */
    List<SysMenuVo> getMenuAuthority(HttpServletRequest request);
}

