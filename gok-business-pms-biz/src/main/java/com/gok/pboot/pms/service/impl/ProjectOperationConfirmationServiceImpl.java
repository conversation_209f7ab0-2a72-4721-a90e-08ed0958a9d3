package com.gok.pboot.pms.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.components.common.user.PigxUser;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.entity.domain.ProjectOperationConfirmation;
import com.gok.pboot.pms.entity.dto.ProjectOperationConfirmationDTO;
import com.gok.pboot.pms.entity.dto.ProjectSystemProcessInfoDTO;
import com.gok.pboot.pms.enumeration.*;
import com.gok.pboot.pms.eval.entity.domain.EvalUserRole;
import com.gok.pboot.pms.eval.enums.EvalUserRoleEnum;
import com.gok.pboot.pms.eval.mapper.EvalUserRoleMapper;
import com.gok.pboot.pms.mapper.ProjectOperationConfirmationMapper;
import com.gok.pboot.pms.service.IProjectOperationConfirmationService;
import com.gok.pboot.pms.service.IProjectSystemProcessInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 项目操作状态确认记录表服务实现类
 *
 * <AUTHOR> Generated
 * @since 2025-07-02
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProjectOperationConfirmationServiceImpl extends ServiceImpl<ProjectOperationConfirmationMapper, ProjectOperationConfirmation>
        implements IProjectOperationConfirmationService {

    private final EvalUserRoleMapper evalUserRoleMapper;

    private final IProjectSystemProcessInfoService systemProcessService;

    @Override
    @Transactional
    public List<Long> saveOrUpdate(ProjectOperationConfirmationDTO dto) {
        if (null == dto) {
            return null;
        }

        // 未指定用户则获取当前操作用户
        PigxUser user = SecurityUtils.getUser();
        Integer module = dto.getModule();
        Long projectId = dto.getProjectId();
        Long userId = Optional.ofNullable(dto.getUserId()).orElse(user.getId());
        String userName = StrUtil.isNotBlank(dto.getUserName()) ? dto.getUserName() : user.getName();
        List<Integer> operationRoleList = Optional.ofNullable(dto.getRole()).orElse(getOperationRole(projectId, userId));
        if (CollUtil.isEmpty(operationRoleList)) {
            log.warn("当前用户：{}无操作权限", user.getName());
            return ListUtil.empty();
        }

        // 判断是否确认操作
        boolean isConfirmedOperate = dto.getOperationType().equals(ProjectOperationTypeEnum.CONFIRM.getValue());

        LocalDate reminderDate = dto.getReminderDate();

        // 查找每个项目内不同角色对应的操作
        LambdaQueryWrapper<ProjectOperationConfirmation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectOperationConfirmation::getModule, module);
        queryWrapper.in(ProjectOperationConfirmation::getProjectId, projectId);
        queryWrapper.in(ProjectOperationConfirmation::getRole, operationRoleList.stream().filter(r -> !r.equals(ProjectOperationRoleEnum.PMO.getValue())).collect(Collectors.toList()));
        List<ProjectOperationConfirmation> entityList = baseMapper.selectList(queryWrapper);

        if (CollUtil.isEmpty(entityList)) {
            return ListUtil.empty();
        }

        List<ProjectOperationConfirmation> updateList = new ArrayList<>();
        for (ProjectOperationConfirmation entity : entityList) {
            if (entity.getOperationType().equals(ProjectOperationTypeEnum.CONFIRM.getValue())) {
                log.info("当前用户角色已确认，操作结束");
                continue;
            }
            entity.setUserId(userId);
            entity.setUserName(userName);
            entity.setOperationType(dto.getOperationType());
            entity.setConfirmTime(isConfirmedOperate ? LocalDateTime.now() : null);
            entity.setReminderDate(reminderDate);
            BaseBuildEntityUtil.buildUpdate(entity);
            updateList.add(entity);
        }
        this.saveOrUpdateBatch(updateList);

        // 记录过程动态
        if (isConfirmedOperate) {
            saveConfirmSystemProcess(projectId, CollUtil.getFirst(entityList).getId(), module);
        }

        return entityList.stream().map(ProjectOperationConfirmation::getId).collect(Collectors.toList());
    }

    @Transactional
    public void saveConfirmSystemProcess(Long projectId, Long operationConfirmationId, Integer module) {
        if (null == operationConfirmationId) {
            return;
        }

        String name = ProjectOperationModuleEnum.WARRANTY.getValue().equals(module)
                ? "项目质保：项目质保确认"
                : "项目关闭：项目关闭确认";

        ProjectSystemProcessInfoDTO systemProcessInfoDTO = ProjectSystemProcessInfoDTO.builder()
                .name(name)
                .status(String.valueOf(ProcessInfoStatusEnum.ARCHIVE.getValue()))
                .processType(SystemProcessTypeEnum.PROJECT_CONFIRM.getValue())
                .applicatId(SecurityUtils.getUser().getId())
                .applicat(SecurityUtils.getUser().getName())
                .projectId(projectId)
                .requestId(operationConfirmationId)
                .build();
        systemProcessService.innerBatchSave(Arrays.asList(systemProcessInfoDTO));
    }

    @Override
    public boolean judgeConfirmedComplete(Long projectId, Integer module) {
        ProjectOperationConfirmation condition = ProjectOperationConfirmation.builder()
                .projectId(projectId)
                .module(module)
                .operationType(ProjectOperationTypeEnum.CONFIRM.getValue())
                .build();
        List<ProjectOperationConfirmation> entityList =
                CollUtil.emptyIfNull(baseMapper.selectList(new QueryWrapper<>(condition)));

        // 获取对应模块确认操作需要的角色数量
        int confirmRoleNum =
                ProjectOperationRoleEnum.getConfirmModuleValue(EnumUtils.getEnumByValue(ProjectOperationModuleEnum.class, module));
        if (0 == confirmRoleNum) {
            // 无需角色确认则直接返回
            return true;
        } else {
            // 判断已确认的角色数量和需要操作的角色数量是否一致
            return entityList.stream().map(ProjectOperationConfirmation::getRole).distinct().collect(Collectors.toList()).size()
                    == confirmRoleNum
                    ? true
                    : false;
        }
    }

    /**
     * 获取当前用户操作角色
     *
     * @param projectId
     * @param currentUserId
     * @return {@link ProjectOperationRoleEnum#getValue}
     */
    @Override
    public List<Integer> getOperationRole(Long projectId, Long currentUserId) {
        List<Integer> roleList = new ArrayList<>();
        Map<String, Long> projectRoleIdMap = baseMapper.getProjectRoleIdMap(projectId);
        if (CollUtil.isEmpty(projectRoleIdMap)) {
            return roleList;
        }

        if (currentUserId.equals(projectRoleIdMap.get("managerUserId"))) {
            roleList.add(ProjectOperationRoleEnum.MANAGER.getValue());
        }
        if (currentUserId.equals(projectRoleIdMap.get("salesmanUserId"))) {
            roleList.add(ProjectOperationRoleEnum.SALESMAN.getValue());
        }
        if (currentUserId.equals(projectRoleIdMap.get("managerUserLeaderId"))) {
            roleList.add(ProjectOperationRoleEnum.MANAGER_LEADER.getValue());
        }
        if (currentUserId.equals(projectRoleIdMap.get("salesmanUserLeaderId"))) {
            roleList.add(ProjectOperationRoleEnum.SALES_LEADER.getValue());
        }

        EvalUserRole query = EvalUserRole.builder()
                .role(EvalUserRoleEnum.PMO.getValue())
                .build();
        List<EvalUserRole> evalUserRoles = CollUtil.emptyIfNull(evalUserRoleMapper.selectList(new QueryWrapper<>(query)));
        for (EvalUserRole evalUserRole : evalUserRoles) {
            if (currentUserId.equals(evalUserRole.getUserId())) {
                roleList.add(ProjectOperationRoleEnum.PMO.getValue());
            }
        }

        return roleList.stream().distinct().collect(Collectors.toList());
    }

    @Override
    public List<Long> batchSaveKnowOperation(Long projectId, Integer module, List<ProjectOperationRoleEnum> roleEnumList) {
        if (null == projectId || null == module || CollUtil.isEmpty(roleEnumList)) {
            return ListUtil.empty();
        }

        LambdaQueryWrapper<ProjectOperationConfirmation> queryWrapper = Wrappers.<ProjectOperationConfirmation>query().lambda()
                .eq(ProjectOperationConfirmation::getProjectId, projectId)
                .eq(ProjectOperationConfirmation::getModule, module)
                .in(ProjectOperationConfirmation::getRole, roleEnumList.stream().map(ProjectOperationRoleEnum::getValue).collect(Collectors.toList()));
        List<ProjectOperationConfirmation> existList = baseMapper.selectList(queryWrapper);

        if (CollUtil.isNotEmpty(existList)) {
            existList.stream().forEach(entity -> {
                entity.setOperationType(ProjectOperationTypeEnum.KNOW.getValue());
                BaseBuildEntityUtil.buildUpdate(entity);
            });
            this.saveOrUpdateBatch(existList);
            return existList.stream().map(ProjectOperationConfirmation::getId).collect(Collectors.toList());
        }

        return ListUtil.empty();
    }

}
