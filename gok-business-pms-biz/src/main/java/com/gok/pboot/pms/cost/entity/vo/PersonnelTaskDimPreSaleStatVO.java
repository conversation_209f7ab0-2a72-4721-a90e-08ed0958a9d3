package com.gok.pboot.pms.cost.entity.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 人员工单看板-工单维度售前工单统计VO
 *
 * <AUTHOR>
 * @date 2025/05/15
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PersonnelTaskDimPreSaleStatVO extends PersonnelPreSaleStatBaseVO {

    /**
     * 人员工号
     */
    @ExcelProperty("员工工号")
    private String workCode;

    /**
     * 人员姓名
     */
    @ExcelProperty("员工姓名")
    private String employeeName;

    /**
     * 项目ID
     */
    @ExcelIgnore
    private Long projectId;

    /**
     * 项目编号
     */
    @ExcelProperty("项目编号")
    private String projectNo;

    /**
     * 项目名称
     */
    @ExcelProperty("项目名称")
    private String projectName;

    @ExcelIgnore
    private Long taskId;

    /**
     * 工单编号
     */
    @ExcelProperty("工单编号")
    private String taskNo;

    /**
     * 工单名称
     */
    @ExcelProperty("工单名称")
    private String taskName;

    /**
     * 工单综合评分
     */
    @ExcelProperty("工单综合评分")
    private BigDecimal comprehensiveScore;


}
