package com.gok.pboot.pms.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import com.gok.pboot.pms.enumeration.BusinessOrderReimbursedStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 滴滴酒店订单表
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("dd_hotel_order")
public class DdHotelOrder extends BeanEntity<Long> {

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 入住人ID
     */
    private Long checkinUserId;

    /**
     * 入住人工号
     */
    private String checkinEmployeeNo;

    /**
     * 入住人姓名
     */
    private String checkinPersonName;

    /**
     * 入住人部门ID
     */
    private Long checkinDeptId;

    /**
     * 入住人部门名称
     */
    private String checkinDeptName;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 房间房型
     */
    private String roomType;

    /**
     * 入住时间
     */
    private LocalDate checkinTime;

    /**
     * 离店时间
     */
    private LocalDate checkoutTime;

    /**
     * 企业实付金额
     */
    private BigDecimal companyActualPayment;

    /**
     * 服务费
     */
    private BigDecimal serviceFee;

    /**
     * 天数
     */
    private BigDecimal numberOfDays;

    /**
     * 房间数
     */
    private BigDecimal numberOfRooms;

    /**
     * 间夜
     */
    private BigDecimal roomNights;

    /**
     * 单价
     */
    private BigDecimal unitPrice;

    /**
     * 房间差标
     */
    private BigDecimal roomStandardDifference;

    /**
     * 订单状态
     */
    private String orderStatus;

    /**
     * 预订日期
     */
    private LocalDate bookingDate;

    /**
     * 预订人ID
     */
    private Long bookingUserId;

    /**
     * 预订人工号
     */
    private String bookingEmployeeNo;

    /**
     * 预订人姓名
     */
    private String bookingEmployeeName;

    /**
     * 预订人部门ID
     */
    private Long bookingDeptId;

    /**
     * 预订人部门名称
     */
    private String bookingDeptName;

    /**
     * 出差申请单号
     */
    private String businessTripApplicationNo;

    /**
     * 出差事由
     */
    private String businessTripReason;

    /**
     * 成本中心ID
     */
    private Long costCenterId;

    /**
     * 成本中心名称
     */
    private String costCenterName;

    /**
     * 所属项目ID
     */
    private Long projectId;

    /**
     * 所属项目名称
     */
    private String projectName;

    /**
     * 项目编码
     */
    private String projectCode;

    /**
     * 所属公司ID
     */
    private Long companyId;

    /**
     * 所属公司名称
     */
    private String companyName;

    /**
     * 所属账期(YYYY-MM)
     */
    private String accountingPeriod;

    /**
     * 报销单号
     */
    private String expenseReportNo;

    /**
     * 发起状态(0=待发起,1=已发起,2=发起错误-OA接口报错,3=发起错误-预算不足)
     *
     * @see BusinessOrderReimbursedStatusEnum#getValue()
     */
    private Integer initiationStatus;

    /**
     * 发起日期
     */
    private LocalDate initiationDate;

    /**
     * 流程发起ID
     */
    private Long requestId;

    @TableField(exist = false)
    private Integer projectStatus;
}