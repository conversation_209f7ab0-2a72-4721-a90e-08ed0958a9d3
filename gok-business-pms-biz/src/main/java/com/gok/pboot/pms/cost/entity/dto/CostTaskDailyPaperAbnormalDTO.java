package com.gok.pboot.pms.cost.entity.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.List;

/**
 * 异常工时列表查询DTO
 *
 * <AUTHOR>
 * @date 2024/04/16
 */
@Data
@Accessors(chain = true)
public class CostTaskDailyPaperAbnormalDTO {

    /**
     * 提交日期范围 - 开始日期
     */
    private LocalDate startDate;

    /**
     * 提交日期范围 - 结束日期
     */
    private LocalDate endDate;

    /**
     * 工单负责人ID列表
     */
    private List<Long> taskOwnerIds;

    /**
     * 审核人ID列表
     */
    private List<Long> reviewerIds;

    /**
     * 项目名称（模糊匹配）
     */
    private String projectName;

    /**
     * 异常类型（2未审核、4未评价）
     */
    private Integer abnormalType;
} 