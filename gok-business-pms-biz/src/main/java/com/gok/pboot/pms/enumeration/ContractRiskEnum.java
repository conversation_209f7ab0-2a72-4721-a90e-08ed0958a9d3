package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 合同风险类型枚举
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Getter
@AllArgsConstructor
public enum ContractRiskEnum implements ValueEnum<String> {

    /**
     * 合同收付风险
     */
    PAYMENT("0", "合同款项风险"),

    /**
     * 进度管控风险
     */
    CONTROL("1", "进度管控风险");

    private final String value;

    private final String name;


}
