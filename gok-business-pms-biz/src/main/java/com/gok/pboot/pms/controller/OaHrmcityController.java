package com.gok.pboot.pms.controller;


import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.entity.vo.OaHrmcityVO;
import com.gok.pboot.pms.service.IOaHrmcityService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Oa所在城市
 *
 * <AUTHOR>
 * @date 2023/11/20
 * @menu 所在城市
 */
@RestController
@RequestMapping("/oa-city")
@AllArgsConstructor
public class OaHrmcityController {

    private final IOaHrmcityService service;

    /**
     * 模糊查询所在城市（参数没传值，默认前50个）
     *
     * @return {@link ApiResult}<{@link List}<{@link OaHrmcityVO}>
     */
    @GetMapping("/getListByName")
    public ApiResult<List<OaHrmcityVO>> getListByName(@RequestParam("name") String name) {
        return ApiResult.success(service.getListByName(name));
    }

    /**
     * 获取所有城市
     *
     * @return {@link ApiResult }<{@link List }<{@link OaHrmcityVO }>>
     */
    @GetMapping("/getAllList")
    public ApiResult<List<OaHrmcityVO>> getAllCityList() {
        return ApiResult.success(service.getAllCityList(), "获取成功");
    }
}
