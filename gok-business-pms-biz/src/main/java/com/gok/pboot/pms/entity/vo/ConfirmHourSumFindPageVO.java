package com.gok.pboot.pms.entity.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * @Auther chenhc
 * @Date 2022-08-24 14:52
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ConfirmHourSumFindPageVO {


    /**
     * 项目ID
     */
    @ExcelIgnore
    private Long id;

    /**
     * 项目编号
     */
    @ExcelProperty("项目编号")
    private String code;

    /**
     * 项目名称
     */
    @ExcelProperty("项目名称")
    private String projectName;

    /**
     * 项目状态
     */
    @ExcelIgnore
    private String projectStatus;

    /**
     * 项目状态值
     */
    @ExcelProperty("项目状态")
    private String projectStatusName;

    /**
     * 收入归属部门编号
     */
    @ExcelIgnore
    private Long projectDeptId;
    /**
     * 收入归属部门
     */
    @ExcelProperty("收入归属部门")
    private String projectDeptName;

    /**
     * 员工id
     */
    @ExcelIgnore
    private Long userId;

    /**
     * 姓名
     */
    @ExcelProperty("姓名")
    private String name;

    /**
     * 项目正常工时
     */
    @ExcelProperty("项目正常工时")
    private BigDecimal normalHours;

    /**
     * 项目加班工时
     */
    @ExcelProperty("项目加班工时")
    private BigDecimal addedHours;

    /**
     * OA加班工时
     */
    @ExcelProperty("OA加班工时")
    private BigDecimal hourData;

    /**
     * 项目耗用工时
     */
    @ExcelProperty("项目耗用工时")
    private BigDecimal projectHours;

    /**
     * 人员状态名称
     */
    @ExcelProperty("状态")
    private String personnelStatusName;

    /**
     * 人员归属部门全称
     */
    @ExcelProperty("人员归属部门")
    private String personnelDeptName;

    /**
     * 人员归属id
     */
    @ExcelIgnore
    private Long deptId;


    /**
     * 工时审核员
     */
    @ExcelProperty("工时审核员")
    private String privilegeUserName;

    /**
     * 销售人员
     */
    @ExcelIgnore
    private String salesmanUserName;


    /**
     * 经理人员
     */
    @ExcelIgnore
    private String managerUserName;

}
