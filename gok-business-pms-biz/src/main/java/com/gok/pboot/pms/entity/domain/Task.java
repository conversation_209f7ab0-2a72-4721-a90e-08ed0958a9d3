package com.gok.pboot.pms.entity.domain;


import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 任务
 *
 * <AUTHOR>
 * @since 2022-08-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(Task.ALIAS)
public class Task extends BeanEntity<Long> {

    public static final String ALIAS = "mhour_task";
    /**
    * 任务名称
    */
    private String taskName;
    /**
    * 任务类型（0=默认任务，1=手动添加，2=后期维保）
    */
    private Integer taskType;
    /**
    * 任务状态（0=正常，1=关闭）
    */
    private Integer taskStatus;
    /**
    * 所属项目ID
    */
    private Long projectId;


}
