package com.gok.pboot.pms.cost.entity.vo;

import cn.hutool.core.bean.BeanUtil;
import com.gok.pboot.pms.cost.entity.dto.CostManagePersonnelCustomDetailDto;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 成本管理人员级别自定义补贴明细VO类
 *
 * <AUTHOR>
 * @create 2025/01/10
 **/
@Data
public class CostManagePersonnelCustomDetailVO {

    /**
     * 自定义补贴明细ID
     */
    private Long id;

    /**
     * 版本ID
     */
    private Long versionId;

    /**
     * 成本管理估算结果ID
     */
    private Long estimationResultsId;

    /**
     * 自定义补贴配置ID
     */
    private Long subsidyCustomConfigId;

    /**
     * 自定义补贴名称
     */
    private String subsidyCustomName;

    /**
     * 自定义补贴金额
     */
    private BigDecimal subsidyCustomAmount;

    public static CostManagePersonnelCustomDetailVO of(CostManagePersonnelCustomDetailDto dto) {
        if (null == dto) {
            return new CostManagePersonnelCustomDetailVO();
        }
        return BeanUtil.copyProperties(dto, CostManagePersonnelCustomDetailVO.class);
    }

}
