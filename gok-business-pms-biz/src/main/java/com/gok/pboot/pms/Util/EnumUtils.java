package com.gok.pboot.pms.Util;


import com.gok.pboot.pms.enumeration.ValueEnum;

/**
 * - 枚举工具类 -
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/7/22 9:42
 */
@SuppressWarnings({"unchecked"})
public class EnumUtils {

    /**
    * @description 根据名称获取值
    * @param enumClass 枚举类模板
    * @param value 枚举值
    * @return java.lang.String
    * <AUTHOR>
    * @date 2022/7/22 9:44
    */
    public static<E extends Enum<? extends ValueEnum<V>>, V> String getNameByValue(Class<E> enumClass, V value){
        ValueEnum<V> nameValueEnum;

        for (Enum<? extends ValueEnum<V>> e : enumClass.getEnumConstants()){
            nameValueEnum = (ValueEnum<V>) e;
            if (nameValueEnum.getValue().equals(value)){
                return nameValueEnum.getName();
            }
        }

        return null;
    }

    /**
    * @description 根据枚举名称获取枚举值
    * @param enumClass 枚举类模板
    * @param name 枚举名称
    * @return V
    * <AUTHOR>
    * @date 2022/7/22 9:45
    */
    public static<E extends Enum<? extends ValueEnum<V>>, V> V getValueByName(Class<E> enumClass, String name){
        ValueEnum<V> nameValueEnum;

        for (Enum<? extends ValueEnum<V>> e : enumClass.getEnumConstants()){
            nameValueEnum = (ValueEnum<V>) e;
            if (nameValueEnum.getName().equals(name)){
                return nameValueEnum.getValue();
            }
        }

        return null;
    }

    /**
    * ~ 根据名称获取枚举 ~
    * @param enumClass 枚举类模板
    * @param name 枚举名称
    * @return com.gok.pboot.pms.enumeration.ValueEnum<V>
    * <AUTHOR>
    * @date 2022/7/28 9:34
    */
    public static<E extends Enum<? extends ValueEnum<V>>, V> E getEnumByName(
            Class<E> enumClass, String name
    ){
        ValueEnum<V> nameValueEnum;

        for (Enum<? extends ValueEnum<V>> e : enumClass.getEnumConstants()){
            nameValueEnum = (ValueEnum<V>) e;
            if (nameValueEnum.getName().equals(name)){
                return (E) nameValueEnum;
            }
        }

        return null;
    }

    /**
     * ~ 根据值获取枚举 ~
     * @param enumClass 枚举类模板
     * @param value 枚举值
     * @return com.gok.pboot.pms.enumeration.ValueEnum<V>
     * <AUTHOR>
     * @date 2022/7/28 9:34
     */
    public static<E extends Enum<? extends ValueEnum<V>>, V> E getEnumByValue(
            Class<E> enumClass, V value
    ){
        ValueEnum<V> nameValueEnum;

        for (Enum<? extends ValueEnum<V>> e : enumClass.getEnumConstants()){
            nameValueEnum = (ValueEnum<V>) e;
            if (nameValueEnum.getValue().equals(value)){
                return (E) nameValueEnum;
            }
        }

        return null;
    }

    public static <V> boolean valueEquals(V value, ValueEnum<V> enumObj) {
        return enumObj.getValue().equals(value);
    }

    public static <V> boolean valueOrEquals(V value, ValueEnum<V>... enumObjs) {
        for (ValueEnum<V> enumObj : enumObjs) {
            if (enumObj.getValue().equals(value)) {
                return true;
            }
        }
        return false;
    }

    public static <V> boolean valueAndEquals(V value, ValueEnum<V>... enumObjs) {
        for (ValueEnum<V> enumObj : enumObjs) {
            if (!enumObj.getValue().equals(value)) {
                return false;
            }
        }
        return true;
    }

    public static <V> boolean enumOrEquals(ValueEnum<V> e, ValueEnum<V>... enumObjs) {
        for (ValueEnum<V> enumObj : enumObjs) {
            if (enumObj.equals(e)) {
                return true;
            }
        }
        return false;
    }

    public static <V> boolean enumAndEquals(ValueEnum<V> e, ValueEnum<V>... enumObjs) {
        for (ValueEnum<V> enumObj : enumObjs) {
            if (!enumObj.equals(e)) {
                return false;
            }
        }
        return true;
    }

    public static <E extends Enum<? extends ValueEnum<V>>, V> boolean existsEnumValue(V value, Class<E> clazz){
        ValueEnum<V> nameValueEnum;

        for (E e : clazz.getEnumConstants()) {
            nameValueEnum = ((ValueEnum<V>) e);
            if (nameValueEnum.getValue().equals(value)){
                return true;
            }
        }

        return false;
    }
}
