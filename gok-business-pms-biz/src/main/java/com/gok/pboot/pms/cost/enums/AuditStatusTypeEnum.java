package com.gok.pboot.pms.cost.enums;

import com.gok.pboot.pms.enumeration.ValueEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum AuditStatusTypeEnum implements ValueEnum<Integer> {

    /**
     * A表流程同步
     */
    ABLCTB(0, "A表流程同步"),
    WSH(1, "未审核"),
    WTG(2, "未通过"),
    YSH(3, "已审核"),
    BBLCYFQ(4, "B表流程已发起"),
    BBLCYGD(5, "B表流程已归档");

    private final Integer value;

    private final String name;

}