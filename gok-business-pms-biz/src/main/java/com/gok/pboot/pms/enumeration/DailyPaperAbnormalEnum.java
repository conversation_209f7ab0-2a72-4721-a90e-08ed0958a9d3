package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;

/**
 * 异常日报类型枚举
 *
 * <AUTHOR>
 * @version 1.3.4
 */
@AllArgsConstructor
public enum DailyPaperAbnormalEnum implements ValueEnum<Integer> {

    SUBMIT(0, "提交异常"),
    WAITING_REVIEW(1, "待审核")
    ;

    private final Integer value;

    private final String name;

    @Override
    public Integer getValue() {
        return value;
    }

    @Override
    public String getName() {
        return name;
    }
}
