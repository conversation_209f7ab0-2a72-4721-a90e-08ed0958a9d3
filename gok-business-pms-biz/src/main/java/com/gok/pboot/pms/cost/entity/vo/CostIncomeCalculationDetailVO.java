package com.gok.pboot.pms.cost.entity.vo;

import cn.hutool.core.bean.BeanUtil;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.cost.entity.domain.CostIncomeCalculationDetail;
import com.gok.pboot.pms.cost.enums.ConfirmStatusEnum;
import com.gok.pboot.pms.cost.enums.PersonnelAttributeEnum;
import com.gok.pboot.pms.cost.enums.SettlementStatusEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 收入测算明细
 *
 * <AUTHOR>
 * @create 2025/02/18
 **/
@Data
public class CostIncomeCalculationDetailVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 收入测算汇总ID
     */
    private Long calculationId;

    /**
     * 姓名
     */
    private String memberName;

    /**
     * 工号
     */
    private String workCode;

    /**
     * 人员属性（0=国科人员，1=第三方）
     * {@link com.gok.pboot.pms.cost.enums.PersonnelAttributeEnum}
     */
    private Integer personnelAttribute;

    private String personnelAttributeTxt;

    /**
     * 岗位ID
     */
    private Long jobId;

    /**
     * 岗位名称
     */
    private String jobName;

    /**
     * 归属月份
     */
    private LocalDate belongMonth;

    /**
     * 结算人天
     */
    private BigDecimal settlementHours;

    /**
     * 结算单价
     */
    private BigDecimal settlementUnitPrice;

    /**
     * 客户承担费用
     */
    private BigDecimal customerBearingAmount;

    /**
     * 测算含税金额
     */
    private BigDecimal estimatedInclusiveAmountTax;

    /**
     * 确认状态（0=待确认，1=已确认，2=部分确认）
     * {@link com.gok.pboot.pms.cost.enums.ConfirmStatusEnum}
     */
    private Integer confirmStatus;

    private String confirmStatusTxt;

    /**
     * 确认日期
     */
    private LocalDate confirmDate;

    /**
     * 结算状态（0=待结算，1=已结算）
     * {@link com.gok.pboot.pms.cost.enums.SettlementStatusEnum}
     */
    private Integer settlementStatus;

    private String settlementStatusTxt;

    /**
     * 报价税率
     */
    private Integer quotedRateId;

    private String quotedRateIdTxt;

    /**
     * 结算明细数据
     */
    private List<CostIncomeSettlementDetailVO> settlementList;

    /**
     * 转换为VO
     *
     * @param entity 实体
     * @return VO
     */
    public static CostIncomeCalculationDetailVO of(CostIncomeCalculationDetail entity,
                                                   Map<Integer, String> taxRateMap,
                                                   Map<Long, List<CostIncomeSettlementDetailVO>> settlementDetailMap) {
        if (null == entity) {
            return null;
        }
        CostIncomeCalculationDetailVO vo = BeanUtil.copyProperties(entity, CostIncomeCalculationDetailVO.class);
        vo.setConfirmStatusTxt(EnumUtils.getNameByValue(ConfirmStatusEnum.class, entity.getConfirmStatus()));
        vo.setSettlementStatusTxt(EnumUtils.getNameByValue(SettlementStatusEnum.class, entity.getSettlementStatus()));
        vo.setPersonnelAttributeTxt(EnumUtils.getNameByValue(PersonnelAttributeEnum.class, entity.getPersonnelAttribute()));
        vo.setQuotedRateIdTxt(null != vo.getQuotedRateId() ? taxRateMap.get(vo.getQuotedRateId()) : null);
        vo.setSettlementList(settlementDetailMap.get(entity.getId()));
        return vo;
    }

}
