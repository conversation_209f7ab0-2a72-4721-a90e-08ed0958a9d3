/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 */

package com.gok.pboot.pms.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import com.gok.pboot.pms.entity.dto.ProjectTaskGroupAddDTO;
import com.gok.pboot.pms.entity.dto.ProjectTaskGroupUpdateDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 任务分组
 *
 * <AUTHOR> generator
 * @date 2023-08-18 10:07:30
 */
@Data
@TableName("project_task_group")
@EqualsAndHashCode(callSuper = true)
public class ProjectTaskGroup extends BeanEntity<Long> {

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 分组名称
     */
    private String title;

    /**
     * 在制品数量（最大任务数限制）
     */
    private Integer capacity;

    public static ProjectTaskGroup of(ProjectTaskGroupAddDTO dto) {
        ProjectTaskGroup result = new ProjectTaskGroup();
        Integer capacity = dto.getCapacity();

        result.setProjectId(dto.getProjectId());
        result.setTitle(dto.getTitle());
        if(capacity != null){
            result.setCapacity(capacity);
        }

        return result;
    }

    public static ProjectTaskGroup update(ProjectTaskGroupUpdateDTO dto) {
        ProjectTaskGroup result = new ProjectTaskGroup();
        String title = dto.getTitle();
        Integer capacity = dto.getCapacity();

        result.setId(dto.getGroupId());
        if (title != null) {
            result.setTitle(title);
        }
        if (capacity != null) {
            result.setCapacity(capacity);
        }

        return result;
    }
}
