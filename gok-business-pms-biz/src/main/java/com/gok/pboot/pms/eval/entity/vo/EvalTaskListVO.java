package com.gok.pboot.pms.eval.entity.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2025/5/8
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class EvalTaskListVO {
    /**
     * 工单id
     */
    @ExcelIgnore
    private Long taskId;

    /**
     * 项目id
     */
    @ExcelIgnore
    private Long projectId;

    /**
     * 项目编号
     */
    @ExcelProperty("项目编号")
    private String projectNo;

    /**
     * 项目名称
     */
    @ExcelProperty("项目名称")
    private String projectName;

    /**
     * 工单负责人id
     */
    @ExcelIgnore
    private Long managerId;

    /**
     * 工单负责人编号
     */
    @ExcelProperty("人员工号")
    private String managerNo;

    /**
     * 工单负责人名称
     */
    @ExcelProperty("人员名称")
    private String managerName;

    /**
     * 工单编号
     */
    @ExcelProperty("工单编号")
    private String taskNo;

    /**
     * 工单名称
     */
    @ExcelProperty("工单名称")
    private String taskName;

    /**
     * 评价状态
     */
    @ExcelIgnore
    private Integer evaluationStatus;

    /**
     * 评价状态Txt
     */
    @ExcelProperty("评价状态")
    private String evaluationStatusTxt;

    /**
     * 综合得分
     */
    @ExcelProperty("综合得分")
    private BigDecimal comprehensiveScore;

    /**
     * 结算工单产值
     */
    @ExcelProperty("结算工单产值")
    private BigDecimal income;

    /**
     * 结算工单产值占比
     */
    @ExcelProperty("结算工单产值占比")
    private String settlementValueRatio;

    /**
     * 工单预算人工成本
     */
    @ExcelIgnore
    private String budgetCostStr;
    /**
     * 工单预算人工成本
     */
    @ExcelProperty("工单预算人工成本")
    private String budgetCost;

    /**
     * 工单实际人工成本
     */
    @ExcelIgnore
    private String actualLaborCostStr;
    /**
     * 工单实际人工成本
     */
    @ExcelProperty("工单实际人工成本")
    private String actualLaborCost;

    /**
     * 工单预算工时
     */
    @ExcelProperty("工单预算工时")
    private BigDecimal estimatedHours;

    /**
     * 工单实际工时
     */
    @ExcelProperty("工单实际工时")
    private BigDecimal actualHours;

    /**
     * 工单状态
     */
    @ExcelIgnore
    private Integer taskStatus;

    /**
     * 工单状态Txt
     */
    @ExcelProperty("工单状态")
    private String taskStatusTxt;

    /**
     * 是否有权限评价
     */
    @ExcelIgnore
    private Boolean isEvaluation;
}
