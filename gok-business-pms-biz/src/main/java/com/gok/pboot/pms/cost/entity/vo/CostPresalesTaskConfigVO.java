package com.gok.pboot.pms.cost.entity.vo;

import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.cost.entity.domain.CostPresalesTaskConfig;
import com.gok.pboot.pms.cost.entity.domain.CostTaskCategoryManagement;
import com.gok.pboot.pms.cost.enums.CostPresalesTaskManagerRoleEnum;
import com.gok.pboot.pms.cost.enums.CostPresalesTaskManagerTypeEnum;
import com.gok.pboot.pms.cost.enums.CostTaskDisassemblyTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 售前报工工单配置VO
 *
 * <AUTHOR>
 * @date 2025/01/15
 */
@Data
@Accessors(chain = true)
public class CostPresalesTaskConfigVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 工单名称
     */
    private String taskName;

    /**
     * 父级ID
     */
    private Long parentId;

    /**
     * 工单类别
     */
    private Integer taskCategory;

    /**
     * 工单类别文本
     */
    private String taskCategoryTxt;

    /**
     * 工单级别（1一级，2二级，3三级）
     */
    private Integer taskLevel;

    /**
     * 工单描述
     */
    private String taskDesc;

    /**
     * 拆解类型（0=标准工单，1=总成工单）
     */
    private Integer disassemblyType;

    /**
     * 拆解类型文本
     */
    private String disassemblyTypeTxt;

    /**
     * 工单负责人类型(0=项目角色，1=指定人)
     */
    private Integer managerType;

    /**
     * 工单负责人类型文本
     */
    private String managerTypeTxt;

    /**
     * 工单负责人角色(0=售前经理、1=项目经理、2=客户经理)
     */
    private Integer managerRole;

    /**
     * 工单负责人角色文本
     */
    private String managerRoleTxt;

    /**
     * 工单负责人id
     */
    private Long managerId;

    /**
     * 工单负责人姓名
     */
    private String managerName;

    /**
     * 子级集合
     */
    private List<CostPresalesTaskConfigVO> children;

    public static CostPresalesTaskConfigVO buildVo(CostPresalesTaskConfig entity, Map<Integer, CostTaskCategoryManagement> costTaskCategoryMap){
        return new CostPresalesTaskConfigVO()
                .setId(entity.getId())
                .setParentId(entity.getParentId())
                .setTaskName(entity.getTaskName())
                .setTaskCategory(entity.getTaskCategory())
                .setTaskLevel(entity.getTaskLevel())
                .setTaskDesc(entity.getTaskDesc())
                .setDisassemblyType(entity.getDisassemblyType())
                .setManagerType(entity.getManagerType())
                .setManagerRole(entity.getManagerRole())
                .setManagerId(entity.getManagerId())
                .setManagerName(entity.getManagerName())
                .setTaskCategoryTxt(costTaskCategoryMap.getOrDefault(entity.getTaskCategory(), new CostTaskCategoryManagement()).getTaskCategoryName())
                .setDisassemblyTypeTxt(EnumUtils.getNameByValue(CostTaskDisassemblyTypeEnum.class, entity.getDisassemblyType()))
                .setManagerTypeTxt(EnumUtils.getNameByValue(CostPresalesTaskManagerTypeEnum.class, entity.getManagerType()))
                .setManagerRoleTxt(EnumUtils.getNameByValue(CostPresalesTaskManagerRoleEnum.class, entity.getManagerRole()));
    }
} 