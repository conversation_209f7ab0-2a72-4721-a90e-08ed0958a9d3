package com.gok.pboot.pms.entity.vo;


import com.gok.pboot.pms.entity.domain.ProjectCustomerState;
import com.gok.pboot.pms.enumeration.ExpectedOrderAmountWeightedEnum;
import com.google.common.base.Strings;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

/**
 * 项目客情状态表（数仓同步）
 *
 * <AUTHOR>
 * @date 2023-07-11 17:05:26
 */
@Data
public class ProjectCustomerStateVO {
    /**
     * id
     */
    private Long id;

    /**
     * 项目id
     */
    private Long projectId;
    /**
     * 联系人
     */
    private String contact;
    /**
     * 联系方式
     */
    private String contactPhone;
    /**
     * 角色
     */
    private String role;
    /**
     * 字典名
     *
     * @see com.gok.pboot.pms.enumeration.ProjectCuntomerRoleEnum YES_STR NO_STR null
     */
    private String roleName;

    /**
     * 客情状态
     *
     * @see com.gok.pboot.pms.enumeration.ExpectedOrderAmountWeightedEnum YES_STR NO_STR null
     */
    private String status;

    /**
     * 客情状态字典名
     */
    private String statusName;
    /**
     * 评判依据
     */
    private String judgeBasis;
    /**
     * 支持度
     */
    private String supportDegree;
    /**
     * 创建时间
     */
    private Date ctime;

    public static ProjectCustomerStateVO from(ProjectCustomerState po) {
        ProjectCustomerStateVO result = new ProjectCustomerStateVO();
        result.setId(po.getId());
        result.setContact(Strings.nullToEmpty(po.getContact()));
        result.setContactPhone(Strings.nullToEmpty(po.getContactPhone()));
        result.setProjectId(po.getProjectId());
        result.setRole(Strings.nullToEmpty(po.getRole()));
        result.setRoleName("");
        //角色
        String status = Strings.nullToEmpty(po.getStatus());
        result.setStatus(status);
        result.setStatusName(StringUtils.EMPTY.equals(status) ? StringUtils.EMPTY :
                ExpectedOrderAmountWeightedEnum.getNameByVal(Integer.parseInt(status)));

        result.setJudgeBasis(Strings.nullToEmpty(po.getJudgeBasis()));
        result.setSupportDegree(Strings.nullToEmpty(po.getSupportDegree()));
        return result;
    }
}
