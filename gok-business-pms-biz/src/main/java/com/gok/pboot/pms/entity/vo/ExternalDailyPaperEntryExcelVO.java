package com.gok.pboot.pms.entity.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * - 外部项目日报条目导出 -
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/12/13 16:41
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@ToString
public class ExternalDailyPaperEntryExcelVO {
    @ExcelIgnore
    private Long userId;
    @ExcelProperty("填报人")
    private String userRealName;

    @ExcelIgnore
    private String workCode;

    @ExcelIgnore
    private Long userDeptId;

    @ExcelProperty("部门")
    private String deptName;

    @ExcelIgnore
    private Integer userStatus;

    @ExcelProperty("人员状态")
    private String userStatusName;

    @ExcelIgnore
    private Long projectId;

    @ExcelProperty("所属项目")
    private String projectName;

    @ExcelProperty("项目状态")
    private String projectStatusName;

    @ExcelProperty("任务名称")
    private String taskName;

    @ExcelIgnore
    private LocalDate submissionDate;

    @ExcelProperty("填报日期")
    private String submissionDateFormatted;

    @ExcelProperty("工作内容")
    private String description;

    @ExcelProperty("正常工时（天）")
    private BigDecimal normalHours;

    @ExcelProperty("加班工时（天）")
    private BigDecimal addedHours;

    @ExcelProperty("总工时")
    private BigDecimal totalHours;

    @ExcelProperty("加班工时（OA）")
    private BigDecimal oaAddedHours;




}
