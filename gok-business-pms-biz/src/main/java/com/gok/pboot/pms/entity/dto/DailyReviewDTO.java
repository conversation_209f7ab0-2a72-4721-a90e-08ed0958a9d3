package com.gok.pboot.pms.entity.dto;

import com.gok.pboot.pms.common.base.PageRequest;
import lombok.*;

import java.time.LocalDate;
import java.util.List;

/**
 * 日报审核项目维度
 *
 * <AUTHOR>
 * @version 1.3.2
 */
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class DailyReviewDTO extends PageRequest {

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 开始时间
     */
    private LocalDate startTime;

    /**
     * 结束时间
     */
    private LocalDate endTime;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 审批状态 0待审批 1已审批
     * @see com.gok.pboot.enumeration.entity.YesOrNoEnum
     */
    private Integer auditStatus;

    /**
     * 人员名称
     */
    private String username;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户id集合
     */
    private List<Long> userIds;

    /**
     * 我负责的（0：不勾选，1：勾选）
     * @see com.gok.pboot.enumeration.entity.YesOrNoEnum
     */
    private Integer inCharge;
}
