package com.gok.pboot.pms.entity.dto;

import com.gok.bcp.message.dto.BcpMessageDTO;
import com.gok.bcp.message.dto.BcpMessageTargetDTO;
import com.gok.bcp.message.entity.enums.ChannelEnum;
import com.gok.bcp.message.entity.enums.MsgTypeEnum;
import com.gok.bcp.message.entity.enums.TargetTypeEnum;
import com.gok.components.common.user.PigxUser;
import com.gok.pboot.common.security.util.SecurityUtils;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * 发送消息 DTO
 *
 * <AUTHOR>
 * @date 2025/01/07
 */
@Data
@Accessors(chain = true)
public class BaseSendMsgDTO {

    /**
     * 发送人 ID
     */
    @NotNull(message = "发送人ID不能为空")
    private Long senderId;

    /**
     * 寄件人
     */
    @NotBlank(message = "发送人不能为空")
    private String sender;

    /**
     * 目标列表
     */
    @NotEmpty(message = "目标列表不能为空")
    private List<BcpMessageTargetDTO> targetList;

    /**
     * 目标类型
     */
    @NotNull(message = "目标类型不能为空")
    private TargetTypeEnum targetTypeEnum;

    /**
     * 标题
     */
    @NotBlank(message = "标题类型不能为空")
    private String title;

    /**
     * 内容
     */
    @NotBlank(message = "内容不能为空")
    private String content;

    /**
     * 链接前缀
     * 可为空，为空时默认取业务一体化的链接
     */
    private String linkPrefix;

    /**
     * 路径
     */
    private String path;


    /**
     * 消息类型枚举
     */
    @NotNull(message = "消息类型不能为空")
    private MsgTypeEnum msgTypeEnum;

    /**
     * 渠道
     */
    @NotEmpty(message = "渠道不能为空")
    private Set<ChannelEnum> channelEnums;


    /**
     * 填充发件人（根据当前用户填充）
     *
     * @return {@link BaseSendMsgDTO }
     */
    public BaseSendMsgDTO populateSender() {
        PigxUser user = SecurityUtils.getUser();
        if (user == null) {
            this.setSenderId(10000L);
            this.setSender("admin");
        } else {
            this.setSenderId(user.getId());
            this.setSender(user.getName());
        }
        return this;
    }

    /**
     * 填充发件人（手动指定）
     *
     * @param senderId 发件人 ID
     * @param sender   发件人名称
     * @return {@link BaseSendMsgDTO }
     */
    public BaseSendMsgDTO populateSender(Long senderId, String sender) {
        this.setSenderId(senderId);
        this.setSender(sender);
        return this;
    }

    /**
     * 指定一个目标用户
     *
     * @param targetUserId 目标用户 ID
     * @param targetUser   目标用户
     * @return {@link BaseSendMsgDTO }
     */
    public BaseSendMsgDTO toOneTarget(Long targetUserId, String targetUser) {
        BcpMessageTargetDTO targetDTO = new BcpMessageTargetDTO(String.valueOf(targetUserId), StringUtils.isBlank(targetUser) ? "defaultName" : targetUser, null);
        this.targetList = Collections.singletonList(targetDTO);
        return this;
    }

    /**
     * dto转换
     *
     * @param sendMsgDTO 发送消息 DTO
     * @return {@link BcpMessageDTO }
     */
    public static BcpMessageDTO of(BaseSendMsgDTO sendMsgDTO) {
        BcpMessageDTO bcpMessageDTO = new BcpMessageDTO();
        bcpMessageDTO.setSender(sendMsgDTO.getSender());
        bcpMessageDTO.setSenderId(sendMsgDTO.getSenderId());
        bcpMessageDTO.setTitle(sendMsgDTO.getTitle());
        bcpMessageDTO.setContent(sendMsgDTO.getContent());
        bcpMessageDTO.setType(sendMsgDTO.getMsgTypeEnum().getValue());
        bcpMessageDTO.setTargetType(sendMsgDTO.getTargetTypeEnum().getValue());
        bcpMessageDTO.setTargetList(sendMsgDTO.getTargetList());
        return bcpMessageDTO;
    }


}
