package com.gok.pboot.pms.entity.vo;

import lombok.Data;

import java.util.List;

/**
 * 滴滴订单导入结果VO
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
public class DdOrderImportResultVO {

    /**
     * 导入成功数量
     */
    private Integer successCount;

    /**
     * 导入失败数量
     */
    private Integer failCount;

    /**
     * 错误信息列表
     */
    private List<String> errorMessages;

    /**
     * 导入结果
     */
    private String result;

    public DdOrderImportResultVO() {
        this.successCount = 0;
        this.failCount = 0;
    }

    public DdOrderImportResultVO(Integer successCount, Integer failCount, List<String> errorMessages) {
        this.successCount = successCount;
        this.failCount = failCount;
        this.errorMessages = errorMessages;
        this.result = String.format("导入完成，成功：%d条，失败：%d条", successCount, failCount);
    }

} 