package com.gok.pboot.pms.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.entity.domain.CustomerInfo;
import com.gok.pboot.pms.entity.dto.CustomerCommonPageDTO;
import com.gok.pboot.pms.entity.dto.CustomerPageDTO;
import com.gok.pboot.pms.entity.vo.CustomerCommonVO;
import com.gok.pboot.pms.entity.vo.CustomerListVO;
import com.gok.pboot.pms.entity.vo.OaHrmcityVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 客户表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-02-23 10:46:40
 */
@Mapper
public interface CustomerInfoMapper extends BaseMapper<CustomerInfo> {


    /**
     * 获取客户台账-分页
     *
     * @param filter 台账查询DTO
     * @return {@link Page}<{@link CustomerListVO}>
     */
    List<CustomerListVO> findList(@Param("filter") CustomerPageDTO filter);

    /**
     * 获取关注客户台账-分页
     *
     * @param filter 台账查询DTO
     * @return {@link Page}<{@link CustomerListVO}>
     */
    List<CustomerListVO> findAttentionList( @Param("filter") CustomerPageDTO filter);

    /**
     * 获取客户台账-分页（公共组件）
     *
     * @param page 分页对象
     * @param filter 台账查询DTO
     * @return {@link Page}<{@link CustomerListVO}>
     */
    Page<CustomerCommonVO> findCommonPage(Page page, @Param("filter") CustomerCommonPageDTO filter);


    List<OaHrmcityVO> getCityListByName(String name);
}
