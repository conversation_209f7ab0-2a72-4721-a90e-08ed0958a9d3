package com.gok.pboot.pms.cost.entity.vo;

import com.gok.bcp.upms.vo.SysUserVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 工单顶部统计信息 vo
 *
 * <AUTHOR>
 * @date 2025/01/14
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class CostTaskTopCountMsgVO {

    /**
     * 头像
     */
    private String avatar;

    /**
     * 姓名
     */
    private String name;

    /**
     * 未完成工单计数
     */
    private Integer unfinishedCount;

    /**
     * 待拆解工单计数
     */
    private Integer toBeDisassembledCount;

    /**
     * 待审核工单计数
     */
    private Integer toBeReviewedCount;

    /**
     * 本月确认成本
     */
    private BigDecimal thisMonthConfirmCost;

    public CostTaskTopCountMsgVO(SysUserVo userVo) {
        this.avatar = userVo.getAvatar();
        this.name = userVo.getName();
        this.unfinishedCount = 0;
        this.toBeDisassembledCount = 0;
        this.toBeReviewedCount = 0;
        this.thisMonthConfirmCost = BigDecimal.ZERO;
    }
}
