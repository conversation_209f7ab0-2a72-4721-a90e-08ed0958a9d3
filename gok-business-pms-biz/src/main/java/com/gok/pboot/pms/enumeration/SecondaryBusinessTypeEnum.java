package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 新业务类型枚举
 *
 * <AUTHOR>
 * @version 1.3.7
 * @create 2024/07/10
 **/
@Getter
@AllArgsConstructor
public enum SecondaryBusinessTypeEnum implements ValueEnum<Integer>{

    /**
     * 数字化建设服务
     */
    DIGITAL_TALENT_SERVICE(0, "数字化建设服务"),
    /**
     * 数字化运维服务
     */
    DIGITAL_TECHNOLOGY_SERVICE(1, "数字化运维服务"),
    /**
     * 数字人才培养服务
     */
    CONSULTING_SOLUTION(2, "数字人才培养服务");

    private final Integer value;

    private final String name;

}
