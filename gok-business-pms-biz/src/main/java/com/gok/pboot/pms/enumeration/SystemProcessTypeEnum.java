package com.gok.pboot.pms.enumeration;

import lombok.Getter;

/**
 * 项目系统过程动态类型枚举类
 *
 * <AUTHOR>
 * @since 2023-07-19
 **/
@Getter
public enum SystemProcessTypeEnum implements ValueEnum<String> {

    /**
     * 项目会议纪要
     */
    MEETING("meeting", "会议纪要"),

    /**
     * 项目周报
     */
    WEEKLY("weekly", "项目周报"),

    /**
     * 项目确认
     */
    PROJECT_CONFIRM("project_confirmation", "项目确认"),;

    /**
     * 值
     */
    private String value;

    /**
     * 名称
     */
    private String name;

    SystemProcessTypeEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

}
