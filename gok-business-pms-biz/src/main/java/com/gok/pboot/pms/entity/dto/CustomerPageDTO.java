package com.gok.pboot.pms.entity.dto;

import lombok.Data;

import java.util.List;

/**
 * 客户台账查询DTO
 *
 * <AUTHOR>
 * @date 2023/11/20
 */
@Data
public class CustomerPageDTO {
    /**
     * 客户名称
     */
    private String khmc;
    /**
     * 客户分级
     */
    private List<Integer> khfjList;

    /**
     * 行业"
     */
    private List<Integer> khxyyjList;

    /**
     * 归属业务部门
     */
    private List<Long> gsywbmejbmList;

    /**
     * 客户所在地
     */
    private List<Integer> khszdList;

    /**
     * 客户经理名字
     */
    private String khjlxm;

    /**
     * 是否关注
     */
    private Boolean isAttention=false;


    /**
     * 当前用户id(前端不用管)
     */
    private Long userId;

    // 页号
    private int pageNumber = 0;
    // 每页大小
    private int pageSize = 10;

}
