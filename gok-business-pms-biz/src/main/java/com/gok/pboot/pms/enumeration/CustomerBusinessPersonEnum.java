package com.gok.pboot.pms.enumeration;


import lombok.AllArgsConstructor;

/**
 * 客户经营单元-项目负责人角色
 *
 * <AUTHOR>
 */
@AllArgsConstructor
public enum CustomerBusinessPersonEnum implements ValueEnum<Integer> {

    MAIN_MANAGER(1, "主办客户经理","MAIN_MANAGER"),
    SECONDARY_MANAGER(2, "协办客户经理","SECONDARY_MANAGER"),
    SUPPORT_OFFICER(3, "总支撑官","SUPPORT_OFFICER"),
    PROGRAM_MANAGER(4, "方案经理","PROGRAM_MANAGER"),
    HUMAN_RESOURCE_BP(5, "人力BP","HUMAN_RESOURCE_BP"),
    DELIVERY_MANAGER(6, "交付经理","DELIVERY_MANAGER")
            ;

    private final Integer value;

    private final String name;

    private final String code;

    @Override
    public Integer getValue() {
        return value;
    }

    @Override
    public String getName() {
        return name;
    }


    public String getCode(){   return code; }

    /**
     * 获取角色code
     * @return
     */
    public static String getCodeByValue(Integer value){
        for (CustomerBusinessPersonEnum e : CustomerBusinessPersonEnum.values()) {
            if(e.getValue().equals(value)){
                return e.getCode();
            }
        }
        return null;
    }

}
