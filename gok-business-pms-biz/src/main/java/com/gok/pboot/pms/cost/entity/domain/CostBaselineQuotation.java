package com.gok.pboot.pms.cost.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 报价与毛利测算表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("cost_baseline_quotation")
public class CostBaselineQuotation extends BeanEntity<Long> {

    private static final long serialVersionUID = 1L;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 版本ID
     */
    private Long versionId;

    /**
     * 审核状态（0=A表流程同步,1=未审核，2=未通过，3=已审核）
     */
    private Integer auditStatus;

    /**
     * 收入总额(含税)
     */
    private BigDecimal incomeAmountIncludedTax;

    /**
     * 收入总额(不含税)
     */
    private BigDecimal incomeAmountExcludingTax;

    /**
     * 预计提前投入成本(万元)
     */
    private BigDecimal expectedEarlyInvestmentCost;

}
