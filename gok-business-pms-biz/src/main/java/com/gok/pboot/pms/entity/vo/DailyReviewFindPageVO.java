package com.gok.pboot.pms.entity.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * @Auther chenhc
 * @Date 2022-08-24 14:52
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class DailyReviewFindPageVO {

    /**
     * 提交日期
     */
    private LocalDate submissionDate;

    /**
     * 提交日期-星期
     */
    private String submissionDateName;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目正常工时
     */
    private BigDecimal normalHours;

    /**
     * 项目加班工时
     */
    private BigDecimal addedHours;

    /**
     * 待操作人数
     */
    private Long number;

    /**
     * 需操作总人数
     */
    private Long numberTotal;

    private Long userId;
    /**
     * 是否可编辑 ,1可0不可
     * {@link com.gok.pboot.enumeration.entity.YesOrNoEnum}
     */
    private Integer isEdit;
    /**
     * 用户id集合
     */
    private String userStr;
    /**
     * 用户id集合
     */
    private List<String> userIds;

}
