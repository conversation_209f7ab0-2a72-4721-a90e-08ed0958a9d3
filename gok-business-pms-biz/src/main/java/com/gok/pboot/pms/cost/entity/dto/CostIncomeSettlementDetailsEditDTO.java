package com.gok.pboot.pms.cost.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 收入结算明细编辑新增DTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CostIncomeSettlementDetailsEditDTO {

    /**
     * id
     */
    private Long id;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 结算开始日期
     */
    private LocalDate startDate;

    /**
     * 结算截止日期
     */
    private LocalDate endDate;

    /**
     * 姓名
     */
    private String userName;

    /**
     * 工号
     */
    private String workCode;

    /**
     * 测算含税金额
     */
    private BigDecimal estimatedInclusiveAmountTax;

    /**
     * 结算含税金额
     */
    private BigDecimal budgetAmountIncludedTax;

    /**
     * 税率OA字典ID
     */
    private String taxRate;

    /**
     * 结算不含税金额
     */
    private BigDecimal budgetAmountExcludingTax;

    /**
     * 备注说明
     */
    private String remarksDesc;

    /**
     * 数据来源 1手动添加
     */
    private Integer dataSources;

    /**
     * 收入测算ID
     */
    private Long costIncomeCalculationId;

    /**
     * 测算明细ID
     */
    private Long costIncomeCalculationDetailId;

    /**
     * 操作结算日期
     */
    private LocalDateTime operationSettlementDate;

}
