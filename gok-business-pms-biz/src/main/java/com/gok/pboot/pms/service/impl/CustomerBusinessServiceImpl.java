package com.gok.pboot.pms.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.bcp.upms.dto.DeptCacheDto;
import com.gok.bcp.upms.feign.RemoteDeptService;
import com.gok.bcp.upms.feign.RemoteOutService;
import com.gok.bcp.upms.feign.RemoteRoleService;
import com.gok.bcp.upms.vo.SysDeptVo;
import com.gok.bcp.upms.vo.SysMenuVo;
import com.gok.bcp.upms.vo.SysUserVo;
import com.gok.components.common.util.R;
import com.gok.module.file.entity.SysFile;
import com.gok.module.file.service.SysFileService;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.Util.*;
import com.gok.pboot.pms.common.base.PropertyFilters;
import com.gok.pboot.pms.common.constant.FunctionConstants;
import com.gok.pboot.pms.common.exception.ServiceException;
import com.gok.pboot.pms.entity.CustomerBusiness;
import com.gok.pboot.pms.entity.CustomerBusinessPerson;
import com.gok.pboot.pms.entity.CustomerBusinessUnit;
import com.gok.pboot.pms.entity.SysDept;
import com.gok.pboot.pms.entity.dto.CustomerBusinessDTO;
import com.gok.pboot.pms.entity.dto.CustomerBusinessSearchDTO;
import com.gok.pboot.pms.entity.vo.*;
import com.gok.pboot.pms.enumeration.BusinessStatusEnum;
import com.gok.pboot.pms.enumeration.CustomerBusinessPersonEnum;
import com.gok.pboot.pms.enumeration.LogContentEnum;
import com.gok.pboot.pms.enumeration.ProjectStatusEnum;
import com.gok.pboot.pms.handler.ProjectScopeHandle;
import com.gok.pboot.pms.handler.perm.PmsRetriever;
import com.gok.pboot.pms.mapper.CustomerBusinessMapper;
import com.gok.pboot.pms.service.*;
import com.google.common.collect.ImmutableList;
import lombok.RequiredArgsConstructor;
import lombok.Synchronized;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;


/**
 * <p>
 * 客户经营单元表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-12
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class CustomerBusinessServiceImpl extends ServiceImpl<CustomerBusinessMapper, CustomerBusiness> implements ICustomerBusinessService {

    private final BcpLoggerUtils bcpLoggerUtils;

    private final ICustomerBusinessPersonService customerBusinessPersonService;

    private final ICustomerBusinessUnitService customerBusinessUnitService;

    public static final String PARAM_FILTER_BUSINESS_ID = "businessId";
    public static final String PARAM_FILTER_PERMISSION = "permission";
    public static final String PARAM_FILTER_APPLICATION = "application";
    public static final String PARAM_FILTER_MENU_TYPE = "menuType";

    private final RemoteOutService remoteOutService;

    private final RemoteRoleService remoteRoleService;

    private final ProjectScopeHandle projectScopeHandle;

    private final ICustomerInfoService customerInfoService;

    private final PmsRetriever pmsRetriever;

    private final SysFileService sysFileService;

    private final RemoteDeptService remoteDeptService;

    private final IProjectInfoService projectInfoService;

    private final IBusinessInfoService businessInfoService;

    private final DbApiUtil dbApiUtil;

    @Value("${oa.unitMainId}")
    private String unitMainId;

    @Value("${oa.projectUnitFieldId}")
    private String projectUnitFieldId;

    @Value("${oa.businessUnitFieldId}")
    private String businessUnitFieldId;

    public static final List<String> DATA_SCOPE_PERMISSION_LIST =
            ImmutableList.of("CUSTOMER_BUSINESS_DETAIL",
                    "CUSTOMER_UNITS:ADD",
                    "CUSTOMER_BUSINESS_DETAIL:TAB_BASE",
                    "CUSTOMER_BUSINESS_DETAIL:TAB_BASE:EDIT_UNIT",
                    "CUSTOMER_BUSINESS_DETAIL:TAB_BASE:EDIT_BASE",
                    "CUSTOMER_BUSINESS_DETAIL:TAB_BASE:EDIT_CUSTOMER",
                    "CUSTOMER_BUSINESS_DETAIL:TAB_BASE:EDIT_ORG",
                    "CUSTOMER_BUSINESS_DETAIL:TAB_BUSINESS_DATA",
                    "CUSTOMER_BUSINESS_DATA",
                    "CUSTOMER_TB:BUSINESS_DATA",
                    "CUSTOMER_TB:BASE",
                    "BELONG_CUSTOMER:DETAIL",
                    "BELONG_CUSTOMERS:ADD",
                    "BELONG_CUSTOMERS:EDIT"
            );

    @Override
    public List<CustomerBusinessListVO> findNameList(CustomerBusinessSearchDTO customerBusinessSearchDTO) {
        return baseMapper.findNameList(customerBusinessSearchDTO);
    }


    @Override
    public Page<CustomerBusinessPageVO> findPageList(CustomerBusinessDTO dto) {
        List<Long> businessIdsAvailable = pmsRetriever.getBusinessIdsAvailable(dto);
        dto.setBusinessIdsInDataScope(businessIdsAvailable);
        Page<CustomerBusiness> page = new Page<>(dto.getPageNumber(), dto.getPageSize());
        if (!"all".equals(dto.getScope()) && businessIdsAvailable.isEmpty()) {
            return PageUtils.mapTo(page, x -> null);
        }
        Page<CustomerBusiness> listPage = baseMapper.findListPage(page, dto);
        Page<CustomerBusinessPageVO> businessPage = new Page<>();
        List<CustomerBusinessPageVO> recods = new ArrayList<>();
        BeanUtils.copyProperties(listPage, businessPage);
        if (CollectionUtils.isNotEmpty(listPage.getRecords())) {
            for (CustomerBusiness businessVO : listPage.getRecords()) {
                buildPageVo(businessVO, recods);
            }
        }
        businessPage.setRecords(recods);
        return businessPage;
    }

    private void buildPageVo(CustomerBusiness businessVO, List<CustomerBusinessPageVO> recods) {
        CustomerBusinessPageVO pageVO = new CustomerBusinessPageVO();
        pageVO.setId(businessVO.getId());
        pageVO.setName(businessVO.getName());
        //查询相关责任人
        CustomerBusinessPerson customerBusinessPerson = new CustomerBusinessPerson();
        customerBusinessPerson.setBusinessId(businessVO.getId());
        List<CustomerBusinessPerson> personList = customerBusinessPersonService.findList(customerBusinessPerson);
        List<String> mainList = personList.stream().filter(person -> person.getManagerRole() == CustomerBusinessPersonEnum.MAIN_MANAGER.getValue()).map(CustomerBusinessPerson::getManagerName).collect(Collectors.toList());
        pageVO.setMainManager(String.join("、", mainList));
        List<CustomerBusinessUnitPageVO> unitList = customerBusinessUnitService.selectSimplyList(businessVO.getId());
        List<String> secondaryList = unitList.stream().filter(unit -> unit.getUnitManager() != null).map(CustomerBusinessUnitPageVO::getUnitManager).collect(Collectors.toList());
        secondaryList = secondaryList.stream().distinct().collect(Collectors.toList());
        pageVO.setSecondaryManager(String.join("、", secondaryList));
        List<String> supportList = personList.stream().filter(person -> person.getManagerRole() == CustomerBusinessPersonEnum.SUPPORT_OFFICER.getValue()).map(CustomerBusinessPerson::getManagerName).collect(Collectors.toList());
        pageVO.setSupportManager(String.join("、", supportList));
        List<String> programList = personList.stream().filter(person -> person.getManagerRole() == CustomerBusinessPersonEnum.PROGRAM_MANAGER.getValue()).map(CustomerBusinessPerson::getManagerName).collect(Collectors.toList());
        pageVO.setProgrammeManager(String.join("、", programList));
        List<String> humanList = personList.stream().filter(person -> person.getManagerRole() == CustomerBusinessPersonEnum.HUMAN_RESOURCE_BP.getValue()).map(CustomerBusinessPerson::getManagerName).collect(Collectors.toList());
        pageVO.setHumanManager(String.join("、", humanList));
        List<String> deliverList = personList.stream().filter(person -> person.getManagerRole() == CustomerBusinessPersonEnum.DELIVERY_MANAGER.getValue()).map(CustomerBusinessPerson::getManagerName).collect(Collectors.toList());
        pageVO.setDeliverManager(String.join("、", deliverList));
        //查询关联客户数
        pageVO.setUnitCount(customerBusinessUnitService.count(new QueryWrapper<CustomerBusinessUnit>().eq("business_id", businessVO.getId())));
        recods.add(pageVO);
    }


    @Override
    @Synchronized
    @Transactional(rollbackFor = Exception.class)
    public Long saveBusiness(CustomerBusiness entity) {
        if (Optional.ofNullable(entity.getId()).isPresent()) {
            CustomerBusiness customerBusiness = baseMapper.selectById(entity.getId());
            if (StringUtils.isNotEmpty(entity.getName()) && !Objects.equals(customerBusiness.getName(), entity.getName())) {
                //编辑经营单元名称日志
                bcpLoggerUtils.log(FunctionConstants.CUSTOMER_BUSINESS, LogContentEnum.UPDATE_CUSTOMER_BUSINESS,
                        SecurityUtils.getUser().getName(), entity.getName());
            }
            if ((StringUtils.isNotEmpty(entity.getOverview()) && !Objects.equals(customerBusiness.getOverview(), entity.getOverview())) ||
                    (StringUtils.isNotEmpty(entity.getReportUrl()) && !Objects.equals(customerBusiness.getReportUrl(), entity.getReportUrl())) ||
                    (StringUtils.isNotEmpty(entity.getStructureUrl()) && !Objects.equals(customerBusiness.getStructureUrl(), entity.getStructureUrl()))) {
                //编辑经营单元基础信息日志
                bcpLoggerUtils.log(FunctionConstants.CUSTOMER_BUSINESS, LogContentEnum.UPDATE_CUSTOMER_BUSINESS_INFO,
                        SecurityUtils.getUser().getName(), customerBusiness.getName());
            }
            BaseBuildEntityUtil.buildUpdate(entity);
            updateById(entity);

            //修改经营单元同步至OA
            updateCustomerBusinessSyncOA(entity);
        } else {
            if (StringUtils.isBlank(entity.getName())) {
                throw new ServiceException("新增客户经营单元名称不能为空");
            }
            BaseBuildEntityUtil.buildInsert(entity);
            //创建经营单元日志
            bcpLoggerUtils.log(FunctionConstants.CUSTOMER_BUSINESS, LogContentEnum.CREATE_CUSTOMER_BUSINESS,
                    SecurityUtils.getUser().getName(), entity.getName());
            save(entity);

            //新增经营单元同步至OA
            addCustomerBusinessSyncOA(entity);
        }
        return entity.getId();
    }


    @Override
    public CustomerBusinessVO getById(Long id) {
        CustomerBusinessVO customerBusinessVO = new CustomerBusinessVO();
        CustomerBusiness customerBusiness = this.baseMapper.selectById(id);
        if (customerBusiness != null) {
            customerBusinessVO.setId(customerBusiness.getId());
            BeanUtils.copyProperties(customerBusiness, customerBusinessVO);
            List<CustomerBusinessUnitPageVO> unitList = customerBusinessUnitService.selectSimplyList(id);
            CustomerBusinessPerson customerBusinessPerson = new CustomerBusinessPerson();
            customerBusinessPerson.setBusinessId(id);
            List<CustomerBusinessPerson> personList = customerBusinessPersonService.findList(customerBusinessPerson);
            customerBusinessVO.setUnits(unitList);
            if (CollectionUtils.isNotEmpty(unitList)) {
                for (CustomerBusinessUnitPageVO temp : unitList) {
                    //查询跟进商机
                    temp.setBusinessCount(businessInfoService.countByUnit(temp.getId(), BusinessStatusEnum.BUSINESS.getValue()));
                    //查询在建项目
                    temp.setDoingProjectCount(projectInfoService.countByUnitId(temp.getId(), ProjectStatusEnum.ZJ.getStrValue()));
                    //查询结项项目
                    temp.setDoneProjectCount(projectInfoService.countByUnitId(temp.getId(), ProjectStatusEnum.JX.getStrValue()));
                    CustomerBusinessPerson person = new CustomerBusinessPerson();
                    person.setManagerId(temp.getUnitManagerId());
                    person.setManagerName(temp.getUnitManager());
                    person.setManagerRole(CustomerBusinessPersonEnum.SECONDARY_MANAGER.getValue());
                    //查找部门
                    R<SysUserVo> userInfo = remoteOutService.getUserInfoById(temp.getUnitManagerId());
                    if (userInfo.getData() != null) {
                        SysUserVo user = userInfo.getData();
                        List<SysDeptVo> deptVoList = userInfo.getData().getDeptList();
                        if (CollectionUtils.isNotEmpty(deptVoList)) {
                            //获取最后一级部门数据
                            SysDeptVo sysDeptVo = deptVoList.get(deptVoList.size() - 1);
                            //获取部门名称
                            List<DeptCacheDto> deptList = remoteDeptService.getAllDeptList(false);
                            Map<Long, SysDept> deptIdMap = SysDeptUtils.getDeptIdMap(deptList);
                            person.setManagerDeptName(sysDeptVo.getDeptId() == null ?
                                    StrUtil.EMPTY : SysDeptUtils.collectFullName(deptIdMap, sysDeptVo.getDeptId()).replace("-", "/"));
                        }
                    }
                    if (StringUtils.isNotBlank(person.getManagerName())) {
                        personList.add(person);
                    }

                }
            }
            customerBusinessVO.setPersons(personList.stream()
                    .sorted(Comparator.comparing(CustomerBusinessPerson::getManagerRole))
                    .collect(Collectors.toList()));
            //查询附件
            if (StringUtils.isNotEmpty(customerBusiness.getStructureUrl())) {
                List<String> structIdList = Arrays.asList(customerBusiness.getStructureUrl().split(","));
                List<SysFile> structList = sysFileService.listByIds(structIdList);
                customerBusinessVO.setStructureList(structList);
            }
            if (StringUtils.isNotEmpty(customerBusiness.getReportUrl())) {
                List<String> reportIdList = Arrays.asList(customerBusiness.getReportUrl().split(","));
                List<SysFile> reportList = sysFileService.listByIds(reportIdList);
                customerBusinessVO.setReportList(reportList);
            }
        }
        return customerBusinessVO;
    }


    public CustomerBusinessSysMenuVo getMenuAuthority(HttpServletRequest request) {
        CustomerBusinessSysMenuVo customerBusinessSysMenuVo = new CustomerBusinessSysMenuVo();
        List<SysMenuVo> sysMenuAuthority = getSysMenuAuthority(request);
        customerBusinessSysMenuVo.setSysMenuVo(sysMenuAuthority);
        Object businessIdObj = PropertyFilters.get(request, true).get(PARAM_FILTER_BUSINESS_ID);
        Long businessId = Long.valueOf(businessIdObj.toString());
        CustomerBusiness business = baseMapper.selectById(businessId);
        if (business != null) {
            customerBusinessSysMenuVo.setName(business.getName());
        }
        CustomerBusinessPerson qo = new CustomerBusinessPerson();
        qo.setBusinessId(businessId);
        List<CustomerBusinessPerson> personList = customerBusinessPersonService.findList(qo);
        Optional<CustomerBusinessPerson> mainManager = personList.stream()
                .filter(p -> p.getManagerRole().equals(CustomerBusinessPersonEnum.MAIN_MANAGER.getValue()))
                .findFirst();
        mainManager.ifPresent(p -> customerBusinessSysMenuVo.setMainManager(p.getManagerName()));
        Optional<CustomerBusinessPerson> support = personList.stream()
                .filter(p -> p.getManagerRole().equals(CustomerBusinessPersonEnum.SUPPORT_OFFICER.getValue()))
                .findFirst();
        support.ifPresent(p -> customerBusinessSysMenuVo.setSupportManager(p.getManagerName()));
        List<CustomerBusinessUnitPageVO> customerBusinessUnits = customerBusinessUnitService.selectSimplyList(businessId);
        List<Long> unitIdList = customerBusinessUnits.stream().map(CustomerBusinessUnitPageVO::getId).collect(Collectors.toList());
        customerBusinessSysMenuVo.setUnitIdList(unitIdList);
        return customerBusinessSysMenuVo;
    }

    private List<SysMenuVo> getSysMenuAuthority(HttpServletRequest request) {
        Map<String, Object> filter = PropertyFilters.get(request, true);
        Object businessIdObj = filter.get(PARAM_FILTER_BUSINESS_ID);
        Object permissionObj = filter.get(PARAM_FILTER_PERMISSION);
        Object menuType = filter.get(PARAM_FILTER_MENU_TYPE);
        Long application = Long.valueOf(request.getHeader(PARAM_FILTER_APPLICATION));
        // 获取普通角色菜单权限
        List<SysMenuVo> sysMenuVos = new ArrayList<>();
        com.gok.components.common.util.R<List<SysMenuVo>> funcAuthListByIdAndAppId = remoteOutService.getFuncAuthListByIdAndAppId(SecurityUtils.getUser().getId(), application);
        if (Optional.ofNullable(funcAuthListByIdAndAppId).isPresent() && Optional.ofNullable(permissionObj).isPresent()) {
            String permission = String.valueOf(permissionObj);
            List<SysMenuVo> allSysMenuVos = funcAuthListByIdAndAppId.getData();
            if (CollUtil.isNotEmpty(allSysMenuVos)) {
                Optional<SysMenuVo> any = allSysMenuVos.stream().filter(s -> permission.equals(s.getPermission())).findAny();
                if (any.isPresent()) {
                    SysMenuVo sysMenuVo = any.get();
                    sysMenuVos = allSysMenuVos.stream().filter(s -> sysMenuVo.getMenuId().equals(s.getParentId())).collect(Collectors.toList());
                }
            }
        }
        // 获取业务角色菜单权限
        List<SysMenuVo> buttonAuthorities = new ArrayList<>();
        if (Optional.ofNullable(businessIdObj).isPresent() && Optional.ofNullable(permissionObj).isPresent()) {
            buttonAuthorities = getBusinessAuthorities(Long.valueOf(String.valueOf(businessIdObj)),
                    String.valueOf(permissionObj), menuType, application);
        }
        buttonAuthorities.addAll(sysMenuVos);
        return buttonAuthorities.stream().collect(collectingAndThen(
                        toCollection(() -> new TreeSet<>(
                                Comparator.comparing(SysMenuVo::getPermission))), ArrayList::new))
                .stream().sorted(Comparator.comparing(SysMenuVo::getSortOrder)).collect(Collectors.toList());
    }


    /**
     * 获取当前用户在此项目的业务角色
     *
     * @param businessId    传入的客户经营单元id
     * @param permissionObj 传入的权限标识
     * @param menuType      传入的权限标识
     * @param application   application
     * @return
     */
    @Override
    public List<SysMenuVo> getBusinessAuthorities(Long businessId,
                                                  Object permissionObj,
                                                  Object menuType,
                                                  Long application) {
        List<SysMenuVo> buttonAuthorities = new ArrayList<>();
        Long userId = SecurityUtils.getUser().getId();
        String permission = String.valueOf(permissionObj);
        List<String> businessAuthorities = new ArrayList<>();
        // 获取当前用户在此项目的业务角色
        List<Integer> roleTypes = customerBusinessPersonService.manageRoleList(businessId, userId);
        if (CollUtil.isNotEmpty(roleTypes)) {
            roleTypes.forEach(r -> businessAuthorities.add(CustomerBusinessPersonEnum.getCodeByValue(r)));
        }
        if (customerBusinessPersonService.isManageRole(businessId, ImmutableList.of(userId), CustomerBusinessPersonEnum.MAIN_MANAGER.getValue())) {
            businessAuthorities.add(CustomerBusinessPersonEnum.getCodeByValue(CustomerBusinessPersonEnum.MAIN_MANAGER.getValue()));
        }
//        if (customerBusinessPersonService.isManageRole(businessId, ImmutableList.of(userId), CustomerBusinessPersonEnum.SECONDARY_MANAGER.getValue())) {
//            businessAuthorities.add(CustomerBusinessPersonEnum.getCodeByValue(CustomerBusinessPersonEnum.SECONDARY_MANAGER.getValue()));
//        }
        //查询该用户是否是此经营单元的协助客户经理
        CustomerBusinessUnit customerBusinessUnit = new CustomerBusinessUnit();
        customerBusinessUnit.setBusinessId(businessId);
        customerBusinessUnit.setUnitManagerId(userId);
        List<CustomerBusinessUnit> unitList = customerBusinessUnitService.findList(customerBusinessUnit);
        if (CollectionUtils.isNotEmpty(unitList)) {
            businessAuthorities.add(CustomerBusinessPersonEnum.getCodeByValue(CustomerBusinessPersonEnum.SECONDARY_MANAGER.getValue()));
        }
        if (customerBusinessPersonService.isManageRole(businessId, ImmutableList.of(userId), CustomerBusinessPersonEnum.SUPPORT_OFFICER.getValue())) {
            businessAuthorities.add(CustomerBusinessPersonEnum.getCodeByValue(CustomerBusinessPersonEnum.SUPPORT_OFFICER.getValue()));
        }
        if (customerBusinessPersonService.isManageRole(businessId, ImmutableList.of(userId), CustomerBusinessPersonEnum.PROGRAM_MANAGER.getValue())) {
            businessAuthorities.add(CustomerBusinessPersonEnum.getCodeByValue(CustomerBusinessPersonEnum.PROGRAM_MANAGER.getValue()));
        }
        if (customerBusinessPersonService.isManageRole(businessId, ImmutableList.of(userId), CustomerBusinessPersonEnum.HUMAN_RESOURCE_BP.getValue())) {
            businessAuthorities.add(CustomerBusinessPersonEnum.getCodeByValue(CustomerBusinessPersonEnum.HUMAN_RESOURCE_BP.getValue()));
        }
        if (customerBusinessPersonService.isManageRole(businessId, ImmutableList.of(userId), CustomerBusinessPersonEnum.DELIVERY_MANAGER.getValue())) {
            businessAuthorities.add(CustomerBusinessPersonEnum.getCodeByValue(CustomerBusinessPersonEnum.DELIVERY_MANAGER.getValue()));
        }
        if (Optional.ofNullable(menuType).isPresent() && CollUtil.isNotEmpty(businessAuthorities)) {
            buttonAuthorities = remoteRoleService.getMenuAuthListByRoleListAndClientId(application,
                    businessAuthorities.stream().distinct().collect(Collectors.toList()), menuType.toString(), permission).getData();
        } else if (CollUtil.isNotEmpty(businessAuthorities)) {
            buttonAuthorities = remoteRoleService.getMenuAuthListByRoleListAndClientId(application,
                    businessAuthorities.stream().distinct().collect(Collectors.toList()), null, permission).getData();
        }
        // 获取数据权限
        SysUserDataScopeVO dataScope = projectScopeHandle.findDataScope();
        List<Long> userIds = dataScope.getUserIdList();
        if (CollUtil.isNotEmpty(userIds)) {
            List<SysMenuVo> buttonAuthorities2 = new ArrayList<>();
            List<String> businessAuthorities2 = new ArrayList<>();
            List<Integer> roleTypes2 = customerBusinessPersonService.manageRoleList(businessId, userIds);
            if (CollUtil.isNotEmpty(roleTypes2)) {
                roleTypes.forEach(r -> businessAuthorities2.add(CustomerBusinessPersonEnum.getCodeByValue(r)));
            }
            if (customerBusinessPersonService.isManageRole(businessId, userIds, CustomerBusinessPersonEnum.MAIN_MANAGER.getValue())) {
                businessAuthorities2.add(CustomerBusinessPersonEnum.getCodeByValue(CustomerBusinessPersonEnum.MAIN_MANAGER.getValue()));
            }
//            if (customerBusinessPersonService.isManageRole(businessId, userIds, CustomerBusinessPersonEnum.SECONDARY_MANAGER.getValue())) {
//                businessAuthorities2.add(CustomerBusinessPersonEnum.getCodeByValue(CustomerBusinessPersonEnum.SECONDARY_MANAGER.getValue()));
//            }
            //查询该用户是否是此经营单元的协助客户经理
            if (CollectionUtils.isNotEmpty(unitList)) {
                businessAuthorities.add(CustomerBusinessPersonEnum.getCodeByValue(CustomerBusinessPersonEnum.SECONDARY_MANAGER.getValue()));
            }

            if (customerBusinessPersonService.isManageRole(businessId, userIds, CustomerBusinessPersonEnum.SUPPORT_OFFICER.getValue())) {
                businessAuthorities2.add(CustomerBusinessPersonEnum.getCodeByValue(CustomerBusinessPersonEnum.SUPPORT_OFFICER.getValue()));
            }
            if (customerBusinessPersonService.isManageRole(businessId, userIds, CustomerBusinessPersonEnum.PROGRAM_MANAGER.getValue())) {
                businessAuthorities2.add(CustomerBusinessPersonEnum.getCodeByValue(CustomerBusinessPersonEnum.PROGRAM_MANAGER.getValue()));
            }
            if (customerBusinessPersonService.isManageRole(businessId, userIds, CustomerBusinessPersonEnum.HUMAN_RESOURCE_BP.getValue())) {
                businessAuthorities2.add(CustomerBusinessPersonEnum.getCodeByValue(CustomerBusinessPersonEnum.HUMAN_RESOURCE_BP.getValue()));
            }
            if (customerBusinessPersonService.isManageRole(businessId, userIds, CustomerBusinessPersonEnum.DELIVERY_MANAGER.getValue())) {
                businessAuthorities2.add(CustomerBusinessPersonEnum.getCodeByValue(CustomerBusinessPersonEnum.DELIVERY_MANAGER.getValue()));
            }
            if (Optional.ofNullable(menuType).isPresent() && CollUtil.isNotEmpty(businessAuthorities2)) {
                buttonAuthorities2 = remoteRoleService.getMenuAuthListByRoleListAndClientId(application, businessAuthorities2, menuType.toString(), permission).getData();
            } else if (CollUtil.isNotEmpty(businessAuthorities2)) {
                buttonAuthorities2 = remoteRoleService.getMenuAuthListByRoleListAndClientId(application, businessAuthorities2, null, permission).getData();
            }
            if (CollUtil.isNotEmpty(buttonAuthorities2)) {
                buttonAuthorities2 = buttonAuthorities2.stream().filter(b -> DATA_SCOPE_PERMISSION_LIST.contains(b.getPermission())).collect(Collectors.toList());
                buttonAuthorities.addAll(buttonAuthorities2);
            }
        }
        return buttonAuthorities;
    }


    /**
     * 新增经营单元同步至OA
     *
     * @param customerBusiness
     */
    public void addCustomerBusinessSyncOA(CustomerBusiness customerBusiness) {
        //查询OA公共字典最大值
        int modeSelectitempagedetailMax = dbApiUtil.getModeSelectitempagedetailMax(unitMainId, "1");

        //插入OA公共字典
        dbApiUtil.insertModeSelectitempagedetail(unitMainId, customerBusiness.getName(), String.valueOf(modeSelectitempagedetailMax + 1), "0", "1", String.valueOf(customerBusiness.getId()));

        //查询OA公共字典id
        //String modeSelectitempagedetailId = dbApiUtil.getModeSelectitempagedetail(String.valueOf(customerBusiness.getId()));

        //查询OA流程字典最大值（项目台账）
        //int projectMax = dbApiUtil.getWorkflowSelectitemMax(projectUnitFieldId);

        //插入OA流程字典（项目台账）
        //dbApiUtil.insertWorkflowSelectitem(projectUnitFieldId, String.valueOf(projectMax + 1), customerBusiness.getName(), modeSelectitempagedetailId, String.valueOf(customerBusiness.getId()));

        //查询OA流程字典最大值（商机报备）
        //int businessMax = dbApiUtil.getWorkflowSelectitemMax(businessUnitFieldId);

        //插入OA流程字典（商机报备）
        //dbApiUtil.insertWorkflowSelectitem(businessUnitFieldId, String.valueOf(businessMax + 1), customerBusiness.getName(), modeSelectitempagedetailId, String.valueOf(customerBusiness.getId()));

        //同步OA公共字典缓存
        customerBusinessUnitService.saveSelectItemSyncOA();
    }


    /**
     * 修改经营单元同步至OA
     *
     * @param customerBusiness
     */
    public void updateCustomerBusinessSyncOA(CustomerBusiness customerBusiness) {
        //更新OA公共字典
        dbApiUtil.updateModeSelectitempagedetail(customerBusiness.getName(), null, null, String.valueOf(customerBusiness.getId()));

        //更新OA流程字典
        //dbApiUtil.updateWorkflowSelectitem(customerBusiness.getName(), null, null, String.valueOf(customerBusiness.getId()));

        //同步OA公共字典缓存
        customerBusinessUnitService.saveSelectItemSyncOA();
    }

    /**
     * 删除经营单元同步至OA
     *
     * @param id
     */
    public void deleteCustomerBusinessSyncOA(String id) {
        //更新OA公共字典
        dbApiUtil.updateModeSelectitempagedetail(null, "1", null, id);

        //更新OA流程字典
        //dbApiUtil.updateWorkflowSelectitem(null, "1", null, id);

        //同步OA公共字典缓存
        customerBusinessUnitService.saveSelectItemSyncOA();
    }
}
