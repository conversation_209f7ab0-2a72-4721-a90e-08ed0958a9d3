package com.gok.pboot.pms.service.impl;


import com.gok.bcp.admin.entity.SysDictItem;
import com.gok.bcp.admin.feign.RemoteBcpDictService;
import com.gok.bcp.admin.vo.DictKvVo;
import com.gok.bcp.admin.vo.DictLevelKvVo;
import com.gok.components.common.util.R;
import com.gok.pboot.pms.service.IDictService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;


/**
 * <p>
 * 字典 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-30
 */
@Service
@Slf4j
@AllArgsConstructor
public class IDictServiceImpl implements IDictService {

    private final RemoteBcpDictService remoteBcpDictService;


    @Override
    public R<Map<String, List<DictKvVo>>> getDictKvBatchList(Set<String> dictKeyList) {
        R<Map<String, List<DictKvVo>>> dictKvBatchList = remoteBcpDictService.getDictKvBatchList(dictKeyList);
        Map<String, List<DictKvVo>> listMap =dictKvBatchList.getData();
        return R.ok( listMap,"请求成功");
    }

    @Override
    public R<List<DictKvVo>> getDictKvList(String dictKey) {
        return R.ok( remoteBcpDictService.getDictKvList(dictKey).getData(),"请求成功");
    }

    @Override
    public R<Map<String,List<DictLevelKvVo>>> batchTreeList(Set<String> dictKeyList) {
        return R.ok( remoteBcpDictService.batchTreeList(dictKeyList).getData(),"请求成功");
    }

    @Override
    public R<Map<String,List<SysDictItem>>> batchLeverList(Set<String> dictKeyList) {
        return R.ok( remoteBcpDictService.batchLeverList(dictKeyList).getData(),"请求成功");
    }


}
