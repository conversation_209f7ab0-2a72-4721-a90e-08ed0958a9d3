package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 项目作模块 enum
 *
 * <AUTHOR>
 * @date 2025/07/03
 */
@AllArgsConstructor
@Getter
public enum ProjectOperationModuleEnum implements ValueEnum<Integer> {

    /**
     * 项目质保
     */
    WARRANTY(0, "项目质保"),

    /**
     * 项目关闭
     */
    CLOSE(1, "项目关闭");

    private final Integer value;
    private final String name;



}
