package com.gok.pboot.pms.entity.dto;

import com.gok.pboot.pms.common.base.PageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.List;

/**
 * - 工时统计查询 -
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/10/17 15:33
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class WorkHourStatisticsFindPageDTO extends PageRequest {

    private List<Long> userIds;
    /**
     * 任务id
     */
    private List<String> taskId;

    private List<Long> projectIds;

    private LocalDate startDate;

    private LocalDate endDate;

    /**
     * 部门id
     */
    private List<Long> deptIds;

    /**
     * 人员状态
     */
    private Integer personnelStatus;

    /**
     * 项目状态
     */
    private List<Integer> projectStatusList;

    /**
     * 内外部项目(1:内部项目，2：外部项目)
     */
    private Integer isNotInternalProject;


    /**
     * 内部项目类型
     */
    private List<Integer> projectTypeList;
}
