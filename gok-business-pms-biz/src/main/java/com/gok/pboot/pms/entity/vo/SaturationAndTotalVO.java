package com.gok.pboot.pms.entity.vo;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/2/23
 * 工时饱和度-工时饱和度实体 -> 包含合计
 */
@Data
public class SaturationAndTotalVO {
    /**
     * 分页条目
     */
    private Page<SituationAnalysisVO> page;

    /**
     * 列表条目 - 工时饱和度不用分页
     */
    private List<SituationAnalysisDeptVO> list;

    /**
     * 合计 - 正常工时（人天）
     */
    private BigDecimal totalNormalHours;
    /**
     * 合计 - 加班工时（人天）
     */
    private BigDecimal totalAddedHours;

    /**
     * 合计 - 工作日加班工时（人天）
     */
    private BigDecimal totalWorkOvertimeHours;

    /**
     * 合计 - 休息日加班工时（人天）
     */
    private BigDecimal totalRestOvertimeHours;

    /**
     * 合计 - 节假日加班工时（人天）
     */
    private BigDecimal totalHolidayOvertimeHours;


    /**
     * 合计 - 调休工时（人天）
     */
    private BigDecimal totalLeaveHours;

    /**
     * 合计 - 项目耗用工时（人天）
     */
    private BigDecimal totalProjectHours;

    /**
     * 合计 - 项目分摊工时（人天）
     */
    private BigDecimal totalProjectShareHours;
    /**
     * 合计 - 项目管理工时（人天）
     */
    private BigDecimal totalManagementHours;
    /**
     * 合计 - 应出勤天数
     */
    private BigDecimal totalAttendanceDays;
    /**
     * 合计 - 工资核算出勤天数
     */
    private BigDecimal totalSalaryAttendanceDays;
    /**
     * 合计 - 工时饱和度
     */
    private BigDecimal totalHourSaturation;


}
