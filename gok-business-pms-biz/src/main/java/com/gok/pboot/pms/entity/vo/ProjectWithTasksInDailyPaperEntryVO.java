package com.gok.pboot.pms.entity.vo;

import lombok.*;

import java.util.List;

/**
 * - 项目及其下的可用任务 -
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/9/5 11:59
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@ToString
public class ProjectWithTasksInDailyPaperEntryVO {
    /**
     * ID
     */
    private Long id;
    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 是否内部项目
     */
    private Integer isInsideProject;

    /**
     * 销售人员名
     */
    private String salesmanUserName;
    /**
     * 项目经理人名
     */
    private String managerUserName;
    /**
     * 工时审核人姓名列表
     */
    private List<String> auditorNames;
    /**
     * 任务列表
     */
    private List<TaskInDailyPaperEntryVO> tasks;
    /**
     * 收藏项目实体ID
     */
    private Long collectId;
    /**
     * 收藏人id
     */
    private Long collectUserId;
    /**
     * 收藏时间
     */
    private String collectTime;
    public ProjectWithTasksInDailyPaperEntryVO(ProjectInDailyPaperEntryVO project, List<TaskInDailyPaperEntryVO> tasks){
        this.id = project.getId();
        this.projectName = project.getProjectName();
        this.isInsideProject = project.getIsInsideProject();
        this.salesmanUserName = project.getSalesmanUserName();
        this.managerUserName = project.getManagerUserName();
        this.auditorNames = project.getAuditorNames();
        this.collectId = project.getCollectId();
        this.collectUserId = project.getCollectUserId();
        this.collectTime = project.getCollectTime();
        this.tasks = tasks;
    }
}
