package com.gok.pboot.pms.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.common.user.PigxUser;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.entity.Filing;
import com.gok.pboot.pms.entity.vo.FilingFindPageVO;
import com.gok.pboot.pms.enumeration.FilingTypeEnum;
import com.gok.pboot.pms.mapper.FilingMapper;
import com.gok.pboot.pms.service.IFilingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * <p>
 * 归档 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-29
 */
@Service
@Slf4j
@Transactional(readOnly = true, rollbackFor = Exception.class)
public class FilingServiceImpl implements IFilingService {

    private final FilingMapper mapper;



    @Autowired
    public FilingServiceImpl(FilingMapper mapper){
        this.mapper = mapper;
    }

    /**yyyy-MM**/
    private static final String MONTH_REGEX = "^([1-9]\\d{3}-)(([0][1-9])|([1][0-2]))$";

    @Override
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public ApiResult<String> filingTask(String para) {
        String yaaerMonth = findYearMonth();
        if (para != null && Pattern.matches(MONTH_REGEX, para)){
            yaaerMonth = para;
        }

        String[] split = yaaerMonth.split("-");
        String year = split[0];
        String month = split[1];
        if (split[1].toCharArray()[0] == 0){
            month = String.valueOf(split[1].toCharArray()[0]);
        }

        List<Filing> filings = mapper.selectList(new QueryWrapper<Filing>().eq("year", year)
                .eq("month", month));
        if (filings.size() > 0 ){
            //JOB执行状态：1执行失败
            return ApiResult.failure("1");
        }

        LocalDateTime startTime = LocalDateTime.of(Integer.valueOf(year), Integer.valueOf(month), 1, 0, 0);

        Integer nextMonth = Integer.valueOf(month)+1;
        Integer nextYear = Integer.valueOf(year);
        if (Integer.valueOf(month) == 12){
            nextMonth = 1;
            nextYear = nextYear + 1;
        }
        LocalDateTime endTime = LocalDateTime.of(nextYear, nextMonth, 1, 0, 0);

        Filing filing = new Filing();
        filing.setYear(Integer.valueOf(year));
        filing.setMonth(Integer.valueOf(month));
        filing.setFiled(FilingTypeEnum.WGD.getValue());
        filing.setOperator("系统定时任务");
        filing.setFilingStartDatetime(startTime);
        filing.setFilingEndDatetime(endTime);
        BaseBuildEntityUtil.buildInsert(filing);

        ArrayList<Filing> filings1 = new ArrayList<>();
        filings1.add(filing);

        mapper.batchSave(filings1);

        return ApiResult.success("0");
    }

    @Override
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public ApiResult file(Long id) {
        //归档
        PigxUser user = SecurityUtils.getUser();
        mapper.getFileById(id,user.getName());
        return ApiResult.success("归档操作成功");
    }

    @Override
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public ApiResult cancelFile(Long id) {
        //取消归档
        PigxUser user = SecurityUtils.getUser();
        mapper.getCancelFileById(id,user.getName());
        return ApiResult.success("取消归档操作成功");
    }

    @Override
    public Page<FilingFindPageVO> FilingFindPageVO(PageRequest pageRequest, Map<String, Object> filter) {
        Page<FilingFindPageVO> list = mapper.FilingFindPageVO(new Page<Filing>(pageRequest.getPageNumber(), pageRequest.getPageSize()), filter);
        return list;
    }

    //获取当前时间的年月格式字符串
    public String findYearMonth() {
        int year;
        int month;
        String date;
        Calendar calendar = Calendar.getInstance();
        year = calendar.get(Calendar.YEAR);
        month = calendar.get(Calendar.MONTH) + 1;
        date = year + "-" + (month < 10 ? "0" + month : month);
        return date;
    }
}
