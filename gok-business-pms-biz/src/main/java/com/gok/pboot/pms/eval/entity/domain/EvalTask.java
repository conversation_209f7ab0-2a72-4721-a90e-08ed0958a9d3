package com.gok.pboot.pms.eval.entity.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 工单评价
 * <AUTHOR>
 * @create 2025/5/8
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("eval_task")
@Accessors(chain = true)
public class EvalTask extends BeanEntity<Long> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工单id
     */
    private Long taskId;

    /**
     * 任务交付质量评分（1-4）
     */
    private Integer qualityScore;

    /**
     * 任务交付质量评分备注
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String qualityRemark;

    /**
     * 任务按时交付评分（1-4）
     */
    private Integer timelinessScore;

    /**
     * 任务按时完成评分备注
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String timelinessRemark;

    /**
     * 工作配合度评分（1-4）
     */
    private Integer cooperationScore;

    /**
     * 工作配合度评分备注
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String cooperationRemark;

    /**
     * 执行规范评分（1-4）
     */
    private Integer disciplineScore;

    /**
     * 执行规范评分备注
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String disciplineRemark;

    /**
     * 综合评分
     */
    private BigDecimal comprehensiveScore;
}
