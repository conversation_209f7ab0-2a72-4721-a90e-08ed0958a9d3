package com.gok.pboot.pms.entity.vo;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * 合同款项情况Vo
 *
 * <AUTHOR>
 * @date 2024/2/22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class ContractPaymentVo {

    /**
     * '明细id'
     */
    private Long id;

    /**
     * '合同id'
     */
    private Long mainid;

    /**
     * '款项名称'
     */
    private String djbkx;

    /**
     * '款项比例
     */
    private BigDecimal kxbl;

    /**
     * '预计收(付)款日期  改 预计日期
     */
    private String yjskrq;

    /**
     * '收(付)款金额（含税）  改 款项金额
     */
    private BigDecimal skje;

    /**
     * '收(付)款金额（含税）
     */
    private String skjeTxt;

    /**
     * 税率
     */
    private Integer sl;

    /**
     * 税率
     */
    private String slTxt;

    /**
     * 收(付)款金额(不含税)  改  款项金额(不含税)
     */
    private BigDecimal sfkjebhs;

    /**
     * 收(付)款金额(不含税)
     */
    private String sfkjebhsTxt;

    /**
     * 收(付)款状态  改 款项状态
     */
    private Integer skzt;

    /**
     * 收(付)款状态txt
     */
    private String skztTxt;

    /**
     * 实际收(付)款金额(含税)   改  实际款项金额
     */
    private BigDecimal sjsfkjehs;

    /**
     * 实际收(付)款金额(含税)
     */
    private String sjsfkjehsTxt;

    /**
     * '实际收(付)款日期  改 实际款项日期
     */
    private String sjsfkrq;

    /**
     * 收(付)款条件（项目进度）  改 款项条件
     */
    private String sktjxmjd;

    /**
     * 待收（付）款金额  改 款项差额
     */
    private BigDecimal dsfkje;

    /**
     * 待收（付）款金额
     */
    private String dsfkjeTxt;

    /**
     * 收(付)款备注  改 款项备注
     */
    private String skbz;

    /**
     * 结算单（电子版）
     */
    private String ddfj;

    /**
     * 结算单（电子版）图像文件id
     */
    private String ddfjImagefileid;

    /**
     * 结算单（电子版）集合
     */
    List<OaFileInfoVo> ddfjList;

    /**
     * 结算单（扫描件）
     */
    private String jsdfj;

    /**
     * 结算单（扫描件）图像文件id
     */
    private String jsdfjImagefileid;

    /**
     * 结算单（扫描件）集合
     */
    List<OaFileInfoVo> jsdfjList;

}
