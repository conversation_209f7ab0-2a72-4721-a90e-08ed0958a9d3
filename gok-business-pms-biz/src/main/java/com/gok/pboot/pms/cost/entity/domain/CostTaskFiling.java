package com.gok.pboot.pms.cost.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 工单工时归档
 *
 * <AUTHOR>
 * @date 2024/04/16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("cost_task_filing")
public class CostTaskFiling extends BeanEntity<Long> {

    /**
     * 年
     */
    private Integer year;

    /**
     * 月
     */
    private Integer month;

    /**
     * 归档年月对应开始日期时间（当月1号0点）
     */
    private LocalDateTime filingStartDatetime;

    /**
     * 归档年月对应结束日期时间（次月1号0点）
     */
    private LocalDateTime filingEndDatetime;

    /**
     * 对应状态（0=未归档，1=已归档）
     */
    private Integer filed;

    /**
     * 操作人
     */
    private String operator;
} 