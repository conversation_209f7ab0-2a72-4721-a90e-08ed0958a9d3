package com.gok.pboot.pms.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023-07-11 17:05:26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectInfoMemberVO {

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 项目销售人员（客户经理）
     */
    private String projectSalesperson;

    /**
     * 项目销售 ID（客户经理id）
     */
    private String salesmanUserId;
    /**
     * 售前经理姓名
     */
    private String preSaleUserName;

    /**
     * 售前经理 ID
     */
    private String preSaleUserId;

    /**
     * 项目经理人员姓名
     */
    private String managerUserName;

    /**
     * 项目经理 ID
     */
    private String managerUserId;

    /**
     * 业务经理人员姓名
     */
    private String businessManagerName;

    /**
     * 业务经理 ID
     */
    private String businessManagerId;

}
