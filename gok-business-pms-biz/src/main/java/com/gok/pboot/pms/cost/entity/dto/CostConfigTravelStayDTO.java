package com.gok.pboot.pms.cost.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 差旅住宿标准配置 dto
 *
 * <AUTHOR>
 * @date 2025/01/07
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CostConfigTravelStayDTO {

    /**
     * 城市标准
     */
    @Length(max = 25, message = "城市标准长度不能超过25")
    @NotBlank(message = "城市标准不能为空")
    private String cityStandards;

    /**
     * 城市ids
     */
    @NotBlank(message = "对应城市不能为空")
    private String cityIds;

    /**
     * 总经办金额
     */
    @DecimalMin(value = "0.00", message = "总经办金额必须大于或等于0")
    @DecimalMax(value = "9999.00", message = "总经办金额必须小于或等于9999")
    @NotNull(message = "总经办金额不能为空")
    private BigDecimal generalOfficePrice;


    /**
     * 总监级以上金额
     */
    @DecimalMin(value = "0.00", message = "总监级以上金额必须大于或等于0")
    @DecimalMax(value = "9999.00", message = "总监级以上金额必须小于或等于9999")
    @NotNull(message = "总监级以上金额不能为空")
    private BigDecimal directorAbovePrice;


    /**
     * 总监级以下金额
     */
    @DecimalMin(value = "0.00", message = "总监级以下金额必须大于或等于0")
    @DecimalMax(value = "9999.00", message = "总监级以下金额必须小于或等于9999")
    @NotNull(message = "总监级以下金额不能为空")
    private BigDecimal directorBelowPrice;
}
