package com.gok.pboot.pms.Util;

import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

/**
 * RSA工具类
 *
 * <AUTHOR>
 * @version 2.0.3
 */
@UtilityClass
public class RSAUtils {

    private static final int MAX_ENCRYPT_BLOCK = 117;

    private static final int MAX_DECRYPT_BLOCK = 128;

    public static String encrypt(String content, String base64Key){
        if (StringUtils.isBlank(content)){
            return "";
        }

        Cipher cipher;
        byte[] srcBytes;
        byte[] resultBytes = null, tmpBytes;
        final RSAPublicKey key;

        try {
            key = (RSAPublicKey) KeyFactory.getInstance("RSA").generatePublic(
                    new X509EncodedKeySpec(Base64.getDecoder().decode(base64Key))
            );
            cipher = Cipher.getInstance("RSA");
            cipher.init(Cipher.ENCRYPT_MODE, key);
            srcBytes = content.getBytes(StandardCharsets.UTF_8);
            for (int i = 0; i < srcBytes.length; i += MAX_ENCRYPT_BLOCK) {
                // 分段加密，Java不支持内容超出MAX限制的加密
                tmpBytes = cipher.doFinal(ArrayUtils.subarray(srcBytes, i, i + MAX_ENCRYPT_BLOCK));
                resultBytes = ArrayUtils.addAll(resultBytes, tmpBytes);
            }
        } catch (
                NoSuchAlgorithmException |
                NoSuchPaddingException |
                InvalidKeyException |
                IllegalBlockSizeException |
                BadPaddingException |
                InvalidKeySpecException e
        ) {
            throw new AssertionError(e);
        }

        return Base64.getEncoder().encodeToString(resultBytes);
    }

    public static String decrypt(String content, String base64key){
        if (StringUtils.isEmpty(content)){
            return "";
        }

        byte[] inputByte = Base64.getDecoder().decode(content);
        Cipher cipher;
        byte[] tmpBytes;
        int inputLen = inputByte.length;
        int offset = 0;
        int i = 0;
        final RSAPrivateKey key;

        try (ByteArrayOutputStream out = new ByteArrayOutputStream()){
            key = (RSAPrivateKey) KeyFactory.getInstance("RSA").generatePrivate(
                    new PKCS8EncodedKeySpec(Base64.getDecoder().decode(base64key))
            );
            cipher = Cipher.getInstance("RSA");
            cipher.init(Cipher.DECRYPT_MODE, key);
            // 分段解密，Java不支持内容超过MAX长度的解密
            while (inputLen - offset > 0) {
                if (inputLen - offset > MAX_DECRYPT_BLOCK) {
                    tmpBytes = cipher.doFinal(inputByte, offset, MAX_DECRYPT_BLOCK);
                } else {
                    tmpBytes = cipher.doFinal(inputByte, offset, inputLen - offset);
                }
                out.write(tmpBytes, 0, tmpBytes.length);
                i++;
                offset = i * MAX_DECRYPT_BLOCK;
            }

            return out.toString();
        } catch (
                NoSuchAlgorithmException | NoSuchPaddingException | InvalidKeyException | IllegalBlockSizeException |
                BadPaddingException | IOException | InvalidKeySpecException e
        ) {
            throw new AssertionError(e);
        }
    }
}
