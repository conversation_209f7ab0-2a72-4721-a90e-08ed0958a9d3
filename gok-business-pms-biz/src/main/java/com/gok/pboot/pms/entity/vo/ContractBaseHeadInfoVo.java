package com.gok.pboot.pms.entity.vo;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 合同基本信息头部数据vo
 *
 * <AUTHOR>
 * @date 2024/2/22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class ContractBaseHeadInfoVo {

    /**
     * 合同id
     */
    private Long id;

    /**
     * 合同名称
     */
    private String htmc;

    /**
     * 合同编号
     */
    private String htbh;

    /**
     * 合同状态
     */
    private Integer htzt;

    /**
     * 合同状态
     */
    private String htztText;

    /**
     * 项目名称
     */
    private Long xmmc;

    /**
     * 项目名称
     */
    private String xmmcName;

    /**
     * 项目编号
     */
    private String xmbh;

    /**
     * 合同所属公司
     */
    private Integer htssgs;
    /**
     * 合同所属公司
     */
    private String htssgsText;

    /**
     * 对方名称id
     */
    private String khmc;
    /**
     * 对方名称
     */
    private String khmcName;

    /**
     * 合同类别
     */
    private Integer htlb;
    /**
     * 合同类别
     */
    private String htlbText;

    /**
     * 合同细类
     */
    private Integer htxl;
    /**
     * 合同细类
     */
    private String htxlText;

    /**
     * 项目销售人员（客户经理）id
     */
    private Long xmxsry;

    /**
     * 项目销售人员（客户经理）
     */
    private String xmxsryName;

    /**
     * 合同金额（含税）
     */
    private String htje;

    /**
     * 累计收款金额
     */
    private String ljskje;

    /**
     * 合同款项进度
     */
    private String htkxjd;

    /**
     * 是否内部项目（1=是，2=否，""）
     *
     * @see com.gok.pboot.pms.enumeration.IsNoInternalProjectEnum
     */
    private Long isNotInternalProject;

}
