package com.gok.pboot.pms.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.Util.PmsDictUtil;
import com.gok.pboot.pms.enumeration.PmsDictEnum;
import com.google.common.collect.HashMultimap;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 商机对应联系人表(BusinessContact)表实体类
 *
 * <AUTHOR>
 * @since 2023-11-22 00:59:53
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("business_contact")
public class BusinessContact {

    /**
     * 商机ID(项目ID)
     */
    private Long businessId;

    /**
     * 联系人ID
     */
    private Long contactUserId;

    /**
     * 联系人
     */
    private String contactUserName;

    /**
     * 联系方式
     */
    private String contactWay;

    /**
     * 联系人人员类型
     */
    private String contactUserType;

    /**
     * 更新结果
     *
     * @param dictItemMap 字典项map
     * @param r 商机对应联系人表
     */
    public static void updateResultParam(
            HashMultimap<String, PmsDictItem> dictItemMap,
            BusinessContact r
    ) {
        // 更新联系人人员类型字段
        r.setContactUserType(PmsDictUtil.getLabelFromPmsDictItems(r.getContactUserType(), dictItemMap.get(PmsDictEnum.CONTACT_ROLE.getValue())));
    }
}