package com.gok.pboot.pms.entity.dto;

import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.springframework.util.Assert;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * 项目会议纪要 新增/编辑 Dto类
 *
 * <AUTHOR>
 * @since 2023-07-13
 **/
@Data
public class ProjectMeetingDTO {

    /**
     * 项目风险主键id
     */
    private Long id;

    /**
     * 项目id
     */
    @NotNull(message = "项目id不能为空")
    private Long projectId;

    /**
     * 会议名称
     */
    @NotBlank(message = "会议名称不能为空")
    private String name;

    /**
     * 召集人id
     */
    private Long convenerId;

    /**
     * 召集人
     */
    @NotBlank(message = "召集人不能为空")
    private String convener;

    /**
     * 会议日期
     * 格式为：yyyy-MM-dd
     */
    @NotNull(message = "会议日期不能为空")
    private LocalDate meetingDate;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 记录人id
     */
    private Long recorderId;

    /**
     * 记录人
     */
    private String recorder;

    /**
     * 会议地点
     */
    private String place;

    /**
     * 参会人员
     * e.g: 张三，李四，王五
     */
    @Length(max = 100)
    private String member;

    /**
     * 会议目标
     */
    @Length(max = 500)
    private String objective;

    /**
     * 会议过程
     */
    @Length(max = 2000)
    private String process;

    /**
     * 会议决议
     */
    @Length(max = 500)
    private String resolution;

    /**
     * 待办事项
     */
    @Length(max = 500)
    private String backlog;

    /**
     * 文件id集合
     */
    private String docIds;

    /**
     * 文件名称集合
     */
    @Length(max = 512)
    private String docNames;

    public void validate() {
        String regexp = "\\d{2}:\\d{2}";
        if (StrUtil.isNotBlank(this.startTime)) {
            Assert.isTrue(ReUtil.isMatch(regexp, this.startTime), "开始时间不合法");
        }
        if (StrUtil.isNotBlank(this.endTime)) {
            Assert.isTrue(ReUtil.isMatch(regexp, this.endTime), "结束时间不合法");
        }
        if (StrUtil.isNotBlank(docIds)) {
            Assert.isTrue(StrUtil.split(docIds, StrUtil.C_COMMA).size() <= 5, "附件个数超出限制");
        }
    }

}
