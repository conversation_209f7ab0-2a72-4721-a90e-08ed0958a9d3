package com.gok.pboot.pms.service;


import com.gok.pboot.pms.entity.vo.OaFileDownloadVo;
import com.gok.pboot.pms.enumeration.OaFileDownLoadTypeEnum;

import javax.servlet.http.HttpServletResponse;

/**
 * <p>
 * Oa 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-19
 */
public interface IOaService {

    /**
     *  Oa附件下载
     * @param contractId 合同id
     * @param fileType 文件类型
     * @return
     */
    OaFileDownloadVo oaFileDownload(Long contractId, OaFileDownLoadTypeEnum fileType, String fileId);

    /**
     * 根据OA链接下载文件
     * @param response  response
     * @param fileUrl oa文件地址
     * @param fileName OA文件名
     * @return
     */
    void getFileByUrl(HttpServletResponse response, String fileUrl, String fileName,boolean ifPreview);

    /**
     * 获取 OA 流状态
     *
     * @param requestId 请求 ID
     * @return {@link Integer }
     */
    Integer getOaWorkFlowStatus(Long requestId);


    /**
     * 根据项目id查询 OA合同台账是否存在归档的合同
     *
     * @param projectId 项目 ID
     * @return {@link Boolean }
     */
    Boolean existContractByOaHttz(Long projectId);
}
