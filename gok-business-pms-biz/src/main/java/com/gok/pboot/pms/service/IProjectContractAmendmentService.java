package com.gok.pboot.pms.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.entity.domain.ProjectContractAmendment;
import com.gok.pboot.pms.entity.vo.ContractChangeInfoVo;

import java.util.List;

/**
 * 合同变更 服务类接口
 *
 * <AUTHOR>
 * @date 2023/11/21
 */
public interface IProjectContractAmendmentService extends IService<ProjectContractAmendment> {


    /**
     *  更具合同id查询合同变更记录
     * @param id 合同id
     * @return 变更记录
     */
    List<ContractChangeInfoVo> getContractChangeInfoVoList(Long id);


}
