package com.gok.pboot.pms.cost.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 * 成本管理人员级别自定义补贴明细
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("cost_manage_personnel_custom_detail")
public class CostManagePersonnelCustomDetail extends BeanEntity<Long> {

    private static final long serialVersionUID = 1L;

    /**
     * 版本ID
     */
    private Long versionId;

    /**
     * 成本管理估算结果ID
     */
    private Long estimationResultsId;

    /**
     * 自定义补贴配置ID
     */
    private Long subsidyCustomConfigId;

    /**
     * 自定义补贴名称
     */
    private String subsidyCustomName;

    /**
     * 自定义补贴金额
     */
    private BigDecimal subsidyCustomAmount;

}
