package com.gok.pboot.pms.entity.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 项目任务进展填报
 *
 * <AUTHOR>
 * @version 1.1.0
 */
@Getter
@Setter
@ToString
public class ProjectTaskProgressAddDTO {

    /**
     * 任务ID
     */
    @NotNull(message = "任务ID不能为空")
    private Long taskId;

    /**
     * 进展值
     * @see com.gok.pboot.pms.enumeration.ProjectTaskProgressEnum
     */
    @NotNull(message = "进度不能为空")
    private Integer progress;

    /**
     * 进展内容
     */
    @NotBlank(message = "进展内容不能为空")
    @Length(max = 600, message = "进展内容不能超过600字")
    private String content;
}
