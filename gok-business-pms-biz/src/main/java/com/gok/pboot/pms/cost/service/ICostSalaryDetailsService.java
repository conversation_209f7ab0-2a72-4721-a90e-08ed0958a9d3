package com.gok.pboot.pms.cost.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.cost.entity.domain.CostSalaryDetails;
import com.gok.pboot.pms.cost.entity.dto.CostSalaryDTO;
import com.gok.pboot.pms.enumeration.CostSalaryRelateTypeEnum;

import java.util.Collection;

/**
 * 成本薪资明细表 服务接口
 *
 * <AUTHOR>
 * @date 2025/05/08
 */
public interface ICostSalaryDetailsService extends IService<CostSalaryDetails> {
    /**
     * 批量保存或更新
     *
     * @param salaryDTOList salaryDTOList
     */
    void batchSaveOrUpdate(Collection<CostSalaryDTO> salaryDTOList);

    /**
     * 按关联 ID 删除批处理
     *
     * @param relateIds        relateIds
     * @param costSalaryRelateTypeEnum 相关类型枚举
     */
    void removeBatchByRelateIds(Collection<Long> relateIds, CostSalaryRelateTypeEnum costSalaryRelateTypeEnum);
}