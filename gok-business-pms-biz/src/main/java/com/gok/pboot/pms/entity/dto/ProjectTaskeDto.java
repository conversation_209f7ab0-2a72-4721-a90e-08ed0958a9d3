package com.gok.pboot.pms.entity.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
 * 项目任务Dto
 *
 * <AUTHOR>
 * @date 2024/01/22
 */
@Data
public class ProjectTaskeDto {

    /**
     * 任务ID
     */
    private Long id;

    /**
     * 所属项目ID
     */
    @NotNull(message = "所属项目ID不能为空")
    private Long projectId;

    /**
     * 任务名称
     */
    @NotBlank(message = "任务名称不能为空")
    private String title;

    /**
     * 任务类型（0=售前支撑，1=售后交付，null=内部项目不区分）
     * @link com.gok.pboot.pms.enumeration.ProjectTaskKindEnum
     */
    private Integer kind;

    /**
     * 任务状态（0=正常，1=结束）
     * @libk com.gok.pboot.pms.enumeration.ProjectTaskStateEnum
     */
    private Integer state;

    /**
     * 是否长期任务
     */
    @NotNull(message = "是否长期任务不能为空")
    private Integer permanentFlag;

    /**
     * 计划开始日期
     */
    private LocalDate expectStartDate;

    /**
     * 计划结束日期
     */
    private LocalDate expectEndDate;

    /**
     * 实际开始日期
     */
    private LocalDate startDate;

    /**
     * 实际结束日期
     */
    private LocalDate endDate;

    /**
     * 任务负责人列表（key为用户id，value为用户姓名）
     */
    private List<ProjectTaskeUserDto> leaderNames;

    /**
     * 任务参与人列表（key为用户id，value为用户姓名）
     */
    private List<ProjectTaskeUserDto> memberNames;

}
