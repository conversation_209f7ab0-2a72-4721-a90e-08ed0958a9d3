package com.gok.pboot.pms.enumeration;

/**
 * 项目-资源通道枚举
 *
 * <AUTHOR>
 */
public enum ResourceChannelEnum implements ValueEnum<Integer> {
    /**
     * 华为体系
     */
    huawei(0, "华为体系"),
    /**
     * 运营商体系
     */
    operator(1, "运营商体系"),
    /**
     * 能源体系
     */
    energy(2, "能源体系"),
    /**
     * 金融体系
     */
    finance(3, "金融体系"),
    /**
     * 政府体系
     */
    government(4, "政府体系"),
    /**
     * 长虹体系
     */
    changhong(5, "长虹体系"),
    /**
     * 其他厂商体系
     */
    other_manufacturers(6, "其他厂商体系"),
    /**
     * 其他
     */
    other(7, "其他");

    //值
    private Integer  value;
    //名称
    private String name;

    ResourceChannelEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
    /**
     * 获取值
     *
     * @return Integer
     */
    @Override
    public Integer getValue() {
        return value;
    }

    /**
     * 获取名称
     *
     * @return String
     */
    @Override
    public String getName() {
        return name;
    }

    public static String getNameByVal(Integer value) {
        for (ResourceChannelEnum resourceChannelEnum : ResourceChannelEnum.values()) {
            if (resourceChannelEnum.value.equals(value)) {
                return resourceChannelEnum.name;
            }
        }
        return "";
    }
    public static Integer getValByName(String name) {
        for (ResourceChannelEnum resourceChannelEnum : ResourceChannelEnum.values()) {
            if (resourceChannelEnum.getName().equals(name)) {
                return resourceChannelEnum.getValue();
            }
        }
        return null;
    }

    public static ResourceChannelEnum getResourceChannelEnum(Integer value) {
        for (ResourceChannelEnum resourceChannelEnum : ResourceChannelEnum.values()) {
            if (resourceChannelEnum.value.equals(value)) {
                return resourceChannelEnum;
            }
        }
        return null;
    }
}
