package com.gok.pboot.pms.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.bcp.upms.vo.SysMenuVo;
import com.gok.pboot.pms.Util.CollectionUtils;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.exception.ServiceException;
import com.gok.pboot.pms.entity.CustomerBusiness;
import com.gok.pboot.pms.entity.CustomerBusinessPerson;
import com.gok.pboot.pms.entity.dto.CustomerBusinessDTO;
import com.gok.pboot.pms.entity.dto.CustomerBusinessSearchDTO;
import com.gok.pboot.pms.entity.vo.*;
import com.gok.pboot.pms.service.ICustomerBusinessPersonService;
import com.gok.pboot.pms.service.ICustomerBusinessService;
import com.gok.pboot.pms.service.ICustomerBusinessUnitService;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;


/**
 * <p>
 * 客户经营单元(基础概况) controller
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-12
 * @menu 客户经营单元
 */

@RestController
@RequestMapping("/customerBusiness")
@AllArgsConstructor
public class CustomerBusinessController {

    private final ICustomerBusinessService service;

    private final ICustomerBusinessUnitService unitService;

    private final ICustomerBusinessPersonService personService;
    /**
     * 分页查询客户经营单元
     * @param dto
     * @return {@link ApiResult}
     */
    @GetMapping("/findPage")
    @PreAuthorize("@pms.hasPermission('CUSTOMER_UNITS')")
    public ApiResult<Page<CustomerBusinessPageVO>> findPage(CustomerBusinessDTO dto) {
        return ApiResult.success(service.findPageList(dto));
    }

    /**
     * 新增客户经营单元(新增不用传id，编辑要传id)
     *
     * @param request 新增请求实体
     * @return {@link ApiResult}
     */
    @PostMapping("/saveBusiness")
    public ApiResult<Long> save( @Valid @RequestBody CustomerBusiness request) {
        return ApiResult.success(service.saveBusiness(request));
    }

    /**
     * 根据id查询客户经营单元
     *
     * @param id 客户经营单元主键id
     * @return {@link ApiResult}{@link CustomerBusinessVO}
     */
    @GetMapping("/{id}")
    public ApiResult<CustomerBusinessVO> findById(@PathVariable("id") Long id) {
        return ApiResult.success(service.getById(id));
    }

    /**
     * 获取菜单权限
     * @param request 请求对象
     * @return {@link ApiResult}<{@link List}<{@link SysMenuVo}>>
     * customParam filter_L_businessId 传入的客户经营单元id
     * customParam filter_S_permission 传入的权限标识
     * customParam filter_S_menuType 传入的菜单类型（0菜单 1按钮，9系统）
     */
    @GetMapping("/getMenuAuthority")
    @PreAuthorize("@pms.hasPermission('CUSTOMER_BUSINESS_DETAIL')")
    public ApiResult<CustomerBusinessSysMenuVo> getMenuAuthority(HttpServletRequest request) {
        return ApiResult.success(service.getMenuAuthority(request));
    }


    /**
     * 删除客户经营单元
     * @param id
     * @return
     */
    @GetMapping("/deletedById")
    public ApiResult<String> deletedById(@RequestParam ("id") String id) {
        //若有所属客户，不能删除经营单元
        List<CustomerBusinessUnitPageVO> units = unitService.selectSimplyList(Long.valueOf(id));
        if(CollectionUtils.isNotEmpty(units)){
            throw new ServiceException("当前经营单元下有所属客户，无法删除");
        }
        service.removeById(id);
        personService.remove((new QueryWrapper<CustomerBusinessPerson>().eq("business_id", id)));

        //删除经营单元同步至OA
        service.deleteCustomerBusinessSyncOA(id);
        return ApiResult.success("删除成功");
    }


    /**
     * 获取客户经营单元名称列表
     * @param customerBusinessSearchDTO
     * @return
     */
    @PostMapping("/findNameList")
    public ApiResult<List<CustomerBusinessListVO>> findNameList(@RequestBody CustomerBusinessSearchDTO customerBusinessSearchDTO) {
        return ApiResult.success(service.findNameList(customerBusinessSearchDTO));
    }

}
