package com.gok.pboot.pms.cost.service.impl;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.cost.config.MergeCellStrategyHandler;
import com.gok.pboot.pms.cost.entity.domain.CostDeliverTask;
import com.gok.pboot.pms.cost.entity.domain.CostTaskDailyPaperEntry;
import com.gok.pboot.pms.cost.entity.dto.TaskStatQueryDTO;
import com.gok.pboot.pms.cost.entity.vo.*;
import com.gok.pboot.pms.cost.enums.CostTaskStatDimensionEnum;
import com.gok.pboot.pms.cost.enums.CostTaskStatusEnum;
import com.gok.pboot.pms.cost.mapper.CostTaskStatMapper;
import com.gok.pboot.pms.cost.service.CostTaskStatService;
import com.gok.pboot.pms.cost.service.ICostTaskDailyPaperEntryService;
import com.gok.pboot.pms.entity.domain.ProjectInfo;
import com.gok.pboot.pms.entity.domain.Roster;
import com.gok.pboot.pms.entity.vo.SysUserDataScopeVO;
import com.gok.pboot.pms.enumeration.ApprovalStatusEnum;
import com.gok.pboot.pms.enumeration.ProjectTaskKindEnum;
import com.gok.pboot.pms.eval.entity.domain.EvalUserRole;
import com.gok.pboot.pms.eval.enums.EvalUserRoleEnum;
import com.gok.pboot.pms.eval.service.IEvalUserRoleService;
import com.gok.pboot.pms.handler.ProjectScopeHandle;
import com.gok.pboot.pms.handler.perm.PmsRetriever;
import lombok.AllArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.codec.CharEncoding;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 多维度工单数据统计服务实现
 *
 * <AUTHOR>
 * @date 2025/05/12
 */
@Service
@Log4j2
@AllArgsConstructor
public class CostTaskStatServiceImpl implements CostTaskStatService {

    private final ICostTaskDailyPaperEntryService costTaskDailyPaperEntryService;

    private final CostTaskStatMapper costTaskStatMapper;

    private final ProjectScopeHandle projectScopeHandle;

    private final PmsRetriever pmsRetriever;

    private final IEvalUserRoleService evalUserRoleService;

    private final static String PROJECT_KANBAN = "项目工单看板";

    private final static String PERSONNEL_KANBAN = "人员工单看板";

    /**
     * 项目工单看板-多维度售后交付工单统计
     *
     * @param pageRequest 页面请求
     * @param query       查询
     * @return {@link Page }<{@link ? } {@link extends } {@link ProjectDeliverStatBaseVO }>
     */
    @Override
    public Page<? extends ProjectDeliverStatBaseVO> findProjectDeliverTaskStat(PageRequest pageRequest, TaskStatQueryDTO query) {
        query.setTaskType(ProjectTaskKindEnum.AFTER_SALES_DELIVERY.getValue());
        CostTaskStatDimensionEnum dimensionEnum = query.getDimensionEnum();
        if (dimensionEnum == null) {
            return pageRequest.of();
        }
        // 根据维度选择不同的查询方法
        switch (dimensionEnum) {
            case PROJECT:
                return projectProDimDeliverStatPage(query, pageRequest);
            case PERSONNEL:
                return projectPerDimDeliverStatPage(query, pageRequest);
            case TASK:
                return projectTaskDimDeliverStatPage(query, pageRequest);
            default:
                return pageRequest.of();
        }
    }

    /**
     * 项目工单看板-多维度售后交付工单统计导出
     *
     * @param query    查询
     * @param response 响应
     */
    @Override
    public synchronized void exportProjectDeliverTaskStat(TaskStatQueryDTO query, HttpServletResponse response) {
        ProjectTaskKindEnum taskTypeEnum = ProjectTaskKindEnum.AFTER_SALES_DELIVERY;
        query.setTaskType(taskTypeEnum.getValue());
        CostTaskStatDimensionEnum dimensionEnum = query.getDimensionEnum();
        // 根据维度获取导出的数据
        List<? extends ProjectDeliverStatBaseVO> resultList = new ArrayList<>();
        switch (dimensionEnum) {
            case PROJECT:
                resultList = projectProDimDeliverStatList(query);
                break;
            case PERSONNEL:
                resultList = projectPerDimDeliverStatList(query);
                break;
            case TASK:
                resultList = projectTaskDimDeliverStatList(query);
                break;
            default:
        }
        exportExcel(response, dimensionEnum, taskTypeEnum, resultList, PROJECT_KANBAN);
    }


    /**
     * 项目工单看板-多维度售前支撑工单统计
     *
     * @param pageRequest 页面请求
     * @param query       查询
     * @return {@link Page }<{@link ? } {@link extends } {@link ProjectPreSaleStatBaseVO }>
     */
    @Override
    public Page<? extends ProjectPreSaleStatBaseVO> findProjectPreSaleTaskStat(PageRequest pageRequest, TaskStatQueryDTO query) {
        query.setTaskType(ProjectTaskKindEnum.PRE_SALES_SUPPORT.getValue());
        CostTaskStatDimensionEnum dimensionEnum = query.getDimensionEnum();
        if (dimensionEnum == null) {
            return pageRequest.of();
        }
        // 根据维度选择不同的查询方法
        switch (dimensionEnum) {
            case PROJECT:
                return projectProDimPreSaleStatPage(query, pageRequest);
            case PERSONNEL:
                return projectPerDimPreSaleStatPage(query, pageRequest);
            case TASK:
                return projectTaskDimPreSaleStatPage(query, pageRequest);
            default:
                return pageRequest.of();
        }
    }

    /**
     * 项目工单看板-多维度售前支撑工单统计导出
     *
     * @param query    查询
     * @param response 响应
     */
    @Override
    public synchronized void exportProjectPreSaleTaskStat(TaskStatQueryDTO query, HttpServletResponse response) {
        ProjectTaskKindEnum taskTypeEnum = ProjectTaskKindEnum.PRE_SALES_SUPPORT;
        query.setTaskType(taskTypeEnum.getValue());
        CostTaskStatDimensionEnum dimensionEnum = query.getDimensionEnum();
        // 根据维度获取导出的数据
        List<? extends ProjectPreSaleStatBaseVO> resultList = new ArrayList<>();
        switch (dimensionEnum) {
            case PROJECT:
                resultList = projectProDimPreSaleStatList(query);
                break;
            case PERSONNEL:
                resultList = projectPerDimPreSaleStatList(query);
                break;
            case TASK:
                resultList = projectTaskDimPreSaleStatList(query);
                break;
            default:
        }
        exportExcel(response, dimensionEnum, taskTypeEnum, resultList, PROJECT_KANBAN);
    }


    /*人员工单看板*/

    /**
     * 人员工单看板-多维度售后交付工单统计
     *
     * @param pageRequest 页面请求
     * @param query       查询
     * @return {@link Page }<{@link ? } {@link extends } {@link PersonnelDeliverStatBaseVO }>
     */
    @Override
    public Page<? extends PersonnelDeliverStatBaseVO> findPersonnelDeliverTaskStat(PageRequest pageRequest, TaskStatQueryDTO query) {
        query.setTaskType(ProjectTaskKindEnum.AFTER_SALES_DELIVERY.getValue());
        CostTaskStatDimensionEnum dimensionEnum = query.getDimensionEnum();
        if (dimensionEnum == null) {
            return pageRequest.of();
        }
        // 根据维度选择不同的查询方法
        switch (dimensionEnum) {
            case PROJECT:
                return personnelProDimDeliverStatPage(query, pageRequest);
            case PERSONNEL:
                return personnelPerDimDeliverStatPage(query, pageRequest);
            case TASK:
                return personnelTaskDimDeliverStatPage(query, pageRequest);
            default:
                return pageRequest.of();
        }
    }

    /**
     * 人员工单看板-项目维度售后交付工单统计
     *
     * @param query       查询
     * @param pageRequest 页面请求
     * @return {@link Page }<{@link ? } {@link extends } {@link PersonnelDeliverStatBaseVO }>
     */
    private Page<PersonnelProDimDeliverStatVO> personnelProDimDeliverStatPage(TaskStatQueryDTO query, PageRequest pageRequest) {

        // 按人员分页
        Page<Roster> rosterPage = getRosterPage(pageRequest, query);

        List<Roster> userInfoList = rosterPage.getRecords();

        if (CollUtil.isEmpty(userInfoList)) {
            return pageRequest.of();
        }
        Set<Long> userIds = CollStreamUtil.toSet(userInfoList, Roster::getId);

        query.setUserIds(userIds);

        List<PersonnelProDimDeliverStatVO> records = costTaskStatMapper.personnelProDimDeliverStatList(query);

        personnelProDimDeliverStatHandle(records, query);

        Page<PersonnelProDimDeliverStatVO> page = pageRequest.of();

        return page.setTotal(rosterPage.getTotal()).setRecords(records);
    }

    private Page<Roster> getRosterPage(PageRequest pageRequest, TaskStatQueryDTO query) {
        getUserDataScope(query);
        if (Boolean.FALSE.equals(query.getIsAll()) && CollUtil.isEmpty(query.getUserAuth())) {
            return pageRequest.of();
        }
        Page<Roster> rosterPage = pageRequest.of();
        costTaskStatMapper.rosterPage(rosterPage, query);
        return rosterPage;
    }

    private List<Roster> getRosterList(TaskStatQueryDTO query) {
        getUserDataScope(query);
        if (Boolean.FALSE.equals(query.getIsAll()) && CollUtil.isEmpty(query.getUserAuth())) {
            return Collections.emptyList();
        }
        return costTaskStatMapper.rosterPage(query);
    }

    private void getUserDataScope(TaskStatQueryDTO query) {
        boolean pmo = evalUserRoleService.lambdaQuery()
                .eq(EvalUserRole::getRole, EvalUserRoleEnum.PMO.getValue())
                .eq(EvalUserRole::getUserId, SecurityUtils.getUser().getId())
                .exists();

        if (pmo) {
            query.setIsAll(true);
        } else {
            SysUserDataScopeVO dataScope = projectScopeHandle.findDataScope();
            query.setIsAll(dataScope.getIsAll());
            List<Long> userIdList = dataScope.getUserIdList();
            query.setUserAuth(userIdList);
        }
        log.warn("dataScope:{},{},{}", query.getIsAll(), query.getUserAuth(), query.getUserIds());
    }


    private void personnelProDimDeliverStatHandle(List<PersonnelProDimDeliverStatVO> records, TaskStatQueryDTO query) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        Set<Long> userIds = CollStreamUtil.toSet(records, PersonnelProDimDeliverStatVO::getUserId);

        query.setUserIds(userIds);

        List<CostDeliverTask> allTasks = getCostTasks(query);

        //  按项目ID分组
        Map<String, List<CostDeliverTask>> projectTasksMap = allTasks.stream()
                .collect(Collectors.groupingBy(e -> buildPerProKey(e.getProjectId(), e.getManagerId())));

        //  计算每个项目的预算成本和实际成本
        records.forEach(stat -> {
            String key = buildPerProKey(stat.getProjectId(), stat.getUserId());
            List<CostDeliverTask> projectTasks = projectTasksMap.getOrDefault(key, Collections.emptyList());

            // 计算预算人工成本总和
            BigDecimal totalBudgetCost = projectTasks.stream()
                    .map(task -> toBigDecimal(task.getBudgetCost()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 计算实际人工成本总和(只计算已完成的工单)
            BigDecimal totalActualCost = projectTasks.stream()
                    .filter(task -> EnumUtils.valueEquals(task.getTaskStatus(), CostTaskStatusEnum.YWC))
                    .map(task -> toBigDecimal(task.getActualLaborCost()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 预计工时
            BigDecimal totalEstimatedHours = projectTasks.stream()
                    .map(CostDeliverTask::getEstimatedHours)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 实际工时
            BigDecimal totalActualHours = projectTasks.stream()
                    .filter(task -> EnumUtils.valueEquals(task.getTaskStatus(), CostTaskStatusEnum.YWC))
                    .map(CostTaskStatServiceImpl::calculateTotalHours)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 设置计算结果
            stat.setBudgetCost(totalBudgetCost);
            stat.setActualLaborCost(totalActualCost);
            stat.setRemainingLaborCosts(totalBudgetCost.subtract(totalActualCost));
            stat.setEstimatedHours(totalEstimatedHours);
            stat.setActualHours(totalActualHours);
        });

    }

    /**
     * 人员工单看板-人员维度售后交付工单统计
     *
     * @param query       查询
     * @param pageRequest 页面请求
     * @return {@link Page }<{@link ? } {@link extends } {@link PersonnelDeliverStatBaseVO }>
     */
    private Page<PersonnelPerDimDeliverStatVO> personnelPerDimDeliverStatPage(TaskStatQueryDTO query, PageRequest pageRequest) {

        getUserDataScope(query);

        if (Boolean.FALSE.equals(query.getIsAll()) && CollUtil.isEmpty(query.getUserAuth())) {
            return pageRequest.of();
        }

        Page<PersonnelPerDimDeliverStatVO> page = pageRequest.of();

        costTaskStatMapper.personnelPerDimDeliverStatPage(page, query);

        personnelPerDimDeliverHandle(page.getRecords(), query);

        return page;

    }

    private void personnelPerDimDeliverHandle(List<PersonnelPerDimDeliverStatVO> records, TaskStatQueryDTO query) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        Set<Long> userIds = CollStreamUtil.toSet(records, PersonnelPerDimDeliverStatVO::getUserId);

        query.setUserIds(userIds);

        List<CostDeliverTask> allTasks = getCostTasks(query);
        //  按用户ID分组
        Map<Long, List<CostDeliverTask>> projectTasksMap = allTasks.stream()
                .collect(Collectors.groupingBy(CostDeliverTask::getManagerId));

        //  计算每个项目的预算成本和实际成本
        records.forEach(stat -> {
            List<CostDeliverTask> projectTasks = projectTasksMap.getOrDefault(stat.getUserId(), Collections.emptyList());

            // 计算预算人工成本总和
            BigDecimal totalBudgetCost = projectTasks.stream()
                    .map(task -> toBigDecimal(task.getBudgetCost()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 计算实际人工成本总和(只计算已完成的工单)
            BigDecimal totalActualCost = projectTasks.stream()
                    .filter(task -> EnumUtils.valueEquals(task.getTaskStatus(), CostTaskStatusEnum.YWC))
                    .map(task -> toBigDecimal(task.getActualLaborCost()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 预计工时
            BigDecimal totalEstimatedHours = projectTasks.stream()
                    .map(CostDeliverTask::getEstimatedHours)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 实际工时
            BigDecimal totalActualHours = projectTasks.stream()
                    .filter(task -> EnumUtils.valueEquals(task.getTaskStatus(), CostTaskStatusEnum.YWC))
                    .map(CostTaskStatServiceImpl::calculateTotalHours)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 赋值计算结果
            stat.setBudgetCost(totalBudgetCost);
            stat.setActualLaborCost(totalActualCost);
            stat.setRemainingLaborCosts(totalBudgetCost.subtract(totalActualCost));
            stat.setEstimatedHours(totalEstimatedHours);
            stat.setActualHours(totalActualHours);
        });
    }

    /**
     * 人员工单看板-工单维度售后交付工单统计
     *
     * @param query       查询
     * @param pageRequest 页面请求
     * @return {@link Page }<{@link ? } {@link extends } {@link PersonnelDeliverStatBaseVO }>
     */
    private Page<PersonnelTaskDimDeliverStatVO> personnelTaskDimDeliverStatPage(TaskStatQueryDTO query, PageRequest pageRequest) {
        Page<Roster> rosterPage = getRosterPage(pageRequest, query);

        List<Roster> userInfoList = rosterPage.getRecords();

        if (CollUtil.isEmpty(userInfoList)) {
            return pageRequest.of();
        }

        Set<Long> userIds = CollStreamUtil.toSet(userInfoList, Roster::getId);

        query.setUserIds(userIds);

        List<PersonnelTaskDimDeliverStatVO> records = costTaskStatMapper.personnelTaskDimDeliverStatList(query);

        personnelTaskDimDeliverStatHandle(records);

        Page<PersonnelTaskDimDeliverStatVO> page = pageRequest.of();

        return page.setTotal(rosterPage.getTotal()).setRecords(records);
    }

    private void personnelTaskDimDeliverStatHandle(List<PersonnelTaskDimDeliverStatVO> records) {
        records.forEach(stat -> {
            stat.setTaskStatusStr(EnumUtils.getNameByValue(CostTaskStatusEnum.class, stat.getTaskStatus()));
            stat.decrypt();
        });
    }


    /**
     * 人员工单看板-多维度售后交付工单统计导出
     *
     * @param query    查询
     * @param response 响应
     */
    @Override
    public synchronized void exportPersonnelDeliverTaskStat(TaskStatQueryDTO query, HttpServletResponse response) {
        ProjectTaskKindEnum taskTypeEnum = ProjectTaskKindEnum.AFTER_SALES_DELIVERY;
        query.setTaskType(taskTypeEnum.getValue());
        CostTaskStatDimensionEnum dimensionEnum = query.getDimensionEnum();
        // 根据维度获取导出的数据
        List<? extends PersonnelDeliverStatBaseVO> resultList = new ArrayList<>();
        switch (dimensionEnum) {
            case PROJECT:
                resultList = personnelProDimDeliverStatList(query);
                break;
            case PERSONNEL:
                resultList = personnelPerDimDeliverStatList(query);
                break;
            case TASK:
                resultList = personnelTaskDimDeliverStatList(query);
                break;
            default:
        }
        exportExcel(response, dimensionEnum, taskTypeEnum, resultList, PERSONNEL_KANBAN);
    }

    /**
     * 人员工单看板-项目维度售后交付工单统计导出
     *
     * @param query 查询
     * @return {@link List }<{@link ? } {@link extends } {@link PersonnelProDimDeliverStatVO }>
     */
    private List<PersonnelProDimDeliverStatVO> personnelProDimDeliverStatList(TaskStatQueryDTO query) {

        List<Roster> userInfoList = getRosterList(query);

        if (CollUtil.isEmpty(userInfoList)) {
            return Collections.emptyList();
        }
        Set<Long> userIds = CollStreamUtil.toSet(userInfoList, Roster::getId);

        query.setUserIds(userIds);

        List<PersonnelProDimDeliverStatVO> records = costTaskStatMapper.personnelProDimDeliverStatList(query);

        personnelProDimDeliverStatHandle(records, query);

        return records;
    }

    /**
     * 人员工单看板-人员维度售后交付工单统计导出
     *
     * @param query 查询
     * @return {@link List }<{@link ? } {@link extends } {@link PersonnelPerDimDeliverStatVO }>
     */
    private List<PersonnelPerDimDeliverStatVO> personnelPerDimDeliverStatList(TaskStatQueryDTO query) {

        getUserDataScope(query);

        if (Boolean.FALSE.equals(query.getIsAll()) && CollUtil.isEmpty(query.getUserAuth())) {
            return Collections.emptyList();
        }

        List<PersonnelPerDimDeliverStatVO> records = costTaskStatMapper.personnelPerDimDeliverStatPage(query);

        personnelPerDimDeliverHandle(records, query);

        return records;
    }

    /**
     * 人员工单看板-工单维度售后交付工单统计导出
     *
     * @param query 查询
     * @return {@link List }<{@link ? } {@link extends } {@link PersonnelTaskDimDeliverStatVO }>
     */
    private List<PersonnelTaskDimDeliverStatVO> personnelTaskDimDeliverStatList(TaskStatQueryDTO query) {
        List<Roster> userInfoList = getRosterList(query);

        if (CollUtil.isEmpty(userInfoList)) {
            return Collections.emptyList();
        }

        Set<Long> userIds = CollStreamUtil.toSet(userInfoList, Roster::getId);

        query.setUserIds(userIds);

        List<PersonnelTaskDimDeliverStatVO> records = costTaskStatMapper.personnelTaskDimDeliverStatList(query);

        personnelTaskDimDeliverStatHandle(records);

        return records;
    }

    /**
     * 人员工单看板-多维度售前支撑工单统计
     *
     * @param pageRequest 页面请求
     * @param query       查询
     * @return {@link Page }<{@link ? } {@link extends } {@link PersonnelPreSaleStatBaseVO }>
     */
    @Override
    public Page<? extends PersonnelPreSaleStatBaseVO> findPersonnelPreSaleTaskStat(PageRequest pageRequest, TaskStatQueryDTO query) {
        query.setTaskType(ProjectTaskKindEnum.PRE_SALES_SUPPORT.getValue());
        CostTaskStatDimensionEnum dimensionEnum = query.getDimensionEnum();
        if (dimensionEnum == null) {
            return pageRequest.of();
        }
        // 根据维度选择不同的查询方法
        switch (dimensionEnum) {
            case PERSONNEL:
                return personnelPerDimPreSaleStatPage(query, pageRequest);
            case PROJECT:
                return personnelProDimPreSaleStatPage(query, pageRequest);
            case TASK:
                return personnelTaskDimPreSaleStatPage(query, pageRequest);
            default:
                return pageRequest.of();
        }
    }

    /**
     * 人员工单看板-项目维度售前支撑工单统计
     *
     * @param query       查询
     * @param pageRequest 页面请求
     * @return {@link Page }<{@link ? } {@link extends } {@link PersonnelProDimPreSaleStatVO }>
     */
    private Page<PersonnelProDimPreSaleStatVO> personnelProDimPreSaleStatPage(TaskStatQueryDTO query, PageRequest pageRequest) {
        Page<Roster> rosterPage = getRosterPage(pageRequest, query);

        List<Roster> userInfoList = rosterPage.getRecords();

        if (CollUtil.isEmpty(userInfoList)) {
            return pageRequest.of();
        }
        Set<Long> userIds = CollStreamUtil.toSet(userInfoList, Roster::getId);

        query.setUserIds(userIds);

        List<PersonnelProDimPreSaleStatVO> records = costTaskStatMapper.personnelProDimPreSaleStatList(query);

        personnelProDimPreSaleStatHandle(records, query);

        Page<PersonnelProDimPreSaleStatVO> page = pageRequest.of();

        return page.setTotal(rosterPage.getTotal()).setRecords(records);
    }

    private void personnelProDimPreSaleStatHandle(List<PersonnelProDimPreSaleStatVO> records, TaskStatQueryDTO query) {
        if (CollUtil.isEmpty(records)) {
            return;
        }

        Set<Long> userIds = CollStreamUtil.toSet(records, PersonnelProDimPreSaleStatVO::getUserId);

        query.setUserIds(userIds);

        List<CostDeliverTask> allTasks = getCostTasks(query);

        Set<Long> taskIds = CollStreamUtil.toSet(allTasks, CostDeliverTask::getId);

        List<CostTaskDailyPaperEntry> paperEntryList = getCostTaskDailyPaperEntries(taskIds);

        // 分组：projectId + userId -> approvalStatus -> List<entry>
        Map<String, Map<ApprovalStatusEnum, List<CostTaskDailyPaperEntry>>> groupedEntries = paperEntryList.stream()
                .peek(CostTaskDailyPaperEntry::decrypt)
                .collect(Collectors.groupingBy(e -> buildPerProKey(e.getProjectId(), e.getUserId()),
                        Collectors.groupingBy(entry -> EnumUtils.getEnumByValue(ApprovalStatusEnum.class, entry.getApprovalStatus()))
                ));

        records.forEach(stat -> {
            String key = buildPerProKey(stat.getProjectId(), stat.getUserId());
            Map<ApprovalStatusEnum, List<CostTaskDailyPaperEntry>> entriesMap = groupedEntries.getOrDefault(key, Collections.emptyMap());
            sumPerDailyPaperEntry(stat, entriesMap);
        });
    }

    /**
     * 人员工单看板-人员维度售前支撑工单统计
     *
     * @param query       查询
     * @param pageRequest 页面请求
     * @return {@link Page }<{@link ? } {@link extends } {@link PersonnelPreSaleStatBaseVO }>
     */
    private Page<PersonnelPerDimPreSaleStatVO> personnelPerDimPreSaleStatPage(TaskStatQueryDTO query, PageRequest pageRequest) {

        getUserDataScope(query);

        if (Boolean.FALSE.equals(query.getIsAll()) && CollUtil.isEmpty(query.getUserAuth())) {
            return pageRequest.of();
        }

        Page<PersonnelPerDimPreSaleStatVO> page = pageRequest.of();

        costTaskStatMapper.personnelPerDimPreSaleStatPage(page, query);

        personnelPerDimPreSaleStatHandle(query, page.getRecords());

        return page;
    }

    private void personnelPerDimPreSaleStatHandle(TaskStatQueryDTO query, List<PersonnelPerDimPreSaleStatVO> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }

        Set<Long> userIds = CollStreamUtil.toSet(records, PersonnelPerDimPreSaleStatVO::getUserId);

        query.setUserIds(userIds);

        List<CostDeliverTask> costTasks = getCostTasks(query);

        Set<Long> taskIds = CollStreamUtil.toSet(costTasks, CostDeliverTask::getId);

        // 获取所有待审核和待提交审核的日报条目
        List<CostTaskDailyPaperEntry> paperEntryList = getCostTaskDailyPaperEntries(taskIds);

        // 分组：userId -> approvalStatus -> List<entry>
        Map<Long, Map<ApprovalStatusEnum, List<CostTaskDailyPaperEntry>>> groupedEntries = paperEntryList.stream()
                .peek(CostTaskDailyPaperEntry::decrypt)
                .collect(Collectors.groupingBy(
                        CostTaskDailyPaperEntry::getUserId,
                        Collectors.groupingBy(entry -> EnumUtils.getEnumByValue(ApprovalStatusEnum.class, entry.getApprovalStatus()))
                ));

        records.forEach(stat -> {
            Long userId = stat.getUserId();
            Map<ApprovalStatusEnum, List<CostTaskDailyPaperEntry>> entriesMap = groupedEntries.getOrDefault(userId, Collections.emptyMap());
            sumPerDailyPaperEntry(stat, entriesMap);
        });
    }


    /**
     * 人员工单看板-工单维度售前支撑工单统计
     *
     * @param query       查询
     * @param pageRequest 页面请求
     * @return {@link Page }<{@link PersonnelTaskDimPreSaleStatVO }>
     */
    private Page<PersonnelTaskDimPreSaleStatVO> personnelTaskDimPreSaleStatPage(TaskStatQueryDTO query, PageRequest pageRequest) {
        Page<Roster> rosterPage = getRosterPage(pageRequest, query);

        List<Roster> userInfoList = rosterPage.getRecords();

        if (CollUtil.isEmpty(userInfoList)) {
            return pageRequest.of();
        }
        Set<Long> userIds = CollStreamUtil.toSet(userInfoList, Roster::getId);

        query.setUserIds(userIds);

        List<PersonnelTaskDimPreSaleStatVO> records = costTaskStatMapper.personnelTaskDimPreSaleStatList(query);

        personnelTaskDimPreSaleStatHandle(records);

        Page<PersonnelTaskDimPreSaleStatVO> page = pageRequest.of();

        return page.setTotal(rosterPage.getTotal()).setRecords(records);
    }

    private void personnelTaskDimPreSaleStatHandle(List<PersonnelTaskDimPreSaleStatVO> records) {

        Set<Long> taskIds = CollStreamUtil.toSet(records, PersonnelTaskDimPreSaleStatVO::getTaskId);

        // 获取所有待审核和待提交审核的日报条目
        List<CostTaskDailyPaperEntry> paperEntryList = getCostTaskDailyPaperEntries(taskIds);

        // 分组：taskId -> approvalStatus -> List<entry>
        Map<Long, Map<ApprovalStatusEnum, List<CostTaskDailyPaperEntry>>> groupedEntries = paperEntryList.stream()
                .peek(CostTaskDailyPaperEntry::decrypt)
                .collect(Collectors.groupingBy(
                        CostTaskDailyPaperEntry::getTaskId,
                        Collectors.groupingBy(entry -> EnumUtils.getEnumByValue(ApprovalStatusEnum.class, entry.getApprovalStatus()))
                ));

        records.forEach(stat -> {
            Long taskId = stat.getTaskId();
            Map<ApprovalStatusEnum, List<CostTaskDailyPaperEntry>> entriesMap = groupedEntries.getOrDefault(taskId, Collections.emptyMap());
            sumPerDailyPaperEntry(stat, entriesMap);
        });
    }

    /**
     * 人员工单看板-多维度售前支撑工单统计导出
     *
     * @param query    查询
     * @param response 响应
     */
    @Override
    public synchronized void exportPersonnelPreSaleTaskStat(TaskStatQueryDTO query, HttpServletResponse response) {
        ProjectTaskKindEnum taskTypeEnum = ProjectTaskKindEnum.PRE_SALES_SUPPORT;
        query.setTaskType(taskTypeEnum.getValue());
        CostTaskStatDimensionEnum dimensionEnum = query.getDimensionEnum();
        // 根据维度获取导出的数据
        List<? extends PersonnelPreSaleStatBaseVO> resultList = new ArrayList<>();
        switch (dimensionEnum) {
            case PROJECT:
                resultList = personnelProDimPreSaleStatList(query);
                break;
            case PERSONNEL:
                resultList = personnelPerDimPreSaleStatList(query);
                break;
            case TASK:
                resultList = personnelTaskDimPreSaleStatList(query);
                break;
            default:
        }
        exportExcel(response, dimensionEnum, taskTypeEnum, resultList, PERSONNEL_KANBAN);
    }

    /**
     * 人员工单看板-项目维度售前支撑工单统计导出
     *
     * @param query 查询
     * @return {@link List }<{@link ? } {@link extends } {@link PersonnelPreSaleStatBaseVO }>
     */
    private List<PersonnelProDimPreSaleStatVO> personnelProDimPreSaleStatList(TaskStatQueryDTO query) {

        List<Roster> rosterList = getRosterList(query);

        if (CollUtil.isEmpty(rosterList)) {
            return Collections.emptyList();
        }
        Set<Long> userIds = CollStreamUtil.toSet(rosterList, Roster::getId);

        query.setUserIds(userIds);

        List<PersonnelProDimPreSaleStatVO> records = costTaskStatMapper.personnelProDimPreSaleStatList(query);

        personnelProDimPreSaleStatHandle(records, query);

        return records;
    }


    /**
     * 人员工单看板-人员维度售前支撑工单统计导出
     *
     * @param query 查询
     * @return {@link List }<{@link ? } {@link extends } {@link PersonnelPreSaleStatBaseVO }>
     */
    private List<PersonnelPerDimPreSaleStatVO> personnelPerDimPreSaleStatList(TaskStatQueryDTO query) {

        getUserDataScope(query);

        if (Boolean.FALSE.equals(query.getIsAll()) && CollUtil.isEmpty(query.getUserAuth())) {
            return Collections.emptyList();
        }

        List<PersonnelPerDimPreSaleStatVO> records = costTaskStatMapper.personnelPerDimPreSaleStatPage(query);

        personnelPerDimPreSaleStatHandle(query, records);

        return records;
    }


    /**
     * 人员工单看板-工单维度售前支撑工单统计导出
     *
     * @param query 查询
     * @return {@link List }<{@link ? } {@link extends } {@link PersonnelPreSaleStatBaseVO }>
     */
    private List<PersonnelTaskDimPreSaleStatVO> personnelTaskDimPreSaleStatList(TaskStatQueryDTO query) {

        List<Roster> rosterList = getRosterList(query);

        if (CollUtil.isEmpty(rosterList)) {
            return Collections.emptyList();
        }
        Set<Long> userIds = CollStreamUtil.toSet(rosterList, Roster::getId);

        query.setUserIds(userIds);

        List<PersonnelTaskDimPreSaleStatVO> records = costTaskStatMapper.personnelTaskDimPreSaleStatList(query);

        personnelTaskDimPreSaleStatHandle(records);

        return records;
    }

    /**
     * 项目工单看板-项目维度售后交付工单统计
     *
     * @param pageRequest 页面请求
     * @param query       查询
     * @return {@link Page }<{@link ? } {@link extends } {@link ProjectProDimDeliverStatVO }>
     */
    private Page<ProjectProDimDeliverStatVO> projectProDimDeliverStatPage(TaskStatQueryDTO query, PageRequest pageRequest) {

        Page<ProjectProDimDeliverStatVO> page = pageRequest.of();

        setProjectDataScope(query);

        if (!Boolean.TRUE.equals(query.getIsAll()) && CollUtil.isEmpty(query.getProjectAuth())) {
            return page;
        }

        costTaskStatMapper.projectProDimDeliverStatPage(page, query);

        projectProDimDeliverHandle(page.getRecords(), query);

        return page;
    }

    /**
     * 项目工单看板-项目维度售后交付工单统计
     *
     * @param query 查询
     * @return {@link List }<{@link ProjectProDimDeliverStatVO }>
     */
    private List<ProjectProDimDeliverStatVO> projectProDimDeliverStatList(TaskStatQueryDTO query) {

        setProjectDataScope(query);

        if (!Boolean.TRUE.equals(query.getIsAll()) && CollUtil.isEmpty(query.getProjectAuth())) {
            return Collections.emptyList();
        }

        List<ProjectProDimDeliverStatVO> records = costTaskStatMapper.projectProDimDeliverStatPage(query);

        projectProDimDeliverHandle(records, query);

        return records;
    }


    /**
     * 项目工单看板-人员维度售后交付工单统计
     *
     * @param pageRequest 页面请求
     * @param query       查询
     * @return {@link Page }<{@link ? } {@link extends } {@link ProjectPerDimDeliverStatVO }>
     */
    private Page<ProjectPerDimDeliverStatVO> projectPerDimDeliverStatPage(TaskStatQueryDTO query, PageRequest pageRequest) {
        // 按项目分页
        Page<ProjectInfo> projectPage = getProjectInfoPage(pageRequest, query);

        List<ProjectInfo> projectInfoList = projectPage.getRecords();

        if (CollUtil.isEmpty(projectInfoList)) {
            return pageRequest.of();
        }

        Set<Long> projectIds = CollStreamUtil.toSet(projectInfoList, ProjectInfo::getId);

        query.setProjectIds(projectIds);

        List<ProjectPerDimDeliverStatVO> personalStatVOList = costTaskStatMapper.projectPerDimDeliverStatList(query);

        projectPerDimDeliverStatHandle(personalStatVOList, query);

        Page<ProjectPerDimDeliverStatVO> page = pageRequest.of();

        return page.setTotal(projectPage.getTotal()).setRecords(personalStatVOList);
    }

    /**
     * 项目工单看板-人员维度售后交付工单统计
     *
     * @param query 查询
     * @return {@link List }<{@link ? } {@link extends } {@link ProjectPerDimDeliverStatVO }>
     */
    private List<ProjectPerDimDeliverStatVO> projectPerDimDeliverStatList(TaskStatQueryDTO query) {
        List<ProjectInfo> projectInfoList = getProjectInfoList(query);

        if (CollUtil.isEmpty(projectInfoList)) {
            return Collections.emptyList();
        }
        Set<Long> projectIds = CollStreamUtil.toSet(projectInfoList, ProjectInfo::getId);

        query.setProjectIds(projectIds);

        List<ProjectPerDimDeliverStatVO> personalStatVOList = costTaskStatMapper.projectPerDimDeliverStatList(query);

        projectPerDimDeliverStatHandle(personalStatVOList, query);

        return personalStatVOList;
    }

    /**
     * 项目工单看板-工单维度售后交付工单统计
     *
     * @param pageRequest 页面请求
     * @param query       查询
     * @return {@link Page }<{@link ? } {@link extends } {@link ProjectTaskDimDeliverStatVO }>
     */
    private Page<ProjectTaskDimDeliverStatVO> projectTaskDimDeliverStatPage(TaskStatQueryDTO query, PageRequest pageRequest) {
        // 按项目分页
        Page<ProjectInfo> projectPage = getProjectInfoPage(pageRequest, query);

        List<ProjectInfo> projectInfoList = projectPage.getRecords();

        if (CollUtil.isEmpty(projectInfoList)) {
            return pageRequest.of();
        }
        Set<Long> projectIds = CollStreamUtil.toSet(projectInfoList, ProjectInfo::getId);

        query.setProjectIds(projectIds);

        List<ProjectTaskDimDeliverStatVO> taskStatVOList = costTaskStatMapper.projectTaskDimDeliverStatList(query);

        projectTaskDimDeliverStatHandle(taskStatVOList);

        Page<ProjectTaskDimDeliverStatVO> page = pageRequest.of();

        return page.setTotal(projectPage.getTotal()).setRecords(taskStatVOList);
    }

    /**
     * 项目工单看板-工单维度售后交付工单统计
     *
     * @param query 查询
     * @return {@link List }<{@link ? } {@link extends } {@link ProjectTaskDimDeliverStatVO }>
     */
    private List<ProjectTaskDimDeliverStatVO> projectTaskDimDeliverStatList(TaskStatQueryDTO query) {

        List<ProjectInfo> projectInfoList = getProjectInfoList(query);

        if (CollUtil.isEmpty(projectInfoList)) {
            return Collections.emptyList();
        }
        Set<Long> projectIds = CollStreamUtil.toSet(projectInfoList, ProjectInfo::getId);

        query.setProjectIds(projectIds);

        List<ProjectTaskDimDeliverStatVO> taskStatVOList = costTaskStatMapper.projectTaskDimDeliverStatList(query);

        projectTaskDimDeliverStatHandle(taskStatVOList);

        return taskStatVOList;
    }


    private void projectPerDimDeliverStatHandle(List<ProjectPerDimDeliverStatVO> records, TaskStatQueryDTO query) {
        if (CollUtil.isEmpty(records)) {
            return;
        }

        Set<Long> projectIds = CollStreamUtil.toSet(records, ProjectPerDimDeliverStatVO::getProjectId);
        // 查询这些项目的所有工单
        query.setProjectIds(projectIds);

        List<CostDeliverTask> allTasks = getCostTasks(query);

        //  按项目ID+人员id分组
        Map<String, List<CostDeliverTask>> personalTasksMap = allTasks.stream()
                .collect(Collectors.groupingBy(t -> buildPerProKey(t.getProjectId(), t.getManagerId())));

        records.forEach(stat -> {
            String personalStatKey = buildPerProKey(stat.getProjectId(), stat.getUserId());
            List<CostDeliverTask> personalTasks = personalTasksMap.getOrDefault(personalStatKey, Collections.emptyList());

            // 计算预算人工成本总和
            BigDecimal totalBudgetCost = personalTasks.stream()
                    .map(task -> toBigDecimal(task.getBudgetCost()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 计算实际人工成本总和(只计算已完成的工单)
            BigDecimal totalActualCost = personalTasks.stream()
                    .filter(task -> EnumUtils.valueEquals(task.getTaskStatus(), CostTaskStatusEnum.YWC))
                    .map(task -> toBigDecimal(task.getActualLaborCost()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 预计工时
            BigDecimal totalEstimatedHours = personalTasks.stream()
                    .map(CostDeliverTask::getEstimatedHours)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 实际工时
            BigDecimal totalActualHours = personalTasks.stream()
                    .filter(task -> EnumUtils.valueEquals(task.getTaskStatus(), CostTaskStatusEnum.YWC))
                    .map(CostTaskStatServiceImpl::calculateTotalHours)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 设置计算结果
            stat.setBudgetCost(totalBudgetCost);
            stat.setActualLaborCost(totalActualCost);
            stat.setRemainingLaborCosts(totalBudgetCost.subtract(totalActualCost));
            stat.setEstimatedHours(totalEstimatedHours);
            stat.setActualHours(totalActualHours);
        });

    }


    private void projectTaskDimDeliverStatHandle(List<ProjectTaskDimDeliverStatVO> taskStatVOList) {
        taskStatVOList.forEach(s -> {
            s.setTaskStatusStr(EnumUtils.getNameByValue(CostTaskStatusEnum.class, s.getTaskStatus()));
            s.decrypt();
        });
    }

    /**
     * 获取项目信息分页
     *
     * @param pageRequest 页面请求
     * @param query       query
     * @return {@link Page }<{@link ProjectInfo }>
     */
    private Page<ProjectInfo> getProjectInfoPage(PageRequest pageRequest, TaskStatQueryDTO query) {

        setProjectDataScope(query);

        if (!Boolean.TRUE.equals(query.getIsAll()) && CollUtil.isEmpty(query.getProjectAuth())) {
            return pageRequest.of();
        }
        Page<ProjectInfo> projectPage = pageRequest.of();

        costTaskStatMapper.projectPage(projectPage, query);

        return projectPage;
    }

    /**
     * 获取项目信息分页
     *
     * @return {@link Page }<{@link ProjectInfo }>
     */
    private List<ProjectInfo> getProjectInfoList(TaskStatQueryDTO query) {

        setProjectDataScope(query);

        if (!Boolean.TRUE.equals(query.getIsAll()) && CollUtil.isEmpty(query.getProjectAuth())) {
            return Collections.emptyList();
        }

        return costTaskStatMapper.projectPage(query);
    }


    private void projectProDimDeliverHandle(List<ProjectProDimDeliverStatVO> records, TaskStatQueryDTO query) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        // 获取所有项目ID
        Set<Long> projectIds = CollStreamUtil.toSet(records, ProjectProDimDeliverStatVO::getProjectId);

        // 查询这些项目的所有工单
        query.setProjectIds(projectIds);
        List<CostDeliverTask> allTasks = getCostTasks(query);

        //  按项目ID分组
        Map<Long, List<CostDeliverTask>> projectTasksMap = allTasks.stream()
                .collect(Collectors.groupingBy(CostDeliverTask::getProjectId));

        //  计算每个项目的预算成本和实际成本
        records.forEach(stat -> {
            List<CostDeliverTask> projectTasks = projectTasksMap.getOrDefault(stat.getProjectId(), Collections.emptyList());

            // 计算预算人工成本总和
            BigDecimal totalBudgetCost = projectTasks.stream()
                    .map(task -> toBigDecimal(task.getBudgetCost()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 计算实际人工成本总和(只计算已完成的工单)
            BigDecimal totalActualCost = projectTasks.stream()
                    .filter(task -> EnumUtils.valueEquals(task.getTaskStatus(), CostTaskStatusEnum.YWC))
                    .map(task -> toBigDecimal(task.getActualLaborCost()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 预计工时
            BigDecimal totalEstimatedHours = projectTasks.stream()
                    .map(CostDeliverTask::getEstimatedHours)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 实际工时
            BigDecimal totalActualHours = projectTasks.stream()
                    .filter(task -> EnumUtils.valueEquals(task.getTaskStatus(), CostTaskStatusEnum.YWC))
                    .map(CostTaskStatServiceImpl::calculateTotalHours)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 设置计算结果
            stat.setBudgetCost(totalBudgetCost);
            stat.setActualLaborCost(totalActualCost);
            stat.setRemainingLaborCosts(totalBudgetCost.subtract(totalActualCost));
            stat.setEstimatedHours(totalEstimatedHours);
            stat.setActualHours(totalActualHours);
        });
    }


    private Page<ProjectProDimPreSaleStatVO> projectProDimPreSaleStatPage(TaskStatQueryDTO query, PageRequest pageRequest) {

        setProjectDataScope(query);

        if (!Boolean.TRUE.equals(query.getIsAll()) && CollUtil.isEmpty(query.getProjectAuth())) {
            return pageRequest.of();
        }

        Page<ProjectProDimPreSaleStatVO> page = pageRequest.of();

        costTaskStatMapper.projectProDimPreSaleStatPage(page, query);

        projectProDimPreSaleStatHandle(page.getRecords(), query);

        return page;
    }

    List<ProjectProDimPreSaleStatVO> projectProDimPreSaleStatList(TaskStatQueryDTO query) {

        setProjectDataScope(query);

        if (!Boolean.TRUE.equals(query.getIsAll()) && CollUtil.isEmpty(query.getProjectAuth())) {
            return Collections.emptyList();
        }

        List<ProjectProDimPreSaleStatVO> records = costTaskStatMapper.projectProDimPreSaleStatPage(query);

        projectProDimPreSaleStatHandle(records, query);

        return records;
    }


    private Page<ProjectPerDimPreSaleStatVO> projectPerDimPreSaleStatPage(TaskStatQueryDTO query, PageRequest pageRequest) {
        // 按项目分页
        Page<ProjectInfo> projectPage = getProjectInfoPage(pageRequest, query);

        List<ProjectInfo> projectInfoList = projectPage.getRecords();

        if (CollUtil.isEmpty(projectInfoList)) {
            return pageRequest.of();
        }
        Set<Long> projectIds = CollStreamUtil.toSet(projectInfoList, ProjectInfo::getId);

        query.setProjectIds(projectIds);

        List<ProjectPerDimPreSaleStatVO> personalStatVOList = costTaskStatMapper.projectPerDimPreSaleStatList(query);

        projectPerDimPreSaleStatHandle(personalStatVOList, query, projectIds);

        Page<ProjectPerDimPreSaleStatVO> page = pageRequest.of();

        return page.setTotal(projectPage.getTotal()).setRecords(personalStatVOList);
    }

    private List<ProjectPerDimPreSaleStatVO> projectPerDimPreSaleStatList(TaskStatQueryDTO query) {

        List<ProjectInfo> projectInfoList = getProjectInfoList(query);

        if (CollUtil.isEmpty(projectInfoList)) {
            return Collections.emptyList();
        }
        Set<Long> projectIds = CollStreamUtil.toSet(projectInfoList, ProjectInfo::getId);

        query.setProjectIds(projectIds);

        List<ProjectPerDimPreSaleStatVO> personalStatVOList = costTaskStatMapper.projectPerDimPreSaleStatList(query);

        projectPerDimPreSaleStatHandle(personalStatVOList, query, projectIds);

        return personalStatVOList;
    }

    private void projectPerDimPreSaleStatHandle(List<ProjectPerDimPreSaleStatVO> records, TaskStatQueryDTO query, Set<Long> projectIds) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        query.setProjectIds(projectIds);

        List<CostDeliverTask> costDeliverTasks = getCostTasks(query);

        Set<Long> taskIds = CollStreamUtil.toSet(costDeliverTasks, CostDeliverTask::getId);

        // 获取所有待审核和待提交审核的日报条目
        List<CostTaskDailyPaperEntry> entries = getCostTaskDailyPaperEntries(taskIds);

        // 分组：personalStatKey(项目id+人员id) -> approvalStatus(类型) -> List<entry> (条目)
        Map<String, Map<ApprovalStatusEnum, List<CostTaskDailyPaperEntry>>> personalEntriesMap = entries.stream()
                .peek(CostTaskDailyPaperEntry::decrypt)
                .collect(Collectors.groupingBy(e -> buildPerProKey(e.getProjectId(), e.getUserId()),
                        Collectors.groupingBy(entry ->
                                EnumUtils.getEnumByValue(ApprovalStatusEnum.class, entry.getApprovalStatus()))));

        records.forEach(stat -> {
            String personalStatKey = buildPerProKey(stat.getProjectId(), stat.getUserId());

            Map<ApprovalStatusEnum, List<CostTaskDailyPaperEntry>> projectEntries = personalEntriesMap.getOrDefault(personalStatKey, Collections.emptyMap());

            sumProDailyPaperEntry(stat, projectEntries);
        });

    }


    private Page<ProjectTaskDimPreSaleStatVO> projectTaskDimPreSaleStatPage(TaskStatQueryDTO query, PageRequest pageRequest) {
        // 按项目分页
        Page<ProjectInfo> projectPage = getProjectInfoPage(pageRequest, query);

        List<ProjectInfo> projectInfoList = projectPage.getRecords();

        if (CollUtil.isEmpty(projectInfoList)) {
            return pageRequest.of();
        }
        Set<Long> projectIds = CollStreamUtil.toSet(projectInfoList, ProjectInfo::getId);

        query.setProjectIds(projectIds);

        List<ProjectTaskDimPreSaleStatVO> taskStatVOList = costTaskStatMapper.projectTaskDimPreSaleStatList(query);

        projectTaskDimPreSaleStatHandle(taskStatVOList);

        Page<ProjectTaskDimPreSaleStatVO> page = pageRequest.of();

        return page.setTotal(projectPage.getTotal()).setRecords(taskStatVOList);
    }


    private List<ProjectTaskDimPreSaleStatVO> projectTaskDimPreSaleStatList(TaskStatQueryDTO query) {

        List<ProjectInfo> projectInfoList = getProjectInfoList(query);

        if (CollUtil.isEmpty(projectInfoList)) {
            return Collections.emptyList();
        }
        Set<Long> projectIds = CollStreamUtil.toSet(projectInfoList, ProjectInfo::getId);

        query.setProjectIds(projectIds);

        List<ProjectTaskDimPreSaleStatVO> taskStatVOList = costTaskStatMapper.projectTaskDimPreSaleStatList(query);

        projectTaskDimPreSaleStatHandle(taskStatVOList);

        return taskStatVOList;
    }

    private void projectTaskDimPreSaleStatHandle(List<ProjectTaskDimPreSaleStatVO> taskStatVOList) {

        Set<Long> taskIds = CollStreamUtil.toSet(taskStatVOList, ProjectTaskDimPreSaleStatVO::getTaskId);

        // 获取所有待审核和待提交审核的日报条目
        List<CostTaskDailyPaperEntry> paperEntryList = getCostTaskDailyPaperEntries(taskIds);

        Map<Long, Map<ApprovalStatusEnum, List<CostTaskDailyPaperEntry>>> entriesStatusMap = paperEntryList.stream()
                .peek(CostTaskDailyPaperEntry::decrypt)
                .collect(Collectors.groupingBy(
                        CostTaskDailyPaperEntry::getTaskId,
                        Collectors.groupingBy(entry -> EnumUtils.getEnumByValue(ApprovalStatusEnum.class, entry.getApprovalStatus()))
                ));

        taskStatVOList.forEach(stat -> {
            Map<ApprovalStatusEnum, List<CostTaskDailyPaperEntry>> entriesMap = entriesStatusMap.getOrDefault(stat.getTaskId(), Collections.emptyMap());
            sumProDailyPaperEntry(stat, entriesMap);
        });
    }


    /**
     * 处理售前工单的工时和成本数据
     */
    private void projectProDimPreSaleStatHandle(List<ProjectProDimPreSaleStatVO> voList, TaskStatQueryDTO query) {
        if (CollUtil.isEmpty(voList)) {
            return;
        }

        Set<Long> projectIds = CollStreamUtil.toSet(voList, ProjectProDimPreSaleStatVO::getProjectId);

        query.setProjectIds(projectIds);

        List<CostDeliverTask> costDeliverTasks = getCostTasks(query);

        Set<Long> taskIds = CollStreamUtil.toSet(costDeliverTasks, CostDeliverTask::getId);

        List<CostTaskDailyPaperEntry> paperEntryList = getCostTaskDailyPaperEntries(taskIds);

        // 分组：projectId -> approvalStatus -> List<entry>
        Map<Long, Map<ApprovalStatusEnum, List<CostTaskDailyPaperEntry>>> groupedEntries = paperEntryList.stream()
                .peek(CostTaskDailyPaperEntry::decrypt)
                .collect(Collectors.groupingBy(
                        CostTaskDailyPaperEntry::getProjectId,
                        Collectors.groupingBy(entry -> EnumUtils.getEnumByValue(ApprovalStatusEnum.class, entry.getApprovalStatus()))
                ));

        voList.forEach(stat -> {
            Long projectId = stat.getProjectId();
            Map<ApprovalStatusEnum, List<CostTaskDailyPaperEntry>> projectEntries = groupedEntries.getOrDefault(projectId, Collections.emptyMap());
            sumProDailyPaperEntry(stat, projectEntries);
        });
    }

    private List<CostTaskDailyPaperEntry> getCostTaskDailyPaperEntries(Collection<Long> taskIds) {
        if (CollUtil.isEmpty(taskIds)) {
            return Collections.emptyList();
        }
        // 获取所有待审核和待提交审核的日报条目
        return costTaskDailyPaperEntryService
                .lambdaQuery()
                .in(CostTaskDailyPaperEntry::getTaskId, taskIds)
                .in(CostTaskDailyPaperEntry::getApprovalStatus, ApprovalStatusEnum.DSH.getValue(), ApprovalStatusEnum.YTG.getValue())
                .list();
    }

    private List<CostDeliverTask> getCostTasks(TaskStatQueryDTO query) {

        if (Boolean.FALSE.equals(query.getIsAll()) && CollUtil.isEmpty(query.getProjectAuth()) && CollUtil.isEmpty(query.getUserAuth())) {
            return Collections.emptyList();
        }

        if (CollUtil.isEmpty(query.getProjectIds()) && CollUtil.isEmpty(query.getUserIds())) {
            return Collections.emptyList();
        }
        List<CostDeliverTask> costTasks = costTaskStatMapper.getCostTasks(query);

        costTasks.forEach(CostDeliverTask::decrypt);

        return costTasks;
    }

    /**
     * 设置项目数据范围
     *
     * @param query 查询
     */
    private void setProjectDataScope(TaskStatQueryDTO query) {

        Map<String, Object> filter = new HashMap<>(2);

        List<Long> projectIdsAvailable = pmsRetriever.getProjectIdScope(filter,false);

        Object scope = filter.get(PmsRetriever.SCOPE);

        query.setIsAll(PmsRetriever.ALL.equals(scope));

        query.setProjectAuth(projectIdsAvailable);
    }

    /**
     * 统计日报数据
     *
     * @param stat       统计
     * @param entriesMap 条目映射
     */
    private static void sumProDailyPaperEntry(ProjectPreSaleStatBaseVO stat, Map<ApprovalStatusEnum, List<CostTaskDailyPaperEntry>> entriesMap) {
        // 待审核工时
        List<CostTaskDailyPaperEntry> waitReviewList = entriesMap.getOrDefault(ApprovalStatusEnum.DSH, Collections.emptyList());
        stat.setWaitReviewHours(calculateTotalHours(waitReviewList));

        // 已审核工时
        List<CostTaskDailyPaperEntry> approvedList = entriesMap.getOrDefault(ApprovalStatusEnum.YTG, Collections.emptyList());
        stat.setReviewedHours(calculateTotalHours(approvedList));

        // 已审核人工成本
        stat.setReviewedLaborCost(calculateLaborCost(approvedList));
    }

    private static void sumPerDailyPaperEntry(PersonnelPreSaleStatBaseVO stat, Map<ApprovalStatusEnum, List<CostTaskDailyPaperEntry>> entriesMap) {
        // 待审核工时
        List<CostTaskDailyPaperEntry> waitReviewList = entriesMap.getOrDefault(ApprovalStatusEnum.DSH, Collections.emptyList());
        stat.setWaitReviewHours(calculateTotalHours(waitReviewList));

        // 已审核工时
        List<CostTaskDailyPaperEntry> approvedList = entriesMap.getOrDefault(ApprovalStatusEnum.YTG, Collections.emptyList());
        stat.setReviewedHours(calculateTotalHours(approvedList));

        // 已审核人工成本
        stat.setReviewedLaborCost(calculateLaborCost(approvedList));
    }

    /**
     * 计算工时总和
     */
    private static BigDecimal calculateTotalHours(List<CostTaskDailyPaperEntry> entries) {
        if (CollUtil.isEmpty(entries)) {
            return BigDecimal.ZERO;
        }

        return entries.stream()
                .map(entry -> {
                    BigDecimal total = BigDecimal.ZERO;
                    if (entry.getNormalHours() != null) {
                        total = total.add(entry.getNormalHours());
                    }
                    if (entry.getAddedHours() != null) {
                        total = total.add(entry.getAddedHours());
                    }
                    return total;
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private static BigDecimal calculateLaborCost(List<CostTaskDailyPaperEntry> entries) {
        return entries.stream()
                .map(entry -> toBigDecimal(entry.getActualLaborCost()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public static BigDecimal calculateTotalHours(CostDeliverTask task) {
        BigDecimal totalHours = BigDecimal.ZERO;
        if (task.getNormalHours() != null) {
            totalHours = totalHours.add(task.getNormalHours());
        }
        if (task.getWorkOvertimeHours() != null) {
            totalHours = totalHours.add(task.getWorkOvertimeHours());
        }
        if (task.getRestOvertimeHours() != null) {
            totalHours = totalHours.add(task.getRestOvertimeHours());
        }
        if (task.getHolidayOvertimeHours() != null) {
            totalHours = totalHours.add(task.getHolidayOvertimeHours());
        }
        return totalHours;
    }


    private static String buildPerProKey(Long projectId, Long userId) {
        return projectId + StringPool.DASH + userId;
    }


    private static BigDecimal toBigDecimal(String str) {
        if (StringUtils.isBlank(str)) {
            return BigDecimal.ZERO;
        }
        try {
            return new BigDecimal(str);
        } catch (Exception e) {
            return BigDecimal.ZERO;
        }
    }

    private static void exportExcel(HttpServletResponse response, CostTaskStatDimensionEnum dimensionEnum,
                                    ProjectTaskKindEnum taskTypeEnum, List<?> resultList, String kanBanType) {
        Class<?> headClazz;
        if (PROJECT_KANBAN.equals(kanBanType)) {
            headClazz = dimensionEnum.getProKanbanVoClazz(taskTypeEnum);
        } else {
            headClazz = dimensionEnum.getPerKanbanVoClazz(taskTypeEnum);
        }
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding(CharEncoding.UTF_8);
        String timestampStr = LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMMddHHmm");
        try {
            String fileName = URLEncoder.encode("工单数据看板-" + timestampStr, "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream())
                    .head(headClazz)
                    .inMemory(false)
                    .registerWriteHandler(new MergeCellStrategyHandler(true, 1, dimensionEnum.getMergeColumnIndex()))
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .sheet(String.format("%s-%s-%s", kanBanType, dimensionEnum.getName(), taskTypeEnum.getName()))
                    .doWrite(resultList);
        } catch (Exception e) {
            log.error("{}-{}-{}工单统计导出失败~", kanBanType, dimensionEnum.getName(), taskTypeEnum.getName(), e);
        }
    }
}
