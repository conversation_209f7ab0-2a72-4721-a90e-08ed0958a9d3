package com.gok.pboot.pms.entity.vo;

import com.gok.pboot.pms.enumeration.ProjectTaskKindEnum;
import lombok.*;

import java.util.List;

/**
 * 日报条目中的任务对象VO
 *
 * <AUTHOR>
 * @date 2024/01/24
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@ToString
public class TaskInDailyPaperEntryUnifyVO {

    /**
     * ID
     */
    private Long id;

    /**
     * 名称
     */
    private String taskName;

    /**
     * 任务类型
     * {@link ProjectTaskKindEnum}
     */
    private Integer taskKind;

    /**
     * 任务类型txt
     */
    private String taskKindTxt;

    /**
     * 工时审核人姓名列表
     */
    private List<String> auditorNames;

    /**
     * 任务完成/结束标识
     */
    private Boolean taskFinishFlag;

}
