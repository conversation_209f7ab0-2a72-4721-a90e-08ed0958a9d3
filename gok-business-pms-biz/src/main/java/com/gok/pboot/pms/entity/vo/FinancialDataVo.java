package com.gok.pboot.pms.entity.vo;

import com.gok.pboot.pms.Util.DecimalFormatUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.Optional;

/**
 * 财务数据VO
 *
 * <AUTHOR>
 * @date 2023/08/22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class FinancialDataVo {

    /**
     * id
     */
    private Long id;

    /**
     * 项目名称
     */
    private String itemName;

    /**
     * 预算总收入（含税）
     */
    private String totalBudgetRevenueIncludeTax;

    /**
     * 预算总收入（不含税）
     */
    private String totalBudgetRevenue;

    /**
     * 预算总成本（含税）
     */
    private String totalBudgetCostIncludeTax;

    /**
     * 预算总成本（不含税）
     */
    private String totalBudgetCost;

    /**
     * 预算毛利
     */
    private String budgetMargin;

    /**
     * 预算毛利率
     */
    private String budgetGrossMargin;

    /**
     * 预估总人天
     */
    private String estimatedTotalManDays;

    /**
     * 主营成本-直接人工预算
     */
    private String mainCostHumanBudget;

    /**
     * 主营成本-外采预算(含税)
     */
    private String mainCostExternalBudgetIncludeTax;

    /**
     * 主营成本-外采预算(不含税)
     */
    private String mainCostExternalBudget;

    /**
     * 管理费用预算（不含税）
     */
    private String managementCostBudget;

    /**
     * 销售费用预算（不含税）
     */
    private String salesCostBudget;

    /**
     * 研发支出预算（不含税）
     */
    private String developmentPaymentBudget;

    /**
     * 预算变更次数
     */
    private Long budgetAlterFrequency;

    /**
     * 实际总成本(含税)
     */
    private String ljzcb;

    /**
     * 实际总成本(不含税)
     */
    private String sjzcbbhs;

    /**
     * 计划总成本
     */
    private String totalScheduleCost;

    /**
     * 劳动力成本计划
     */
    private String laborCostSchedule;

    /**
     * 劳动力投入总成本
     */
    private String totalLaborInputCost;

    /**
     * 主要业务直接人工成本
     */
    private String mainBusinessCostDirectLabor;

    /**
     * 学生复用成本
     */
    private String studentReuseCost;

    /**
     * sjwbcg税排除在外
     */
    private String sjwbcgTaxExcluded;

    /**
     * 管理成本
     */
    private String managementCost;

    /**
     * 销售成本
     */
    private String salesCost;

    /**
     * 研发支出
     */
    private String developmentPayment;

    /**
     * 收入总和（含税）
     */
    private String incomeSumHs;

    /**
     * 收入总和
     */
    private String incomeSum;

    /**
     * 发票金额
     */
    private String invoicingSum;

    /**
     * 偿还金额(汇款累计/回款(累计))
     */
    private String payBackSum;

    /**
     * 收入利润(累计)
     */
    private String ljsrlr;

    /**
     * 收入利润率(累计)
     */
    private String ljsrlrl;

    /**
     * 项目实际毛利
     */
    private String xmsjml;

    /**
     * 学员复用人天占比
     */
    private String studentRatio;

    /**
     * 已确认产值待确认收入
     */
    private String forIncome;

    /**
     * 已确认收入待确认回款
     */
    private String forReturn;

    /**
     * 项目净现金流
     */
    private String projectNetCashFlow;

    /**
     * 售前人工投入
     */
    private String preSalesLaborInput;

    /**
     * 售前费用投入
     */
    private String sqfytr;

    /**
     * 内部项目预算总成本
     */
    private String internalTotalBudgetCostIncludeTax;

    /**
     * 内部项目预估总人天
     */
    private String internalEstimatedTotalManDays;

    /**
     * 内部项目人工预算
     */
    private String internalLaborBudget;

    /**
     * 内部项目外采预算(含税)
     */
    private String internalOutsourcingBudgetTax;

    /**
     * 内部项目预算变更次数
     */
    private Long internalBudgetChangesNum;

    public void setScale(int newScale, RoundingMode roundingMode, DecimalFormat decimalFormat) {
        this.totalBudgetRevenueIncludeTax = DecimalFormatUtil.setAndValidate(totalBudgetRevenueIncludeTax, newScale, roundingMode, decimalFormat);
        this.totalBudgetRevenue = DecimalFormatUtil.setAndValidate(totalBudgetRevenue, newScale, roundingMode, decimalFormat);
        this.totalBudgetCostIncludeTax = DecimalFormatUtil.setAndValidate(totalBudgetCostIncludeTax, newScale, roundingMode, decimalFormat);
        this.totalBudgetCost = DecimalFormatUtil.setAndValidate(totalBudgetCost, newScale, roundingMode, decimalFormat);
        this.budgetMargin = DecimalFormatUtil.setAndValidate(budgetMargin, newScale, roundingMode, decimalFormat);
        if (Optional.ofNullable(budgetGrossMargin).isPresent()) {
            this.budgetGrossMargin = new BigDecimal(budgetGrossMargin).multiply(BigDecimal.valueOf(100)).toString();
            this.budgetGrossMargin = DecimalFormatUtil.setAndValidate(budgetGrossMargin, newScale, roundingMode, decimalFormat);
            if (Optional.ofNullable(this.budgetGrossMargin).isPresent()) {
                this.budgetGrossMargin = this.budgetGrossMargin + "%";
            }
        }
        this.mainCostHumanBudget = DecimalFormatUtil.setAndValidate(mainCostHumanBudget, newScale, roundingMode, decimalFormat);
        this.mainCostExternalBudgetIncludeTax = DecimalFormatUtil.setAndValidate(mainCostExternalBudgetIncludeTax, newScale, roundingMode, decimalFormat);
        this.mainCostExternalBudget = DecimalFormatUtil.setAndValidate(mainCostExternalBudget, newScale, roundingMode, decimalFormat);
        this.managementCostBudget = DecimalFormatUtil.setAndValidate(managementCostBudget, newScale, roundingMode, decimalFormat);
        this.salesCostBudget = DecimalFormatUtil.setAndValidate(salesCostBudget, newScale, roundingMode, decimalFormat);
        this.developmentPaymentBudget = DecimalFormatUtil.setAndValidate(developmentPaymentBudget, newScale, roundingMode, decimalFormat);
        this.ljzcb = DecimalFormatUtil.setAndValidate(ljzcb, newScale, roundingMode, decimalFormat);
        this.sjzcbbhs = DecimalFormatUtil.setAndValidate(sjzcbbhs, newScale, roundingMode, decimalFormat);
        this.totalScheduleCost = DecimalFormatUtil.setAndValidate(totalScheduleCost, newScale, roundingMode, decimalFormat);
        this.laborCostSchedule = DecimalFormatUtil.setAndValidate(laborCostSchedule, newScale, roundingMode, decimalFormat);
        this.totalLaborInputCost = DecimalFormatUtil.setAndValidate(totalLaborInputCost, newScale, roundingMode, decimalFormat);
        this.mainBusinessCostDirectLabor = DecimalFormatUtil.setAndValidate(mainBusinessCostDirectLabor, newScale, roundingMode, decimalFormat);
        this.studentReuseCost = DecimalFormatUtil.setAndValidate(studentReuseCost, newScale, roundingMode, decimalFormat);
        this.sjwbcgTaxExcluded = DecimalFormatUtil.setAndValidate(sjwbcgTaxExcluded, newScale, roundingMode, decimalFormat);
        this.managementCost = DecimalFormatUtil.setAndValidate(managementCost, newScale, roundingMode, decimalFormat);
        this.salesCost = DecimalFormatUtil.setAndValidate(salesCost, newScale, roundingMode, decimalFormat);
        this.developmentPayment = DecimalFormatUtil.setAndValidate(developmentPayment, newScale, roundingMode, decimalFormat);
        this.incomeSumHs = DecimalFormatUtil.setAndValidate(incomeSumHs, newScale, roundingMode, decimalFormat);
        this.incomeSum = DecimalFormatUtil.setAndValidate(incomeSum, newScale, roundingMode, decimalFormat);
        this.invoicingSum = DecimalFormatUtil.setAndValidate(invoicingSum, newScale, roundingMode, decimalFormat);
        this.payBackSum = DecimalFormatUtil.setAndValidate(payBackSum, newScale, roundingMode, decimalFormat);
        this.ljsrlr = DecimalFormatUtil.setAndValidate(ljsrlr, newScale, roundingMode, decimalFormat);
        this.ljsrlrl = DecimalFormatUtil.setAndValidate(ljsrlrl, newScale, roundingMode, decimalFormat);
        this.xmsjml = DecimalFormatUtil.setAndValidate(xmsjml, newScale, roundingMode, decimalFormat);
        if (Optional.ofNullable(studentRatio).isPresent()) {
            this.studentRatio = new BigDecimal(studentRatio).multiply(BigDecimal.valueOf(100)).toString();
            this.studentRatio = DecimalFormatUtil.setAndValidate(studentRatio, newScale, roundingMode, decimalFormat);
            if (Optional.ofNullable(this.studentRatio).isPresent()) {
                this.studentRatio = this.studentRatio + "%";
            }
        }
        this.forIncome = DecimalFormatUtil.setAndValidate(forIncome, newScale, roundingMode, decimalFormat);
        this.forReturn = DecimalFormatUtil.setAndValidate(forReturn, newScale, roundingMode, decimalFormat);
        this.projectNetCashFlow = DecimalFormatUtil.setAndValidate(projectNetCashFlow, newScale, roundingMode, decimalFormat);
        this.preSalesLaborInput = DecimalFormatUtil.setAndValidate(preSalesLaborInput, newScale, roundingMode, decimalFormat);
        this.sqfytr = DecimalFormatUtil.setAndValidate(sqfytr, newScale, roundingMode, decimalFormat);
        this.internalTotalBudgetCostIncludeTax = DecimalFormatUtil.setAndValidate(internalTotalBudgetCostIncludeTax, newScale, roundingMode, decimalFormat);
        this.internalLaborBudget = DecimalFormatUtil.setAndValidate(internalLaborBudget, newScale, roundingMode, decimalFormat);
        this.internalOutsourcingBudgetTax = DecimalFormatUtil.setAndValidate(internalOutsourcingBudgetTax, newScale, roundingMode, decimalFormat);
    }

}
