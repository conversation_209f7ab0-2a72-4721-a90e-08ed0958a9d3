package com.gok.pboot.pms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.Util.*;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.common.constant.FunctionConstants;
import com.gok.pboot.pms.entity.domain.ProjectInfo;
import com.gok.pboot.pms.entity.domain.ProjectRisk;
import com.gok.pboot.pms.entity.dto.ProjectResponsePlanDTO;
import com.gok.pboot.pms.entity.dto.ProjectRiskDTO;
import com.gok.pboot.pms.entity.vo.ProjectRiskFindPageVO;
import com.gok.pboot.pms.entity.vo.ProjectRiskSpecialVo;
import com.gok.pboot.pms.enumeration.*;
import com.gok.pboot.pms.mapper.ProjectInfoMapper;
import com.gok.pboot.pms.mapper.ProjectMapper;
import com.gok.pboot.pms.mapper.ProjectRiskMapper;
import com.gok.pboot.pms.service.IProjectRiskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 * 项目风险表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-12
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class ProjectRiskServiceImpl extends ServiceImpl<ProjectRiskMapper, ProjectRisk> implements IProjectRiskService {

    private final ProjectMapper projectMapper;

    private final ProjectInfoMapper projectInfoMapper;

    private final BcpLoggerUtils bcpLoggerUtils;

    @Override
    public Page<ProjectRiskFindPageVO> findPage(PageRequest pageRequest, Map<String, Object> filter) {
        // 分页查询项目风险信息
        Page<ProjectRiskFindPageVO> pageResult =
                baseMapper.findListPage(new Page<>(pageRequest.getPageNumber(), pageRequest.getPageSize()), filter);
        if (CollUtil.isEmpty(pageResult.getRecords())) {
            return pageResult;
        }
        // 补全响应字段
        pageResult.getRecords().forEach(r -> {
            r.setInfluenceDegreeTxt(EnumUtils.getNameByValue(InfluenceDegreeEnum.class, r.getInfluenceDegree()));
            r.setLevelTxt(EnumUtils.getNameByValue(RiskLevelEnum.class, r.getLevel()));
            r.setStatusTxt(EnumUtils.getNameByValue(RiskStatusEnum.class, r.getStatus()));
        });
        return pageResult;
    }

    @Override
    public ProjectRisk findById(Long id) {
        if (!Optional.ofNullable(id).isPresent()) {
            return null;
        }
        return baseMapper.selectById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long save(ProjectRiskDTO request) {
        // 赋值实体类
        ProjectRisk entity = ProjectRisk.buildSave(request);

        // 持久化数据库
        baseMapper.insert(entity);
        //日志记录
        bcpLoggerUtils.log(FunctionConstants.PROJECT_DETAILS_PROJECT_RISKS, LogContentEnum.NEW_RISKS,
                request.getDescription());
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long update(ProjectRiskDTO request) {
        Long id = request.getId();
        Assert.isTrue(Optional.ofNullable(id).isPresent(), "主键id不能为空");
        ProjectRisk entity = findById(id);
        if (!Optional.ofNullable(entity).isPresent()) {
            log.warn("id:{}对应数据不存在，操作结束", id);
            return request.getId();
        }
        BeanUtil.copyProperties(request, entity);
        BaseBuildEntityUtil.buildUpdate(entity);

        // 持久化数据库
        baseMapper.updateById(entity);
        //日志记录
        bcpLoggerUtils.log(FunctionConstants.PROJECT_DETAILS_PROJECT_RISKS, LogContentEnum.EDITING_RISKS,
                request.getDescription());
        return id;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApiResult<String> batchDel(List<Long> list) {
        if (CollectionUtils.isEmpty(list)) {
            return ApiResult.failure("请选择删除数据");
        }
        List<ProjectRisk> projectRisks = baseMapper.selectBatchIds(list);

        baseMapper.batchDel(list);
        //日志记录
        if(CollUtil.isNotEmpty(projectRisks)){
            projectRisks.stream().forEach(r->{
                bcpLoggerUtils.log(FunctionConstants.PROJECT_DETAILS_PROJECT_RISKS, LogContentEnum.DELETE_RISK,
                        r.getDescription());
            });
        }

        return ApiResult.success("操作成功");
    }

    @Override
    public ApiResult<Integer> findChargeRiskNum() {
        Long id = SecurityUtils.getUser().getId();
        Integer myRiskNum;
        myRiskNum = baseMapper.findChargeRiskNum(id);
        if (YesOrNoEnum.NO.getValue().equals(myRiskNum)) {
            return ApiResult.success(YesOrNoEnum.NO.getValue());
        }
        return ApiResult.success(myRiskNum);

    }

    @Override
    public Page<ProjectRiskFindPageVO> findPageOfCharge(PageRequest pageRequest, Map<String, Object> filter) {
        //// 负责人id为空时，返回空数据
        //Long chargeUserId = (Long) filter.get("chargeUserId");
        //if (chargeUserId == null) {
        //    return new Page<>();
        //}1
        Long chargeUserId = SecurityUtils.getUser().getId();
        filter.put("chargeUserId", chargeUserId);

        // 根据负责人id分页查询项目风险信息
        List<ProjectInfo> infos;
        Map<Long, String> projectIdNameMap;
        Page<ProjectRisk> pageResult;

        infos = projectInfoMapper.findByProjectNameLike(filter);
        if (infos.isEmpty()){
            return new Page<>();
        }
        filter.put("projectIds", BaseEntityUtils.mapToIdList(infos));
        // 查询风险表
        pageResult = baseMapper.findPage(new Page<>(pageRequest.getPageNumber(),pageRequest.getPageSize()),filter);
        if (CollUtil.isEmpty(pageResult.getRecords())) {
            return new Page<>();
        }
        List<ProjectInfo> infosFilter = infos.stream()
                .filter(projectInfo -> !StringUtils.isEmpty(projectInfo.getItemName()))
                .collect(Collectors.toList());
        projectIdNameMap = BaseEntityUtils.mapCollectionToMap(infosFilter, ProjectInfo::getId, ProjectInfo::getItemName);
        return PageUtils.mapTo(pageResult, po -> ProjectRiskFindPageVO.of(po, projectIdNameMap));
    }

    //@Override
    //public Page<ProjectRiskFindPageVO> findByItemName(PageRequest pageRequest, Map<String, Object> filter) {
    //    // 模糊查询项目表
    //    List<ProjectInfo> infos;
    //    Map<Long, String> projectIdNameMap;
    //    Page<ProjectRisk> pageResult;
    //
    //    infos = projectInfoMapper.findByProjectNameLike(filter);
    //    if (infos.isEmpty()){
    //        return new Page<>();
    //    }
    //    filter.put("projectIds", BaseEntityUtils.mapToIdList(infos));
    //    // 查询风险表
    //    pageResult = baseMapper.findPage(new Page<>(pageRequest.getPageNumber(),pageRequest.getPageSize()),filter);
    //    projectIdNameMap = BaseEntityUtils.mapCollectionToMap(infos, BaseEntity::getId, ProjectInfo::getItemName);
    //
    //    // 返回list11
    //    return PageUtils.mapTo(pageResult, po -> ProjectRiskFindPageVO.of(po, projectIdNameMap));
    //}

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long updateResponsePlan(ProjectResponsePlanDTO request) {
        ProjectRisk projectRisk = new ProjectRisk();
        Long id = request.getId();
        String responsePlan = request.getResponsePlan();

        if (Objects.isNull(id) || findById(id) == null) {
            log.warn("id:{}为空或对应数据不存在，操作结束", id);
            return id;
        }
        projectRisk.setId(id);

        if (responsePlan != null) {
            projectRisk.setResponsePlan(responsePlan);
        }else {
            projectRisk.setResponsePlan("");
        }
        updateById(projectRisk);
        return id;
    }

    @Override
    public ProjectRiskSpecialVo findRiskSpecialById(Long id) {
        // 调用可查询删除项的mapper
        ProjectRisk risk = baseMapper.findRiskSpecialById(id);
        return ProjectRiskSpecialVo.of(risk);
    }


}
