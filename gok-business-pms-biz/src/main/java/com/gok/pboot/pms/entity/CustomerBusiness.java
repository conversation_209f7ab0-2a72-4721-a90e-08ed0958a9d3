package com.gok.pboot.pms.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 客户经营单元(基础概况)
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-12
 */
@Data
@Accessors(chain = true)
@TableName("customer_business")
@ApiModel(value="CustomerBusiness对象", description="客户经营单元(基础概况)")
public class CustomerBusiness extends BeanEntity<Long> {

    private static final long serialVersionUID = 1L;

    /**
     * 经营单元名称
     */
    @ApiModelProperty(name = "name",value = "经营单元名称")
    @TableField("name")
    private String name;

    /**
     * 主营业务
     */
    @ApiModelProperty(name = "overview",value = "主营业务")
    private String overview;

    /**
     * 组织架构附件
     */
    @ApiModelProperty(name = "structureUrl",value = "组织架构附件")
    private String structureUrl;

    /**
     * 分析报告附件
     */
    @ApiModelProperty(name = "reportUrl",value = "分析报告附件")
    private String reportUrl;

    /**
     * 经营计划
     */
    private String plan;
}
