package com.gok.pboot.pms.cost.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 人员级别单价配置 DTO
 *
 * <AUTHOR>
 * @date 2025/01/08
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class CostConfigLevelPriceDTO {

    /**
     * 职务id
     */
    @NotNull(message = "请选择岗位职务")
    private Long jobActivityId;

    /**
     * 人员级别
     */
    @NotNull(message = "人员级别不能为空")
    private Long personnelLevel;


    /**
     * 多地域标准
     */
    @NotBlank(message = "地区不能为空")
    private String region;

//    /**
//     * 人员固定薪资单价（元/天）
//     */
//    @NotNull(message = "人员固定薪资单价（元/天）不能为空")
//    @DecimalMin(value = "0.00", message = "人天单价必须大于或等于0")
//    @DecimalMax(value = "9999.00", message = "人天单价必须小于或等于9999")
//    private BigDecimal levelPrice;

    /**
     * 人员基本薪资单价（元/天）
     */
    @NotNull(message = "人员基本薪资单价（元/天）不能为空")
    @DecimalMin(value = "0.00", message = "人天单价必须大于或等于0")
    @DecimalMax(value = "9999.00", message = "人天单价必须小于或等于9999")
    private BigDecimal baseSalaryPrice;


    /**
     * 工资（元/天）
     */
    @NotNull(message = "工资（元/天）不能为空")
    @DecimalMin(value = "0.00", message = "人天单价必须大于或等于0")
    @DecimalMax(value = "9999.00", message = "人天单价必须小于或等于9999")
    private BigDecimal salaryPerDay;

    /**
     * 社保（元/天）
     */
    @NotNull(message = "社保（元/天）不能为空")
    @DecimalMin(value = "0.00", message = "人天单价必须大于或等于0")
    @DecimalMax(value = "9999.00", message = "人天单价必须小于或等于9999")
    private BigDecimal socialSecurityPerDay;

    /**
     * 公积金（元/天）
     */
    @NotNull(message = "公积金（元/天）不能为空")
    @DecimalMin(value = "0.00", message = "人天单价必须大于或等于0")
    @DecimalMax(value = "9999.00", message = "人天单价必须小于或等于9999")
    private BigDecimal housingFundPerDay;

//    /**
//     * 残保金（元/天）
//     */
//    @NotNull(message = "残保金（元/天）不能为空")
//    @DecimalMin(value = "0.00", message = "人天单价必须大于或等于0")
//    @DecimalMax(value = "9999.00", message = "人天单价必须小于或等于9999")
//    private BigDecimal disabilityLevyPerDay;
}
