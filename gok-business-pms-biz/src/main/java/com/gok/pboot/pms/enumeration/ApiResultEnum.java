package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * date: 16/4/19 上午9:46
 *
 * <AUTHOR>
 * @version 1.0
 */
@AllArgsConstructor
@Getter
public enum ApiResultEnum implements ValueEnum<Integer> {
    /**
     * 成功
     */
    SUCCESS(0, "操作成功"),
    FAILURE(1, "未知服务异常"),
    VALIDATION_FAILURE(1001, "校验不通过"),
    ACL_FAILURE(1002, "用户名或密码不正确"),
    NO_RESPONSE(1003, "无应答"),
    IMPORT_VALID_FAIL(1005, "导入校验不通过"),
    UNAUTHORIZED(1007, "未授权"),
    NO_RECORD(1010, "没有找到相关记录"),
    FORWARD(1011, "校验通过，转发其他接口"),
    SERVICE_EXCEPTION(500, "服务运行异常"),
    UNKNOWN_EXCEPTION(900, "未知服务运行异常"),
    OA_EXCEPTION(11, "同步OA异常");

    private final Integer value;
    private final String name;




}
