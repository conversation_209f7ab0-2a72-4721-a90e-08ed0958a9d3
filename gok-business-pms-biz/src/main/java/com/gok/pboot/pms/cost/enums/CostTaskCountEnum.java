package com.gok.pboot.pms.cost.enums;

import cn.hutool.core.collection.CollUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 工单统计 枚举
 *
 * <AUTHOR>
 * @date 2025/01/15
 */
@AllArgsConstructor
@Getter
public enum CostTaskCountEnum {

    /**
     * 未完成
     */
    UNFINISHED(0, CollUtil.newArrayList(CostTaskStatusEnum.WWC, CostTaskStatusEnum.BTH)),

    /**
     * 待分解
     */
    TO_BE_DISASSEMBLED(1,  CollUtil.newArrayList(CostTaskStatusEnum.DCJ)),

    /**
     * 待审核
     */
    TO_BE_REVIEWED(2, CollUtil.newArrayList(CostTaskStatusEnum.DEJFZRSH, CostTaskStatusEnum.DYJFZRSH, CostTaskStatusEnum.DXMJLSH));

    private final Integer value;

    private final List<CostTaskStatusEnum> statusEnums;

    /**
     * 根据值获取枚举
     *
     * @param statusEnum status 枚举
     * @return {@link List }<{@link CostTaskCountEnum }>
     */
    public static List<CostTaskCountEnum> getTaskCountEnumsByValue(CostTaskStatusEnum statusEnum) {
        if (statusEnum == null) {
            return Collections.emptyList();
        }
        List<CostTaskCountEnum> taskCountEnums = new ArrayList<>(0);
        switch (statusEnum) {
            case WWC:
            case BTH:
                taskCountEnums.add(UNFINISHED);
                break;
            case DCJ:
                taskCountEnums.add(TO_BE_DISASSEMBLED);
                break;
            case DEJFZRSH:
            case DYJFZRSH:
            case DXMJLSH:
            case DCJRSH:
                taskCountEnums.add(TO_BE_REVIEWED);
                break;
            default:
                return Collections.emptyList();
        }
        return taskCountEnums;
    }

}
