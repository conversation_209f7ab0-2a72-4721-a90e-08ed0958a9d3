package com.gok.pboot.pms.cost.controller;


import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.cost.entity.vo.CostManagePersonnelLevelDetailVO;
import com.gok.pboot.pms.cost.service.ICostManagePersonnelLevelDetailService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 成本管理人员级别测算明细 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @menu 成本管理人员级别测算明细
 * @since 2025-01-07
 */
@AllArgsConstructor
@RestController
@RequestMapping("/costManagePersonnelLevelDetail")
public class CostManagePersonnelLevelDetailController {

    private final ICostManagePersonnelLevelDetailService service;

    /**
     * 查询预算明细
     *
     * @param estimationResultsId 估算结果id
     * @return
     */
    @GetMapping("/getByEstimationResultsId/{estimationResultsId}")
    public ApiResult<List<CostManagePersonnelLevelDetailVO>> getByEstimationResultsId(@PathVariable Long estimationResultsId) {
        return ApiResult.success(service.getByEstimationResultsId(estimationResultsId), "查询成功");
    }

}
