package com.gok.pboot.pms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.entity.domain.PmsDictItem;
import com.gok.pboot.pms.entity.domain.PmsDictItemMultiLevel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 字典项(PmsDictItem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-11-21 17:22:43
 */
@Mapper
public interface PmsDictItemMapper extends BaseMapper<PmsDictItem> {

    List<PmsDictItem> findAll();

    List<PmsDictItem> findSubItem();

    List<PmsDictItem> findDictItemByDictType(@Param("dictType") String dictType);

    List<PmsDictItem> findDictMapByTypeList(@Param("list") List<String> singletonList);

    List<PmsDictItemMultiLevel> findDictItemMultiLevelByDictType(@Param("dictType") String dictType);
}