package com.gok.pboot.pms.cost.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.cost.entity.domain.CostConfigVersion;
import com.gok.pboot.pms.cost.entity.vo.VersionHistoryVO;
import com.gok.pboot.pms.cost.enums.CostConfigVersionTypeEnum;

/**
 * <p>
 * 成本配置版本记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
public interface ICostConfigVersionService extends IService<CostConfigVersion> {

    /**
     * 生成版本名称
     *
     * @param versionTypeEnum version 类型 enum
     * @return {@link Long }
     */
    Long generateVersionName(CostConfigVersionTypeEnum versionTypeEnum);

    /**
     * 获取 当前成本配置版本
     *
     * @param versionTypeEnum version 类型 enum
     * @return {@link CostConfigVersion }
     */
    CostConfigVersion getCrrCostConfigVersion(CostConfigVersionTypeEnum versionTypeEnum);

    /**
     * 获取最大版本号
     *
     * @param versionTypeEnum version 类型 enum
     * @return {@link String }
     */
    String getMaxVersionNum(CostConfigVersionTypeEnum versionTypeEnum);

    /**
     * 获取历史版本数据
     *
     * @param pageRequest     页面请求
     * @param versionTypeEnum version 类型 enum
     * @return {@link Page }<{@link VersionHistoryVO }>
     */
    Page<VersionHistoryVO> getHistoryVersions(PageRequest pageRequest, CostConfigVersionTypeEnum versionTypeEnum);
}
