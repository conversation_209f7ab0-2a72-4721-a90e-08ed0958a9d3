package com.gok.pboot.pms.entity.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 销售收款明细表格VO
 * <AUTHOR>
 * @since 2023-10-7
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SalesReceiptDetailsTableVO {

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键id")
    private Long id;

    /**
     * 销售收款主表ID
     */
    @ApiModelProperty(value = "销售收款主表ID")
    private Long mainId;

    /**
     * 款项名称
     */
    @ApiModelProperty(value = "款项名称")
    private String paymentName;

    /**
     * 款项占比(预留字段)
     */
    @ApiModelProperty(value = "款项占比(预留字段)")
    private String proportionFunds;

    /**
     * 款项金额(预留字段)
     */
    @ApiModelProperty(value = "款项金额(预留字段)")
    private String fundsAmount;

    /**
     * 预计收(付)款日期
     */
    @ApiModelProperty(value = "预计收(付)款日期")
    private String expectedDate;

    /**
     * 实际收(付)款日期
     */
    @ApiModelProperty(value = "实际收(付)款日期")
    private String actualDate;

    /**
     * 收(付)款金额（含税）
     */
    @ApiModelProperty(value = "收(付)款金额（含税）")
    private String paymentAmountIncludingTax;

    /**
     * 税率
     */
    @ApiModelProperty(value = "税率")
    private String taxRate;

    /**
     * 收(付)款金额(不含税)
     */
    @ApiModelProperty(value = "收(付)款金额(不含税)")
    private String paymentAmount;

    /**
     * 收(付)款条件（项目进度）
     */
    @ApiModelProperty(value = "收(付)款条件（项目进度）")
    private String projectSchedule;

    /**
     * 收(付)款状态
     */
    @ApiModelProperty(value = "收(付)款状态")
    private Integer paymentStatus;

    /**
     * 收(付)款状态Txt
     */
    private String paymentStatusTxt;

    /**
     * 收(付)款备注
     */
    @ApiModelProperty(value = "收(付)款备注")
    private String paymentRemark;

    /**
     * 发票开具状态
     */
    @ApiModelProperty(value = "发票开具状态")
    private Integer invoiceStatus;

    /**
     * 发票开具状态txt
     */
    private String invoiceStatusTxt;

    /**
     * 实际开票日期
     */
    @ApiModelProperty(value = "实际开票日期")
    private String actualInvoicingDate;


}
