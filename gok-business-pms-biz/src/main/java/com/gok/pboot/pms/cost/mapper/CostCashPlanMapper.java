package com.gok.pboot.pms.cost.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.cost.entity.domain.CostCashPlan;
import com.gok.pboot.pms.cost.entity.vo.CostCashPlanVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CostCashPlanMapper extends BaseMapper<CostCashPlan> {

    /**
     * 获取现金流计划列表
     *
     * @param versionIds 版本ID集合
     * @return 计划列表
     */
    List<CostCashPlanVO> getCostCashPlanList(@Param("versionIds") List<Long> versionIds);

}