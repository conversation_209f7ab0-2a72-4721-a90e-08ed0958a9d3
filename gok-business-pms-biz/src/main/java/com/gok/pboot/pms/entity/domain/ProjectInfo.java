package com.gok.pboot.pms.entity.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BaseEntity;
import com.gok.pboot.pms.entity.vo.ProjectInfoVO;
import com.gok.pboot.pms.entity.vo.ProjectVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 项目信息
 *
 * <AUTHOR>
 * @since 2023-07-14
 */
@Data
@TableName("project_info")
@EqualsAndHashCode(callSuper = true)
public class ProjectInfo extends BaseEntity<Long> {

    /**
     * 项目ID
     */
    private Long id;

    /**
     * 项目编号
     */
    private String itemNo;

    /**
     * 项目名称
     */
    private String itemName;

    /**
     * 项目状态
     */
    private String projectStatus;

    /**
     * 经营单元ID
     */
    private Long businessId;

    /**
     * 经营单元名称
     */
    @TableField(exist = false)
    private String businessUnitName;

    /**
     * 所属客户ID
     */
    private Long unitId;

    /**
     * 所属客户名称
     */
    @TableField(exist = false)
    private String unitName;

    /**
     * 项目所在地
     */
    private String projectLocation;

    /**
     * 业务归属一级部门id
     */
    private Long firstLevelDepartmentId;

    /**
     * 业务归属一级部门
     */
    private String firstLevelDepartment;

    /**
     * 项目交付部门id
     */
    private Long proDeliveryDepartmentId;

    /**
     * 项目交付部门
     */
    private String proDeliveryDepartment;

    /**
     * 是否内部项目
     */
    private Long isNotInternalProject;

    /**
     * 资源通道
     */
    private String resourceChannel;

    /**
     * 是否需要招投标,0:是 1:否
     */
    private Integer sfzjqht;

    /**
     * 是否签订合同
     */
    private String signContract;

    /**
     * 立项日期
     */
    private String projectDate;

    /**
     * 项目归档时间（结束日期）
     */
    private LocalDate projectFilingTime;

    /**
     * 预估毛利率
     */
    private String ygmll;

    /**
     * 项目整包预算
     */
    private BigDecimal proPackageBudget;

    /**
     * 预计签单金额
     */
    private BigDecimal expectedOrderAmount;

    /**
     * 预计签单时间（预计完成时间）
     */
    private String expectedCompleteTime;

    /**
     * 商机报备转在建时间点
     */
    private String businessToProjectTime;

    /**
     * 签单金额评判依据
     */
    private String qdjeppyj;

    /**
     * 业务方向
     */
    private String businessDirection;

    /**
     * 成熟度
     */
    private Long maturity;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 技术领域
     */
    private String technologyField;

    /**
     * 项目背景及建设内容（项目目标及建设范围）
     */
    private String proConstructionScope;

    /**
     * 我司建设内容
     */
    private String wsjsnr;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 客户联系人
     */
    private String customerContact;

    /**
     * 客户联系方式
     */
    private String customerPhone;

    /**
     * 最终客户行业
     */
    private String endCustomerIndustry;

    /**
     * 项目销售人员ID(客户经理ID)
     */
    private Long salesmanUserId;

    /**
     * 项目销售人员(客户经理)
     */
    private String projectSalesperson;

    /**
     * 售前人员ID(售前经理id)
     */
    private Long preSaleUserId;

    /**
     * 售前人员姓名(售前经理)
     */
    private String preSaleUserName;

    /**
     * 项目经理人员ID
     */
    private Long managerUserId;

    /**
     * 项目经理人员姓名
     */
    private String managerUserName;

    /**
     * 项目区域主管ID
     */
    private Long regionUserId;

    /**
     * 项目区域主管姓名
     */
    private String regionUserName;

    /**
     * 项目市场总监ID
     */
    private Long directorUserId;

    /**
     * 项目市场总监姓名
     */
    private String directorUserName;

    /**
     * 项目阶段
     */
    private String projectStage;

    /**
     * 项目把握度
     */
    private String projectGraspDegree;

    /**
     * 招标方式
     */
    private String zbfs;

    /**
     * 国科优势分析
     */
    private String gkysfx;

    /**
     * 国科劣势分析
     */
    private String gklsfx;

    /**
     * 项目当前进展
     */
    private String currentProgress;

    /**
     * 遇到的问题及所需支持
     */
    private String yddwtjsxdzc;

    /**
     * 下一步推进计划
     */
    private String nextStepForwardPlan;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDate ctime;

    /**
     * 初始化默认任务标识（0未创建，1已创建）
     */
    private Integer initTaskFlag;

    /**
     * 任务自动结束标识（0未结束·过，1已结束过）
     */
    private Integer autoFinishFlag;

    /**
     * 项目类型
     */
    private Integer projectType;

    /**
     * 项目归属部门id
     */
    private Long projectDepartmentId;

    /**
     * 项目归属部门
     */
    private String projectDepartment;

    /**
     * 业务归属部门id
     */
    private Long businessDepartmentId;

    /**
     * 业务归属部门
     */
    private String businessDepartment;

    /**
     * 业务经理id
     */
    private Long businessManagerId;

    /**
     * 业务经理
     */
    private String businessManager;

    /**
     * 预估成本
     */
    private BigDecimal estimatedCost;

    /**
     * 业务背景和现状
     */
    private String businessBackgroundAndSituation;

    /**
     * 业务需求
     */
    private String businessRequirements;

    /**
     * 业务目标
     */
    private String businessObjectives;

    /**
     * 进度要求
     */
    private String progressRequirements;

    /**
     * OA项目业务需求文档文件id
     */
    private String projectBusinessRequirementsOafileid;

    /**
     * 'OA项目业务需求文档文件流程'
     */
    private Long projectBusinessRequirementsOafileidRequestid;

    /**
     * OA项目业务需求文档文件流程操作人id
     */
    private Long projectBusinessRequirementsOafileidNodeoperator;

    /**
     * 签约客户ID
     */
    private Long contractCustomerId;

    /**
     * 签约客户
     */
    private String contractCustomer;

    /**
     * 签约客户分级
     */
    private Integer contractCustomerGrade;

    /**
     * 项目签约主体
     */
    private String contractEntity;

    /**
     * 最终客户ID
     */
    private Long endCustomerId;

    /**
     * 最终客户
     */
    private String endCustomer;

    /**
     * 最终客户分级
     */
    private Integer endCustomerGrade;

    /**
     * 客户集
     */
    private String customerCollection;

    /**
     * 交付类型
     */
    private Integer deliverType;

    /**
     * 是否涉及外采
     */
    private Integer isExternalProcurement;

    /**
     * 采购类别
     */
    private Integer purchasingCategories;

    /**
     * 商机里程碑
     */
    private Integer businessMilestone;

    /**
     * 商机阶段
     */
    private Integer businessStage;

    /**
     * 预计签约时间
     */
    private LocalDate expectedSignDate;

    /**
     * 合同结算方式
     */
    private Integer contractSettlementMethod;

    /**
     * 技术类型
     */
    private String technologyType;

    /**
     * 收入类型
     */
    private Integer incomeType;

    /**
     * 业务类型
     */
    private Integer secondaryBusinessType;

    /**
     * 业务归属二级部门id
     */
    private Long secondLevelDepartmentId;

    /**
     * 业务归属二级部门
     */
    private String secondLevelDepartment;

    /**
     * 是否要提前投入 0-是 1-否
     */
    private Integer isAdvanceInvestment;

    /**
     * 回款条件
     */
    private String paymentCondition;

    /**
     * 项目转在建依据及佐证
     */
    private Integer converseOngoingEvidence;

    /**
     * 进度、质量等要
     */
    private String progressQualityRequirement;

    /**
     * 项目归属二级部门ID
     */
    private Long xmgsbmejbmId;

    /**
     * 项目归属二级部门
     */
    private String xmgsbmejbm;

    /**
     * 收入板块
     */
    private Integer srlx;

    /**
     * 技术类型B表
     */
    private String jslxbb;

    /**
     * 是否为涉密项目
     */
    private Integer sfwsmxm;

    /**
     * 项目需求
     */
    private String xmbj;

    /**
     * 相关详细说明文档
     */
    private String xgxxsmwd;

    /**
     * 交付物
     */
    private String jfw;

    /**
     * 交付地点
     */
    private String jfdd;

    /**
     * 交付期限
     */
    private String jfqx;

    /**
     * 质保期(月)
     */
    private String zbqy;

    /**
     * 保密要求
     */
    private String bmyq;

    /**
     * 售前报告是否移交
     */
    private Integer sqbgsfyj;

    /**
     * 其它售前移交资料
     */
    private String qtsqyjzl;

    /**
     * 计划交付开始时间
     */
    private LocalDate jhjfkssj;

    /**
     * 计划交付完成时间
     */
    private LocalDate jhjfwcsj;

    /**
     * 约定服务开始日期
     */
    private LocalDate ydfwksrq;

    /**
     * 约定服务结束日期
     */
    private LocalDate ydfwjsrq;

    /**
     * 总服务周期(月)
     */
    private String zfwzqy;

    /**
     * 预计每次结算金额
     */
    private BigDecimal yjmcjsje;

    /**
     * 结束周期(月)
     */
    private String jszqy;

    /**
     * 预计回款账期(天)
     */
    private String yjhkzqy;

    /**
     * B表立项流程
     */
    private Long bblxfl;

    /**
     * 项目重启日期
     */
    private LocalDate xmcqrq;

    public static ProjectVO toProjectVO(ProjectInfoVO request) {
        ProjectVO result = new ProjectVO();

        result.setId(Long.valueOf(request.getId()));
        result.setProjectName(request.getItemName());
        result.setCode(request.getItemNo());
        result.setProjectStatus(Integer.valueOf(request.getProjectStatus()));

        return result;
    }

}
