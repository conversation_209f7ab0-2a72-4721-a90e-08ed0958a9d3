package com.gok.pboot.pms.eval.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.eval.entity.domain.EvalUserRole;
import com.gok.pboot.pms.eval.entity.vo.EvalUserRoleVO;

import java.util.List;

/**
 * 项目评价用户角色Service
 *
 * <AUTHOR>
 * @date 2025/05/12
 */
public interface IEvalUserRoleService extends IService<EvalUserRole> {

    /**
     * 查询所有用户角色
     *
     * @return 用户角色列表
     */
    List<EvalUserRoleVO> findAll();

} 