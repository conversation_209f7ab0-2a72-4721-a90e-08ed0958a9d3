package com.gok.pboot.pms.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.common.user.PigxUser;
import com.gok.module.excel.api.domain.vo.ErrorMessageVo;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.Util.BcpLoggerUtils;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.common.constant.FunctionConstants;
import com.gok.pboot.pms.entity.PersonnelReuse;
import com.gok.pboot.pms.entity.domain.ProjectInfo;
import com.gok.pboot.pms.entity.dto.PersonnelReuseImproExcelDTO;
import com.gok.pboot.pms.entity.dto.PersonnelReuseUpdateDTO;
import com.gok.pboot.pms.entity.vo.PersonnelReusePageVO;
import com.gok.pboot.pms.enumeration.ApiResultEnum;
import com.gok.pboot.pms.enumeration.ApprovalStatusEnum;
import com.gok.pboot.pms.enumeration.LogContentEnum;
import com.gok.pboot.pms.enumeration.YesOrNoEnum;
import com.gok.pboot.pms.handler.perm.PmsRetriever;
import com.gok.pboot.pms.mapper.FilingMapper;
import com.gok.pboot.pms.mapper.PersonnelReuseMapper;
import com.gok.pboot.pms.mapper.ProjectInfoMapper;
import com.gok.pboot.pms.service.IPersonnelReuseService;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.BindingResult;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.ValidationException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 人才复用 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-01
 */
@Service
@Slf4j
@Transactional(readOnly = true, rollbackFor = Exception.class)
@RequiredArgsConstructor
public class PersonnelReuseServiceImpl implements IPersonnelReuseService {

    private final PersonnelReuseMapper mapper;
    private final ProjectInfoMapper projectInfoMapper;
    private final FilingMapper filingMapper;
    private final PmsRetriever pmsRetriever;
    private final BcpLoggerUtils bcpLoggerUtils;


    public static final String REGEX_MOBILE = "^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\\d{8}$";

    @Override
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public ApiResult importUser(List<PersonnelReuseImproExcelDTO> excelVOList, BindingResult bindingResult, MultipartFile file) {

        // 通用校验获取失败的数据
        List<ErrorMessageVo> errorMessageList = (List<ErrorMessageVo>) bindingResult.getTarget();
        ArrayList<PersonnelReuseImproExcelDTO> objects = new ArrayList<>();

        if(excelVOList.isEmpty()){
            throw new ValidationException("文件内容不能为空");
        }
        //模版日期解析
        String head = (String)(Object)excelVOList.get(0);
        String[] yearMonth = head.substring(head.indexOf("（") + 1, head.indexOf("）")).replace('年', '-').replaceAll("月","")
                .split("-");
        String yearMonths = yearMonth[0]+"-"+yearMonth[1]+"-01";
        if (yearMonth[1].toCharArray().length < 2){
            yearMonths = yearMonth[0]+"-0"+yearMonth[1]+"-01";
        }
        Integer filedByDate = filingMapper.isFiledByDate(yearMonths);
        if (YesOrNoEnum.YES.getValue().equals( filedByDate)){
            return ApiResult.failure("已归档！拒接导入");
        }

        // 个性化校验逻辑
        List<ProjectInfo> projectList = projectInfoMapper.selectList(null);
        Map<String, ProjectInfo> projectMap = projectList.stream()
                .collect(Collectors.toMap(ProjectInfo::getItemNo, x->x, (x1, x2) -> x1));

        Map<String,PersonnelReuseImproExcelDTO> personnelReuseImproExcelDTOMap = new HashMap<>(excelVOList.size());
        // 执行数据插入操作 组装 从数据行读取
        for (int i = 1; i < excelVOList.size(); i++) {

            PersonnelReuseImproExcelDTO excel = (PersonnelReuseImproExcelDTO)excelVOList.get(i);
            Set<String> errorMsg = new HashSet<>();
            String key = excel.getProjectCode() + excel.getProjectName() + excel.getGrade() + excel.getUserRealName() +excel.getPersonnelType() + excel.getSchoolName() + excel.getMajor();
            //校验项目编号是否存在 获取项目id
            ProjectInfo project = projectMap.get(excel.getProjectCode());
            if (null == project ){
                errorMsg.add(String.format("%s 项目编码数据库未找到匹配", excel.getProjectCode()));
            }else{
                excel.setProjectId(project.getId());
                excel.setProjectName(project.getItemName());
            }

            //手机号校验
            String mobile = excel.getMobile();
            if (!mobile.matches(REGEX_MOBILE)){
                errorMsg.add(String.format("%s 手机号码格式错误", mobile));
            }

            //工时合法校验
            if (!NumberUtils.isCreatable(excel.getAggregatedDays())){
                errorMsg.add(String.format("%s 汇总工时（人天）格式错误", excel.getAggregatedDays()));
            }
            // 数据合法情况
            if (CollUtil.isEmpty(errorMsg)) {
                // 手动生成id保证有序
                excel.setId(IdWorker.getId());
                // 表格数据重复时取最下面一条
                if (personnelReuseImproExcelDTOMap.containsKey(key)){
                    personnelReuseImproExcelDTOMap.replace(key,excel);
                }
                personnelReuseImproExcelDTOMap.putIfAbsent(key,excel);
//                objects.add(excel);
            } else {
                // 数据不合法情况
                errorMessageList.add(new ErrorMessageVo((long) (i + 2), errorMsg));
            }
        }

        if (CollUtil.isNotEmpty(errorMessageList)) {
            // 组装错误信息
            StringJoiner allErrMsg = new StringJoiner("; ");
            StringJoiner perErrMsg = null;
            for (ErrorMessageVo errorMessage : errorMessageList) {
                perErrMsg = new StringJoiner(", ");
                for (String error : errorMessage.getErrors()) {
                    perErrMsg.add(error);
                }
                allErrMsg.add("第" + errorMessage.getLineNum() + "行: " + perErrMsg.toString());
            }
            return ApiResult.builder().apiResultEnum(ApiResultEnum.IMPORT_VALID_FAIL).result(errorMessageList).retMessage(allErrMsg.toString()).build();
        }
        //获取该月份已导入的人才复用信息列表
        List<PersonnelReuse> personnelReuseList = mapper.selectList(new QueryWrapper<PersonnelReuse>().eq("reuse_date", yearMonths).eq("del_flag", 0));
        Map<String, PersonnelReuse> personnelReuseMap = personnelReuseList.stream().collect(Collectors.toMap(x -> {
            return x.getReuseDate() + x.getProjectCode() + x.getProjectName() + x.getGrade() + x.getUserRealName() + x.getPersonnelType() + x.getSchoolName() + x.getMajor();
        }, x -> x));
        List<PersonnelReuseImproExcelDTO> datas = personnelReuseImproExcelDTOMap.values().stream().collect(Collectors.toList());
        insertExcelUser(datas,yearMonths,personnelReuseMap);
        //导入【文件名称】，数据【XX条】
        bcpLoggerUtils.log(FunctionConstants.TALENT_REUSE_INTRODUCTION, LogContentEnum.IMPORT,
                file.getOriginalFilename(),datas.size());
        return ApiResult.success("人才复用表导入成功！");
    }

    @Override
    public Page<PersonnelReusePageVO> findPage(PageRequest pageRequest, Map<String, Object> filter) {
        //导入人信息
        Long id = SecurityUtils.getUser().getId();
        filter.put("userId",id);

        // 根据配置权限获取当前可查看的项目id
        Set<Long> projectIdsAvailableSet = Sets.newHashSet(pmsRetriever.getProjectIdsAvailable());
        filter.put("projectIds", CollectionUtils.isEmpty(projectIdsAvailableSet) ?
                ImmutableList.of()
                :
                projectIdsAvailableSet);
        Page<PersonnelReusePageVO> list = mapper.findPage(new Page<>(pageRequest.getPageNumber(), pageRequest.getPageSize()), filter);
        return list;
    }


    @Override
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public ApiResult batchDel(List<Long> list) {
        List<PersonnelReuse> personnelReuses = mapper.selectBatchIds(list);
        if (CollUtil.isNotEmpty(personnelReuses)) {
            personnelReuses.stream().map(PersonnelReuse::getApprovalStatus).filter(s -> s == 4).findAny().ifPresent(s -> {
                throw new ValidationException("‘已审核’的数据不允许删除");
            });
        }
        mapper.batchDel(list);
        return ApiResult.success("操作成功");
    }

    @Override
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public ApiResult<String> update(PersonnelReuseUpdateDTO personnelReuseUpdateDTO) {
        //导入人信息
        PigxUser user = SecurityUtils.getUser();
        personnelReuseUpdateDTO.setModifier(user.getName());
        personnelReuseUpdateDTO.setModifierId(user.getId());
        personnelReuseUpdateDTO.setMtime(new Timestamp(System.currentTimeMillis()));
        // 不通过的重新提交变成待审核
        if (ApprovalStatusEnum.BTG.getValue().equals(personnelReuseUpdateDTO.getApprovalStatus())){
            personnelReuseUpdateDTO.setApprovalStatus(ApprovalStatusEnum.DSH.getValue());
        }
        mapper.updateById(personnelReuseUpdateDTO);
        return ApiResult.success("操作成功");
    }

    /**
     * 数据合法后操作
     * @param datas
     * @param yearMonths
     */
    private void insertExcelUser(List<PersonnelReuseImproExcelDTO> datas,String yearMonths,Map<String, PersonnelReuse> personnelReuseMap){

        ArrayList<PersonnelReuse> update = new ArrayList<>();
        ArrayList<PersonnelReuse> insert = new ArrayList<>();

        //导入人信息
        PigxUser user = SecurityUtils.getUser();
        Long userId = user.getId();
        String userName = user.getName();

        //日期格式转换
        LocalDate beginDateTime = LocalDate.parse(yearMonths, DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        for (int i = 0; i < datas.size(); i++) {
            PersonnelReuseImproExcelDTO data = datas.get(i);
            // 校验联合key是否重复（月份+项目编号+项目名称+年级+人员名称+人才类型+院校名称+专业）
            String bigKey =  beginDateTime + data.getProjectCode() + data.getProjectName() + data.getGrade()
                    + data.getUserRealName() + data.getPersonnelType() + data.getSchoolName() + data.getMajor();
            PersonnelReuse personnelReuse = personnelReuseMap.get(bigKey);
            String remark = data.getRemark();
            // 根据重复项数据判断该条数据的审核状态，“不通过”、“审核通过”数据不导入，“待审核”数据取覆盖操作
            if (personnelReuse != null && ApprovalStatusEnum.DSH.getValue().equals(personnelReuse.getApprovalStatus())){
                //更新
                personnelReuse.setRemark(remark==null?"":remark);
                personnelReuse.setAggregatedDays(
                        new BigDecimal(data.getAggregatedDays()).setScale(2, RoundingMode.HALF_UP)
                );
                personnelReuse.setExecutorUserId(userId);
                personnelReuse.setExecutorUserRealName(userName);
                BaseBuildEntityUtil.buildUpdate(personnelReuse);
                // 更新创建时间
                BaseBuildEntityUtil.buildInsertNoId(personnelReuse);
                update.add(personnelReuse);
            }else if (personnelReuse == null){
                //插入
                PersonnelReuse personnelReuseInsert = new PersonnelReuse();
                BeanUtils.copyProperties(data,personnelReuseInsert);
                personnelReuseInsert.setRemark(remark==null?"":remark);
                personnelReuseInsert.setAggregatedDays(
                        new BigDecimal(data.getAggregatedDays()).setScale(2, RoundingMode.HALF_UP)
                );
                personnelReuseInsert.setReuseDate(beginDateTime);
                personnelReuseInsert.setExecutorUserId(userId);
                personnelReuseInsert.setExecutorUserRealName(userName);
                BaseBuildEntityUtil.buildInsertNoId(personnelReuseInsert);
                insert.add(personnelReuseInsert);
            }
        }

        if (update.size() > 0){
            mapper.batchUpdate(update);
        }

        if (insert.size() > 0){
            mapper.batchSave(insert);
        }

    }

}
