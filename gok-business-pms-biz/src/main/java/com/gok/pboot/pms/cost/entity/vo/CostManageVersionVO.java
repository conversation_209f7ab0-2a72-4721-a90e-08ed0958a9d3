package com.gok.pboot.pms.cost.entity.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.sql.Timestamp;
import java.time.LocalDateTime;

/**
 * <p>
 * 成本管理版本记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class CostManageVersionVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 版本类型（0=目标管理，1=成本管理，2=报价与毛利测算）
     * {@link com.gok.pboot.pms.cost.enums.CostManageVersionEnum}
     */
    private Integer versionType;

    private String versionTypeName;

    /**
     * 历史版本id
     */
    private Long versionId;

    /**
     * 版本状态（0=当前版本，1=历史版本）
     */
    private Integer versionStatus;

    /**
     * 版本状态中文
     */
    private String versionStatusName;

    /**
     * 版本名称
     */
    private String versionName;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 关联流程ID
     */
    private Long requestId;

    /**
     * 关联流程名称
     */
    private String requestName;

    /**
     * 流程编号
     */
    private String requestNumber;

    /**
     * 归档时间
     */
    private String filingTime;

    /**
     * 关联流程状态（0=未提交，1=审批中，1=已归档）
     */
    private Integer requestStatus;

    /**
     * 成本预算类型（0=售前成本，1=交付总成本）
     */
    private Integer costBudgetType;

    /**
     * 成本预算类型中文
     */
    private String costBudgetTypeName;

    /**
     * 状态（0=未确认，1=已确认，2=已拒绝）
     */
    private Integer status;

    /**
     * 状态中文值
     */
    private String statusName;

    /**
     * 操作人姓名
     */
    private String operatorName;

    /**
     * 操作人当前角色
     */
    private String operatorRole;

    /**
     * 创建人id
     */
    private Long creatorId;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp mtime;

    /**
     * 关联售前版本ID
     */
    private Long preSaleVersionId;

    /**
     * 关联售前版本ID
     */
    private String preSaleVersionName;

    /**
     * 创建时间
     */
    private LocalDateTime ctime;

}
