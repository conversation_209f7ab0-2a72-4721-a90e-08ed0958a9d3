package com.gok.pboot.pms.cost.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.cost.entity.domain.CostDeliverTask;
import com.gok.pboot.pms.cost.entity.dto.CostSupportTaskAddEditDTO;
import com.gok.pboot.pms.cost.enums.CostTaskDisassemblyTypeEnum;
import com.gok.pboot.pms.cost.enums.CostTaskStatusEnum;
import com.gok.pboot.pms.enumeration.OperateEnum;
import lombok.Data;

import java.util.*;

/**
 * 售前工单树处理器
 *
 * <AUTHOR>
 * @date 2024/04/10
 */
@Data
public class SupportTaskTreeProcessor {

    /**
     * 需要添加的工单
     */
    private List<CostDeliverTask> addTasks = new ArrayList<>();

    /**
     * 需要更新的工单
     */
    private List<CostDeliverTask> updateTasks = new ArrayList<>();

    /**
     * 需要删除的工单
     */
    private List<CostDeliverTask> removeTasks = new ArrayList<>();

    /**
     * 已处理的工单ID集合
     */
    private Set<Long> processedTaskIds = new HashSet<>();

    /**
     * 处理工单树
     *
     * @param editDTO         编辑DTO
     * @param parentTask      父工单
     * @param originalTaskMap 原工单Map
     */
    public void processTaskTree(CostSupportTaskAddEditDTO editDTO,
                                CostDeliverTask parentTask,
                                Map<Long, CostDeliverTask> originalTaskMap) {
        // 处理当前节点
        processCurrentNode(editDTO, parentTask, originalTaskMap);

        // 递归处理子节点
        if (CollUtil.isNotEmpty(editDTO.getChildren()) && EnumUtils.valueEquals(editDTO.getDisassemblyType(),
                CostTaskDisassemblyTypeEnum.TOTAL_WORK_ORDER)) {
            for (CostSupportTaskAddEditDTO childDTO : editDTO.getChildren()) {
                childDTO.setTaskLevel(editDTO.getTaskLevel() + 1);
                childDTO.setParentId(editDTO.getId());
                processTaskTree(childDTO, parentTask, originalTaskMap);
            }
        }
    }

    private void processCurrentNode(CostSupportTaskAddEditDTO editDTO,
                                    CostDeliverTask parentTask,
                                    Map<Long, CostDeliverTask> originalTaskMap) {
        // 记录已处理的工单ID
        Optional.ofNullable(editDTO.getId()).ifPresent(processedTaskIds::add);

        OperateEnum operateEnum = editDTO.getOperateEnum();
        if (operateEnum == null || OperateEnum.DEFAULT.equals(operateEnum)) {
            return;
        }

        switch (operateEnum) {
            case ADD:
                CostDeliverTask newTask = buildNewSupportChildTask(parentTask, editDTO);
                editDTO.setId(newTask.getId());
                addTasks.add(newTask);
                break;
            case UPDATE:
                CostDeliverTask originalTask = originalTaskMap.get(editDTO.getId());
                if (originalTask != null) {
                    CostDeliverTask updatedTask = updateSupportTask(originalTask, editDTO);
                    updateTasks.add(updatedTask);
                }
                break;
            default:
                break;
        }
    }


    private static CostDeliverTask buildNewSupportChildTask(CostDeliverTask firstLevelTask, CostSupportTaskAddEditDTO dto) {
        CostDeliverTask costDeliverTask = new CostDeliverTask();
        BeanUtil.copyProperties(dto, costDeliverTask);

        // 自动继承父级工单的数据
        costDeliverTask.setTaskType(firstLevelTask.getTaskType());
        costDeliverTask.setProjectId(firstLevelTask.getProjectId());
        costDeliverTask.setProjectName(firstLevelTask.getProjectName());

        costDeliverTask.setAccountId(firstLevelTask.getAccountId());
        costDeliverTask.setTaxRate(firstLevelTask.getTaxRate());
        costDeliverTask.setAccountName(firstLevelTask.getAccountName());
        costDeliverTask.setAccountOaId(firstLevelTask.getAccountOaId());

        costDeliverTask.setTaskStatus(CostTaskStatusEnum.ZC.getValue());
        return BaseBuildEntityUtil.buildInsert(costDeliverTask);
    }

    private static CostDeliverTask updateSupportTask(CostDeliverTask originalTask, CostSupportTaskAddEditDTO taskAddEditDTO) {

        BeanUtil.copyProperties(taskAddEditDTO, originalTask, CopyOptions.create().ignoreNullValue());

        return BaseBuildEntityUtil.buildUpdate(originalTask);
    }
} 