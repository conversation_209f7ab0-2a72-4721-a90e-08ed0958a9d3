/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 */

package com.gok.pboot.pms.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import com.google.common.base.Strings;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

/**
 * 项目任务附件
 *
 * <AUTHOR> generator
 * @date 2023-08-18 10:07:30
 */
@Data
@TableName("project_task_attachment")
@EqualsAndHashCode(callSuper = true)
public class ProjectTaskAttachment extends BeanEntity<Long> {

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 附件文件ID
     */
    private Long fileId;

    /**
     * 文件名称
     */
    private String fileName;

    public static ProjectTaskAttachment of(Long taskId, Long projectId, Long fileId, Map<Long, String> docNameMap) {
        ProjectTaskAttachment result = new ProjectTaskAttachment();

        result.setProjectId(projectId);
        result.setTaskId(taskId);
        result.setFileId(fileId);
        //result.setFileName(Strings.nullToEmpty(fileName));
        result.setFileName(Strings.nullToEmpty(docNameMap.get(fileId)));

        return result;
    }
}
