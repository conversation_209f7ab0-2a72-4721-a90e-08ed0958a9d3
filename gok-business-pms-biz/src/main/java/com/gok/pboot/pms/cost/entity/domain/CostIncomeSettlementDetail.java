package com.gok.pboot.pms.cost.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 人力外包-收入结算明细
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("cost_income_settlement_detail")
public class CostIncomeSettlementDetail extends BeanEntity<Long> {

    private static final long serialVersionUID = 1L;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 结算开始日期
     */
    private LocalDate startDate;

    /**
     * 结算截止日期
     */
    private LocalDate endDate;

    /**
     * 姓名
     */
    private String userName;

    /**
     * 工号
     */
    private String workCode;

    /**
     * 收入测算id
     */
    private Long costIncomeCalculationId;

    /**
     * 收入测算明细id
     */
    private Long costIncomeCalculationDetailId;

    /**
     * 操作结算日期
     */
    private LocalDateTime operationSettlementDate;

    /**
     * 测算含税金额
     */
    private BigDecimal estimatedInclusiveAmountTax;

    /**
     * 结算含税金额
     */
    private BigDecimal budgetAmountIncludedTax;

    /**
     * 税率OA字典ID
     */
    private String taxRate;

    /**
     * 结算不含税金额
     */
    private BigDecimal budgetAmountExcludingTax;

    /**
     * 备注说明
     */
    private String remarksDesc;

    /**
     * 归档时间
     */
    private String filingTime;

    /**
     * OA结算单流程id
     */
    private Long requestId;

    /**
     * OA结算单编号
     */
    private String requestNumber;

    /**
     * 结算明细编号
     */
    private String settlementDetailsNumber;

    /**
     * 结算单编号
     */
    private String settlementNumber;

    /**
     * 数据来源
     */
    private Integer dataSources;

}