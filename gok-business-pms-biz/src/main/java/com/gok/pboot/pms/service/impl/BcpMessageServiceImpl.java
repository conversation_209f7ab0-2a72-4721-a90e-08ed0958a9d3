package com.gok.pboot.pms.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import com.gok.bcp.message.dto.BcpMessageBatchDTO;
import com.gok.bcp.message.dto.BcpMessageDTO;
import com.gok.bcp.message.dto.BcpMessageTargetBatchDTO;
import com.gok.bcp.message.dto.BcpMessageTargetDTO;
import com.gok.bcp.message.entity.enums.ChannelEnum;
import com.gok.bcp.message.entity.enums.MsgTypeEnum;
import com.gok.bcp.message.entity.enums.SourceEnum;
import com.gok.bcp.message.entity.enums.TargetTypeEnum;
import com.gok.bcp.message.entity.model.*;
import com.gok.bcp.message.feign.RemoteMailService;
import com.gok.bcp.message.feign.RemoteSendMsgService;
import com.gok.bcp.message.vo.MsgResultVO;
import com.gok.components.common.constant.SecurityConstants;
import com.gok.components.common.util.R;
import com.gok.pboot.common.core.constant.CommonConstants;
import com.gok.pboot.pms.common.exception.ServiceException;
import com.gok.pboot.pms.entity.dto.BaseSendMsgDTO;
import com.gok.pboot.pms.entity.dto.BaseSendMsgOutDTO;
import com.gok.pboot.pms.service.BcpMessageService;
import com.google.common.base.Charsets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * BCP 消息服务实现
 *
 * <AUTHOR>
 * @date 2025/01/08
 */
@Slf4j
@Service
public class BcpMessageServiceImpl implements BcpMessageService {

    public static final String NAME = "name";
    public static final String LINK_PARAM = "link_param";
    @Resource
    private RemoteSendMsgService remoteSendMsgService;

    @Resource
    private RemoteMailService remoteMailService;


    @Value("${project.task.leftUrl}")
    private String projectUrl;

    @Value("${pushMessage.redirectPrefixUrl}")
    private String redirectPrefix;

    @Value("${aliyun.sms.accessKeyId}")
    private String accessKeyId;

    @Value("${aliyun.sms.signName}")
    private String signName;

    @Value("${aliyun.sms.templateCode}")
    private String templateCode;

    private static final String READ_ONLY = "readonly";

    @Override
    public void sendMsg(@Valid BaseSendMsgDTO sendMsgDTO) {
        if (sendMsgDTO == null || CollUtil.isEmpty(sendMsgDTO.getTargetList())) {
            return;
        }
        BcpMessageDTO bcpMessageDTO = buildBcpMessageDTO(sendMsgDTO);
        Set<ChannelEnum> channelEnums = sendMsgDTO.getChannelEnums();
        if (CollUtil.isEmpty(channelEnums)) {
            return;
        }
        for (ChannelEnum channelEnum : channelEnums) {
            bcpMessageDTO.setChannel(channelEnum.getValue());
            // 克隆原消息
            BcpMessageDTO msgDTO = ObjectUtil.clone(bcpMessageDTO);
            // 设置内容URL
            setContentUrl(channelEnum, msgDTO);
            try {
                switch (channelEnum) {
                    case MAIL:
                        MailModel mailModel = MailModel.from(msgDTO);
                        remoteMailService.sendMsg(mailModel);
                        break;
                    case WECOM:
                        WeComModel weComModel = WeComModel.from(msgDTO);
                        remoteSendMsgService.sendMsg(weComModel);
                        break;
                    case SMS:
                        SmsModel smsModel = SmsModel.from(msgDTO);
                        remoteSendMsgService.sendMsg(smsModel);
                        break;
                    case OA:
                        OaModel oaModel = OaModel.from(msgDTO);
                        remoteSendMsgService.sendMsg(oaModel);
                        break;
                    case DINDDING:
                        DdModel ddModel = DdModel.from(msgDTO);
                        remoteSendMsgService.sendMsg(ddModel);
                        break;
                    case EMAIL:
                        EMailModel eMailModel = EMailModel.from(msgDTO);
                        remoteSendMsgService.sendMsg(eMailModel);
                        break;
                    default:
                }
            } catch (Exception e) {
                log.error("{}消息推送失败: {}", channelEnum.getName(), e.getMessage());
                e.printStackTrace();
            }
        }
        log.info("消息推送完成");
    }


    @Override
    @Async
    public void batchSendMsg(@Validated List<BaseSendMsgDTO> baseSendMsgDTOList) {
        if (CollUtil.isEmpty(baseSendMsgDTOList)) {
            return;
        }
        // 验证参数
        baseSendMsgDTOList.forEach(this::verify);
        Map<ChannelEnum, List<BcpMessageDTO>> bcpMessageDtoMap = new ConcurrentHashMap<>(ChannelEnum.values().length);
        for (BaseSendMsgDTO sendMsgDTO : baseSendMsgDTOList) {
            // 构建bcp消息DTO
            BcpMessageDTO bcpMessageDTO = buildBcpMessageDTO(sendMsgDTO);
            Set<ChannelEnum> channelEnums = sendMsgDTO.getChannelEnums();
            // 遍历渠道 对消息分类
            channelEnums.forEach(channelEnum -> {
                // 设置渠道
                bcpMessageDTO.setChannel(channelEnum.getValue());
                // 克隆原消息
                BcpMessageDTO msgDTO = ObjectUtil.clone(bcpMessageDTO);
                // 设置内容URL
                setContentUrl(channelEnum, msgDTO);
                // 添加到map中
                bcpMessageDtoMap.computeIfAbsent(channelEnum, k -> new ArrayList<>()).add(msgDTO);
            });
        }
        bcpMessageDtoMap.forEach((channelEnum, dtoList) -> {
            if (CollUtil.isEmpty(dtoList)) {
                return;
            }
            try {
                switch (channelEnum) {
                    case MAIL:
                    case EMAIL:
                        BcpMessageBatchDTO bcpMessageBatchDTO = buildBcpMessageBatchDTO(dtoList);
                        bcpMessageBatchDTO.setChannel(channelEnum.getValue());
                        remoteSendMsgService.sendMsgBatch(SecurityConstants.FROM_IN, bcpMessageBatchDTO);
                        break;
                    case WECOM:
                        List<WeComModel> weComModels = dtoList.stream().map(WeComModel::from).collect(Collectors.toList());
                        WeComBatchModel weComBatchModel = new WeComBatchModel();
                        weComBatchModel.setData(weComModels);
                        remoteSendMsgService.sendWeComMsgBatch(SecurityConstants.FROM_IN, weComBatchModel);
                        break;
                    default:
                }
            } catch (Exception e) {
                log.error("{}消息批量推送失败: {}", channelEnum.getName(), e.getMessage());
                e.printStackTrace();
            }
        });
        log.info("消息批量推送完成~");
    }

    private BcpMessageDTO buildBcpMessageDTO(BaseSendMsgDTO sendMsgDTO) {

        String redirectUrl = READ_ONLY;
        if (StringUtils.isNotBlank(sendMsgDTO.getPath()) && !READ_ONLY.equals(sendMsgDTO.getPath())) {
            // 跳转链接
            if (StringUtils.isBlank(sendMsgDTO.getLinkPrefix())) {
                redirectUrl = redirectPrefix + Base64.encode(projectUrl + sendMsgDTO.getPath(), Charsets.UTF_8);
            } else {
                redirectUrl = sendMsgDTO.getLinkPrefix() + Base64.encode(projectUrl + sendMsgDTO.getPath(), Charsets.UTF_8);
            }
        }

        BcpMessageDTO bcpMessageDTO = BaseSendMsgDTO.of(sendMsgDTO);
        bcpMessageDTO.setSource(SourceEnum.PROJECT.getValue());
        bcpMessageDTO.setRedirectUrl(redirectUrl);
        bcpMessageDTO.setSendTime(LocalDateTimeUtil.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        return bcpMessageDTO;
    }

    private void verify(@Valid BaseSendMsgDTO sendMsgDTO) {
        // 验证参数
    }

    private static BcpMessageBatchDTO buildBcpMessageBatchDTO(List<BcpMessageDTO> bcpMessageDTOList) {
        if (CollUtil.isEmpty(bcpMessageDTOList)) {
            throw new ServiceException("消息列表不能为空");
        }
        BcpMessageDTO bcpMessageDTO = bcpMessageDTOList.get(0);
        String channel = bcpMessageDTO.getChannel();
        BcpMessageBatchDTO result = new BcpMessageBatchDTO();
        int mSize = bcpMessageDTOList.size();
        List<BcpMessageTargetBatchDTO> targetList = new ArrayList<>();
        int contentId = 0;
        Map<String, BcpMessageContentModel> contentIdMap = new HashMap<>(mSize);
        result.setSource(bcpMessageDTO.getSource());
        result.setChannel(channel);
        result.setSenderId(bcpMessageDTO.getSenderId());
        result.setSender(bcpMessageDTO.getSender());
        result.setSendTime(bcpMessageDTO.getSendTime());
        result.setTargetType(bcpMessageDTO.getTargetType());
        for (BcpMessageDTO m : bcpMessageDTOList) {
            contentId++;
            for (BcpMessageTargetDTO targetDTO : m.getTargetList()) {
                BcpMessageTargetBatchDTO target = new BcpMessageTargetBatchDTO();
                // 绑定消息内容id
                String contentIdStr = String.valueOf(contentId);

                target.setTargetId(targetDTO.getTargetId());
                target.setTargetName(targetDTO.getTargetName());
                target.setContentId(contentIdStr);
                targetList.add(target);

                BcpMessageContentModel content = new BcpMessageContentModel();
                content.setType(m.getType());
                content.setTitle(m.getTitle());
                if (ChannelEnum.MAIL.getValue().equals(channel)) {
                    content.setContent(ArrayUtils.get(StringUtils.split(m.getContent(), "\n"), 0));
                } else {
                    content.setContent(m.getContent());
                }
                content.setRedirectUrl(m.getRedirectUrl());
                contentIdMap.put(contentIdStr, content);
            }
        }
        result.setTargetList(targetList);
        result.setContentMap(contentIdMap);
        return result;
    }

    /**
     * 设置内容 URL
     *
     * @param channelEnum   通道枚举
     * @param bcpMessageDTO BCP 消息 DTO
     */
    private static void setContentUrl(ChannelEnum channelEnum, BcpMessageDTO bcpMessageDTO) {
        String redirectUrl = bcpMessageDTO.getRedirectUrl();
        if (StringUtils.isBlank(redirectUrl) || READ_ONLY.equals(redirectUrl)) {
            return;
        }
        String content = bcpMessageDTO.getContent();
        switch (channelEnum) {
            case MAIL:
                content = content + "\n查看详情~>";
                break;
            case SMS:
            case OA:
            case DINDDING:
            case WECOM:
                content = content + "\n<a href=\"" + redirectUrl + "\">查看详情~></a>";
                break;
            default:
        }
        bcpMessageDTO.setContent(content);
    }

    /**
     * 外部人员消息发送
     *
     * @param dto DTO
     */
    @Override
    public void sendSmsMsg(BaseSendMsgOutDTO dto) {
        Map<String, String> smsParamMap = new HashMap<>(0);
        smsParamMap.put(NAME, dto.getTargetList().get(0).getTargetName());
        smsParamMap.put(LINK_PARAM, dto.getProjectId());

        try {
            SmsModel smsModel = setSmsModel(dto, smsParamMap);
            remoteSendMsgService.sendSmsMsgOut(smsModel);
        } catch (Exception e) {
            log.error("短信发送失败: {}", e.getMessage());
        }

    }

    private SmsModel setSmsModel(BaseSendMsgOutDTO dto, Map<String, String> smsParamMap) {
        SmsModel smsModel = new SmsModel();
        smsModel.setAccessKeyId(accessKeyId);
        smsModel.setSignName(signName);
        smsModel.setTemplateCode(templateCode);
        smsModel.setSmsParamMap(smsParamMap);
        smsModel.setSource(SourceEnum.PROJECT.getValue());
        smsModel.setType(dto.getMsgTypeEnum().getValue());
        smsModel.setTitle(dto.getTitle());
        smsModel.setContent(dto.getContent());
        smsModel.setSenderId(dto.getSenderId());
        smsModel.setSender(dto.getSender());
        smsModel.setTargetType(dto.getTargetTypeEnum().getValue());
        smsModel.setTargetList(dto.getTargetList());
        return smsModel;
    }

    /**
     * 测试手机短信
     */
    public R<Boolean> sendCodeTest() {
        List<BcpMessageTargetDTO> targetList = new ArrayList<>(1);
        targetList.add(new BcpMessageTargetDTO("896", "熊文辉", "15079280867"));
        Map<String, String> smsParamMap = new HashMap<>();
        smsParamMap.put("name", "熊文辉");
        smsParamMap.put("link_param", "1234");

        SmsModel smsModel = new SmsModel();
        smsModel.setAccessKeyId("LTAI5tRs3F5MsMMa9snkz5nE");
        smsModel.setSignName("国科科技");
        smsModel.setTemplateCode("SMS_485360740");
        smsModel.setSmsParamMap(smsParamMap);
        smsModel.setSource(SourceEnum.PROJECT.getValue());
        smsModel.setType(MsgTypeEnum.TEXT_MSG.getValue());
        smsModel.setTitle("满意度调查");
        smsModel.setContent("1234");
        smsModel.setSenderId(10000L);
        smsModel.setSender("超级管理员");
        smsModel.setTargetType(TargetTypeEnum.USERS.getValue());
        smsModel.setTargetList(targetList);
        com.gok.components.common.util.R<List<MsgResultVO>> listR = remoteSendMsgService.sendSmsMsgOut(smsModel);
        if (listR.getCode() != CommonConstants.SUCCESS) {
            return R.ok(Boolean.FALSE, listR.getMsg());
        }
        return R.ok(Boolean.TRUE);
    }

}
