package com.gok.pboot.pms.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目关注
 *
 * <AUTHOR>
 * @date 2023/9/1
 */
@Data
@TableName("project_attention")
@EqualsAndHashCode(callSuper = true)
public class ProjectAttention extends BeanEntity<Long> {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 项目ID
     */
    private Long projectId;
}
