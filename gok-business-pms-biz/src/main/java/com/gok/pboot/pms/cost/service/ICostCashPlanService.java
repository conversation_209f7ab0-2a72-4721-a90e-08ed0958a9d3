package com.gok.pboot.pms.cost.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.cost.entity.dto.CostCashPlanDto;
import com.gok.pboot.pms.cost.entity.dto.CostCashPlanSaveDTO;
import com.gok.pboot.pms.cost.entity.vo.CostCashPlanVO;
import com.gok.pboot.pms.cost.entity.vo.CostCashPlanVersionVO;

import java.util.List;

public interface ICostCashPlanService {

    /**
     * 查询项目现金流计划
     *
     * @param request 查询请求
     * @return 现金流计划列表
     */
    List<CostCashPlanVO> getCostCashPlanList(CostCashPlanDto request);

    /**
     * 查询项目现金流计划
     *
     * @param planList 计划列表
     * @return 现金流计划列表
     */
    List<CostCashPlanVO> getCostCashPlanList(List<CostCashPlanVO> planList);

    /**
     * 保存项目现金流计划
     *
     * @param dto 保存请求
     */
    Long saveCostCashPlan(CostCashPlanSaveDTO dto);

    /**
     * 查询现金流计划版本记录
     *
     * @param projectId   项目ID
     * @param pageRequest 页面请求
     * @return 版本记录列表
     */
    Page<CostCashPlanVersionVO> findVersionPage(Long projectId, PageRequest pageRequest);

    CostCashPlanVersionVO getCurrentVersion(Long projectId);
}