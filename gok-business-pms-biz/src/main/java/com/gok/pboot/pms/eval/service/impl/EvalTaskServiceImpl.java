/*
 * @Author: luoyq
 * @Date: 2025-05-08
 */
package com.gok.pboot.pms.eval.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.bcp.message.entity.enums.ChannelEnum;
import com.gok.bcp.message.entity.enums.MsgTypeEnum;
import com.gok.bcp.message.entity.enums.TargetTypeEnum;
import com.gok.bcp.upms.feign.RemoteRoleService;
import com.gok.bcp.upms.vo.SysUserRoleDataVo;
import com.gok.components.common.user.PigxUser;
import com.gok.components.common.user.UserUtils;
import com.gok.pboot.common.secret.AESEncryptor;
import com.gok.pboot.pms.Util.*;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.common.exception.ServiceException;
import com.gok.pboot.pms.cost.entity.domain.CostDeliverTask;
import com.gok.pboot.pms.cost.entity.domain.CostTaskDailyPaperEntry;
import com.gok.pboot.pms.cost.enums.CostTaskDisassemblyTypeEnum;
import com.gok.pboot.pms.cost.enums.CostTaskStatusEnum;
import com.gok.pboot.pms.cost.mapper.CostDeliverTaskMapper;
import com.gok.pboot.pms.cost.mapper.CostTaskDailyPaperEntryMapper;
import com.gok.pboot.pms.entity.domain.ProjectInfo;
import com.gok.pboot.pms.entity.dto.BaseSendMsgDTO;
import com.gok.pboot.pms.enumeration.ApprovalStatusEnum;
import com.gok.pboot.pms.enumeration.ProjectTaskKindEnum;
import com.gok.pboot.pms.enumeration.YesOrNoEnum;
import com.gok.pboot.pms.eval.entity.domain.*;
import com.gok.pboot.pms.eval.entity.dto.EvalTaskCalibrationDTO;
import com.gok.pboot.pms.eval.entity.dto.EvalTaskDistributionDTO;
import com.gok.pboot.pms.eval.entity.dto.EvalTaskListDTO;
import com.gok.pboot.pms.eval.entity.dto.EvalTaskSubmitDTO;
import com.gok.pboot.pms.eval.entity.vo.*;
import com.gok.pboot.pms.eval.enums.EvalLevelEnum;
import com.gok.pboot.pms.eval.enums.EvalStatusEnum;
import com.gok.pboot.pms.eval.enums.EvalTaskStatusEnum;
import com.gok.pboot.pms.eval.enums.EvalUserRoleEnum;
import com.gok.pboot.pms.eval.mapper.*;
import com.gok.pboot.pms.eval.service.IEvalProjectOverviewService;
import com.gok.pboot.pms.eval.service.IEvalTaskService;
import com.gok.pboot.pms.eval.service.IEvalUserRoleService;
import com.gok.pboot.pms.mapper.ProjectInfoMapper;
import com.gok.pboot.pms.service.BcpMessageService;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Table;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.ValidationException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 工单评价
 *
 * <AUTHOR>
 * @create 2025/5/8
 **/
@Log4j2
@Service
public class EvalTaskServiceImpl extends ServiceImpl<EvalTaskMapper, EvalTask> implements IEvalTaskService {
    @Resource
    private EvalTaskMapper baseMapper;
    @Resource
    private CostDeliverTaskMapper costDeliverTaskMapper;
    @Resource
    private RemoteRoleService remoteRoleService;
    @Resource
    private ProjectInfoMapper projectInfoMapper;
    @Resource
    private CostTaskDailyPaperEntryMapper costTaskDailyPaperEntryMapper;
    @Resource
    private EvalTaskCalibrationMapper evalTaskCalibrationMapper;
    @Resource
    private EvalProjectOverallDistributionMapper evalProjectOverallDistributionMapper;
    @Resource
    private IEvalProjectOverviewService IEvalProjectOverviewService;
    @Resource
    private EvalProjectManagerMapper evalProjectManagerMapper;
    @Resource
    private EvalProjectOverviewMapper evalProjectOverviewMapper;
    @Resource
    private BcpMessageService bcpMessageService;
    @Resource
    private IEvalUserRoleService evalUserRoleService;

    public final static int TASK_FIRST_LEVEL = 1;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitEvaluation(EvalTaskSubmitDTO dto) {
        CostDeliverTask task = costDeliverTaskMapper.selectById(dto.getTaskId());
        if (null == task) {
            throw new ServiceException("工单不存在！");
        }
        if (CostTaskDisassemblyTypeEnum.TOTAL_WORK_ORDER.getValue().equals(task.getDisassemblyType())) {
            throw new ServiceException("总成工单不允许评价！");
        }
        // 1. 保存评价表
        EvalTask evalTask = baseMapper.selectOne(Wrappers.<EvalTask>lambdaQuery().eq(EvalTask::getTaskId, dto.getTaskId()));
        if (evalTask != null) {
            throw new ServiceException("工单已评价！");
        }
        EvalTask evaluation = BeanUtil.copyProperties(dto, EvalTask.class);
        BaseBuildEntityUtil.buildInsert(evaluation);
        baseMapper.insert(evaluation);

        // 2. 更新工单状态为已评价
        task.setEvaluationStatus(EvalTaskStatusEnum.YPJ.getValue());
        BaseBuildEntityUtil.buildUpdate(task);
        costDeliverTaskMapper.updateById(task);
        // 3. 更新工单评价校准表
        countComprehensiveScore(task);
    }

    @Override
    public EvalTaskDetailVO evaluationDetail(Long taskId) {
        // 1. 查询工单
        CostDeliverTask task = costDeliverTaskMapper.selectById(taskId);
        if (task == null) {
            throw new ServiceException("工单不存在");
        }
        EvalTaskDetailVO vo = new EvalTaskDetailVO();
        BeanUtil.copyProperties(task, vo);
        vo.setTaskTypeName(EnumUtils.getNameByValue(ProjectTaskKindEnum.class, task.getTaskType()));

        if (task.getTaskType().equals(ProjectTaskKindEnum.PRE_SALES_SUPPORT.getValue())) {
            vo.setStartDate(task.getCtime().toLocalDateTime().toLocalDate());
            vo.setEndDate(task.getCompletionTime().toLocalDate());
        }
        // 2. 查询评价
        LambdaQueryWrapper<EvalTask> query = new LambdaQueryWrapper<>();
        query.eq(EvalTask::getTaskId, taskId);
        EvalTask evaluation = this.getOne(query);
        if (evaluation != null) {
            BeanUtil.copyProperties(evaluation, vo);
        }
        return vo;
    }

    /**
     * 处理数据权限
     *
     * @param dto 查询参数
     * @return 处理后的查询参数
     */
    private EvalTaskListDTO handleDataPermission(EvalTaskListDTO dto) {
        PigxUser user = getUser();
        SysUserRoleDataVo roleData = remoteRoleService.getRoleDataDetailByUserId(dto.getClientId(), user.getId(), dto.getMenuCode()).getData();
        boolean pmo = evalUserRoleService.lambdaQuery()
                .eq(EvalUserRole::getRole, EvalUserRoleEnum.PMO.getValue())
                .eq(EvalUserRole::getUserId, user.getId())
                .exists();
        dto.setAuthAll(true);
        if (Boolean.FALSE.equals(roleData.getIsAll()) && !pmo) {
            List<CostDeliverTask> authTasks = getAuthTaskIds(user, roleData, dto);
            List<Long> authTaskIds = CollStreamUtil.toList(authTasks, CostDeliverTask::getId);
            if (CollectionUtil.isEmpty(authTasks)) {
                dto.setAuthAll(false);
                return dto;
            }
            dto.setAuthTaskIdList(authTaskIds);
            dto.setAuthAll(false);
        }
        return dto;
    }

    @Override
    public Page<EvalTaskListVO> findPage(PageRequest pageRequest, EvalTaskListDTO dto) {
        //获取数据权限范围
        PigxUser user = getUser();
        dto = handleDataPermission(dto);
        if (CollectionUtil.isEmpty(dto.getAuthTaskIdList()) && !dto.getAuthAll()) {
            return Page.of(pageRequest.getPageNumber(), pageRequest.getPageSize());
        }
        List<Long> evalAuthList = new ArrayList<>();
        Page<EvalTaskListVO> voPage = baseMapper.findListByDto(Page.of(pageRequest.getPageNumber(), pageRequest.getPageSize()), dto);
        // 根据工单级别获取上级负责人
        List<EvalTaskListVO> records = voPage.getRecords();
        if (CollectionUtil.isNotEmpty(records)) {
            List<CostDeliverTask> costDeliverTasks = costDeliverTaskMapper.selectList(Wrappers.lambdaQuery(CostDeliverTask.class)
                    .in(CostDeliverTask::getId, records.stream().map(EvalTaskListVO::getTaskId).collect(Collectors.toList())));
            List<CostDeliverTask> recordList = getHigherManagerInfo(costDeliverTasks);
            if (CollectionUtils.isNotEmpty(recordList)) {
                evalAuthList = recordList.stream()
                        .filter(f -> user.getId().equals(f.getManagerId())).collect(Collectors.toList())
                        .stream().map(CostDeliverTask::getId).collect(Collectors.toList());
            }
        }
        handleAbnormalData(records, evalAuthList);
        return voPage;
    }

    @Override
    public List<EvalTaskListVO> export(EvalTaskListDTO dto) {
        dto = handleDataPermission(dto);
        if (CollectionUtil.isEmpty(dto.getAuthTaskIdList()) && !dto.getAuthAll()) {
            return ListUtil.empty();
        }
        List<EvalTaskListVO> list = baseMapper.findListByDto(dto);
        handleAbnormalData(list, ListUtil.empty());
        return list;
    }

    @Override
    public List<EvalPreTaskExportVO> preExport(EvalTaskListDTO dto) {
        dto = handleDataPermission(dto);
        if (CollectionUtil.isEmpty(dto.getAuthTaskIdList()) && !dto.getAuthAll()) {
            return ListUtil.empty();
        }
        List<EvalTaskListVO> list = baseMapper.findListByDto(dto);
        handleAbnormalData(list, ListUtil.empty());
        List<EvalPreTaskExportVO> preTaskExportVOList = list.stream().map(a -> {
            EvalPreTaskExportVO vo = new EvalPreTaskExportVO();
            BeanUtil.copyProperties(a, vo);
            return vo;
        }).collect(Collectors.toList());
        return preTaskExportVOList;
    }

    private List<CostDeliverTask> getAuthTaskIds(PigxUser user, SysUserRoleDataVo roleData, EvalTaskListDTO dto) {
        List<Long> userIdList = roleData.getUserIdList();
        userIdList.add(user.getId());
        // 获取当前用户工单
        List<CostDeliverTask> resultList = costDeliverTaskMapper.selectList(Wrappers.lambdaQuery(CostDeliverTask.class)
                .in(CostDeliverTask::getManagerId, userIdList)
                .eq(CostDeliverTask::getTaskType, dto.getTaskType()));
        // 获取我下级的工单（售后不包括下下级，售前包括下下级）
        List<Long> taskIds = CollStreamUtil.toList(resultList, CostDeliverTask::getId);
        List<CostDeliverTask> childrenList = getMyDecompositionTask(dto.getTaskType(), taskIds);
        resultList.addAll(childrenList);
        // 获取我负责的项目的一级工单
        List<CostDeliverTask> costDeliverTasks = getMyChargeProjectTasks(dto.getTaskType(), userIdList);
        resultList.addAll(costDeliverTasks);
        // 获取创建人创建的工单
        List<CostDeliverTask> createList = costDeliverTaskMapper.selectList(Wrappers.lambdaQuery(CostDeliverTask.class)
                .in(CostDeliverTask::getCreatorId, userIdList)
                .eq(CostDeliverTask::getTaskType, dto.getTaskType()));
        resultList.addAll(createList);
        return resultList;
    }

    /**
     * 根据工单级别获取上级负责人
     *
     * @param resultList
     * @return
     */
    private List<CostDeliverTask> getHigherManagerInfo(List<CostDeliverTask> resultList) {
        List<Long> parentIds = CollStreamUtil.toList(resultList, CostDeliverTask::getParentId);
        Map<Long, CostDeliverTask> parentTaskMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(parentIds)) {
            List<CostDeliverTask> parentTaskList = costDeliverTaskMapper.selectList(Wrappers.lambdaQuery(CostDeliverTask.class)
                    .in(CostDeliverTask::getId, parentIds));
            if (CollectionUtil.isNotEmpty(parentTaskList)) {
                parentTaskMap = parentTaskList.stream()
                        .collect(Collectors.toMap(CostDeliverTask::getId, a -> a, (a, b) -> a));
            }
        }
        List<Long> projectIds = CollStreamUtil.toList(resultList, CostDeliverTask::getProjectId);
        List<ProjectInfo> projectInfos = projectInfoMapper.selectList(Wrappers.lambdaQuery(ProjectInfo.class)
                .in(ProjectInfo::getId, projectIds));
        Map<Long, ProjectInfo> projectInfoMap = projectInfos.stream().collect(Collectors.toMap(ProjectInfo::getId, a -> a));
        Map<Long, CostDeliverTask> finalParentTaskMap = parentTaskMap;
        resultList.stream().forEach(task -> {
            if (task.getTaskLevel() != TASK_FIRST_LEVEL) {
                CostDeliverTask parentTask = finalParentTaskMap.get(task.getParentId());
                if (parentTask != null) {
                    task.setManagerId(parentTask.getManagerId());
                    task.setManagerName(parentTask.getManagerName());
                }
            } else {
                ProjectInfo projectInfo = projectInfoMap.get(task.getProjectId());
                if (ProjectTaskKindEnum.AFTER_SALES_DELIVERY.getValue().equals(task.getTaskType())) {
                    task.setManagerId(task.getCreatorId());
                    task.setManagerName(task.getCreator());
                } else {
                    Long preSaleUserId = projectInfo.getPreSaleUserId();
                    task.setManagerId(preSaleUserId);
                    task.setManagerName(projectInfo.getPreSaleUserName());
                }
            }
        });

        return resultList;
    }

    /**
     * 处理工单评价数据
     *
     * @param list         工单评价列表
     * @param evalAuthList 有权限评价TaskIdList
     */
    private void handleAbnormalData(List<EvalTaskListVO> list, List<Long> evalAuthList) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        list.forEach(vo -> {
            vo.setEvaluationStatusTxt(EnumUtils.getNameByValue(EvalTaskStatusEnum.class, vo.getEvaluationStatus()));
            vo.setTaskStatusTxt(EnumUtils.getNameByValue(CostTaskStatusEnum.class, vo.getTaskStatus()));
            vo.setBudgetCost(AESEncryptor.justDecrypt(vo.getBudgetCostStr()));
            vo.setActualLaborCost(AESEncryptor.justDecrypt(vo.getActualLaborCostStr()));
            vo.setSettlementValueRatio(MoneyUtil.getInstance().proportion(vo.getSettlementValueRatio()));
            if (CollectionUtil.isNotEmpty(evalAuthList)) {
                vo.setIsEvaluation(evalAuthList.contains(vo.getTaskId()));
            }
        });
    }

    /**
     * 获取当前用户信息
     *
     * @return {@link PigxUser }
     */
    private static PigxUser getUser() {
        PigxUser user = UserUtils.getUser();
        if (user == null) {
            throw new ValidationException("无操作权限");
        }
        return user;
    }

    /**
     * 获取我的下级工单
     *
     * @param taskType
     * @param taskIds
     * @return
     */
    private List<CostDeliverTask> getMyDecompositionTask(Integer taskType, List<Long> taskIds) {
        List<CostDeliverTask> childrenList = new ArrayList<>();
        if (CollUtil.isNotEmpty(taskIds) && ProjectTaskKindEnum.AFTER_SALES_DELIVERY.getValue().equals(taskType)) {
            childrenList = getChildrenByParentIds(taskIds, taskType);
        }
        if (CollUtil.isNotEmpty(taskIds) && ProjectTaskKindEnum.PRE_SALES_SUPPORT.getValue().equals(taskType)) {
            List<CostDeliverTask> secondChildrenList = getChildrenByParentIds(taskIds, taskType);
            childrenList.addAll(secondChildrenList);
            // 获取第三级子节点列表
            List<CostDeliverTask> thirdChildrenList = getChildrenByParentIds(CollStreamUtil.toList(secondChildrenList, CostDeliverTask::getId), taskType);
            if (CollUtil.isNotEmpty(thirdChildrenList)) {
                // 组装三级子节点集合
                childrenList.addAll(thirdChildrenList);
            }
        }
        return childrenList;
    }


    private List<CostDeliverTask> getChildrenByParentIds(List<Long> parentIds, Integer taskType) {
        if (CollUtil.isEmpty(parentIds)) {
            return new ArrayList<>();
        }
        return costDeliverTaskMapper.selectList(Wrappers.lambdaQuery(CostDeliverTask.class)
                .in(CostDeliverTask::getParentId, parentIds)
                .eq(CostDeliverTask::getTaskType, taskType));
    }

    /**
     * 获取我负责项目的一级工单
     *
     * @param taskType 任务类型
     * @param userIds  用户 ID
     * @return {@link List }<{@link CostDeliverTask }>
     */
    private List<CostDeliverTask> getMyChargeProjectTasks(Integer taskType, List<Long> userIds) {
        ProjectTaskKindEnum taskKindEnum = EnumUtils.getEnumByValue(ProjectTaskKindEnum.class, taskType);
        if (taskKindEnum == null) {
            return Collections.emptyList();
        }
        List<ProjectInfo> projectInfoList = projectInfoMapper.selectList(Wrappers.lambdaQuery(ProjectInfo.class)
                .in(ProjectTaskKindEnum.PRE_SALES_SUPPORT.equals(taskKindEnum), ProjectInfo::getPreSaleUserId, userIds)
                .in(ProjectTaskKindEnum.AFTER_SALES_DELIVERY.equals(taskKindEnum), ProjectInfo::getManagerUserId, userIds));
        if (CollUtil.isEmpty(projectInfoList)) {
            return Collections.emptyList();
        }
        List<Long> projectIds = projectInfoList.stream().map(ProjectInfo::getId).collect(Collectors.toList());
        return costDeliverTaskMapper.selectList(Wrappers.lambdaQuery(CostDeliverTask.class)
                .in(CostDeliverTask::getProjectId, projectIds)
                .eq(CostDeliverTask::getTaskType, taskType)
                .eq(CostDeliverTask::getTaskLevel, NumberUtils.INTEGER_ONE));
    }

    @Override
    public Page<EvalTaskDistributionAfterVO> getAfterEvalDistribution(PageRequest pageRequest, EvalTaskDistributionDTO dto) {
        List<EvalTaskDistributionAfterVO> afterVOList = new ArrayList<>();
        Page<EvalTaskDistributionVO> distributionPage = baseMapper.getEvalDistribution(Page.of(pageRequest.getPageNumber(), pageRequest.getPageSize()), dto);
        List<EvalTaskDistributionVO> distributionList = distributionPage.getRecords();
        if (CollUtil.isNotEmpty(distributionList)) {
            List<CostDeliverTask> costDeliverTasks = costDeliverTaskMapper.selectList(Wrappers.lambdaQuery(CostDeliverTask.class)
                            .eq(CostDeliverTask::getTaskStatus, CostTaskStatusEnum.YWC.getValue())
                            .eq(CostDeliverTask::getEvaluationStatus, EvalTaskStatusEnum.YPJ.getValue())
                            .eq(CostDeliverTask::getProjectId, dto.getProjectId())
                            .in(CostDeliverTask::getManagerId, distributionList.stream().map(EvalTaskDistributionVO::getManagerId).collect(Collectors.toList())))
                    .stream().map(CostDeliverTask::decrypt).collect(Collectors.toList());

            Map<Long, List<CostDeliverTask>> taskByManagerIdMap = costDeliverTasks.stream().collect(Collectors.groupingBy(CostDeliverTask::getManagerId));
            afterVOList = distributionList.stream().map(item -> {
                EvalTaskDistributionAfterVO vo = BeanUtil.copyProperties(item, EvalTaskDistributionAfterVO.class);
                vo.setPersonalLevel(EnumUtils.getNameByValue(EvalLevelEnum.class, vo.getPersonalLevelInt()));
                afterTaskVO(taskByManagerIdMap, vo);
                return vo;
            }).collect(Collectors.toList());
        }
        return PageUtils.page(afterVOList, pageRequest);
    }

    @Override
    public List<EvalTaskDistributionAfterVO> exportAfterEvalDistribution(EvalTaskDistributionDTO dto) {
        List<EvalTaskDistributionAfterVO> afterVOList = new ArrayList<>();
        List<EvalTaskDistributionVO> distributionList = baseMapper.getEvalDistribution(dto);
        if (CollUtil.isEmpty(distributionList)) {
            return afterVOList;
        }
        List<CostDeliverTask> costDeliverTasks = costDeliverTaskMapper.selectList(Wrappers.lambdaQuery(CostDeliverTask.class)
                        .eq(CostDeliverTask::getTaskStatus, CostTaskStatusEnum.YWC.getValue())
                        .eq(CostDeliverTask::getEvaluationStatus, EvalTaskStatusEnum.YPJ.getValue())
                        .eq(CostDeliverTask::getProjectId, dto.getProjectId())
                        .in(CostDeliverTask::getManagerId, distributionList.stream().map(EvalTaskDistributionVO::getManagerId).collect(Collectors.toList())))
                .stream().map(CostDeliverTask::decrypt).collect(Collectors.toList());

        Map<Long, List<CostDeliverTask>> taskByManagerIdMap = costDeliverTasks.stream().collect(Collectors.groupingBy(CostDeliverTask::getManagerId));
        afterVOList = distributionList.stream().map(item -> {
            EvalTaskDistributionAfterVO vo = BeanUtil.copyProperties(item, EvalTaskDistributionAfterVO.class);
            vo.setPersonalLevel(EnumUtils.getNameByValue(EvalLevelEnum.class, vo.getPersonalLevelInt()));
            vo.setPersonalScore(null != item.getAdjustedScore() ? item.getAdjustedScore() : item.getPersonalScore());
            afterTaskVO(taskByManagerIdMap, vo);
            return vo;
        }).collect(Collectors.toList());

        return afterVOList;
    }

    /**
     * 售后工单评价分布vo工单数据组装
     */
    public void afterTaskVO(Map<Long, List<CostDeliverTask>> taskByManagerIdMap, EvalTaskDistributionAfterVO vo) {
        List<CostDeliverTask> tasks = taskByManagerIdMap.get(vo.getManagerId());
        BigDecimal income = BigDecimal.ZERO;
        BigDecimal budgetCost = BigDecimal.ZERO;
        BigDecimal actualLaborCost = BigDecimal.ZERO;
        BigDecimal estimatedHours = BigDecimal.ZERO;
        BigDecimal actualHours = BigDecimal.ZERO;
        if (CollUtil.isNotEmpty(tasks)) {
            income = tasks.stream()
                    .map(CostDeliverTask::getIncome)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            budgetCost = tasks.stream()
                    .map(CostDeliverTask::getBudgetCost)
                    .filter(StrUtil::isNotEmpty)
                    .map(BigDecimal::new)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            actualLaborCost = tasks.stream()
                    .map(CostDeliverTask::getActualLaborCost)
                    .filter(StrUtil::isNotEmpty)
                    .map(BigDecimal::new)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            estimatedHours = tasks.stream()
                    .map(CostDeliverTask::getEstimatedHours)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal normalHours = tasks.stream()
                    .map(CostDeliverTask::getNormalHours)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal workOvertimeHours = tasks.stream()
                    .map(CostDeliverTask::getWorkOvertimeHours)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal restOvertimeHours = tasks.stream()
                    .map(CostDeliverTask::getRestOvertimeHours)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal holidayOvertimeHours = tasks.stream()
                    .map(CostDeliverTask::getHolidayOvertimeHours)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            actualHours = normalHours.add(workOvertimeHours).add(restOvertimeHours).add(holidayOvertimeHours);
        }
        vo.setIncome(income);
        vo.setBudgetCost(budgetCost);
        vo.setActualLaborCost(actualLaborCost);
        vo.setEstimatedHours(estimatedHours);
        vo.setActualHours(actualHours);
    }

    @Override
    public Page<EvalTaskDistributionAfterDetailVO> getAfterEvalDistributionDetail(PageRequest pageRequest, EvalTaskDistributionDTO dto) {
        Page<EvalTaskDistributionAfterDetailVO> page = Page.of(pageRequest.getPageNumber(), pageRequest.getPageSize());
        Page<EvalTaskDistributionAfterDetailVO> afterDetailPage = baseMapper.getAfterEvalDistributionDetail(page, dto);
        if (CollectionUtil.isNotEmpty(afterDetailPage.getRecords())) {
            afterDetailPage.getRecords().stream().forEach(item -> {
                item.setBudgetCost(AESEncryptor.justDecrypt(item.getBudgetCost()));
                item.setActualLaborCost(AESEncryptor.justDecrypt(item.getActualLaborCost()));
                item.setTaskStatusTxt(EnumUtils.getNameByValue(CostTaskStatusEnum.class, item.getTaskStatus()));
                item.setSettlementValueRatio(MoneyUtil.getInstance().proportion(item.getSettlementValueRatio()));
            });
        }
        return afterDetailPage;
    }

    @Override
    public Page<EvalTaskDistributionPreVO> getPreEvalDistribution(PageRequest pageRequest, EvalTaskDistributionDTO dto) {
        List<EvalTaskDistributionPreVO> preVOList = new ArrayList<>();
        Page<EvalTaskDistributionVO> distributionPage = baseMapper.getEvalDistribution(Page.of(pageRequest.getPageNumber(), pageRequest.getPageSize()), dto);
        List<EvalTaskDistributionVO> distributionList = distributionPage.getRecords();
        if (CollUtil.isNotEmpty(distributionList)) {
            Table<Long, Integer, List<CostTaskDailyPaperEntry>> paperEntryTable = HashBasedTable.create();

            List<CostTaskDailyPaperEntry> taskDailyPaperEntries = costTaskDailyPaperEntryMapper.selectList(Wrappers.lambdaQuery(CostTaskDailyPaperEntry.class)
                            .eq(CostTaskDailyPaperEntry::getProjectId, dto.getProjectId())
                            .in(CostTaskDailyPaperEntry::getUserId, distributionList.stream().map(EvalTaskDistributionVO::getManagerId).collect(Collectors.toList())))
                    .stream().map(CostTaskDailyPaperEntry::decrypt).collect(Collectors.toList());
            taskDailyPaperEntries.stream().forEach(d -> {
                List<CostTaskDailyPaperEntry> exists = paperEntryTable.get(d.getUserId(), d.getApprovalStatus());
                if (CollectionUtil.isEmpty(exists)) {
                    paperEntryTable.put(d.getUserId(), d.getApprovalStatus(), Lists.newArrayList(d));
                } else {
                    exists.add(d);
                }
            });
            preVOList = distributionList.stream().map(item -> {
                EvalTaskDistributionPreVO preVO = BeanUtil.copyProperties(item, EvalTaskDistributionPreVO.class);
                preVO.setPersonalLevel(EnumUtils.getNameByValue(EvalLevelEnum.class, item.getPersonalLevelInt()));
                preTaskVo(paperEntryTable, preVO);
                return preVO;
            }).collect(Collectors.toList());
        }
        return PageUtils.page(preVOList, pageRequest);
    }

    @Override
    public List<EvalTaskDistributionPreVO> exportPreEvalDistribution(EvalTaskDistributionDTO dto) {
        List<EvalTaskDistributionPreVO> preVOList = new ArrayList<>();
        List<EvalTaskDistributionVO> distributionList = baseMapper.getEvalDistribution(dto);
        if (CollUtil.isEmpty(distributionList)) {
            return preVOList;
        }
        Table<Long, Integer, List<CostTaskDailyPaperEntry>> paperEntryTable = HashBasedTable.create();

        List<CostTaskDailyPaperEntry> taskDailyPaperEntries = costTaskDailyPaperEntryMapper.selectList(Wrappers.lambdaQuery(CostTaskDailyPaperEntry.class)
                        .eq(CostTaskDailyPaperEntry::getProjectId, dto.getProjectId())
                        .in(CostTaskDailyPaperEntry::getUserId, distributionList.stream().map(EvalTaskDistributionVO::getManagerId).collect(Collectors.toList())))
                .stream().map(CostTaskDailyPaperEntry::decrypt).collect(Collectors.toList());
        taskDailyPaperEntries.stream().forEach(d -> {
            List<CostTaskDailyPaperEntry> exists = paperEntryTable.get(d.getUserId(), d.getApprovalStatus());
            if (CollectionUtil.isEmpty(exists)) {
                paperEntryTable.put(d.getUserId(), d.getApprovalStatus(), Lists.newArrayList(d));
            } else {
                exists.add(d);
            }
        });
        preVOList = distributionList.stream().map(item -> {
            EvalTaskDistributionPreVO preVO = BeanUtil.copyProperties(item, EvalTaskDistributionPreVO.class);
            preVO.setPersonalScore(null != item.getAdjustedScore() ? item.getAdjustedScore() : item.getPersonalScore());
            preVO.setPersonalLevel(EnumUtils.getNameByValue(EvalLevelEnum.class, item.getPersonalLevelInt()));
            preTaskVo(paperEntryTable, preVO);
            return preVO;
        }).collect(Collectors.toList());

        return preVOList;
    }

    public void preTaskVo(Table<Long, Integer, List<CostTaskDailyPaperEntry>> paperEntryTable, EvalTaskDistributionPreVO preVO) {
        BigDecimal actualLaborCost = BigDecimal.ZERO;
        BigDecimal auditedHours = BigDecimal.ZERO;
        BigDecimal unAuditedHours = BigDecimal.ZERO;
        List<CostTaskDailyPaperEntry> ytgEntries = paperEntryTable.get(preVO.getManagerId(), ApprovalStatusEnum.YTG.getValue());
        if (CollUtil.isNotEmpty(ytgEntries)) {
            BigDecimal normalHours = ytgEntries.stream()
                    .map(CostTaskDailyPaperEntry::getNormalHours)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal addedHours = ytgEntries.stream()
                    .map(CostTaskDailyPaperEntry::getAddedHours)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            actualLaborCost = ytgEntries.stream()
                    .map(CostTaskDailyPaperEntry::getActualLaborCost)
                    .filter(StrUtil::isNotEmpty)
                    .map(BigDecimal::new)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            auditedHours = normalHours.add(addedHours);
        }
        List<CostTaskDailyPaperEntry> dshEntries = paperEntryTable.get(preVO.getManagerId(), ApprovalStatusEnum.DSH.getValue());
        if (CollUtil.isNotEmpty(dshEntries)) {
            BigDecimal normalHours = dshEntries.stream()
                    .map(CostTaskDailyPaperEntry::getNormalHours)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal addedHours = dshEntries.stream()
                    .map(CostTaskDailyPaperEntry::getAddedHours)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            unAuditedHours = normalHours.add(addedHours);
        }
        preVO.setUnAuditedHours(unAuditedHours);
        preVO.setActualLaborCost(actualLaborCost);
        preVO.setAuditedHours(auditedHours);
    }

    @Override
    public Page<EvalTaskDistributionPreDetailVO> getPreEvalDistributionDetail(PageRequest pageRequest, EvalTaskDistributionDTO dto) {
        Page<EvalTaskDistributionPreDetailVO> page = Page.of(pageRequest.getPageNumber(), pageRequest.getPageSize());
        Page<EvalTaskDistributionPreDetailVO> preDetailPage = baseMapper.getPreEvalDistributionDetail(page, dto);
        if (CollectionUtil.isNotEmpty(preDetailPage.getRecords())) {
            preDetailPage.getRecords().stream().forEach(item -> {
                item.setActualLaborCost(AESEncryptor.justDecrypt(item.getActualLaborCost()));
                item.setTaskStatusTxt(EnumUtils.getNameByValue(CostTaskStatusEnum.class, item.getTaskStatus()));
            });
        }
        return preDetailPage;
    }

    @Override
    public EvalTaskCalibrationDetailVO getCalibrationDetail(EvalTaskDistributionDTO dto) {
        EvalTaskCalibration evalTaskCalibration = evalTaskCalibrationMapper.selectOne(Wrappers.<EvalTaskCalibration>lambdaQuery()
                .eq(EvalTaskCalibration::getProjectId, dto.getProjectId())
                .eq(EvalTaskCalibration::getEmployeeId, dto.getManagerId())
                .eq(EvalTaskCalibration::getTaskType, dto.getTaskType())
        );

        EvalTaskCalibrationDetailVO vo = BeanUtil.copyProperties(evalTaskCalibration, EvalTaskCalibrationDetailVO.class);
        vo.setCurrentLevelTxt(EnumUtils.getNameByValue(EvalLevelEnum.class, evalTaskCalibration.getCurrentLevel()));
        vo.setAdjustedLevelTxt(EnumUtils.getNameByValue(EvalLevelEnum.class, evalTaskCalibration.getAdjustedLevel()));
        return vo;
    }

    @Override
    public void calibration(EvalTaskCalibrationDTO dto) {
        EvalTaskCalibration evalTaskCalibration = evalTaskCalibrationMapper.selectOne(Wrappers.<EvalTaskCalibration>lambdaQuery()
                .eq(EvalTaskCalibration::getProjectId, dto.getProjectId())
                .eq(EvalTaskCalibration::getEmployeeId, dto.getEmployeeId())
                .eq(EvalTaskCalibration::getTaskType, dto.getTaskType())
        );
        if (evalTaskCalibration == null) {
            throw new ServiceException("数据不存在");
        }
        BeanUtil.copyProperties(dto, evalTaskCalibration);
        evalTaskCalibration.setAdjustedLevel(EnumUtils.getValueByName(EvalLevelEnum.class, dto.getAdjustedLevelTxt()));
        //计算排名（单项目评分进行倒序排列，得分相同的比较结算工单产值，结算工单产值越大越靠前）
        BaseBuildEntityUtil.buildUpdate(evalTaskCalibration);
        evalTaskCalibrationMapper.updateById(evalTaskCalibration);
        //计算排名
        countRank(dto.getProjectId(), dto.getTaskType());
    }

    @Override
    public EvalProjectOverallStaffVO getProjectOverallDistribution(Long projectId) {
        EvalProjectOverallStaffVO.EvalProjectOverallStaffVOBuilder staffVOBuilder = EvalProjectOverallStaffVO.builder()
                .projectId(projectId);
        //查询项目评价详情
        EvalProjectOverviewVO evalProjectOverviewVO = IEvalProjectOverviewService.findEvalProjectOverviewByProjectId(projectId);
        if (null == evalProjectOverviewVO || null == evalProjectOverviewVO.getTotalScore()) {
            return staffVOBuilder
                    .distributionList(Collections.emptyList())
                    .build();
        }
        //获取项目整体得分
        BigDecimal totalScore = evalProjectOverviewVO.getTotalScore();
        //查询项目整体评价分布范围
        List<EvalProjectOverallDistribution> distributionsList = evalProjectOverallDistributionMapper.selectList(Wrappers.<EvalProjectOverallDistribution>lambdaQuery()
                .le(EvalProjectOverallDistribution::getOverallStartingScore, totalScore)
                .gt(EvalProjectOverallDistribution::getOverallEndScore, totalScore));
        //查询工单评价人员分级分布
        EvalTaskDistributionDTO dto = EvalTaskDistributionDTO.builder()
                .projectId(projectId)
                .build();
        List<EvalTaskDistributionVO> distributionList = baseMapper.getEvalDistribution(dto);
        if (CollectionUtil.isEmpty(distributionList)) {
            return staffVOBuilder
                    .totalScore(totalScore)
                    .projectGrade(distributionsList.get(0).getProjectOverallGrade())
                    .build();
        }
        distributionList.stream().forEach(item -> {
            item.setPersonalLevel(EnumUtils.getNameByValue(EvalLevelEnum.class, item.getPersonalLevelInt()));
        });
        //人员评分等级分组
        Map<String, List<EvalTaskDistributionVO>> personalLevelMap = distributionList.stream().collect(Collectors.groupingBy(EvalTaskDistributionVO::getPersonalLevel));

        //项目人员整体评价分布明细组装
        List<EvalProjectOverallStaffDistributionVO> distributionVOList = distributionsList.stream().map(item -> {
            List<EvalTaskDistributionVO> evalTaskDistributionVOS = personalLevelMap.getOrDefault(item.getEvalGrade(), Collections.emptyList());
            BigDecimal currentRange = BigDecimal.valueOf(evalTaskDistributionVOS.size()).divide(BigDecimal.valueOf(distributionList.size()), 2, RoundingMode.HALF_UP);
            return EvalProjectOverallStaffDistributionVO.builder()
                    .evalGrade(item.getEvalGrade())
                    .startRange(MoneyUtil.getInstance().proportionInt(item.getStartingRange().toPlainString()))
                    .endRange(MoneyUtil.getInstance().proportionInt(item.getEndRange().toPlainString()))
                    .currentRange(MoneyUtil.getInstance().proportionInt(currentRange.toPlainString())).build();
        }).collect(Collectors.toList());
        //获取退回原因
        List<EvalTaskCalibration> calibrationList = evalTaskCalibrationMapper.selectList(Wrappers.<EvalTaskCalibration>lambdaQuery()
                .eq(EvalTaskCalibration::getProjectId, projectId));
        if (CollectionUtil.isNotEmpty(calibrationList)) {
            EvalTaskCalibration evalTaskCalibration = calibrationList.get(0);
            staffVOBuilder.returnReason(evalTaskCalibration.getReturnReason());
        }
        return staffVOBuilder
                .totalScore(totalScore)
                .projectGrade(distributionsList.get(0).getProjectOverallGrade())
                .evalStatus(evalProjectOverviewVO.getEvalStatus())
                .evalStatusName(EnumUtils.getNameByValue(EvalStatusEnum.class, evalProjectOverviewVO.getEvalStatus()))
                .distributionList(distributionVOList)
                .build();
    }

    @Override
    public void changeCalibrationStatus(EvalTaskCalibrationDTO dto) {
        Long projectId = dto.getProjectId();
        List<EvalTaskCalibration> evalTaskCalibrationList = evalTaskCalibrationMapper.selectList(Wrappers.<EvalTaskCalibration>lambdaQuery()
                .eq(EvalTaskCalibration::getProjectId, projectId));
        if (CollectionUtil.isEmpty(evalTaskCalibrationList)) {
            throw new ServiceException("数据不存在");
        }

        // 通过时校验项目经理评分是否完成
        if (dto.getCalibrationStatus() == 2) {
            List<EvalProjectManager> evalProjectManagers = evalProjectManagerMapper.selectList(Wrappers.<EvalProjectManager>lambdaQuery()
                    .eq(EvalProjectManager::getProjectId, projectId)
                    .eq(EvalProjectManager::getDelFlag, YesOrNoEnum.NO.getValue()));
            boolean score = CollUtil.emptyIfNull(evalProjectManagers).stream()
                    .anyMatch(item -> item.getPmoScore() != null && item.getManagerScore() != null);
            if (!score) {
                throw new ServiceException("项目经理评分未完成");
            }
        }

        evalTaskCalibrationList.stream().forEach(item -> {
            item.setCalibrationStatus(dto.getCalibrationStatus());
            if (dto.getCalibrationStatus() == 0) {
                item.setReturnReason(dto.getReturnReason());
            }
            BaseBuildEntityUtil.buildUpdate(item);
        });
        evalTaskCalibrationMapper.batchUpdate(evalTaskCalibrationList);

        //发送消息
        changeCalibrationStatusSendMsg(dto);
        // 通过后归档项目评价
        if (dto.getCalibrationStatus() == 2) {
            IEvalProjectOverviewService.archiveEval(projectId);
        } else if (dto.getCalibrationStatus() == 0) {
            EvalProjectOverview evalProjectOverview = evalProjectOverviewMapper.getByProjectId(projectId);
            if (null != evalProjectOverview) {
                evalProjectOverview.setEvalStatus(EvalStatusEnum.CALIBRATE.getValue());
                BaseBuildEntityUtil.buildUpdate(evalProjectOverview);
                evalProjectOverviewMapper.updateById(evalProjectOverview);
            }
        } else if (dto.getCalibrationStatus() == 1) {
            EvalProjectOverview evalProjectOverview = evalProjectOverviewMapper.getByProjectId(projectId);
            if (null != evalProjectOverview) {
                evalProjectOverview.setEvalStatus(EvalStatusEnum.PMO_APPROVAL.getValue());
                BaseBuildEntityUtil.buildUpdate(evalProjectOverview);
                evalProjectOverviewMapper.updateById(evalProjectOverview);
            }
        }
    }

    /**
     * 计算排名
     *
     * @param projectId
     * @param taskType
     * @return
     */
    public void countRank(Long projectId, Integer taskType) {
        //查询项目工单评价排名计算
        List<ProjectEvalRankVO> projectEvalRankList = evalTaskCalibrationMapper.getProjectEvalRankList(projectId, taskType);
        Map<Long, ProjectEvalRankVO> rankVOMap = projectEvalRankList.stream().collect(Collectors.toMap(ProjectEvalRankVO::getEmployeeId, a -> a, (a, b) -> a));

        List<EvalTaskCalibration> evalTaskCalibrations = evalTaskCalibrationMapper.selectList(Wrappers.<EvalTaskCalibration>lambdaQuery()
                .eq(EvalTaskCalibration::getProjectId, projectId)
                .eq(EvalTaskCalibration::getTaskType, taskType));
        evalTaskCalibrations.stream().forEach(item -> {
            ProjectEvalRankVO rankVO = rankVOMap.get(item.getEmployeeId());
            item.setRank(projectEvalRankList.indexOf(rankVO) + 1);
        });
        evalTaskCalibrationMapper.batchUpdate(evalTaskCalibrations);
    }

    @Override
    public void taskSendAbnormalMsg() {
        List<CostDeliverTask> costDeliverTasks = costDeliverTaskMapper.selectList(Wrappers.<CostDeliverTask>lambdaQuery()
                .in(CostDeliverTask::getTaskStatus, CostTaskStatusEnum.YWC.getValue(), CostTaskStatusEnum.YJFZRYTG.getValue(), CostTaskStatusEnum.EJFZRYTG.getValue(), CostTaskStatusEnum.JS.getValue())
                .eq(CostDeliverTask::getEvaluationStatus, EvalTaskStatusEnum.DPJ.getValue())
                .eq(CostDeliverTask::getDisassemblyType, CostTaskDisassemblyTypeEnum.STANDARD_WORK_ORDER.getValue())
                .isNotNull(CostDeliverTask::getActualLaborCost));

        if (CollectionUtil.isEmpty(costDeliverTasks)) {
            return;
        }
        List<BaseSendMsgDTO> noticeMsgList = new ArrayList<>();
        //根据工单级别获取上级负责人
        getHigherManagerInfo(costDeliverTasks);
        //根据评价人分组
        Map<Long, List<CostDeliverTask>> tasksMapByManagerId = costDeliverTasks.stream()
                .filter(item -> item.getManagerId() != null)
                .collect(Collectors.groupingBy(CostDeliverTask::getManagerId));
        //组装消息推送列表
        tasksMapByManagerId.keySet().stream().forEach(managerId -> {
            List<CostDeliverTask> tasks = tasksMapByManagerId.get(managerId);
            String content = String.format("您好，您目前存在%d个未评价的工单，请及时评价～",
                    tasks.size());
            final String title = "工单未评价提醒";
            BaseSendMsgDTO baseSendMsgDTO = new BaseSendMsgDTO()
                    .setTitle(title)
                    .setContent(content)
                    .setMsgTypeEnum(MsgTypeEnum.NOTICE_MSG)
                    .setTargetTypeEnum(TargetTypeEnum.USERS)
                    .setChannelEnums(CollUtil.newLinkedHashSet(ChannelEnum.WECOM, ChannelEnum.MAIL))
                    .setPath("/view-businesses/cooperate-office/work-order-evaluation-manage/work-order-evaluation/index")
                    .populateSender()
                    .toOneTarget(managerId, tasks.get(0).getManagerName());
            noticeMsgList.add(baseSendMsgDTO);
        });
        bcpMessageService.batchSendMsg(noticeMsgList);
    }

    @Override
    public void deleteEvaluation(Long taskId) {
        CostDeliverTask task = costDeliverTaskMapper.selectById(taskId);
        if (null == task) {
            throw new ServiceException("工单不存在");
        }
        EvalTask evalTask = baseMapper.selectOne(Wrappers.<EvalTask>lambdaQuery().eq(EvalTask::getTaskId, taskId));
        if (evalTask != null) {
            baseMapper.delById(evalTask.getId());
            //更新工单评价校准表
            countComprehensiveScore(task);
        }
    }


    public void changeCalibrationStatusSendMsg(EvalTaskCalibrationDTO dto) {
        ProjectInfo projectInfo = projectInfoMapper.selectById(dto.getProjectId());
        List<BaseSendMsgDTO> noticeMsgList = new ArrayList<>();
        String content = StrUtil.EMPTY;
        String title = StrUtil.EMPTY;
        Long managerId = NumberUtils.LONG_ZERO;
        String mangerName = StrUtil.EMPTY;
        if (dto.getCalibrationStatus() == 0) {
            content = StrUtil.format("您好，{}项目的工单评价得分审核被回退，请及时前往调整～",
                    projectInfo.getItemName());
            title = "工单评价审核回退提醒";
            managerId = projectInfo.getManagerUserId();
            mangerName = projectInfo.getManagerUserName();
        } else if (dto.getCalibrationStatus() == 1) {
            content = StrUtil.format("您好，{}项目的工单评价得分已提交，需要您进行审核，请及时前往～",
                    projectInfo.getItemName());
            title = "工单评价审核提醒";
            EvalUserRole pmo = evalUserRoleService.getOne(Wrappers.<EvalUserRole>lambdaQuery()
                    .eq(EvalUserRole::getRole, EvalUserRoleEnum.PMO.getValue()));
            if (pmo != null) {
                managerId = pmo.getUserId();
                mangerName = pmo.getUserName();
            }
        }

        String url = StrUtil.format("/view-businesses/project-manage/project-manage-list?projectId={}&detailTab=PROJECT_DETAIL_EVALUATE&detailSubTab=overall", dto.getProjectId());

        BaseSendMsgDTO baseSendMsgDTO = new BaseSendMsgDTO()
                .setTitle(title)
                .setContent(content)
                .setMsgTypeEnum(MsgTypeEnum.NOTICE_MSG)
                .setTargetTypeEnum(TargetTypeEnum.USERS)
                .setChannelEnums(CollUtil.newLinkedHashSet(ChannelEnum.WECOM, ChannelEnum.MAIL))
                .setPath(url)
                .populateSender()
                .toOneTarget(managerId, mangerName);
        noticeMsgList.add(baseSendMsgDTO);

        bcpMessageService.batchSendMsg(noticeMsgList);
    }

    /**
     * 更新工单评价校准表
     *
     * @param task
     */
    public void countComprehensiveScore(CostDeliverTask task) {
        EvalTaskCalibration evalTaskCalibration = evalTaskCalibrationMapper.selectOne(Wrappers.<EvalTaskCalibration>lambdaQuery()
                .eq(EvalTaskCalibration::getProjectId, task.getProjectId())
                .eq(EvalTaskCalibration::getEmployeeId, task.getManagerId())
                .eq(EvalTaskCalibration::getTaskType, task.getTaskType()));
        //计算项目人员综合得分
        ProjectEvalRankVO projectEvalRankVO = baseMapper.countComprehensiveScore(task.getProjectId(), task.getManagerId());
        if (null != evalTaskCalibration) {
            if (null == projectEvalRankVO) {
                evalTaskCalibrationMapper.delById(evalTaskCalibration.getId());
            } else {
                evalTaskCalibration.setCurrentScore(projectEvalRankVO.getComprehensiveScore());
                evalTaskCalibration.setCurrentLevel(projectEvalRankVO.getCurrentLevel());
                BaseBuildEntityUtil.buildUpdate(evalTaskCalibration);
                evalTaskCalibrationMapper.updateById(evalTaskCalibration);
            }
        } else {
            EvalTaskCalibration calibration = EvalTaskCalibration.builder()
                    .projectId(task.getProjectId())
                    .taskType(task.getTaskType())
                    .employeeId(task.getManagerId())
                    .employeeName(task.getManagerName())
                    .currentScore(projectEvalRankVO.getComprehensiveScore())
                    .currentLevel(projectEvalRankVO.getCurrentLevel())
                    .build();
            BaseBuildEntityUtil.buildInsert(calibration);
            evalTaskCalibrationMapper.insert(calibration);
        }
        //4. 计算排名
        countRank(task.getProjectId(), task.getTaskType());
    }

}
