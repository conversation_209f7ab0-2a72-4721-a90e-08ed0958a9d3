package com.gok.pboot.pms.eval.entity.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import com.gok.pboot.pms.eval.enums.EvalSatisfactionResultEnum;
import com.gok.pboot.pms.eval.enums.EvalSatisfactionSurveyStatusEnum;
import com.gok.pboot.pms.eval.enums.EvalSendStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 客户满意度调查实体类
 *
 * <AUTHOR>
 * @date 2025/05/08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("eval_customer_satisfaction_survey")
public class EvalCustomerSatisfactionSurvey extends BeanEntity<Long> {

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 干系人ID
     */
    private Long stakeholderId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 联系方式
     */
    private String contactPhone;

    /**
     * 发送状态（0=未发送，1=已发送，2=发送失败）
     *
     * @see EvalSendStatusEnum
     */
    private Integer sendStatus;

    /**
     * 调查评价日期
     */
    private LocalDate surveyDate;

    /**
     * 调查下发日期
     */
    private LocalDate releaseDate;

    /**
     * 得分（取平均分）
     */
    private BigDecimal totalScore;

    /**
     * 评价状态（0=未评价,1=已评价,2=超期未评价）
     *
     * @see EvalSatisfactionSurveyStatusEnum
     */
    private Integer evalStatus;

    /**
     * 问题响应结果（5=非常满意,4=满意,3=一般,2=不满意,1=非常不满意,0=未提供服务）
     *
     * @see EvalSatisfactionResultEnum
     */
    private Integer problemResponseResult;

    /**
     * 方案设计结果
     *
     * @see EvalSatisfactionResultEnum
     */
    private Integer designSchemeResult;

    /**
     * 进度控制结果
     *
     * @see EvalSatisfactionResultEnum
     */
    private Integer progressControlResult;

    /**
     * 质量控制结果
     *
     * @see EvalSatisfactionResultEnum
     */
    private Integer qualityControlResult;

    /**
     * 服务态度与沟通结果
     *
     * @see EvalSatisfactionResultEnum
     */
    private Integer serviceAttitudeCommunicationResult;

    /**
     * 其他建议
     */
    private String otherSuggestion;

    /**
     * 邮箱
     */
    @TableField(exist = false)
    private String email;

} 