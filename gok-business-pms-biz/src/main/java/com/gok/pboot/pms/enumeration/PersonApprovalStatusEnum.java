package com.gok.pboot.pms.enumeration;

/**
 * 人才复用审核状态枚举
 * <AUTHOR>
 * @create 2023/2/9
 */
public enum PersonApprovalStatusEnum implements ValueEnum<Integer> {
    /**
     * 不通过
     */
    BTG(1, "不通过"),
    /**
     * 待审核
     */
    DSH(2, "待审核"),
    /**
     * 审核通过
     */
    YTG(3, "审核通过");
    //值
    private Integer  value;
    //名称
    private String name;

    PersonApprovalStatusEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    /**
     * 获取值
     *
     * @return Integer
     */
    @Override
    public Integer getValue() {
        return value;
    }

    /**
     * 获取名称
     *
     * @return String
     */
    @Override
    public String getName() {
        return name;
    }

    public static String getNameByVal(Integer value) {
        for (PersonApprovalStatusEnum statusEnum : PersonApprovalStatusEnum.values()) {
            if (statusEnum.value.equals(value)) {
                return statusEnum.name;
            }
        }
        return "";
    }
    public static Integer getValByName(String name) {
        for (PersonApprovalStatusEnum statusEnum : PersonApprovalStatusEnum.values()) {
            if (statusEnum.getName().equals(name)) {
                return statusEnum.getValue();
            }
        }
        return null;
    }

    public static PersonApprovalStatusEnum getApprovalStatusEnum(Integer value) {
        for (PersonApprovalStatusEnum statusEnum : PersonApprovalStatusEnum.values()) {
            if (statusEnum.value.equals(value)) {
                return statusEnum;
            }
        }
        return null;
    }
}
