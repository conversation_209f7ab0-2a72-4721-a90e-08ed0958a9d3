package com.gok.pboot.pms.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.data.datascope.BusinessDataScope;
import com.gok.components.data.datascope.DataScope;
import com.gok.pboot.pms.Util.PageAdapter;
import com.gok.pboot.pms.common.base.MapperHandler;
import com.gok.pboot.pms.common.join.DailyPapersStatisticMonthly;
import com.gok.pboot.pms.entity.DailyPaper;
import com.gok.pboot.pms.entity.dto.DailyFindPageDTO;
import com.gok.pboot.pms.entity.vo.DailyExcelExportVO;
import com.gok.pboot.pms.entity.vo.DailyFindPageVO;
import com.gok.pboot.pms.entity.vo.DailyPaperVO;
import com.gok.pboot.pms.entity.vo.TaskUserInfoVO;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import javax.annotation.Nullable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * - 日报Mapper -
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/8/23 15:02
 */
@Mapper
public interface DailyPaperMapper extends MapperHandler<DailyPaper> {

    /**
     * 分页查询工时一览表前置
     *
     * @param
     * @return
     */
    List<TaskUserInfoVO> dailyFindPageFront(@Param("dailyFindPageDTO") DailyFindPageDTO dailyFindPageDTO);


    /**
     * 分页查询工时一览表
     *
     * @param
     * @return
     */
    List<DailyFindPageVO> dailyFindPage(@Param("adapter") PageAdapter adapter, @Param("dailyFindPageDTO") DailyFindPageDTO dailyFindPageDTO);

    /**
     * 分页查询工时一览表总数
     *
     * @param
     * @return
     */
    List<Long> dailyFindPageCount(@Param("dailyFindPageDTO") DailyFindPageDTO dailyFindPageDTO);

    /**
     * ~ 根据提交时间查询日报 ~
     *
     * @param submissionDate 提交时间
     * @param userId         用户ID
     * @return java.util.List<com.gok.pboot.pms.entity.DailyPaper>
     * <AUTHOR>
     * @date 2022/8/25 10:53
     */
    List<DailyPaper> findBySubmissionDateAndUserId(
            @Param("submissionDate")
                    LocalDate submissionDate,
            @Param("userId")
                    Long userId
    );

    /**
     * 根据提交时间和用户ID查询日报ID
     * @param submissionDate 提交时间
     * @param userId 用户ID
     * @return 日报ID
     */
    Long findIdBySubmissionDateAndUserId(
            @Param("submissionDate") LocalDate submissionDate,
            @Param("userId") Long userId
    );

    /**
     * ~ 根据提交日期和用户ID列表查询 ~
     *
     * @param submissionDate 提交时间
     * @param userIds        用户ID列表
     * @return java.util.List<com.gok.pboot.pms.entity.DailyPaper>
     * <AUTHOR>
     * @date 2022/9/16 15:51
     */
    List<DailyPaper> findBySubmissionDateAndUserIds(
            @Param("submissionDate")
                    LocalDate submissionDate,
            @Param("userIds")
                    List<Long> userIds
    );

    List<DailyPaper> findByDailyFindParams(
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate,
            @Nullable @Param("name") String name,
            @Nullable @Param("personnelStatus") Integer personnelStatus,
            @Nullable @Param("userIds") Collection<Long> userIds,
            @Nullable @Param("deptIds") Collection<Long> deptIds,
            @Nullable @Param("filterUserIds") Collection<Long> filterUserIds
    );

    List<DailyPaper> findBySubmissionDateRangeAndUserIdsDataScope(
            @Param("startDate")
                    LocalDate startDate,
            @Param("endDate")
                    LocalDate endDate,
            @Param("userIds")
                    List<Long> userIds,
            DataScope dataScope
    );

    /**
     * ~ 根据提交时间和用户ID查找距离提交时间之前最近的一个日报 ~
     *
     * @param submissionDate 提交时间
     * @param userId         用户ID
     * @return java.util.List<com.gok.pboot.pms.entity.DailyPaper>
     * <AUTHOR>
     * @date 2022/9/7 11:34
     */
    DailyPaper findOneBeforeBySubmissionDateAndUserId(
            @Param("submissionDate")
                    LocalDate submissionDate,
            @Param("userId")
                    Long userId
    );

    /**
     * ~ 根据提交日期范围查询 ~
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return java.util.List<com.gok.pboot.pms.entity.DailyPaper>
     * <AUTHOR>
     * @date 2022/8/26 12:01
     */
    List<DailyPaperVO> findBySubmissionDateRange(
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate,
            @Param("userId") Long userId,
            @Nullable @Param("projectNameLike") String projectNameLike,
            @Nullable @Param("approvalStatusTab") Integer approvalStatusTab,
            @Nullable @Param("approvalStatusList") Collection<Integer> approvalStatusList
    );

    /**
     * ~ 按月份查询日报填报统计数据（多条目去重有BUG，已废弃） ~
     *
     * @param startDate 日期
     * @return com.gok.pboot.pms.common.join.DailyPapersStatisticMonthly
     * <AUTHOR>
     * @date 2022/9/5 17:30
     */
    @Deprecated
    DailyPapersStatisticMonthly findStatisticBySubmissionDateRange(
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate,
            @Param("userId") Long userId,
            @Param("projectName") String projectName
    );

    /**
     * ~ 批量保存 ~
     *
     * @param list 列表
     * <AUTHOR>
     * @date 2022/9/16 16:57
     */
    void batchSave(List<DailyPaper> list);

    /**
     * ~ 批量更新 ~
     *
     * @param list 列表
     * <AUTHOR>
     * @date 2022/9/16 17:18
     */
    void batchUpdate(List<DailyPaper> list);

    /**
     * 根据时间获取所有未审核的日报
     *
     * @param startTime
     * @param endTime
     * @return
     */
    List<DailyPaper> findUnAuditDailyByTime(@Param("startDate") LocalDate startTime, @Param("endDate") LocalDate endTime);

    /**
     * @create by yzs at 2023/5/15
     * @description:工时饱和度滞后提交人天
     * @param: dto
     * @return: com.gok.pboot.pms.common.base.R<com.gok.pboot.pms.entity.vo.PaneSaturationAnalysisVO>
     */
    BigDecimal saturationAnalysisBySubmissionDateRangeAndDeptIdsAndUserIdsAndProjectIds(
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate,
            @Param("deptIds") Collection<Long> deptIds,
            @Param("userIds") Collection<Long> userIds,
            @Nullable @Param("projectIds") Collection<Long> projectIds
    );

    /**
     * 根据ID更新日报审批状态
     * @param papers 日报对象
     */
    void updateApprovalStatus(@Param("papers") Collection<DailyPaper> papers);

    /**
     * 日报提交一览表前置查询（为了分页找出符合筛选条件的用户）
     * @param dto 参数
     * @return 用户ID
     */
    @BusinessDataScope(deptOrUser = "user", scopeUserNameList = "userId")
    List<Long> findSubmitUserIdList(Page<Long> page, @Param("dto") DailyFindPageDTO dto);


    /**
     * 日报提交一览表前置查询（为了分页找出符合筛选条件的用户）
     * @param dto 参数
     * @return 用户ID
     */
    @BusinessDataScope(deptOrUser = "user", scopeUserNameList = "userId")
    List<Long> findSubmitUserIdList(@Param("dto") DailyFindPageDTO dto);

    /**
     * 日报提交一览表日报查询（需要dto中的userIds不为空）
     * @param dto 参数
     * @return 日报列表
     */
    List<DailyPaper> findSubmitList(@Param("dto") DailyFindPageDTO dto);

    /**
     * 日报提交一览表日报查询（需要dto中的userIds不为空）
     * @param dto 参数
     * @return 日报列表
     */
    List<DailyExcelExportVO> findDailyExcelList(@Param("dto") DailyFindPageDTO dto);

    /**
     * 根据数据权限查询ID Map
     * @param filter 查询参数
     * @return ID列表
     */
    @MapKey("id")
    @BusinessDataScope(deptOrUser = "user", scopeUserNameList = "userId")
    Map<Long, DailyPaper> findIdMapInDataScope(@Param("filter") Map<String, Object> filter);

    /**
     * 查询可能有异常的日报（缩小异常日报计算范围）
     * @param page 分页参数
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param userIds 用户ID列表
     * @param filterUserIds 要过滤的用户ID列表
     */
    Page<DailyPaper> findMayAbnormal(
            Page<DailyPaper> page,
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate,
            @Nullable @Param("userIds") Collection<Long> userIds,
            @Nullable @Param("filterUserIds") Collection<Long> filterUserIds
    );

    /**
     * 根据日期范围和审批状态过滤查询提交日期
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param approvalStatus 审批状态
     * @return 日期列表
     */
    Set<LocalDate> findSubmissionDateByUserIdAndSubmissionDateRangeAndApprovalStatusNot(
            @Param("userId") Long userId,
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate,
            @Param("approvalStatus") Integer approvalStatus
    );

    /**
     * 清理无用日报
     * 已离职人员的日报
     * @return 删除数量
     */
    int cleanUpUseless();
}
