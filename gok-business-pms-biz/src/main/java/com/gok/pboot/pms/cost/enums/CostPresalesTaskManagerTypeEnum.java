package com.gok.pboot.pms.cost.enums;

import com.gok.pboot.pms.enumeration.ValueEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 售前工单负责人类型枚举
 *
 * <AUTHOR>
 * @date 2025/01/15
 */
@AllArgsConstructor
@Getter
public enum CostPresalesTaskManagerTypeEnum implements ValueEnum<Integer> {

    /**
     * 项目角色
     */
    XMJZ(0, "项目角色"),

    /**
     * 指定人
     */
    ZDR(1, "指定人");

    private final Integer value;
    private final String name;
} 