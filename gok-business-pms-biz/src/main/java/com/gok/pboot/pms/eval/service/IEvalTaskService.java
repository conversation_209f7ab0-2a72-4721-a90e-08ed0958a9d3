package com.gok.pboot.pms.eval.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.cost.entity.domain.CostDeliverTask;
import com.gok.pboot.pms.eval.entity.domain.EvalTask;
import com.gok.pboot.pms.eval.entity.dto.EvalTaskCalibrationDTO;
import com.gok.pboot.pms.eval.entity.dto.EvalTaskDistributionDTO;
import com.gok.pboot.pms.eval.entity.dto.EvalTaskSubmitDTO;
import com.gok.pboot.pms.eval.entity.vo.*;
import com.gok.pboot.pms.eval.entity.dto.EvalTaskListDTO;

import java.util.List;

/**
 * 工单评价
 * <AUTHOR>
 * @create 2025/5/8
 **/
public interface IEvalTaskService extends IService<EvalTask> {

    /**
     * 工单评价提交
     * @param dto
     */
    void submitEvaluation(EvalTaskSubmitDTO dto);

    /**
     * 查询工单评价详情
     * @param taskId 工单ID
     * @return 评价详情
     */
    EvalTaskDetailVO evaluationDetail(Long taskId);

    /**
     * 查询工单评价列表
     * @param pageRequest 分页
     * @param dto 查询条件
     * @return 评价列表
     */
    Page<EvalTaskListVO> findPage(PageRequest pageRequest, EvalTaskListDTO dto);

    /**
     * 导出售前工单评价列表
     * @param dto
     * @return
     */
    List<EvalTaskListVO> export(EvalTaskListDTO dto);

    /**
     * 导出售后工单评价列表
     * @param dto
     * @return
     */
    List<EvalPreTaskExportVO> preExport(EvalTaskListDTO dto);

    /**
     * 查询项目人员售后工单评价分布
     * @param pageRequest 分页参数
     * @param dto 查询条件
     * @return 评价分布列表
     */
    Page<EvalTaskDistributionAfterVO> getAfterEvalDistribution(PageRequest pageRequest, EvalTaskDistributionDTO dto);

    /**
     * 导出项目人员售后工单评价分布
     * @param dto
     * @return
     */
    List<EvalTaskDistributionAfterVO> exportAfterEvalDistribution(EvalTaskDistributionDTO dto);

    /**
     * 查询项目人员售后工单评价分布详情
     * @param dto
     * @param pageRequest
     * @return
     */
    Page<EvalTaskDistributionAfterDetailVO> getAfterEvalDistributionDetail(PageRequest pageRequest,EvalTaskDistributionDTO dto);

    /**
     * 查询项目人员售前工单评价分布
     * @param pageRequest 分页参数
     * @param dto 查询条件
     * @return 评价分布列表
     */
    Page<EvalTaskDistributionPreVO> getPreEvalDistribution(PageRequest pageRequest, EvalTaskDistributionDTO dto);

    /**
     * 导出项目人员售前工单评价分布
     * @param dto
     * @return
     */
    List<EvalTaskDistributionPreVO> exportPreEvalDistribution(EvalTaskDistributionDTO dto);

    /**
     * 查询项目人员售前工单评价分布详情
     * @param pageRequest
     * @param dto
     * @return
     */
    Page<EvalTaskDistributionPreDetailVO> getPreEvalDistributionDetail(PageRequest pageRequest,EvalTaskDistributionDTO dto);

    /**
     * 获取工单评价校准详情
     * @param dto
     * @return
     */
    EvalTaskCalibrationDetailVO getCalibrationDetail(EvalTaskDistributionDTO dto);

    /**
     * 工单评价校准
     * @param dto
     */
    void calibration(EvalTaskCalibrationDTO dto);

    /**
     * 获取项目人员整体评价分布
     * @param projectId
     * @return
     */
    EvalProjectOverallStaffVO getProjectOverallDistribution(Long projectId);

    /**
     * 修改工单评价校准状态
     * @param dto
     */
    void changeCalibrationStatus(EvalTaskCalibrationDTO dto);

    /**
     * 发送未评价的工单提醒
     */
    void taskSendAbnormalMsg();

    /**
     * 删除工单评价
     * @param taskId
     */
    void deleteEvaluation(Long taskId);
}
