package com.gok.pboot.pms.entity.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.gok.pboot.pms.common.serializer.TwoDecimalToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2023/2/23
 * 饱和度分析VO - 人员纬度
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class SituationAnalysisDeptVO {
    /**
     * 项目id
     */
    @ExcelIgnore
    @ApiModelProperty(value = "项目id")
    private Long projectId;
    /**
     * 项目名称
     */
    @ExcelIgnore
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 部门名称
     */
    @ExcelProperty("部门")
    @ApiModelProperty(value = "部门名称")
    private String deptName;
    /**
     * 开始时间
     */
    @ExcelProperty("开始时间")
    @ApiModelProperty(value = "开始时间")
    private String startTime;
    /**
     * 截止时间
     */
    @ExcelProperty("截止时间")
    @ApiModelProperty(value = "截止时间")
    private String endTime;
    /**
     * 姓名
     */
    @ExcelIgnore
    @ApiModelProperty(value = "姓名")
    private String userName;

    /**
     * 部门id
     */
    @ExcelIgnore
    @ApiModelProperty(value = "部门id")
    private Long deptId;


    /**
     * 用户id
     */
    @ExcelIgnore
    @ApiModelProperty(value = "用户id")
    private Long userId;
    /**
     * 正常工时（人天）
     */
    @ExcelProperty("正常工时（人天）")
    @ApiModelProperty(value = "正常工时（人天）")
    @ContentStyle(dataFormat = 2)
    @JsonSerialize(using = TwoDecimalToStringSerializer.class)
    private BigDecimal normalHours;
    /**
     * 加班工时（人天）
     */
    @ExcelProperty("总加班工时（人天）")
    @ApiModelProperty(value = "加班工时（人天）")
    @ContentStyle(dataFormat = 2)
    @JsonSerialize(using = TwoDecimalToStringSerializer.class)
    private BigDecimal addedHours;

    @ExcelProperty("工作日加班工时（人天）")
    @ApiModelProperty(value = "工作日加班工时（人天）")
    @ContentStyle(dataFormat = 2)
    @JsonSerialize(using = TwoDecimalToStringSerializer.class)
    private BigDecimal workOvertimeHours;

    @ExcelProperty("休息日加班工时（人天）")
    @ApiModelProperty(value = "休息日加班工时（人天）")
    @ContentStyle(dataFormat = 2)
    @JsonSerialize(using = TwoDecimalToStringSerializer.class)
    private BigDecimal restOvertimeHours;

    @ExcelProperty("节假日加班工时（人天）")
    @ApiModelProperty(value = "节假日加班工时（人天）")
    @ContentStyle(dataFormat = 2)
    @JsonSerialize(using = TwoDecimalToStringSerializer.class)
    private BigDecimal holidayOvertimeHours;

    @ExcelProperty("调休工时（人天）")
    @ApiModelProperty(value = "调休工时（人天）")
    @ContentStyle(dataFormat = 2)
    @JsonSerialize(using = TwoDecimalToStringSerializer.class)
    private BigDecimal leaveHours;
    /**
     * 项目耗用工时（人天）
     */
    @ExcelIgnore
    @ApiModelProperty(value = "项目耗用工时（人天）")
    @ContentStyle(dataFormat = 2)
    @JsonSerialize(using = TwoDecimalToStringSerializer.class)
    private BigDecimal projectHours;

    /**
     * 项目耗用工时（人天）
     */
    @ExcelProperty("项目分摊工时（人天）")
    @ApiModelProperty(value = "项目分摊工时（人天）")
    @ContentStyle(dataFormat = 2)
    @JsonSerialize(using = TwoDecimalToStringSerializer.class)
    private BigDecimal projectShareHours;


    /**
     * 项目耗用工时（人天）
     */
    @ExcelProperty("项目管理工时（人天）")
    @ApiModelProperty(value = "项目管理工时（人天）")
    @ContentStyle(dataFormat = 2)
    @JsonSerialize(using = TwoDecimalToStringSerializer.class)
    private BigDecimal managementHours;
    /**
     * 实际出勤天数
     */
    @ExcelProperty("实际出勤天数")
    @ApiModelProperty(value = "实际出勤天数")
    @ContentStyle(dataFormat = 2)
    @JsonSerialize(using = TwoDecimalToStringSerializer.class)
    private BigDecimal attendanceDays;
    /**
     * 工资核算出勤天数
     */
    @ExcelProperty("工资核算出勤天数")
    @ApiModelProperty(value = "工资核算出勤天数")
    @ContentStyle(dataFormat = 2)
    @JsonSerialize(using = TwoDecimalToStringSerializer.class)
    private BigDecimal salaryAttendanceDays;
    /**
     * 工时饱和度
     */
    @ExcelProperty("工时饱和度（%）")
    @ApiModelProperty(value = "工时饱和度（%）")
    @ContentStyle(dataFormat = 2)
    @JsonSerialize(using = TwoDecimalToStringSerializer.class)
    private BigDecimal hourSaturation;

    /**
     * 是否内部项目
     */
    @ExcelIgnore
    private Integer isInsideProject;
}
