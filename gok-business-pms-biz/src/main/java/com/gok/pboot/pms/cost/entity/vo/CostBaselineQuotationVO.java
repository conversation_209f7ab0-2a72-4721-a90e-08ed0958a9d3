package com.gok.pboot.pms.cost.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class CostBaselineQuotationVO {

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 版本ID
     */
    private Long versionId;

    /**
     * 版本名称
     */
    private String versionName;

    /**
     * 版本类型（0=目标管理，1=成本管理，2=报价与毛利测算）
     * {@link com.gok.pboot.pms.cost.enums.CostManageVersionEnum}
     */
    private Integer versionType;

    /**
     * 审核状态（0=A表流程同步,1=未审核，2=未通过，3=已审核）
     */
    private Integer auditStatus;

    /**
     * 审核状态名称
     */
    private String auditStatusName;

    /**
     * 收入总额(含税)
     */
    private String incomeAmountIncludedTax;

    /**
     * 收入总额(不含税)
     */
    private String incomeAmountExcludingTax;

    /**
     * 预计毛利率(不含税)
     */
    private String expectedGrossProfitMargin;

    /**
     * 预计毛利(不含税)
     */
    private String expectedGrossProfit;

    /**
     * 预计提前投入成本(万元)
     */
    private String expectedEarlyInvestmentCost;

    /**
     * 提交时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime ctime;

    /**
     * 提交人
     */
    private String creator;

    /**
     * 审核人
     */
    private String operatorName;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime mtime;

    /**
     * 拒绝理由
     */
    private String refuseReason;

    /**
     * 预算成本总额(不含税)
     */
    private String budgetAmountExcludingTax;

}