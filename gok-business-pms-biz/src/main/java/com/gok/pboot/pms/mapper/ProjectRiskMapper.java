package com.gok.pboot.pms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.entity.domain.ProjectRisk;
import com.gok.pboot.pms.entity.vo.ProjectRiskFindPageVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 项目风险 Mapper接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-12
 **/
@Mapper
public interface ProjectRiskMapper extends BaseMapper<ProjectRisk> {

    /**
     * 项目信息详情查询
     */
    ProjectRisk selectById(@Param("id") Long id);

    /**
     * 根据id列表批量查询风险信息，需支持查询已删除项目
     */
    List<ProjectRisk> selectByIds(@Param("ids") Set<String> ids);

    /**
     * 项目信息详情查询，需支持查询已删除项目
     */
    ProjectRisk findRiskSpecialById(@Param("id") Long id);

    /**
     * 根据projectId查询风险信息列表
     */
    List<ProjectRisk> selectByProjectId(@Param("projectId") Long projectId);

    /**
     * 项目风险分页查询
     *
     * @param page
     * @param filter
     * @return
     */
    Page<ProjectRiskFindPageVO> findListPage(Page<ProjectRisk> page,
                                             @Param("filter") Map<String, Object> filter);

    /**
     * 批量逻辑删除
     *
     * @param list id集合
     */
    void batchDel(@Param("list") List<Long> list);

    /**
     * 根据id查询风险项目数
     *
     * @param id
     * @return {@link Integer}
     */
    Integer findChargeRiskNum(Long id);

    /**
     * 根据负责人id分页查询风险表
     * @param page
     * @param filter
     * @return {@link Page<ProjectRisk>}
     */
    Page<ProjectRisk> findPage(Page<Object> page, @Param("filter") Map<String, Object> filter);
}
