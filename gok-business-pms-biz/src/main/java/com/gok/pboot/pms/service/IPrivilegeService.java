package com.gok.pboot.pms.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.entity.dto.PrivilegeAddAndUpdateDTO;
import com.gok.pboot.pms.entity.dto.PrivilegeFindPageDTO;
import com.gok.pboot.pms.entity.vo.AdminConfigFindPageVo;

import java.util.List;

/**
 * <p>
 * 部门审核人员及权限表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-29
 */
public interface IPrivilegeService {

    /**
     * 添加
     *
     * @param request 审核人员及权限表请求实体
     * @return ApiResult
     */
    ApiResult save(PrivilegeAddAndUpdateDTO request);

    /**
     * 编辑
     *
     * @param request 审核人员及权限表实体
     * @return ApiResult
     */
    ApiResult update(PrivilegeAddAndUpdateDTO request);


    /**
     * 批量删除
     *
     * @param list id集合
     */
    void batchDel(List<Long> list);

    /**
     * 分页查询
     *
     * @param pageRequest          分页请求
     * @param privilegeFindPageDTO 过滤条件
     * @return 分页记录
     */
    Page<AdminConfigFindPageVo> findPage(PageRequest pageRequest, PrivilegeFindPageDTO privilegeFindPageDTO);

    /**
     * 分页查询2：支持查询未配置权限项目列表
     *
     * @param pageRequest          分页请求
     * @param privilegeFindPageDTO 过滤条件
     * @return 分页记录
     */
    Page<AdminConfigFindPageVo> findPage2(PageRequest pageRequest, PrivilegeFindPageDTO privilegeFindPageDTO);
}
