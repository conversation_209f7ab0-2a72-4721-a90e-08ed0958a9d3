package com.gok.pboot.pms.eval.entity.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 工单评价提交DTO
 * <AUTHOR>
 */
@Data
public class EvalTaskSubmitDTO {
    /**
     * 工单ID
     */
    @NotNull(message = "工单ID不能为空")
    private Long taskId;

    /**
     * 工单交付质量评分
     */
    @NotNull(message = "任务交付质量评分不能为空")
    private Integer qualityScore;

    /**
     * 工单按时交付评分
     */
    @NotNull(message = "任务按时交付评分不能为空")
    private Integer timelinessScore;

    /**
     * 工单工作配合度评分
     */
    @NotNull(message = "工作配合度评分不能为空")
    private Integer cooperationScore;

    /**
     * 工单执行规范评分
     */
    @NotNull(message = "执行规范评分不能为空")
    private Integer disciplineScore;

    /**
     * 工单交付质量评分描述
     */
    private String qualityRemark;

    /**
     * 工单按时交付评分描述
     */
    private String timelinessRemark;

    /**
     * 工单工作配合度评分描述
     */
    private String cooperationRemark;

    /**
     * 工单执行规范评分描述
     */
    private String disciplineRemark;

    /**
     * 综合得分
     */
    @NotNull(message = "综合得分不能为空")
    private BigDecimal comprehensiveScore;
}