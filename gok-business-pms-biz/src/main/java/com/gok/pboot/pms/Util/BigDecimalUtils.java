package com.gok.pboot.pms.Util;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * - BigDecimal工具类 -
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/7/27 15:09
 */
public class BigDecimalUtils {
    private BigDecimalUtils(){}

    public final static BigDecimal HUNDRED_DECIMAL = new BigDecimal("100");

    public final static BigDecimal SEVEN_DECIMAL = new BigDecimal("7");

    /**
    * ~ 将小数转换为百分比整数 ~
    * @param bigDecimal 小数
    * @return java.lang.Integer
    * <AUTHOR>
    * @date 2022/7/27 15:12
    */
    public static Integer bigDecimalToPercentageInteger(BigDecimal bigDecimal){
        return bigDecimal.multiply(HUNDRED_DECIMAL).intValue();
    }

    public static BigDecimal percentageIntegerToBigDecimal(Integer integer){
        return BigDecimal.valueOf(integer).divide(HUNDRED_DECIMAL, 2, RoundingMode.DOWN);
    }

    /**
     * 如果为null转为0
     * @param bigDecimal bigDecimal
     */
    public static BigDecimal bigDecimalNullToZero(BigDecimal bigDecimal) {
        return bigDecimal != null ? bigDecimal : BigDecimal.ZERO;
    }
}
