package com.gok.pboot.pms.entity.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * - 人员审核权限 -
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/8/23 14:42
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class PrivilegeAddAndUpdateDTO {

    /**
     * 可以审核的项目ID
     */
    @NotNull(message = "项目编号不能为空")
    private Long projectId;

    /**
     * 工时审核员ID集合
     */
    @NotEmpty(message = "工时审核员ID不能为空")
    private List<Long> mhourAuditorIdList;

    /**
     * 项目操作员ID集合
     */
    private List<Long> operatorIdList;


}
