package com.gok.pboot.pms.enumeration;

/**
 * 项目-项目把握度枚举
 *
 * <AUTHOR>
 */
public enum ProjectGraspDegreeEnum implements ValueEnum<Integer> {
    /**
     * 必签商机
     */
    signature(0, "必签商机"),
    /**
     * 靠谱商机
     */
    reliable(1, "靠谱商机"),
    /**
     * 商机线索
     */
    clue(2, "商机线索"),
    /**
     * 一般商机
     */
    common(3, "一般商机");

    //值
    private Integer  value;
    //名称
    private String name;

    ProjectGraspDegreeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
    /**
     * 获取值
     *
     * @return Integer
     */
    @Override
    public Integer getValue() {
        return value;
    }

    /**
     * 获取名称
     *
     * @return String
     */
    @Override
    public String getName() {
        return name;
    }

    public static String getNameByVal(Integer value) {
        for (ProjectGraspDegreeEnum projectGraspDegreeEnum : ProjectGraspDegreeEnum.values()) {
            if (projectGraspDegreeEnum.value.equals(value)) {
                return projectGraspDegreeEnum.name;
            }
        }
        return "";
    }
    public static Integer getValByName(String name) {
        for (ProjectGraspDegreeEnum projectGraspDegreeEnum : ProjectGraspDegreeEnum.values()) {
            if (projectGraspDegreeEnum.getName().equals(name)) {
                return projectGraspDegreeEnum.getValue();
            }
        }
        return null;
    }

    public static ProjectGraspDegreeEnum getProjectGraspDegreeEnum(Integer value) {
        for (ProjectGraspDegreeEnum projectGraspDegreeEnum : ProjectGraspDegreeEnum.values()) {
            if (projectGraspDegreeEnum.value.equals(value)) {
                return projectGraspDegreeEnum;
            }
        }
        return null;
    }
}
