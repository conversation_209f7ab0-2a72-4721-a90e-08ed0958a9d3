package com.gok.pboot.pms.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;

/**
 * 更新任务分组dto
 *
 * <AUTHOR>
 * @date 2023/8/23
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProjectTaskGroupUpdateDTO {

    /**
     * 分组ID
     */
    @NotNull(message = "分组id不能为空")
    private Long groupId;

    /**
     * 分组名称
     */
    private String title;

    /**
     * 在制品数量（最大任务数限制）
     */
    @Range(min = -1, max = 500, message = "在制品数量不能超过500")
    private Integer capacity;
}
