package com.gok.pboot.pms.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gok.module.file.entity.SysFile;
import com.gok.pboot.pms.entity.CustomerBusinessPerson;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
* <p>
* 客户经营单元(基础概况)VO
* </p>
*
* <AUTHOR>
* @since 2024-10-12
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "CustomerBusinessVO对象", description = "客户经营单元(基础概况)")
public class CustomerBusinessVO {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "经营单元名称")
    private String name;

    @ApiModelProperty(value = "主营业务")
    private String overview;

    @ApiModelProperty(value = "组织架构附件")
    private String structureUrl;

    @ApiModelProperty(value = "分析报告附件")
    private String reportUrl;

    @ApiModelProperty(value = "经营计划")
    private String plan;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建人ID")
    private Long creatorId;

    @ApiModelProperty(value = "修改人")
    private String modifier;

    @ApiModelProperty(value = "修改人ID")
    private Long modifierId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date ctime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "修改时间")
    private Date mtime;

    @ApiModelProperty(value = "相关负责人列表")
    private List<CustomerBusinessPerson> persons;

    @ApiModelProperty(value = "客户列表")
    private List<CustomerBusinessUnitPageVO> units;

    @ApiModelProperty(value = "组织架构附件列表")
    private List<SysFile> structureList;

    @ApiModelProperty(value = "分析报告附件列表")
    private List<SysFile> reportList;

    @ApiModelProperty(value = "经营计划附件列表")
    private List<SysFile> planList;

}
