package com.gok.pboot.pms.entity;


import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 加班、请假、销假数据同步
 *
 * <AUTHOR>
 * @since 2022-10-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(OvertimeLeaveData.ALIAS)
public class OvertimeLeaveData  {

    public static final String ALIAS = "mhour_overtime_leave_data";

    @TableId
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    /**
    * OA编号
    */
    private Long oaId;
    /**
    * 分钟数
    */
    private Integer minuteData;
    /**
    * 小时数
    */
    private BigDecimal hourData;
    /**
    * 流程归属日期
    */
    private LocalDate belongdate;
    /**
    * 项目编号
    */
    private Long xmmc;
    /**
    * 数据类型(1:加班、2:请假、3:销假)
    */
    private String type;


}
