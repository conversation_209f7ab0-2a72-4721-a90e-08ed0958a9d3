package com.gok.pboot.pms.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.bcp.upms.feign.RemoteDeptService;
import com.gok.bcp.upms.feign.RemoteOutService;
import com.gok.bcp.upms.vo.SysUserVo;
import com.gok.components.common.util.R;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.Util.*;
import com.gok.pboot.pms.common.constant.FunctionConstants;
import com.gok.pboot.pms.common.exception.ServiceException;
import com.gok.pboot.pms.entity.CustomerBusiness;
import com.gok.pboot.pms.entity.CustomerBusinessUnit;
import com.gok.pboot.pms.entity.dto.CustomerBusinessSearchDTO;
import com.gok.pboot.pms.entity.dto.CustomerBusinessUnitPageDTO;
import com.gok.pboot.pms.entity.vo.CustomerBusinessListVO;
import com.gok.pboot.pms.entity.vo.CustomerBusinessUnitPageVO;
import com.gok.pboot.pms.entity.vo.CustomerBusinessUnitProjectVO;
import com.gok.pboot.pms.entity.vo.CustomerBusinessUnitVO;
import com.gok.pboot.pms.enumeration.BusinessStatusEnum;
import com.gok.pboot.pms.enumeration.LogContentEnum;
import com.gok.pboot.pms.enumeration.ProjectStatusEnum;
import com.gok.pboot.pms.handler.perm.PmsRetriever;
import com.gok.pboot.pms.mapper.CustomerBusinessMapper;
import com.gok.pboot.pms.mapper.CustomerBusinessUnitMapper;
import com.gok.pboot.pms.oa.OaUtil;
import com.gok.pboot.pms.oa.client.OaClient;
import com.gok.pboot.pms.service.IBusinessInfoService;
import com.gok.pboot.pms.service.ICustomerBusinessUnitService;
import com.gok.pboot.pms.service.IProjectInfoService;
import lombok.RequiredArgsConstructor;
import lombok.Synchronized;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;


/**
 * <p>
 * 客户经营单元表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-12
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class CustomerBusinessUnitServiceImpl extends ServiceImpl<CustomerBusinessUnitMapper, CustomerBusinessUnit> implements ICustomerBusinessUnitService {


    private final BcpLoggerUtils bcpLoggerUtils;

    private final CustomerBusinessMapper customerBusinessMapper;

    private final RemoteOutService remoteOutService;

    private final RemoteDeptService remoteDeptService;

    private final IProjectInfoService projectInfoService;

    private final IBusinessInfoService businessInfoService;

    private final DbApiUtil dbApiUtil;

    private final PmsRetriever pmsRetriever;

    @Value("${oa.unitMainId}")
    private String unitMainId;

    @Value("${oa.projectCustomerFieldId}")
    private String projectCustomerFieldId;

    @Value("${oa.businessCustomerFieldId}")
    private String businessCustomerFieldId;

    @Value("${oa.url.httpUrl}")
    private String url;

    /**
     * oa的资源appId
     */
    @Value("${oa.resourcesAppId}")
    private String appId;

    /**
     * oa的spk
     */
    @Value("${oa.spk}")
    private String spk;

    private final OaUtil oaUtil;

    private final OaClient oaClient;

    @Override
    public Page<CustomerBusinessUnitPageVO> findPageList(CustomerBusinessUnitPageDTO dto) {
        List<Long> businessIdsAvailable = pmsRetriever.getBusinessIdsAvailable(dto);
        dto.setBusinessIdsInDataScope(businessIdsAvailable);
        Page<CustomerBusinessUnitPageVO> page = new Page<>(dto.getPageNumber(), dto.getPageSize());
        if (!"all".equals(dto.getScope()) && businessIdsAvailable.isEmpty()) {
            return PageUtils.mapTo(page, x -> null);
        }

        Page<CustomerBusinessUnitPageVO> listPage = baseMapper.findListPage(page, dto);
        if (CollectionUtils.isNotEmpty(listPage.getRecords())) {
            for (CustomerBusinessUnitPageVO temp : listPage.getRecords()) {
                //查询跟进商机
                temp.setBusinessCount(businessInfoService.countByUnit(temp.getId(), BusinessStatusEnum.BUSINESS.getValue()));
                //查询在建项目
                temp.setDoingProjectCount(projectInfoService.countByUnitId(temp.getId(), ProjectStatusEnum.ZJ.getStrValue()));
                //查询结项项目
                temp.setDoneProjectCount(projectInfoService.countByUnitId(temp.getId(), ProjectStatusEnum.JX.getStrValue()));
            }
        }
        return listPage;
    }

    @Override
    public List<CustomerBusinessUnit> findList(CustomerBusinessUnit qo) {
        if (qo == null) {
            qo = new CustomerBusinessUnit();
        }
        return this.list(getQueryWrapper(qo));
    }

    @Override
    public List<CustomerBusinessUnitPageVO> selectSimplyList(Long businessId) {
        return baseMapper.selectSimplyList(businessId);
    }

    public LambdaQueryWrapper<CustomerBusinessUnit> getQueryWrapper(CustomerBusinessUnit qo) {
        return Wrappers.<CustomerBusinessUnit>lambdaQuery()
                .eq(ObjectUtils.isNotEmpty(qo.getBusinessId()), CustomerBusinessUnit::getBusinessId, qo.getBusinessId())
                .eq(ObjectUtils.isNotEmpty(qo.getUnitManagerId()), CustomerBusinessUnit::getUnitManagerId, qo.getUnitManagerId())
                ;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDel(List<Long> list) {
        baseMapper.batchDel(list);
    }


    @Override
    @Synchronized
    @Transactional(rollbackFor = Exception.class)
    public void saveList(List<CustomerBusinessUnit> list, Integer source) {
        if (CollectionUtils.isNotEmpty(list)) {
            StringBuffer addUnitName = new StringBuffer();
            Boolean addFlag = false;
            for (CustomerBusinessUnit temp : list) {
                //查询负责人姓名及部门id
                Long userId = temp.getUnitManagerId();
                R<SysUserVo> userInfo = remoteOutService.getUserInfoById(userId);
                if (userInfo.getData() != null) {
                    SysUserVo user = userInfo.getData();
                    temp.setUnitManager(user.getName());
                }
                //名称重复校验
                int checkUnitCount = baseMapper.findByName(temp);
                if (checkUnitCount > 0) {
                    throw new ServiceException("客户名称" + temp.getUnitName() + "已存在，请更换");
                }
                if (Optional.ofNullable(temp.getId()).isPresent()) {
                    //编辑
                    CustomerBusinessUnit customerBusinessUnit = baseMapper.selectById(temp.getId());
                    BaseBuildEntityUtil.buildUpdate(temp);
                    updateById(temp);

                    //修改所属客户同步至OA
                    updateCustomerBusinessUnitSyncOA(temp);
                    if (!temp.getUnitName().equals(customerBusinessUnit.getUnitName()) ||
                            !temp.getBusinessId().equals(customerBusinessUnit.getBusinessId()) ||
                            (temp.getUnitManagerId() != null && !temp.getUnitManagerId().equals(customerBusinessUnit.getUnitManagerId()))
                    ) {
                        //编辑日志
                        if (source == 1) {
                            //新增客户经营单元/所属客户日志
                            bcpLoggerUtils.log(FunctionConstants.CUSTOMER_BUSINESS_UNIT, LogContentEnum.UPDATE_CUSTOMER_BUSINESS_UNIT,
                                    SecurityUtils.getUser().getName(), temp.getUnitName());
                        } else {
                            //新增所属客户日志
                            bcpLoggerUtils.log(FunctionConstants.CUSTOMER_UNIT, LogContentEnum.UPDATE_CUSTOMER_BUSINESS_UNIT,
                                    SecurityUtils.getUser().getName(), temp.getUnitName());
                        }
                    }
                } else {
                    //新增
                    BaseBuildEntityUtil.buildInsert(temp);
                    save(temp);
                    addFlag = true;
                    addUnitName.append(temp.getUnitName()).append("、");

                    //新增所属客户同步至OA
                    addCustomerBusinessUnitSyncOA(temp);
                }
            }
            if (addFlag) {
                if (source == 1) {
                    //新增客户经营单元/所属客户日志
                    bcpLoggerUtils.log(FunctionConstants.CUSTOMER_BUSINESS_UNIT, LogContentEnum.CREATE_CUSTOMER_BUSINESS_UNIT,
                            SecurityUtils.getUser().getName(), addUnitName.deleteCharAt(addUnitName.lastIndexOf("、")));
                } else {
                    //新增所属客户日志
                    bcpLoggerUtils.log(FunctionConstants.CUSTOMER_UNIT, LogContentEnum.CREATE_CUSTOMER_BUSINESS_UNIT,
                            SecurityUtils.getUser().getName(), addUnitName.deleteCharAt(addUnitName.lastIndexOf("、")));
                }
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(List<Long> ids) {
        for (Long id : ids) {
            //查询该客户是否有关联项目
            Integer count = projectInfoService.countByUnitId(Long.valueOf(id), null);
            Integer businessCount = businessInfoService.countByUnit(Long.valueOf(id), null);
            if (count > 0) {
                throw new ServiceException("该所属客户已有对应项目，不可删除，请直接修改其经营单元");
            } else if (businessCount > 0) {
                throw new ServiceException("该所属客户已有对应商机，不可删除，请直接修改其经营单元");
            } else {
                CustomerBusinessUnit customerBusinessUnit = baseMapper.selectById(id);
                removeById(id);

                //删除所属客户同步至OA
                deleteCustomerBusinessUnitSyncOA(String.valueOf(id));
                if (customerBusinessUnit != null) {
                    bcpLoggerUtils.log(FunctionConstants.CUSTOMER_UNIT, LogContentEnum.DELETE_CUSTOMER_BUSINESS_UNIT,
                            SecurityUtils.getUser().getName(), customerBusinessUnit.getUnitName());
                }
            }
        }
    }

    /**
     * 新增所属客户同步至OA
     *
     * @param customerBusinessUnit
     */
    public void addCustomerBusinessUnitSyncOA(CustomerBusinessUnit customerBusinessUnit) {
        //查询OA公共字典最大值
        int modeSelectitempagedetailMax = dbApiUtil.getModeSelectitempagedetailMax(unitMainId, "2");

        //查询经营单元id
        String businessId = dbApiUtil.getModeSelectitempagedetail(String.valueOf(customerBusinessUnit.getBusinessId()));

        //插入OA公共字典
        dbApiUtil.insertModeSelectitempagedetail(unitMainId, customerBusinessUnit.getUnitName(), String.valueOf(modeSelectitempagedetailMax + 1), businessId, "2", String.valueOf(customerBusinessUnit.getId()));

        //查询OA公共字典id
        //String modeSelectitempagedetailId = dbApiUtil.getModeSelectitempagedetail(String.valueOf(customerBusinessUnit.getId()));

        //查询OA流程字典最大值（项目台账）
        //int projectMax = dbApiUtil.getWorkflowSelectitemMax(projectCustomerFieldId);

        //插入OA流程字典（项目台账）
        //dbApiUtil.insertWorkflowSelectitem(projectCustomerFieldId, String.valueOf(projectMax + 1), customerBusinessUnit.getUnitName(), modeSelectitempagedetailId, String.valueOf(customerBusinessUnit.getId()));

        //查询OA流程字典最大值（商机报备）
        //int businessMax = dbApiUtil.getWorkflowSelectitemMax(businessCustomerFieldId);

        //插入OA流程字典（商机报备）
        //dbApiUtil.insertWorkflowSelectitem(businessCustomerFieldId, String.valueOf(businessMax + 1), customerBusinessUnit.getUnitName(), modeSelectitempagedetailId, String.valueOf(customerBusinessUnit.getId()));

        //同步OA公共字典缓存
        saveSelectItemSyncOA();
    }


    /**
     * 修改所属客户同步至OA
     *
     * @param customerBusinessUnit
     */
    public void updateCustomerBusinessUnitSyncOA(CustomerBusinessUnit customerBusinessUnit) {
        String businessId = null;
        if (customerBusinessUnit.getBusinessId() != null) {
            //查询经营单元id
            businessId = dbApiUtil.getModeSelectitempagedetail(String.valueOf(customerBusinessUnit.getBusinessId()));
        }

        //更新OA公共字典
        dbApiUtil.updateModeSelectitempagedetail(customerBusinessUnit.getUnitName(), null, businessId, String.valueOf(customerBusinessUnit.getId()));

        //更新OA流程字典
        //dbApiUtil.updateWorkflowSelectitem(customerBusinessUnit.getUnitName(), null, null, String.valueOf(customerBusinessUnit.getId()));

        //同步OA公共字典缓存
        saveSelectItemSyncOA();
    }

    /**
     * 删除所属客户同步至OA
     *
     * @param id
     */
    public void deleteCustomerBusinessUnitSyncOA(String id) {
        //更新OA公共字典
        dbApiUtil.updateModeSelectitempagedetail(null, "1", null, id);

        //更新OA流程字典
        //dbApiUtil.updateWorkflowSelectitem(null, "1", null, id);

        //同步OA公共字典缓存
        saveSelectItemSyncOA();
    }

    /**
     * 同步OA公共字典缓存
     */
    public void saveSelectItemSyncOA() {
        // 获取token
        String token = oaUtil.getToken();
        // 对userID进行加密
        String encryptUserId = RSAUtils.encrypt(String.valueOf(1), spk);
        Map<String, Object> map = oaClient.saveSelectItem(token, appId, encryptUserId, url, String.valueOf(unitMainId), "经营单元(BU)");
        log.info("同步OA公共字典缓存:" + map);
        Object id = map.get("id");
        if (id == null) {
            log.error("同步OA失败，原因:" + map);
            throw new ServiceException("同步OA失败，原因:" + map);
        }
    }

    @Override
    public List<CustomerBusinessListVO> findNameList(CustomerBusinessSearchDTO customerBusinessSearchDTO) {
        return baseMapper.findNameList(customerBusinessSearchDTO);
    }

    @Override
    public CustomerBusinessUnitVO getById(Long id) {
        CustomerBusinessUnit customerBusinessUnit = baseMapper.selectById(id);
        CustomerBusinessUnitVO customerBusinessUnitVO = new CustomerBusinessUnitVO();
        if (customerBusinessUnit != null) {
            BeanUtils.copyProperties(customerBusinessUnit, customerBusinessUnitVO);
            customerBusinessUnitVO.setId(customerBusinessUnit.getId());
            CustomerBusiness customerBusiness = customerBusinessMapper.selectById(customerBusinessUnit.getBusinessId());
            if (customerBusiness != null) {
                customerBusinessUnitVO.setBusinessName(customerBusiness.getName());
            }
        }
        return customerBusinessUnitVO;
    }

    @Override
    public CustomerBusinessUnitProjectVO checkUnit(Long id) {
        CustomerBusinessUnitProjectVO customerBusinessUnitProjectVO = new CustomerBusinessUnitProjectVO();
        //查询该客户是否有关联项目
        Integer projectCount = projectInfoService.countByUnitId(Long.valueOf(id), null);
        customerBusinessUnitProjectVO.setProjectCount(projectCount);
        Integer businessCount = businessInfoService.countByUnit(Long.valueOf(id), null);
        customerBusinessUnitProjectVO.setBusinessCount(businessCount);
        return customerBusinessUnitProjectVO;
    }
}
