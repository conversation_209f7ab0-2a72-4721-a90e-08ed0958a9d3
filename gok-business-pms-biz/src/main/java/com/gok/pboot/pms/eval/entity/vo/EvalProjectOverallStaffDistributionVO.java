package com.gok.pboot.pms.eval.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 获取项目人员整体评价分布
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Builder
public class EvalProjectOverallStaffDistributionVO {
    /**
     * 建议分布等级
     */
    private String evalGrade;

    /**
     * 建议分布起始范围
     */
    private String startRange;

    /**
     * 建议分布结束范围
     */
    private String endRange;

    /**
     * 当前分布
     */
    private String  currentRange;
}