package com.gok.pboot.pms.entity.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 滴滴用车订单导入DTO
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DdVehicleOrderImportDTO {

    /**
     * 订单号
     */
    @ExcelProperty(value = "*订单号")
    private String orderNo;

    /**
     * 乘车人工号
     */
    @ExcelProperty(value = "乘车人工号")
    private String passengerEmployeeNo;
    /**
     * 乘车人ID
     */
    @ExcelIgnore
    private Long passengerId;

    /**
     * 乘车人部门ID
     */
    @ExcelIgnore
    private Long passengerDeptId;
    /**
     * 乘车人姓名
     */
    @ExcelProperty(value = "*乘车人")
    private String passengerName;

    /**
     * 乘坐时间
     */
    @ExcelProperty(value = "乘坐时间")
    private LocalDateTime travelTime;

    /**
     * 到达时间
     */
    @ExcelProperty(value = "到达时间")
    private LocalDateTime arrivalTime;

    /**
     * 用车类型
     */
    @ExcelProperty(value = "用车类型")
    private String vehicleType;

    /**
     * 出发城市
     */
    @ExcelProperty(value = "出发城市")
    private String departureCity;

    /**
     * 出发地址
     */
    @ExcelProperty(value = "出发地址")
    private String departureAddress;

    /**
     * 到达城市
     */
    @ExcelProperty(value = "到达城市")
    private String arrivalCity;

    /**
     * 到达地址
     */
    @ExcelProperty(value = "到达地址")
    private String arrivalAddress;

    /**
     * 用车行驶距离
     */
    @ExcelProperty(value = "用车行驶距离")
    private BigDecimal travelDistance;

    /**
     * 企业实付金额
     */
    @ExcelProperty(value = "*企业金额")
    private BigDecimal companyActualPayment;

    /**
     * 服务费
     */
    @ExcelProperty(value = "服务费")
    private BigDecimal serviceFee;

    /**
     * 支付类型
     */
    @ExcelProperty(value = "支付类型")
    private String paymentType;

    /**
     * 预订日期
     */
    @ExcelProperty(value = "*预订日期")
    private LocalDate bookingDate;

    /**
     * 预订人工号
     */
    @ExcelProperty(value = "*预订人工号")
    private String bookingEmployeeNo;

    /**
     * 预订人姓名
     */
    @ExcelProperty(value = "预订人")
    private String bookingEmployeeName;

    /**
     * 预订人ID
     */
    @ExcelIgnore
    private Long bookingUserId;

    /**
     * 预订人部门ID
     */
    @ExcelIgnore
    private Long bookingDeptId;
    /**
     * 出差申请单号
     */
    @ExcelProperty(value = "出差申请单号")
    private String businessTripApplicationNo;

    /**
     * 出差事由
     */
    @ExcelProperty(value = "出差事由")
    private String businessTripReason;

    /**
     * 成本中心/所属项目
     */
    @ExcelProperty(value = "*成本中心/所属项目")
    private String costCenterProject;

    @ExcelIgnore
    private Long companyId;
    /**
     * 所属公司
     */
    @ExcelProperty(value = "*所属公司")
    private String companyName;

    /**
     * 成本中心ID
     */
    @ExcelIgnore
    private Long costCenterId;

    /**
     * 成本中心名称
     */
    @ExcelIgnore
    private String costCenterName;

    /**
     * 所属项目ID
     */
    @ExcelIgnore
    private Long projectId;

    /**
     * 所属项目名称
     */
    @ExcelIgnore
    private String projectName;

    @ExcelIgnore
    private String projectCode;
} 