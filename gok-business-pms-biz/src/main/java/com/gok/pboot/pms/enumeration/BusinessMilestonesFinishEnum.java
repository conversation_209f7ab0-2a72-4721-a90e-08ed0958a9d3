package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 商务里程碑是否完成枚举
 *
 * <AUTHOR>
 * @date 2025/07/10
 */
@AllArgsConstructor
@Getter
public enum BusinessMilestonesFinishEnum implements ValueEnum<String> {

    /**
     * 未完成
     */
    UNFINISHED("1", "未完成"),
    /**
     * 完成
     */
    FINISHED("0", "已完成");

    private final String value;

    private final String name;
}
