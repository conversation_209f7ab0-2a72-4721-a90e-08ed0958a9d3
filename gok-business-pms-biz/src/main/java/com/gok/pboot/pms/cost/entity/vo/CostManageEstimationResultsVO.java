package com.gok.pboot.pms.cost.entity.vo;

import cn.hutool.core.bean.BeanUtil;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.cost.entity.domain.CostManageEstimationResults;
import com.gok.pboot.pms.cost.enums.AccountTypeEnum;
import com.gok.pboot.pms.cost.enums.CostBudgetTypeEnum;
import lombok.*;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 成本估算结果VO类
 *
 * <AUTHOR>
 * @create 2025/01/07
 **/
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class CostManageEstimationResultsVO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 版本ID
     */
    private Long versionId;

    /**
     * 父级ID
     */
    private Long parentId;

    /**
     * 成本项来源 0=新增成本项 1=人工成本测算
     * {@link com.gok.pboot.pms.cost.enums.CostManageSourceEnum}
     */
    private Integer source;

    /**
     * 成本预算类型（0=售前成本，1=A表成本，2=B表成本）
     * {@link com.gok.pboot.pms.cost.enums.CostBudgetTypeEnum}
     */
    private Integer costBudgetType;

    /**
     * 成本预算类型Txt
     */
    private String costBudgetTypeTxt;

    /**
     * 说明
     */
    private String remark;

    /**
     * 成本科目ID
     */
    private Long accountId;

    /**
     * 成本科目OA ID
     */
    private Long accountOaId;

    /**
     * 成本科目名称
     */
    private String accountName;

    /**
     * 自定义补贴配置ID
     */
    private Long subsidyCustomConfigId;

    /**
     * 自定义补贴配置名称
     */
    private String subsidyCustomConfigName;

    /**
     * 成本科目类型
     * {@link com.gok.pboot.pms.cost.enums.AccountTypeEnum}
     */
    private Integer accountType;

    /**
     * 成本科目类型Txt
     */
    private String accountTypeTxt;

    /**
     * 成本科目类别id
     */
    private Long accountCategoryId;

    /**
     * 成本科目类别名称
     */
    private String accountCategoryName;

    /**
     * 科目代码
     */
    private String accountCode;

    /**
     * 预算金额(含税)
     */
    private BigDecimal budgetAmountIncludedTax;

    /**
     * 已用预算
     */
    private BigDecimal usedBudget;

    /**
     * 剩余预算
     */
    private BigDecimal remainBudget;

    /**
     * 税率中台OA字典ID
     */
    private Integer taxRate;

    /**
     * 税率文本
     */
    private String taxRateTxt;

    /**
     * 预算金额(不含税)
     */
    private BigDecimal budgetAmountExcludingTax;

    /**
     * 该科目是否已被删除 true-已删除 false-未删除
     */
    private boolean accountDeleteFlag = false;

    /**
     * 子级
     */
    List<CostManageEstimationResultsVO> children;

    /**
     * 人员级别集合
     */
    List<CostManagePersonnelLevelDetailVO> personnelLevelList;

    /**
     * 自定义补贴费用集合
     */
    private List<CostManagePersonnelCustomDetailVO> customDetailList;

    /**
     * 是否有预算明细
     */
    private Integer hasDetail = 0;

    public static CostManageEstimationResultsVO of(CostManageEstimationResults entity, Map<Integer, String> taxRateMap) {
        if (null == entity) {
            return new CostManageEstimationResultsVO();
        }
        CostManageEstimationResultsVO vo = BeanUtil.copyProperties(entity, CostManageEstimationResultsVO.class);
        vo.setAccountTypeTxt(EnumUtils.getNameByValue(AccountTypeEnum.class, vo.getAccountType()));
        vo.setCostBudgetTypeTxt(EnumUtils.getNameByValue(CostBudgetTypeEnum.class, vo.getCostBudgetType()));
        vo.setTaxRateTxt(null != vo.getTaxRate() && null != taxRateMap ? taxRateMap.get(vo.getTaxRate().toString()) : null);
        return vo;
    }

}
