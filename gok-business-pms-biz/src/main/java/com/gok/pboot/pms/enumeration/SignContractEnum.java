package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;

/**
 * （项目）是否签订合同枚举
 *
 * <AUTHOR>
 * @date 2023-07-19
 */
@AllArgsConstructor
public enum SignContractEnum implements ValueEnum<String> {

    YES("0", "是"),
    NO("1", "否")
    ;

    private final String value;
    private final String name;

    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getName() {
        return name;
    }
}
