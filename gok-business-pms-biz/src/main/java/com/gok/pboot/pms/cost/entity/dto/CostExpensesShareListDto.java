package com.gok.pboot.pms.cost.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;

/**
 * 交付管理-费用报销台账查询参数
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CostExpensesShareListDto {

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 是否客户承担
     */
    private Integer customerUndertakes;

    /**
     * 起始归属月份
     */
    private String belongingStartMonth;

    /**
     * 截止归属月份
     */
    private String belongingEndMonth;

    /**
     * 申请人姓名/人员姓名/工号
     */
    private String applicantName;

    /**
     * 科目名称
     */
    private String accountName;

    /**
     * 项目id集合
     */
    private List<Long> projectIds;

    /**
     * 工号集合
     */
    private List<String> workCodes;

    /**
     * 归属月份集合
     */
    private List<String> belongMonths;

    /**
     * 人员姓名列表
     */
    private Set<String> nameSet;

    /**
     * 科目id
     */
    private List<Long> accountIds;

    /**
     * 可查看权限范围人员工号集合
     */
    private List<String> purviewNames;
}