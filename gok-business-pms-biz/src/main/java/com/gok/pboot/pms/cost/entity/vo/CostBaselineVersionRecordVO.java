package com.gok.pboot.pms.cost.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gok.pboot.pms.Util.DateUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 基线版本记录 vo
 *
 * <AUTHOR>
 * @date 2025/01/14
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class CostBaselineVersionRecordVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 项目ID
     */
    private Long projectId;
    /**
     * 基线版本
     */
    private String versionName;

    /**
     * 报价与毛利测算版本ID
     */
    private Long quotationVersionId;

    /**
     * 报价与毛利测算版本名称
     */
    private String quotationVersionName;

    /**
     * 目标版本id
     */
    private Long targetVersionId;

    /**
     * 目标版本名称
     */
    private String targetVersionName;

    /**
     * 成本版本id
     */
    private Long costVersionId;

    /**
     * 成本版本名称
     */
    private String costVersionName;

    /**
     * 关联现金流版本ID
     */
    private Long cashPlanVersionId;

    /**
     * 关联现金流版本名称
     */
    private String cashPlanVersionName;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = DateUtil.DATETIME_FORMAT, timezone = DateUtil.GMT_8)
    private LocalDateTime updateTime;

    /**
     * 更新标识
     * 0-无更新
     * 1-报价与毛利测算更新
     * 2-目标版本更新
     * 3-成本版本更新
     * 4-现金流版本更新
     */
    private Integer updateFlag;

}
