package com.gok.pboot.pms.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.bcp.upms.vo.SysMenuVo;
import com.gok.pboot.pms.entity.CustomerBusiness;
import com.gok.pboot.pms.entity.dto.CustomerBusinessDTO;
import com.gok.pboot.pms.entity.dto.CustomerBusinessSearchDTO;
import com.gok.pboot.pms.entity.vo.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <p>
 * 客户经营单元表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-12
 **/
public interface ICustomerBusinessService extends IService<CustomerBusiness> {


    /**
     * 客户经营单元分页查询
     *
     * @param dto dto
     * @return {@link Page}{@link ProjectMeetingFindPageVO}
     */
    Page<CustomerBusinessPageVO> findPageList(CustomerBusinessDTO dto);

    /**
     * 查询名称列表
     * @return
     */
    List<CustomerBusinessListVO> findNameList(CustomerBusinessSearchDTO customerBusinessSearchDTO);

    /**
     * 新增客户经营单元
     *
     * @param entity 新增请求
     * @return 客户经营单元主键id
     */
    Long saveBusiness(CustomerBusiness entity);

    /**
     * 获取详情
     * @param id 主键id
     * @return
     */
    CustomerBusinessVO getById(Long id);
    /**
     * 获取菜单权限
     *
     * @param request 请求对象
     * @return {@link List}<{@link SysMenuVo}>
     */
    CustomerBusinessSysMenuVo getMenuAuthority(HttpServletRequest request);


    /**
     * 获取当前用户在此项目的业务角色
     * @param businessId   传入的客户经营单元id
     * @param permissionObj 传入的权限标识
     * @param menuType  传入的权限标识
     * @param application application
     * @return
     */
   List<SysMenuVo> getBusinessAuthorities(Long businessId,
                                                               Object permissionObj,
                                                               Object menuType,
                                                               Long application);

    /**
     * 新增经营单元同步至OA
     * @param customerBusiness
     */
   void addCustomerBusinessSyncOA(CustomerBusiness customerBusiness);

    /**
     * 修改经营单元同步至OA
     * @param customerBusiness
     */
   void updateCustomerBusinessSyncOA(CustomerBusiness customerBusiness);

    /**
     * 删除经营单元同步至OA
     * @param id
     */
    void deleteCustomerBusinessSyncOA(String id);
}
