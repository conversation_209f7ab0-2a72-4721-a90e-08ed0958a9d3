package com.gok.pboot.pms.didi.service;

import com.gok.pboot.pms.didi.entity.dto.DidiAuthReq;

import java.time.LocalDate;

/**
 * 滴滴项目同步服务接口
 *
 * <AUTHOR>
 * @date 2025/01/27
 */
public interface DidiSyncService {


    /**
     * 批量同步项目信息
     *
     */
    void batchSyncProjects(LocalDate dateParam, Boolean withManager);

    /**
     * 获取滴滴身份验证要求
     *
     * @return {@link DidiAuthReq }
     */
    DidiAuthReq getDidiAuthReq();

    /**
     * 批量同步用户
     *
     * @param dateParam 日期参数
     */
    void batchSyncUsers(LocalDate dateParam);

    /**
     * 获取访问令牌
     *
     * @return {@link String }
     */
    String getAccessToken();



}