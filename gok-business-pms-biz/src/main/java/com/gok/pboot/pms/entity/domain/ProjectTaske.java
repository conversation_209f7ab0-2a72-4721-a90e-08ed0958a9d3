package com.gok.pboot.pms.entity.domain;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import com.gok.pboot.pms.entity.dto.ProjectTaskeDto;
import com.gok.pboot.pms.entity.dto.ProjectTaskeUserDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 项目任务
 *
 * <AUTHOR>
 * @date 2024/01/19
 */
@Data
@TableName("project_taske")
@EqualsAndHashCode(callSuper = true)
public class ProjectTaske extends BeanEntity<Long> {

    /**
     * 所属项目ID
     */
    private Long projectId;

    /**
     * 任务名称
     */
    private String title;

    /**
     * 任务类型（0=售前支撑，1=售后交付，null=内部项目不区分）
     * @link com.gok.pboot.pms.enumeration.ProjectTaskKindEnum
     */
    private Integer kind;

    /**
     * 任务状态（0=正常，1=结束）
     * @libk com.gok.pboot.pms.enumeration.ProjectTaskStateEnum
     */
    private Integer state;

    /**
     * 是否长期任务
     */
    private Integer permanentFlag;

    /**
     * 计划开始日期
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private LocalDate expectStartDate;

    /**
     * 计划结束日期
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private LocalDate expectEndDate;

    /**
     * 实际开始日期
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private LocalDate startDate;

    /**
     * 实际结束日期
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private LocalDate endDate;

    /**
     * 任务负责人姓名（逗号分隔）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String leaderNames;

    /**
     * 任务参与人姓名（逗号分隔）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String memberNames;

    /**
     * 通过dto构建对象
     *
     * @param request 项目任务Dto
     * @return {@link ProjectTaske}
     */
    public static ProjectTaske of(ProjectTaskeDto request) {
        ProjectTaske projectTaske = new ProjectTaske();
        projectTaske.setId(request.getId());
        projectTaske.setProjectId(request.getProjectId());
        projectTaske.setTitle(request.getTitle());
        projectTaske.setKind(request.getKind());
        projectTaske.setState(request.getState());
        projectTaske.setPermanentFlag(request.getPermanentFlag());
        projectTaske.setExpectStartDate(request.getExpectStartDate());
        projectTaske.setExpectEndDate(request.getExpectEndDate());
        projectTaske.setStartDate(request.getStartDate());
        projectTaske.setEndDate(request.getEndDate());
        List<ProjectTaskeUserDto> leaders = request.getLeaderNames();
        if (CollUtil.isNotEmpty(leaders)) {
            projectTaske.setLeaderNames(leaders.stream().map(ProjectTaskeUserDto::getUserName).collect(Collectors.joining(",")));
        } else {
            projectTaske.setLeaderNames(CharSequenceUtil.EMPTY);
        }
        List<ProjectTaskeUserDto> members = request.getMemberNames();
        if (CollUtil.isNotEmpty(members)) {
            projectTaske.setMemberNames(members.stream().map(ProjectTaskeUserDto::getUserName).collect(Collectors.joining(",")));
        } else {
            projectTaske.setMemberNames(CharSequenceUtil.EMPTY);
        }

        return projectTaske;
    }

}
