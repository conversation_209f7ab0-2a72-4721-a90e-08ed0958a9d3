package com.gok.pboot.pms.entity.vo;

import com.gok.pboot.pms.Util.DecimalFormatUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.time.LocalDate;

/**
 * 项目管理-查询合同明细
 *
 * <AUTHOR>
 * @date 2023/08/23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class ContractDetailsPageVo {

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 合同签署日期
     */
    private LocalDate contractSignDate;

    /**
     * 合同编号
     */
    private Long contractNo;

    /**
     * 合同名称
     */
    private String contractName;

    /**
     * 合同金额（含税）
     */
    private String contractPrice;

    /**
     * 合同金额（不含税）
     */
    private String contractPriceBhs;

    /**
     * 设置小数位和舍进规则
     *
     * @param newScale 小数保留位数
     * @param roundingMode 舍进规则
     * @param decimalFormat 小数
     */
    public void setScale(int newScale, RoundingMode roundingMode, DecimalFormat decimalFormat) {
        this.contractPrice = DecimalFormatUtil.setAndValidate(contractPrice, newScale, roundingMode, decimalFormat);
        this.contractPriceBhs = DecimalFormatUtil.setAndValidate(contractPriceBhs, newScale, roundingMode, decimalFormat);
    }

}
