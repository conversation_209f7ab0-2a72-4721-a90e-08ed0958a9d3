package com.gok.pboot.pms.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.gok.components.common.constant.SecurityConstants;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.service.IEhrService;
import com.gok.pboot.service.commons.base.ApiResult;
import com.gok.pboot.service.entity.position.vo.GradeNodeVo;
import com.gok.pboot.service.entity.position.vo.JobTreeNodeVo;
import com.gok.pboot.service.feign.RemoteEhrService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ehr服务
 *
 * <AUTHOR>
 * @date 2025/05/06
 */
@Service
public class IEhrServiceImpl implements IEhrService {

    @Resource
    private RemoteEhrService remoteEhrService;


    /**
     * 职级树
     *
     * @return {@link ApiResult }<{@link List }<{@link GradeNodeVo }>>
     */
    @Override
    public ApiResult<List<GradeNodeVo>> positionGradeTree() {
        return remoteEhrService.positionGradeTree(null, false, SecurityConstants.FROM_IN);
    }

    @Override
    public ApiResult<List<JobTreeNodeVo>> jobTitlesTree() {
        return remoteEhrService.jobTitlesTree(null, false, false, SecurityConstants.FROM_IN);
    }

    /**
     * 获取职务名称
     *
     * @return {@link Map }<{@link Long },{@link String }>
     */
    @Override
    public Map<Long, String> getJobActivityNameMap() {
        ApiResult<List<JobTreeNodeVo>> result = remoteEhrService.jobTitlesTree(null, false, false, SecurityConstants.FROM_IN);
        List<JobTreeNodeVo> jobTreeNodeVos = BaseBuildEntityUtil.apiResult(result);
        // 获取职务名称
        Map<Long, String> jobActivityNameMap = new HashMap<>(10);
        jobTreeNodeVos.forEach(jobGroup ->
                CollUtil.emptyIfNull(jobGroup.getChildren()).forEach(jobActivity ->
                        jobActivityNameMap.put(Long.valueOf(jobActivity.getId()), jobActivity.getName())));

        return jobActivityNameMap;
    }

    /**
     * 职级名称
     *
     * @return {@link Map }<{@link Long }, {@link String }>
     */
    @Override
    public Map<Long, String> getPositionGradeNameMap() {
        ApiResult<List<GradeNodeVo>> result = remoteEhrService.positionGradeTree(null, false, SecurityConstants.FROM_IN);
        List<GradeNodeVo> gradeNodeVos = BaseBuildEntityUtil.apiResult(result);
        Map<Long, String> gradeNameMap = new HashMap<>(27);
        gradeNodeVos.forEach(gradeType ->
                CollUtil.emptyIfNull(gradeType.getChildren()).forEach(sequence ->
                        CollUtil.emptyIfNull(sequence.getChildren()).forEach(grade ->
                                gradeNameMap.put(grade.getId(), grade.getName()))));
        return gradeNameMap;
    }

}
