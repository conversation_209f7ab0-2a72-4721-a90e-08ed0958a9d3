/*
 *
 *      Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the pig4cloud.com developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: lengleng (<EMAIL>)
 *
 */

package com.gok.pboot.pms.service.fegin;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.common.core.constant.SecurityConstants;
import com.gok.pboot.pms.common.base.R;
import com.gok.pboot.pms.common.base.ServiceNameConstants;
import com.gok.pboot.pms.common.base.TenantConstants;
import com.gok.pboot.pms.entity.SysUser;
import com.gok.pboot.pms.entity.SysUserIdBind;
import com.gok.pboot.pms.entity.dto.UserAndRoleDTO;
import com.gok.pboot.pms.entity.dto.UserInfo;
import com.gok.pboot.pms.entity.dto.UserPmsPageDTO;
import com.gok.pboot.pms.entity.vo.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/6/22
 */
@FeignClient(contextId = "oldRemoteUserService", value = ServiceNameConstants.UPMS_SERVICE)
public interface RemoteUserService {

    /**
     * 通过用户名查询用户、角色信息
     *
     * @param username 用户名
     * @param from     调用标志
     * @return R
     */
    @GetMapping("/user/info/{username}")
    R<UserInfo> info(@PathVariable("username") String username, @RequestHeader(SecurityConstants.FROM) String from);

    /**
     * 获取指定用户全部信息
     *
     * @return 用户信息
     */
    @GetMapping("/user/info/mini/{openid}")
    R<UserInfo> infoMini(@PathVariable("openid") String openid, @RequestHeader(SecurityConstants.FROM) String from);

    /**
     * 获取指定用户openid
     *
     * @return 用户信息
     */
    @GetMapping("/user/openid/{code}")
    R<UserInfo> getopenidCode(@PathVariable("code") String code, @RequestHeader(SecurityConstants.FROM) String from);

    /**
     * 通过WeChatCode验证指定用户的openid，返回用户ID
     */
    @GetMapping("/user/openid/validate/{code}")
    R<String> getopenidCodeValidate(
            @PathVariable("code") String code, @RequestHeader(SecurityConstants.FROM) String from
    );

    /**
     * 通过用户名获取绑定关系
     */
    @GetMapping("/user/bind/username/{username}")
    R<SysUserIdBind> getBindByUsername(
            @PathVariable("username") String username,
            @RequestHeader(SecurityConstants.FROM) String from
    );

    /**
     * 通过社交账号或手机号查询用户、角色信息
     *
     * @param inStr appid@code
     * @param from  调用标志
     * @return
     */
    @GetMapping("/social/info/{inStr}")
    R<UserInfo> social(@PathVariable("inStr") String inStr, @RequestHeader(SecurityConstants.FROM) String from);

    /**
     * 通过社交账号或手机号或openId查询用户、角色信息
     *
     * @param inStr appid@code
     * @param from  调用标志
     * @return
     */
    @GetMapping("/social/validate/{inStr}")
    R<Boolean> socialValidate(@PathVariable("inStr") String inStr, @RequestHeader(SecurityConstants.FROM) String from);

    /**
     * 查询上级部门的用户信息
     *
     * @param username 用户名
     * @return R
     */
    @GetMapping("/user/ancestor/{username}")
    R<List<SysUser>> ancestorUsers(@PathVariable("username") String username);

    /**
     * 通过ID查询用户信息
     *
     * @param id ID
     * @return 用户信息
     */
    @GetMapping("/user/{id}")
    R<UserVO> user(@PathVariable("id") Long id);

    /**
     * 通过岗位ID查询用户
     *
     * @param jobId 岗位ID
     * @return 用户列表
     */
    @GetMapping("/user/getUserByJob/{jobId}")
    R<List<SysUser>> getUserByJob(@PathVariable("jobId") Long jobId);

    /**
     * PMS人员列表校验
     *
     * @param userList 人员列表
     * @return 检验信息
     */
    @PostMapping("/user/pms/userCheckout")
    R<List<SysUserVO>> getUserCheckout(@RequestBody List<Long> userList
            , @RequestHeader(SecurityConstants.FROM) String from);

    /**
     * PMS人员姓名查询人员列表
     *
     * @param userNames 人员列表
     * @return {@link R<List<SysUserVO>>}
     */
    @PostMapping("/user/pms/getUserByNames")
    R<List<SysUser>> getUserByNames(@RequestBody List<String> userNames
            , @RequestHeader(SecurityConstants.FROM) String from);

    /**
     * PMS 用户过滤
     *
     * @param userPmsPageDTO 查询参数列表
     * @return 用户集合
     */
    @PostMapping("/user/pms/filterUser")
    R<List<SysUserPmsCqVO>> getUserCountQueryPagePms(@RequestBody UserPmsPageDTO userPmsPageDTO
            , @RequestHeader(SecurityConstants.FROM) String from);

    /**
     * PMS 用户过滤
     *
     * @param userPmsPageDTO 查询参数列表
     * @return 用户集合
     */
    @PostMapping("/user/pms/getUserByDataScopePms")
    R<List<SysUserPmsCqVO>> getUserByDataScopePms(@RequestBody UserPmsPageDTO userPmsPageDTO);

    /**
     * 通过ID查询用户部门职责信息
     *
     * @param id 用户ID
     * @return UserJobDeptVo
     */
    @GetMapping("/user/UserJobDept/{id}")
    R<UserJobDeptVo> getUserJobDeptByUserId(@PathVariable("id") Long id);

    @GetMapping("/user/pms/userCq/listChildren/{id}")
    R<List<SysUserPmsCqVO>> listChildrenById(
            @PathVariable("id") Long id,
            @RequestHeader(SecurityConstants.FROM) String from,
            @RequestHeader("tenant-id") Integer tenantId
    );

    /**
     * 查询我和我的下级
     *
     * @param id
     * @param from
     * @param tenantId
     * @return
     */
    @GetMapping("/user/pms/userCq/myListChildren/{id}")
    R<List<SysUserPmsCqVO>> myLstChildrenById(
            @PathVariable("id") Long id,
            @RequestHeader(SecurityConstants.FROM) String from,
            @RequestHeader("tenant-id") Integer tenantId
    );

    @GetMapping("/user/findChildrenBrief/{id}")
    R<List<SysUserVO>> findChildrenBriefById(
            @PathVariable("id") Long id,
            @RequestHeader(SecurityConstants.FROM) String from,
            @RequestHeader(TenantConstants.REQUEST_HEADER) Integer tenantId
    );

    @GetMapping("/user/findCurrentChildrenBrief")
    R<List<SysUserVO>> findCurrentChildrenBrief();


    /**
     * PMS根据userId判断是否是管理员标识 ROLE_ADMIN
     *
     * @param id 用户id
     * @return 是否是管理员标识 ROLE_ADMIN
     */
    @GetMapping("/user/pms/roleAdmin/{id}")
    public R<Boolean> getIsRoleAdmin(@PathVariable("id") Long id
            , @RequestHeader(SecurityConstants.FROM) String from);

    /**
     * PMS根据用户ID获取用户
     *
     * @param id 用户ID
     * @return com.gok.pboot.common.core.util.R<com.gok.pboot.upms.vo.SysUserPmsCqVO>
     * <AUTHOR>
     * @date 2022/9/6 15:35
     */
    @GetMapping("/user/pms/userCq/{id}")
    R<SysUserPmsCqVO> getPmsCqById(@PathVariable("id") Long id, @RequestHeader(SecurityConstants.FROM) String from);

    /**
     * ~ PMS获取所有用户 ~
     *
     * @param from 来源
     * @return com.gok.pboot.common.core.util.R<com.gok.pboot.upms.vo.SysUserPmsCqVO>
     * <AUTHOR>
     * @date 2022/9/20 10:20
     */
    @GetMapping("/user/pms/userCq/list")
    R<List<SysUserPmsCqVO>> listAllPmsUser(
            @RequestHeader(SecurityConstants.FROM) String from,
            @RequestHeader("tenant-id") Integer tenantId
    );


    @GetMapping("/user/info/bind/mini")
    R bind(@RequestParam(value = "username") String username,
           @RequestParam(value = "code") String code,
           @RequestHeader(SecurityConstants.FROM) String from);

    @PostMapping("/user/info/casBind/mini")
    R<Void> casBind(
            @RequestParam(value = "userId") Long userId,
            @RequestParam(value = "username") String username,
            @RequestParam(value = "code") String code,
            @RequestHeader(SecurityConstants.FROM) String from
    );

    @PostMapping("/mini/unbind")
    R<Void> unbindWxOpenId(@RequestParam(value = "userId") Long userId,
            @RequestHeader(SecurityConstants.FROM) String from);

    /**
     * PMS根据userId判断是否是项目经理、销售标识 ROLE_XMJL
     *
     * @param id 用户id
     * @return 是否是项目经理、销售标识 ROLE_XMJL
     */
    @GetMapping("/user/pms/roleXmjl/{id}")
    R<Boolean> getIsRoleXmjl(@PathVariable("id") Long id
            , @RequestHeader(SecurityConstants.FROM) String from);

    @PostMapping("/user/findByDeptIds")
    R<List<SysUserVO>> findByDeptIds(
            @RequestBody List<Long> deptIds,
            @RequestParam(required = false, defaultValue = "false", value = "allowDeleted") Boolean allowDeleted,
            @RequestHeader(SecurityConstants.FROM) String from,
            @RequestHeader(TenantConstants.REQUEST_HEADER) Integer tenantId
    );

    @GetMapping("/user/isRole")
    R<Boolean> isRole(
            @RequestParam("id") Long id,
            @RequestParam("roleCode") String roleCode,
            @RequestHeader(SecurityConstants.FROM) String from
    );

    @GetMapping("/user/hasRoleByCodePrefix")
    R<Boolean> hasRoleByCodePrefix(
            @RequestParam("id") Long id,
            @RequestParam("roleCodePrefix") String roleCodePrefix,
            @RequestHeader(SecurityConstants.FROM) String from
    );

    @PostMapping("/user/getByIds")
    R<List<SysUser>> getByIds(
            @RequestBody List<Long> ids,
            @RequestHeader(SecurityConstants.FROM) String from,
            @RequestHeader("tenant-id") Long tenantId
    );

    @PostMapping("/user/findBriefByIds")
    R<List<SysUserVO>> findBriefByIds(
            @RequestBody List<Long> ids,
            @RequestHeader(SecurityConstants.FROM) String from,
            @RequestHeader("tenant-id") Integer tenantId
    );

    @PostMapping("/user/jx/findByIds")
    R<List<SysUserJxVO>> findJxByIds(
            @RequestBody List<Long> ids,
            @RequestHeader(SecurityConstants.FROM) String from,
            @RequestHeader("tenant-id") Integer tenantId
    );

    @GetMapping("/user/jx/findAll")
    R<List<SysUserJxVO>> findAllJx(
            @RequestHeader(SecurityConstants.FROM) String from,
            @RequestHeader(TenantConstants.REQUEST_HEADER) Integer tenantId
    );

    /**
     * 根据用户id集合获取上级id
     *
     * @param ids
     * @param from
     * @param tenantId
     * @return
     */
    @PostMapping("/user/findManageByIds")
    R<List<SysUser>> findManageByIds(
            @RequestBody List<Long> ids,
            @RequestHeader(SecurityConstants.FROM) String from,
            @RequestHeader("tenant-id") Integer tenantId
    );

    /**
     * 经营分析  人员分页获取  查询有效数据
     *
     * @param pageNo
     * @param pageSize
     * @param username
     * @param nickname
     * @return
     */
    @GetMapping("/user/analysis/validPage")
    R<Page<UserValidPageVO>> getUserValidPage(@RequestParam("pageNumber") int pageNo, @RequestParam("pageSize") int pageSize, @RequestParam("filter_S_username") String username, @RequestParam("filter_S_nickname") String nickname);

    /**
     * 查询某写角色不存在的用户
     *
     * @param dto
     * @create by yzs at 2023/5/16
     * @return: com.gok.pboot.common.core.util.R<java.util.List < com.gok.pboot.upms.entity.SysUser>>
     */
    @PostMapping("/user/findByIdsAndRole")
    R<List<SysUserVO>> findByIdsAndRole(@RequestBody UserAndRoleDTO dto, @RequestHeader(SecurityConstants.FROM) String from, @RequestHeader("tenant-id") Integer tenantId);

    /**
     * 每5分钟更新同步用户信息的缓存
     *
     * @param
     * @create by yzs at 2023/5/23
     * @return: void
     */
    @GetMapping("/user/updateUserCache")
    R updateUserCache(@RequestParam("para") String para, @RequestHeader(SecurityConstants.FROM) String from, @RequestHeader("tenant-id") Integer tenantId);
}
