package com.gok.pboot.pms.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.pboot.pms.entity.domain.ProjectContractAmendment;
import com.gok.pboot.pms.entity.vo.ContractChangeInfoVo;
import com.gok.pboot.pms.entity.vo.OaFileInfoVo;
import com.gok.pboot.pms.entity.vo.OaFileVo;
import com.gok.pboot.pms.mapper.ProjectContractAmendmentMapper;
import com.gok.pboot.pms.oa.OaUtil;
import com.gok.pboot.pms.service.IPmsDocImageFileService;
import com.gok.pboot.pms.service.IProjectContractAmendmentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;


/**
 * 合同变更 Service
 *
 * <AUTHOR>
 * @date 2023/11/21
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProjectContractAmendmentServiceImpl
        extends ServiceImpl<ProjectContractAmendmentMapper, ProjectContractAmendment>
        implements IProjectContractAmendmentService {


    private final IPmsDocImageFileService pmsDocImageFileService;

    private final OaUtil oaUtil;


    @Override
    public List<ContractChangeInfoVo> getContractChangeInfoVoList(Long id) {
        QueryWrapper<ProjectContractAmendment> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ProjectContractAmendment::getLlhtmc,id)
                .orderByDesc(ProjectContractAmendment::getXsjhtqdsj);
        List<ProjectContractAmendment> list = baseMapper.selectList(queryWrapper);
        List<ContractChangeInfoVo> contractChangeInfoVos=new ArrayList<>();
        if(CollectionUtil.isNotEmpty(list)){
             contractChangeInfoVos = BeanUtil.copyToList(list, ContractChangeInfoVo.class);
            for (int i = 0; i <contractChangeInfoVos.size(); i++) {
                ContractChangeInfoVo c = contractChangeInfoVos.get(i);
                ProjectContractAmendment c1 = list.get(i);
                if(Optional.ofNullable(c.getRequestId()).isPresent()
                            &&Optional.ofNullable(c.getNodeoperator()).isPresent()){
                        List<OaFileVo> resourcesData = oaUtil.getResourcesData(String.valueOf(c.getRequestId()),
                                c.getNodeoperator());

                    if (Optional.ofNullable(c1.getHtfj()).isPresent()) {
                        OaFileInfoVo htfjOaFileInfoVo = new OaFileInfoVo();
                        htfjOaFileInfoVo.setRequestId(String.valueOf(c.getRequestId()));
                        htfjOaFileInfoVo.setFileId(c1.getHtfj());
                        htfjOaFileInfoVo.setImageFileId(c1.getHtfjImageFileId());
                        c.setHtfjList(pmsDocImageFileService.getOaOaFileInfoList(htfjOaFileInfoVo
                                , resourcesData));
                    }

                    if (Optional.ofNullable(c1.getXhtfj()).isPresent()) {
                        OaFileInfoVo xhtfjOaFileInfoVo = new OaFileInfoVo();
                        xhtfjOaFileInfoVo.setRequestId(String.valueOf(c.getRequestId()));
                        xhtfjOaFileInfoVo.setFileId(c1.getXhtfj());
                        xhtfjOaFileInfoVo.setImageFileId(c1.getXhtfjImageFileId());
                        c.setXhtfjList(pmsDocImageFileService.getOaOaFileInfoList(xhtfjOaFileInfoVo
                                , resourcesData));
                    }
                    if (Optional.ofNullable(c1.getHtfjygz()).isPresent()) {
                        OaFileInfoVo htfjygzOaFileInfoVo = new OaFileInfoVo();
                        htfjygzOaFileInfoVo.setRequestId(String.valueOf(c.getRequestId()));
                        htfjygzOaFileInfoVo.setFileId(c1.getHtfjygz());
                        htfjygzOaFileInfoVo.setImageFileId(c1.getHtfjygzImageFileId());
                        c.setHtfjygzList(pmsDocImageFileService.getOaOaFileInfoList(htfjygzOaFileInfoVo
                                , resourcesData));
                    }
                    if (Optional.ofNullable(c1.getBgbcjczzxyfjygz()).isPresent()) {
                        OaFileInfoVo bgbcjczzxyfjygzOaFileInfoVo = new OaFileInfoVo();
                        bgbcjczzxyfjygzOaFileInfoVo.setRequestId(String.valueOf(c.getRequestId()));
                        bgbcjczzxyfjygzOaFileInfoVo.setFileId(c1.getBgbcjczzxyfjygz());
                        bgbcjczzxyfjygzOaFileInfoVo.setImageFileId(c1.getBgbcjczzxyfjygzImageFileId());
                        c.setBgbcjczzxyfjygzList(pmsDocImageFileService.getOaOaFileInfoList(bgbcjczzxyfjygzOaFileInfoVo
                                , resourcesData));
                    }
                }

            };
        }

        return contractChangeInfoVos;
    }
}
