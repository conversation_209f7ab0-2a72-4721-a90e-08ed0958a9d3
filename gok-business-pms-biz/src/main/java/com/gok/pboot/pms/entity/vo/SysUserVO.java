/*
 *
 *      Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the pig4cloud.com developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: lengleng (<EMAIL>)
 *
 */

package com.gok.pboot.pms.entity.vo;

import com.gok.bcp.upms.vo.SysUserOutVO;
import lombok.Data;

/**
 * <p>
 * 用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2017-10-29
 */
@Data
public class SysUserVO {

	/**
	 * 用户编号
	 */
	private Long userId;
	/**
	 * 姓名
	 */
	private String name;

	public static SysUserVO from(SysUserOutVO request) {
		SysUserVO result = new SysUserVO();
		result.setUserId(request.getUserId());
		result.setName(request.getUsername());
		return result;
	}
}
