package com.gok.pboot.pms.entity.vo;

import com.gok.pboot.pms.Util.DecimalFormatUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.util.List;

/**
 * 项目合同分页 Vo类
 *
 * <AUTHOR>
 * @date 2023/08/22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class ProjectContractPageVo {

    /**
     * 申请人ID
     */
    private String creatorid;

    /**
     * 流程id
     */
    private String requestid;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 合同id
     */
    private Long contractId;

    /**
     * 合同编号
     */
    private String contractNo;

    /**
     * 合同名称
     */
    private String contractName;

    /**
     * 合同所属公司
     */
    private String affiliatedCompany;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 合同金额（含税）
     */
    private String contractPrice;

    /**
     * 合同金额（不含税）
     */
    private String contractPriceBhs;

    /**
     * 合同起始时间
     */
    private LocalDate contractStartTime;

    /**
     * 合同截止时间
     */
    private LocalDate contractEndTime;

    /**
     * 合同签订日期
     */
    private LocalDate contractSignDate;

    /**
     * 文件列表
     */
    private List<ContractFileVo> contractFileVoList;

    public void setScale(int newScale, RoundingMode roundingMode, DecimalFormat decimalFormat) {
        this.contractPrice = DecimalFormatUtil.setAndValidate(contractPrice, newScale, roundingMode, decimalFormat);
        this.contractPriceBhs = DecimalFormatUtil.setAndValidate(contractPriceBhs, newScale, roundingMode, decimalFormat);
    }

}
