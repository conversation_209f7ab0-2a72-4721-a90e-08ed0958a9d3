package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;

/**
 * 项目业务类型枚举
 *
 * <AUTHOR>
 */
@AllArgsConstructor
public enum ProjectBusinessTypeEnum implements ValueEnum<String> {

    EDUCATION("0", "教学相关"),

    NOT_EDUCATION("1", "非教学相关");

    private final String value;

    private final String name;

    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getName() {
        return name;
    }
}
