package com.gok.pboot.pms.Util;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Locale;
import java.util.Optional;

/**
 * - 日报提交日期相关工具类 -
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/8/24 16:57
 */
public class DailyPaperDateUtils {
    public static final DateTimeFormatter DAILY_PAPER_DATE_FORMATTER =
            DateTimeFormatter.ofPattern("yyyy-MM-dd", Locale.CHINA);

    public static final DateTimeFormatter DAILY_PAPER_DATETIME_FORMATTER =
            DateTimeFormatter.ofPattern("MM-dd HH:mm", Locale.CHINA);

    private DailyPaperDateUtils(){
        throw new RuntimeException();
    }

    public static String asDateString(LocalDate date){
        StringBuilder builder = new StringBuilder(date.format(DAILY_PAPER_DATE_FORMATTER));

        switch (date.getDayOfWeek()){
            case MONDAY:
                builder.append("（周一）");
                break;
            case TUESDAY:
                builder.append("（周二）");
                break;
            case WEDNESDAY:
                builder.append("（周三）");
                break;
            case THURSDAY:
                builder.append("（周四）");
                break;
            case FRIDAY:
                builder.append("（周五）");
                break;
            case SATURDAY:
                builder.append("（周六）");
                break;
            case SUNDAY:
                builder.append("（周日）");
                break;
            default:
                break;
        }

        return builder.toString();
    }

    public static String asDatetimeString(LocalDateTime datetime){
        if(Optional.ofNullable(datetime).isPresent()){
            return datetime.format(DAILY_PAPER_DATETIME_FORMATTER);
        }
        return "-";
    }
}
