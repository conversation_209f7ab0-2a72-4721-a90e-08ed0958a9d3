package com.gok.pboot.pms.enumeration;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;

/**
 * 项目流程信息状态枚举类
 *
 * <AUTHOR>
 * @since 2023-07-19
 **/
@Getter
public enum ProcessInfoStatusEnum implements ValueEnum<Integer> {

    INITIATOR(0, "发起人"),
    ARCHIVE(3, "归档");

    /**
     * 值
     */
    private Integer value;

    /**
     * 名称
     */
    private String name;

    ProcessInfoStatusEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public static String getNameByValue(String value) {
        if (StrUtil.isBlank(value)) {
            return StrUtil.EMPTY;
        }
        Optional<ProcessInfoStatusEnum> result =
                Arrays.stream(ProcessInfoStatusEnum.values()).filter(e -> e.getValue().equals(Integer.valueOf(value))).findAny();
        return result.isPresent() ? result.get().getName() : StrUtil.EMPTY;
    }

}
