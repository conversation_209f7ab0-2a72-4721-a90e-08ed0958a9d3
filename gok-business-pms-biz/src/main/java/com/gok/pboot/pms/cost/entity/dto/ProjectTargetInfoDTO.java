package com.gok.pboot.pms.cost.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProjectTargetInfoDTO {

    /**
     * 版本ID
     */
    private Long versionId;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 流程ID
     */
    private Long requestId;

    /**
     * 流程名称
     */
    private String requestName;

    /**
     * 项目需求
     */
    private String projectRequirements;

    /**
     * 交付要求
     */
    private String deliveryRequirements;

    /**
     * 交付物
     */
    private String deliveryItems;

    /**
     * 交付期限
     */
    private String deliveryDeadline;

    /**
     * 交付地点
     */
    private String deliveryPlace;

    /**
     * 质保期
     */
    private String warrantyPeriod;

    /**
     * 保密要求
     */
    private String secrecyRequirements;

    /**
     * 其他要求
     */
    private String otherRequirements;

    /**
     * 其他要求附件id，多个逗号隔开
     */
    private String detailFiles;

}