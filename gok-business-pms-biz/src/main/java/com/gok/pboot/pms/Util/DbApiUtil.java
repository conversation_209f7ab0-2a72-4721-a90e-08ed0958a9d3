package com.gok.pboot.pms.Util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dtflys.forest.exceptions.ForestNetworkException;
import com.gok.pboot.pms.Util.client.ForestClient;
import com.gok.pboot.pms.common.exception.ServiceException;
import com.gok.pboot.pms.cost.entity.domain.CostConfigAccount;
import com.gok.pboot.pms.cost.entity.dto.ProjectBudgetInfoDTO;
import com.gok.pboot.pms.cost.entity.dto.ProjectTargetInfoDTO;
import com.gok.pboot.pms.cost.entity.vo.CostManageVersionVO;
import com.gok.pboot.pms.cost.entity.vo.SettlementDetailVO;
import com.gok.pboot.pms.entity.vo.*;
import com.gok.pboot.pms.oa.dto.*;
import com.google.common.collect.ImmutableList;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * DBApi接入
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class DbApiUtil {

    final String DBAPI_TOKEN = "dbapi:analysis:token";

    /**
     * DbApi获取token的url地址
     */
    @Value("${dbapi.url}")
    private String url;

    /**
     * DbApi经营分析应用的appId
     */
    @Value("${dbapi.appid}")
    private String appId;

    /**
     * DbApi经营分析应用的secret
     */
    @Value("${dbapi.secret}")
    private String secret;

    /**
     * 项目管理-合同明细url
     */
    @Value("${dbapi.contractDetailsUrl}")
    private String contractDetailsUrl;

    /**
     * 项目管理-人天明细url
     */
    @Value("${dbapi.manDayDetailsUrl}")
    private String manDayDetailsUrl;

    /**
     * 项目管理导出-合同数据url
     */
    @Value("${dbapi.exportContractDetailsUrl}")
    private String exportContractDetailsUrl;

    /**
     * 项目采购付款明细url
     */
    @Value("${dbapi.procureDetailsUrl}")
    private String procureDetailsUrl;

    /**
     * 项目字典url
     */
    @Value("${dbapi.projectDictUrl}")
    private String projectDictUrl;

    /**
     * 项目费用项科目对照代表详情url
     */
    @Value("${dbapi.subjectDetailsUrl}")
    private String subjectDetailsUrl;

    /**
     * 项目费用报销url
     */
    @Value("${dbapi.reimburseDetailsUrl}")
    private String reimburseDetailsUrl;

    /**
     * 项目垫资成本url
     */
    @Value("${dbapi.advanceDetailsUrl}")
    private String advanceDetailsUrl;

    /**
     * 直接人工（项目分摊）明细url
     */
    @Value("${dbapi.totalCostDetailsUrl}")
    private String totalCostDetailsUrl;

    /**
     * 预估外部采购明细url
     */
    @Value("${dbapi.externalProcureDetailsUrl}")
    private String externalProcureDetailsUrl;

    /**
     * 项目详情-合同数据url
     */
    private final static String PROJECT_CONTRACT_URL = "/api/projectContract";

    /**
     * 项目详情-财务数据url
     */
    private final static String FINANCIAL_DATA_URL = "/api/financialData";

    /**
     * 财务数据-追加预算总收入
     */
    private final static String ADDITIONAL_INCOME_URL = "/api/additionalIncome";

    /**
     * 财务数据-已有预算总成本
     */
    private final static String EXISTING_BUDGET_URL = "/api/existingBudget";

    /**
     * 财务数据-已有预算总收入
     */
    private final static String EXISTING_INCOME_URL = "/api/existingIncome";

    /**
     * 财务数据-追加预算总成本
     */
    private final static String ADDITIONAL_COSTS_URL = "/api/additionalCosts";

    private final static String updateProjectXmcyUrl = "/api/updateProjectXmcy";

    private final static String batchExistsOaUserIdUrl = "/api/batchExistsOaUserId";

    private final static String getExistsOaUserIdsUrl = "/api/getExistsOaUserIds";

    private final static String COST_SUBJECT_CONFIG_URL = "/api/costSubjectConfigInfo";

    private final static String PROJECT_TARGET_INFO_URL = "/api/getProjectCurrentTargetInfo";

    private final static String PROJECT_BUDGET_INFO_URL = "/api/getProjectBudgetInfo";

    private final String PROJECT_BUSINESS_INFO_URL = "/api/getGrossProfitMeasurementInfo";

    private static final String PARAM_SUCCESS = "success";
    private static final String PARAM_USER_ID = "user_id";
    private static final String PARAM_OA_USER_ID = "oa_user_id";
    private static final String DPAPI_EXCEPTION_MSG = "DBAPI异常,获取不到明细内容";
    private static final String DPAPI_EXCEPTION = "请求DBAPI接口异常";

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private ForestClient forestClient;

    @PostConstruct
    private void removeToken() {
        stringRedisTemplate.delete(DBAPI_TOKEN);
    }

    /**
     * token获取缓存
     */
    private String readToken() {
        String token = null;
        try {
            if (stringRedisTemplate.hasKey(DBAPI_TOKEN)) {
                token = stringRedisTemplate.opsForValue().get(DBAPI_TOKEN);
            }
        } catch (Exception e) {
            log.error("读取dbapi的token缓存异常", e);
            stringRedisTemplate.delete(DBAPI_TOKEN);
        }
        if (StrUtil.isNotBlank(token)) {
            return token;
        }
        //促发forest进行获取token并写入(加锁防止同时请求token并写入缓存时，出现失效token并覆盖有效token的问题)
        synchronized (this) {
            JSONObject jsonObject = forestClient.getDbApiToken(url, appId, secret);
            token = (String) jsonObject.getOrDefault("token", "");
            if (StringUtils.isEmpty(token)) {
                throw new ServiceException("DBAPI异常,获取不到token信息");
            }
            writeToken(token);
        }
        return token;
    }

    /**
     * token写入缓存
     * dbapi配置一小时失效 缓存设置50分钟
     */
    private void writeToken(String token) {
        //写入redis
        try {
            stringRedisTemplate.opsForValue().set(DBAPI_TOKEN, token, 45, TimeUnit.MINUTES);
        } catch (Exception e) {
            log.error("写入dbapi的token缓存异常", e);
        }
    }

    /**
     * 删除token
     * 防止token修改后无法使用
     */
    private void deleteToken() {
        // 删除token
        try {
            stringRedisTemplate.delete(DBAPI_TOKEN);
        } catch (Exception e) {
            log.error("写入dbapi的token缓存异常", e);
        }
    }


    public List<ProjectDictVo> projectDict(String fieldid) {
        List<ProjectDictVo> result = new ArrayList<>();
        String token = readToken();
        try {
            JSONObject jsonObject = forestClient.projectDict(url, projectDictUrl, token, fieldid);
            if ((Boolean) jsonObject.get(PARAM_SUCCESS)) {
                result = jsonObject.getJSONArray("data").toJavaList(ProjectDictVo.class);
            } else {
                throw new ServiceException(DPAPI_EXCEPTION_MSG);
            }
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }

        return result;
    }

    public List<SubjectDetailsVO> subjectDetails() {
        List<SubjectDetailsVO> result = new ArrayList<>();
        String token = readToken();
        try {
            JSONObject jsonObject = forestClient.subjectDetails(url, subjectDetailsUrl, token);
            if ((Boolean) jsonObject.get(PARAM_SUCCESS)) {
                result = jsonObject.getJSONArray("data").toJavaList(SubjectDetailsVO.class);
            } else {
                throw new ServiceException(DPAPI_EXCEPTION_MSG);
            }
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }

        return result;
    }

    public List<ReimburseDetailsVO> reimburseDetails(String projectId,
                                                     String searchstarttime, String searchendtime, String fyxlb) {
        List<ReimburseDetailsVO> result = new ArrayList<>();
        String token = readToken();
        try {
            JSONObject jsonObject = forestClient.reimburseDetails(url, reimburseDetailsUrl, token
                    , projectId, searchstarttime, searchendtime, fyxlb);
            if ((Boolean) jsonObject.get(PARAM_SUCCESS)) {
                result = jsonObject.getJSONArray("data").toJavaList(ReimburseDetailsVO.class);
            } else {
                throw new ServiceException(DPAPI_EXCEPTION_MSG);
            }
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }

        return result;
    }

    /**
     * 项目详情-合同数据
     *
     * @param projectId    项目id
     * @param contractType 合同类型
     * @return {@link List}<{@link ProjectContractPageVo}>
     */
    public List<ProjectContractPageVo> projectContract(String projectId, String contractType) {
        List<ProjectContractPageVo> result = new ArrayList<>();
        String token = readToken();
        try {
            JSONObject jsonObject = forestClient.projectContract(url, PROJECT_CONTRACT_URL, token,
                    projectId, contractType);
            if ((Boolean) jsonObject.get(PARAM_SUCCESS)) {
                result = jsonObject.getJSONArray("data").toJavaList(ProjectContractPageVo.class);
            } else {
                throw new ServiceException(DPAPI_EXCEPTION_MSG);
            }
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }

        return result;
    }

    /**
     * 项目详情-财务数据
     *
     * @param projectId 项目id
     * @return {@link FinancialDataVo}
     */
    public FinancialDataVo financialData(String projectId, String searchstarttime, String searchendtime) {
        FinancialDataVo result = new FinancialDataVo();
        String token = readToken();
        try {
            JSONObject jsonObject = forestClient.financialData(url, FINANCIAL_DATA_URL, token, projectId, searchstarttime, searchendtime);
            if ((Boolean) jsonObject.get("success")) {
                List<FinancialDataVo> datas = jsonObject.getJSONArray("data").toJavaList(FinancialDataVo.class);
                result = Optional.ofNullable(datas).isPresent() && datas.isEmpty() ? result : datas.get(0);
            } else {
                throw new ServiceException(DPAPI_EXCEPTION_MSG);
            }
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }

        return result;
    }

    /**
     * 财务数据-追加预算总收入
     *
     * @param projectId 项目id
     * @return {@link List}<{@link AdditionalIncomePageVo}>
     */
    public List<AdditionalIncomePageVo> additionalIncome(String projectId, String searchstarttime, String searchendtime) {
        List<AdditionalIncomePageVo> result = new ArrayList<>();
        String token = readToken();
        try {
            JSONObject jsonObject = forestClient.additionalIncome(url, ADDITIONAL_INCOME_URL, token, projectId, searchstarttime, searchendtime);
            if ((Boolean) jsonObject.get("success")) {
                result = jsonObject.getJSONArray("data").toJavaList(AdditionalIncomePageVo.class);
            } else {
                throw new ServiceException(DPAPI_EXCEPTION_MSG);
            }
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }

        return result;
    }

    /**
     * 财务数据-已有预算总收入
     *
     * @param projectId 项目id
     * @return {@link List}<{@link AdditionalIncomePageVo}>
     */
    public List<AdditionalIncomePageVo> existingIncome(String projectId, String searchstarttime, String searchendtime) {
        List<AdditionalIncomePageVo> result = new ArrayList<>();
        String token = readToken();
        try {
            JSONObject jsonObject = forestClient.existingIncome(url, EXISTING_INCOME_URL, token, projectId, searchstarttime, searchendtime);
            if ((Boolean) jsonObject.get("success")) {
                result = jsonObject.getJSONArray("data").toJavaList(AdditionalIncomePageVo.class);
            } else {
                throw new ServiceException(DPAPI_EXCEPTION_MSG);
            }
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }

        return result;
    }

    /**
     * 财务数据-已有预算总收入
     *
     * @param projectId 项目id
     * @return {@link List}<{@link AdditionalCostsPageVo}>
     */
    public List<AdditionalCostsPageVo> additionalCosts(String projectId, String searchstarttime, String searchendtime) {
        List<AdditionalCostsPageVo> result = new ArrayList<>();
        String token = readToken();
        try {
            JSONObject jsonObject = forestClient.additionalCosts(url, ADDITIONAL_COSTS_URL, token, projectId, searchstarttime, searchendtime);
            if ((Boolean) jsonObject.get("success")) {
                result = jsonObject.getJSONArray("data").toJavaList(AdditionalCostsPageVo.class);
            } else {
                throw new ServiceException(DPAPI_EXCEPTION_MSG);
            }
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }

        return result;
    }

    /**
     * 财务数据-已有预算总收入
     *
     * @param projectId 项目id
     * @return {@link List}<{@link AdditionalIncomePageVo}>
     */
    public List<AdditionalCostsPageVo> existingBudget(String projectId, String searchstarttime, String searchendtime) {
        List<AdditionalCostsPageVo> result = new ArrayList<>();
        String token = readToken();
        try {
            JSONObject jsonObject = forestClient.existingBudget(url, EXISTING_BUDGET_URL, token, projectId, searchstarttime, searchendtime);
            if ((Boolean) jsonObject.get("success")) {
                result = jsonObject.getJSONArray("data").toJavaList(AdditionalCostsPageVo.class);
            } else {
                throw new ServiceException(DPAPI_EXCEPTION_MSG);
            }
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }

        return result;
    }

    /**
     * 项目管理-人天明细
     *
     * @param projectId       项目id
     * @param workType        工作类型
     * @param searchstarttime 起始时间
     * @param searchendtime   终止时间
     * @param userStatus      用户状态
     * @return {@link List}<{@link ManDayDetailsPageVo}>
     */
    public List<ManDayDetailsPageVo> manDayDetails(Long projectId, String workType,
                                                   String searchstarttime, String searchendtime, String userStatus) {
        List<ManDayDetailsPageVo> result = new ArrayList<>();
        String token = readToken();
        try {
            JSONObject jsonObject = forestClient
                    .manDays(url, manDayDetailsUrl, token, projectId, userStatus, workType, searchstarttime, searchendtime);
            if ((Boolean) jsonObject.get(PARAM_SUCCESS)) {
                result = jsonObject.getJSONArray("data").toJavaList(ManDayDetailsPageVo.class);
            } else {
                throw new ServiceException(DPAPI_EXCEPTION_MSG);
            }
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }

        return result;
    }

    /**
     * 项目管理-查询直接人工（项目分摊）明细
     *
     * @param projectName     项目名称
     * @param searchstarttime 起始时间
     * @param searchendtime   终止时间
     * @return {@link List}<{@link TotalCostDetailsPageVo}>
     */
    public List<TotalCostDetailsPageVo> totalCostDetails(String projectName, String searchstarttime,
                                                         String searchendtime) {
        List<TotalCostDetailsPageVo> result = new ArrayList<>();
        String token = readToken();
        try {
            JSONObject jsonObject = forestClient.
                    totalCostDetails(url, totalCostDetailsUrl, token, projectName, searchstarttime, searchendtime);
            if ((Boolean) jsonObject.get(PARAM_SUCCESS)) {
                result = jsonObject.getJSONArray("data").toJavaList(TotalCostDetailsPageVo.class);
            } else {
                throw new ServiceException(DPAPI_EXCEPTION_MSG);
            }
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }

        return result;
    }

    /**
     * 项目采购付款明细
     *
     * @param projectId       项目id
     * @param searchstarttime 起始时间
     * @param searchendtime   终止时间
     * @return {@link List}<{@link ProcureDetailsPageVo}>
     */
    public List<ProcureDetailsPageVo> procureDetails(String projectId, String searchstarttime,
                                                     String searchendtime) {
        List<ProcureDetailsPageVo> result = new ArrayList<>();
        String token = readToken();
        try {
            JSONObject jsonObject = forestClient
                    .procureDetails(url, procureDetailsUrl, token, projectId, searchstarttime, searchendtime);
            if ((Boolean) jsonObject.get(PARAM_SUCCESS)) {
                result = jsonObject.getJSONArray("data").toJavaList(ProcureDetailsPageVo.class);
            } else {
                throw new ServiceException(DPAPI_EXCEPTION_MSG);
            }
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }

        return result;
    }

    /**
     * 项目管理-查询合同明细
     *
     * @param projectId       项目id
     * @param contractType    合同类型
     * @param searchstarttime 起始时间
     * @param searchendtime   终止时间
     * @return {@link List}<{@link ContractDetailsPageVo}>
     */
    public List<ContractDetailsPageVo> contractDetails(String projectId, String contractType,
                                                       String searchstarttime, String searchendtime) {
        List<ContractDetailsPageVo> result = new ArrayList<>();
        String token = readToken();
        try {
            JSONObject jsonObject = forestClient.contractDetails(url, contractDetailsUrl, token, contractType, projectId,
                    searchstarttime, searchendtime);
            if ((Boolean) jsonObject.get(PARAM_SUCCESS)) {
                result = jsonObject.getJSONArray("data").toJavaList(ContractDetailsPageVo.class);
            } else {
                throw new ServiceException(DPAPI_EXCEPTION_MSG);
            }
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }

        return result;
    }

    /**
     * 修改OA项目成员
     *
     * @param projectId 项目id
     * @param xmcy      项目成员
     * @return int
     */
    public void updateProjectXmcy(String projectId, String xmcy) {
        String token = readToken();
        try {
            JSONObject jsonObject = forestClient.updateProjectXmcy(url, updateProjectXmcyUrl, token, projectId, xmcy);
            if (!Boolean.TRUE.equals(jsonObject.get(PARAM_SUCCESS))) {
                throw new ServiceException(DPAPI_EXCEPTION_MSG);
            }
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }
    }

    /**
     * 获取oa存在账号用户id
     *
     * @param userIds 用户id列表
     * @return {@link List}<{@link Long}>
     */
    public List<Long> batchExistsOaUserId(List<String> userIds) {
        String token = readToken();
        try {
            JSONObject jsonObject = forestClient.batchExistsOaUserId(url, batchExistsOaUserIdUrl, token, userIds);
            if (!Boolean.TRUE.equals(jsonObject.get(PARAM_SUCCESS))) {
                throw new ServiceException(DPAPI_EXCEPTION_MSG);
            } else {
                JSONArray jsonArray = jsonObject.getJSONArray("data");
                if (CollUtil.isNotEmpty(jsonArray)) {
                    return jsonArray.stream().map(j -> {
                        JSONObject json = (JSONObject) j;
                        return json.getLong(PARAM_OA_USER_ID);
                    }).collect(Collectors.toList());
                }
                return ImmutableList.of();
            }
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }
        return null;
    }

    /**
     * 获取oa存在账号用户id
     *
     * @param userIds 用户id列表
     * @return {@link List}<{@link Long}>
     */
    public List<Long> getExistsOaUserIds(List<String> userIds) {
        String token = readToken();
        try {
            JSONObject jsonObject = forestClient.getExistsOaUserIds(url, getExistsOaUserIdsUrl, token, userIds);
            if (!Boolean.TRUE.equals(jsonObject.get(PARAM_SUCCESS))) {
                throw new ServiceException(DPAPI_EXCEPTION_MSG);
            } else {
                JSONArray jsonArray = jsonObject.getJSONArray("data");
                if (CollUtil.isNotEmpty(jsonArray)) {
                    return jsonArray.stream().map(j -> {
                        JSONObject json = (JSONObject) j;
                        return json.getLong(PARAM_USER_ID);
                    }).collect(Collectors.toList());
                }
                return ImmutableList.of();
            }
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }
        return null;
    }

    /**
     * 客户台帐
     *
     * @param customerName 客户名称
     * @return {@link List}<{@link CustomerAccountVO}>
     */
    public List<CustomerAccountVO> getCustomerAccount(String customerName) {
        List<CustomerAccountVO> result = new ArrayList<>();
        try {
            JSONObject jsonObject = forestClient.customerAccount(url, readToken(), customerName);
            if ((boolean) jsonObject.get(PARAM_SUCCESS)) {
                result = jsonObject.getJSONArray("data").toJavaList(CustomerAccountVO.class);
            } else {
                throw new ServiceException(DPAPI_EXCEPTION_MSG);
            }
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }
        return result;
    }

    /**
     * 银行台帐
     *
     * @param name 银行名称
     * @return {@link List}<{@link BackAccountVO}>
     */
    public List<BackAccountVO> getBankAccount(String name) {
        List<BackAccountVO> result = new ArrayList<>();
        try {
            JSONObject jsonObject = forestClient.bankAccount(url, readToken(), name);
            if ((boolean) jsonObject.get(PARAM_SUCCESS)) {
                result = jsonObject.getJSONArray("data").toJavaList(BackAccountVO.class);
            } else {
                throw new ServiceException(DPAPI_EXCEPTION_MSG);
            }
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }
        return result;
    }

    /**
     * 根据合同编码搜索归属合同收款明细
     *
     * @param id id
     * @return {@link List}<{@link ContractPaymentVO2}>
     */
    public List<ContractPaymentVO2> getContractPayment(String id) {
        List<ContractPaymentVO2> result = new ArrayList<>();
        try {
            JSONObject jsonObject = forestClient.contractPayment(url, readToken(), id);
            if ((boolean) jsonObject.get(PARAM_SUCCESS)) {
                result = jsonObject.getJSONArray("data").toJavaList(ContractPaymentVO2.class);
            } else {
                throw new ServiceException(DPAPI_EXCEPTION_MSG);
            }
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }
        return result;
    }

    /**
     * 根据合同名称搜索归属合同收款明细
     *
     * @param contractName 合同名称
     * @return {@link List}<{@link ContractPaymentVO2>}>
     */
    public List<ContractPaymentVO2> getContractName(String contractName) {
        List<ContractPaymentVO2> result = new ArrayList<>();
        try {
            JSONObject jsonObject = forestClient.contractName(url, readToken(), contractName);
            if ((boolean) jsonObject.get(PARAM_SUCCESS)) {
                result = jsonObject.getJSONArray("data").toJavaList(ContractPaymentVO2.class);
            } else {
                throw new ServiceException(DPAPI_EXCEPTION_MSG);
            }
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }
        return result;
    }

    /**
     * 项目台帐
     *
     * @param projectName 项目名称
     * @return {@link List}<{@link ProjectAccountVO}>
     */
    public List<ProjectAccountVO> getProjectAccount(String projectName) {
        List<ProjectAccountVO> result = new ArrayList<>();
        try {
            JSONObject jsonObject = forestClient.projectAccount(url, readToken(), projectName);
            if ((boolean) jsonObject.get(PARAM_SUCCESS)) {
                result = jsonObject.getJSONArray("data").toJavaList(ProjectAccountVO.class);
            } else {
                throw new ServiceException(DPAPI_EXCEPTION_MSG);
            }
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }
        return result;
    }

    /**
     * 归属合同收款明细
     *
     * @param contractNumber 合同编码列表
     * @return {@link List}<{@link ContractPaymentVO2}>
     */
    public List<ContractPaymentVO2> getContractPaymentByNumber(List<String> contractNumber) {
        List<ContractPaymentVO2> result = new ArrayList<>();
        try {
            JSONObject jsonObject = forestClient.contractPaymentByNumber(url, readToken(), contractNumber);
            if ((boolean) jsonObject.get(PARAM_SUCCESS)) {
                result = jsonObject.getJSONArray("data").toJavaList(ContractPaymentVO2.class);
            } else {
                throw new ServiceException(DPAPI_EXCEPTION_MSG);
            }
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }
        return result;
    }

    /**
     * 项目台帐
     *
     * @param projectName 项目名称集合
     * @param projectId 项目Id集合
     * @return {@link List}<{@link ProjectAccountVO}>
     */
    public List<ProjectAccountVO> getProjectAccountByName(List<String> projectName, List<Long> projectId) {
        List<ProjectAccountVO> result = new ArrayList<>();
        try {
            JSONObject jsonObject = forestClient.projectAccountByName(url, readToken(), projectName, projectId);
            if ((boolean) jsonObject.get(PARAM_SUCCESS)) {
                result = jsonObject.getJSONArray("data").toJavaList(ProjectAccountVO.class);
            } else {
                throw new ServiceException(DPAPI_EXCEPTION_MSG);
            }
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }
        return result;
    }


    /**
     * 根据合同编码搜索归属合同收款明细
     *
     * @param contractId 合同ID
     * @return {@link List}<{@link ContractChangeInfoVo}>
     */
    public List<ContractAcceptanceVo> getAcceptanceRecordsUrl(Long contractId) {
        List<ContractAcceptanceVo> result = new ArrayList<>();
        try {
            JSONObject jsonObject = forestClient.getAcceptanceRecordsUrl(url, readToken(), contractId);
            if ((boolean) jsonObject.get(PARAM_SUCCESS)) {
                result = jsonObject.getJSONArray("data").toJavaList(ContractAcceptanceVo.class);
            } else {
                throw new ServiceException(DPAPI_EXCEPTION_MSG);
            }
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }
        return result;
    }

    /**
     * 根据合同编码搜索归属合同收款明细
     *
     * @param contractIds 合同IDs
     * @return {@link List}<{@link ContractChangeInfoVo}>
     */
    public List<ContractAcceptanceVo> getAcceptanceRecordsUrlByIds(List<Long> contractIds) {
        List<ContractAcceptanceVo> result = new ArrayList<>();
        try {
            JSONObject jsonObject = forestClient.getAcceptanceRecordsUrlByIds(url, readToken(), contractIds);
            if ((boolean) jsonObject.get(PARAM_SUCCESS)) {
                result = jsonObject.getJSONArray("data").toJavaList(ContractAcceptanceVo.class);
            } else {
                throw new ServiceException(DPAPI_EXCEPTION_MSG);
            }
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }
        return result;
    }

    public List<ContractRiskInfoVO> getContractRiskInfoVo(Long contractId) {
        List<ContractRiskInfoVO> result = new ArrayList<>();
        try {
            JSONObject jsonObject = forestClient.getContractRiskUrl(url, readToken(), contractId);
            if ((boolean) jsonObject.get(PARAM_SUCCESS)) {
                result = jsonObject.getJSONArray("data").toJavaList(ContractRiskInfoVO.class);
            } else {
                throw new ServiceException(DPAPI_EXCEPTION_MSG);
            }
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }
        return result;
    }

    public List<ContractRiskInfoVO> getContractRiskInfoVoByIds(List<Long> contractIds) {
        List<ContractRiskInfoVO> result = new ArrayList<>();
        try {
            JSONObject jsonObject = forestClient.getContractRiskUrlByIds(url, readToken(), contractIds);
            if ((boolean) jsonObject.get(PARAM_SUCCESS)) {
                result = jsonObject.getJSONArray("data").toJavaList(ContractRiskInfoVO.class);
            } else {
                throw new ServiceException(DPAPI_EXCEPTION_MSG);
            }
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }
        return result;
    }

    /**
     * 获取OA用户信息
     */
    public OaAccountVO getOaAccountInfoByUserId(Long id) {
        OaAccountVO oaAccountVO = new OaAccountVO();
        try {
            JSONObject jsonObject = forestClient.getOaAccountById(url, readToken(), id);
            if ((boolean) jsonObject.get(PARAM_SUCCESS)) {
                List<OaAccountVO> data = jsonObject.getJSONArray("data").toJavaList(OaAccountVO.class);
                if (CollUtil.isNotEmpty(data)) {
                    oaAccountVO = data.get(0);
                }
            } else {
                throw new ServiceException(DPAPI_EXCEPTION_MSG);
            }
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }
        return oaAccountVO;
    }

    /**
     * 获取OA用户信息
     */
    public BusinessCaseVO getBusinessCaseIdByProjectId(Long id) {
        BusinessCaseVO vo = null;
        try {
            JSONObject jsonObject = forestClient.getBusinessCaseIdByProjectId(url, readToken(), id);
            if ((boolean) jsonObject.get(PARAM_SUCCESS)) {
                List<BusinessCaseVO> data = jsonObject.getJSONArray("data").toJavaList(BusinessCaseVO.class);
                if (CollUtil.isNotEmpty(data)) {
                    vo = data.get(0);
                }
            } else {
                throw new ServiceException(DPAPI_EXCEPTION_MSG);
            }
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }
        return vo;
    }

    /**
     * 获取OA用户信息
     */
    public void checkToken(String token, Long id) {
        forestClient.getOaAccountById(url, token, id);
    }


    /**
     * 插入OA流程字典
     */
    public void insertWorkflowSelectitem(String fieldid, String selectvalue, String selectname,
                                         String pubid, String uuid) {
        String token = readToken();
        try {
            JSONObject jsonObject = forestClient.insertWorkflowSelectitem(url, token, fieldid, "1", selectvalue, selectname, selectvalue, "0", "-1,-1,-1", "0", "0", pubid, uuid);
            if (!Boolean.TRUE.equals(jsonObject.get(PARAM_SUCCESS))) {
                throw new ServiceException(DPAPI_EXCEPTION_MSG);
            }
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }
    }


    /**
     * 更新OA流程字典
     */
    public void updateWorkflowSelectitem(String selectname, String cancel, String pubid, String uuid) {
        String token = readToken();
        try {
            JSONObject jsonObject = forestClient.updateWorkflowSelectitem(url, token, selectname, cancel, pubid, uuid);
            if (!Boolean.TRUE.equals(jsonObject.get(PARAM_SUCCESS))) {
                throw new ServiceException(DPAPI_EXCEPTION_MSG);
            }
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }
    }

    /**
     * 查询OA流程字典最大值
     */
    public int getWorkflowSelectitemMax(String fieldid) {
        int result = 0;
        String token = readToken();
        try {
            JSONObject jsonObject = forestClient.getWorkflowSelectitemMax(url, token, fieldid);
            if ((Boolean) jsonObject.get("success")) {
                JSONArray jsonArray = jsonObject.getJSONArray("data");
                result = Optional.ofNullable(jsonArray).isPresent() && jsonArray.isEmpty() ? result : (int) ((JSONObject) jsonArray.get(0)).get("MAX");
            } else {
                throw new ServiceException(DPAPI_EXCEPTION_MSG);
            }
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }

        return result;
    }

    /**
     * 插入OA公共字典
     */
    public void insertModeSelectitempagedetail(String mainid, String name, String disorder, String pid, String statelev, String uuid) {
        String token = readToken();
        try {
            JSONObject jsonObject = forestClient.insertModeSelectitempagedetail(url, token, mainid, name, disorder, "-1,-1,-1", "0", pid, statelev, "0", uuid);
            if (!Boolean.TRUE.equals(jsonObject.get(PARAM_SUCCESS))) {
                throw new ServiceException(DPAPI_EXCEPTION_MSG);
            }
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }
    }

    /**
     * 更新OA公共字典
     */
    public void updateModeSelectitempagedetail(String name, String cancel, String pid, String uuid) {
        String token = readToken();
        try {
            JSONObject jsonObject = forestClient.updateModeSelectitempagedetail(url, token, name, cancel, pid, uuid);
            if (!Boolean.TRUE.equals(jsonObject.get(PARAM_SUCCESS))) {
                throw new ServiceException(DPAPI_EXCEPTION_MSG);
            }
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }
    }


    /**
     * 查询OA公共字典id
     */
    public String getModeSelectitempagedetail(String uuid) {
        String result = "";
        String token = readToken();
        try {
            JSONObject jsonObject = forestClient.getModeSelectitempagedetail(url, token, uuid);
            log.info("jsonObject:" + jsonObject);

            if ((Boolean) jsonObject.get("success")) {
                JSONArray jsonArray = jsonObject.getJSONArray("data");
                result = Optional.ofNullable(jsonArray).isPresent() && jsonArray.isEmpty() ? result : String.valueOf(((JSONObject) jsonArray.get(0)).get("ID"));
            } else {
                throw new ServiceException(DPAPI_EXCEPTION_MSG);
            }
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }

        return result;
    }


    /**
     * 查询OA公共字典最大值
     */
    public int getModeSelectitempagedetailMax(String mainid, String statelev) {
        int result = 0;
        String token = readToken();
        try {
            JSONObject jsonObject = forestClient.getModeSelectitempagedetailMax(url, token, mainid, statelev);
            if ((Boolean) jsonObject.get("success")) {
                JSONArray jsonArray = jsonObject.getJSONArray("data");
                result = Optional.ofNullable(jsonArray).isPresent() && jsonArray.isEmpty() ? result : (int) ((JSONObject) jsonArray.get(0)).get("MAX");
            } else {
                throw new ServiceException(DPAPI_EXCEPTION_MSG);
            }
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }

        return result;
    }

    /**
     * 获取成本科目配置信息列表
     *
     * @return data
     */
    public List<CostConfigAccount> getCostSubjectConfigInfoList() {
        List<CostConfigAccount> result = new ArrayList<>();
        String token = readToken();
        try {
            JSONObject jsonObject = forestClient.getCostSubjectConfigInfoList(url, COST_SUBJECT_CONFIG_URL, token);
            if ((Boolean) jsonObject.get(PARAM_SUCCESS)) {
                result = jsonObject.getJSONArray("data").toJavaList(CostConfigAccount.class);
            } else {
                throw new ServiceException(DPAPI_EXCEPTION_MSG);
            }
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }
        return result;
    }

    /**
     * 处理OA请求状态的通用方法
     *
     * @param requestIds 请求ID列表
     * @return JSON响应对象
     */
    private JSONObject handleOaRequestStatus(List<Long> requestIds) {
        if (ObjectUtil.isEmpty(requestIds)) {
            return null;
        }
        String token = readToken();
        try {
            JSONObject jsonObject = forestClient.getOARequestStatus(url, token, requestIds);
            if (jsonObject.getBoolean(PARAM_SUCCESS)) {
                return jsonObject;
            } else {
                throw new ServiceException(DPAPI_EXCEPTION_MSG);
            }
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
            return null;
        }
    }

    /**
     * 查询OA流程状态（批量）
     *
     * @param requestIds 请求ID列表
     * @param clazz      返回对象类型
     * @return 指定类型的对象列表
     */
    public <T> List<T> getOaRequestStatusToObj(List<Long> requestIds, Class<T> clazz) {
        List<T> result = new ArrayList<>();
        requestIds = requestIds.stream().filter(Objects::nonNull).collect(Collectors.toList());
        JSONObject response = handleOaRequestStatus(requestIds);
        if (response != null) {
            result = response.getJSONArray("data").toJavaList(clazz);
        }
        return result;
    }


    /**
     * 查询单个OA流程状态
     *
     * @param requestId 请求ID
     * @return 流程状态值，如果查询失败返回null
     */
    public Integer getOaRequestStatus(Long requestId) {
        if (requestId == null) {
            return null;
        }
        JSONObject response = handleOaRequestStatus(Collections.singletonList(requestId));
        if (response != null) {
            log.error(response.toJSONString());
            JSONArray dataArray = response.getJSONArray("data");
            if (!dataArray.isEmpty()) {
                return dataArray.getJSONObject(0).getInteger("REQUESTSTATUS");
            }
            return null;
        }
        return null;
    }

    /**
     * 查询单个OA流程状态
     *
     * @return 流程状态值，如果查询失败返回null
     */
    public Map<Long, Integer> getOaRequestStatus(List<Long> requestIds) {
        if (CollUtil.isEmpty(requestIds)) {
            return Collections.emptyMap();
        }
        Map<Long, Integer> result = new HashMap<>(requestIds.size());
        requestIds = requestIds.stream().filter(Objects::nonNull).collect(Collectors.toList());
        JSONObject response = handleOaRequestStatus(requestIds);
        if (response != null) {
            JSONArray dataArray = response.getJSONArray("data");
            for (int i = 0; i < dataArray.size(); i++) {
                JSONObject item = dataArray.getJSONObject(i);
                Long requestId = item.getLong("REQUESTID");
                Integer requestStatus = item.getInteger("REQUESTSTATUS");
                result.put(requestId, requestStatus);
            }
        }
        return result;
    }

    /**
     * 同步OA项目台账最新目标内容
     *
     * @return {@link List}<{@link ProjectTargetInfoDTO}>
     */
    public List<ProjectTargetInfoDTO> getProjectTargetInfo() {
        List<ProjectTargetInfoDTO> result = new ArrayList<>();
        String token = readToken();
        try {
            JSONObject jsonObject = forestClient.getProjectTargetInfo(url, PROJECT_TARGET_INFO_URL, token);
            if ((Boolean) jsonObject.get(PARAM_SUCCESS)) {
                result = jsonObject.getJSONArray("data").toJavaList(ProjectTargetInfoDTO.class);
            } else {
                throw new ServiceException(DPAPI_EXCEPTION_MSG);
            }
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }
        return result;
    }

    /**
     * 同步OA项目预算台账最新成本管理信息内容
     *
     * @return {@link List}<{@link ProjectBudgetInfoDTO}>
     */
    public List<ProjectBudgetInfoDTO> getProjectBudgetInfo(Long start, Long end) {
        List<ProjectBudgetInfoDTO> result = new ArrayList<>();
        String token = readToken();
        try {
            JSONObject jsonObject = forestClient.getProjectBudgetInfo(url, PROJECT_BUDGET_INFO_URL, token, start, end);
            if ((Boolean) jsonObject.get(PARAM_SUCCESS)) {
                result = jsonObject.getJSONArray("data").toJavaList(ProjectBudgetInfoDTO.class);
            } else {
                throw new ServiceException(DPAPI_EXCEPTION_MSG);
            }
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }
        return result;
    }

    /**
     * 查询OA流程项目商业论证(A表)
     */
    public String getOARequestByA(Long projectId) {
        String result = null;
        String token = readToken();
        try {
            JSONObject jsonObject = forestClient.getOARequestByA(url, token, projectId);
            if ((Boolean) jsonObject.get("success")) {
                JSONArray jsonArray = jsonObject.getJSONArray("data");
                result = Optional.ofNullable(jsonArray).isPresent() && jsonArray.isEmpty() ? result : (String) ((JSONObject) jsonArray.get(0)).get("REQUESTNAME");
            } else {
                throw new ServiceException(DPAPI_EXCEPTION_MSG);
            }
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }

        return result;
    }

    /**
     * 查询OA流程项目立项申请(B表)
     */
    public String getOARequestByB(Long projectId) {
        String result = null;
        String token = readToken();
        try {
            JSONObject jsonObject = forestClient.getOARequestByB(url, token, projectId);
            if ((Boolean) jsonObject.get("success")) {
                JSONArray jsonArray = jsonObject.getJSONArray("data");
                result = Optional.ofNullable(jsonArray).isPresent() && jsonArray.isEmpty() ? result : (String) ((JSONObject) jsonArray.get(0)).get("REQUESTNAME");
            } else {
                throw new ServiceException(DPAPI_EXCEPTION_MSG);
            }
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }

        return result;
    }

    /**
     * 查询OA流程项目变更、AB表
     */
    public String getOARequestByXMBG(Long projectId) {
        String result = null;
        String token = readToken();
        try {
            JSONObject jsonObject = forestClient.getOARequestByXMBG(url, token, projectId);
            if ((Boolean) jsonObject.get("success")) {
                JSONArray jsonArray = jsonObject.getJSONArray("data");
                result = Optional.ofNullable(jsonArray).isPresent() && jsonArray.isEmpty() ? result : (String) ((JSONObject) jsonArray.get(0)).get("REQUESTNAME");
            } else {
                throw new ServiceException(DPAPI_EXCEPTION_MSG);
            }
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }

        return result;
    }


    /**
     * 查询OA流程是否被删除
     */
    public List<CostManageVersionVO> getOARequest(Set<Long> requestIds) {
        List<CostManageVersionVO> result = new ArrayList<>();
        if (ObjectUtil.isEmpty(requestIds)) {
            return result;
        }
        String token = readToken();
        try {
            JSONObject jsonObject = forestClient.getOARequest(url, token, requestIds);
            if ((Boolean) jsonObject.get(PARAM_SUCCESS)) {
                result = jsonObject.getJSONArray("data").toJavaList(CostManageVersionVO.class);
            } else {
                throw new ServiceException(DPAPI_EXCEPTION_MSG);
            }
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }

        return result;
    }

    /**
     * 查询OA流程是否被删除
     */
    public List<SettlementDetailVO> getSettlementDetail(Long requestId) {
        List<SettlementDetailVO> result = new ArrayList<>();
        if (!Optional.ofNullable(requestId).isPresent()) {
            return result;
        }
        String token = readToken();
        try {
            JSONObject jsonObject = forestClient.getSettlementDetail(url, token, requestId);
            if ((Boolean) jsonObject.get(PARAM_SUCCESS)) {
                result = jsonObject.getJSONArray("data").toJavaList(SettlementDetailVO.class);
            } else {
                throw new ServiceException(DPAPI_EXCEPTION_MSG);
            }
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }

        return result;
    }


    /**
     * 通过OA流程名称查询流程workflowId
     */
    public Integer getWorkflowIdByName(String workflowName) {
        Integer result = null;
        String token = readToken();
        try {
            JSONObject jsonObject = forestClient.getWorkflowIdByName(url, token, workflowName);
            if ((Boolean) jsonObject.get("success")) {
                JSONArray jsonArray = jsonObject.getJSONArray("data");
                result = CollUtil.isEmpty(jsonArray) ? null : ((JSONObject) jsonArray.get(0)).getInteger("ID");
            } else {
                throw new ServiceException(DPAPI_EXCEPTION_MSG);
            }
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }
        if (result == null) {
            throw new ServiceException("OA未找到相关流程!");
        }

        return result;
    }

    /**
     * 获取项目预算台账信息
     *
     * @param projectId 项目 ID
     * @param projectIds 项目 IDS
     * @return {@link List }<{@link OaXmysbDTO }>
     */
    public List<OaXmysbDTO> getInfoByXmysb(Long projectId, List<Long> projectIds) {
        String token = readToken();
        JSONObject jsonObject = null;
        try {
            jsonObject = forestClient.getInfoByXmysb(url, token, projectId, projectIds);
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }
        if (jsonObject != null && jsonObject.getBoolean("success")) {
            JSONArray jsonArray = jsonObject.getJSONArray("data");
            return jsonArray.toJavaList(OaXmysbDTO.class);
        } else {
            throw new ServiceException(DPAPI_EXCEPTION_MSG);
        }
    }


    /**
     * 获取部门月预算
     *
     * @return {@link List }<{@link OaBmyysbDTO }>
     */
    public List<OaBmyysbDTO> getInfoByBmyysb() {
        String token = readToken();
        JSONObject jsonObject = null;
        try {
            jsonObject = forestClient.getInfoByBmyysb(url, token);
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }
        if (jsonObject != null && jsonObject.getBoolean("success")) {
            JSONArray jsonArray = jsonObject.getJSONArray("data");
            return jsonArray.toJavaList(OaBmyysbDTO.class);
        } else {
            throw new ServiceException(DPAPI_EXCEPTION_MSG);
        }
    }

    /**
     * 获取OA部门
     *
     * @return {@link List }<{@link OaDeptDTO }>
     */
    public List<OaDeptDTO> getInfoByDept() {
        String token = readToken();
        JSONObject jsonObject = null;
        try {
            jsonObject = forestClient.getInfoByDept(url, token);
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }
        if (jsonObject != null && jsonObject.getBoolean("success")) {
            JSONArray jsonArray = jsonObject.getJSONArray("data");
            return jsonArray.toJavaList(OaDeptDTO.class);
        } else {
            throw new ServiceException(DPAPI_EXCEPTION_MSG);
        }
    }

    /**
     * 查询OA在途流程预算
     *
     * @return {@link List }<{@link OaDeptDTO }>
     */
    public List<OaBudgetFlowDTO> getBudgetByFlow() {
        String token = readToken();
        JSONObject jsonObject = null;
        try {
            jsonObject = forestClient.getBudgetByFlow(url, token);
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }
        if (jsonObject != null && jsonObject.getBoolean("success")) {
            JSONArray jsonArray = jsonObject.getJSONArray("data");
            return jsonArray.toJavaList(OaBudgetFlowDTO.class);
        } else {
            throw new ServiceException(DPAPI_EXCEPTION_MSG);
        }
    }


    public List<OaCashFlowPlanDetailDTO> getOaCashFlowPlanDetailList(Long projectId) {
        JSONObject jsonObject = null;
        String token = readToken();
        try {
            jsonObject = forestClient.getOaCashFlowPlanDt(url, token, projectId);
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }
        if (jsonObject != null && jsonObject.getBoolean("success")) {
            JSONArray jsonArray = jsonObject.getJSONArray("data");
            return jsonArray.toJavaList(OaCashFlowPlanDetailDTO.class);
        } else {
            throw new ServiceException(DPAPI_EXCEPTION_MSG);
        }
    }

    /**
     * 获取项目变更流程信息
     *
     * @return
     */
    public JSONArray getXMBGByRequestIds(List<Long> requestIds) {
        JSONObject jsonObject = null;
        String token = readToken();
        try {
            jsonObject = forestClient.getXMBGByRequestIds(url, token, requestIds);
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }
        if (jsonObject != null && jsonObject.getBoolean("success")) {
            JSONArray jsonArray = jsonObject.getJSONArray("data");
            return jsonArray;
        } else {
            throw new ServiceException(DPAPI_EXCEPTION_MSG);
        }
    }

    public List<OaProjectDTO> getProjectInfoByAfterDate(LocalDate localDate){
        if (localDate == null) {
            // 默认取一天内更新的项目数据
            localDate=LocalDate.now().minusDays(1);
        }
        JSONObject jsonObject = null;
        String token = readToken();
        try {
            jsonObject = forestClient.getProjectInfoByAfterDate(url, token, localDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }
        if (jsonObject != null && jsonObject.getBoolean("success")) {
            JSONArray jsonArray = jsonObject.getJSONArray("data");
            return jsonArray.toJavaList(OaProjectDTO.class);
        } else {
            throw new ServiceException(DPAPI_EXCEPTION_MSG);
        }

  }



}
