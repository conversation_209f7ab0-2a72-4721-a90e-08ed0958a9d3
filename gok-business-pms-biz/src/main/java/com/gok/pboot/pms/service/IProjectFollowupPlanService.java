package com.gok.pboot.pms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.entity.domain.ProjectFollowupPlan;
import com.gok.pboot.pms.entity.dto.ProjectFollowupPlanDTO;
import com.gok.pboot.pms.entity.vo.ProjectFollowupPlanVO;

import java.util.List;

/**
 * 项目后续计划服务类
 *
 * <AUTHOR>
 * @create 2025/07/03
 **/
public interface IProjectFollowupPlanService extends IService<ProjectFollowupPlan> {

    /**
     * 全量批量保存项目后续计划
     *
     * @param dtoList 项目后续计划集合
     * @return
     */
    List<Long> batchSave(List<ProjectFollowupPlanDTO> dtoList);


    /**
     * 根据项目ID查询项目后续计划
     *
     * @param projectId 项目ID
     * @return
     */
    List<ProjectFollowupPlanVO> findByProjectId(Long projectId);

}
