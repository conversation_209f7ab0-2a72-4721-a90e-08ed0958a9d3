package com.gok.pboot.pms.cost.enums;

import com.gok.pboot.pms.enumeration.ValueEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 成本预算类型枚举类
 *
 * <AUTHOR>
 * @create 2025/01/07
 **/
@Getter
@AllArgsConstructor
public enum CostBudgetTypeEnum implements ValueEnum<Integer> {

    /**
     * 售前成本
     */
    SQCB(0, "售前成本"),

    /**
     * 项目总成本-A表成本
     */
    ABCB(1, "A表成本"),

    /**
     * 项目总成本-B表成本
     */
    BBCB(2, "B表成本"),
    ;

    private final Integer value;

    private final String name;

}
