package com.gok.pboot.pms.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.BaseController;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.common.base.PropertyFilters;
import com.gok.pboot.pms.entity.vo.ProjectCustomerStateVO;
import com.gok.pboot.pms.service.IProjectCustomerStateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;


/**
 * <AUTHOR>
 * @description 客情状态 前端控制器
 * @menu 客情状态
 * @since 2023-07-11 17:05:26
 */
@Validated
@Api("客情状态")
@RestController
@RequestMapping("/projectCustomerState")
public class ProjectCustomerStateController extends BaseController {

    @Resource
    private IProjectCustomerStateService service;

    /**
     * 分页查询客情状态列表
     *
     * @param pageRequest 分页请求实体
     * @param request     {@link HttpServletRequest}
     * @return {@link ApiResult}<{@link Page}<{@link ProjectCustomerStateVO}>>
     */
    @ApiOperation(value = "分页查询客情状态列表", notes = "分页查询客情状态列表")
    @GetMapping("/findPage")
    public ApiResult<Page<ProjectCustomerStateVO>> findPage(PageRequest pageRequest, HttpServletRequest request) {
        return ApiResult.success(service.findPage(pageRequest, PropertyFilters.get(request)));
    }

}
