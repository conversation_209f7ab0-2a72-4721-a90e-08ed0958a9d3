package com.gok.pboot.pms.cost.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.cost.entity.domain.CostConfigVersion;
import com.gok.pboot.pms.cost.entity.vo.VersionHistoryVO;
import com.gok.pboot.pms.cost.enums.CostConfigVersionTypeEnum;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 成本配置版本记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Mapper
public interface CostConfigVersionMapper extends BaseMapper<CostConfigVersion> {


    /**
     * 获取最大版本号
     *
     * @param versionTypeEnum version 类型 enum
     * @return {@link String }
     */
    String getMaxVersionNum(@Param("versionTypeEnum") CostConfigVersionTypeEnum versionTypeEnum);

    /**
     * 获取历史记录版本
     *
     * @param page            页
     * @param versionTypeEnum version 类型 enum
     * @return {@link Page }<{@link VersionHistoryVO }>
     */
    Page<VersionHistoryVO> findPage(@Param("page") Page<VersionHistoryVO> page, @Param("versionTypeEnum") CostConfigVersionTypeEnum versionTypeEnum);
}
