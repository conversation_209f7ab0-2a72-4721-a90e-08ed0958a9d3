package com.gok.pboot.pms.handler;

import com.gok.pboot.pms.entity.vo.SysUserDataScopeVO;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023-08-04
 * <p>
 * data scope 判断处理器,抽象服务扩展
 */
public interface ProjectScopeHandle {

    String PROJECT_AVAILABLE_KEY = "projectIdsInDataScope";

    /**
     * 计算用户数据权限
     *
     * @param filter 参数Map
     */
    void calcScope(Map<String, Object> filter);

    /**
     * 查询用户数据权限
     */
    SysUserDataScopeVO findDataScope();

    /**
     * 获取数据权限
     *
     * @param menuCode  菜单权限编码
     * @param projectId 项目ID
     * @param isAll     是否查询全部权限的标志
     * @return 数据权限结果
     */
    SysUserDataScopeVO getDeliverManagementDataPermission(String menuCode, Long projectId, Boolean isAll);

}
