package com.gok.pboot.pms.entity.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * @Auther chenhc
 * @Date 2022-08-24 14:52
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AllocationFindPageVO {

    /**
     * 项目ID
     */
    @ExcelIgnore
    private Long id;

    /**
     * 项目编号
     */
    @ExcelProperty("项目编号")
    private String code;

    /**
     * 项目名称
     */
    @ExcelProperty("项目名称")
    private String projectName;

    /**
     * 项目状态
     */
    @ExcelIgnore
    private String projectStatus;

    /**
     * 项目状态值
     */
    @ExcelProperty("项目状态")
    private String projectStatusName;

    /**
     * 项目类型
     */
    @ExcelProperty("项目类型")
    private String projectTypeName;

    @ExcelProperty("是否内部项目")
    private Integer isNotInternalProject;


    @ExcelProperty("是否内部项目")
    private String isNotInternalProjectStr;
    /**
     * 收入归属部门编号
     */
    @ExcelIgnore
    private Long projectDeptId;
    /**
     * 收入归属部门
     */
    @ExcelProperty("收入归属部门")
    private String projectDeptName;

    /**
     * 人员归属部门id
     */
    @ExcelIgnore
    private Long userDeptId;

    /**
     * 员工id
     */
    @ExcelIgnore
    private Long userId;

    /**
     * 姓名
     */
    @ExcelProperty("姓名")
    private String name;
    /**
     * 员工工号
     */
    @ExcelProperty("员工工号")
    private String workCode;

    /**
     * 身份证号
     */
    @ExcelProperty("身份证号")
    private String idCardNo;

    /**
     * 实际出勤天数
     */
    @ExcelProperty("出勤天数")
    private BigDecimal cwActualAttendance;

    @ExcelProperty("项目正常工时")
    private BigDecimal projectNormalHours;


    @ExcelProperty("总加班工时")
    private BigDecimal projectAddedHours;

    @ExcelProperty("工作日加班工时")
    private BigDecimal workOvertimeHours;

    @ExcelProperty("休息日加班工时")
    private BigDecimal restOvertimeHours;

    @ExcelProperty("节假日加班工时")
    private BigDecimal holidayOvertimeHours;

    /**
     * 调休工时
     */
    @ExcelProperty("调休工时")
    private BigDecimal leaveHours;

    /**
     * 项目分摊工时
     */
    @ExcelProperty("项目分摊工时")
    private BigDecimal projectShareHours;

    /**
     * 售前工时
     */
    @ExcelProperty("售前工时")
    private String preSaleHours;

    /**
     * 售后工时
     */
    @ExcelProperty("售后工时")
    private String afterSaleHours;


    /**
     * 人员状态名称
     */
    @ExcelProperty("状态")
    private String personnelStatusName;

    /**
     * 人员归属部门全称
     */
    @ExcelProperty("人员归属部门")
    private String personnelDeptName;


    /**
     * 工时审核员
     */
    @ExcelIgnore
    private String privilegeUserName;

    /**
     * 销售人员
     */
    @ExcelIgnore
    private String salesmanUserName;

    /**
     * 经理人员
     */
    @ExcelIgnore
    private String managerUserName;

    /**
     * 对应日期
     */
    @ExcelIgnore
    private String date;

}
