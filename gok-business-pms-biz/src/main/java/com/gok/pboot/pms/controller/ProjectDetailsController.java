package com.gok.pboot.pms.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.module.excel.api.annotation.ResponseExcel;
import com.gok.module.excel.api.enums.ExcelDateEnum;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.common.base.PropertyFilters;
import com.gok.pboot.pms.entity.vo.*;
import com.gok.pboot.pms.service.ProjectDetailsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @menu 项目详情（财务数据、项目合同）dbapi调用接口
 */
@RestController
@RequestMapping("/project/details")
public class ProjectDetailsController {

    @Autowired
    private ProjectDetailsService service;

    /**
     * 财务数据-追加预算总收入-dbApi调用
     *
     * @param pageRequest 页面请求
     * @param request     请求
     * @return {@link ApiResult}<{@link AdditionalIncomePageVo}>
     */
    @GetMapping("/additionalIncome")
    public ApiResult<AdditionalIncomeVo> additionalIncome(PageRequest pageRequest, HttpServletRequest request) {
        Map<String, Object> filter = PropertyFilters.get(request, true);
        return service.additionalIncome(pageRequest, filter);
    }

    /**
     * 财务数据-已有预算总收入-dbApi调用
     *
     * @param pageRequest 页面请求
     * @param request     请求
     * @return {@link ApiResult}<{@link AdditionalIncomeVo}>
     */
    @GetMapping("/existingIncome")
    public ApiResult<AdditionalIncomeVo> existingIncome(PageRequest pageRequest, HttpServletRequest request) {
        Map<String, Object> filter = PropertyFilters.get(request, true);
        return service.existingIncome(pageRequest, filter);
    }

    /**
     * 财务数据-追加预算总成本-dbApi调用
     *
     * @param pageRequest 页面请求
     * @param request     请求
     * @return {@link ApiResult}<{@link AdditionalCostsVo}>
     */
    @GetMapping("/additionalCosts")
    public ApiResult<AdditionalCostsVo> additionalCosts(PageRequest pageRequest, HttpServletRequest request) {
        Map<String, Object> filter = PropertyFilters.get(request, true);
        return service.additionalCosts(pageRequest, filter);
    }

    /**
     * 财务数据-已有预算总成本-dbApi调用
     *
     * @param pageRequest 页面请求
     * @param request     请求
     * @return {@link ApiResult}<{@link AdditionalCostsVo}>
     */
    @GetMapping("/existingBudget")
    public ApiResult<AdditionalCostsVo> existingBudget(PageRequest pageRequest, HttpServletRequest request) {
        Map<String, Object> filter = PropertyFilters.get(request, true);
        return service.existingBudget(pageRequest, filter);
    }

    /**
     * 项目管理-项目费用报销明细-dbApi调用
     *
     * @param request
     * @return
     */
    @GetMapping("/reimburseDetails")
    public ApiResult<Page<ReimburseDetailsVO>> reimburseDetails(PageRequest pageRequest, HttpServletRequest request) {
        Map<String, Object> filter = PropertyFilters.get(request, true);
        return service.reimburseDetails(pageRequest, filter);
    }

    /**
     * 项目详情-合同数据-dbApi调用
     *
     * @param request 请求
     * @return {@link ApiResult}<{@link Page}<{@link ProjectContractPageVo}>>
     */
    @GetMapping("/projectContract")
    public ApiResult<ProjectContractVo> projectContract(PageRequest pageRequest, HttpServletRequest request) {
        Map<String, Object> filter = PropertyFilters.get(request, true);
        return service.projectContract(pageRequest, filter);
    }

    /**
     * 项目详情-财务数据-dbApi调用
     *
     * @param request 请求
     * @return {@link ApiResult}<{@link FinancialDataVo}>
     */
    @GetMapping("/financialData")
    public ApiResult<FinancialDataVo> financialData(HttpServletRequest request) {
        Map<String, Object> filter = PropertyFilters.get(request, true);
        return service.financialData(filter);
    }

    /**
     * 项目管理-人天明细-dbApi调用
     *
     * @param pageRequest 页面请求
     * @param request     请求
     * @return {@link ApiResult}<{@link Page}<{@link ManDayDetailsPageVo}>>
     */
    @GetMapping("/manDayDetails")
    public ApiResult<Page<ManDayDetailsPageVo>> manDayDetails(PageRequest pageRequest, HttpServletRequest request) {
        Map<String, Object> filter = PropertyFilters.get(request, true);
        return service.manDayDetails(pageRequest, filter);
    }

    /**
     * 项目管理-查询直接人工（项目分摊）明细-dbApi调用
     *
     * @param pageRequest 页面请求
     * @param request     请求
     * @return {@link ApiResult}<{@link Page}<{@link TotalCostDetailsPageVo}>>
     */
    @GetMapping("/totalCostDetails")
    public ApiResult<Page<TotalCostDetailsPageVo>> totalCostDetails(PageRequest pageRequest, HttpServletRequest request) {
        Map<String, Object> filter = PropertyFilters.get(request, true);
        return service.totalCostDetails(pageRequest, filter);
    }

    /**
     * 项目采购付款明细-dbApi调用
     *
     * @param pageRequest 页面请求
     * @param request     请求
     * @return {@link ApiResult}<{@link ProcureDetailsPageVo}>
     */
    @GetMapping("/procureDetails")
    public ApiResult<Page<ProcureDetailsPageVo>> procureDetails(PageRequest pageRequest, HttpServletRequest request) {
        Map<String, Object> filter = PropertyFilters.get(request, true);
        return service.procureDetails(pageRequest, filter);
    }

    /**
     * 项目管理-查询合同明细-dbApi调用
     *
     * @param pageRequest 页面请求
     * @param request     请求
     * @return {@link ApiResult}<{@link Page}<{@link ContractDetailsPageVo}>>
     */
    @GetMapping("/contractDetails")
    public ApiResult<Page<ContractDetailsPageVo>> contractDetails(PageRequest pageRequest, HttpServletRequest request) {
        Map<String, Object> filter = PropertyFilters.get(request, true);
        return service.contractDetails(pageRequest, filter);
    }

    /**
     * 导出-项目管理-查询直接人工（项目分摊）明细-dbApi调用
     *
     * @param pageRequest 页面请求
     * @param request     请求
     * @return {@link List}<{@link TotalCostDetailsPageVo}>
     */
    @GetMapping("/export/totalCostDetails")
    @ResponseExcel(name = "直接人工(项目分摊)", nameWithDate = ExcelDateEnum.DATE, dateFormat = "yyyy-MM-dd")
    public List<TotalCostDetailsPageVo> exportTotalCostDetails(PageRequest pageRequest, HttpServletRequest request) {
        Map<String, Object> filter = PropertyFilters.get(request, true);
        List<TotalCostDetailsPageVo> records = service.totalCostDetails(pageRequest, filter).getData().getRecords();
        if (CollUtil.isNotEmpty(records)) {
            return records;
        } else {
            return Arrays.asList(new TotalCostDetailsPageVo());
        }
    }

    /**
     * 导出-项目管理-项目费用报销明细-dbApi调用
     *
     * @param pageRequest 页面请求
     * @param request     请求
     * @return {@link List}<{@link ReimburseDetailsVO}>
     */
    @GetMapping("/export/reimburseDetails")
    @ResponseExcel(name = "项目费用报销明细",nameWithDate = ExcelDateEnum.DATE, dateFormat = "yyyy-MM-dd")
    public List<ReimburseDetailsVO> exportReimburseDetails(PageRequest pageRequest, HttpServletRequest request) {
        Map<String, Object> filter = PropertyFilters.get(request, true);
        List<ReimburseDetailsVO> records = service.reimburseDetails(pageRequest, filter).getData().getRecords();
        if (CollUtil.isNotEmpty(records)) {
            return records;
        } else {
            return Arrays.asList(new ReimburseDetailsVO());
        }
    }

    /**
     * 导出-项目采购付款明细-dbApi调用
     *
     * @param pageRequest 页面请求
     * @param request     请求
     * @return {@link List}<{@link ProcureDetailsPageVo}>
     */
    @GetMapping("/export/procureDetails")
    @ResponseExcel(name = "项目采购付款明细", nameWithDate = ExcelDateEnum.DATE, dateFormat = "yyyy-MM-dd")
    public List<ProcureDetailsPageVo> exportProcureDetails(PageRequest pageRequest, HttpServletRequest request) {
        Map<String, Object> filter = PropertyFilters.get(request, true);
        List<ProcureDetailsPageVo> records = service.procureDetails(pageRequest, filter).getData().getRecords();
        if (CollUtil.isNotEmpty(records)) {
            return records;
        } else {
            return Arrays.asList(new ProcureDetailsPageVo());
        }
    }

    /**
     * 合同文件下载接口
     *
     * @param fileName
     * @param fileUrl
     * @param response
     * @return {@link ApiResult}<{@link String}>
     */
    @GetMapping("/install")
    public void install(String fileName, String fileUrl, HttpServletResponse response) {
        service.install(fileName, fileUrl, response);
    }

    /**
     * 财务数据-预算总成本-内部项目OA数据查询
     *
     * @param pageRequest 页面请求
     * @param request     请求
     * @return {@link ApiResult}<{@link AdditionalCostsVo}>
     * @version 1.3.6
     */
    @GetMapping("/internalBudget")
    public ApiResult<AdditionalCostsVo> internalBudget(PageRequest pageRequest, HttpServletRequest request) {
        Map<String, Object> filter = PropertyFilters.get(request, true);
        return service.internalBudget(pageRequest, filter);
    }

}
