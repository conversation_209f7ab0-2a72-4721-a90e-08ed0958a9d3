package com.gok.pboot.pms.entity.vo;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
/**
* <p>
* 客户经营单元(分页VO)
* </p>
*
* <AUTHOR>
* @since 2024-10-12
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class CustomerBusinessPageVO {

    /**
     * id
     */
    private Long id;

    /**
     * 经营单元名称
     */
    private String name;

    /**
     * 关联客户数
     */
    private long unitCount;

    /**
     * 主办客户经理
     */
    private String mainManager;

    /**
     * 协办客户经理
     */
    private String secondaryManager;

    /**
     * 总支撑官
     */
    private String supportManager;

    /**
     * 方案经理
     */
    private String  programmeManager;

    /**
     *人力BP
     */
    private String humanManager;

    /**
     *交付经理
     */
    private String deliverManager;
}
