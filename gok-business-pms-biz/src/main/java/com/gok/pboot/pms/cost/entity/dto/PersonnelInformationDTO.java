package com.gok.pboot.pms.cost.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <p>
 * 人员信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PersonnelInformationDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 工号
     */
    private String workCode;

    /**
     * 人员属性(0: 国科人员,1: 第三方)
     */
    private Integer personnelAttribute;

    /**
     * 所属部门ID
     */
    private Long deptId;

    /**
     * 所属部门名称
     */
    private String deptName;

    /**
     * 岗位ID
     */
    private Long jobId;

    /**
     * 岗位名称
     */
    private String jobName;

    /**
     * 职级ID
     */
    private Long positionId;

    /**
     * 职级名称
     */
    private String positionName;

    /**
     * 入场时间
     */
    private String entryTime;

    /**
     * 驻场时长(天)
     */
    private BigDecimal durationDays;

    /**
     * 驻场地点
     */
    private String domicile;

    /**
     * 报价方式(0: 人天,1: 人月,2: 固定费率)
     */
    private Integer quotationType;

    /**
     * 含税报价
     */
    private BigDecimal quotationIncludeTax;

    /**
     * 报价税率ID
     */
    private String quotedRateId;

    /**
     * 不含税报价
     */
    private BigDecimal quotationExcludeTax;

    /**
     * 固定费率
     */
    private BigDecimal flatRate;

    /**
     * 外采含税单价
     */
    private BigDecimal foreignPurchaseUnitPriceIncludeTax;

    /**
     * 外采税率ID
     */
    private String foreignPurchaseTaxRateId;

    /**
     * 外采不含税单价
     */
    private BigDecimal foreignPurchaseUnitPriceExcludeTax;

    /**
     * 归属月份
     */
    private String belongMonth;

}