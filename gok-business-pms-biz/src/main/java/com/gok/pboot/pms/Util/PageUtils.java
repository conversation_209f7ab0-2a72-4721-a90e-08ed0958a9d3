package com.gok.pboot.pms.Util;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.common.base.PageRequest;

import javax.annotation.Nonnull;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * - 分页工具类 -
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/8/2 14:04
 */
public class PageUtils {
    private PageUtils(){
        throw new AssertionError();
    }

    @Nonnull
    public static <T> List<T> doPaging(
            @Nonnull List<T> allDataList,
            @Nonnull Page<T> pageBean,
            boolean updatePageBeanFlag
    ){
        if (allDataList.isEmpty()){
            if (updatePageBeanFlag){
                pageBean.setTotal(0);
                pageBean.setSize(0);
                pageBean.setPages(0);
                pageBean.setRecords(allDataList);
            }

            return allDataList;
        }

        long currPage = pageBean.getCurrent();
        // 跳过指定记录数后，需要向后获取的记录数
        long limitNum = pageBean.getSize();
        // 如果当前页数大于1，则可能需要跳过指定页数的记录数，如果小于等于1，则一定不需要跳过任何记录
        long skipNum = currPage > 1 ? (currPage - 1) * limitNum : 0;
        int total = allDataList.size();
        List<T> result;

        // 如果当前的查询记录总数小于等于要获取的记录数，则不跳过任何记录并获取全部
        if (total <= limitNum){
            limitNum = allDataList.size();
            skipNum = 0;
        }
        result = allDataList.stream()
                .skip(skipNum)
                .limit(limitNum)
                .collect(Collectors.toList());
        // 如果需要本方法对传入的PageBean进行修改以反映最终的查询结果数，就设置flag为true
        if (updatePageBeanFlag){
            pageBean.setTotal(total);
            pageBean.setSize(result.size());
            pageBean.setPages((long) Math.ceil((double) total / result.size()));
            pageBean.setRecords(result);
        }

        return result;
    }

    @Nonnull
    public static <T> List<T> doPaging(
            @Nonnull List<T> allDataList,
            @Nonnull PageRequest pageRequest
    ){
        return doPaging(allDataList, new Page<>(pageRequest.getPageNumber(), pageRequest.getPageSize()), false);
    }

    @Nonnull
    public static <T> Page<T> page(
            @Nonnull List<T> allDataList,
            @Nonnull PageRequest pageRequest
    ){
        Page<T> page = new Page<>(pageRequest.getPageNumber(), pageRequest.getPageSize());

        page.setRecords(doPaging(allDataList, page, true));

        return page;
    }

    /**
     * 给分好页的数据创建Page对象
     * @param dataList 已分页查询出来的数据
     * @param pageAdapter 分页适配器
     * @param total 总数据条数
     * @return 分页对象
     */
    @Nonnull
    public static <T> Page<T> page(
            @Nonnull List<T> dataList,
            @Nonnull PageAdapter pageAdapter,
            int total
    ){
        int size = Math.max(pageAdapter.getSize(), 1);
        int page = (Math.max(pageAdapter.getBegin(), 0) / size) + 1;
        Page<T> result = new Page<>(page, size);

        if (total < 1 || dataList.isEmpty()){
            buildEmptyPageBean(result);

            return result;
        }
        result.setRecords(dataList);
        result.setTotal(total);
        result.setSize(dataList.size());
        result.setPages((long) Math.ceil((double) total / size));

        return result;
    }

    /***
     * ~ 通过新的总数据条数更新分页对象 ~
     * @param pageBean 分页对象
     * @param total 总数据条数
     * <AUTHOR>
     */
    public static void updatePageBean(@Nonnull Page<?> pageBean, int total){
        pageBean.setTotal(total);
        pageBean.setPages((long) Math.ceil((double) total / pageBean.getSize()));
    }

    public static void buildEmptyPageBean(@Nonnull Page<?> pageBean){
        pageBean.setTotal(0);
        pageBean.setSize(0);
        pageBean.setPages(0);
    }

    /**
     * 将Page中的元素转换为目标类型（原地转换，低内存成本）
     */
    @SuppressWarnings("unchecked")
    public static<T, O> Page<T> mapTo(Page<O> page, @Nonnull Function<O, T> action){
        Page<T> result = (Page<T>) page;
        List<O> originRecords;
        List<T> records;
        long size;

        if (page.getTotal() < 1){
            return result;
        }
        originRecords = page.getRecords();
        records = (List<T>) originRecords;
        size = records.size();
        for (int i = 0; i < size; i++){
            records.set(i, action.apply(originRecords.get(i)));
        }

        return result;
    }
}
