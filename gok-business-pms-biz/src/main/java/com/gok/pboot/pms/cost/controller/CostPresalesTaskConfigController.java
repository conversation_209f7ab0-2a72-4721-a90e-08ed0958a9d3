package com.gok.pboot.pms.cost.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.common.security.annotation.Inner;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.cost.entity.dto.CostPresalesTaskConfigDTO;
import com.gok.pboot.pms.cost.entity.vo.CostPresalesTaskConfigVO;
import com.gok.pboot.pms.cost.service.ICostPresalesTaskConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 售前报工工单配置表 控制器
 *
 * <AUTHOR>
 * @date 2025/04/09
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/costPresalesTaskConfig")
@Api(tags = "售前报工工单配置管理")
public class CostPresalesTaskConfigController {

    private final ICostPresalesTaskConfigService costPresalesTaskConfigService;

    /**
     * 分页查询工单配置列表
     *
     * @param pageRequest 分页参数
     * @return 分页结果
     */
    @GetMapping("/page")
    @ApiOperation("分页查询工单配置列表")
    public ApiResult<Page<CostPresalesTaskConfigVO>> findPage(PageRequest pageRequest) {
        return ApiResult.success(costPresalesTaskConfigService.findPage(pageRequest));
    }

    /**
     * 删除工单配置
     *
     * @param id 工单配置ID
     * @return 是否成功
     */
    @DeleteMapping("/delete/{id}")
    @ApiOperation("删除工单配置")
    public ApiResult<String> deletePresalesTaskConfig(@PathVariable @ApiParam("工单配置ID") Long id) {
        costPresalesTaskConfigService.deletePresalesTaskConfig(id);
        return ApiResult.success("删除成功");
    }

    /**
     * 批量编辑子工单
     *
     * @param batchDto 批量 DTO
     * @return 操作结果
     */
    @PutMapping("/batchEdit")
    @ApiOperation("批量编辑子工单")
    public ApiResult<String> batchEditPresalesTaskConfig(
            @RequestBody @Valid @ApiParam("工单配置DTO列表") CostPresalesTaskConfigDTO batchDto) {
        costPresalesTaskConfigService.batchEditPresalesTaskConfig(batchDto);
        return ApiResult.successMsg("批量编辑成功");
    }

    /**
     * 批量保存默认工单配置
     *
     * @param batchDto 批量 DTO
     * @return 操作结果
     */
    @PostMapping("/batchSaveDefault")
    @ApiOperation("批量保存默认工单配置")
    public ApiResult<String> batchSaveDefaultTaskConfig(
            @RequestBody @Valid @ApiParam("工单配置DTO树形列表") CostPresalesTaskConfigDTO batchDto) {
        costPresalesTaskConfigService.batchSaveDefaultTaskConfig(batchDto);
        return ApiResult.successMsg("批量保存成功");
    }

    /**
     * 同步默认工单配置
     */
    @GetMapping("/sync")
    @ApiOperation("同步默认工单配置")
    @Inner(false)
    public ApiResult<String> initGenerateDefaultTask() {
        costPresalesTaskConfigService.syncGenerateDefaultTask();
        return ApiResult.successMsg("同步成功");
    }
} 