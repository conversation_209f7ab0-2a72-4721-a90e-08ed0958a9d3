package com.gok.pboot.pms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.gok.base.admin.feign.RemoteOaService;
import com.gok.bcp.admin.feign.RemoteBcpDictService;
import com.gok.bcp.admin.vo.DictKvVo;
import com.gok.bcp.message.dto.BcpMessageBatchDTO;
import com.gok.bcp.message.dto.BcpMessageTargetBatchDTO;
import com.gok.bcp.message.dto.BcpMessageTargetDTO;
import com.gok.bcp.message.entity.enums.ChannelEnum;
import com.gok.bcp.message.entity.enums.MsgTypeEnum;
import com.gok.bcp.message.entity.enums.SourceEnum;
import com.gok.bcp.message.entity.enums.TargetTypeEnum;
import com.gok.bcp.message.entity.model.BcpMessageContentModel;
import com.gok.bcp.message.entity.model.WeComBatchModel;
import com.gok.bcp.message.entity.model.WeComModel;
import com.gok.bcp.message.feign.RemoteSendMsgService;
import com.gok.components.common.constant.SecurityConstants;
import com.gok.components.common.user.PigxUser;
import com.gok.module.file.dto.InnerDoc;
import com.gok.module.file.service.SysFileService;
import com.gok.pboot.common.core.exception.BusinessException;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.Util.*;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.R;
import com.gok.pboot.pms.common.exception.ServiceException;
import com.gok.pboot.pms.cost.entity.domain.*;
import com.gok.pboot.pms.cost.entity.dto.CostCashPlanDto;
import com.gok.pboot.pms.cost.entity.dto.CostManageVersionDTO;
import com.gok.pboot.pms.cost.entity.vo.*;
import com.gok.pboot.pms.cost.enums.ChangeContentEnum;
import com.gok.pboot.pms.cost.enums.CostBudgetTypeEnum;
import com.gok.pboot.pms.cost.enums.CostManageVersionEnum;
import com.gok.pboot.pms.cost.enums.CostRequestStatusEnum;
import com.gok.pboot.pms.cost.mapper.CostBaselineQuotationMapper;
import com.gok.pboot.pms.cost.mapper.CostCashPlanVersionMapper;
import com.gok.pboot.pms.cost.mapper.CostManagePersonnelCustomDetailMapper;
import com.gok.pboot.pms.cost.mapper.CostManagePersonnelLevelDetailMapper;
import com.gok.pboot.pms.cost.service.*;
import com.gok.pboot.pms.cost.service.impl.CostManageEstimationResultsServiceImpl;
import com.gok.pboot.pms.entity.domain.ContractLedger;
import com.gok.pboot.pms.entity.domain.ProjectBusinessMilestones;
import com.gok.pboot.pms.entity.domain.ProjectInfo;
import com.gok.pboot.pms.entity.dto.CreateRequestDTO;
import com.gok.pboot.pms.entity.dto.ProjectBussMilSettlementDTO;
import com.gok.pboot.pms.entity.vo.BusinessCaseVO;
import com.gok.pboot.pms.entity.vo.OaAccountVO;
import com.gok.pboot.pms.entity.vo.OaFileInfoVo;
import com.gok.pboot.pms.entity.vo.ProjectBusinessMilestonesVo;
import com.gok.pboot.pms.enumeration.CostManageStatusEnum;
import com.gok.pboot.pms.enumeration.MsgModelEnum;
import com.gok.pboot.pms.enumeration.OAFormTypeEnum;
import com.gok.pboot.pms.enumeration.PaymentStatusEnum;
import com.gok.pboot.pms.mapper.ContractLedgerMapper;
import com.gok.pboot.pms.mapper.ProjectBusinessMilestonesMapper;
import com.gok.pboot.pms.mapper.ProjectInfoMapper;
import com.gok.pboot.pms.oa.OaUtil;
import com.gok.pboot.pms.oa.client.OaClient;
import com.gok.pboot.pms.oa.dto.OaCashFlowPlanDetailDTO;
import com.gok.pboot.pms.oa.dto.OaCreateRequestDTO;
import com.gok.pboot.pms.oa.dto.OaMainParamDTO;
import com.gok.pboot.pms.oa.dto.OaXmysbDTO;
import com.gok.pboot.pms.service.IDictService;
import com.gok.pboot.pms.service.IPmsDocImageFileService;
import com.gok.pboot.pms.service.IProjectBusinessMilestonesService;
import com.google.common.base.Charsets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProjectBusinessMilestonesImpl extends ServiceImpl<ProjectBusinessMilestonesMapper, ProjectBusinessMilestones>
        implements IProjectBusinessMilestonesService {


    @Value("${oa.workflowUrl}")
    private String workflowUrl;

    /**
     * 业务一体化-项目台账-项目详情-验收管理
     */
    @Value("${pushMessage.acceptanceManagementUrl}")
    private String acceptanceManagementUrl;

    @Value("${project.task.leftUrl}")
    private String projectUrl;

    @Value("${pushMessage.redirectPrefixUrl}")
    private String redirectPrefix;
    /**
     * 业务一体化-项目台账-项目详情-验收管理
     */
    @Value("${pushMessage.acceptanceManagementUrlRedirect}")
    private String redirect;
    /**
     * oa的url地址
     */
    @Value("${oa.url.httpUrl}")
    private String url;

    /**
     * oa的资源appId
     */
    @Value("${oa.resourcesAppId}")
    private String appId;

    /**
     * oa的spk
     */
    @Value("${oa.spk}")
    private String spk;

    private final OaUtil oaUtil;
    private final DbApiUtil dbApiUtil;
    private final OaClient oaClient;

    private final IPmsDocImageFileService pmsDocImageFileService;
    @Resource
    private final RemoteOaService remoteOaService;
    @Resource
    private final RemoteSendMsgService remoteSendMsgService;

    private final ProjectInfoMapper projectInfoMapper;

    private final ICostManageTargetService costManageTargetService;

    private final ICostManageEstimationResultsService costManageEstimationResultsService;

    private final ICostManageVersionService costManageVersionService;

    private final CostBaselineQuotationMapper costBaselineQuotationMapper;

    private final ICostBaselineVersionRecordService costBaselineVersionRecordService;

    private final SysFileService sysFileService;

    private final IDictService idictService;

    private final ICostIncomeSettlementDetailService settlementDetailService;

    private final ICostIncomeSettlementService settlementService;
    @Resource
    private final RemoteBcpDictService remoteBcpDictService;

    private final ContractLedgerMapper contractLedgerMapper;

    private final ICostCashPlanService costCashPlanService;

    private final CostCashPlanVersionMapper costCashPlanVersionMapper;

    private final CostManagePersonnelLevelDetailMapper costManagePersonnelLevelDetailMapper;

    private final CostManagePersonnelCustomDetailMapper costManagePersonnelCustomDetailMapper;


    /**
     * 通过项目Id查询项目商务里程碑
     *
     * @param projectId 项目 ID
     * @return {@link R }<{@link List }<{@link ProjectBusinessMilestonesVo }>>
     */
    @Override
    public R<List<ProjectBusinessMilestonesVo>> getListByProjectId(Long projectId) {
        List<ProjectBusinessMilestones> businessMilestonesList = lambdaQuery().eq(ProjectBusinessMilestones::getProjectId, projectId).list();
        List<ProjectBusinessMilestonesVo> projectBusinessMilestonesVoList = new ArrayList<>();
        Map<Integer, String> taxRateMap = idictService.getDictKvList("税率").getData().stream()
                .collect(Collectors.toMap(vo -> Integer.parseInt(vo.getValue()), DictKvVo::getName));
        for (ProjectBusinessMilestones businessMilestones : businessMilestonesList) {
            ProjectBusinessMilestonesVo vo = new ProjectBusinessMilestonesVo();
            BeanUtil.copyProperties(businessMilestones, vo, CopyOptions.create().ignoreNullValue());
            vo.setExpectCollectAmount(DecimalFormatUtil.setThousandthAndTwoDecimal(businessMilestones.getExpectCollectAmount(), StringPool.EMPTY));
            vo.setSettlementAmount(DecimalFormatUtil.setThousandthAndTwoDecimal(businessMilestones.getSettlementAmount(), StringPool.EMPTY));
            vo.setSettlementAmountExcludingTax(DecimalFormatUtil.setThousandthAndTwoDecimal(businessMilestones.getSettlementAmountExcludingTax(), StringPool.EMPTY));
            vo.setCurrentPaymentMoney(DecimalFormatUtil.setThousandthAndTwoDecimal(businessMilestones.getCurrentPaymentMoney(), StringPool.EMPTY));
            if (StrUtil.isNotBlank(vo.getIfFinish()) && vo.getExpectedCompleteDate() != null) {
                if ("0".equals(vo.getIfFinish())) {
                    if (vo.getActualCompleteDate() != null) {
                        vo.setIfOverdueFinishFlag(vo.getExpectedCompleteDate().isBefore(vo.getActualCompleteDate()) ? 1 : 0);
                    }
                } else {
                    vo.setIfOverdueFlag(vo.getExpectedCompleteDate().isBefore(LocalDate.now()) ? 1 : 0);
                }
            } else {
                vo.setIfOverdueFlag(null);
                vo.setIfOverdueFinishFlag(null);
            }
            if (StrUtil.isNotBlank(vo.getIfFinish())) {
                vo.setIfFinish("1".equals(vo.getIfFinish()) ? "否" : "是");
            }
            if (vo.getPaymentStatus() != null) {
                vo.setPaymentStatusTxt(EnumUtils.getNameByValue(PaymentStatusEnum.class, String.valueOf(vo.getPaymentStatus())));
            }
            Integer taxRate = vo.getTaxRate();
            if (taxRate != null) {
                vo.setTaxRateTxt(taxRateMap.get(taxRate));
            }
            if (StrUtil.isNotBlank(vo.getCollectRatio())) {
                MathContext mathContext = new MathContext(4, RoundingMode.HALF_UP);
                BigDecimal ratio = new BigDecimal(vo.getCollectRatio()).multiply(new BigDecimal(100), mathContext);
                // 设置小数位数为2
                ratio = ratio.setScale(2, RoundingMode.HALF_UP);
                vo.setCollectRatio(ratio.toString());
            }
            projectBusinessMilestonesVoList.add(vo);
        }
        projectBusinessMilestonesVoList.stream()
                .filter(item -> item.getRequestId() == null)
                .forEach(item -> item.setRequestStatus(CostRequestStatusEnum.UNCOMMIT.getValue()));
        return R.ok(projectBusinessMilestonesVoList, "请求成功");
    }


    @Override
    public R<List<OaFileInfoVo>> getSupportMaterialsListById(Long id) {
        List<OaFileInfoVo> oaFileInfoVoList = new ArrayList<>();
        ProjectBusinessMilestones projectBusinessMilestones = this.baseMapper.selectById(id);
        String supportMaterials = projectBusinessMilestones.getSupportMaterials();
        if (StrUtil.isNotBlank(projectBusinessMilestones.getSupportMaterials())) {
            oaFileInfoVoList = pmsDocImageFileService.getOaFileVoList(supportMaterials);
        }
        return R.ok(oaFileInfoVoList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<String> doCreateRequest(JSONObject requestMap) {
        Long projectId = requestMap.getLong("projectId");
        PigxUser user = SecurityUtils.getUser();
        Long currentUserId = user.getId();
        //获得当前用户免登录token
        String redirectOaToken = getOaTokenByUserId(currentUserId);
        OAFormTypeEnum businessTypeEnum = OAFormTypeEnum.getBusinessTypeEnum(requestMap.getInteger("formType"));
        if (businessTypeEnum == null) {
            throw new ServiceException("发起流程失败，原因:未找到对应表单~");
        }
        // 查询OA用户信息
        OaAccountVO oaAccountVO = dbApiUtil.getOaAccountInfoByUserId(currentUserId);
        if (oaAccountVO == null || oaAccountVO.getOaId() == null) {
            log.error("OA用户信息:{}", oaAccountVO);
            throw new ServiceException("发起OA流程失败，原因:未查询到当前用户的OA信息~");
        }
        // 查询项目信息
        ProjectInfo projectInfo = getProjectInfo(projectId);
        try {
            Long requestId = generateRequestIdByFormTypeEnum(requestMap, businessTypeEnum, oaAccountVO, projectInfo);
            return R.ok(StrUtil.format(workflowUrl, redirectOaToken, requestId), "请求成功");
        } catch (Exception e) {
            log.error("发起OA流程失败:{}", e.getMessage());
            e.printStackTrace();
            throw new ServiceException("发起OA流程失败:" + e.getMessage());
        }
    }

    @NotNull
    private ProjectInfo getProjectInfo(Long projectId) {
        //项目里程碑信息
        ProjectInfo projectInfo = projectInfoMapper.selectById(projectId);
        if (projectInfo == null) {
            throw new ServiceException("根据id:" + projectId + ",没有找到对应项目数据");
        }
        return projectInfo;
    }

    @NotNull
    private String getOaTokenByUserId(Long currentUserId) {
        String redirectOaToken;
        try {
            com.gok.components.common.util.R<String> res = remoteOaService.getOaSsoTokenByUserId(currentUserId);
            redirectOaToken = res.getData();
            if (StrUtil.isBlank(redirectOaToken)) {
                log.error("获取跳转token为空,{}", res);
                throw new ServiceException("跳转失败，请使用有OA权限的账号~");
            }
        } catch (Exception e) {
            log.error("获取跳转token失败:{}", e.getMessage());
            throw new ServiceException("获取跳转token失败" + e.getMessage());
        }

        return redirectOaToken;
    }

    /**
     * 检查业务里程碑流程
     *
     * @param businessMilestones 业务里程碑
     * @return {@link Long }
     */
    private Long checkBusinessMilestonesFlow(ProjectBusinessMilestones businessMilestones) {
        Long requestId = businessMilestones.getRequestId();
        Integer oaRequestStatus = dbApiUtil.getOaRequestStatus(requestId);
        if (oaRequestStatus != null) {
            return requestId;
        }
        return null;
    }

    /**
     * 根据表单类型生成OA流程请求ID
     *
     * @param requestMap       请求参数
     * @param businessTypeEnum OA表单类型枚举
     * @param oaAccountVO      OA账户信息
     * @param projectInfo      项目信息
     * @return OA流程请求ID
     * @throws JsonProcessingException JSON处理异常
     */
    private Long generateRequestIdByFormTypeEnum(final JSONObject requestMap, final OAFormTypeEnum businessTypeEnum,
                                                 final OaAccountVO oaAccountVO, final ProjectInfo projectInfo) throws JsonProcessingException {
        // 初始化基础参数

        final String projectIdStr = String.valueOf(projectInfo.getId());

        // 创建OA请求DTO
        OaCreateRequestDTO oaCreateRequestDTO = initOaCreateRequestDTO(businessTypeEnum, oaAccountVO);

        // 构建主表数据
        List<OaMainParamDTO> mainDataList = buildMainData(oaAccountVO, projectIdStr);
        List<Map<String, Object>> detailDataList = new ArrayList<>();
        List<CostManageVersionVO> costManageVersionVoList = new ArrayList<>();

        // 处理特殊业务类型
        handleSpecialBusinessTypes(businessTypeEnum, requestMap, oaAccountVO, mainDataList, detailDataList, costManageVersionVoList);

        // 根据业务类型构建具体请求数据
        Long existRequestId = buildRequestByBusinessType(businessTypeEnum, projectInfo, requestMap, mainDataList, detailDataList);
        if (existRequestId != null) {
            return existRequestId;
        }

        // 获取OA标题
        String oaTitle = getOaTitleByBusinessType(businessTypeEnum, projectInfo);

        // 设置请求名称和数据
        oaCreateRequestDTO.setRequestName(oaTitle)
                .setMainData(mainDataList)
                .setDetailData(detailDataList)
                .setOtherParams(Collections.singletonMap("isnextflow", "0"));

        // 发起工作流
        Long requestId = initiateWorkflow(oaCreateRequestDTO);

        // 更新基准信息
        CreateRequestDTO createRequestDto = requestMap.toJavaObject(CreateRequestDTO.class);
        updateBaselineInfo(createRequestDto, projectInfo, businessTypeEnum, costManageVersionVoList, requestId, oaCreateRequestDTO);

        // 执行最终操作
        finalAction(requestMap, businessTypeEnum, oaCreateRequestDTO);

        return requestId;
    }


    /**
     * 初始化OA创建请求DTO
     */
    private OaCreateRequestDTO initOaCreateRequestDTO(OAFormTypeEnum businessTypeEnum, OaAccountVO oaAccountVO) {
        OaCreateRequestDTO dto = new OaCreateRequestDTO();
        dto.setOaAccountVO(oaAccountVO);
        dto.setWorkflowId(dbApiUtil.getWorkflowIdByName(businessTypeEnum.getName()));
        return dto;
    }

    /**
     * 构建主表基础数据
     */
    private List<OaMainParamDTO> buildMainData(OaAccountVO oaAccountVO, String projectIdStr) {
        List<OaMainParamDTO> mainDataList = new ArrayList<>();
        mainDataList.add(buildMainParam("ApplicantID", String.valueOf(oaAccountVO.getOaId())));
        mainDataList.add(buildMainParam("bh", String.valueOf(oaAccountVO.getWorkcode())));
        mainDataList.add(buildMainParam("ApplicantDeptID", oaAccountVO.getDepartmentid()));
        mainDataList.add(buildMainParam("ApplicantTel", oaAccountVO.getMobile()));
        mainDataList.add(buildMainParam("ApplicantJobID", oaAccountVO.getJobtitle()));
        return mainDataList;
    }

    /**
     * 构建主表参数DTO
     */
    private OaMainParamDTO buildMainParam(String fieldName, String fieldValue) {
        return OaMainParamDTO.builder()
                .fieldName(fieldName)
                .fieldValue(fieldValue)
                .build();
    }

    /**
     * 处理特殊业务类型
     */
    private void handleSpecialBusinessTypes(OAFormTypeEnum businessTypeEnum, JSONObject requestMap,
                                            OaAccountVO oaAccountVO, List<OaMainParamDTO> mainDataList,
                                            List<Map<String, Object>> detailDataList,
                                            List<CostManageVersionVO> costManageVersionVoList) {
        CreateRequestDTO createRequestDto = requestMap.toJavaObject(CreateRequestDTO.class);

        // 处理需要添加项目目标成本流程的类型
        if (EnumUtils.enumOrEquals(businessTypeEnum, OAFormTypeEnum.SYLZAB, OAFormTypeEnum.XMLXBB, OAFormTypeEnum.XMBG)) {
            addCostFlowRequest(createRequestDto, oaAccountVO, mainDataList, detailDataList, costManageVersionVoList, businessTypeEnum);
        }

        // 处理需要添加办公地点的类型
        if (EnumUtils.enumOrEquals(businessTypeEnum, OAFormTypeEnum.SYLZAB, OAFormTypeEnum.LCBDCSP)) {
            mainDataList.add(buildMainParam("bgdd", oaAccountVO.getLocationid()));
        }
        Set<Integer> changeContentSet = StrUtil.split(createRequestDto.getChangeContentStr(), ",")
                .stream().filter(StrUtil::isNotBlank).map(Integer::parseInt).collect(Collectors.toSet());
        if (OAFormTypeEnum.XMLXBB.equals(businessTypeEnum) ||
                (EnumUtils.enumOrEquals(businessTypeEnum, OAFormTypeEnum.XMBG) &&
                        changeContentSet.contains(ChangeContentEnum.CASH.getValue()))) {
            // 添加项目毛利测算流程请求
            //addQuotationFlowRequest(createRequestDto, mainDataList, costManageVersionVoList);
            // 添加现金流计划数据
            addCashPlanData(businessTypeEnum, createRequestDto.getProjectId(), mainDataList, detailDataList);
        }
    }

    private void addCashPlanData(OAFormTypeEnum businessTypeEnum, Long projectId, List<OaMainParamDTO> mainDataList, List<Map<String, Object>> detailDataList) {

        List<CostCashPlanVO> costCashPlanList = costCashPlanService.getCostCashPlanList(CostCashPlanDto.builder().projectId(projectId).build());
        if (CollUtil.isEmpty(costCashPlanList)) {
            return;
        }
        String detailTable = null;
        Map<String, Long> oaCashFlowPlanDetailMap = new HashMap<>();
        if (OAFormTypeEnum.XMLXBB.equals(businessTypeEnum)) {
            detailTable = "formtable_main_366_dt7";
            // 现金流URL：case_flow_url
            String url = projectUrl + "/cash-flows-management?id=" + Base64.encode(projectId.toString(), Charsets.UTF_8);
            mainDataList.add(buildOaMainParamDto("case_flow_url", url));
        } else if (OAFormTypeEnum.XMBG.equals(businessTypeEnum)) {
            detailTable = "formtable_main_371_dt7";
            mainDataList.add(buildOaMainParamDto("pdsfxzxjljh", 0));
            List<OaCashFlowPlanDetailDTO> oaCashFlowPlanDetailList = dbApiUtil.getOaCashFlowPlanDetailList(projectId);
            oaCashFlowPlanDetailMap = CollStreamUtil.toMap(oaCashFlowPlanDetailList, OaCashFlowPlanDetailDTO::getCYCLE, OaCashFlowPlanDetailDTO::getId);
        }

        List<Map<String, Object>> recordMaps = new ArrayList<>();
        for (CostCashPlanVO costCashPlanVO : costCashPlanList) {

            List<OaMainParamDTO> tableFieldList = new ArrayList<>();
            // 时间 cycle
            String cycle = String.format("第%d个月", costCashPlanVO.getTimeMonth());
            tableFieldList.add(buildOaMainParamDto("cycle",cycle) );
            // 月份 month
            tableFieldList.add(buildOaMainParamDto("month", costCashPlanVO.getPlanMonth()));
            // 现金流金额 cash_flow_amount
            tableFieldList.add(buildOaMainParamDto("cash_flow_amount", costCashPlanVO.getCashFlowAmount()));
            // 现金流利息：cash flow interest
            tableFieldList.add(buildOaMainParamDto("cash_flow_interest", costCashPlanVO.getCashFlowInterest()));
            // 获取OA对应月份的mxId
            Long mxId = oaCashFlowPlanDetailMap.get(cycle);
            if (mxId != null) {
                // OA mxid
                tableFieldList.add(buildOaMainParamDto("mxid",mxId));
            }
            Map<String, Object> tableRecordMap = new HashMap<>(2);
            tableRecordMap.put("recordOrder", 0);
            tableRecordMap.put("workflowRequestTableFields", tableFieldList);
            recordMaps.add(tableRecordMap);
        }
        Map<String, Object> detailDataMap = new HashMap<>(2);
        detailDataMap.put("tableDBName", detailTable);
        detailDataMap.put("workflowRequestTableRecords", recordMaps);
        detailDataList.add(detailDataMap);
    }

    private Long initiateWorkflow(OaCreateRequestDTO oaCreateRequestDTO) throws JsonProcessingException {
        // 获取token
        final String token = oaUtil.getToken();
        // 对userID进行加密
        final OaAccountVO oaAccountVO = oaCreateRequestDTO.getOaAccountVO();
        final String encryptUserId = RSAUtils.encrypt(String.valueOf(oaAccountVO.getOaId()), spk);
        final String requestName = oaCreateRequestDTO.getRequestName();
        final Map<String, Object> otherParams = oaCreateRequestDTO.getOtherParams();
        final List<OaMainParamDTO> mainData = oaCreateRequestDTO.getMainData();
        final Integer workflowId = oaCreateRequestDTO.getWorkflowId();
        final List<Map<String, Object>> detailData = oaCreateRequestDTO.getDetailData();
        log.info("OA流程请求参数：workflowId:{}\nRequestName:{}\nmainParam:{}\notherParams:{}\ndetailParam:{}", workflowId,
                requestName, mainData.toArray(), otherParams, detailData.toArray());

        String mainParamStr = new ObjectMapper().writeValueAsString(mainData);
        String otherParamsStr = JSONUtil.toJsonStr(otherParams);
        String detailParamStr = new ObjectMapper().writeValueAsString(detailData);
        // 发起OA流程
        JSONObject jsonObject = oaClient.doCreateRequest(token, appId, encryptUserId, url, String.valueOf(workflowId),
                requestName, mainParamStr, otherParamsStr, detailParamStr);
        if (!"SUCCESS".equals(jsonObject.getString("code"))) {
            log.error("原因:{}", jsonObject);
            throw new ServiceException("发起OA流程失败1，原因:" + jsonObject);
        }
        //流程id
        Long requestId = jsonObject.getJSONObject("data").getLong("requestid");
        oaCreateRequestDTO.setRequestId(requestId);
        return requestId;
    }

    private void buildLcbdcspDeatil(ProjectBusinessMilestones businessMilestones5, List<Map<String, Object>> detailDataList, ContractLedger contractLedger) {
        // 明细数据构建
        List<OaMainParamDTO> tableFieldList = new ArrayList<>();
        // 商务里程碑
        tableFieldList.add(buildOaMainParamDto("swlcb", businessMilestones5.getBusinessMilestones()));
        // 里程碑id
        tableFieldList.add(buildOaMainParamDto("lcbid", String.valueOf(businessMilestones5.getOaId())));
        // 里程碑说明
        tableFieldList.add(buildOaMainParamDto("lcbsm", businessMilestones5.getMilestoneDesc()));
        // 计划完成时间
        tableFieldList.add(buildOaMainParamDto("yjwcsj", String.valueOf(businessMilestones5.getExpectedCompleteDate())));
        // 是否已完成（写死：0是）
        tableFieldList.add(buildOaMainParamDto("sfywc", "0"));
        // 实际完成时间
        tableFieldList.add(buildOaMainParamDto("sjwcsj", String.valueOf(businessMilestones5.getActualCompleteDate())));
        // 完成佐证
        tableFieldList.add(buildOaMainParamDto("wczz", businessMilestones5.getSupportMaterials()));
        // 所属合同
        tableFieldList.add(buildOaMainParamDto("ssht", businessMilestones5.getContractId()));
        // 合同编号
        tableFieldList.add(buildOaMainParamDto("htbh", contractLedger.getHtbh()));
        // 账款类型
        tableFieldList.add(buildOaMainParamDto("zklx", getAccountTypeValue(businessMilestones5.getAccountType())));
        // 结算金额(含税)
        tableFieldList.add(buildOaMainParamDto("jsjehs", businessMilestones5.getSettlementAmount()));
        // 税率
        tableFieldList.add(buildOaMainParamDto("sl", businessMilestones5.getTaxRate()));
        // 结算金额(不含税)
        tableFieldList.add(buildOaMainParamDto("jsjebhs", businessMilestones5.getSettlementAmountExcludingTax()));

        detailDataList.add(buildSingletonDetailData(tableFieldList, OAFormTypeEnum.LCBDCSP.getDetailTable()));
    }

    @NotNull
    private static Map<String, Object> buildSingletonDetailData(List<OaMainParamDTO> tableFieldList, String tableName) {
        Map<String, Object> tableRecordMap = new HashMap<>(2);
        tableRecordMap.put("recordOrder", 0);
        tableRecordMap.put("workflowRequestTableFields", tableFieldList);
        Map<String, Object> detailDataMap = new HashMap<>(2);
        detailDataMap.put("tableDBName", tableName);
        detailDataMap.put("workflowRequestTableRecords", Collections.singletonList(tableRecordMap));
        return detailDataMap;
    }

    private void buildRlwbjsspDeatil(OAFormTypeEnum businessTypeEnum, ProjectBusinessMilestones projectBusinessMilestones, ProjectBussMilSettlementDTO bussMilSettlementDto, List<Map<String, Object>> detailDataList) {

        // 明细数据构建
        List<OaMainParamDTO> tableFieldList = new ArrayList<>();
        // 款项类型
        tableFieldList.add(buildOaMainParamDto("kxmci", getAccountTypeValue(projectBusinessMilestones.getAccountType())));
        // 税率
        if (StrUtil.isNotBlank(bussMilSettlementDto.getTaxRate())) {
            tableFieldList.add(buildOaMainParamDto("sl", bussMilSettlementDto.getTaxRate()));
        }
        // 结算周期
        // 订单开始日期
        tableFieldList.add(buildOaMainParamDto("ddksrq", bussMilSettlementDto.getSettlementPeriodStartDate()));
        // 订单结算日期
        tableFieldList.add(buildOaMainParamDto("ddjsrq", bussMilSettlementDto.getSettlementPeriodEndDate()));
        // 结算金额(含税)
        tableFieldList.add(buildOaMainParamDto("yjsfkjehs", String.valueOf(bussMilSettlementDto.getSettlementAmount())));
        // 结算金额(不含税)
        tableFieldList.add(buildOaMainParamDto("yjsfkjebhs", String.valueOf(bussMilSettlementDto.getSettlementAmountExcludingTax())));
        // 商务里程碑oa id
        tableFieldList.add(buildOaMainParamDto("mxid", String.valueOf(projectBusinessMilestones.getOaId())));
        // 商务里程碑
        tableFieldList.add(buildOaMainParamDto("swlcb", projectBusinessMilestones.getBusinessMilestones()));
        // 是否已完成（写死：0是）
        tableFieldList.add(buildOaMainParamDto("sfwc", "0"));
        // 明细链接
        Map<String, String> jumpLinkMap = bussMilSettlementDto.getJumpLinkMap();
        jumpLinkMap.forEach((k, v) -> {
            tableFieldList.add(buildOaMainParamDto("idtag", k));
            tableFieldList.add(buildOaMainParamDto("url", v));
        });
        Map<String, Object> detailDataMap = buildSingletonDetailData(tableFieldList, businessTypeEnum.getDetailTable());
        detailDataList.add(detailDataMap);
    }

    /**
     * 最终操作
     *
     * @param requestMap       请求地图
     * @param businessTypeEnum 业务类型枚举
     */
    private void finalAction(JSONObject requestMap, OAFormTypeEnum businessTypeEnum, OaCreateRequestDTO oaCreateRequestDTO) {
        CreateRequestDTO createRequestDto = requestMap.toJavaObject(CreateRequestDTO.class);
        Long requestId = oaCreateRequestDTO.getRequestId();
        String requestName = oaCreateRequestDTO.getRequestName();
        // 人力外包结算审批
        if (OAFormTypeEnum.RLWBJSSP.equals(businessTypeEnum)) {
            JSONArray costIncomeSettlementDetailIdsArr = requestMap.getJSONArray("costIncomeSettlementDetailIds");
            JSONArray costIncomeSettlementIdsArr = requestMap.getJSONArray("costIncomeSettlementIds");

            if (CollUtil.isNotEmpty(costIncomeSettlementIdsArr)) {
                List<Long> settlementIds = costIncomeSettlementIdsArr.toJavaList(Long.class);
                List<CostIncomeSettlement> settlementList = settlementService.lambdaQuery().in(CostIncomeSettlement::getId, settlementIds).list();
                settlementService.updateSettlementNumber(settlementList, Collections.emptyList(), requestId);
            } else {
                List<Long> settlementDetailIds = costIncomeSettlementDetailIdsArr.toJavaList(Long.class);
                List<CostIncomeSettlementDetail> detailList = settlementDetailService.lambdaQuery().in(CostIncomeSettlementDetail::getId, settlementDetailIds).list();
                settlementService.updateSettlementNumber(Collections.emptyList(), detailList, requestId);
            }
        }
        // 商务里程碑关联
        if (EnumUtils.enumOrEquals(businessTypeEnum, OAFormTypeEnum.RLWBJSSP, OAFormTypeEnum.LCBDCSP)) {
            Long businessMilestoneId = requestMap.getLong("projectBusinessMilestoneId");
            lambdaUpdate().set(ProjectBusinessMilestones::getRequestId, requestId)
                    .eq(ProjectBusinessMilestones::getId, businessMilestoneId).update();
        }

        Long projectId = createRequestDto.getProjectId();
        Set<Integer> changeContentSet = StrUtil.split(createRequestDto.getChangeContentStr(), ",")
                .stream().filter(StrUtil::isNotBlank).map(Integer::parseInt).collect(Collectors.toSet());

        // 更新现金流版本的流程信息
        if (EnumUtils.enumOrEquals(businessTypeEnum, OAFormTypeEnum.XMLXBB) ||
                (EnumUtils.enumOrEquals(businessTypeEnum, OAFormTypeEnum.XMBG) &&
                        changeContentSet.contains(ChangeContentEnum.CASH.getValue()))) {
            CostCashPlanVersion currentVersion = costCashPlanVersionMapper.getCurrentVersion(projectId);
            if (currentVersion == null) {
                return;
            }
            currentVersion.setRequestId(requestId);
            currentVersion.setRequestName(requestName);
            costCashPlanVersionMapper.updateById(currentVersion);
        }
    }

    private String getAccountTypeValue(String accountTypeName) {
        Map<String, String> accountTypeMapByName = remoteBcpDictService.getRemoteDictKvList("款项名称(A表)").getData().stream()
                .collect(Collectors.toMap(DictKvVo::getName, DictKvVo::getValue, (a, b) -> a));
        return accountTypeMapByName.get(accountTypeName);
    }

    @Nullable
    private ProjectBussMilSettlementDTO getProjectBussMilSettlementDTO(JSONObject requestMap, ProjectInfo projectInfo) {
        JSONArray costIncomeSettlementDetailIdsArr = requestMap.getJSONArray("costIncomeSettlementDetailIds");
        JSONArray costIncomeSettlementIdsArr = requestMap.getJSONArray("costIncomeSettlementIds");
        if (CollUtil.isEmpty(costIncomeSettlementDetailIdsArr) && CollUtil.isEmpty(costIncomeSettlementIdsArr)) {
            throw new ServiceException("请选择结算数据~");
        }
        Long projectInfoId = projectInfo.getId();
        // 结算金额（含税）
        BigDecimal settlementAmountTotal = BigDecimal.ZERO;
        // 结算金额（不含税）
        BigDecimal settlementAmountExcludingTaxTotal = BigDecimal.ZERO;
        //结算周期开始时间
        LocalDate settlementPeriodStartDate = null;
        //结算周期结束时间
        LocalDate settlementPeriodEndDate = null;
        // 税率map
        Set<String> taxRateSet = new HashSet<>();
        Set<String> settlementNumberSet = new LinkedHashSet<>();
        // 工作流ids
        List<Long> requestIdList = new ArrayList<>();
        if (CollUtil.isNotEmpty(costIncomeSettlementDetailIdsArr)) {
            List<Long> costIncomeSettlementDetailIds = costIncomeSettlementDetailIdsArr.toJavaList(Long.class);
            List<CostIncomeSettlementDetail> settlementDetailList = settlementDetailService.lambdaQuery()
                    .in(CostIncomeSettlementDetail::getId, costIncomeSettlementDetailIds)
                    .eq(CostIncomeSettlementDetail::getProjectId, projectInfoId).list();
            for (CostIncomeSettlementDetail costIncomeSettlementDetailVO : settlementDetailList) {
                settlementAmountTotal = settlementAmountTotal.add(costIncomeSettlementDetailVO.getBudgetAmountIncludedTax());
                settlementAmountExcludingTaxTotal = settlementAmountExcludingTaxTotal.add(costIncomeSettlementDetailVO.getBudgetAmountExcludingTax());
                taxRateSet.add(costIncomeSettlementDetailVO.getTaxRate());
                // 时间取最大区间
                if (settlementPeriodStartDate == null) {
                    settlementPeriodStartDate = costIncomeSettlementDetailVO.getStartDate();
                }
                if (settlementPeriodEndDate == null) {
                    settlementPeriodEndDate = costIncomeSettlementDetailVO.getEndDate();
                }
                if (costIncomeSettlementDetailVO.getStartDate().isBefore(settlementPeriodStartDate)) {
                    settlementPeriodStartDate = costIncomeSettlementDetailVO.getStartDate();
                }
                if (costIncomeSettlementDetailVO.getEndDate().isAfter(settlementPeriodEndDate)) {
                    settlementPeriodEndDate = costIncomeSettlementDetailVO.getEndDate();
                }
                settlementNumberSet.add(costIncomeSettlementDetailVO.getSettlementDetailsNumber());
                requestIdList.add(costIncomeSettlementDetailVO.getRequestId());
            }
        } else {
            List<Long> costIncomeSettlementIds = costIncomeSettlementIdsArr.toJavaList(Long.class);
            List<CostIncomeSettlement> settlementList = settlementService.lambdaQuery()
                    .in(CostIncomeSettlement::getId, costIncomeSettlementIds)
                    .eq(CostIncomeSettlement::getProjectId, projectInfoId).list();
            for (CostIncomeSettlement costIncomeSettlementVO : settlementList) {
                settlementAmountTotal = settlementAmountTotal.add(costIncomeSettlementVO.getBudgetAmountIncludedTax());
                settlementAmountExcludingTaxTotal = settlementAmountExcludingTaxTotal.add(costIncomeSettlementVO.getBudgetAmountExcludingTax());
                taxRateSet.add(costIncomeSettlementVO.getTaxRate());
                if (settlementPeriodStartDate == null) {
                    settlementPeriodStartDate = costIncomeSettlementVO.getStartDate();
                }
                if (settlementPeriodEndDate == null) {
                    settlementPeriodEndDate = costIncomeSettlementVO.getEndDate();
                }
                if (costIncomeSettlementVO.getStartDate().isBefore(settlementPeriodStartDate)) {
                    settlementPeriodStartDate = costIncomeSettlementVO.getStartDate();
                }
                if (costIncomeSettlementVO.getEndDate().isAfter(settlementPeriodEndDate)) {
                    settlementPeriodEndDate = costIncomeSettlementVO.getEndDate();
                }
                settlementNumberSet.add(costIncomeSettlementVO.getSettlementNumber());
                String requestIdsStr = costIncomeSettlementVO.getRequestId();
                if (StrUtil.isNotBlank(requestIdsStr)) {
                    String[] splitRequestIds = requestIdsStr.split(StringPool.COMMA);
                    requestIdList.addAll(Arrays.stream(splitRequestIds).map(Long::parseLong).collect(Collectors.toList()));
                }
            }
        }

        //判断与里程碑已发起的流程是否有交集
        Set<Long> intersection = lambdaQuery().eq(ProjectBusinessMilestones::getProjectId, projectInfoId).list()
                .stream().map(ProjectBusinessMilestones::getRequestId)
                .filter(Objects::nonNull).collect(Collectors.toSet());
        intersection.retainAll(requestIdList);
        if (CollUtil.isNotEmpty(intersection)) {
            throw new ServiceException("当前页面有数据变动，请刷新重试~");
        }
        // 若存在已发起的流程则抛出异常
        Map<Long, Integer> oaRequestStatusMap = dbApiUtil.getOaRequestStatus(requestIdList);
        if (CollUtil.isNotEmpty(oaRequestStatusMap)) {
            List<Integer> commitList = oaRequestStatusMap.values()
                    .stream().filter(Objects::nonNull)
                    .filter(item -> !EnumUtils.valueEquals(item, CostRequestStatusEnum.UNCOMMIT))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(commitList)) {
                throw new ServiceException("当前页面有数据变动，请刷新重试~");
            }
        }

        final String keys = String.join(StringPool.COMMA, settlementNumberSet);
        final String jumpLink = redirectPrefix + Base64.encode(projectUrl +
                "/view-businesses/project-manage/project-manage-list?projectId=" + projectInfoId
                + "&detailTab=DELIVERY_MANAGEMENT&detailSubTab=IncomeSettlementManagement"
                + "&queryType=" + (CollUtil.isNotEmpty(costIncomeSettlementIdsArr) ? "summary" : "detail")
                + "&queryCode=" + keys, Charsets.UTF_8);
        return new ProjectBussMilSettlementDTO()
                .setSettlementAmount(settlementAmountTotal)
                .setSettlementAmountExcludingTax(settlementAmountExcludingTaxTotal)
                .setTaxRate(taxRateSet.size() == 1 ? taxRateSet.iterator().next() : null)
                .setSettlementPeriodStartDate(LocalDateTimeUtil.format(settlementPeriodStartDate, "yyyy-MM-dd"))
                .setSettlementPeriodEndDate(LocalDateTimeUtil.format(settlementPeriodEndDate, "yyyy-MM-dd"))
                .setJumpLinkMap(Collections.singletonMap(keys, jumpLink));
    }

    private static String getOaFlowReqTitle(ProjectInfo projectInfo, String formTypeName) {
        return StrUtil.format(formTypeName + "-{}-{}（商机名称:{}）"
                , SecurityUtils.getUser().getName()
                , LocalDate.now()
                , projectInfo.getItemName()
        );
    }

    private static String getOaFlowReqTitle(String formTypeName) {
        return StrUtil.format(formTypeName + "-{}-{}"
                , SecurityUtils.getUser().getName()
                , LocalDate.now()
        );
    }

    private void updateBaselineInfo(CreateRequestDTO dto,
                                    ProjectInfo projectInfo,
                                    OAFormTypeEnum businessTypeEnum,
                                    List<CostManageVersionVO> costManageVersionVoList,
                                    Long requestId,
                                    OaCreateRequestDTO oaCreateRequestDTO) {
        if (dto.getFormType().equals(OAFormTypeEnum.SYLZAB.getValue())
                || dto.getFormType().equals(OAFormTypeEnum.XMLXBB.getValue())
                || dto.getFormType().equals(OAFormTypeEnum.XMBG.getValue())) {
            //生成基线版本记录
            costBaselineVersionRecordService.syncCostBaselineVersionRecord(projectInfo.getId(), businessTypeEnum.getName(), null);

            costManageVersionVoList.forEach(costManageVersionVO -> {
                //如果流程id为空或者流程状态不为已归档则更新流程信息
                if (costManageVersionVO.getRequestId() == null
                        || !CostRequestStatusEnum.FINISH.getValue().equals(costManageVersionVO.getRequestStatus())) {
                    CostManageVersion costManageVersion = new CostManageVersion();
                    costManageVersion.setId(costManageVersionVO.getVersionId());
                    costManageVersion.setRequestId(requestId);
                    costManageVersion.setRequestName(oaCreateRequestDTO.getRequestName());
                    costManageVersion.setRequestType(dto.getFormType());
                    costManageVersionService.updateById(costManageVersion);


                    //修改报价与毛利测算审核状态
//                    if (CostManageVersionEnum.BJYMLCS.getValue().equals(costManageVersionVO.getVersionType())) {
//                        costBaselineQuotationMapper.updateByVersionId(costManageVersionVO.getVersionId(), AuditStatusTypeEnum.BBLCYFQ.getValue());
//                    }
                }
            });
        }
    }


    /**
     * 添加项目目标成本流程请求
     *
     * @param dto                     dto
     * @param oaAccountVO             用户OA账号
     * @param mainDataList            流程主表信息
     * @param detailDataList          流程明细表信息
     * @param costManageVersionVoList 版本流程信息
     */
    public void addCostFlowRequest(CreateRequestDTO dto,
                                   OaAccountVO oaAccountVO,
                                   List<OaMainParamDTO> mainDataList,
                                   List<Map<String, Object>> detailDataList,
                                   List<CostManageVersionVO> costManageVersionVoList,
                                   OAFormTypeEnum businessTypeEnum) {
        //项目变更、商业论证A表流程、项目立项B表流程这3个流程，只要有一个未归档，其他两个则无法发起
        Long projectId = dto.getProjectId();
        String requestCheck = dbApiUtil.getOARequestByXMBG(projectId);
        if (ObjectUtil.isNotEmpty(requestCheck)) {
            throw new ServiceException("当前项目已发起过未归档流程：" + requestCheck);
        }

        Set<Integer> changeContentSet = StrUtil.split(dto.getChangeContentStr(), ",").stream()
                .filter(StrUtil::isNotBlank).map(Integer::parseInt).collect(Collectors.toSet());
        if (changeContentSet.contains(ChangeContentEnum.TARGET.getValue())) {
            //查询项目目标信息
            CostManageTargetVO costManageTarget = costManageTargetService.getCostManageTargetInfo(null, projectId);
            if (costManageTarget != null) {
                //处理版本流程信息
                CostManageVersionVO costManageVersionVO = new CostManageVersionVO();
                BeanUtil.copyProperties(costManageTarget, costManageVersionVO);
                costManageVersionVoList.add(costManageVersionVO);

                //项目需求
                if (StringUtils.isNotEmpty(costManageTarget.getProjectRequirements())) {
                    mainDataList.add(buildOaMainParamDto("xmxq", costManageTarget.getProjectRequirements()));
                }
                //交付要求
                if (StringUtils.isNotEmpty(costManageTarget.getDeliveryRequirements())) {
                    mainDataList.add(buildOaMainParamDto("jfyq", costManageTarget.getDeliveryRequirements()));
                }
                //交付物
                if (StringUtils.isNotEmpty(costManageTarget.getDeliveryItems())) {
                    mainDataList.add(buildOaMainParamDto("jfw", costManageTarget.getDeliveryItems()));
                }
                //交付期限
                if (StringUtils.isNotEmpty(costManageTarget.getDeliveryDeadline())) {
                    mainDataList.add(buildOaMainParamDto("jfqx", costManageTarget.getDeliveryDeadline()));
                }
                //交付地点
                if (StringUtils.isNotEmpty(costManageTarget.getDeliveryPlace())) {
                    mainDataList.add(buildOaMainParamDto("jfdd", costManageTarget.getDeliveryPlace()));
                }
                //质保期(月)
                if (StringUtils.isNotEmpty(costManageTarget.getWarrantyPeriod())) {
                    mainDataList.add(buildOaMainParamDto(
                            OAFormTypeEnum.XMBG.getValue().equals(dto.getFormType()) ? "zbqy1" : "zbqy",
                            costManageTarget.getWarrantyPeriod()));
                }
                //保密要求
                if (StringUtils.isNotEmpty(costManageTarget.getSecrecyRequirements())) {
                    mainDataList.add(buildOaMainParamDto("bmyq", costManageTarget.getSecrecyRequirements()));
                }
                //其他要求
                if (StringUtils.isNotEmpty(costManageTarget.getOtherRequirements())) {
                    mainDataList.add(buildOaMainParamDto("qtyq", costManageTarget.getOtherRequirements()));
                }
                //详细说明文档
                if (StringUtils.isNotEmpty(costManageTarget.getDetailFiles())) {
                    //上传流程附件
                    StringBuilder fileIdBuilder = new StringBuilder();
                    uploadFiles(costManageTarget, oaAccountVO, fileIdBuilder);
                    mainDataList.add(buildOaMainParamDto("xgxxsmwd", String.valueOf(fileIdBuilder)));
                }
            }
        }

        // 根据传入请求 查询项目成本
        List<Integer> costBudgetTypeList = changeContentSet.stream()
                .filter(e -> null != EnumUtils.getEnumByValue(CostBudgetTypeEnum.class, e))
                .collect(Collectors.toList());
        // 获取当前最新的版本
        CostManageVersionDTO costResultQuery = CostManageVersionDTO.builder().projectId(projectId).costBudgetTypeList(costBudgetTypeList).build();
        CostManageListVO costManageVO;
        boolean isTotalCost = OAFormTypeEnum.SYLZAB.equals(businessTypeEnum) || OAFormTypeEnum.XMLXBB.equals(businessTypeEnum);
        if (isTotalCost) {
            costManageVO = costManageEstimationResultsService.getCostManageListVO(projectId,
                    Arrays.asList(CostBudgetTypeEnum.SQCB.getValue(), CostBudgetTypeEnum.BBCB.getValue(), CostBudgetTypeEnum.ABCB.getValue()));
        } else {
            costManageVO = costManageEstimationResultsService.getCostManageListVO(projectId, costBudgetTypeList);
        }
        // 获取版本携带的成本明细数据
        List<CostManageEstimationResultsVO> processEstimationResults =
                costManageEstimationResultsService.findProcessEstimationResults(costResultQuery, businessTypeEnum);

        // 成本金额-不含税（不含垫资）
        BigDecimal budgetAmountTotalExcludingTax = processEstimationResults.stream()
                .filter(e -> !CostManageEstimationResultsServiceImpl.ADVANCE_FUNDING_NAME.equals(e.getAccountName()))
                .map(CostManageEstimationResultsVO::getBudgetAmountExcludingTax)
                .reduce(BigDecimal.ZERO, BigDecimal::add);


        Integer changePrecosOrUnprecos = CollUtil.isEmpty(costBudgetTypeList)
                ? CostBudgetTypeEnum.SQCB.getValue().equals(costManageVO.getCostBudgetType()) ? 0 : 1
                : CollUtil.contains(costBudgetTypeList, CostBudgetTypeEnum.SQCB.getValue()) ? 0 : 1;

        if (isTotalCost || (OAFormTypeEnum.XMBG.equals(businessTypeEnum) && CollUtil.isNotEmpty(costBudgetTypeList))) {
            if (BeanUtil.isNotEmpty(costManageVO) && ArrayUtil.isNotEmpty(costManageVO.getCostList())) {
                //处理版本流程信息
                CostManageVersionVO costManageVersionVO = new CostManageVersionVO();
                BeanUtil.copyProperties(costManageVO, costManageVersionVO);

                // 判断项目立项B表
                if (OAFormTypeEnum.XMLXBB.equals(businessTypeEnum)) {
                    // 判断b表是否存在
                    CostManageVersion currentBBCostVersion =
                            costManageVersionService.getLatestCostManageVersion(CostManageVersionDTO.builder().projectId(projectId).costBudgetTypeList(Arrays.asList(CostBudgetTypeEnum.BBCB.getValue())).build());
                    if (null == currentBBCostVersion) {
                        CostManageVersion newBBCostVersion = BeanUtil.copyProperties(costManageVersionVO, CostManageVersion.class, "requestId", "requestName");
                        CostManageVersion lastPreSalesVersion = costManageEstimationResultsService.getLatestConfirmedPreSalesCost(projectId);
                        newBBCostVersion.setProjectId(projectId);
                        newBBCostVersion.setVersionType(CostManageVersionEnum.CBGL.getValue());
                        newBBCostVersion.setPreSaleVersionId(null != lastPreSalesVersion ? lastPreSalesVersion.getId() : null);
                        newBBCostVersion.setPreSaleVersionName(null != lastPreSalesVersion ? lastPreSalesVersion.getVersionName() : null);
                        newBBCostVersion.setCostBudgetType(CostBudgetTypeEnum.BBCB.getValue());
                        newBBCostVersion.setOperatorId(SecurityUtils.getUser() == null ? 0 : SecurityUtils.getUser().getId());
                        newBBCostVersion.setOperatorName(SecurityUtils.getUser() == null ? "" : SecurityUtils.getUser().getName());
                        BaseBuildEntityUtil.buildInsert(newBBCostVersion);
                        costManageVersionService.save(newBBCostVersion);
                        BeanUtil.copyProperties(newBBCostVersion, costManageVersionVO);
                        costManageVersionVO.setVersionId(newBBCostVersion.getId());
                        costManageVersionVO.setVersionName(newBBCostVersion.getVersionName());
                        costManageVersionVO.setCostBudgetType(CostBudgetTypeEnum.BBCB.getValue());
                        costManageVersionVO.setCostBudgetTypeName(CostBudgetTypeEnum.BBCB.getName());
                        costManageVersionVO.setStatus(CostManageStatusEnum.DRAFT.getValue());

                        List<CostManageEstimationResultsVO> results = costManageEstimationResultsService.findByVersionId(Arrays.asList(costManageVO.getVersionId()));
                        List<CostManageEstimationResults> newResults = new ArrayList<>(results.size());
                        List<CostManagePersonnelLevelDetail> newPersonnelLevelDetailList = new ArrayList<>();
                        List<CostManagePersonnelCustomDetail> newPersonnelCustomDetailList = new ArrayList<>();
                        if (CollUtil.isNotEmpty(results)) {
                            List<Long> resultIds = results.stream().map(CostManageEstimationResultsVO::getId).collect(Collectors.toList());
                            Map<Long, List<CostManagePersonnelLevelDetail>> personnelLevelMap =
                                    CollUtil.emptyIfNull(costManagePersonnelLevelDetailMapper.selectList(Wrappers.<CostManagePersonnelLevelDetail>lambdaQuery()
                                                    .in(CostManagePersonnelLevelDetail::getEstimationResultsId, resultIds))).stream()
                                            .collect(Collectors.groupingBy(CostManagePersonnelLevelDetail::getEstimationResultsId));
                            Map<Long, List<CostManagePersonnelCustomDetail>> personnelCustomMap =
                                    CollUtil.emptyIfNull(costManagePersonnelCustomDetailMapper.selectList(Wrappers.<CostManagePersonnelCustomDetail>lambdaQuery()
                                                    .in(CostManagePersonnelCustomDetail::getEstimationResultsId, resultIds))).stream()
                                            .collect(Collectors.groupingBy(CostManagePersonnelCustomDetail::getEstimationResultsId));
                            results.forEach(e -> {
                                CostManageEstimationResults costManageEstimationResults = BeanUtil.copyProperties(e, CostManageEstimationResults.class);
                                costManageEstimationResults.setVersionId(newBBCostVersion.getId());
                                costManageEstimationResults.setProjectId(projectId);
                                BaseBuildEntityUtil.buildInsert(costManageEstimationResults);
                                Long newResultId = costManageEstimationResults.getId();
                                newResults.add(costManageEstimationResults);

                                List<CostManagePersonnelLevelDetail> costManagePersonnelLevelDetails = personnelLevelMap.get(e.getId());
                                if (CollUtil.isNotEmpty(costManagePersonnelLevelDetails)) {
                                    costManagePersonnelLevelDetails.forEach(levelItem -> {
                                        CostManagePersonnelLevelDetail costManagePersonnelLevelDetail =
                                                BeanUtil.copyProperties(levelItem, CostManagePersonnelLevelDetail.class);
                                        costManagePersonnelLevelDetail.setEstimationResultsId(newResultId);
                                        BaseBuildEntityUtil.buildInsert(costManagePersonnelLevelDetail);
                                        newPersonnelLevelDetailList.add(costManagePersonnelLevelDetail);
                                    });
                                }

                                List<CostManagePersonnelCustomDetail> costManagePersonnelCustomDetails = personnelCustomMap.get(e.getId());
                                if (CollUtil.isNotEmpty(costManagePersonnelCustomDetails)) {
                                    costManagePersonnelCustomDetails.forEach(customItem -> {
                                        CostManagePersonnelCustomDetail costManagePersonnelCustomDetail =
                                                BeanUtil.copyProperties(customItem, CostManagePersonnelCustomDetail.class);
                                        costManagePersonnelCustomDetail.setEstimationResultsId(newResultId);
                                        BaseBuildEntityUtil.buildInsert(costManagePersonnelCustomDetail);
                                        newPersonnelCustomDetailList.add(costManagePersonnelCustomDetail);
                                    });
                                }
                            });
                            costManageEstimationResultsService.saveBatch(newResults);
                            if (CollUtil.isNotEmpty(newPersonnelLevelDetailList)) {
                                costManagePersonnelLevelDetailMapper.batchSave(newPersonnelLevelDetailList);
                            }
                            if (CollUtil.isNotEmpty(newPersonnelCustomDetailList)) {
                                costManagePersonnelCustomDetailMapper.batchSave(newPersonnelCustomDetailList);
                            }
                        }
                    }
                }

                costManageVersionVoList.add(costManageVersionVO);

                //  发起项目变更流程的时候，额外传入预算明细id和发生金额累计值
                Map<String, OaXmysbDTO> oaXmysbDtoMap;
                if (OAFormTypeEnum.XMBG.equals(businessTypeEnum)) {
                    List<OaXmysbDTO> xmysbDTOList = dbApiUtil.getInfoByXmysb(projectId, null);
                    oaXmysbDtoMap = xmysbDTOList.stream().collect(Collectors.toMap(e ->
                                    e.getKmmc() + StringPool.DASH + e.getSl(),
                                    Function.identity()));
                }else {
                    oaXmysbDtoMap = Collections.emptyMap();
                }

                //构建明细表数据
                Map<String, Object> detailDataMap = new HashMap<>();
                List<Map<String, Object>> tableRecordList = new ArrayList<>();
                processEstimationResults.forEach(costResult -> {
                            List<OaMainParamDTO> tableFieldList = new ArrayList<>();

                            //科目名称-科目id
                            if (costResult.getAccountId() != null) {
                                tableFieldList.add(buildOaMainParamDto("kmmc", costResult.getAccountOaId()));
                            }
                            //科目代码
                            if (StringUtils.isNotEmpty(costResult.getAccountCode())) {
                                tableFieldList.add(buildOaMainParamDto("kmdm", costResult.getAccountCode()));
                            }
                            //成本费用项-科目类别
                            if (costResult.getAccountCategoryId() != null) {
                                tableFieldList.add(buildOaMainParamDto("cbfyx", costResult.getAccountCategoryId()));
                            }
                            //预算金额(含税)
                            if (costResult.getBudgetAmountIncludedTax() != null) {
                                tableFieldList.add(buildOaMainParamDto(
                                        OAFormTypeEnum.SYLZAB.getValue().equals(dto.getFormType()) ? "ysjehs" : "cbjehs",
                                        costResult.getBudgetAmountIncludedTax()));
                            }
                            //税率
                            if (costResult.getTaxRate() != null) {
                                tableFieldList.add(buildOaMainParamDto("sl", costResult.getTaxRate()));
                            }
                            //预算金额(不含税)
                            if (costResult.getBudgetAmountExcludingTax() != null) {
                                tableFieldList.add(buildOaMainParamDto(
                                        OAFormTypeEnum.SYLZAB.getValue().equals(dto.getFormType()) ? "ysjebhs" : "cbjebhs",
                                        costResult.getBudgetAmountExcludingTax()));
                            }
                            //成本费用说明
                            if (StringUtils.isNotEmpty(costResult.getRemark())) {
                                tableFieldList.add(buildOaMainParamDto("cbfysm", costResult.getRemark()));
                            }

                            //  发起项目变更流程的时候，额外传入预算明细id和发生金额累计值
                            OaXmysbDTO oaXmysbDTO = oaXmysbDtoMap.get(costResult.getAccountOaId() + StringPool.DASH + costResult.getTaxRate());
                            if (OAFormTypeEnum.XMBG.equals(businessTypeEnum) && oaXmysbDTO != null) {
                                // 费用报销明细id
                                tableFieldList.add(buildOaMainParamDto("mxid", oaXmysbDTO.getId()));
                                // 发生金额累计
                                tableFieldList.add(buildOaMainParamDto("fsjelj", oaXmysbDTO.getFsjelj()));
                                // 可用预算金额
                                tableFieldList.add(buildOaMainParamDto("kyysje", oaXmysbDTO.getKyysje()));
                            }

                            Map<String, Object> tableRecordMap = new HashMap<>();
                            tableRecordMap.put("recordOrder", 0);
                            tableRecordMap.put("workflowRequestTableFields", tableFieldList);
                            tableRecordList.add(tableRecordMap);
                        }
                );

                detailDataMap.put("tableDBName", OAFormTypeEnum.getDetailTableByVal(dto.getFormType()));
                detailDataMap.put("workflowRequestTableRecords", tableRecordList);
                detailDataList.add(detailDataMap);
            }
            if (EnumUtils.enumOrEquals(businessTypeEnum, OAFormTypeEnum.XMBG)) {
                mainDataList.add(buildOaMainParamDto("pdsfxzcbmx", "0"));
                mainDataList.add(buildOaMainParamDto("change_precos_or_unprecos", changePrecosOrUnprecos));
            }
            if (EnumUtils.enumOrEquals(businessTypeEnum, OAFormTypeEnum.XMLXBB)) {
                // 成本金额-不含税（不含垫资）
                mainDataList.add(buildOaMainParamDto("cbjebhsbhdz", budgetAmountTotalExcludingTax.toString()));
            }
        }
    }

    private static OaMainParamDTO buildOaMainParamDto(String name, Object obj) {
        return OaMainParamDTO.builder().fieldName(name).fieldValue(obj == null ? StringPool.EMPTY : String.valueOf(obj)).build();
    }


    /**
     * 添加项目毛利测算流程请求
     *
     * @param dto                     请求参数
     * @param mainDataList            流程主表信息
     * @param costManageVersionVoList 版本流程信息
     */
    public void addQuotationFlowRequest(CreateRequestDTO dto,
                                        List<OaMainParamDTO> mainDataList,
                                        List<CostManageVersionVO> costManageVersionVoList) {
        //查询项目报价与毛利测算信息
        CostBaselineQuotationVO costBaselineQuotationVO = costBaselineQuotationMapper.getCostBaselineQuotationLast(dto.getProjectId());
        if (BeanUtil.isNotEmpty(costBaselineQuotationVO)) {
//            //处理毛利测算详情
//            costBaselineQuotationService.setGrossProfitMeasurementDetail(dto.getProjectId(), costBaselineQuotationVO);
            //处理版本流程信息
            CostManageVersionVO costManageVersionVO = new CostManageVersionVO();
            BeanUtil.copyProperties(costBaselineQuotationVO, costManageVersionVO);
            costManageVersionVoList.add(costManageVersionVO);

            //销售金额（含税）-》收入总额(含税)
            if (StringUtils.isNotEmpty(costBaselineQuotationVO.getIncomeAmountIncludedTax())) {
                mainDataList.add(buildOaMainParamDto("xsjehs", costBaselineQuotationVO.getIncomeAmountIncludedTax()));
            }
            //A表收入总额(含税) -》收入总额(含税)
            if (StringUtils.isNotEmpty(costBaselineQuotationVO.getIncomeAmountIncludedTax())) {
                mainDataList.add(buildOaMainParamDto("absrzehs", costBaselineQuotationVO.getIncomeAmountIncludedTax()));
            }
            //销售金额（不含税）-》收入总额(不含税)
            if (StringUtils.isNotEmpty(costBaselineQuotationVO.getIncomeAmountExcludingTax())) {
                mainDataList.add(buildOaMainParamDto("xsjebhs", costBaselineQuotationVO.getIncomeAmountExcludingTax()));
            }
            //成本（不含税）-》成本管理的预算金额（不含税）合计值
            if (StringUtils.isNotEmpty(costBaselineQuotationVO.getBudgetAmountExcludingTax())) {
                mainDataList.add(buildOaMainParamDto("cbbhs", costBaselineQuotationVO.getBudgetAmountExcludingTax()));
            }
            //毛利(不含税) -》预计毛利(不含税)
            if (StringUtils.isNotEmpty(costBaselineQuotationVO.getExpectedGrossProfit())) {
                mainDataList.add(buildOaMainParamDto("mlbhs", costBaselineQuotationVO.getExpectedGrossProfit()));
                mainDataList.add(buildOaMainParamDto("mlbhdz", costBaselineQuotationVO.getExpectedGrossProfit()));
            }
            //毛利率(不含税) -》预计毛利率(不含税)
            if (StringUtils.isNotEmpty(costBaselineQuotationVO.getExpectedGrossProfitMargin())) {
                mainDataList.add(buildOaMainParamDto("mllbhs", costBaselineQuotationVO.getExpectedGrossProfitMargin()));
                mainDataList.add(buildOaMainParamDto("mllbhdz", costBaselineQuotationVO.getExpectedGrossProfitMargin()));
            }
            //A表项目预计毛利率 -》预计毛利率(不含税)
            if (StringUtils.isNotEmpty(costBaselineQuotationVO.getExpectedGrossProfitMargin())) {
                mainDataList.add(buildOaMainParamDto("abxmyjmll", costBaselineQuotationVO.getExpectedGrossProfitMargin()));
            }
        }
    }


    /**
     * 上传流程附件：项目目标-详细说明文档
     *
     * @param costManageTarget 项目目标
     * @param oaAccountVO      用户OA账号
     * @param fileIdBuilder    文件ids
     */
    private void uploadFiles(CostManageTargetVO costManageTarget,
                             OaAccountVO oaAccountVO,
                             StringBuilder fileIdBuilder) {
        if (StringUtils.isEmpty(costManageTarget.getDetailFiles())) {
            return;
        }
        List<InnerDoc> innerDocList = new ArrayList<>();
        try {
            Arrays.asList(costManageTarget.getDetailFiles().split(Constants.COMMA)).stream().forEach(s -> {
                InnerDoc innerDoc = sysFileService.getFileByte(Long.valueOf(s));
                if (!Optional.ofNullable(innerDoc).isPresent()) {
                    throw new BusinessException("根据文件id:" + s + ",没有找到对应的文件");
                }
                innerDocList.add(innerDoc);
            });
        } catch (Exception e) {
            log.error("获取文件异常，原因:{}", e.getMessage());
            throw new BusinessException("获取文件异常，原因:{}", e.getMessage());
        }

        // 调用OaUtil的通用文件上传方法
        oaUtil.uploadFiles(innerDocList, oaAccountVO, fileIdBuilder);
    }


    @Override
    public ApiResult<String> remind() {
        QueryWrapper<ProjectBusinessMilestones> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ProjectBusinessMilestones::getType, 2);
        List<ProjectBusinessMilestones> milestones = baseMapper.selectList(queryWrapper);
        LocalDate today = LocalDate.now();
        LocalDate sevenDaysBefore = today.plusDays(6);

        //商务里程碑逾期提醒
        List<ProjectBusinessMilestones> achievementList =
                milestones.stream().filter(m -> today.equals(m.getExpectedCompleteDate())).collect(Collectors.toList());
        //商务里程碑达成提醒
        List<ProjectBusinessMilestones> overdueList =
                milestones.stream().filter(m -> sevenDaysBefore.equals(m.getExpectedCompleteDate())).collect(Collectors.toList());

        if (CollUtil.isNotEmpty(achievementList)) {
            sendReminder(MsgModelEnum.BUSINESS_MILESTONE_OVERDUE_REMINDER, achievementList);
        }
        if (CollUtil.isNotEmpty(overdueList)) {
            sendReminder(MsgModelEnum.BUSINESS_MILESTONE_ACHIEVEMENT_REMINDER, overdueList);
        }
        return ApiResult.successMsg("提醒发送成功!");
    }

    private void sendReminder(MsgModelEnum msgModelEnum, List<ProjectBusinessMilestones> milestones) {
        String sendTime = DateUtil.formatDateTime(new Date());
        WeComBatchModel weComBatchModel = new WeComBatchModel();
        List<WeComModel> weComModelList = new ArrayList<>();
        BcpMessageBatchDTO bcpMessageDto = new BcpMessageBatchDTO();
        Map<String, BcpMessageContentModel> bcMessageContentModelMap = new HashMap<>();
        List<BcpMessageTargetBatchDTO> bcpMessageTargetList = new ArrayList<>();
        for (ProjectBusinessMilestones milestone : milestones) {
            ProjectInfo projectInfo = projectInfoMapper.selectById(milestone.getProjectId());
            if (projectInfo != null && projectInfo.getManagerUserId() != null) {
                WeComModel model = new WeComModel();
                String contentTemplate = msgModelEnum.getName();
                String contentTemplate1 = msgModelEnum.getName1();

                List<BcpMessageTargetDTO> targetList = new ArrayList<>();
                // 企微消息接收对象
                BcpMessageTargetDTO bcpMessageTargetDTO = new BcpMessageTargetDTO();
                // 站内信消息接收对象
                BcpMessageTargetBatchDTO bcpMessageTargetBatchDto = new BcpMessageTargetBatchDTO();
                bcpMessageTargetDTO.setTargetId(String.valueOf(projectInfo.getManagerUserId()));
                bcpMessageTargetBatchDto.setTargetId(String.valueOf(projectInfo.getManagerUserId()));
                bcpMessageTargetDTO.setTargetName(projectInfo.getManagerUserName());
                targetList.add(bcpMessageTargetDTO);

                String url = acceptanceManagementUrl +
                        Base64.encode(CharSequenceUtil.format(redirect, milestone.getProjectId()), Charsets.UTF_8);
                // 进行消息推送
                String content = CharSequenceUtil.format(contentTemplate,
                        CharSequenceUtil.blankToDefault(milestone.getProjectName(), CharSequenceUtil.EMPTY),
                        CharSequenceUtil.blankToDefault(milestone.getBusinessMilestones().toString(), CharSequenceUtil.EMPTY));

                String content1 = CharSequenceUtil.format(contentTemplate1,
                        CharSequenceUtil.blankToDefault(milestone.getProjectName(), CharSequenceUtil.EMPTY),
                        CharSequenceUtil.blankToDefault(milestone.getBusinessMilestones().toString(), CharSequenceUtil.EMPTY),
                        url);

                model.setSource(SourceEnum.PROJECT.getValue());
                model.setType(MsgTypeEnum.TEXT_MSG.getValue());
                model.setTitle(msgModelEnum.getTitle());
                model.setContent(content1);
                model.setSenderId(1000L);
                model.setSender("admin");
                model.setTargetType(TargetTypeEnum.USERS.getValue());
                model.setTargetList(targetList);
                model.setSendTime(sendTime);
                weComModelList.add(model);

                // 站内信消息
                String demandIdStr = String.valueOf(milestone.getId());
                BcpMessageContentModel bcm = new BcpMessageContentModel();
                bcm.setContent(content);
                bcm.setTitle(msgModelEnum.getTitle());
                bcm.setType(MsgTypeEnum.TEXT_MSG.getValue());
                bcm.setRedirectUrl(url);
                bcMessageContentModelMap.put(demandIdStr, bcm);
                bcpMessageTargetBatchDto.setContentId(demandIdStr);
                bcpMessageTargetBatchDto.setTargetName(projectInfo.getManagerUserName());
                bcpMessageTargetList.add(bcpMessageTargetBatchDto);
            }
        }
        // 发送企微消息
        weComBatchModel.setData(weComModelList);
        remoteSendMsgService.sendWeComMsgBatch(weComBatchModel);

        // 发送站内信消息
        bcpMessageDto.setSenderId(1000L);
        bcpMessageDto.setSender("admin");
        bcpMessageDto.setChannel(ChannelEnum.MAIL.getValue());
        bcpMessageDto.setSource(SourceEnum.PROJECT.getValue());
        bcpMessageDto.setTargetType(TargetTypeEnum.USERS.getValue());
        bcpMessageDto.setTargetList(bcpMessageTargetList);
        bcpMessageDto.setContentMap(bcMessageContentModelMap);
        bcpMessageDto.setSendTime(sendTime);
        remoteSendMsgService.sendMsgBatch(SecurityConstants.FROM_IN, bcpMessageDto);
    }

    /**
     * 根据业务类型构建具体请求数据
     *
     * @param businessTypeEnum 业务类型枚举
     * @param projectInfo      项目信息
     * @param requestMap       请求参数
     * @param mainDataList     主表数据列表
     * @param detailDataList   明细表数据列表
     */
    private Long buildRequestByBusinessType(OAFormTypeEnum businessTypeEnum,
                                            ProjectInfo projectInfo,
                                            JSONObject requestMap,
                                            List<OaMainParamDTO> mainDataList,
                                            List<Map<String, Object>> detailDataList) {
        String projectIdStr = String.valueOf(projectInfo.getId());
        switch (businessTypeEnum) {
            case SYLZAB:
                return handleSYLZAB(projectInfo, projectIdStr, mainDataList);
            case XMLXBB:
                return handleXMLXBB(requestMap, projectInfo, projectIdStr, mainDataList);
            case HTHQ:
                return handleHTHQ(projectIdStr, mainDataList);
            case XMBG:
                return handleXMBG(requestMap, projectInfo, projectIdStr, mainDataList);
            case BGLCB:
                return handleBGLCB(projectIdStr, mainDataList);
            case BGHTHQ:
                return handleBGHTHQ(projectInfo, mainDataList);
            case XMJX:
                return handleXMJX(projectIdStr, mainDataList);
            case LCBDCSP:
                return handleLCBDCSP(requestMap, projectIdStr, mainDataList, detailDataList);
            case RLWBJSSP:
                return handleRLWBJSSP(requestMap, projectInfo, projectIdStr, mainDataList, detailDataList);
            default:
                return null;
        }
    }


    /**
     * 根据业务类型获取OA标题
     *
     * @param businessTypeEnum 业务类型枚举
     * @param projectInfo      项目信息
     * @return OA流程标题
     */
    private String getOaTitleByBusinessType(OAFormTypeEnum businessTypeEnum, ProjectInfo projectInfo) {
        switch (businessTypeEnum) {
            case SYLZAB:
            case XMLXBB:
            case XMBG:
            case BGHTHQ:
                return getOaFlowReqTitle(projectInfo, businessTypeEnum.getName());
            default:
                return getOaFlowReqTitle(businessTypeEnum.getName());
        }
    }

    private Long handleXMJX(String projectIdStr, List<OaMainParamDTO> mainDataList) {
        mainDataList.add(buildMainParam("xmmc", projectIdStr));
        return null;
    }

    /**
     * 处理商业论证A表
     */
    private Long handleSYLZAB(ProjectInfo projectInfo, String projectIdStr,
                              List<OaMainParamDTO> mainDataList) {
        String requestName = dbApiUtil.getOARequestByA(projectInfo.getId());
        if (ObjectUtil.isNotEmpty(requestName)) {
            throw new ServiceException("当前项目已发起过：" + requestName);
        }
        mainDataList.add(buildMainParam("sjmc", projectIdStr));
        mainDataList.add(buildMainParam("pjname", projectIdStr));
        //流程提交验证
        mainDataList.add(buildMainParam("workflow_submit", "0"));
        return null;
    }

    /**
     * 处理项目立项B表
     */
    private Long handleXMLXBB(JSONObject requestMap, ProjectInfo projectInfo, String projectIdStr,
                              List<OaMainParamDTO> mainDataList) {
        String requestName = dbApiUtil.getOARequestByB(projectInfo.getId());
        if (ObjectUtil.isNotEmpty(requestName)) {
            throw new ServiceException("当前项目已发起过：" + requestName);
        }
        BusinessCaseVO businessCaseVO = dbApiUtil.getBusinessCaseIdByProjectId(projectInfo.getId());
        if (businessCaseVO == null || businessCaseVO.getId() == null) {
            throw new ServiceException("未发起商业论证A表，不可发起项目立项B表流程");
        }
        mainDataList.add(buildMainParam("sjmc", String.valueOf(businessCaseVO.getId())));
        mainDataList.add(buildMainParam("pjname", projectIdStr));
        mainDataList.add(buildMainParam("xmmcid", projectIdStr));
        mainDataList.add(buildMainParam("xmid", projectIdStr));
        //流程提交验证
        mainDataList.add(buildMainParam("workflow_submit", "0"));
        return null;
    }

    /**
     * 处理合同会签
     */
    private Long handleHTHQ(String projectIdStr, List<OaMainParamDTO> mainDataList) {
        mainDataList.add(buildMainParam("xmmcid", projectIdStr));
        return null;
    }

    /**
     * 处理项目变更
     */
    private Long handleXMBG(JSONObject requestMap,
                            ProjectInfo projectInfo,
                            String projectIdStr,
                            List<OaMainParamDTO> mainDataList) {
        mainDataList.add(buildMainParam("xmmc", projectIdStr));
        mainDataList.add(buildMainParam("xzbgxm", projectIdStr));
        mainDataList.add(buildMainParam("bgnr", getBgnrStr(requestMap)));
        mainDataList.add(buildMainParam("pdzjxmmcsftx", "0"));
        mainDataList.add(buildMainParam("pdsfxzxmmb", "0"));
        //流程提交验证
        mainDataList.add(buildMainParam("workflow_submit", "0"));
        return null;
    }

    private String getBgnrStr(JSONObject requestMap) {
        String changeContentStr = requestMap.getString("changeContentStr");
        if (StrUtil.isNotBlank(changeContentStr)) {
            List<Integer> bgnrList = new ArrayList<>(5);
            Arrays.stream(changeContentStr.split(",")).forEach(e -> {
                if ("0".equals(e) || "1".equals(e) || "2".equals(e)) {
                    bgnrList.add(4);
                } else if ("3".equals(e)) {
                    bgnrList.add(0);
                } else if ("4".equals(e)) {
                    bgnrList.add(9);
                }
            });
            return CollUtil.join(bgnrList, ",");
        } else {
            return "";
        }
    }

    /**
     * 处理变更里程碑
     */
    private Long handleBGLCB(String projectIdStr, List<OaMainParamDTO> mainDataList) {
        mainDataList.add(buildMainParam("xmmc", projectIdStr));
        mainDataList.add(buildMainParam("bgnr", "8"));
        return null;
    }

    /**
     * 处理变更合同会签
     */
    private Long handleBGHTHQ(ProjectInfo projectInfo, List<OaMainParamDTO> mainDataList) {
        List<ProjectBusinessMilestones> businessMilestonesList = lambdaQuery()
                .eq(ProjectBusinessMilestones::getProjectId, projectInfo.getId())
                .eq(ProjectBusinessMilestones::getType, 2).list();
        List<String> contractIds = businessMilestonesList.stream()
                .map(ProjectBusinessMilestones::getContractId).distinct().collect(Collectors.toList());
        if (CollUtil.isNotEmpty(businessMilestonesList) && contractIds.size() == 1) {
            mainDataList.add(buildMainParam("htmcid", String.valueOf(contractIds.get(0))));
        }
        mainDataList.add(buildMainParam("bgnr", "2"));
        mainDataList.add(buildMainParam("sfxzhkjh", "0"));
        return null;
    }

    /**
     * 处理里程碑达成审批
     */
    private Long handleLCBDCSP(JSONObject requestMap, String projectIdStr,
                               List<OaMainParamDTO> mainDataList, List<Map<String, Object>> detailDataList) {
        Long businessMilestoneId = requestMap.getLong("projectBusinessMilestoneId");
        ProjectBusinessMilestones businessMilestones = getById(businessMilestoneId);
        if (ObjectUtil.isEmpty(businessMilestones)) {
            throw new ServiceException("里程碑id不能为空~");
        }
        Long requestId = checkBusinessMilestonesFlow(businessMilestones);
        if (requestId != null) {
            return requestId;
        }
        mainDataList.add(buildMainParam("pjname", projectIdStr));
        ContractLedger contractLedger = contractLedgerMapper.selectById(businessMilestones.getContractId());
        if (contractLedger == null) {
            throw new ServiceException("合同台账未找到对应合同~");
        }
        //合同签约主体
        mainDataList.add(buildMainParam("xmqyzt", String.valueOf(contractLedger.getHtssgs())));
        //流程提交验证
        mainDataList.add(buildMainParam("workflow_submit", "0"));
        buildLcbdcspDeatil(businessMilestones, detailDataList, contractLedger);
        return null;
    }

    /**
     * 处理人力外包结算审批
     */
    private Long handleRLWBJSSP(JSONObject requestMap, ProjectInfo projectInfo, String projectIdStr,
                                List<OaMainParamDTO> mainDataList, List<Map<String, Object>> detailDataList) {
        Long businessMilestoneId = requestMap.getLong("projectBusinessMilestoneId");
        ProjectBusinessMilestones businessMilestones = getById(businessMilestoneId);
        if (ObjectUtil.isEmpty(businessMilestones)) {
            throw new ServiceException("里程碑id不能为空~");
        }
        Long requestId = checkBusinessMilestonesFlow(businessMilestones);
        if (requestId != null) {
            return requestId;
        }
        mainDataList.add(buildMainParam("htssxmmc", projectIdStr));
        mainDataList.add(buildMainParam("xmbh", projectInfo.getItemNo()));
        mainDataList.add(buildMainParam("htmc", String.valueOf(businessMilestones.getContractId())));
        //流程提交验证
        mainDataList.add(buildMainParam("workflow_submit", "0"));
        ProjectBussMilSettlementDTO bussMilSettlementDto = getProjectBussMilSettlementDTO(requestMap, projectInfo);
        buildRlwbjsspDeatil(OAFormTypeEnum.RLWBJSSP, businessMilestones, bussMilSettlementDto, detailDataList);
        return null;
    }
}
