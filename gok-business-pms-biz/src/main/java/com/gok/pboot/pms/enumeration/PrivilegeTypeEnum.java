package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;

/**
 * - 人员审核权限类型枚举 -
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/8/23 14:45
 */
@AllArgsConstructor
public enum PrivilegeTypeEnum implements ValueEnum<Integer> {

    // 注释详见name属性
    DEPT_MANAGER(0, "业务部门管理员"),
    PROJECT_AUDITOR(1, "项目审核员"),
    ADMIN(2, "管理员"),
    PROJECT_OPERATOR(3, "项目操作员");

    private final Integer value;
    private final String name;

    @Override
    public Integer getValue() {
        return value;
    }

    @Override
    public String getName() {
        return name;
    }

}
