package com.gok.pboot.pms.cost.enums;

import com.gok.pboot.pms.enumeration.ValueEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 配置版本类型 enum
 *
 * <AUTHOR>
 * @date 2025/01/07
 */
@AllArgsConstructor
@Getter
public enum CostConfigVersionTypeEnum implements ValueEnum<Integer> {

    /**
     * 差旅住宿标准
     */
    CLZSBZ(0, "差旅住宿标准"),

    /**
     * 差旅补贴标准
     */
    CLBZBZ(1, "差旅补贴标准"),

    /**
     * 自定义补贴
     */
    ZDYBT(2, "自定义补贴"),

    /**
     * 成本科目配置
     */
    CBKMPZ(3, "成本科目配置"),

    /**
     * 人员级别单价配置
     */
    RYJBDJPZ(4, "人员级别单价配置"),

    /**
     * 现金流计划
     */
    XJLJH(5, "现金流计划");

    private final Integer value;

    private final String name;

}
