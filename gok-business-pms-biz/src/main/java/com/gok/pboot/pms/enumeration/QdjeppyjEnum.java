package com.gok.pboot.pms.enumeration;

/**
 * 项目-签单金额评判依据枚举
 *
 * <AUTHOR>
 */
public enum QdjeppyjEnum implements ValueEnum<Integer> {
    /**
     * 预估
     */
    estimate(0, "预估"),
    /**
     * 预算已申报
     */
    declaration(1, "预算已申报"),
    /**
     * 预算已审批
     */
    examine(2, "预算已审批");

    //值
    private Integer  value;
    //名称
    private String name;

    QdjeppyjEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
    /**
     * 获取值
     *
     * @return Integer
     */
    @Override
    public Integer getValue() {
        return value;
    }

    /**
     * 获取名称
     *
     * @return String
     */
    @Override
    public String getName() {
        return name;
    }

    public static String getNameByVal(Integer value) {
        for (QdjeppyjEnum qdjeppyjEnum : QdjeppyjEnum.values()) {
            if (qdjeppyjEnum.value.equals(value)) {
                return qdjeppyjEnum.name;
            }
        }
        return "";
    }
    public static Integer getValByName(String name) {
        for (QdjeppyjEnum qdjeppyjEnum : QdjeppyjEnum.values()) {
            if (qdjeppyjEnum.getName().equals(name)) {
                return qdjeppyjEnum.getValue();
            }
        }
        return null;
    }

    public static QdjeppyjEnum getQdjeppyjEnum(Integer value) {
        for (QdjeppyjEnum qdjeppyjEnum : QdjeppyjEnum.values()) {
            if (qdjeppyjEnum.value.equals(value)) {
                return qdjeppyjEnum;
            }
        }
        return null;
    }
}
