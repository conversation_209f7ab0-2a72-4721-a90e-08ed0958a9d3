package com.gok.pboot.pms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.entity.CustomerBusiness;
import com.gok.pboot.pms.entity.dto.CustomerBusinessDTO;
import com.gok.pboot.pms.entity.dto.CustomerBusinessSearchDTO;
import com.gok.pboot.pms.entity.vo.CustomerBusinessListVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 客户经营单元(基础概况) Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-12
 */
@Mapper
public interface CustomerBusinessMapper extends BaseMapper<CustomerBusiness> {

    Page<CustomerBusiness> findListPage(Page<CustomerBusiness> page,
                                          @Param("filter") CustomerBusinessDTO filter
                                           );

    List<CustomerBusinessListVO> findNameList(@Param("filter") CustomerBusinessSearchDTO customerBusinessSearchDTO);

}
