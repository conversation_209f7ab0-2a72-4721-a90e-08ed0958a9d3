package com.gok.pboot.pms.Util;

import java.util.ArrayList;
import java.util.List;

/**
 * @Auther chenhc
 * @Date 2022-08-22 16:41
 */
public class NewOldComparer {
    //新增元素集合
    private List<Long> insertIds = new ArrayList<>();
    //修改元素集合
    private List<Long> updateIds = new ArrayList<>();
    //删除元素集合
    private List<Long> deleteIds = new ArrayList<>();

    public List<Long> insertIds() {
        return insertIds;
    }

    public List<Long> updateIds() {
        return updateIds;
    }

    public List<Long> deleteIds() {
        return deleteIds;
    }

    public void NewOldComparer(List<Long> newIds, List<Long> oldIds) {
        if (null != newIds && newIds.size() > 0) {
            hasNewIds(newIds, oldIds);
        } else {
            //newIds为空表示后端oldIds全部删除
            noHasNewIds(oldIds);
        }
    }

    private void hasNewIds(List<Long> newIds, List<Long> oldIds) {
        if (oldIds.size() == 0) {
            this.insertIds = newIds;
        } else {
            for (Long newId : newIds) {
                //可以根据他有没有传入id 来判断是否为新增的数据  代码要改
                if (!oldIds.contains(newId)) {
                    this.insertIds.add(newId);
                } else {
                    this.updateIds.add(newId);
                }

            }
            //需要删除的，将页面传过来的新集合的id获取得到List集合
            List<Long> newidList = newIds;
            for (Long oldId : oldIds) {
                //如果新集合中不包含就集合元素的id就将其加入到删除集合
                if (!newidList.contains(oldId)) {
                    this.deleteIds.add(oldId);
                }
            }
        }
    }

    private void noHasNewIds(List<Long> oldIds) {
        if (null != oldIds && oldIds.size() > 0) {
            this.deleteIds = oldIds;
        }
    }

}
