package com.gok.pboot.pms.entity.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * 交付人员工时导入实体
 *
 * <AUTHOR>
 * @create 2023/2/9
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PersonnelDeliveryHourImportDTO {

    /**
     * 项目编号
     */
    @ExcelProperty(value = "项目编号", index = 0)
    @NotBlank(message = "项目编号不能为空")
    private String projectCode;

    /**
     * 项目名称
     */
    @ExcelProperty(value = "项目名称", index = 1)
    private String projectName;

    /**
     * 人员名称
     */
    @ExcelProperty(value = "姓名", index = 2)
    private String userRealName;

    /**
     * 工号
     */
    @ExcelProperty(value = "工号", index = 3)
    @NotBlank(message = "工号不能为空")
    private String workCode;

    /**
     * 应出勤天数
     */
    @ExcelProperty(value = "应出勤天数", index = 4)
    @NotBlank(message = "应出勤天数不能为空")
    private String cwDueAttendance;

    /**
     * 实际出勤天数
     */
    @ExcelProperty(value = "实际出勤天数", index = 5)
    private String attendanceDays;

    /**
     * 工作日正常工时（天）
     */
    @ExcelProperty(value = "工作日正常工时（天）", index = 6)
    private String normalWorkDays;
    /**
     * 工作日调休工时（天）
     */
    @ExcelProperty(value = "工作日调休工时（天）", index = 7)
    private String ompensatoryDays;
    /**
     * 休息日加班工时（天）
     */
    @ExcelProperty(value = "休息日加班工时（天）", index = 8)
    private String restWorkDays;
    /**
     * 节假日加班工时（天）
     */
    @ExcelProperty(value = "节假日加班工时（天）", index = 9)
    private String holidaysWorkDays;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注", index = 10)
    private String remark;


}
