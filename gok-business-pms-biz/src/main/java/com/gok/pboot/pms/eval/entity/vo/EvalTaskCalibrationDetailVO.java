package com.gok.pboot.pms.eval.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 工单评价校准
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class EvalTaskCalibrationDetailVO{

    /**
     * id
     */
    private Long id;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 员工id
     */
    private Long employeeId;

    /**
     * 员工姓名
     */
    private String employeeName;

    /**
     * 当前评分
     */
    private Double currentScore;

    /**
     * 当前等级
     */
    private Integer currentLevel;

    /**
     * 当前等级Txt
     */
    private String currentLevelTxt;

    /**
     * 调整后的评分
     */
    private Double adjustedScore;

    /**
     * 调整后等级
     */
    private Integer adjustedLevel;

    /**
     * 调整后等级Txt
     */
    private String adjustedLevelTxt;

    /**
     * 调整原因
     */
    private String adjustmentReason;
}