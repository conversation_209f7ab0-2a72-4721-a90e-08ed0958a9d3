package com.gok.pboot.pms.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.sql.Timestamp;
import java.time.LocalDate;

/**
 * 项目会议纪要表分页查询 Vo
 *
 * <AUTHOR>
 * @since 2023-07-13
 **/
@Data
public class ProjectMeetingFindPageVO {

    /**
     * 项目会议纪要主键id
     */
    private Long id;

    /**
     * 会议名称
     */
    private String name;

    /**
     * 召集人id
     */
    private Long convenerId;

    /**
     * 召集人
     */
    private String convener;

    /**
     * 会议日期
     * pattern = "yyyy-MM-dd"
     */
    private LocalDate meetingDate;

    /**
     * 会议地点
     */
    private String place;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 修订人
     */
    private String modifier;

    /**
     * 创建时间
     * pattern = "yyyy-MM-dd HH:mm:ss"
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp ctime;

    /**
     * 修订时间
     * pattern = "yyyy-MM-dd HH:mm:ss"
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp mtime;

}
