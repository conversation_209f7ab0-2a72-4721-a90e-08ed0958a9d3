package com.gok.pboot.pms.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 项目干系人-参与人批量dto
 *
 * <AUTHOR>
 * @date 2024/02/19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectStakeholderMemberBatchDTO {

    /**
     * 项目id
     */
    @NotNull
    private Long projectId;

    /**
     * 角色类型（0=项目销售经理，1=项目售前经理，2=项目经理，3=项目操作助理，4=项目成员）
     * {@link com.gok.pboot.pms.enumeration.RoleTypeEnum}
     */
    @NotNull
    private Integer roleType;

    /**
     * 成员列表
     */
    private List<ProjectStakeholderMemberDTO> members;

}
