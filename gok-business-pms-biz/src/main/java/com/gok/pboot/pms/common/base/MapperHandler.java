package com.gok.pboot.pms.common.base;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface MapperHandler<T> extends BaseMapper<T> {


    Page<T> findList(Page<T> page, @Param("filter") Map<String, Object> filter);

    List<T> findList(@Param("filter") T filter);

}
