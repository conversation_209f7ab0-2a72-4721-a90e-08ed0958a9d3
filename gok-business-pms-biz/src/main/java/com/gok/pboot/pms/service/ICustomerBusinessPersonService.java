package com.gok.pboot.pms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.entity.CustomerBusinessPerson;

import java.util.List;

/**
 * <p>
 * 客户经营单元表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-12
 **/
public interface ICustomerBusinessPersonService extends IService<CustomerBusinessPerson> {

    /**
     * 批量逻辑删除
     * @param list id列表
     */
   void batchDel(List<Long> list);

    /**
     * 批量新增
     * @param list 人员列表
     */
    void batchCreate(List<CustomerBusinessPerson> list);

    /**
     * 列表查找
     * @param qo 查询茶树
     * @return  List<CustomerBusinessPerson>
     */
    List<CustomerBusinessPerson> findList(CustomerBusinessPerson qo);

    /**
     * 判断用户是否是业务角色
     * @param businessId
     * @param userId
     * @return
     */
    List<Integer> manageRoleList(Long businessId, Long userId);


    /**
     * 判断用户是否是业务角色
     * @param businessId
     * @param userIds
     * @return
     */
    List<Integer> manageRoleList(Long businessId, List<Long> userIds);


    /**
     * 判断是否是业务角色
     *
     * @param businessId 项目id
     * @param userId 用户id
     * @return boolean
     */
    boolean isManageRole(Long businessId, List<Long> userId,Integer role);





}
