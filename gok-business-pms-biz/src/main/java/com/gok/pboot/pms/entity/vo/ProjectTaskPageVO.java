package com.gok.pboot.pms.entity.vo;

import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.entity.domain.ProjectTask;
import com.gok.pboot.pms.enumeration.TaskWorkingState;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 任务统计的分页列表vo
 *
 * <AUTHOR>
 * @date 2023/8/24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectTaskPageVO {

    /**
     * 任务id
     */
    private Long taskId;

    /**
     * 父级ID
     */
    private Long parentId;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 任务标题
     */
    private String title;

    /**
     * 负责人ID
     */
    private Long managerUserId;

    /**
     * 负责人姓名
     */
    private String managerUserName;

    /**
     * 父级到子级的全路径
     */
    private String treePathIds;

    /**
     * 状态（0=未开始，1=进行中，2=已完成）
     *
     * @see com.gok.pboot.pms.enumeration.TaskWorkingState
     */
    private Integer state;

    /**
     * 状态文本描述
     *
     * @see com.gok.pboot.pms.enumeration.TaskWorkingState
     */
    private String stateText;

    /**
     * 计划开始时间
     */
    private LocalDateTime expectedStartTime;

    /**
     * 计划结束时间
     */
    private LocalDateTime expectedEndTime;

    /**
     * 实际开始时间
     */
    private LocalDateTime actualStartTime;

    /**
     * 实际结束时间
     */
    private LocalDateTime actualEndTime;

    /**
     * 子节点集合
     */
    private List<ProjectTaskPageVO> children;

    public static ProjectTaskPageVO of(ProjectTask request) {
        ProjectTaskPageVO result = new ProjectTaskPageVO();
        Integer state = request.getState();
        LocalDateTime actualStartTime = request.getActualStartTime();
        LocalDateTime actualEndTime = request.getActualEndTime();
        List<ProjectTaskPageVO> list = new ArrayList<>();

        result.setTaskId(request.getId());
        result.setParentId(request.getParentId());
        result.setProjectId(request.getProjectId());
        result.setTitle(request.getTitle());
        result.setManagerUserId(request.getManagerUserId());
        result.setManagerUserName(request.getManagerUserName());
        result.setState(state);
        result.setStateText(EnumUtils.getNameByValue(TaskWorkingState.class, state));
        result.setExpectedStartTime(request.getExpectedStartTime());
        result.setExpectedEndTime(request.getExpectedEndTime());
        result.setChildren(list);
        result.setTreePathIds(request.getTreePathIds());
        if (actualStartTime != null) {
            result.setActualStartTime(actualStartTime);
        }
        if (actualEndTime != null) {
            result.setActualEndTime(actualEndTime);
        }

        return result;
    }
}
