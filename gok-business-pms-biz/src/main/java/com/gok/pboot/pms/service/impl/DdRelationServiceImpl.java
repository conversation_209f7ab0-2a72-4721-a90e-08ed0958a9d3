package com.gok.pboot.pms.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.pboot.pms.entity.domain.DdRelation;
import com.gok.pboot.pms.enumeration.DdRelationTypeEnum;
import com.gok.pboot.pms.mapper.DdRelationMapper;
import com.gok.pboot.pms.service.IDdRelationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 滴滴关联表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Slf4j
@Service
public class DdRelationServiceImpl extends ServiceImpl<DdRelationMapper, DdRelation> implements IDdRelationService {

    @Override
    public boolean saveOrUpdateRelation(Long relateId, DdRelationTypeEnum relateType, String didiId) {
        try {
            // 先查询是否已存在关联关系
            DdRelation existingRelation = baseMapper.selectByRelateIdAndType(relateId, relateType.getValue());
            
            if (existingRelation != null) {
                // 更新现有关联关系
                existingRelation.setDidiId(didiId);
                existingRelation.setSyncTime(LocalDateTime.now());
                return updateById(existingRelation);
            } else {
                // 创建新的关联关系
                DdRelation newRelation = new DdRelation()
                        .setRelateId(relateId)
                        .setRelateType(relateType.getValue())
                        .setDidiId(didiId)
                        .setSyncTime(LocalDateTime.now());
                return save(newRelation);
            }
        } catch (Exception e) {
            log.error("保存或更新关联关系失败，关联ID: {}, 关联类型: {}, 滴滴ID: {}",
                    relateId, relateType.getName(), didiId, e);
            return false;
        }
    }


    @Override
    public DdRelation getByDidiIdAndType(String didiProjectId, DdRelationTypeEnum relateType) {
        return baseMapper.selectByDidiIdAndType(didiProjectId, relateType.getValue());
    }

    @Override
    public DdRelation getByRelateIdAndType(Long relateId, DdRelationTypeEnum relateType) {
        return baseMapper.selectByRelateIdAndType(relateId, relateType.getValue());
    }

    @Override
    public void updateSyncTime(Long relateId, DdRelationTypeEnum relateType) {
             baseMapper.updateSyncTimeByRelateIdAndType(relateId, relateType.getValue());

    }

}
