package com.gok.pboot.pms.cost.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CostBaselineQuotationDTO {

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 版本ID
     */
    private Long versionId;

    /**
     * 流程ID
     */
    private Long requestId;

    /**
     * 流程名称
     */
    private String requestName;

    /**
     * 审核状态（0=A表流程同步,1=未审核，2=未通过，3=已审核）
     */
    private Integer auditStatus;

    /**
     * 收入总额(含税)
     */
    private String incomeAmountIncludedTax;

    /**
     * 收入总额(不含税)
     */
    private String incomeAmountExcludingTax;

    /**
     * 预计毛利率(不含税)
     */
    private String expectedGrossProfitMargin;

    /**
     * 预计毛利(不含税)
     */
    private String expectedGrossProfit;

    /**
     * 预计提前投入成本(万元)
     */
    private String expectedEarlyInvestmentCost;

}