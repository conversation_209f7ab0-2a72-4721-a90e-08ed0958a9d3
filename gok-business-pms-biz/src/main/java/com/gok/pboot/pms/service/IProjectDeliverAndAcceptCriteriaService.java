package com.gok.pboot.pms.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.entity.domain.ProjectDeliverAndAcceptCriteria;

import java.util.List;

/**
 * 商机管理 服务类接口
 *
 * <AUTHOR>
 * @date 2023/11/21
 */
public interface IProjectDeliverAndAcceptCriteriaService extends IService<ProjectDeliverAndAcceptCriteria> {

    /**
     * 项目id查询
     * @param id
     */
    List<ProjectDeliverAndAcceptCriteria> getByProjectId(Long id);
}
