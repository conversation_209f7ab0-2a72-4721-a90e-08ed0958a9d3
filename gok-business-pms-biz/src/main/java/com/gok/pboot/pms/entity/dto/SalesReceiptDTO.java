package com.gok.pboot.pms.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 销售收款计划DTO
 * <AUTHOR>
 * @since 2023-09-27
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SalesReceiptDTO {

    /**
     * 当前页
     */
    @NotNull(message = "当前页条件缺失")
    private Long pageNumber;

    /**
     * 每页条数
     */
    @NotNull(message = "分页条数条件缺失")
    private Long pageSize;

    /**
     * 项目名称/编码
     */
    private String projectNameOrNo;

    /**
     * 合同名称/编码
     */
    private String contractNameOrNo;

    /**
     * 项目状态
     */
    private String projectStatus;

    /**
     * 最低合同金额
     */
    private BigDecimal contractMoneyLow;

    /**
     * 最高合同金额
     */
    private BigDecimal contractMoneyUp;

    /**
     * 款项名称
     */
    private String currentPaymentName;

    /**
     * 预警等级
     */
    private Integer warningLevel;

    /**
     * 销售负责
     */
    private String salesmanOrManageUserName;

    /**
     * 归属主体
     */
    private String attributableSubject;

    /**
     * 业务归属部门
     */
    private List<Long> deptIdList;

    /**
     * 客户端id
     */
    private Long clientId;

    /**
     * 菜单
     */
    private String menuCode;

    /**
     * 有权限的一级部门id（前端不需要传入）
     */
    private List<Long> authDeptIdList;

    /**
     * 权限设置用的用户Id（前端不需要传入）
     */
    private List<Long> userId;

    /**
     * 数据权限标识
     */
    private Boolean authority;

}
