package com.gok.pboot.pms.controller.feign;

import com.gok.base.admin.dto.SalesReceiptCollectionRecordsDTO;
import com.gok.pboot.common.security.annotation.Inner;
import com.gok.pboot.pms.common.base.R;
import com.gok.pboot.pms.service.ISalesReceiptCollectionRecordsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 销售回款跟踪外部调用
 *
 * <AUTHOR>
 * @since 2024-03-15
 * @menu 销售回款跟踪外部调用
 */
@Inner(false)
@RestController
@RequiredArgsConstructor
@Api(tags = "销售回款跟踪外部调用")
@RequestMapping("/inner/sales-receipt")
public class SalesReceiptClientController {

    private final ISalesReceiptCollectionRecordsService iSalesReceiptCollectionRecordsService;

    /**
     * 新增记录
     *
     * @param dto {@link SalesReceiptCollectionRecordsDTO}
     * @return {@link R}<{@link Boolean}>
     */
    @PostMapping("/save-records")
    @ApiOperation(value = "新增记录", notes = "新增记录")
    public R<Boolean> saveRecords(@RequestBody @Valid SalesReceiptCollectionRecordsDTO dto) {
        return iSalesReceiptCollectionRecordsService.saveRecords(dto)
                ? R.ok(Boolean.TRUE) : R.failed(Boolean.FALSE);
    }
}
