package com.gok.pboot.pms.cost.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class CostConfigAccountVO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * OA_ID
     */
    private Long oaId;

    /**
     * 版本ID
     */
    private Long versionId;

    /**
     * 版本名称
     */
    private String versionName;

    /**
     * 成本科目类别ID
     */
    private Long accountCategoryId;

    /**
     * 成本科目类别名称
     */
    private String accountCategoryName;

    /**
     * 科目名称
     */
    private String accountName;

    /**
     * 科目代码
     */
    private String accountCode;

    /**
     * 对应成本类型（0=人工成本，1=费用报销，2=外采费用）
     */
    private Integer accountType;

    /**
     * 对应成本类型名称
     */
    private String accountTypeName;

    /**
     * 是否被引用
     */
    private boolean cite;

    /**
     * 科目定义说明
     */
    private String accountDefinitionDesc;

    /**
     * 是否用于成本估算
     * {@link com.gok.pboot.pms.enumeration.YesOrNoEnum}
     */
    private Integer forEstimateFlag;

    private String forEstimateFlagName;

}