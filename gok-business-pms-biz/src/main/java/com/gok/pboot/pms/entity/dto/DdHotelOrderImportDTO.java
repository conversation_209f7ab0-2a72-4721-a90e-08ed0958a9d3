package com.gok.pboot.pms.entity.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 滴滴酒店订单导入DTO
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DdHotelOrderImportDTO {

    /**
     * 订单号
     */
    @ExcelProperty(value = "*订单号")
    private String orderNo;

    /**
     * 入住人工号
     */
    @ExcelProperty(value = "入住人工号")
    private String checkinEmployeeNo;

    /**
     * 入住人姓名
     */
    @ExcelProperty(value = "*入住人")
    private String checkinPersonName;

    /**
     * 入住人ID
     */
    @ExcelIgnore
    private Long checkinUserId;

    /**
     * 入住人部门ID
     */
    @ExcelIgnore
    private Long checkinDeptId;

    /**
     * 入住人部门名称
     */
    @ExcelIgnore
    private String checkinDeptName;

    /**
     * 城市名称
     */
    @ExcelProperty(value = "城市名称")
    private String cityName;

    /**
     * 酒店名称
     */
    @ExcelProperty(value = "酒店名称")
    private String hotelName;

    /**
     * 房间房型
     */
    @ExcelProperty(value = "房间房型")
    private String roomType;

    /**
     * 入住时间
     */
    @ExcelProperty(value = "入住时间")
    private LocalDate checkinTime;

    /**
     * 离店时间
     */
    @ExcelProperty(value = "离店时间")
    private LocalDate checkoutTime;

    /**
     * 企业实付金额
     */
    @ExcelProperty(value = "*企业实付")
    private BigDecimal companyActualPayment;

    /**
     * 服务费
     */
    @ExcelProperty(value = "服务费")
    private BigDecimal serviceFee;

    /**
     * 天数
     */
    @ExcelProperty(value = "天数")
    private BigDecimal numberOfDays;

    /**
     * 房间数
     */
    @ExcelProperty(value = "房间数")
    private BigDecimal numberOfRooms;

    /**
     * 间夜
     */
    @ExcelProperty(value = "间夜")
    private BigDecimal roomNights;

    /**
     * 单价
     */
    @ExcelProperty(value = "单价")
    private BigDecimal unitPrice;

    /**
     * 房间差标
     */
    @ExcelProperty(value = "房间差标")
    private BigDecimal roomStandardDifference;

    /**
     * 订单状态
     */
    @ExcelProperty(value = "订单状态")
    private String orderStatus;

    /**
     * 预订日期
     */
    @ExcelProperty(value = "*预订日期")
    private LocalDate bookingDate;

    /**
     * 预订人ID
     */
    @ExcelIgnore
    private Long bookingUserId;

    /**
     * 预订人部门ID
     */
    @ExcelIgnore
    private Long bookingDeptId;
    /**
     * 预订人工号
     */
    @ExcelProperty(value = "*预订人工号")
    private String bookingEmployeeNo;

    /**
     * 预订人姓名
     */
    @ExcelProperty(value = "预订人")
    private String bookingEmployeeName;

    /**
     * 出差申请单号
     */
    @ExcelProperty(value = "出差申请单号")
    private String businessTripApplicationNo;

    /**
     * 出差事由
     */
    @ExcelProperty(value = "出差事由")
    private String businessTripReason;

    /**
     * 成本中心/所属项目
     */
    @ExcelProperty(value = "*成本中心/所属项目")
    private String costCenterProject;

    @ExcelIgnore
    private Long companyId;
    /**
     * 所属公司
     */
    @ExcelProperty(value = "*所属公司")
    private String companyName;

    /**
     * 成本中心ID
     */
    @ExcelIgnore
    private Long costCenterId;

    /**
     * 成本中心名称
     */
    @ExcelIgnore
    private String costCenterName;

    /**
     * 所属项目ID
     */
    @ExcelIgnore
    private Long projectId;

    /**
     * 所属项目名称
     */
    @ExcelIgnore
    private String projectName;

    @ExcelIgnore
    private String projectCode;
} 