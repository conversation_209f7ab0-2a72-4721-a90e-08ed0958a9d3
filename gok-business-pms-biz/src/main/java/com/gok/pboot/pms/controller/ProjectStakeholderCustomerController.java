package com.gok.pboot.pms.controller;

import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.R;
import com.gok.pboot.pms.entity.dto.ProjectStakeholderCustomerDTO;
import com.gok.pboot.pms.entity.vo.ProjectStakeholderCustomerVO;
import com.gok.pboot.pms.service.IProjectStakeholderCustomerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;


/**
 * <AUTHOR>
 * @description 项目 前端控制器
 * @menu 项目干系人-客户干系人
 * @since 2023-07-11 17:05:26
 */
@RestController
@RequestMapping("/projectStakeholderCustomer")
@Api("项目客户")
public class ProjectStakeholderCustomerController {

    @Resource
    private IProjectStakeholderCustomerService projectStakeholderCustomerService;

    /**
     * 查询项目客户列表
     *
     * @param projectId 项目id
     * @return {@link ApiResult}<{@link List}<{@link ProjectStakeholderCustomerVO}>>
     */
    @ApiOperation(value = "查询项目客户列表", notes = "查询项目客户列表")
    @GetMapping("/list/{projectId}")
    public ApiResult<List<ProjectStakeholderCustomerVO>> list(@PathVariable("projectId") Long projectId) {
        return ApiResult.success(projectStakeholderCustomerService.getCustomerByProjectId(projectId));
    }

    /**
     * 保存or修改项目客户
     *
     * @param dto dto
     * @return {@link ApiResult}
     */
    @ApiOperation(value = "保存or修改项目客户", notes = "保存or修改项目客户")
    @PostMapping("/saveOrUpdate")
    public ApiResult<Boolean> save(@RequestBody @Validated ProjectStakeholderCustomerDTO dto) {
        return ApiResult.success(projectStakeholderCustomerService.saveOrUpdateCustomer(dto));
    }

    /**
     * 删除项目客户
     *
     * @param id 客户id
     * @return {@link R}
     */
    @ApiOperation(value = "删除项目客户", notes = "删除项目客户")
    @DeleteMapping("/delete/{id}")
    public ApiResult<Boolean> delete(@PathVariable("id") Long id) {
        return ApiResult.success(projectStakeholderCustomerService.deleteById(id));
    }

}
