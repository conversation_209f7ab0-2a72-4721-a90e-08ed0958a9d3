package com.gok.pboot.pms.eval.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.eval.entity.domain.EvalCommendationLetter;
import com.gok.pboot.pms.eval.entity.domain.EvalUserRole;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 项目评价表扬信Mapper
 *
 * <AUTHOR>
 * @date 2025/05/16
 */
@Mapper
public interface EvalCommendationLetterMapper extends BaseMapper<EvalCommendationLetter> {

    /**
     * 根据项目ID获取表扬信
     *
     * @return 项目ID
     */
    EvalCommendationLetter getByProjectId(@Param("projectId") Long projectId);

} 