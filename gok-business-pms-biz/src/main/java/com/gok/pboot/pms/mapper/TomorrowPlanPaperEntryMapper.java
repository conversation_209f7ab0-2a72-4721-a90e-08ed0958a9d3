package com.gok.pboot.pms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.entity.domain.TomorrowPlanPaperEntry;
import com.gok.pboot.pms.entity.dto.SubordinatesDailyPaperDTO;
import com.gok.pboot.pms.entity.vo.DailyPaperEntryVO;
import com.gok.pboot.pms.entity.vo.DailyReviewProjectAuditPageVO;
import com.gok.pboot.pms.entity.vo.TomorrowPlanPaperVo;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023/2/7
 */
@Mapper
public interface TomorrowPlanPaperEntryMapper extends BaseMapper<TomorrowPlanPaperEntry> {

    /**
     * ~ 批量插入 ~
     *
     * @param poList 实体列表
     * <AUTHOR>
     * @date 2022/8/24 14:16
     */
    void batchSave(@Param("poList") List<TomorrowPlanPaperEntry> poList);

    /**
     * 批量更新
     *
     * @param poList
     */
    void batchUpdate(@Param("poList") List<TomorrowPlanPaperEntry> poList);

    /**
     * id查看详情
     *
     * @param id
     * @return
     */
    List<TomorrowPlanPaperEntry> findByDailyPaperId(Long id);

    /**
     * @create by yzs at 2023/5/11
     * @description:时间、用户id集合，项目名称，用户名称条件查询
     * @param: dto 实体条件参数
     * @return: java.util.List<com.gok.pboot.pms.entity.domain.TomorrowPlanPaperEntry>
     */
    List<TomorrowPlanPaperEntry> findByFilter(@Param("filter") SubordinatesDailyPaperDTO dto);

    /**
     * 根据项目IDs查询昨日计划
     * 包含已审、未审的日报数据
     *
     * @return
     */
    @MapKey("id")
    @Deprecated
    Map<Long, DailyReviewProjectAuditPageVO> getYesterdayPlanByIds(@Param("voList") List<DailyPaperEntryVO> voList);

    /**
     * 根据提交日期列表查询明日计划Map
     * @param submissionDates 提交日期列表
     * @param userIds 日报ID列表
     * @return 明日计划列表
     */
    List<TomorrowPlanPaperVo> findBySubmissionDatesAndUserIds(
            @Param("submissionDates") Collection<LocalDate> submissionDates,
            @Param("userIds") Collection<Long> userIds
    );

    /**
     * 根据提交日期范围查询明日计划Map
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param userIds 日报ID列表
     * @return 明日计划列表
     */
    List<TomorrowPlanPaperEntry> findBySubmissionDateRangeAndUserIds(
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate,
            @Param("userIds") Collection<Long> userIds
    );

    /**
     * 更新任务名称
     *
     * @param taskId    任务id
     * @param taskName  任务名称
     * @return int
     */
    int updateTaskNameByTaskId(@Param("taskId") Long taskId, @Param("taskName") String taskName);

}
