package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 发起状态枚举
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@AllArgsConstructor
@Getter
public enum InitiationStatusEnum {

    /**
     * 待发起
     */
    PENDING(0, "待发起"),

    /**
     * 已发起
     */
    INITIATED(1, "已发起"),

    /**
     * 发起异常-OA接口报错
     */
    OA_ERROR(2, "发起异常-OA接口报错"),

    /**
     * 发起异常-预算不足
     */
    BUDGET_INSUFFICIENT(3, "发起异常-预算不足");

    private final Integer value;
    private final String name;

    /**
     * 判断是否可以发起报销
     *
     * @param status 状态值
     * @return 是否可以发起
     */
    public static boolean canInitiate(Integer status) {
        return PENDING.getValue().equals(status) 
            || OA_ERROR.getValue().equals(status) 
            || BUDGET_INSUFFICIENT.getValue().equals(status);
    }

    /**
     * 根据值获取枚举
     *
     * @param value 状态值
     * @return 枚举
     */
    public static InitiationStatusEnum getByValue(Integer value) {
        for (InitiationStatusEnum status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }
} 