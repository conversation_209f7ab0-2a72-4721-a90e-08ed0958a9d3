package com.gok.pboot.pms.entity.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @description 工时明细详情
 * @since 2024/7/18
 */
@Data
public class WorkHoursDetailVO {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户姓名
     */
    private String userRealName;

    /**
     * 任务id
     */
    private Long taskId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 工时类型（0=售前支撑，1=售后交付）
     */
    private Integer workType;

    /**
     * 工时类型描述
     */
    private String workTypeTxt;

    /**
     * 填报日期
     */
    private LocalDate submissionDate;

    /**
     * 日期+星期
     */
    private String submissionDateFormatted;

    /**
     * 假期类型(0-普通休息日 1-法定节假日 null-工作日)
     */
    private Integer holidayType;

    /**
     * 今日工作内容
     */
    private String todayJob;

    /**
     * 昨日计划
     */
    private String yesterdayPlan;

    /**
     * 日报ID
     */
    private Long dailyPaperId;

    /**
     * 正常工时
     */
    private BigDecimal normalHours;

    /**
     * 加班工时
     */
    private BigDecimal addedHours;

    /**
     * 工作日加班工时
     */
    private BigDecimal workOvertimeHours;

    /**
     * 休息日加班工时
     */
    private BigDecimal restOvertimeHours;

    /**
     * 节假日加班工时
     */
    private BigDecimal holidayOvertimeHours;

    /**
     * 人员类别
     */
    private Integer employeeType;

    /**
     * 人员类别描述
     */
    private String employeeTypeTxt;

    /**
     * 审核状态
     */
    private String approvalStatus;
}
