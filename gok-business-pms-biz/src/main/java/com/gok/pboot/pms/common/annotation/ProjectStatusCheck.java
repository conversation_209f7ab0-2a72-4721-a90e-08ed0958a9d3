package com.gok.pboot.pms.common.annotation;

import java.lang.annotation.*;

/**
 * 项目状态检查注解
 * <p>
 * 仅支持一对多映射关系, 即一个项目对应多个用户或 一个用户对应多个项目
 *
 * <AUTHOR> Assistant
 * @since 2025-07-01
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ProjectStatusCheck {

    /**
     * 项目ID获取表达式
     * <p>
     * 支持两种方式：
     * 1. SpEL表达式（推荐）：
     * - "#dto.projectId" - 从参数dto中获取projectId
     * - "#projectId" - 直接从名为projectId的参数获取
     * - "#tasks[0].projectId" - 从集合tasks中第一个task提取projectId
     * - "#tasks.![projectId]" - 从集合tasks中提取所有projectId
     * 2. 传统字段名：
     * - "projectId" - 查找名为projectId的字段(支持嵌套对象)
     * </p>
     * 默认为 "projectId"
     */
    String projectIdField() default "projectId";

    /**
     * 用户ID获取表达式
     * <p>
     * 支持两种方式：
     * 1. SpEL表达式（推荐）：
     * - "#dto.userId" - 从参数dto中获取userId
     * - "#userId" - 直接从名为userId的参数获取
     * - "#tasks[0].userId" - 从集合tasks中第一个task提取userId
     * - "#tasks.![userId]" - 从集合tasks中提取所有userId
     * 2. 传统字段名：
     * - "userId" - 查找名为userId的字段(支持嵌套对象)
     * </p>
     * 默认为 "userId"
     */
    String userIdField() default "userId";

    /**
     * 项目处于关闭、商机终止、异常终止 状态时的错误提示
     */
    String disableMessage() default "不允许报工和任何费用支出~";

    /**
     * 项目处于挂起状态的错误提示
     *
     * @return {@link String }
     */
    String suspendMessage() default "仅允许项目经理进行报工和费用支出~";
}
