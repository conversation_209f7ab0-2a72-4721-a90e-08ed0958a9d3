package com.gok.pboot.pms.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gok.pboot.pms.common.base.BeanEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * 项目流程信息表（数仓同步)
 *
 * <AUTHOR>
 * @since 2023-07-17
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(ProjectRisk.ALIAS)
public class ProjectProcessInfo extends BeanEntity<Long> {

    public static final String ALIAS = "project_process_info";

    /**
     * oa流程id
     */
    private Long requestId;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 申请人id
     */
    private Long applicatId;

    /**
     * 申请人
     */
    private String applicat;

    /**
     * 流程类型
     */
    private String processType;

    /**
     * 流程状态
     */
    private String status;

    /**
     * 流程名
     */
    private String name;

    /**
     * 归档时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp filingDateTime;

    /**
     * 当前节点名称
     */
    private String currentNodeName;

}
