package com.gok.pboot.pms.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 客户沟通情况
 *
 * <AUTHOR>
 * @date 2023/11/20
 */
@Data
@TableName("customer_communication_record")
@EqualsAndHashCode(callSuper = true)
public class CustomerCommunicationRecord extends BeanEntity<Long> {

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 客户分级
     */
    private String customerGrade;

    /**
     * 交流方式
     */
    private String communicationMethod;

    /**
     * 交流时间
     */
    private String communicationTime;

    /**
     * 时间段
     */
    private String timeSlot;

    /**
     * 交流主题及目标
     */
    private String themeAndObjectives;

    /**
     * 交流内容情况
     */
    private String contentSituation;

    /**
     * 客户反馈及要求
     */
    private String feedbackAndRequirements;

    /**
     * 取得的关键进展
     */
    private String keyProgressMade;

    /**
     * 下一步计划
     */
    private String nextStepForwardPlan;

    /**
     * 客户经理ID
     */
    private Long accountManagerId;

    /**
     * 客户经理
     */
    private String accountManager;

    /**
     * 对方参与人
     */
    private String otherParticipants;

    /**
     * 我方参与人
     */
    private String ourParticipants;

    /**
     * 提交人ID
     */
    private Long submitterId;

    /**
     * 提交人
     */
    private String submitter;

    /**
     * 提交时间
     */
    private LocalDateTime submissionTime;

}
