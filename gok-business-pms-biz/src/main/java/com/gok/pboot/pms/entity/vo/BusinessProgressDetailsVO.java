package com.gok.pboot.pms.entity.vo;

import com.gok.pboot.pms.entity.domain.ProjectFile;
import com.google.common.collect.ImmutableList;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;

import java.util.List;

/**
 * 商机进展详情vo
 *
 * <AUTHOR>
 * @date 2023/11/24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BusinessProgressDetailsVO extends BusinessProgressVO {

    /**
     * 签约客户分级
     */
    private String customerGrade;

    /**
     * 招标方式
     */
    private String biddingMethod;

    /**
     * 结算方式
     */
    private String settlementMethod;

    /**
     * 交付形式
     */
    private String deliveryMethod;

    /**
     * 关键决策链
     */
    private String keyDecisionChain;

    /**
     * 项目需求
     */
    private String projectRequirement;

    /**
     * 预估毛利率
     */
    private String ygmll;

    /**
     * 竞争情况
     */
    private String competitionSituation;

    /**
     * 售前人员姓名(售前经理姓名)
     */
    private String preSaleUserName;

    /**
     * 项目经理人员姓名
     */
    private String managerUserName;

    /**
     * 附件列表
     */
    private List<ProjectFile> files;


    /**
     * 对象构造
     *
     * @param businessInfo 项目台账vo
     * @param projectFiles 项目附件列表
     */
    public void of(BusinessInfoVO businessInfo, List<ProjectFile> projectFiles) {
        // 添加基本商机信息
        if(ObjectUtils.isNotEmpty(businessInfo)){
            this.setCustomerGrade(businessInfo.getEndCustomerGrade());
            this.setBiddingMethod(businessInfo.getBiddingMethod());
            this.setSettlementMethod(businessInfo.getSettlementMethod());
            this.setDeliveryMethod(businessInfo.getDeliveryMethod());
            this.setKeyDecisionChain(businessInfo.getKeyDecisionChain());
            this.setProjectRequirement(businessInfo.getProjectRequirement());
            this.setYgmll(businessInfo.getYgmll());
            this.setCompetitionSituation(businessInfo.getCompetitionSituation());
            this.setPreSaleUserName(businessInfo.getPreSaleUserName());
            this.setManagerUserName(businessInfo.getManagerUserName());
        }
        // 添加文件列表
        if (CollectionUtils.isNotEmpty(projectFiles)) {
            this.setFiles(projectFiles);
        } else {
            this.setFiles(ImmutableList.of());
        }
    }
}
