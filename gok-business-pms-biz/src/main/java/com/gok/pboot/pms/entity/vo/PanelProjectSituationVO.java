package com.gok.pboot.pms.entity.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.gok.pboot.pms.common.serializer.TwoDecimalToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @desc 个人面板-项目情况VO
 * @createTime 2023/2/21 14:33
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@ApiModel("个人面板-项目情况VO")
public class PanelProjectSituationVO implements Serializable {
    /**
     * 项目id
     */
    @ExcelIgnore
    @ApiModelProperty(value = "项目id")
    private Long projectId;
    /**
     * 项目名称
     */
    @ExcelProperty("项目名称")
    @ColumnWidth(value = 31)
    @ApiModelProperty(value = "项目名称")
    private String projectName;
    /**
     * 开始时间
     */
    @ExcelProperty("开始时间")
    @ColumnWidth(value = 11)
    @ApiModelProperty(value = "开始时间")
    private LocalDate startTime;
    /**
     * 截止时间
     */
    @ExcelProperty("截止时间")
    @ColumnWidth(value = 11)
    @ApiModelProperty(value = "截止时间")
    private LocalDate endTime;
    /**
     * 正常工时（人天）
     */
    @ExcelProperty("正常工时（天）")
    @ColumnWidth(value = 22)
    @ApiModelProperty(value = "正常工时（人天）")
    @JsonSerialize(using = TwoDecimalToStringSerializer.class)
    @ContentStyle(dataFormat = 2)
    private BigDecimal normalHours;

    /**
     * 总加班工时（人天）
     */
    @ExcelProperty("总加班工时（天）")
    @ColumnWidth(value = 22)
    @ApiModelProperty(value = "总加班工时（天）")
    @JsonSerialize(using = TwoDecimalToStringSerializer.class)
    @ContentStyle(dataFormat = 2)
    private BigDecimal addedHours;


    /**
     * '工作日加班工时'
     */
    @ExcelProperty("工作日加班工时（天）")
    @ColumnWidth(value = 22)
    @ApiModelProperty(value = "工作日加班工时（天）")
    @JsonSerialize(using = TwoDecimalToStringSerializer.class)
    @ContentStyle(dataFormat = 2)
    private BigDecimal workOvertimeHours;

    /**
     * '休息日加班工时'
     */
    @ExcelProperty("休息日加班工时（天）")
    @ColumnWidth(value = 22)
    @ApiModelProperty(value = "休息日加班工时（天）")
    @JsonSerialize(using = TwoDecimalToStringSerializer.class)
    @ContentStyle(dataFormat = 2)
    private BigDecimal restOvertimeHours;
    /**
     * '节假日加班工时'
     */
    @ExcelProperty("节假日加班工时（天）")
    @ColumnWidth(value = 22)
    @ApiModelProperty(value = "节假日加班工时（天）")
    @JsonSerialize(using = TwoDecimalToStringSerializer.class)
    @ContentStyle(dataFormat = 2)
    private BigDecimal holidayOvertimeHours;

    /**
     * '调休工时'
     */
    @ExcelProperty("调休工时（天）")
    @ColumnWidth(value = 22)
    @ApiModelProperty(value = "调休工时（天）")
    @JsonSerialize(using = TwoDecimalToStringSerializer.class)
    @ContentStyle(dataFormat = 2)
    private BigDecimal ompensatoryHours;
    /**
     * 项目分摊工时（人天）
     */
    @ExcelProperty("项目分摊工时（天）")
    @ColumnWidth(value = 30)
    @ApiModelProperty(value = "项目分摊工时（天）")
    @JsonSerialize(using = TwoDecimalToStringSerializer.class)
    @ContentStyle(dataFormat = 2)
    private BigDecimal projectHours;
}
