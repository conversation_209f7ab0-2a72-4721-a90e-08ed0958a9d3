package com.gok.pboot.pms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.entity.domain.ProjectOperationConfirmation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2025/07/02
 **/
@Mapper
public interface ProjectOperationConfirmationMapper extends BaseMapper<ProjectOperationConfirmation> {

    /**
     * 获取项目对应角色ID集合
     *
     * @param projectId
     * @return
     */
    Map<String, Long> getProjectRoleIdMap(@Param("projectId") Long projectId);

}
