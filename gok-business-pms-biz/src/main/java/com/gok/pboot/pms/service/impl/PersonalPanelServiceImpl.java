package com.gok.pboot.pms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.Util.CollectionUtils;
import com.gok.pboot.pms.Util.CommonUtils;
import com.gok.pboot.pms.Util.DailyPaperDateUtils;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.entity.CompensatoryLeaveData;
import com.gok.pboot.pms.entity.Holiday;
import com.gok.pboot.pms.entity.dto.PanelRequestDTO;
import com.gok.pboot.pms.entity.vo.PanelProjectSituationAnalysisVO;
import com.gok.pboot.pms.entity.vo.PanelProjectSituationDetailsExportVO;
import com.gok.pboot.pms.entity.vo.PanelProjectSituationDetailsVO;
import com.gok.pboot.pms.entity.vo.PanelProjectSituationVO;
import com.gok.pboot.pms.mapper.CompensatoryLeaveDataMapper;
import com.gok.pboot.pms.mapper.DailyPaperEntryMapper;
import com.gok.pboot.pms.mapper.HolidayMapper;
import com.gok.pboot.pms.service.ICompensatoryLeaveDataService;
import com.gok.pboot.pms.service.PersonalPanelService;
import com.gok.pboot.pms.service.processor.SaturationCalculator;
import com.google.common.collect.ImmutableList;
import lombok.RequiredArgsConstructor;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc
 * @createTime 2023/2/21 15:19
 */
@Service
@RequiredArgsConstructor
public class PersonalPanelServiceImpl implements PersonalPanelService {
    private final DailyPaperEntryMapper dailyPaperEntryMapper;
    private final SaturationCalculator saturationCalculator;

    private final ICompensatoryLeaveDataService compensatoryLeaveDataService;
    private final CompensatoryLeaveDataMapper compensatoryLeaveDataMapper;
    private final HolidayMapper holidayMapper;

    @Override
    public ApiResult<Page<PanelProjectSituationVO>> page(PanelRequestDTO filter) {
        // 根据时间范围查询当前登录用户已审核通过的项目工时数据
        filter.setUserId(SecurityUtils.getUser().getId());
        Page<PanelProjectSituationVO> page = dailyPaperEntryMapper.selectPageByUserId(filter, Page.of(filter.getPageNumber(), filter.getPageSize()));
        Page<PanelProjectSituationVO> page1=compensatoryLeaveDataMapper.selectPanelProjectPage(filter, Page.of(filter.getPageNumber(), filter.getPageSize()));
        if(CollUtil.isEmpty(page.getRecords())){
            page=page1;
        }else{
            List<PanelProjectSituationVO> records = page.getRecords();
            List<Long> projectIds = records.stream().map(r -> r.getProjectId()).collect(Collectors.toList());
            List<PanelProjectSituationVO> records1 = page1.getRecords();
            for (PanelProjectSituationVO v : records1 ) {
                if (!projectIds.contains(v.getProjectId())){
                    records.add(v);
                }
            }
            page.setRecords(records);
        }
        //项目调休数据
        Map<Long, BigDecimal> xmmcTimeMap = compensatoryLeaveDataService.getCompensatoryLeaveDataMap(filter);

        List<PanelProjectSituationVO> records = page.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return ApiResult.success(new Page<>());
        } else {
            // 将正常工时、加班工时换算成（人天）
            records.forEach(record -> {
                record.setNormalHours(CommonUtils.unitConversion(record.getNormalHours()));
                record.setWorkOvertimeHours(CommonUtils.unitConversion(record.getWorkOvertimeHours()));
                record.setRestOvertimeHours(CommonUtils.unitConversion(record.getRestOvertimeHours()));
                record.setHolidayOvertimeHours(CommonUtils.unitConversion(record.getHolidayOvertimeHours()));
                record.setAddedHours(CommonUtils.unitConversion(record.getAddedHours()));
                record.setOmpensatoryHours(CommonUtils.unitConversion(xmmcTimeMap.get(record.getProjectId())));

                //「项目分摊工时」字段=正常工时+调休工时+休息日加班+法定节假日加班；
                record.setProjectHours(record.getNormalHours()
                        .add(record.getOmpensatoryHours())
                        .add(record.getRestOvertimeHours())
                        .add(record.getHolidayOvertimeHours()));
            });
        }

        return ApiResult.success(page);
    }



    @Override
    public List<PanelProjectSituationVO> export(PanelRequestDTO filter) {
        filter.setPageSize(Integer.MAX_VALUE);
        ApiResult<Page<PanelProjectSituationVO>> page = page(filter);
        if (!Optional.ofNullable(page).isPresent()
                || !Optional.ofNullable(page.getData()).isPresent()
                || CollUtil.isEmpty(page.getData().getRecords())) {
            return Arrays.asList(new PanelProjectSituationVO());
        }
        return page.getData().getRecords();
    }


    @Override
    public ApiResult<Page<PanelProjectSituationDetailsVO>> detailPage(PanelRequestDTO filter) {
        filter.setUserId(SecurityUtils.getUser().getId());
        Page<PanelProjectSituationDetailsVO> page = dailyPaperEntryMapper.selectPanelProjectSituationDetailsPage(filter, Page.of(filter.getPageNumber(), filter.getPageSize()));
        List<PanelProjectSituationDetailsVO> records = page.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return ApiResult.success(new Page<>());
        }
        List<Holiday> holidayList = holidayMapper.findHolidayList();
        LinkedHashMap<LocalDate, Integer>  holidayMap
                = holidayList.stream()
                .filter(h-> h.getHolidayType()!=null)
                .collect(Collectors.toMap(Holiday::getDayDate, Holiday::getHolidayType, (v1, v2) -> v1, LinkedHashMap::new));

        records.forEach(record -> {
            record.setNormalHours(CommonUtils.unitConversion(record.getNormalHours()));
            record.setAddedHours(CommonUtils.unitConversion(record.getAddedHours()));
            record.setSubmissionDateFormatted(DailyPaperDateUtils.asDateString(record.getSubmissionDate()));
            record.setHolidayType(holidayMap.get(record.getSubmissionDate()));
        });

        return ApiResult.success(page);
    }

    @Override
    public List<PanelProjectSituationDetailsExportVO> detailExport(PanelRequestDTO filter) {
        filter.setPageSize(Integer.MAX_VALUE);
        List<PanelProjectSituationDetailsVO> records = detailPage(filter).getData().getRecords();
        List<PanelProjectSituationDetailsExportVO> exportList
                = BeanUtil.copyToList(records, PanelProjectSituationDetailsExportVO.class);
        exportList.stream().forEach(r->{
            if(r.getHolidayType()!=null&&r.getHolidayType()==0){
                r.setSubmissionDateFormatted(r.getSubmissionDateFormatted()+"休");
            }else if(r.getHolidayType()!=null&&r.getHolidayType()==1){
                r.setSubmissionDateFormatted(r.getSubmissionDateFormatted()+"节");
            }
        });
        return exportList;
    }

    @Override
    public ApiResult<Page<PanelProjectSituationAnalysisVO>> analysisPage(PanelRequestDTO filter) {
        Long userId = SecurityUtils.getUser().getId();
        filter.setUserId(userId);
        Page<PanelProjectSituationAnalysisVO> page = dailyPaperEntryMapper.selectPageProjectSituationAnalysis(filter, Page.of(filter.getPageNumber(), filter.getPageSize()));
        List<PanelProjectSituationAnalysisVO> records = page.getRecords();
        Map<Long, Pair<BigDecimal, BigDecimal>> userAttendanceMap = saturationCalculator.calcAttendance(
                filter.getStartTime(), filter.getEndTime(), ImmutableList.of(userId)
        );
        Pair<BigDecimal, BigDecimal> totalAttendanceDaysAndSalaryAttendanceDays =
                userAttendanceMap.getOrDefault(userId, Pair.of(BigDecimal.ZERO, BigDecimal.ZERO));

        //项目调休数据
        Map<Long, BigDecimal> xmmcTimeMap = compensatoryLeaveDataService.getCompensatoryLeaveDataMap(filter);

        // 换算为人天，并计算项目耗用工时
        for (PanelProjectSituationAnalysisVO record : records) {
            record.setNormalHours(CommonUtils.unitConversion(record.getNormalHours()));
            record.setWorkOvertimeHours(CommonUtils.unitConversion(record.getWorkOvertimeHours()));
            record.setRestOvertimeHours(CommonUtils.unitConversion(record.getRestOvertimeHours()));
            record.setHolidayOvertimeHours(CommonUtils.unitConversion(record.getHolidayOvertimeHours()));
            record.setAddedHours(CommonUtils.unitConversion(record.getAddedHours()));
            record.setOmpensatoryHours(CommonUtils.unitConversion(xmmcTimeMap.get(record.getProjectId())));
            //「项目分摊工时」字段=正常工时+调休工时+休息日加班+法定节假日加班；
            record.setProjectHours(record.getNormalHours()
                    .add(record.getOmpensatoryHours())
                    .add(record.getRestOvertimeHours())
                    .add(record.getHolidayOvertimeHours()));

            // 实际出勤天数
            record.setAttendanceDays(totalAttendanceDaysAndSalaryAttendanceDays.getFirst());
            // EHR工资核算出勤天数
            record.setSalaryAttendanceDays(totalAttendanceDaysAndSalaryAttendanceDays.getSecond());

            // 计算工时饱和度  =总工时（包含所有加班，不含调休）/实际出勤天数
            BigDecimal allHours=record.getNormalHours()
                    .add(record.getAddedHours());
            record.setHourSaturation(allHours.divide(record.getAttendanceDays(), 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)));
        }

        return ApiResult.success(page);
    }


    /**
     * 获得当前用户项目调休天数
     * @param filter 条件
     * @return
     */
    private BigDecimal getOmpensatoryHours(PanelRequestDTO filter) {

        //项目调休数据
        List<CompensatoryLeaveData> compensatoryLeaveDataList =
                compensatoryLeaveDataService.getCompensatoryLeaveDataListByUserId(
                        filter.getStartTime(),
                        filter.getEndTime(),
                        filter.getUserId(),"3");

        BigDecimal ompensatoryHours=BigDecimal.ZERO;
        if(CollUtil.isNotEmpty(compensatoryLeaveDataList)){
            ompensatoryHours = compensatoryLeaveDataList.stream()
                    .filter(d -> d.getXmmc() != null)
                    .map(CompensatoryLeaveData::getHourData)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        return ompensatoryHours;
    }

    @Override
    public ApiResult<PanelProjectSituationAnalysisVO> analysisTotal(PanelRequestDTO filter) {
        Long userId = SecurityUtils.getUser().getId();
        filter.setUserId(userId);
        PanelProjectSituationAnalysisVO vo = dailyPaperEntryMapper.selectProjectSituationAnalysis(filter);
        Map<Long, Pair<BigDecimal, BigDecimal>> userAttendanceMap;

        // 调休工时
        BigDecimal ompensatoryHours = getOmpensatoryHours(filter);
        if (ObjectUtil.isNotNull(vo)) {
            userAttendanceMap = saturationCalculator.calcAttendance(
                    filter.getStartTime(),
                    filter.getEndTime(),
                    ImmutableList.of(userId)
            );
           //实际出勤天数：取EHR应出勤天数-请假天数（业务一体化同步oa的请假数据）
            Pair<BigDecimal, BigDecimal> totalAttendanceDaysAndSalaryAttendanceDays =
                    userAttendanceMap.getOrDefault(userId, Pair.of(BigDecimal.ZERO, BigDecimal.ZERO));
            vo.setNormalHours(CommonUtils.unitConversion(vo.getNormalHours()));
            vo.setAddedHours(CommonUtils.unitConversion(vo.getAddedHours()));
            vo.setWorkOvertimeHours(CommonUtils.unitConversion(vo.getWorkOvertimeHours()));
            vo.setRestOvertimeHours(CommonUtils.unitConversion(vo.getRestOvertimeHours()));
            vo.setHolidayOvertimeHours(CommonUtils.unitConversion(vo.getHolidayOvertimeHours()));
            vo.setOmpensatoryHours(CommonUtils.unitConversion(ompensatoryHours));
            //「项目分摊工时」字段=正常工时+调休工时+休息日加班+法定节假日加班；
            vo.setProjectHours(vo.getNormalHours()
                    .add(vo.getOmpensatoryHours())
                    .add(vo.getRestOvertimeHours())
                    .add(vo.getHolidayOvertimeHours()));
            // 计算实际出勤天数
            vo.setAttendanceDays(totalAttendanceDaysAndSalaryAttendanceDays.getFirst());
            vo.setSalaryAttendanceDays(totalAttendanceDaysAndSalaryAttendanceDays.getSecond());

            // 计算工时饱和度
            BigDecimal allHours=vo.getNormalHours()
                    .add(vo.getAddedHours());
            vo.setHourSaturation(allHours.divide(vo.getAttendanceDays(), 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)));
        }else{
            vo=new PanelProjectSituationAnalysisVO();
            if((ompensatoryHours!=null&&!ompensatoryHours.equals(BigDecimal.ZERO))){
                vo.setOmpensatoryHours(CommonUtils.unitConversion(ompensatoryHours));
            }
        }
        return ApiResult.success(vo);
    }
}
