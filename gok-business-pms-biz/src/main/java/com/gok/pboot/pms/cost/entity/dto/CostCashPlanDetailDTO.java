package com.gok.pboot.pms.cost.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 现金流计划明细DTO
 *
 * <AUTHOR> generated
 * @date 2024-03-19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CostCashPlanDetailDTO {
    /**
     * 时间
     */
    @NotNull(message = "时间不能为空")
    private Integer timeMonth;

    /**
     * 计划月份
     */
    @NotNull(message = "计划月份不能为空")
    private String planMonth;

    /**
     * 当月回款
     */
    private BigDecimal monthIncome;

    /**
     * 人工成本
     */
    private BigDecimal laborCost;

    /**
     * 费用报销
     */
    private BigDecimal expenseCost;

    /**
     * 外采支出
     */
    private BigDecimal outsourcingCost;

}