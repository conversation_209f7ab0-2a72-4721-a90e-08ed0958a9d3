package com.gok.pboot.pms.eval.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.eval.entity.domain.EvalProjectDetail;
import com.gok.pboot.pms.eval.entity.domain.EvalProjectOverview;
import com.gok.pboot.pms.eval.entity.vo.EvalProjectDetailVO;
import com.gok.pboot.pms.eval.entity.vo.EvalProjectOverviewVO;
import com.gok.pboot.pms.eval.enums.AssessmentProjectEnum;
import com.gok.pboot.pms.eval.enums.EvalIndexTypeEnum;
import com.gok.pboot.pms.eval.enums.EvalStatusEnum;
import com.gok.pboot.pms.eval.mapper.EvalProjectDetailMapper;
import com.gok.pboot.pms.eval.service.IEvalProjectDetailService;
import com.google.common.collect.ImmutableMap;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 项目评价表服务实现类
 *
 * <AUTHOR>
 * @create 2025/05/08
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class EvalProjectDetailServiceImpl extends ServiceImpl<EvalProjectDetailMapper, EvalProjectDetail> implements IEvalProjectDetailService {

    @Override
    @Transactional
    public List<Long> autoSaveEvalProjectDetail(List<EvalProjectOverview> evalProjectOverviews) {
        if (CollUtil.isEmpty(evalProjectOverviews)) {
            log.info("项目整体评价集合为空，操作结束！");
            return ListUtil.empty();
        }

        List<EvalProjectDetail> saveEntries = new ArrayList<>();
        evalProjectOverviews.forEach(e -> {
            // 封装成本指标
            saveEntries.addAll(assembleCostIndex(e));
            // 封装进度指标
            saveEntries.addAll(assembleProcessIndex(e));
            // 封装质量指标
            saveEntries.addAll(assembleQualityIndex(e));
        });

        if (CollUtil.isNotEmpty(saveEntries)) {
            saveEntries.forEach(e -> BaseBuildEntityUtil.buildInsertNoUser(e, "admin"));
            this.saveBatch(saveEntries);
        }
        return saveEntries.stream().map(EvalProjectDetail::getId).collect(Collectors.toList());
    }


    @Override
    public Map<Long, List<EvalProjectDetailVO>> findEvalProjectDetailVOList(List<EvalProjectOverviewVO> overviewVOList) {
        if (CollUtil.isEmpty(overviewVOList)) {
            return ImmutableMap.of();
        }

        // 获取所有项目评价ID
        List<Long> overviewIds = overviewVOList.stream()
                .map(EvalProjectOverviewVO::getId)
                .collect(Collectors.toList());

        // 查询项目评价详情
        LambdaQueryWrapper<EvalProjectDetail> queryWrapper = Wrappers.<EvalProjectDetail>lambdaQuery()
                .in(EvalProjectDetail::getOverviewId, overviewIds)
                .eq(EvalProjectDetail::getDelFlag, 0);
        List<EvalProjectDetail> detailList = this.list(queryWrapper);

        if (CollUtil.isEmpty(detailList)) {
            return ImmutableMap.of();
        }

        // 转换为VO并计算得分
        Map<Long, EvalProjectOverviewVO> projectOverviewMap = overviewVOList.stream()
                .collect(Collectors.toMap(EvalProjectOverviewVO::getId, v -> v));

        Map<Long, List<EvalProjectDetailVO>> resultMap = new HashMap<>();

        // 按项目整体评价ID分组 key-overviewId value-详情VO集合
        Map<Long, List<EvalProjectDetail>> groupedDetails = detailList.stream()
                .collect(Collectors.groupingBy(EvalProjectDetail::getOverviewId));

        for (Map.Entry<Long, List<EvalProjectDetail>> entry : groupedDetails.entrySet()) {
            Long overviewId = entry.getKey();
            List<EvalProjectDetail> details = entry.getValue();
            EvalProjectOverviewVO overviewVO = projectOverviewMap.get(overviewId);

            if (overviewVO == null) {
                continue;
            }

            List<EvalProjectDetailVO> detailVOList = new ArrayList<>();

            if (EvalStatusEnum.ARCHIVE.getValue().equals(overviewVO.getEvalStatus())) {
                // 已归档数据直接查询得分
                detailVOList = details.stream().map(EvalProjectDetailVO::convertToVO).collect(Collectors.toList());
            } else {
                // 未归档数据计算得出得分
                for (EvalProjectDetail detail : details) {
                    EvalProjectDetailVO detailVO = EvalProjectDetailVO.convertToVO(detail);

                    // 计算各项得分
                    BigDecimal score = calculateScore(detail, overviewVO);
                    detailVO.setScore(Optional.ofNullable(score).orElse(detail.getScore()));
                    detailVOList.add(detailVO);
                }
            }
            overviewVO.setEvalProjectDetailList(detailVOList);
            resultMap.put(overviewId, detailVOList);
        }

        return resultMap;
    }

    /**
     * 组装成本指标集合
     *
     * @param evalProjectOverview
     * @return
     */
    private List<EvalProjectDetail> assembleCostIndex(EvalProjectOverview evalProjectOverview) {
        List<EvalProjectDetail> costIndexList = new ArrayList<>(5);
        if (null == evalProjectOverview) {
            return costIndexList;
        }

        EvalProjectDetail detailItem = EvalProjectDetail.builder()
                .projectId(evalProjectOverview.getProjectId())
                .overviewId(evalProjectOverview.getId())
                .deliverType(evalProjectOverview.getDeliverType())
                .indexType(EvalIndexTypeEnum.COST_INDEX.getValue())
                .assessmentProject(AssessmentProjectEnum.CBPCL.getValue())
                .weight(AssessmentProjectEnum.CBPCL.getWeight())
                .score(BigDecimal.ZERO)
                .build();
        costIndexList.add(detailItem);

        return costIndexList;
    }

    /**
     * 组装进度指标集合
     *
     * @param evalProjectOverview
     * @return
     */
    private List<EvalProjectDetail> assembleProcessIndex(EvalProjectOverview evalProjectOverview) {
        List<EvalProjectDetail> processIndexList = new ArrayList<>(5);
        if (null == evalProjectOverview) {
            return processIndexList;
        }

        EvalProjectDetail planDeviationRateSItem = EvalProjectDetail.builder()
                .projectId(evalProjectOverview.getProjectId())
                .overviewId(evalProjectOverview.getId())
                .deliverType(evalProjectOverview.getDeliverType())
                .indexType(EvalIndexTypeEnum.PROCESS_INDEX.getValue())
                .assessmentProject(AssessmentProjectEnum.SWLCBJHPCL.getValue())
                .weight(AssessmentProjectEnum.SWLCBJHPCL.getWeight())
                .score(calcPlanDeviationRateScore(evalProjectOverview.getPlanDeviationRate()))
                .build();
        processIndexList.add(planDeviationRateSItem);

        EvalProjectDetail baseLineChangeItem = EvalProjectDetail.builder()
                .projectId(evalProjectOverview.getProjectId())
                .overviewId(evalProjectOverview.getId())
                .deliverType(evalProjectOverview.getDeliverType())
                .indexType(EvalIndexTypeEnum.PROCESS_INDEX.getValue())
                .assessmentProject(AssessmentProjectEnum.XMJXBGCS.getValue())
                .weight(AssessmentProjectEnum.XMJXBGCS.getWeight())
                .score(BigDecimal.ZERO)
                .build();
        processIndexList.add(baseLineChangeItem);

        return processIndexList;
    }

    /**
     * 组装质量指标集合
     *
     * @param evalProjectOverview
     * @return
     */
    private List<EvalProjectDetail> assembleQualityIndex(EvalProjectOverview evalProjectOverview) {
        List<EvalProjectDetail> qualityIndexList = new ArrayList<>(5);
        if (null == evalProjectOverview) {
            return qualityIndexList;
        }

        EvalProjectDetail customerSatisfactionItem = EvalProjectDetail.builder()
                .projectId(evalProjectOverview.getProjectId())
                .overviewId(evalProjectOverview.getId())
                .deliverType(evalProjectOverview.getDeliverType())
                .indexType(EvalIndexTypeEnum.QUALITY_INDEX.getValue())
                .assessmentProject(AssessmentProjectEnum.KHMYD.getValue())
                .weight(AssessmentProjectEnum.KHMYD.getWeight())
                .score(BigDecimal.ZERO)
                .build();
        qualityIndexList.add(customerSatisfactionItem);

        return qualityIndexList;
    }

    /**
     * 计算计划偏差率分
     *
     * @param planDeviationRate 计划偏差率
     * @return
     */
    public static BigDecimal calcPlanDeviationRateScore(BigDecimal planDeviationRate) {
        if (planDeviationRate == null) {
            return BigDecimal.ZERO; // 如果偏差率为 null，默认不得分
        }

        // 将输入值转为百分比数值（如 0.05 转为 5）
        BigDecimal ratePercent = planDeviationRate.multiply(new BigDecimal("100"));

        if (ratePercent.compareTo(new BigDecimal("5")) <= 0) {
            return new BigDecimal("5"); // X ≤ 5%，得5分
        } else if (ratePercent.compareTo(new BigDecimal("10")) <= 0) {
            return new BigDecimal("3"); // 5% < X ≤ 10%，得3分
        } else if (ratePercent.compareTo(new BigDecimal("15")) <= 0) {
            return new BigDecimal("1"); // 10% < X ≤ 15%，得1分
        } else {
            return BigDecimal.ZERO; // X > 15%，得0分
        }
    }

    /**
     * 计算指标得分
     */
    private BigDecimal calculateScore(EvalProjectDetail detail, EvalProjectOverviewVO overviewVO) {
        if (EvalIndexTypeEnum.COST_INDEX.getValue().equals(detail.getIndexType())) {
            // 成本指标得分计算
            return calculateCostScore(overviewVO.getCostDeviationRate());
        } else if (AssessmentProjectEnum.SWLCBJHPCL.getValue().equals(detail.getAssessmentProject())) {
            return calcPlanDeviationRateScore(overviewVO.getPlanDeviationRate());
        } else if (AssessmentProjectEnum.XMJXBGCS.getValue().equals(detail.getAssessmentProject())) {
            // 项目基线变更次数得分计算
            return calculateChangeCountScore(overviewVO.getChangeCount());
        } else if (EvalIndexTypeEnum.QUALITY_INDEX.getValue().equals(detail.getIndexType())) {
            // 质量指标得分计算
            return calculateQualityScore(overviewVO);
        }
        return BigDecimal.ZERO;
    }

    /**
     * 计算成本偏差率得分
     */
    private BigDecimal calculateCostScore(BigDecimal costDeviationRate) {
        if (costDeviationRate == null) {
            return BigDecimal.ZERO;
        }

        // 转换为百分比
        BigDecimal ratePercent = costDeviationRate.multiply(new BigDecimal("100")).abs();

        if (ratePercent.compareTo(new BigDecimal("5")) <= 0) {
            return new BigDecimal("5");
        } else if (ratePercent.compareTo(new BigDecimal("10")) <= 0) {
            return new BigDecimal("3");
        } else if (ratePercent.compareTo(new BigDecimal("15")) <= 0) {
            return new BigDecimal("1");
        } else {
            return BigDecimal.ZERO;
        }
    }

    /**
     * 计算项目基线变更次数得分
     */
    private BigDecimal calculateChangeCountScore(Integer changeCount) {
        if (changeCount == null) {
            return BigDecimal.ZERO;
        }

        if (changeCount == 0) {
            return new BigDecimal("5");
        } else if (changeCount == 1) {
            return new BigDecimal("4");
        } else if (changeCount == 2) {
            return new BigDecimal("3");
        } else if (changeCount == 3) {
            return new BigDecimal("2");
        } else if (changeCount == 4) {
            return new BigDecimal("1");
        } else {
            return BigDecimal.ZERO;
        }
    }

    /**
     * 计算质量指标得分
     */
    private BigDecimal calculateQualityScore(EvalProjectOverviewVO overviewVO) {
        // 如果有表扬信，直接得5分
        if (overviewVO.getCommendationLetter() != null && !overviewVO.getCommendationLetter().isEmpty()) {
            return new BigDecimal("5");
        }
        // 否则返回客户评价得分
        return overviewVO.getCustomerEvalScore() != null ? overviewVO.getCustomerEvalScore() : BigDecimal.ZERO;
    }

}
