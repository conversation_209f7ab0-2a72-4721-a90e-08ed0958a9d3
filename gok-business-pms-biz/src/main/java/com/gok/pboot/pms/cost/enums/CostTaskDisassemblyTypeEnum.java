package com.gok.pboot.pms.cost.enums;

import com.gok.pboot.pms.enumeration.ValueEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 工单拆解类型 enum
 *
 * <AUTHOR>
 * @date 2025/01/15
 */
@AllArgsConstructor
@Getter
public enum CostTaskDisassemblyTypeEnum implements ValueEnum<Integer> {
    /**
     * 标准工单
     */
    STANDARD_WORK_ORDER(0, "标准工单"),

    /**
     * 总成工单
     */
    TOTAL_WORK_ORDER(1, "总成工单");

    private final Integer value;

    private final String name;



}
