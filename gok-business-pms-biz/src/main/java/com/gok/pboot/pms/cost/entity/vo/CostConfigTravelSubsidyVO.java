package com.gok.pboot.pms.cost.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 成本配置 差旅补贴 vo
 *
 * <AUTHOR>
 * @date 2025/01/08
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class CostConfigTravelSubsidyVO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 人员数量
     */
    private Integer personNum;

    private String personNumStr;

    /**
     * 出差天数
     */
    private Integer awayDay;

    private String awayDayStr;

    /**
     * 是否自行解决住宿
     */
    private Integer stayOwn;

    private String stayOwnStr;

    /**
     * 补贴标准
     */
    private BigDecimal subsidyPrice;

    /**
     * 版本名称
     */
    private String versionName;

}
