package com.gok.pboot.pms.entity.vo;

import com.gok.pboot.pms.entity.DailyPaperEntry;
import com.gok.pboot.pms.entity.domain.ProjectInfo;
import lombok.*;

import java.math.BigDecimal;
import java.util.Collection;

/**
 * 日报审核（项目维度）
 *
 * <AUTHOR>
 * @version 1.3.2
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class DailyReviewProjectVO {

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 审核数量
     */
    private Integer approvalNum;

    /**
     * 正常工时
     */
    private BigDecimal normalHours;

    /**
     * 加班工时
     */
    private BigDecimal addedHours;

    /**
     * 工作日加班工时
     */
    private BigDecimal workOvertimeHours;

    /**
     * 休息日加班工时
     */
    private BigDecimal restOvertimeHours;

    /**
     * 节假日加班工时
     */
    private BigDecimal holidayOvertimeHours;

    public static DailyReviewProjectVO of(ProjectInfo project, Collection<DailyPaperEntry> entries) {
        DailyReviewProjectVO result = new DailyReviewProjectVO();
        BigDecimal normalHours = BigDecimal.ZERO;
        BigDecimal addedHours = BigDecimal.ZERO;
        BigDecimal workOvertimeHours = BigDecimal.ZERO;
        BigDecimal restOvertimeHours = BigDecimal.ZERO;
        BigDecimal holidayOvertimeHours = BigDecimal.ZERO;

        result.setProjectId(String.valueOf(project.getId()));
        result.setProjectName(project.getItemName());
        result.setApprovalNum(entries.size());
        for (DailyPaperEntry e : entries) {
            normalHours = normalHours.add(e.getNormalHours());
            addedHours = addedHours.add(e.getAddedHours());
            workOvertimeHours = workOvertimeHours.add(e.getWorkOvertimeHours());
            restOvertimeHours = restOvertimeHours.add(e.getRestOvertimeHours());
            holidayOvertimeHours = holidayOvertimeHours.add(e.getHolidayOvertimeHours());
        }
        result.setNormalHours(normalHours);
        result.setAddedHours(addedHours);
        result.setWorkOvertimeHours(workOvertimeHours);
        result.setRestOvertimeHours(restOvertimeHours);
        result.setHolidayOvertimeHours(holidayOvertimeHours);
        return result;
    }
}
