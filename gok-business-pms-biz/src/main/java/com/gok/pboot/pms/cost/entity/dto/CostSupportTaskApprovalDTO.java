package com.gok.pboot.pms.cost.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * 售前工单工时审核查询条件
 *
 * <AUTHOR>
 * @date 2024/04/16
 */
@Data
@ApiModel(description = "售前工单工时审核查询条件")
public class CostSupportTaskApprovalDTO {

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 审核状态：0-待审核，1-已审核
     */
    private Integer approvalStatus;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 填报开始日期
     */
    private LocalDate startDate;

    /**
     * 填报结束日期
     */
    private LocalDate endDate;

    /**
     * 工单名称
     */
    private String taskName;

    /**
     * 人员姓名
     */
    private String userName;

    /**
     * 工单负责人
     */
    private Long managerId;

    /**
     * 下级人员
     */
    private List<Long> juniorUserIds;

    /**
     * 工单ID
     */
    private List<Long> TaskIds;
}