package com.gok.pboot.pms.cost.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 交付采购计划表验收条件及状态
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("cost_deliver_purchase_plan_acceptance")
public class CostDeliverPurchasePlanAcceptance extends BeanEntity<Long>{
    private static final long serialVersionUID = 1L;

    /**
    * 交付采购计划ID
    */
    private Long costDeliverPurchasePlanId;

    /**
    * 验收和付款条件
    */
    private String acceptPaymentTerms;

    /**
    * 是否已验收（0=否，1=是）
    */
    private Integer acceptStatus;
}