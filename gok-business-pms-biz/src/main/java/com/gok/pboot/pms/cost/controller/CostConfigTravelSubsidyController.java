package com.gok.pboot.pms.cost.controller;


import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.cost.entity.dto.CostConfigTravelSubsidyDTO;
import com.gok.pboot.pms.cost.entity.vo.CostConfigTravelSubsidyVO;
import com.gok.pboot.pms.cost.service.ICostConfigTravelSubsidyService;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 差旅补贴标准配置 控制器
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Validated
@RestController
@AllArgsConstructor
@RequestMapping("/costConfigTravelSubsidy")
public class CostConfigTravelSubsidyController {

    private final ICostConfigTravelSubsidyService service;

    /**
     * 获取差旅补贴标准配置列表
     *
     * @return {@link ApiResult }<{@link List }<{@link CostConfigTravelSubsidyVO }>>
     */
    @GetMapping("/findList")
    public ApiResult<List<CostConfigTravelSubsidyVO>> getCostConfigTravelSubsidyList() {
        return ApiResult.success(service.getCostConfigTravelSubsidyList(), "获取成功");
    }

    /**
     * 编辑差旅补贴标准配置
     *
     * @param dtoList DTO 列表
     * @return {@link ApiResult }<{@link String }>
     */
    @PostMapping("/edit")
    public ApiResult<String> editCostConfigTravelSubsidyList(@RequestBody @Valid List<CostConfigTravelSubsidyDTO> dtoList) {
        service.editCostConfigTravelSubsidyList(dtoList);
        return ApiResult.success("编辑成功");
    }

    /**
     * 根据版本id获取差旅补贴标准配置
     *
     * @param versionId 版本 ID
     * @return {@link ApiResult }<{@link List }<{@link CostConfigTravelSubsidyVO }>>
     */
    @GetMapping("/findByVersionId/{versionId}")
    public ApiResult<List<CostConfigTravelSubsidyVO>> getCostConfigTravelSubsidyListByVersionId(@PathVariable Long versionId) {
        return ApiResult.success(service.getCostConfigTravelSubsidyListByVersionId(versionId), "获取成功");
    }


}
