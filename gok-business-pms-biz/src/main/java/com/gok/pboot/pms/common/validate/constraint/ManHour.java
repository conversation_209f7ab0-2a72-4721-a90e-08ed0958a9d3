package com.gok.pboot.pms.common.validate.constraint;

import com.gok.pboot.pms.common.validate.validator.ManHourValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * - 工时校验自定义注解 -
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/8/23 15:41
 */
@Documented
@Target({ElementType.PARAMETER, ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = ManHourValidator.class)
public @interface ManHour {
    String message() default "工时参数不合法";

    /**
     * 允许的小数位数
     */
    int maxDecimalPlaces() default 0;

    /**
     * 允许的最小数值
     */
    double min() default 0;

    /**
     * 允许的最大数值
     */
    double max() default 24;

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
