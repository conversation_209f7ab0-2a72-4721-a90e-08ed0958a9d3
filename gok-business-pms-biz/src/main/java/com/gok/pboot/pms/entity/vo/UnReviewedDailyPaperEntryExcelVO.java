package com.gok.pboot.pms.entity.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gok.module.excel.api.converters.LocalDateStringConverter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * - 未审核日报条目 -
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/11/28 15:24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class UnReviewedDailyPaperEntryExcelVO {

    @ExcelIgnore
    private Long id;

    @ExcelProperty("项目编号")
    private String projectCode;

    @ExcelProperty("项目名称")
    private String projectName;

    @ExcelProperty(value = "日期",converter = LocalDateStringConverter.class)
    private LocalDate submissionDate;

    @ExcelProperty("人员姓名")
    private String userRealName;

    @ExcelProperty("所属部门")
    private String projectDeptName;

    /**
     * 部门id
     */
    @ExcelIgnore
    private Long projectDeptId;

    /**
     * 项目销售姓名
     */
    @ExcelProperty("客户经理")
    private String salesmanUserName;

    /**
     * 售前经理姓名
     */
    @ExcelProperty("售前经理")
    private String preSaleUserName;

    /**
     * 项目经理姓名
     */
    @ExcelProperty("项目经理")
    private String managerUserName;

    @ExcelProperty("任务负责人")
    private String auditPerson;

    /**
     * 项目状态
     */
    @ExcelIgnore
    private String projectStatus;

    /**
     * 项目状态值
     */
    @ExcelProperty("项目状态")
    private String projectStatusName;

    /**
     * 是否内部项目
     */
    @ExcelProperty("是否内部项目")
    private String isNotInternalProject;

    /**
     * 项目类型
     */
    @ExcelProperty("项目类型")
    private String projectTypeName;

    /**
     * 项目正常工时
     */
    @ExcelProperty("项目正常工时")
    private BigDecimal normalHours;

    /**
     * 项目加班工时
     */
    @ExcelProperty("项目加班工时")
    private BigDecimal addedHours;

    @ExcelProperty("工作日加班工时")
    private BigDecimal workOvertimeHours;

    @ExcelProperty("休息日加班工时")
    private BigDecimal restOvertimeHours;

    @ExcelProperty("节假日加班工时")
    private BigDecimal holidayOvertimeHours;

    /**
     * 调休工时
     */
    @ExcelProperty("调休工时")
    private BigDecimal leaveHours;

    /**
     * OA加班工时
     */
    @ExcelProperty("OA加班工时")
    private BigDecimal hourData;

    /**
     * 项目分摊工时
     */
    @ExcelProperty("项目分摊工时")
    private BigDecimal projectShareHours;

    /**
     * 项目耗用工时
     */
    @ExcelProperty("项目耗用工时")
    private BigDecimal projectHours;

    /**
     * 售前工时
     */
    @ExcelProperty("售前工时")
    private BigDecimal preSaleHours;

    /**
     * 售后工时
     */
    @ExcelProperty("售后工时")
    private BigDecimal afterSaleHours;

}
