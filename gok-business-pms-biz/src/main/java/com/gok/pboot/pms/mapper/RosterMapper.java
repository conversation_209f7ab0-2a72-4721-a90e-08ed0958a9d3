package com.gok.pboot.pms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.entity.bo.RosterSelectionBO;
import com.gok.pboot.pms.entity.domain.Roster;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * @menu
 * @author: suzy
 * @create date: 2023/3/10
 * @Description:
 **/
@Mapper
public interface RosterMapper extends BaseMapper<Roster> {

    /**
     * 根据userId列表查询花名册信息，组装为“userId-花名册信息”Map
     *
     * @param userIds userId列表
     * @return “userId-花名册信息”Map
     */
    @MapKey("id")
    Map<Long, Roster> findUserIdMap(@Nullable @Param("userIds") Collection<Long> userIds);

    /**
     * 查询用户ID列表
     *
     * @param filter 参数Map
     * @return 用户ID列表
     */
    List<Long> findIds(@Param("filter") Map<String, Object> filter);

    /**
     * 通过用户id获取其直属下级
     *
     * @param userId 用户id
     * @return {@link List}<{@link Long}>
     */
    List<Long> findLowerByUserId(@Param("userId") Long userId);

    /**
     * 根据部门ID列表查询
     *
     * @param deptIds 部门ID列表
     * @return 人员ID列表
     */
    List<Long> findIdByDeptIds(@Param("deptIds") Collection<Long> deptIds);

    /**
     * 根据用户ID查询姓名
     *
     * @param userId 用户ID
     * @return 姓名
     */
    String findAliasNameById(Long userId);

    /**
     * 根据姓名模糊查询选项
     *
     * @param aliasName 姓名
     * @return 花名册选项
     */
    List<RosterSelectionBO> findSelectionByAliasNameLike(@Param("aliasName") String aliasName);

    /**
     * 查询用户姓名列表
     *
     * @param ids 用户ID列表
     * @return 用户姓名列表
     */
    List<String> findIdAndAliasNameMapByIds(@Param("ids") Collection<Long> ids);

    /**
     * 查询花名册有效用户数据
     */
    List<Long> normalUserIds();

    /**
     * 根据姓名模糊查询
     *
     * @param userNames 用户名
     * @return {@link List }<{@link Roster }>
     */
    List<Roster> findByName(@Param("userNames") Collection<String> userNames);


}
