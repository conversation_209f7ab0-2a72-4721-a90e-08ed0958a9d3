package com.gok.pboot.pms.enumeration;

/**
 * 项目-经办人客情状态枚举
 *
 * <AUTHOR>
 */
public enum ExpectedOrderAmountWeightedEnum implements ValueEnum<Integer> {
    /**
     * 不明确
     */
    ambiguity(0, "不明确"),
    /**
     * 反对
     */
    against(1, "反对"),
    /**
     * 中立
     */
    neutrality(2, "中立"),
    /**
     * 友好
     */
    friendly(3, "友好"),
    /**
     * 信任
     */
    trust(4, "信任"),
    /**
     * 同盟
     */
    alliance(4, "同盟");

    //值
    private Integer  value;
    //名称
    private String name;

    ExpectedOrderAmountWeightedEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
    /**
     * 获取值
     *
     * @return Integer
     */
    @Override
    public Integer getValue() {
        return value;
    }

    /**
     * 获取名称
     *
     * @return String
     */
    @Override
    public String getName() {
        return name;
    }

    public static String getNameByVal(Integer value) {
        for (ExpectedOrderAmountWeightedEnum expectedOrderAmountWeightedEnum : ExpectedOrderAmountWeightedEnum.values()) {
            if (expectedOrderAmountWeightedEnum.value.equals(value)) {
                return expectedOrderAmountWeightedEnum.name;
            }
        }
        return "";
    }
    public static Integer getValByName(String name) {
        for (ExpectedOrderAmountWeightedEnum expectedOrderAmountWeightedEnum : ExpectedOrderAmountWeightedEnum.values()) {
            if (expectedOrderAmountWeightedEnum.getName().equals(name)) {
                return expectedOrderAmountWeightedEnum.getValue();
            }
        }
        return null;
    }

    public static ExpectedOrderAmountWeightedEnum getExpectedOrderAmountWeightedEnum(Integer value) {
        for (ExpectedOrderAmountWeightedEnum expectedOrderAmountWeightedEnum : ExpectedOrderAmountWeightedEnum.values()) {
            if (expectedOrderAmountWeightedEnum.value.equals(value)) {
                return expectedOrderAmountWeightedEnum;
            }
        }
        return null;
    }
}
