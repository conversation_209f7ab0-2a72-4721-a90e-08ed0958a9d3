package com.gok.pboot.pms.entity.vo;

import lombok.*;

import java.util.Map;

/**
 * - 特殊配置用户基本信息 -
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@ToString
public class SpecialSettingUserVO {

    /**
     * 用户id
     */
    private String userId;

    /**
     * 用户姓名
     */
    private String userRealName;

    public static SpecialSettingUserVO from(Long userId, Map<Long, String> userIdAndNameMap) {
        SpecialSettingUserVO result = new SpecialSettingUserVO();

        result.setUserId(userId.toString());
        String userName = userIdAndNameMap.get(userId);
        if(userName != null){
            result.setUserRealName(userName);
        }
        return result;
    }
}
