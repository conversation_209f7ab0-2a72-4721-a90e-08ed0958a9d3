package com.gok.pboot.pms.enumeration;

/**
 * 日报工时类型枚举
 *
 * <AUTHOR>
public enum WorkTypeEnum implements ValueEnum<Integer> {

    /**
     * 售前支撑
     */
    PRE(0, "售前支撑"),
    /**
     * 售后交付
     */
    AFTER(1, "售后交付"),
   ;

    /**
     * 值
     */
    private Integer  value;
    /**
     * 名称
     */
    private String name;

    WorkTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    /**
     * 获取值
     *
     * @return Integer
     */
    @Override
    public Integer getValue() {
        return value;
    }

    /**
     * 获取名称
     *
     * @return String
     */
    @Override
    public String getName() {
        return name;
    }

    public static String getNameByVal(Integer value) {
        for (WorkTypeEnum statusEnum : WorkTypeEnum.values()) {
            if (statusEnum.value.equals(value) ) {
                return statusEnum.name;
            }
        }
        return "";
    }
    public static Integer getValByName(String name) {
        for (WorkTypeEnum statusEnum : WorkTypeEnum.values()) {
            if (statusEnum.getName().equals(name)) {
                return statusEnum.getValue();
            }
        }
        return null;
    }

}
