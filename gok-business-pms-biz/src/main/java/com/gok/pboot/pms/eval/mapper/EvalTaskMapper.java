package com.gok.pboot.pms.eval.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.eval.entity.domain.EvalTask;
import com.gok.pboot.pms.eval.entity.dto.EvalTaskDistributionDTO;
import com.gok.pboot.pms.eval.entity.dto.EvalTaskListDTO;
import com.gok.pboot.pms.eval.entity.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工单评价 Mapper 接口
 * <AUTHOR>
 * @since 2025-05-08
 */
@Mapper
public interface EvalTaskMapper extends BaseMapper<EvalTask> {

    /**
     * 查询工单评价分页列表
     * @param page 分页参数
     * @param dto 查询条件
     * @return 评价列表
     */
    Page<EvalTaskListVO> findListByDto(Page<EvalTaskListVO> page, @Param("dto") EvalTaskListDTO dto);

    /**
     * 查询工单评价列表
     * @param dto
     * @return
     */
    List<EvalTaskListVO> findListByDto(@Param("dto") EvalTaskListDTO dto);

    /**
     * 查询项目人员工单评价分布分页列表
     * @param page 分页参数
     * @param dto
     * @return
     */
    Page<EvalTaskDistributionVO> getEvalDistribution(Page<EvalTaskListVO> page,@Param("dto") EvalTaskDistributionDTO dto);

    /**
     * 查询项目人员工单评价分布列表
     * @param dto
     * @return
     */
    List<EvalTaskDistributionVO> getEvalDistribution(@Param("dto") EvalTaskDistributionDTO dto);

    /**
     * 查询项目人员售后工单评价分布明细
     * @param page
     * @param dto
     * @return
     */
    Page<EvalTaskDistributionAfterDetailVO> getAfterEvalDistributionDetail(Page<EvalTaskDistributionAfterDetailVO> page, @Param("dto")  EvalTaskDistributionDTO dto);

    /**
     * 查询项目人员售前工单评价分布明细
     * @param page
     * @param dto
     * @return
     */
    Page<EvalTaskDistributionPreDetailVO> getPreEvalDistributionDetail(Page<EvalTaskDistributionPreDetailVO> page, @Param("dto")  EvalTaskDistributionDTO dto);

    /**
     * 计算项目人员综合得分
     * @param projectId 项目id
     * @param managerId 人员id
     * @return
     */
    ProjectEvalRankVO countComprehensiveScore(@Param("projectId") Long projectId, @Param("managerId") Long managerId);

    /**
     * 逻辑删除工单评价
     * @param id
     */
    void delById(@Param("id") Long id);
}
