package com.gok.pboot.pms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.entity.OvertimeLeaveData;
import com.gok.pboot.pms.entity.dto.DailyFindPageDTO;
import com.gok.pboot.pms.entity.vo.OvertimeLeaveDataVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 加班、请假、销假数据同步 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-12
 */
@Mapper
public interface OvertimeLeaveDataMapper extends BaseMapper<OvertimeLeaveData> {

    /**
     * 通过日报查询条件获取请假的列表 用户/每天 纬度
     *
     * @param dailyFindPageDTO
     * @return
     */
    List<OvertimeLeaveDataVO> getOvertimeLeaveDataList(@Param("dailyFindPageDTO") DailyFindPageDTO dailyFindPageDTO);

    /**
     * ~ 根据提交日期范围查询加班信息（type=99） ~
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return java.util.List<com.gok.pboot.pms.entity.OvertimeLeaveData>
     * @see com.gok.pboot.pms.enumeration.LeaveStatusEnum
     */
    List<OvertimeLeaveData> getOvertimeDataListByUserId(
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate,
            @Param("userId") Long userId,
            @Param("types") List<String> types
    );

    /**
     * 按用户 ID 获取加班数据列表
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param userIds   用户 ID
     * @return {@link List}<{@link OvertimeLeaveData}>
     */
    List<OvertimeLeaveData> getOvertimeDataListByUserIds(
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate,
            @Param("userIds") Collection<Long> userIds
    );


    /**
     * ~ 根据提交日期和用户ID列表查询请假信息 ~
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param userIds   用户ID列表
     * @return java.util.List<com.gok.pboot.pms.entity.OvertimeLeaveData>
     * <AUTHOR>
     * @date 2022/11/14 10:03
     */
    List<OvertimeLeaveData> getLeaveDataListByUserIds(
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate,
            @Param("userIds") Collection<Long> userIds
    );

    /**
     * 根据日期和用户ID查询请休假数据
     *
     * @param queryDate 日期
     * @param userId    用户ID
     * @return 请休假信息
     */
    List<OvertimeLeaveData> getLeaveDataListByUserId(
            @Param("queryDate") LocalDate queryDate,
            @Param("userId") Long userId
    );

    /**
     * 根据日期和用户ID查询
     *
     * @param queryDate 日期
     * @param userId    用户ID
     * @return 请休假、加班数据列表
     */
    List<OvertimeLeaveData> findByDateAndUserId(@Param("queryDate") LocalDate queryDate, @Param("userId") Long userId);

    /**
     * 根据日期范围和用户ID查询
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param userId    用户ID
     * @return 请休假、加班数据列表
     */
    List<OvertimeLeaveData> findByDateRangeAndUserId(
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate,
            @Param("userId") Long userId
    );


    /**
     * 获取项目调休
     *
     * @param projectId 项目id
     * @return 调休数据列表
     */
    List<OvertimeLeaveData> getTxHoursByParams(@Param("projectId") Long projectId);
}
