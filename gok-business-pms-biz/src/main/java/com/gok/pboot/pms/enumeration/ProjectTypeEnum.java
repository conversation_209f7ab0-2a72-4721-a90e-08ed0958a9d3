package com.gok.pboot.pms.enumeration;

/**
 * OA项目类型枚举
 **/
public enum ProjectTypeEnum implements ValueEnum<Integer> {

    ICTJC(0, "ICT集成"),
    JCSSYW(1, "基础设施运维"),
    AQYW(2, "安全运维"),
    RJDZKF(3, "软件定制开发"),
    RJCPYW(4, "软件产品运维"),
    SJYW(5, "数据运维"),
    QYPX(6, "企业培训"),
    XMSX(7, "项目实训"),
    XQSDHZ(8, "校企深度合作"),
    ZQGJJD(9, "政企共建基地"),
    TOCGRPX(10, "ToC个人培训");


    //值
    private Integer value;
    //名称
    private String name;

    ProjectTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    /**
     * 获取值
     *
     * @return Integer
     */
    @Override
    public Integer getValue() {
        return value;
    }

    /**
     * 获取名称
     *
     * @return String
     */
    @Override
    public String getName() {
        return name;
    }

    public static String getNameByVal(Integer value) {
        for (ProjectTypeEnum statusEnum : ProjectTypeEnum.values()) {
            if (statusEnum.value.equals(value)) {
                return statusEnum.name;
            }
        }
        return "";
    }

    public static Integer getValByName(String name) {
        for (ProjectTypeEnum statusEnum : ProjectTypeEnum.values()) {
            if (statusEnum.getName().equals(name)) {
                return statusEnum.getValue();
            }
        }
        return null;
    }

}
