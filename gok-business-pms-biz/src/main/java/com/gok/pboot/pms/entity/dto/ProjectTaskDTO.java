package com.gok.pboot.pms.entity.dto;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 项目任务DTO
 *
 * <AUTHOR>
 * @date 2023/8/19
 */
@Data
public class ProjectTaskDTO {

    /**
     * 父级ID
     */
    private Long parentId;

    /**
     * 项目ID
     */
    //@NotNull(message = "项目ID不能为空")
    private Long projectId;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 任务标题
     */
    @NotBlank(message = "任务标题不能为空")
    private String title;

    /**
     * 负责人ID
     */
    @NotNull(message = "负责人ID不能为空")
    private Long managerUserId;

    /**
     * 负责人姓名
     */
    private String managerUserName;


    /**
     * 参与人ID集合
     */
    private List<Long> userIds;

    /**
     * 参与人姓名集合
     */
    private List<String> userNames;

    /**
     * 状态（0=未开始，1=进行中，2=已完成）
     * @see com.gok.pboot.pms.enumeration.TaskWorkingState
     */
    private Integer state;

    /**
     * 计划开始时间
     */
    @NotNull(message = "计划开始时间不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expectedStartTime;

    /**
     * 计划结束时间
     */
    @NotNull(message = "计划结束时间不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expectedEndTime;

    /**
     * 实际开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private LocalDateTime actualStartTime;

    /**
     * 实际结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private LocalDateTime actualEndTime;

    /**
     * 是否里程碑
     */
    @NotNull(message = "里程碑不能为空")
    private Boolean milestone;

    /**
     * 详细描述
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String content;

    ///**
    // * 附件文件ID
    // */
    //private Long fileId;

    /**
     * 附件文件ID集合
     */
    @Size(max = 5, message = "附件不能超过5个")
    private List<Long> fileIds;

}
