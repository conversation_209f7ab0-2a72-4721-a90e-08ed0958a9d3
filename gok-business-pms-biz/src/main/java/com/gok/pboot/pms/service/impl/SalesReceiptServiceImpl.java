package com.gok.pboot.pms.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.bcp.message.dto.BcpMessageTargetDTO;
import com.gok.bcp.message.entity.enums.MsgTypeEnum;
import com.gok.bcp.message.entity.enums.SourceEnum;
import com.gok.bcp.message.entity.enums.TargetTypeEnum;
import com.gok.bcp.message.entity.model.MailModel;
import com.gok.bcp.message.entity.model.WeComModel;
import com.gok.bcp.message.feign.RemoteMailService;
import com.gok.bcp.message.feign.RemoteSendMsgService;
import com.gok.bcp.upms.dto.DeptDetailDto;
import com.gok.bcp.upms.feign.RemoteDeptService;
import com.gok.bcp.upms.feign.RemoteRoleService;
import com.gok.bcp.upms.vo.SysUserRoleDataVo;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.Util.MoneyUtil;
import com.gok.pboot.pms.entity.domain.SalesReceipt;
import com.gok.pboot.pms.entity.dto.SalesReceiptDTO;
import com.gok.pboot.pms.entity.vo.SalesReceiptPushVO;
import com.gok.pboot.pms.entity.vo.SalesReceiptVO;
import com.gok.pboot.pms.enumeration.AttributableSubjectEnum;
import com.gok.pboot.pms.enumeration.ProjectStatusEnum;
import com.gok.pboot.pms.mapper.SalesReceiptMapper;
import com.gok.pboot.pms.service.ISalesReceiptService;
import com.google.common.base.Charsets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 销售收款计划ServiceImpl
 *
 * <AUTHOR>
 * @description 针对表【sales_receipt(销售收款计划)】的数据库操作Service实现
 * @createDate 2023-09-27 16:08:24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SalesReceiptServiceImpl extends ServiceImpl<SalesReceiptMapper, SalesReceipt>
        implements ISalesReceiptService {

    /**
     * 门户id
     */
    @Value("${pushMessage.portalAppId}")
    private Long portalAppId;

    /**
     * 教育回款URL
     */
    @Value("${pushMessage.salesReceiptUrl}")
    private String salesReceiptUrl;

    @Value("${pushMessage.redirectPrefixUrl}")
    private String redirectPrefixUrl;

    private final RemoteRoleService remoteRoleService;

    private final RemoteMailService remoteMailService;

    private final RemoteSendMsgService remoteSendMsgService;

    private final RemoteDeptService remoteDeptService;

    @Override
    public Page<SalesReceiptVO> findPage(SalesReceiptDTO dto) {
        // 分页
        Page<SalesReceipt> pageInfo = new Page<>(dto.getPageNumber(), dto.getPageSize());
        // 主体名称转为code
        String attributableSubject = dto.getAttributableSubject();
        if (CharSequenceUtil.isNotBlank(attributableSubject)) {
            dto.setAttributableSubject(EnumUtils.getValueByName(AttributableSubjectEnum.class, attributableSubject));
        }
        baseMapper.querySalesReceiptPage(pageInfo, limitDto(dto));
        List<SalesReceipt> records = pageInfo.getRecords();
        List<SalesReceiptVO> voRecords = new ArrayList<>();
        if (CollUtil.isNotEmpty(records)) {
            //枚举类型转换
            records.forEach(r -> voRecords.add(entityToVo(r)));
        }
        Page<SalesReceiptVO> page = new Page<>();
        BeanUtils.copyProperties(pageInfo, page);
        page.setRecords(voRecords);
        return page;
    }



    /**
     * 导出Excel
     *
     * @param dto dto
     * @return {@link List}
     */
    @Override
    public List<SalesReceiptVO> export(SalesReceiptDTO dto) {
        // 主体名称转为code
        String attributableSubject = dto.getAttributableSubject();
        if (CharSequenceUtil.isNotBlank(attributableSubject)) {
            dto.setAttributableSubject(EnumUtils.getValueByName(AttributableSubjectEnum.class, attributableSubject));
        }
        List<SalesReceipt> salesReceiptList = baseMapper.querySalesReceiptList(limitDto(dto));

        List<SalesReceiptVO> voList = new ArrayList<>();
        for (SalesReceipt salesReceipt : salesReceiptList) {
            SalesReceiptVO vo = entityToVo(salesReceipt);
            voList.add(vo);
        }
        return voList;
    }

    @Override
    public Boolean pushMessage() {
        SimpleDateFormat dateFormatter = new SimpleDateFormat("yyyy-MM-dd");
        try {
            //获取全部的未收款内容
            List<SalesReceiptPushVO> salesReceiptPushVoS = baseMapper.queryPushVo();
            for (SalesReceiptPushVO vo : salesReceiptPushVoS) {
                // 2023/01/01 之前无需推送
                Date expectedDate = dateFormatter.parse(vo.getExpectedDate());
                if (DateUtil.compare(dateFormatter.parse("2023-01-01"), expectedDate) > 0) {
                    continue;
                }
                //当前天数的差距
                Long chaJu = (dateFormatter.parse(dateFormatter.format(new Date())).getTime()
                        - expectedDate.getTime()) / (24 * 60 * 60 * 1000);
                // 按时间推送消息到门户
                pushMessageByTime(chaJu, vo);
            }
            return true;
        } catch (ParseException e) {
            log.error("时间格式化有误");
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 初始化实体类
     *
     * @param salesReceipt 实体类
     * @return {@link SalesReceiptVO}
     */
    private SalesReceiptVO entityToVo(SalesReceipt salesReceipt) {
        SalesReceiptVO vo = new SalesReceiptVO();
        // 基础信息拷贝
        BeanUtils.copyProperties(salesReceipt, vo);
        // 数值类型转换
        vo.setContractMoney(MoneyUtil.getInstance().transType(salesReceipt.getContractMoney()));
        vo.setAccumulatedAmount(MoneyUtil.getInstance().transType(salesReceipt.getAccumulatedAmount()));
        vo.setCurrentPaymentMoney(MoneyUtil.getInstance().transType(salesReceipt.getCurrentPaymentMoney()));

        //累计收款比例 *100%
        if (salesReceipt.getCollectionRatio() == null) {
            vo.setCollectionRatio(null);
        } else {
            if (NumberUtils.INTEGER_ZERO != new BigDecimal(salesReceipt.getCollectionRatio()).signum()) {
                BigDecimal ratio = new BigDecimal(salesReceipt.getCollectionRatio())
                        .multiply(new BigDecimal("100")).setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP);
                vo.setCollectionRatio(ratio + "%");
            } else {
                vo.setCollectionRatio(MoneyUtil.TYPE);
            }
        }
        vo.setProjectStatusTxt(ProjectStatusEnum.getNameByStrVal( salesReceipt.getProjectStatus()));
        vo.setAttributableSubjectTxt(EnumUtils.getNameByValue(AttributableSubjectEnum.class, salesReceipt.getAttributableSubject()));

        return vo;
    }

    /**
     * 给传入的参数添加中台权限
     *
     * @param dto {@link SalesReceiptDTO}
     * @return {@link SalesReceiptDTO}
     */
    private SalesReceiptDTO limitDto(SalesReceiptDTO dto) {
        SysUserRoleDataVo userDataScope = remoteRoleService
                .getRoleDataDetailByUserId(dto.getClientId(), SecurityUtils.getUser().getId(), dto.getMenuCode()).getData();
        if (Boolean.FALSE.equals(userDataScope.getIsAll())) {
            dto.setAuthority(Boolean.TRUE);
            dto.setUserId(userDataScope.getUserIdList());
            dto.setAuthDeptIdList(userDataScope.getDeptIdList());
        }
        return dto;
    }

    private void pushMessageByTime(Long time, SalesReceiptPushVO vo) {
        if (!Optional.ofNullable(vo).isPresent() || !Optional.ofNullable(vo.getSalesmanUserId()).isPresent()
                || !Optional.ofNullable(vo.getSalesmanUserName()).isPresent()) {
            return;
        }
        MailModel model = new MailModel();
        model.setSource(SourceEnum.PROJECT.getValue());
        model.setType(MsgTypeEnum.TEXT_MSG.getValue());
        model.setTitle("业务一体化销售合同待收款");
        model.setSenderId(portalAppId);
        model.setSender(SourceEnum.PROJECT.getName());
        model.setTargetType(TargetTypeEnum.USERS.getValue());
        model.setRedirectUrl(
                redirectPrefixUrl +
                Base64.encode(salesReceiptUrl + "?contractCode=" + vo.getContractCode(), Charsets.UTF_8)
        );

        WeComModel msgModel;
        List<BcpMessageTargetDTO> list = new ArrayList<>();
        BcpMessageTargetDTO salesmanDto = new BcpMessageTargetDTO();
        salesmanDto.setTargetId(vo.getSalesmanUserId().toString());
        salesmanDto.setTargetName(vo.getSalesmanUserName());
        list.add(salesmanDto);

        // 1、合同金额为0不推送
        if (vo.getContractMoney() == null || BigDecimal.ZERO.compareTo(vo.getContractMoney()) == 0) {
            return;
        }
        // 2、推送条件为 -7天提醒
        // 3、推送条件为 0天提醒
        // 4、推送条件为 >=7天且每七天提醒
        if (time == -7 || time >= 7 && time % 7 == 0) {
            model.setTargetList(list);
            model.setContent("你有一笔销售合同"
                    + vo.getContractCode() + "的"
                    + vo.getPaymentName() + "质保金预期"
                    + vo.getExpectedDate() + "收款尚未收款，请及时处理~");
            try {
                remoteMailService.sendMsg(model);
                msgModel = WeComModel.from(MailModel.to(model));
                msgModel.setContent(
                        model.getContent() + "\n<a href=\"" + model.getRedirectUrl() + "\">" + "查看详情</a>"
                );
            } catch (Exception e) {
                log.info("中台推送消息失败, 对应的目标人员id为: {}, 姓名为: {}", vo.getSalesmanUserId(), vo.getSalesmanUserName());
            }
        } else if (time == 0) {
            //获取上级领导的id和名称
            DeptDetailDto deptDetailDto = remoteDeptService.getDeptDetail(vo.getFirstDepartmentId()).getData();
            if (deptDetailDto != null && deptDetailDto.getDeptLeaderMap() != null) {
                Map<Long, String> map = deptDetailDto.getDeptLeaderMap();
                for (Map.Entry<Long, String> m : map.entrySet()) {
                    BcpMessageTargetDTO deptManDto = new BcpMessageTargetDTO();
                    deptManDto.setTargetId(String.valueOf(m.getKey()));
                    deptManDto.setTargetName(map.get(m.getKey()));
                    list.add(deptManDto);
                }
            }
            model.setTargetList(list);
            model.setContent("你有一笔销售合同"
                    + vo.getContractCode() + "的"
                    + vo.getPaymentName() + "质保金预期"
                    + vo.getExpectedDate() + "收款尚未收款，请及时处理~");
            try {
                remoteMailService.sendMsg(model);
                msgModel = WeComModel.from(MailModel.to(model));
                msgModel.setContent(
                        model.getContent() + "\n<a href=\"" + model.getRedirectUrl() + "\">" + "查看详情</a>"
                );
            } catch (Exception e) {
                log.info("中台推送消息失败, 对应的目标人员id为: {}, 姓名为: {}", vo.getSalesmanUserId(), vo.getSalesmanUserName());
            }
        }
    }

}




