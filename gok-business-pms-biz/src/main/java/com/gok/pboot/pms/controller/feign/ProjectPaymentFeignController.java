package com.gok.pboot.pms.controller.feign;

import cn.hutool.core.bean.BeanUtil;
import com.gok.base.admin.bo.ProjectPaymentBO;
import com.gok.base.admin.dto.PaymentDTO;
import com.gok.base.admin.dto.ProjectPaymentClaimDTO;
import com.gok.base.admin.dto.ProjectPaymentDTO;
import com.gok.components.common.util.R;
import com.gok.pboot.common.security.annotation.Inner;
import com.gok.pboot.pms.entity.domain.ProjectPayment;
import com.gok.pboot.pms.service.IProjectPaymentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * 项目回款跟踪外部调用
 *
 * <AUTHOR>
 * @since 2024-02-29
 * @menu 项目回款跟踪外部调用
 */
@Inner(false)
@RestController
@RequiredArgsConstructor
@Api(tags = "项目回款跟踪外部调用")
@RequestMapping("/inner/project-payment")
public class ProjectPaymentFeignController {

    private final IProjectPaymentService projectPaymentService;

    /**
     * 插入数据
     *
     * @param paymentDTO {@link PaymentDTO}
     * @return {@link R}<{@link Boolean}>
     */
    @PostMapping("/save")
    @ApiOperation(value = "插入数据", notes = "插入数据")
    public R<Boolean> save(@Valid @RequestBody PaymentDTO paymentDTO) {
        return projectPaymentService.save(paymentDTO) > NumberUtils.INTEGER_ZERO
                ? R.ok(Boolean.TRUE) : R.failed(Boolean.FALSE);
    }

    /**
     * 插入数据
     *
     * @param boList {@link List}<{@link ProjectPaymentBO}>
     * @return {@link R}<{@link Boolean}>
     */
    @PostMapping("/batch-save")
    @ApiOperation(value = "插入数据", notes = "插入数据")
    public R<Boolean> saveBatch(@RequestBody List<ProjectPaymentBO> boList) {
        List<ProjectPayment> list = new ArrayList<>();
        boList.forEach(b -> list.add(BeanUtil.copyProperties(b, ProjectPayment.class)));
        return projectPaymentService.saveBatch(list) ? R.ok(Boolean.TRUE) : R.failed(Boolean.FALSE);
    }

    /**
     * 根据id更新数据
     *
     * @param projectPaymentClaimDTO {@link ProjectPaymentClaimDTO}
     * @return {@link R}<{@link Boolean}>
     */
    @PutMapping("/update")
    @ApiOperation(value = "根据id更新数据", notes = "根据id更新数据")
    public R<Boolean> update(@Valid @RequestBody ProjectPaymentClaimDTO projectPaymentClaimDTO) {
        return Boolean.TRUE.equals(projectPaymentService.update(projectPaymentClaimDTO))
                ? R.ok(Boolean.TRUE) : R.failed(Boolean.FALSE);
    }

    /**
     * 删除
     *
     * @param paymentDTO {@link PaymentDTO}
     * @return {@link R}
     */
    @DeleteMapping("/delete")
    @ApiOperation(value = "删除选中数据", notes = "删除选中数据")
    public R<String> delete(@RequestBody PaymentDTO paymentDTO) {
        return projectPaymentService.delete(paymentDTO.getIds());
    }

    /**
     * 认领操作(已认领 -> 待认领)
     *
     * @param id 项目回款跟踪id
     * @return {@link R}<{@link Boolean}>
     */
    @PutMapping("/claim/{id}")
    @ApiOperation(value = "认领操作(已认领 -> 待认领)", notes = "认领操作(已认领 -> 待认领)")
    public R<Boolean> unClaim(@PathVariable("id") Long id) {
        return Boolean.TRUE.equals(projectPaymentService.claim(id))
                ? R.ok(Boolean.TRUE) : R.failed(Boolean.FALSE);
    }

    /**
     * 认领操作(待认领 -> 已认领)
     *
     * @param projectPaymentClaimDTO {@link ProjectPaymentClaimDTO}
     * @return {@link R}<{@link Boolean}>
     */
    @PostMapping("/claim")
    @ApiOperation(value = "认领操作(待认领 -> 已认领)", notes = "认领操作(待认领 -> 已认领)")
    public R<Boolean> claim(@Valid @RequestBody ProjectPaymentClaimDTO projectPaymentClaimDTO) {
        return Boolean.TRUE.equals(projectPaymentService.claim(projectPaymentClaimDTO))
                ? R.ok(Boolean.TRUE) : R.failed(Boolean.FALSE);
    }

    /**
     * 锁定与取消锁定
     *
     * @param projectPaymentDTO {@link ProjectPaymentDTO}
     * @return {@link R}<{@link Boolean}>
     */
    @PostMapping("/lock")
    @ApiOperation(value = "锁定与取消锁定", notes = "锁定与取消锁定")
    public R<Boolean> lock(@RequestBody ProjectPaymentDTO projectPaymentDTO) {
        return Boolean.TRUE.equals(projectPaymentService.lock(projectPaymentDTO))
                ? R.ok(Boolean.TRUE) : R.failed(Boolean.FALSE);
    }
}
