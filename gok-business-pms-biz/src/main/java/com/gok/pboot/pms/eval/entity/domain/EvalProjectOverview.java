package com.gok.pboot.pms.eval.entity.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import com.gok.pboot.pms.eval.enums.EvalStatusEnum;
import lombok.*;

import java.math.BigDecimal;

/**
 * 项目整体评价表实体类
 *
 * <AUTHOR>
 * @create 2025/05/08
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("eval_project_overview")
public class EvalProjectOverview extends BeanEntity<Long> {

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目编号
     */
    private String projectNo;

    /**
     * 交付类型
     * {@link com.gok.pboot.pms.enumeration.DeliverTypeEnum}
     */
    private Integer deliverType;

    /**
     * 计划完成周期
     */
    private Integer planCompletionCycle;

    /**
     * 实际完成周期
     */
    private Integer actualCompletionCycle;

    /**
     * 计划偏差率
     */
    private BigDecimal planDeviationRate;

    /**
     * 客户评价得分
     */
    private BigDecimal customerEvalScore;

    /**
     * 评价状态
     * {@link EvalStatusEnum}
     */
    private Integer evalStatus;

    /**
     * 项目经理评价状态
     */
    private Integer managerEvalStatus;

    /**
     * 预算成本（万元）
     */
    private BigDecimal budgetCost;

    /**
     * 实际成本（万元）
     */
    private BigDecimal actualCost;

    /**
     * 成本偏差率
     */
    private BigDecimal costDeviationRate;

    /**
     * 变更次数（非客户原因）
     */
    private Integer changeCount;

    /**
     * 项目经理ID
     */
    @TableField(exist = false)
    private Long managerUserId;

    @TableField(exist = false)
    private String managerUserName;

}