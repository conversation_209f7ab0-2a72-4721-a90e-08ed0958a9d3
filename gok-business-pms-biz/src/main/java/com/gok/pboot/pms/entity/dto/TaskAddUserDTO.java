package com.gok.pboot.pms.entity.dto;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 任务添加人员
 *
 * <AUTHOR>
 * @since 2022-08-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class TaskAddUserDTO {

    /**
    * 任务ID
    */
    private Long id;
    /**
     * 人员id
     */
    private List<Long> taskUserIds;


}
