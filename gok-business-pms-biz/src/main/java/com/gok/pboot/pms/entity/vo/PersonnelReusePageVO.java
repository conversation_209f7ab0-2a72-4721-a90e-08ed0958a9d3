package com.gok.pboot.pms.entity.vo;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 人才复用
 *
 * <AUTHOR>
 * @since 2022-08-29
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PersonnelReusePageVO{
    private Long id;

    /**
    * 日期（存储为x年x月1日）
    */
    private LocalDate reuseDate;
    /**
    * 项目ID
    */
    private Long projectId;
    /**
    * 项目编号
    */
    private String projectCode;
    /**
    * 项目名称
    */
    private String projectName;
    /**
    * 人员名称
    */
    private String userRealName;
    /**
    * 汇总工时（天）
    */
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal aggregatedDays;
    /**
    * 人才类型
    */
    private String personnelType;
    /**
    * 年级
    */
    private String grade;
    /**
    * 院校名称
    */
    private String schoolName;
    /**
    * 专业
    */
    private String major;
    /**
    * 手机号码
    */
    private String mobile;
    /**
    * 备注
    */
    private String remark;
    /**
    * 导入人ID
    */
    private Long executorUserId;
    /**
    * 导入人姓名
    */
    private String executorUserRealName;

    /**
     * 审核状态（0=未提交，1=已退回，2=待审核，3=不通过，4=已通过） 详见ApprovalStatusEnum
     */
    private Integer approvalStatus;

    /**
     * 审核不通过原因
     */
    private String approvalReason;

    /**
     * 项目经理
     */
    private String managerUserName;
    /**
     * 导入时间
     */
    private String ctime;
}
