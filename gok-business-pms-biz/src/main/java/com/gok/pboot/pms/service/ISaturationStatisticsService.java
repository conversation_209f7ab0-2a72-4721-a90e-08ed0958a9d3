package com.gok.pboot.pms.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.R;
import com.gok.pboot.pms.entity.dto.DailyPaperAnalysisDTO;
import com.gok.pboot.pms.entity.dto.SaturationStatisticsDTO;
import com.gok.pboot.pms.entity.vo.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023/2/22
 */
public interface ISaturationStatisticsService {
    /**
     * 项目占比情况
     *
     * @param saturationStatisticsDTO 查询实体
     * @return {@link ApiResult<Map<String, BigDecimal>>}
     */
    List<ProjectPercentVO> projectPercentage(SaturationStatisticsDTO saturationStatisticsDTO);

    List<ProjectPercentVO> top10Ranking(SaturationStatisticsDTO saturationStatisticsDTO);

    SaturationAndTotalVO getDeptDetailData(SaturationStatisticsDTO saturationStatisticsDTO);


    Page<SituationAnalysisVO> getUserDetailData(SaturationStatisticsDTO saturationStatisticsDTO);

    void export(SaturationStatisticsDTO saturationStatisticsDTO, HttpServletResponse response) throws IOException;

    /**
     * @create by yzs at 2023/5/15
     * @description:工时饱和度审核工时总数+滞后提交人天总数
     * @param: dto
     * @return: com.gok.pboot.pms.common.base.R<com.gok.pboot.pms.entity.vo.PaneSaturationAnalysisVO>
     */
    R<PaneSaturationAnalysisVO> analysisTotal(DailyPaperAnalysisDTO dto);

    /**
     * 工时饱和度查询滞后日报和审核日报详情
     * @param page 分页参数
     * @param dto 搜索条件
     * @return 分页日报明细
     */
    Page<DailyReviewProjectAuditPageVO> findBySaturation(
            Page<DailyReviewProjectAuditPageVO> page, DailyPaperAnalysisDTO dto
    );

    /**
     * 工时饱和度查询滞后日报和审核日报详情
     * @param dto 搜索条件
     * @return 分页日报明细
     */
    List<SaturationExportVO> exportBySaturation(
            DailyPaperAnalysisDTO dto
    );
}
