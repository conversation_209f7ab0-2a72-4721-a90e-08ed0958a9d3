package com.gok.pboot.pms.cost.service.impl;

import com.gok.pboot.pms.cost.entity.vo.SupplierInfoVO;
import com.gok.pboot.pms.cost.mapper.SupplierMapper;
import com.gok.pboot.pms.cost.service.ISupplierService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * OA供应商台账 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-18
 */
@Service
@RequiredArgsConstructor
public class SupplierServiceServiceImplI implements ISupplierService {

    private final SupplierMapper supplierMapper;

    /**
     * 根据条件获取供应商信息
     *
     * @return {@link List}<{@link SupplierInfoVO}>
     */
    @Override
    public List<SupplierInfoVO> getAllSupplierInfo() {
        return supplierMapper.getAllSupplierInfo();
    }

}
