package com.gok.pboot.pms.oa.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * OA 项目 DTO
 *
 * <AUTHOR>
 * @date 2025/08/05
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class OaProjectDTO {

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目编号
     */
    private String projectNo;

    /**
     * 项目经理工号
     */
    private String projectManagerWorkCode;

    /**
     * 项目状态
     */
    private Integer projectStatus;
}
