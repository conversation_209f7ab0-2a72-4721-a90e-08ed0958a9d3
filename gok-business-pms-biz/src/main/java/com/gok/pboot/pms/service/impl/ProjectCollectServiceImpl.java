package com.gok.pboot.pms.service.impl;

import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.Util.CollectionUtils;
import com.gok.pboot.pms.common.base.R;
import com.gok.pboot.pms.entity.ProjectCollect;
import com.gok.pboot.pms.mapper.ProjectCollectMapper;
import com.gok.pboot.pms.service.IProjectCollectService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 任务 服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-19
 */
@Service
@RequiredArgsConstructor
public class ProjectCollectServiceImpl implements IProjectCollectService {

    private final ProjectCollectMapper mapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<String> saveOrUpdate(ProjectCollect entity) {
        if (entity.getId() != null) {
            BaseBuildEntityUtil.buildUpdate(entity);
            mapper.updateById(entity);
        } else {
            BaseBuildEntityUtil.buildInsert(entity);
            mapper.insert(entity);
        }
        return R.ok("操作成功");
    }

    @Override
    public ProjectCollect getById(Long id) {
        return mapper.selectById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<String> batchSave(List<ProjectCollect> poList) {
        poList.forEach(BaseBuildEntityUtil::buildInsert);
        mapper.batchSave(poList);
        return R.ok("操作成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<String> batchUpdate(List<ProjectCollect> list) {
        list.forEach(BaseBuildEntityUtil::buildUpdate);
        mapper.batchUpdate(list);
        return R.ok("操作成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<String> batchDel(List<Long> list) {
        if (CollectionUtils.isEmpty(list)) {
            return R.failed("请选择删除数据");
        }
        mapper.batchDel(list);
        return R.ok("操作成功");
    }
}
