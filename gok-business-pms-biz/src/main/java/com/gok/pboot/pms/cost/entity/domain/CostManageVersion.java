package com.gok.pboot.pms.cost.entity.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import com.gok.pboot.pms.cost.enums.CostBudgetTypeEnum;
import com.gok.pboot.pms.enumeration.CostManageStatusEnum;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * 成本管理版本记录
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("cost_manage_version")
public class CostManageVersion extends BeanEntity<Long> {

    private static final long serialVersionUID = 1L;

    /**
     * 版本类型（0=目标管理，1=成本管理）
     * {@link com.gok.pboot.pms.cost.enums.CostManageVersionEnum}
     */
    private Integer versionType;

    /**
     * 版本状态（0=当前版本，1=历史版本）
     * {@link com.gok.pboot.pms.cost.enums.VersionStatusEnum}
     */
    private Integer versionStatus;

    /**
     * 版本名称
     */
    private String versionName;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 关联流程ID
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long requestId;

    /**
     * 关联流程名称
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String requestName;

    /**
     * 关联流程类型（0=A表，1=B表，2=合同会签，3=项目变更）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer requestType;

    /**
     * 成本预算类型（0=售前成本，1=A表成本，2=B表成本）
     * {@link CostBudgetTypeEnum}
     */
    private Integer costBudgetType;

    /**
     * 状态（0=未确认，1=已确认，2=已拒绝，3=草稿）
     * {@link CostManageStatusEnum}
     */
    private Integer status;

    /**
     * 拒绝理由
     */
    private String refuseReason;

    /**
     * 关联现金流版本ID
     */
    private Long cashPlanVersionId;

    /**
     * 关联现金流版本
     */
    private String cashPlanVersion;

    /**
     * 操作人id
     */
    private Long operatorId;

    /**
     * 操作人姓名
     */
    private String operatorName;

    /**
     * 操作人部门id
     */
    private Long operatorDeptId;

    /**
     * 操作人部门名称
     */
    private String operatorDeptName;

    /**
     * 操作人当前角色
     */
    private String operatorRole;

    /**
     * 关联售前版本ID
     */
    private Long preSaleVersionId;

    /**
     * 关联售前版本ID
     */
    private String preSaleVersionName;

    /**
     * 同步创建项目总成本标记
     * 0-未同步 1-已同步
     */
    private Integer generateTotalCost;

}
