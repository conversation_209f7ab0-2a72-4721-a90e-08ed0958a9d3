package com.gok.pboot.pms.enumeration;

import lombok.Getter;

/**
 * 风险状态枚举类
 *
 * <AUTHOR>
 * @since 2023-07-14
 **/
@Getter
public enum RiskStatusEnum implements ValueEnum<Integer> {

    /**
     * 开放
     */
    OPEN(0, "开放"),

    /**
     * 关闭
     */
    CLOSE(1, "关闭");

    /**
     * 值
     */
    private Integer value;

    /**
     * 名称
     */
    private String name;

    RiskStatusEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

}
