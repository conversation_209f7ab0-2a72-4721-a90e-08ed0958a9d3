package com.gok.pboot.pms.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 合同台账数据（OA同步）
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-02-23 10:46:40
 */
@Data
@TableName("contract_ledger")
public class ContractLedger implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 合同id
	 */
	@TableId
	private Long id;
	/**
	 * 
	 */
	private Integer requestid;
	/**
	 * 合同类别
	 */
	private Integer htlb;
	/**
	 * 合同细类
	 */
	private Integer htxl;
	/**
	 * 相关流程
	 */
	private String relateprocess;
	/**
	 * 备注
	 */
	private String remark;
	/**
	 * 合同名称
	 */
	private String htmc;
	/**
	 * 实际合同编号
	 */
	private String sjhtbh;
	/**
	 * 合同所属公司
	 */
	private Integer htssgs;
	/**
	 * 合同所属一级部门id
	 */
	private Long htssbm;
	/**
	 * 合同所属一级部门
	 */
	private String htssbmName;
	/**
	 * 合同所属分部id
	 */
	private Integer htssfb;
	/**
	 * 合同所属分部
	 */
	private String htssfbName;
	/**
	 * 合同等级
	 */
	private Integer htdj;
	/**
	 * 对方名称id
	 */
	private String khmc;
	/**
	 * 对方名称
	 */
	private String khmcName;
	/**
	 * 客户编号
	 */
	private String khbh;
	/**
	 * 联系人
	 */
	private String lxr;
	/**
	 * 联系方式
	 */
	private String lxfs;
	/**
	 * 联系地址
	 */
	private String lxdz;
	/**
	 * 客户方开户银行
	 */
	private String khfkhyx;
	/**
	 * 客户方账号
	 */
	private String khfzh;
	/**
	 * 客户方纳税人登记号
	 */
	private String khfnsrdjh;
	/**
	 * 相关文档
	 */
	private String relatedoc;
	/**
	 * 相关附件
	 */
	private String relateattachment;
	/**
	 * 相关附件图像文件id
	 */
	private Long relateattachmentImagefileid;
	/**
	 * 相关附件文件名
	 */
	private String relateattachmentImagefilename;
	/**
	 * 业务方向
	 */
	private Integer ywfx;
	/**
	 * 项目id
	 */
	private Long xmmc;
	/**
	 * 项目名称
	 */
	private String xmmcName;
	/**
	 * 业务类型
	 */
	private Integer ywlx;
	/**
	 * 合同金额
	 */
	private BigDecimal htje;
	/**
	 * 付款方式
	 */
	private Integer fkfs;
	/**
	 * 客户经理id
	 */
	private Long xmxsry;
	/**
	 * 客户经理
	 */
	private String xmxsryName;
	/**
	 * 合同起始日期
	 */
	private String htqsrq;
	/**
	 * 合同截止日期
	 */
	private String htjzrq;
	/**
	 * 合同附件
	 */
	private String htfj;
	/**
	 * 合同附件图像文件id
	 */
	private Long htfjImagefileid;
	/**
	 * 合同附件文件名
	 */
	private String htfjImagefilename;
	/**
	 * 合同附件（已盖章）
	 */
	private String htfjygz;
	/**
	 * 合同附件（已盖章）图像文件id
	 */
	private Long htfjygzImagefileid;
	/**
	 * 合同附件（已盖章）文件名
	 */
	private String htfjygzImagefilename;
	/**
	 * 合同编号
	 */
	private String htbh;
	/**
	 * 合同会签流程
	 */
	private Integer hthqlc;
	/**
	 * 合同会签流程相关人id
	 */
	private Long hthqlcNodeoperator;
	/**
	 * 合同状态
	 */
	private Integer htzt;
	/**
	 * 验收报告附件
	 */
	private String ysbg;
	/**
	 * 验收报告附件图像文件id
	 */
	private Long ysbgImagefileid;
	/**
	 * 验收报告附件文件名
	 */
	private String ysbgImagefilename;
	/**
	 * 关联进项流程
	 */
	private String gljxlc;
	/**
	 * 关联进项流程相关人id
	 */
	private Long gljxlcNodeoperator;
	/**
	 * 进项总金额
	 */
	private BigDecimal jxzje;
	/**
	 * 进项支出金额
	 */
	private BigDecimal jxzcje;
	/**
	 * 已收款金额
	 */
	private BigDecimal yskje;
	/**
	 * 已开票金额
	 */
	private BigDecimal ykpje;
	/**
	 * 区域归属
	 */
	private Integer qygs;
	/**
	 * 归属区域
	 */
	private Integer gsqy;
	/**
	 * 联系人电话
	 */
	private String lxrdh;
	/**
	 * 项目销售人员2id
	 */
	private String xmxsry2;
	/**
	 * 项目销售人员2
	 */
	private String xmxsry2Name;
	/**
	 * 中标通知书
	 */
	private Integer zbtzs;
	/**
	 * 中标通知书附件
	 */
	private String zbtzsfj;
	/**
	 * 中标通知书附件图像文件id
	 */
	private Long zbtzsfjImagefileid;
	/**
	 * 中标通知书附件文件名
	 */
	private String zbtzsfjImagefilename;
	/**
	 * 验收报告
	 */
	private Integer ysbga;
	/**
	 * 验收日期
	 */
	private String ysrq;
	/**
	 * 质保金收回情况
	 */
	private Integer zbjshqk;
	/**
	 * 质保金收回日期
	 */
	private String zbjshrq;
	/**
	 * 质保金情况
	 */
	private Integer zbdqk;
	/**
	 * 质保金金额
	 */
	private BigDecimal zbjje;
	/**
	 * 质保金到期时间
	 */
	private String zbjdqsj;
	/**
	 * 合同类型
	 */
	private Integer htlx;
	/**
	 * 项目毛利测算流程
	 */
	private Integer xmmlcslc;

    /**
     * 项目毛利测算流程名字
     */
    private String xmmlcslcName;
	/**
	 * 项目毛利测算流程相关人id
	 */
	private Long xmmlcslcNodeoperator;
	/**
	 * 项目负责人id
	 */
	private Long xmfzr;
	/**
	 * 项目负责人
	 */
	private String xmfzrName;
	/**
	 * 项目经理id
	 */
	private Long xmjl;
	/**
	 * 项目经理
	 */
	private String xmjlName;
	/**
	 * 实际合同签订日期
	 */
	private String sjhtqdrq;
	/**
	 * 原件数量
	 */
	private Integer yjshul;
	/**
	 * 项目结项申请流程
	 */
	private Integer xmjxsqlc;
	/**
	 * 项目结项申请流程相关人id
	 */
	private Long xmjxsqlcNodeoperator;
	/**
	 * 项目结项流程
	 */
	private String xmjxlc;
	/**
	 * 项目设备签收单附件
	 */
	private String xmsbqsdfj;
	/**
	 * 项目设备签收单附件图像文件id
	 */
	private Long xmsbqsdfjImagefileid;
	/**
	 * 项目设备签收单附件文件名
	 */
	private String xmsbqsdfjImagefilename;
	/**
	 * 是否涉及硬件采购
	 */
	private Integer sfsjyjcg;
	/**
	 * 变更/补充/解除/终止协议附件（已盖章）
	 */
	private String bcjczzxyfj;
	/**
	 * 变更/补充/解除/终止协议附件（已盖章）图像文件id
	 */
	private Long bcjczzxyfjImagefileid;
	/**
	 * 变更/补充/解除/终止协议附件（已盖章）文件名
	 */
	private String bcjczzxyfjImagefilename;
	/**
	 * 变更协议签订日期
	 */
	private String bgxyqdrq;
	/**
	 * 变更协议原件数量
	 */
	private Integer bgxyyjsl;
	/**
	 * 合同金额（含税）
	 */
	private BigDecimal htjehs;
	/**
	 * 合同金额(不含税)
	 */
	private BigDecimal htjebhs;
	/**
	 * 项目预计毛利
	 */
	private BigDecimal xmyjml;
	/**
	 * 项目预计毛利率
	 */
	private BigDecimal xmyjmll;
	/**
	 * 项目编号
	 */
	private String xmbh;
	/**
	 * 合同所属二级部门id
	 */
	private Long htssejbm;
	/**
	 * 合同所属二级部门
	 */
	private String htssejbmName;
	/**
	 * 中标通知书是否领取
	 */
	private Integer zbtzssflq;
	/**
	 * 中标通知书扫描件
	 */
	private String zbtzssmj;
	/**
	 * 中标通知书扫描件图像文件id
	 */
	private Long zbtzssmjImagefileid;
	/**
	 * 中标通知书扫描件文件名
	 */
	private String zbtzssmjImagefilename;
	/**
	 * 结算方式
	 */
	private Integer jsfs;
	/**
	 * 客户名称id
	 */
	private String khmcnew;
	/**
	 * 客户名称
	 */
	private String khmcnewName;
	/**
	 * 供应商名称id
	 */
	private String gysmc;
	/**
	 * 供应商名称
	 */
	private String gysmcName;
	/**
	 * 供应商编号
	 */
	private String gysbh;
	/**
	 * 是否多方协议
	 */
	private Integer sfdfxy;
	/**
	 * 收入类型
	 */
	private Integer srlx;
	/**
	 * 技术类型
	 */
	private Integer jslx;
	/**
	 * 交付形式
	 */
	private Integer jfxs;
	/**
	 * 业务板块
	 */
	private Integer ywbk;
	/**
	 * 项目类型
	 */
	private Integer xmlx;
	/**
	 * 待收款金额
	 */
	private BigDecimal dskje;
	/**
	 * 已开票比例
	 */
	private BigDecimal ykpbl;
	/**
	 * 待开票金额
	 */
	private BigDecimal dkpje;
	/**
	 * 已收款比例
	 */
	private BigDecimal yskbl;
	/**
	 * 回款申请单流程
	 */
	private Integer hksqdlc;
	/**
	 * 回款申请单流程相关人id
	 */
	private Long hksqdlcNodeoperator;
	/**
	 * 明细ID
	 */
	private String mxid;
	/**
	 * 对外采购类型
	 */
	private Integer dwcglx;
	/**
	 * 选择项目采购申请流程
	 */
	private Integer xzxmcgsqlc;
	/**
	 * 选择项目采购申请流程相关人id
	 */
	private Long xzxmcgsqlcNodeoperator;
	/**
	 * 项目所在地
	 */
	private Integer xmszd;
    /**
     * 项目所在地
     */
    private String xmszdName;
	/**
	 * 最终客户
	 */
	private String zzkh;
	/**
	 * 合同标的（交付物）
	 */
	private String htbdjfw;
	/**
	 * 交付地点
	 */
	private String jfdd;
	/**
	 * 包装运输方式
	 */
	private String bzysfs;
	/**
	 * 交付（服务）期限
	 */
	private String jffwqx;
	/**
	 * 验收条件
	 */
	private String ystj;
	/**
	 * 合同标的明细（附件）
	 */
	private String htbdmxfj;
	/**
	 * 合同标的明细（附件）图像文件id
	 */
	private Long htbdmxfjImagefileid;
	/**
	 * 合同标的明细（附件）文件名
	 */
	private String htbdmxfjImagefilename;
	/**
	 * 质保期
	 */
	private Integer zbq;
	/**
	 * 质保期情况说明
	 */
	private String zbqqksm;
	/**
	 * 供应商名称新id
	 */
	private String gysmcs;
	/**
	 * 供应商名称新
	 */
	private String gysmcsName;
	/**
	 * 创建人
	 */
	private String creator;
	/**
	 * 创建人ID
	 */
	private Long creatorId;
	/**
	 * 修改人
	 */
	private String modifier;
	/**
	 * 修改人ID
	 */
	private Long modifierId;
	/**
	 * 创建时间
	 */
	private Date ctime;
	/**
	 * 修改时间
	 */
	private Date mtime;
	/**
	 * 删除标识
	 */
	private Integer delFlag;


    /**
     * '供应商联系人'
     */
    private String gyslxr1;

    /**
     * 供应商联系方式
     */
    private String gysdh1;

    /**
     * 供应商联系地址
     */
    private String gysbgdzkyjdz;

    /**
     * 供应商开票开户银行
     */
    private String gyskpkhyx;

    /**
     * 供应商开户行账号
     */
    private String gyskpkhzh;


    /**
     * 客户联系人
     */
    private String khlxr1;

    /**
     * 客户联系方式
     */
    private String khlxdh1;

    /**
     * 客户联系地址
     */
    private String khkpdz;

    /**
     * 客户开票开户银行
     */
    private String khkpkhyx;

    /**
     * 客户开户行账号
     */
    private String khkpkhxzh;
    /**
     * '项目产值进度'
     */
    private String xmczjd;

}
