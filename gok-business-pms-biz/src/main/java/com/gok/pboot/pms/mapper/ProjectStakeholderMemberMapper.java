package com.gok.pboot.pms.mapper;


import com.gok.components.data.datascope.BaseMapper;
import com.gok.pboot.pms.common.base.R;
import com.gok.pboot.pms.entity.domain.ProjectStakeholderMember;
import com.gok.pboot.pms.entity.vo.ProjectStakeholderMemberVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 项目干系人-成员
 *
 * <AUTHOR>
 * @date 2023-07-11 17:05:26A
 */
@Mapper
public interface ProjectStakeholderMemberMapper extends BaseMapper<ProjectStakeholderMember> {

    /**
     * 查询项目成员列表
     *
     * @param projectId 项目id
     * @return {@link R}<{@link List}<{@link ProjectStakeholderMemberVO}>>
     */
    List<ProjectStakeholderMemberVO> getMemberByProjectId(@Param("projectId") Long projectId, @Param("roleType") Integer roleType);

    /**
     * 查询
     *
     * @param filter 查询参数Map
     * @return 数据列表
     */
    List<ProjectStakeholderMember> findList(@Param("filter") Map<String, Object> filter);

    /**
     * 根据项目组成员用户ID查询项目ID列表
     *
     * @param memberUserId 项目组成员用户ID
     * @return 项目ID列表
     */
    List<Long> findProjectIdByMemberId(Long memberUserId);

    /**
     * 根据项目ID和用户ID查询
     *
     * @param projectId 项目ID
     * @param memberId  成员用户ID
     * @return 数据列表
     */
    List<ProjectStakeholderMember> findByProjectIdAndMemberId(
            @Param("projectId") Long projectId,
            @Param("memberId") Long memberId
    );

    /**
     * 根据项目ID和用户ID查询
     *
     * @param memberId 成员用户ID
     * @return 数据列表
     */
    List<ProjectStakeholderMember> findByMemberId(Long memberId);

    /**
     * 查询项目成员列表
     *
     * @param projectId 项目id
     * @return {@link R}<{@link List}<{@link ProjectStakeholderMemberVO}>>
     */
    List<ProjectStakeholderMemberVO> getMembersByProjectId(@Param("projectId") Long projectId);

    /**
     * 霹雳同步项目干系人（项目任务参与人调用）
     *
     * @param list 项目干系人列表
     * @return int
     */
    int batchSync(@Param("list") List<ProjectStakeholderMember> list);

    /**
     * 更新角色类型
     *
     * @param dto 数据请求对象
     * @return int
     */
    int updateRoleType(@Param("dto") ProjectStakeholderMember dto);

    /**
     * 更新备注
     *
     * @param dto 数据请求对象
     * @return int
     */
    int updateRemark(@Param("dto") ProjectStakeholderMember dto);

    /**
     * 更新备注
     *
     * @param dto 数据请求对象
     * @return int
     */
    int updateRemarkByUserId(@Param("dto") ProjectStakeholderMember dto);

    /**
     * 通过项目id更新同步OA类型
     *
     * @param projectId 项目id
     * @param userIds   已同步用户id列表
     * @return int
     */
    int updateSyncOaTypeByProjectId(@Param("projectId") Long projectId, @Param("userIds") List<Long> userIds);

    /**
     * 获取已经同步过oa的用户id列表
     *
     * @param projectId 项目id
     * @return {@link List}<{@link Long}>
     */
    List<Long> findSyncedOaUserIdsByProjectId(@Param("projectId") Long projectId);

    /**
     * 获取没有同步过oa的用户id列表
     *
     * @param projectId 项目id
     * @return {@link List}<{@link Long}>
     */
    List<Long> findUnSyncOaUserIdsByProjectId(@Param("projectId") Long projectId);

    /**
     * 根据id列表批量物理删除
     *
     * @param ids id列表
     * @return int
     */
    int logicalDelByIds(@Param("ids") List<Long> ids);

    /**
     * 是否操作助理
     *
     * @param projectId 项目id
     * @param userId    用户id
     * @return boolean
     */
    boolean isOperationsAssistant(@Param("projectId") Long projectId, @Param("userId") Long userId);

    /**
     * 批量新增
     *
     * @param list 干系人列表
     * @return int
     */
    int batchSave(@Param("list") List<ProjectStakeholderMember> list);

    List<ProjectStakeholderMember> findToSyncDeptName();

    /**
     * 对比项目信息表查询所有新增的项目经理、客户经理、售前经理、业务经理
     *
     * @return
     */
    List<ProjectStakeholderMember> getAddMembersByProject();

    /**
     * 对比项目信息表查询所有删除的项目经理、客户经理、售前经理、业务经理
     *
     * @return
     */
    List<Long> getDelMembersByProject();

    /**
     * 获取当前项目用户角色
     *
     * @param projectId 项目ID
     * @param id        用户ID
     * @return {@link Integer}
     */
    Integer getOperatorRole(@Param("projectId") Long projectId, @Param("id") Long id);

    /**
     * 获取项目利益相关者成员 Volist
     *
     * @return {@link List }<{@link ProjectStakeholderMemberVO }>
     */

    List<ProjectStakeholderMemberVO> getProjectStakeholderMemberVOList();
}
