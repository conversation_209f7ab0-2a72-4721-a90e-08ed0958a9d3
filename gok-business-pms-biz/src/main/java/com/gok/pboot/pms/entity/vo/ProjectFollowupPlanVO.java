package com.gok.pboot.pms.entity.vo;

import com.gok.module.file.entity.SysFile;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 项目后续计划表 VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectFollowupPlanVO {

    /**
     * id
     */
    private Long id;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 工作项
     */
    private String workItem;

    /**
     * 负责人id集合 以,分割
     */
    private String manager;

    /**
     * 负责人名称集合 以,分割
     */
    private String managerName;

    /**
     * 预计投入周期
     */
    private Integer planCycle;

    /**
     * 预计工时(人天)
     */
    private BigDecimal planWorkHours;

    /**
     * 预计项目管理费用
     */
    private BigDecimal projectManageCostEst;

    /**
     * 客户侧或外部对接人
     */
    private String clientExternalContact;

    /**
     * 交接或说明文档id集合 以,分割
     */
    private String handoverDescDocs;

    /**
     * 交接或说明文档集合
     */
    private List<SysFile> handoverDescDocsList;

} 