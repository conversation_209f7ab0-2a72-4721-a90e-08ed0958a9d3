/*
 * Copyright 2013-2014 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.gok.pboot.pms.Util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collector;
import java.util.stream.Collectors;


/**
 * 泛型工具类
 *
 *
 */
@SuppressWarnings({"rawtypes","unchecked"})
@Slf4j
public class CollectionUtils extends org.apache.commons.collections.CollectionUtils{

	private CollectionUtils() {
		throw new IllegalStateException("Utility class could not be instantiated");
	}

	/**
	 * 提取集合中的对象的属性(通过Getter函数), 组合成Map.
	 *
	 * @param collection 来源集合.
	 * @param keyPropertyName 要提取为Map中的Key值的属性名.
	 * @param valuePropertyName 要提取为Map中的Value值的属性名.
	 */
	public static Map extractToMap(Collection collection, String keyPropertyName, String valuePropertyName) {
		Map map = new HashMap();

		try {
			for (Object obj : collection) {
				map.put(PropertyUtils.getProperty(obj, keyPropertyName), PropertyUtils.getProperty(obj, valuePropertyName));
			}
		} catch (Exception e) {
			log.error(null, e);
		}

		return map;
	}

	/**
	 * 提取集合中的对象的属性(通过Getter函数), 组合成List.
	 *
	 * @param collection 来源集合.
	 * @param propertyName 要提取的属性名.
	 *
	 * @return List
	 */
	public static <T> List<T> extractToList(Collection collection, String propertyName) {

		return extractToList(collection,propertyName,false);
	}

	/**
	 * 提取集合中的对象的属性(通过Getter函数), 组合成List.
	 *
	 * @param collection 来源集合.
	 * @param propertyName 要提取的属性名.
	 * @param ignoreEmptyValue 是否过滤null值和""值
	 *
	 * @return List
	 */
	public static <T> List<T> extractToList(Collection collection, String propertyName,boolean ignoreEmptyValue) {
		if (collection == null) {
			return null;
		}
		List list = new ArrayList();

		try {
			for (Object obj : collection) {
				T value = (T) PropertyUtils.getProperty(obj, propertyName);
				boolean ignore = ignoreEmptyValue && value == null || "".equals(value.toString());
				if (ignore) {
					continue;
				}
				list.add(PropertyUtils.getProperty(obj, propertyName));
			}
		} catch (Exception e) {
			log.error(null, e);
		}

		return list;
	}

	/**
	 * 提取集合中的对象的属性(通过Getter函数), 组合成由分割符分隔的字符串.
	 *
	 * @param collection 来源集合.
	 * @param propertyName 要提取的属性名.
	 * @param separator 分隔符.
	 */
	public static String extractToString(Collection collection, String propertyName, String separator) {
		List list = extractToList(collection, propertyName);
		return StringUtils.join(list, separator);
	}

	/**
	 * 去除空集合
	 * @param list  String集合
	 * @return   除空后的集合
	 */
	public static List<String> removeBlank(List<String> list) {
		return list.stream().filter(a-> StringUtils.isNoneBlank(a)).collect(Collectors.toList());
	}

	/**
	 * 处理java8 groupingBy key不能为空的问题
	 * @param classifier
	 * @param <T>
	 * @param <A>
	 * @return
	 */
	public static <T, A> Collector<T, ?, Map<A, List<T>>> groupingBy_WithNullKeys(Function<? super T, ? extends A> classifier) {
		return Collectors.toMap(classifier, Collections::singletonList,
				(List<T> oldList, List<T> newEl) -> {
					List<T> newList = new ArrayList<>(oldList.size() + 1);
					newList.addAll(oldList);
					newList.addAll(newEl);
					return newList;
				});
	}
}
