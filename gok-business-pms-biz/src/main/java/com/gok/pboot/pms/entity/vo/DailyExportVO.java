package com.gok.pboot.pms.entity.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 日报提交一览表导出Vo
 *
 * <AUTHOR>
 * @since 2023-08-22
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class DailyExportVO {

    /**
     * 姓名
     */
    @ExcelProperty(index = 1, value = "姓名")
    private String name;

    /**
     * 部门
     */
    @ExcelProperty(index = 2, value = "部门")
    private String deptName;

    /**
     * 状态
     */
    @ExcelProperty(index = 3, value = "状态")
    private String personnelStatusName;

    /**
     * 日报集合
     */
    private List<DailyPaperExportVO> dailyPapers;

}
