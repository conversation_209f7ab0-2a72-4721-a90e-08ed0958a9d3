package com.gok.pboot.pms.config;

import com.gok.components.data.handler.SecurityAspect;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * - 企业微信相关配置 -
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Configuration
public class PreAuthAspectConfig {
    /**
     * 注入权限切面，用于获取注解上的menuCode
     * @return
     */
    @Bean
    public SecurityAspect securityAspect(){
        return new SecurityAspect();
    }
}
