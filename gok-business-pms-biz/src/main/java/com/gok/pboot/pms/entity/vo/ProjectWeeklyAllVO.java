package com.gok.pboot.pms.entity.vo;


import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 项目周报表Vo
 * 
 * <AUTHOR>
 * @LocalDateTime 2023-07-11 17:05:26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectWeeklyAllVO {

	/**
	 * ID id
	 */
	@ExcelIgnore
	private Long id;
	/**
	 * 项目id
	 */
	@ExcelIgnore
	private Long projectId;

	/**
	 * '项目名称'
	 */
	@ExcelProperty({"项目名称"})
	private String projectName;

	/**
	 * 项目状态值
	 */
	@ExcelProperty({"项目状态"})
	private String projectStatusName;

	/**
	 * '业务归属部门'
	 */
	@ExcelProperty({"业务归属部门"})
	private String projectDepartment;

	/**
	 * 汇报周期
	 */
	@ExcelProperty({"汇报周期"})
	private String reportStartEnd;
	/**
	 * 汇报周期-开始
	 */
	@ExcelIgnore
	private LocalDate reportStart;
	/**
	 * 汇报周期-结束
	 */
	@ExcelIgnore
	private LocalDate reportEnd;

	/**
	 * 汇报人id
	 */
	@ExcelIgnore
	private Long reportUserId;
	/**
	 * 汇报人
	 */
	@ExcelProperty({"汇报人"})
	private String reportUser;
	/**
	 * 本周进展情况
	 */
	@ExcelProperty({"本周进展情况"})
	private String currentWorkProgress;
	/**
	 * 当前项目进度
	 */
	@ExcelProperty({"当前进度"})
	private String currentProgress;
	/**
	 * 当期新增工时（人天）
	 */
	@ExcelProperty({"当期新增工时（人天）"})
	private BigDecimal currentHours;
	/**
	 * 累计工时（人天）
	 */
	@ExcelProperty({"累计工时（人天）"})
	private BigDecimal totalHours;
	/**
	 * 下周工作计划
	 */
	@ExcelProperty({"下周工作计划"})
	private String nextWorkPlan;
	/**
	 * 需配合支撑事项
	 */
	@ExcelProperty({"需配合支撑事项"})
	private String needSupportItem;
	/**
	 * 文件id集合
	 */
	@ExcelIgnore
	private String docIds;

	/**
	 * 项目风险
	 */
	@ExcelProperty({"项目风险"})
	private String weeklyRisk;

	/**
	 * 备注
	 */
	@ExcelProperty({"备注"})
	private String remark;

}
