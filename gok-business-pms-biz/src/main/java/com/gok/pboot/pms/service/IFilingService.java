package com.gok.pboot.pms.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.entity.vo.FilingFindPageVO;

import java.util.Map;

/**
 * <p>
 * 归档 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-29
 */
public interface IFilingService {

    /**
     * 自上线后开始，每月1号0点自动生成本月数据条
     * @param para
     * @return
     */
    ApiResult<String> filingTask(String para);

    /**
     * 归档
     *
     * @param id
     * @return ApiResult
     */
    ApiResult file(Long id);

    /**
     * 取消归档
     *
     * @param id
     * @return ApiResult
     */
    ApiResult cancelFile(Long id);

    /**
     * 分页查询
     *
     * @param pageRequest  分页请求
     * @param filter  过滤条件
     * @return 分页记录
     */
    Page<FilingFindPageVO> FilingFindPageVO(PageRequest pageRequest, Map
            <String, Object> filter);


}
