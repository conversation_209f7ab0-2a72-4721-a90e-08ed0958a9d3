package com.gok.pboot.pms.eval.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.pboot.pms.eval.entity.domain.EvalUserRole;
import com.gok.pboot.pms.eval.entity.vo.EvalUserRoleVO;
import com.gok.pboot.pms.eval.mapper.EvalUserRoleMapper;
import com.gok.pboot.pms.eval.service.IEvalUserRoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 项目评价用户角色服务实现类
 *
 * <AUTHOR>
 * @create 2025/05/12
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class EvalUserRoleServiceImpl extends ServiceImpl<EvalUserRoleMapper, EvalUserRole> implements IEvalUserRoleService {

    @Override
    public List<EvalUserRoleVO> findAll() {
        List<EvalUserRole> userRoles = baseMapper.findAll();
        if (CollUtil.isEmpty(userRoles)) {
            return ListUtil.empty();
        }
        return userRoles.stream()
                .map(EvalUserRoleVO::convertToVO)
                .collect(Collectors.toList());
    }

}