package com.gok.pboot.pms.cost.entity.vo;

import com.gok.module.file.entity.SysFile;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CostManageTargetVO {

    /**
     * ID
     */
    private Long id;

    /**
     * 版本ID
     */
    private Long versionId;

    /**
     * 版本名称
     */
    private String versionName;

    /**
     * 关联流程ID
     */
    private Long requestId;

    /**
     * 关联流程名称
     */
    private String requestName;

    /**
     * 关联流程状态（0=未提交，1=审批中，1=已归档）
     */
    private Integer requestStatus;

    /**
     * 关联流程状态名称
     */
    private String requestStatusName;

    /**
     * 状态（0=未确认，1=已确认，2=已拒绝）
     */
    private Integer status;

    /**
     * 状态名称（0=未确认，1=已确认，2=已拒绝）
     */
    private String statusName;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 项目需求
     */
    private String projectRequirements;

    /**
     * 交付要求
     */
    private String deliveryRequirements;

    /**
     * 交付物
     */
    private String deliveryItems;

    /**
     * 交付期限
     */
    private String deliveryDeadline;

    /**
     * 交付地点
     */
    private String deliveryPlace;

    /**
     * 质保期(月)
     */
    private String warrantyPeriod;

    /**
     * 保密要求
     */
    private String secrecyRequirements;

    /**
     * 其他要求
     */
    private String otherRequirements;

    /**
     * 其他要求附件id，多个逗号隔开
     */
    private String detailFiles;

    /**
     * 附件信息列表
     */
    private List<SysFile> fileList;

}