package com.gok.pboot.pms.common.validate.validator;

import com.gok.pboot.pms.common.validate.constraint.SelectMonth;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.regex.Pattern;

/**
 * 选择月份校验器
 *
 * <AUTHOR>
 * @date 2022/8/25
 */
public class SelectMothValidator implements ConstraintValidator<SelectMonth, String> {

    /**yyyy-MM**/
    private static final String MONTH_REGEX = "^([1-9]\\d{3}-)(([0][1-9])|([1][0-2]))$";


    @Override
    public void initialize(SelectMonth constraintAnnotation) {
        ConstraintValidator.super.initialize(constraintAnnotation);
    }

    @Override
    public boolean isValid(String month, ConstraintValidatorContext constraintValidatorContext) {
        if (month == null){
            return false;
        }
        return Pattern.matches(MONTH_REGEX, month);
    }

}
