package com.gok.pboot.pms.entity.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 销售推送
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SalesReceiptPushVO {

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键id")
    private Long id;

    /**
     * 一级部门id
     */
    @ApiModelProperty(value = "一级部门id")
    private Long firstDepartmentId;

    /**
     * 项目销售人员ID
     */
    @ApiModelProperty(value = "项目销售人员ID")
    private Long salesmanUserId;

    /**
     * 项目销售人员
     */
    @ApiModelProperty(value = "项目销售人员")
    private String salesmanUserName;

    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
    private Long contractId;

    /**
     * 合同编码
     */
    @ApiModelProperty(value = "合同编码")
    private String contractCode;


    /**
     * 款项名称
     */
    @ApiModelProperty(value = "款项名称")
    private String paymentName;

    /**
     * 预计收(付)款日期
     */
    @ApiModelProperty(value = "预计收(付)款日期")
    private String expectedDate;

    /**
     * 合同金额
     */
    @ApiModelProperty(value = "合同金额")
    private BigDecimal contractMoney;

}
