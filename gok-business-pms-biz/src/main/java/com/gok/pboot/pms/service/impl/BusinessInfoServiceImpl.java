package com.gok.pboot.pms.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.Util.BcpLoggerUtils;
import com.gok.pboot.pms.Util.PageUtils;
import com.gok.pboot.pms.Util.PmsDictUtil;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.constant.FunctionConstants;
import com.gok.pboot.pms.entity.domain.BusinessContact;
import com.gok.pboot.pms.entity.domain.PmsDictItem;
import com.gok.pboot.pms.entity.domain.PmsDictItemMultiLevel;
import com.gok.pboot.pms.entity.domain.ProjectFile;
import com.gok.pboot.pms.entity.dto.BusinessInfoDTO;
import com.gok.pboot.pms.entity.dto.BusinessProgressDTO;
import com.gok.pboot.pms.entity.vo.*;
import com.gok.pboot.pms.enumeration.FileAssociationTypeEnum;
import com.gok.pboot.pms.enumeration.LogContentEnum;
import com.gok.pboot.pms.handler.perm.PmsRetriever;
import com.gok.pboot.pms.mapper.BusinessInfoMapper;
import com.gok.pboot.pms.mapper.ProjectFileMapper;
import com.gok.pboot.pms.oa.OaUtil;
import com.gok.pboot.pms.service.IBusinessInfoService;
import com.google.common.collect.HashMultimap;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 商机管理 Service
 *
 * <AUTHOR>
 * @date 2023/11/21
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BusinessInfoServiceImpl implements IBusinessInfoService {

    private final BusinessInfoMapper businessInfoMapper;
    private final ProjectFileMapper projectFileMapper;

    private final PmsRetriever pmsRetriever;

    private final OaUtil oaUtil;
    private final PmsDictUtil pmsDictUtil;
    private final BcpLoggerUtils bcpLoggerUtils;

    @Override
    public ApiResult<List<PmsDictItem>> findDictItemByDictType(String dictType) {
        List<PmsDictItem> result = pmsDictUtil.findDictItemByDictType(dictType);
        return ApiResult.success(result);
    }

    @Override
    public ApiResult<List<PmsDictItemMultiLevel>> findDictItemMultiLevelByDictType(String dictType) {
        List<PmsDictItemMultiLevel> result = pmsDictUtil.findDictItemMultiLevelByDictType(dictType);
        return ApiResult.success(result);
    }

    @Override
    public ApiResult<Object> getProgressFile(Long requestId) {
        // 获取流程对应的相关人id
        BusinessProgressVO vo = businessInfoMapper.findBusinessProgressByRequestId(requestId);
        if (vo == null || vo.getNodeoperator() == null) {
            return ApiResult.success(ImmutableList.of());
        }
        Long userId = vo.getNodeoperator();

        // 请求OA，获取流程相关资源
        List<OaFileVo> resourcesData = oaUtil.getResourcesData(String.valueOf(requestId), String.valueOf(userId));
        return ApiResult.success(resourcesData);
    }

    @Override
    public Integer countByUnit(Long unitId, Integer businessStatus) {
        return businessInfoMapper.countByUnitId(unitId, businessStatus);
    }

    @Override
    public ApiResult<Map<String, Collection<PmsDictItem>>> findDictMapByTypeList(String dictTypes) {
        Map<String, Collection<PmsDictItem>> result = pmsDictUtil.findDictMapByTypeList(dictTypes);
        return ApiResult.success(result);
    }

    /**
     * 商机台账 - 分页
     *
     * @param dto 传入参数
     * @return {@link ApiResult}<{@link Page}<{@link BusinessInfoVO}>>
     */
    @Override
    public ApiResult<Page<BusinessInfoVO>> findBusinessInfoPage(BusinessInfoDTO dto) {
        Map<String, Object> filter = Maps.newHashMap();
        // 将实体类转换成Map过滤条件
        convertCondition(filter, dto);
        // 获取用户权限范围内的商机
        List<Long> businessIdsAvailable = pmsRetriever.getBusinessIdsAvailable(filter);
        Page<BusinessInfoVO> result = Page.of(dto.getPageNumber(), dto.getPageSize());
        if (!"all".equals(filter.get("scope")) && businessIdsAvailable.isEmpty()) {
            return ApiResult.success(PageUtils.mapTo(result, x -> null));
        }
        filter.put(PmsRetriever.BUSINESS_AVAILABLE_KEY, businessIdsAvailable);
        result = businessInfoMapper.findBusinessInfoVOPage(result, filter);
        List<BusinessInfoVO> records = result.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return ApiResult.success(result);
        }
        // 更新响应字段
        updateBusinessInfoVoParam(records);
        // 返回结果
        return ApiResult.success(result);
    }

    /**
     * 前端参数转换成Map过滤条件
     *
     * @param filter 过滤条件
     * @param dto    前端参数
     */
    private void convertCondition(Map<String, Object> filter, BusinessInfoDTO dto) {
        // 使用反射获取所有字段
        Field[] fields = BusinessInfoDTO.class.getDeclaredFields();
        // 遍历字段
        for (Field field : fields) {
            // 确保字段可以被访问
            field.setAccessible(true);
            Object value = null;
            try {
                // 获取字段值
                value = field.get(dto);
            } catch (IllegalAccessException e) {
                throw new RuntimeException(e);
            }
            // 如果字段的值不为空，则加入到filter中
            if (value != null) {
                filter.put(field.getName(), value);
            }
        }
    }

    @Override
    public List<BusinessInfoVO> exportBusinessInfo(BusinessInfoDTO dto) {
        Map<String, Object> filter = Maps.newHashMap();
        // 将实体类转换成Map过滤条件
        convertCondition(filter, dto);
        // 获取用户权限范围内的商机
        List<Long> businessIdsAvailable = pmsRetriever.getBusinessIdsAvailable(filter);
        if (!"all".equals(filter.get("scope")) && businessIdsAvailable.isEmpty()) {
            return Collections.emptyList();
        }
        filter.put(PmsRetriever.BUSINESS_AVAILABLE_KEY, businessIdsAvailable);
        List<BusinessInfoVO> resultList = businessInfoMapper.findBusinessInfoVO(filter);

        if (resultList.isEmpty()) {
            return resultList;
        }
        updateBusinessInfoVoParam(resultList);
        //导出【{}】条商机
        bcpLoggerUtils.log(FunctionConstants.BUSINESS_OPPORTUNITY_LEDGER, LogContentEnum.EXPORT_BUSINESS_OPPORTUNITIES, resultList.size());
        return resultList;
    }

    @Override
    public ApiResult<BusinessInfoVO> findOne(Long id) {
        // 查询商机详情
        BusinessInfoVO businessInfoVO = businessInfoMapper.findOne(id);
        if (ObjectUtils.isEmpty(businessInfoVO)) {
            return ApiResult.success(new BusinessInfoVO());
        }
        // 更新响应字段
        List<BusinessInfoVO> records = ImmutableList.of(businessInfoVO);
        updateBusinessInfoVoParam(records);
        BusinessInfoVO result = records.get(0);

        return ApiResult.success(result);
    }

    @Override
    public ApiResult<List<BusinessContact>> findContact(Long businessId) {
        List<BusinessContact> result = businessInfoMapper.findContact(businessId);

        // 获取数据字典枚举
        HashMultimap<String, PmsDictItem> dictItemMap = pmsDictUtil.getPmsDictItemMap();

        // 对联系人人员类型进行枚举值赋值
        result.forEach(r -> BusinessContact.updateResultParam(dictItemMap, r));
        return ApiResult.success(result);
    }

    @Override
    public ApiResult<List<String>> findAllProjectLocation() {
        List<String> result = businessInfoMapper.findAllProjectLocation();
        return ApiResult.success(result);
    }

    @Override
    public ApiResult<Page<BusinessProgressVO>> findBusinessProgressPage(BusinessProgressDTO dto) {
        // 分页查询商机进展
        Page<BusinessProgressVO> result = Page.of(dto.getPageNumber(), dto.getPageSize());
        result = businessInfoMapper.findBusinessProgressPage(result, dto);
        List<BusinessProgressVO> records = result.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return ApiResult.success(result);
        }
        // 更新响应字段
        updateBusinessProgressVO(records);
        // 返回
        return ApiResult.success(result);
    }

    @Override
    public List<BusinessProgressVO> exportBusinessProgress(BusinessProgressDTO dto) {
        List<BusinessProgressVO> resultList = businessInfoMapper.findBusinessProgress(dto);

        if (resultList.isEmpty()) {
            return resultList;
        }
        updateBusinessProgressVO(resultList);
        //导出【{}】条商机进展记录
        bcpLoggerUtils.log(FunctionConstants.BUSINESS_OPPORTUNITY_PROGRESS_RECORD, LogContentEnum.EXPORT_BUSINESS_OPPORTUNITY_PROGRESS_RECORDS, resultList.size());
        return resultList;
    }

    @Override
    public ApiResult<BusinessProgressDetailsVO> findOneProgress(Long id) {
        // 查询进度表
        BusinessProgressDetailsVO result = businessInfoMapper.findOneProgress(id);
        if (ObjectUtils.isEmpty(result)) {
            return ApiResult.success(new BusinessProgressDetailsVO());
        }

        Long businessId = result.getBusinessId();
        HashMultimap<String, PmsDictItem> dictItemMap = pmsDictUtil.getPmsDictItemMap();
        HashMultimap<String, PmsDictItem> subItemMap = pmsDictUtil.getPmsSubItemMap();
        // 获取商机信息
        BusinessInfoVO businessInfo = businessInfoMapper.findOneByProjectId(businessId);
        if (businessInfo == null) {
            return ApiResult.success(result);
        }
        // 获取进度文件列表
        ProjectFile fileRequest = new ProjectFile();
        fileRequest.setProjectId(businessId);
        fileRequest.setAssociationType(FileAssociationTypeEnum.BUSINESS_PROGRESS.getValue());
        List<ProjectFile> projectFiles = projectFileMapper.selectListByParams(fileRequest);
        // 处理返回数据
        BusinessInfoVO.updateResultParam(dictItemMap, subItemMap, null, businessInfo);
        BusinessProgressDetailsVO.updateResultParam(dictItemMap, result);
        result.of(businessInfo, projectFiles);

        return ApiResult.success(result);
    }

    @Override
    public ApiResult<List<BusinessProgressVO>> findProgressGroupByBusinessId(Long id) {
        // 获取商机id对应的项目id
        BusinessInfoVO businessInfoVO = businessInfoMapper.findOne(id);
        if (businessInfoVO == null || businessInfoVO.getProjectId() == null) {
            return ApiResult.success(new ArrayList<>());
        }
        List<BusinessProgressVO> result = businessInfoMapper.findProgressGroupByBusinessId(businessInfoVO.getId());
        if (CollectionUtils.isEmpty(result)) {
            return ApiResult.success(result);
        }
        // 更新响应字段
        updateBusinessProgressVO(result);
        return ApiResult.success(result);
    }

    private void updateBusinessProgressVO(List<BusinessProgressVO> result) {
        // 获取数据字典枚举
        HashMultimap<String, PmsDictItem> dictItemMap = pmsDictUtil.getPmsDictItemMap();
        // 更新返回值参数
        result.forEach(r -> BusinessProgressVO.updateResultParam(dictItemMap, r));
    }

    @Override
    public ApiResult<List<BusinessDataLogVO>> findDataLog(Long id) {
        Long projectId = null;
        // 获取商机id对应的项目id
        BusinessInfoVO businessInfoVO = businessInfoMapper.findOne(id);
        if (ObjectUtils.isNotEmpty(businessInfoVO)) {
            projectId = businessInfoVO.getProjectId();
        }
        List<BusinessDataLogVO> result = businessInfoMapper.findDataLog(id, projectId);

        // 处理返回值
        if (CollectionUtil.isNotEmpty(result)) {
            result.forEach(BusinessDataLogVO::updateResultParam);
        }

        return ApiResult.success(result);
    }

    private void updateBusinessInfoVoParam(List<BusinessInfoVO> records) {
        // 获取数据字典枚举
        HashMultimap<String, PmsDictItem> dictItemMap = pmsDictUtil.getPmsDictItemMap();
        HashMultimap<String, PmsDictItem> subItemMap = pmsDictUtil.getPmsSubItemMap();
        // 获取联系人数量
        List<Long> businessIds = records.stream().map(BusinessInfoVO::getId).distinct().collect(Collectors.toList());
        List<BusinessOfContactCountVo> contactCountList = businessInfoMapper.getContactCount(businessIds);
        Map<Long, Integer> contactCountMap = contactCountList.stream().collect(Collectors.toMap(BusinessOfContactCountVo::getBusinessId, BusinessOfContactCountVo::getContactCount));

        // 更新数据
        records.forEach(r -> BusinessInfoVO.updateResultParam(dictItemMap, subItemMap, contactCountMap, r));

        // 查询对应最新的商机进展，进行数据填充（若有）
        List<BusinessProgressVO> newProgress = businessInfoMapper.findNewProgressByBusinessIds(businessIds);
        Map<Long, BusinessProgressVO> businessIdToProgressMap = newProgress.stream().collect(Collectors.toMap(BusinessProgressVO::getBusinessId, entity -> entity, (a, b) -> a));
        records.forEach(r -> {
            Long businessId = r.getId();
            BusinessProgressVO vo = businessIdToProgressMap.get(businessId);

            if (ObjectUtils.isNotEmpty(vo)) {
                r.setBusinessProgress(vo.getBusinessProgress());
                r.setNextStepForwardPlan(vo.getNextStepForwardPlan());
                r.setNextStepPlanFtime(vo.getNextStepPlanFtime());
                r.setYddwtjsxdzc(vo.getYddwtjsxdzc());
                r.setBusinessUpdateDate(vo.getArchiveTime());
            } else {
                r.setBusinessUpdateDate(r.getBusinessCtime());
            }
        });
    }
}
