package com.gok.pboot.pms.cost.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 项目现金流计划表
 *
 * <AUTHOR> generated
 * @date 2024-03-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cost_cash_plan")
public class CostCashPlan extends BeanEntity<Long> {

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 版本ID
     */
    private Long versionId;

    /**
     * 时间
     */
    private Integer timeMonth;

    /**
     * 计划月份 格式：yyyy-MM
     */
    private String planMonth;

    /**
     * 流入预测-当月回款
     */
    private BigDecimal monthIncome;

    /**
     * 流出预测-人工成本
     */
    private BigDecimal laborCost;

    /**
     * 流出预测-费用报销
     */
    private BigDecimal expenseCost;

    /**
     * 流出预测-外采支出
     */
    private BigDecimal outsourcingCost;

    /**
     * 备注
     */
    private String remark;

}