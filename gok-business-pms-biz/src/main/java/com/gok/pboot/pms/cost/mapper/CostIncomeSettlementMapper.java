package com.gok.pboot.pms.cost.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.cost.entity.domain.CostIncomeSettlement;
import com.gok.pboot.pms.cost.entity.dto.CostIncomeSettlementListDTO;
import com.gok.pboot.pms.cost.entity.vo.CostIncomeSettlementVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface CostIncomeSettlementMapper extends BaseMapper<CostIncomeSettlement> {

    /**
     * 查询收入结算汇总
     *
     * @param dto
     * @return
     */
    List<CostIncomeSettlementVO> selList(@Param("dto") CostIncomeSettlementListDTO dto);

    /**
     * 批量删除
     * @param idList
     */
    void delByIds(@Param("idList") List<Long> idList);

    /**
     * 批量更新
     * @param list
     */
    void batchUpdate(@Param("updateEntries")List<CostIncomeSettlement> list);
}