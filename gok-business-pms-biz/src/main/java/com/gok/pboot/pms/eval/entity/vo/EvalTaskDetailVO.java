package com.gok.pboot.pms.eval.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gok.pboot.pms.Util.DateUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 工单评价详情
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class EvalTaskDetailVO {
    //工单信息
    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 工单负责人id
     */
    private Long managerId;

    /**
     * 工单负责人
     */
    private String managerName;

    /**
     * 工单名称
     */
    private String taskName;
    /**
     * 工单类型（0=售前支撑，1=售后交付）
     *
     * @see com.gok.pboot.pms.enumeration.ProjectTaskKindEnum
     */
    private Integer taskType;

    /**
     * 工单类型名称
     */
    private String taskTypeName;

    /**
     * 开始日期
     */
    @JsonFormat(pattern = DateUtil.SIMPLE_DATE_FORMAT, timezone = DateUtil.GMT_8)
    private LocalDate startDate;

    /**
     * 结束日期
     */
    @JsonFormat(pattern = DateUtil.SIMPLE_DATE_FORMAT, timezone = DateUtil.GMT_8)
    private LocalDate endDate;

    // 评价信息

    /**
     * 评价状态（0=未评价，1=已评价）
     */
    private Integer evaluationStatus;

    /**
     * 任务交付质量评分（1-4）
     */
    private Integer qualityScore;

    /**
     * 任务交付质量评分备注
     */
    private String qualityRemark;

    /**
     * 任务按时交付评分（1-4）
     */
    private Integer timelinessScore;

    /**
     * 任务按时完成评分备注
     */
    private String timelinessRemark;

    /**
     * 工作配合度评分（1-4）
     */
    private Integer cooperationScore;

    /**
     * 工作配合度评分备注
     */
    private String cooperationRemark;

    /**
     * 执行规范评分（1-4）
     */
    private Integer disciplineScore;

    /**
     * 执行规范评分备注
     */
    private String disciplineRemark;

    /**
     * 综合评分
     */
    private BigDecimal comprehensiveScore;
}