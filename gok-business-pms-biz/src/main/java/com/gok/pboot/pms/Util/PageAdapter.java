package com.gok.pboot.pms.Util;

import cn.hutool.core.util.PageUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.common.base.PageRequest;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.annotation.Nonnull;
import java.util.List;

/**
 * <AUTHOR> xinyp
 * @Date 2022/6/16 17:32
 * @Version 1.0
 */
@Data
@NoArgsConstructor
public class PageAdapter {

    private int begin;

    private int size;

    public PageAdapter(Page page) {
        PageUtil.setFirstPageNo(1);
        int[] startEnd = PageUtil.transToStartEnd((int) page.getCurrent(), (int) page.getSize());
        this.begin = startEnd[0];
        this.size = (int)page.getSize();
    }

    public static PageAdapter from(PageRequest pageRequest){
        int size = Math.max(pageRequest.getPageSize(), 1);
        int begin = (Math.max(pageRequest.getPageNumber(), 1) - 1) * size;
        PageAdapter result = new PageAdapter();

        result.setBegin(begin);
        result.setSize(size);

        return result;
    }

    /**
     * 转为分页对象
     * @param dataList 已分好页的数据
     * @param total 总记录数
     * @return 分页对象
     */
    public <T> Page<T> toPage(@Nonnull List<T> dataList, int total){
        int pSize = Math.max(size, 1);
        int pPage = (Math.max(begin, 0) / pSize) + 1;
        Page<T> result = new Page<>(pPage, pSize);

        if (total < 1 || dataList.isEmpty()){
            PageUtils.buildEmptyPageBean(result);

            return result;
        }
        result.setRecords(dataList);
        result.setTotal(total);
        result.setSize(dataList.size());
        result.setPages((long) Math.ceil((double) total / pSize));

        return result;
    }
}
