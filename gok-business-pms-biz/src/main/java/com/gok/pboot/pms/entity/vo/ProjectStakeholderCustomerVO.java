package com.gok.pboot.pms.entity.vo;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 项目干系人-客户Vo
 *
 * <AUTHOR>
 * @date 2023-07-11 17:05:26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectStakeholderCustomerVO {

    /**
     * ID id
     */
    private Long id;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 联系人（手填）
     */
    private String contact;

    /**
     * 所在部门名（手填）
     */
    private String department;

    /**
     * 岗位/职位（手填）
     */
    private String position;

    /**
     * 职责
     */
    private String duty;

    /**
     * 影响程度字典id
     * {@link com.gok.pboot.pms.enumeration.ProjectImpactDegreeEnum}
     */
    private Integer impactDegree;

    /**
     * 联系方式
     */
    private String contactPhone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 备注
     */
    private String remark;

    /**
     * 影响程度字典值
     * {@link com.gok.pboot.pms.enumeration.ProjectImpactDegreeEnum}
     */
    private String impactDegreeTxt;

    /**
     * 是否满意度调查
     */
    private Integer satisfactionSurvey;

    /**
     * 是否满意度调查文本
     */
    private String satisfactionSurveyTxt;

}
