package com.gok.pboot.pms.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 合同台账明细表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-02-23 10:46:40
 */
@Data
@TableName("contract_ledger_detail")
public class ContractLedgerDetail implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 明细id
	 */
	@TableId
	private Long id;
	/**
	 * 合同id
	 */
	private Integer mainid;
	/**
	 * 预计收(付)款日期
	 */
	private String yjskrq;
	/**
	 * 收(付)款金额（含税）
	 */
	private BigDecimal skje;
	/**
	 * 收(付)款条件（项目进度）
	 */
	private String sktjxmjd;
	/**
	 * 收(付)款状态
	 */
	private Integer skzt;
	/**
	 * 收(付)款备注
	 */
	private String skbz;
	/**
	 * 款项名称
	 */
	private String djbkx;
	/**
	 * 实际收(付)款日期
	 */
	private String sjskrq;
	/**
	 * 发票开具状态
	 */
	private Integer fpkjzt;
	/**
	 * 实际开票日期
	 */
	private String sjkprq;
	/**
	 * 收(付)款金额(不含税)
	 */
	private BigDecimal sfkjebhs;
	/**
	 * 税率
	 */
	private Integer sl;
	/**
	 * 款项比例
	 */
	private BigDecimal kxbl;
	/**
	 * 订单开始日期
	 */
	private String ddksrq;
	/**
	 * 订单结算日期
	 */
	private String ddjsrq;
	/**
	 * 结算单（电子版）
	 */
	private String ddfj;
	/**
	 * 结算单（电子版）图像文件id
	 */
	private Long ddfjImagefileid;
	/**
	 * 结算单（电子版）文件名
	 */
	private String ddfjImagefilename;
	/**
	 * 结算单（扫描件）
	 */
	private String jsdfj;
	/**
	 * 结算单（扫描件）图像文件id
	 */
	private Long jsdfjImagefileid;
	/**
	 * 结算单（扫描件）文件名
	 */
	private String jsdfjImagefilename;
	/**
	 * 款项名称（暂不使用）
	 */
	private Integer kxmc;
	/**
	 * 收（付）款状态
	 */
	private Integer sfkzt;
	/**
	 * 实际收(付)款金额(含税)
	 */
	private BigDecimal sjsfkjehs;
	/**
	 * 实际收(付)款日期
	 */
	private String sjsfkrq;
	/**
	 * 待收（付）款金额
	 */
	private BigDecimal dsfkje;
	/**
	 * 订单扫描件
	 */
	private String ddsmj;
	/**
	 * 订单扫描件图像文件id
	 */
	private Long ddsmjImagefileid;
	/**
	 * 订单扫描件文件名
	 */
	private String ddsmjImagefilename;
	/**
	 * 创建人
	 */
	private String creator;
	/**
	 * 创建人ID
	 */
	private Long creatorId;
	/**
	 * 修改人
	 */
	private String modifier;
	/**
	 * 修改人ID
	 */
	private Long modifierId;
	/**
	 * 创建时间
	 */
	private Date ctime;
	/**
	 * 修改时间
	 */
	private Date mtime;
	/**
	 * 删除标识
	 */
	private Integer delFlag;

}
