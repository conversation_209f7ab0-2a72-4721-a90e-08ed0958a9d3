package com.gok.pboot.pms.cost.entity.dto;

import com.gok.pboot.pms.cost.enums.CostBudgetTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 新增成本估算结果DTO类
 *
 * <AUTHOR>
 * @create 2025/01/08
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CostManageEstimationResultsSaveDTO {

    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空")
    private Long projectId;

    /**
     * 成本预算类型
     * {@link com.gok.pboot.pms.cost.enums.CostBudgetTypeEnum}
     */
    @NotNull(message = "成本估算类型不能为空")
    @Min(value = 0, message = "成本估算类型非法")
    @Max(value = 2, message = "成本估算类型非法")
    private Integer costBudgetType;

    /**
     * 成本结果条目集合
     */
    @Valid
    @NotEmpty(message = "成本估算明细不能为空")
    private List<CostManageEstimationResultsEntry> resultEntries;

    /**
     * 成本估算结果条目类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CostManageEstimationResultsEntry {

        /**
         * 说明
         */
        private String remark;

        /**
         * 成本项来源 0=新增成本项 1=人工成本测算
         * {@link com.gok.pboot.pms.cost.enums.CostManageSourceEnum}
         */
        @NotNull(message = "成本估算来源不能为空")
        @Min(value = 0, message = "成本估算来源非法")
        @Max(value = 1, message = "成本估算来源非法")
        private Integer source;

        /**
         * 成本科目ID
         */
        @NotNull(message = "成本科目ID不能为空")
        private Long accountId;

        /**
         * 成本科目OA ID
         */
        @NotNull(message = "成本科目OA ID不能为空")
        private Long accountOaId;

        /**
         * 成本科目名称
         */
        @NotNull(message = "成本科目名称不能为空")
        private String accountName;

        /**
         * 成本科目类型
         * {@link com.gok.pboot.pms.cost.enums.AccountTypeEnum}
         */
        @Min(value = 0, message = "成本科目类型非法")
        @Max(value = 2, message = "成本科目类型非法")
        private Integer accountType;

        /**
         * 成本预算类型（0=售前成本，1=A表成本，2=B表成本）
         * {@link CostBudgetTypeEnum}
         */
        private Integer costBudgetType;

        /**
         * 预算金额(含税)
         */
        private BigDecimal budgetAmountIncludedTax;

        /**
         * 已用预算
         */
        private BigDecimal usedBudget;

        /**
         * 剩余预算
         */
        private BigDecimal remainBudget;

        /**
         * 税率OA字典ID
         */
        @NotNull(message = "税率不能为空")
        private Integer taxRate;

        /**
         * 预算金额(不含税)
         */
        private BigDecimal budgetAmountExcludingTax;

        /**
         * 人员级别测算明细集合
         */
        private List<CostManagePersonnelLevelDTO> personnelLevelEntries;

        /**
         * 自定义补贴明细集合
         */
        private List<CostManagePersonnelCustomDetailDto> personnelCustomDetailEntries;
    }

}
