package com.gok.pboot.pms.entity.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.gok.pboot.pms.common.serializer.TwoDecimalToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @desc 个人面板-饱和度分析VO
 * @createTime 2023/2/21 14:56
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class PanelProjectSituationAnalysisVO {
    /**
     * 项目id
     */
    @ExcelIgnore
    @ApiModelProperty(value = "项目id")
    private Long projectId;
    /**
     * 项目名称
     */
    @ExcelProperty("项目名称")
    @ApiModelProperty(value = "项目名称")
    private String projectName;
    /**
     * 开始时间
     */
    @ExcelProperty("开始时间")
    @ApiModelProperty(value = "开始时间")
    private LocalDate startTime;
    /**
     * 截止时间
     */
    @ExcelProperty("截止时间")
    @ApiModelProperty(value = "截止时间")
    private LocalDate endTime;
    /**
     * 正常工时（人天）
     */
    @ExcelProperty("正常工时（人天）")
    @ApiModelProperty(value = "正常工时（人天）")
    @JsonSerialize(using = TwoDecimalToStringSerializer.class)
    private BigDecimal normalHours;
    /**
     * 加班工时（人天）
     */
    @ExcelProperty("加班工时（人天）")
    @ApiModelProperty(value = "加班工时（人天）")
    @JsonSerialize(using = TwoDecimalToStringSerializer.class)
    private BigDecimal addedHours;

    /**
     * '工作日加班工时'
     */
    @ExcelIgnore
    @ApiModelProperty(value = "工作日加班工时（人天）")
    @JsonSerialize(using = TwoDecimalToStringSerializer.class)
    private BigDecimal workOvertimeHours;

    /**
     * '休息日加班工时'
     */
    @ExcelIgnore
    @ApiModelProperty(value = "休息日加班工时（人天）")
    @JsonSerialize(using = TwoDecimalToStringSerializer.class)
    private BigDecimal restOvertimeHours;
    /**
     * '节假日加班工时'
     */
    @ExcelIgnore
    @ApiModelProperty(value = "节假日加班工时（人天）")
    @JsonSerialize(using = TwoDecimalToStringSerializer.class)
    private BigDecimal holidayOvertimeHours;

    /**
     * 调休工时（人天）
     */
    @ExcelProperty("调休工时（人天）")
    @ApiModelProperty(value = "调休工时（人天）")
    @JsonSerialize(using = TwoDecimalToStringSerializer.class)
    private BigDecimal ompensatoryHours;
    /**
     * 项目分摊工时（人天）
     */
    @ExcelProperty("项目分摊工时（人天）")
    @ApiModelProperty(value = "项目分摊工时（人天）")
    @JsonSerialize(using = TwoDecimalToStringSerializer.class)
    private BigDecimal projectHours;
    /**
     * 实际出勤天数
     */
    @ExcelProperty("实际出勤天数")
    @ApiModelProperty(value = "实际出勤天数")
    @JsonSerialize(using = TwoDecimalToStringSerializer.class)
    private BigDecimal attendanceDays;

    /**
     * EHR工资核算出勤天数
     */
    @ExcelProperty("工资核算出勤天数")
    @ApiModelProperty(value = "工资核算出勤天数")
    @JsonSerialize(using = TwoDecimalToStringSerializer.class)
    private BigDecimal salaryAttendanceDays;

    /**
     * 工时饱和度
     */
    @ExcelProperty("工时饱和度")
    @ApiModelProperty(value = "工时饱和度")
    @JsonSerialize(using = TwoDecimalToStringSerializer.class)
    private BigDecimal hourSaturation;
}
