package com.gok.pboot.pms.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
    * 合同台账验收记录vo
    * <AUTHOR>
*/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ContractAcceptanceVo {
    /**
     * id
     */
    private Long id;
    /**
     * 流程id
     */
    private Long requestId;

    /**
     * 流程相关人id
     */
    private String applicantid;

    /**
     * 流程编号
     */
    private String FlowNo;
    /**
     * 结项启动条件
     */
    private Integer jxqdtj;
    /**
     * 结项启动条件
     */
    private String jxqdtjText;
    /**
     * 验收日期
     */
    private String sjys;
    /**
     * 目前累计已确认收入金额
     */
    private String jdysje;
    /**
     * 本次结项确认收入金额(含税)
     */
    private String bcjxqrsrjebhs;
    /**
     * 本次结项确认收入金额(不含税)
     */
    private String bcjxqrsrje;
    /**
     * 验收报告附件
     */
    private String ysbgfj;

    /**
     * 验收报告附件名字
     */
    private List<OaFileInfoVo> ysbgfjName;

    /**
     * 验收报告附件
     */
    private String ysbgfjImageFileId;


    /**
     * 确认收入日期
     */
    private String qrsrrq;

}