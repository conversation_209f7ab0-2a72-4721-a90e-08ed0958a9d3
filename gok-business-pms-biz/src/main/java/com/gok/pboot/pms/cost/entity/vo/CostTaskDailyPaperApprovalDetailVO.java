package com.gok.pboot.pms.cost.entity.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 工单工时审核查询明细结果
 *
 * <AUTHOR>
 * @date 2024/04/16
 */
@Data
public class CostTaskDailyPaperApprovalDetailVO {

    /**
     * id
     */
    private Long id;

    /**
     * 提交人id
     */
    private Long userId;

    /**
     * 提交人
     */
    private String userRealName;

    /**
     * 填报日期
     */
    private LocalDate submissionDate;

    /**
     * 假期类型
     */
    private String holidayType;

    /**
     * 周几
     */
    private String weekDay;

    /**
     * 工单id
     */
    private Long taskId;

    /**
     * 工单名称
     */
    private String taskName;

    /**
     * 工单类别
     */
    private Integer taskCategory;

    /**
     * 工单类别
     */
    private String taskCategoryTxt;

    /**
     * 正常工时
     */
    private BigDecimal normalHours;

    /**
     * 加班工时
     */
    private BigDecimal overtimeHours;

    /**
     * 工作日加班
     */
    private BigDecimal workOvertimeHours;

    /**
     * 休息日加班
     */
    private BigDecimal restOvertimeHours;

    /**
     * 节假日加班
     */
    private BigDecimal holidayOvertimeHours;

    /**
     * 工作内容
     */
    private String workContent;

    /**
     * 昨日计划
     */
    private String yesterdayPlan;

    /**
     * 提交时间
     */
    private LocalDateTime submissionTime;

    /**
     * 填报状态
     */
    private Integer fillingState;
    /**
     * 填报状态
     */
    private String fillingStateTxt;

    /**
     * 审核状态
     */
    private Integer approvalStatus;

    /**
     * 审核状态
     */
    private String approvalStatusTxt;
} 