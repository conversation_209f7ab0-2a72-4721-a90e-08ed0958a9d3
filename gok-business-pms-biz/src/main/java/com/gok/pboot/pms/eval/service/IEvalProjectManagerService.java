package com.gok.pboot.pms.eval.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.pms.eval.entity.domain.EvalProjectManager;
import com.gok.pboot.pms.eval.entity.domain.EvalProjectOverview;
import com.gok.pboot.pms.eval.entity.dto.EvalProjectManagerDTO;
import com.gok.pboot.pms.eval.entity.vo.EvalProjectManagerVO;
import com.gok.pboot.pms.eval.entity.vo.EvalProjectOverviewVO;

import java.util.List;
import java.util.Map;


/**
 * 项目经理评价Service
 *
 * <AUTHOR>
 * @date 2025/05/08
 */
public interface IEvalProjectManagerService extends IService<EvalProjectManager> {

    /**
     * 根据项目整体评价自动保存项目经理评价
     *
     * @param evalProjectOverviews 项目整体评价集合
     * @return
     */
    List<Long> autoSaveEvalProjectManager(List<EvalProjectOverview> evalProjectOverviews);

    /**
     * 批量获取项目经理评价集合
     *
     * @param overviewVOList 项目整体评价VO集合
     * @return 项目整体评价ID -> 项目经理评价集合的映射
     */
    Map<Long, List<EvalProjectManagerVO>> findEvalProjectManagerVOList(List<EvalProjectOverviewVO> overviewVOList);

    /**
     * 项目经理评分
     *
     * @param requestList 评分数据集合
     */
    void score(List<EvalProjectManagerDTO> requestList);

} 