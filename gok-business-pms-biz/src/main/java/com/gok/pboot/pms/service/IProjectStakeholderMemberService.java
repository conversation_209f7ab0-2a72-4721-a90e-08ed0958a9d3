package com.gok.pboot.pms.service;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.bcp.upms.vo.SysDeptOutVO;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.entity.domain.ProjectStakeholderMember;
import com.gok.pboot.pms.entity.dto.ProjectStakeholderMemberBatchDTO;
import com.gok.pboot.pms.entity.dto.ProjectStakeholderMemberDTO;
import com.gok.pboot.pms.entity.vo.ProjectInfoMemberVO;
import com.gok.pboot.pms.entity.vo.ProjectStakeholderCustomerVO;
import com.gok.pboot.pms.entity.vo.ProjectStakeholderMemberVO;
import com.gok.pboot.pms.entity.vo.UserJobDeptVo;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 项目干系人-成员
 *
 * <AUTHOR>
 * @date 2023-07-11 17:05:26
 */
public interface IProjectStakeholderMemberService extends IService<ProjectStakeholderMember> {
    /**
     * 查询项目成员列表
     *
     * @param projectId 项目id
     * @param roleType  角色类型  {@link com.gok.pboot.pms.enumeration.RoleTypeEnum}
     * @return {@link ApiResult}<{@link List}<{@link ProjectStakeholderMemberVO}>>
     */
    ApiResult<List<ProjectStakeholderMemberVO>> getMemberByProjectId(Long projectId, Integer roleType);

    /**
     * 分页获取项目成员列表
     *
     * @param filter      过滤参数
     * @param pageRequest 分页对象
     * @return {@link ApiResult}<{@link Page}<{@link ProjectStakeholderMemberVO}>>
     */
    ApiResult<Page<ProjectStakeholderMemberVO>> findPage(PageRequest pageRequest, Map<String, Object> filter);

    /**
     * 保存项目成员
     *
     * @param dto 保存请求对象
     * @return {@link Boolean}
     */
    Boolean save(ProjectStakeholderMemberBatchDTO dto);

    /**
     * 构建部门层级名称
     *
     * @param deptId   部门id
     * @param deptMap  部门映射
     * @param isFirst  是否首次
     * @param deptName 部门名称
     * @return {@link String}
     */
    String buildDeptName(Long deptId, Map<Long, SysDeptOutVO> deptMap, Boolean isFirst, String deptName);

    /**
     * 批量修改
     *
     * @param id id
     * @return ApiResult
     */
    ApiResult<String> delById(Long id);

    /**
     * 查询项目铁三角
     *
     * @param projectId 项目id
     * @return {@link ApiResult}<{@link ProjectStakeholderCustomerVO}>>
     */
    ProjectInfoMemberVO getIronTriangleByProjectId(Long projectId);

    /**
     * 通过userId获取用户职位、部门等信息
     *
     * @param userId 用户ID
     * @return 用户职位、部门等信息
     */
    ApiResult<UserJobDeptVo> getUserJobDeptByUserId(Long userId);

    /**
     * 修改项目成员角色
     *
     * @param dto 请求数据对象
     * @return {@link Boolean}
     */
    Boolean updateRoleType(ProjectStakeholderMemberDTO dto);

    /**
     * 修改项目成员备注
     *
     * @param dto 请求数据对象
     * @return {@link Boolean}
     */
    Boolean updateRemark(ProjectStakeholderMemberDTO dto);

    /**
     * 同步OA
     *
     * @param projectId 项目id
     * @return {@link String}
     */
    String syncOa(Long projectId);

    /**
     * 异步同步OA
     *
     * @param projectId 项目id
     */
    void asyncOa(Long projectId);

    /**
     * 批量保存
     *
     * @param newMemberBatchList 新成员批次列表
     */
    void batchSave(List<ProjectStakeholderMemberBatchDTO> newMemberBatchList);

    /**
     * 通过项目 ID 映射获取成员 ID
     *
     * @return {@link Map }<{@link Long }, {@link Set }<{@link Long }>>
     */
    Map<Long, Set<Long>> getMemberIdsByProjectIdMap();

    /**
     * 获取列表
     *
     * @param jsonObject JSON 对象
     * @return {@link List }<{@link ProjectStakeholderMemberVO }>
     */
    List<ProjectStakeholderMemberVO> getList( JSONObject jsonObject);
}

