package com.gok.pboot.pms.cost.entity.dto;

import com.gok.pboot.pms.enumeration.OperateEnum;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 成本支持任务添加编辑 DTO
 *
 * <AUTHOR>
 * @date 2025/04/10
 */
@Data
public class CostSupportTaskAddEditDTO {

    /**
     * id
     */
    private Long id;

    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空")
    private Long projectId;

    /**
     * 父级ID
     */
    private Long parentId;

    /**
     * 工单名称
     */
    @NotBlank(message = "工单名称不能为空")
    @Length(max = 25, message = "工单名称不能超过${max}个字符")
    private String taskName;


    /**
     * 工单类型（0=售前支撑，1=售后交付）
     *
     * @see com.gok.pboot.pms.enumeration.ProjectTaskKindEnum
     */
    private Integer taskType;

    /**
     * 工单描述
     */
    @Length(max = 200, message = "工单描述不能超过${max}个字符")
    private String taskDesc;


    /**
     * 拆解类型（0=标准工单，1=总成工单）
     *
     * @see com.gok.pboot.pms.cost.enums.CostTaskDisassemblyTypeEnum
     */
    @NotNull(message = "拆解类型不能为空")
    private Integer disassemblyType;

    /**
     * 工单类别
     */
    @NotNull(message = "工单类别不能为空")
    private Integer taskCategory;


    /**
     * 工单负责人ID
     */
    @NotNull(message = "工单负责人不能为空")
    private Long managerId;

    /**
     * 工单负责人姓名
     */
    @NotNull(message = "负责人姓名不能为空")
    private String managerName;

    @NotNull(message = "工单级别不能为空")
    private Integer taskLevel;
    /**
     * 操作枚举
     *
     * @see OperateEnum
     */
    private OperateEnum operateEnum;


    /**
     * 子工单
     */
    @Valid
    private List<CostSupportTaskAddEditDTO> children;
}
