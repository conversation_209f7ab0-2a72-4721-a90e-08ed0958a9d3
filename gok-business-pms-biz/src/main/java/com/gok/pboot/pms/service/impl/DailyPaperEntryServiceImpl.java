package com.gok.pboot.pms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.bcp.upms.dto.DeptCacheDto;
import com.gok.bcp.upms.dto.MemberDto;
import com.gok.bcp.upms.feign.RemoteDeptService;
import com.gok.bcp.upms.feign.RemoteUserService;
import com.gok.pboot.common.core.util.DateUtils;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.Util.*;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.common.base.R;
import com.gok.pboot.pms.common.exception.ServiceException;
import com.gok.pboot.pms.common.join.ProjectInDailyPaperEntry;
import com.gok.pboot.pms.entity.DailyPaperEntry;
import com.gok.pboot.pms.entity.Holiday;
import com.gok.pboot.pms.entity.OvertimeLeaveData;
import com.gok.pboot.pms.entity.SysDept;
import com.gok.pboot.pms.entity.bo.TaskReviewerInfoBO;
import com.gok.pboot.pms.entity.domain.ProjectInfo;
import com.gok.pboot.pms.entity.domain.Roster;
import com.gok.pboot.pms.entity.domain.TomorrowPlanPaperEntry;
import com.gok.pboot.pms.entity.dto.CompensatoryLeaveDTO;
import com.gok.pboot.pms.entity.dto.SubordinatesDailyPaperDTO;
import com.gok.pboot.pms.entity.vo.*;
import com.gok.pboot.pms.enumeration.*;
import com.gok.pboot.pms.handler.perm.PmsRetriever;
import com.gok.pboot.pms.mapper.*;
import com.gok.pboot.pms.service.IDailyPaperEntryService;
import com.google.common.base.Splitter;
import com.google.common.collect.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * - 日报条目服务实现类 -
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/8/24 9:52
 */
@Service
@AllArgsConstructor
@Slf4j
public class DailyPaperEntryServiceImpl implements IDailyPaperEntryService {

    private final DailyPaperEntryMapper mapper;

    private final OvertimeLeaveDataMapper overtimeLeaveDataMapper;
    private final CompensatoryLeaveDataMapper compensatoryLeaveDataMapper;

    private final TomorrowPlanPaperEntryMapper planPaperEntryMapper;
    private final ProjectInfoMapper projectInfoMapper;
    private final RosterMapper rosterMapper;

    private final HolidayMapper holidayMapper;

    private final PmsRetriever pmsRetriever;

    // 接入中台的接口
    private final RemoteUserService remoteUserService;
    private final RemoteDeptService remoteDeptService;


    private static final String FILTER_DEPT_IDS = "deptIds";
    private static final String PARAM_USER_IDS = "userIds";
    private static final String PARAM_TASK_IDS = "taskIds";
    private static final String PROJECT_STATUS_LIST = "projectStatusList";
    private static final String PROJECT_TYPE_LIST = "projectTypeList";
    private static final String PARAM_TASK_ID = "taskId";
    private static final String PARAM_HOUR_STATUS = "hourStatus";
    private static final String PARAM_PROJECT_IDS = "projectIds";
    private static final String XMJL = "项目经理";
    private static final String XSJL = "销售经理";
    private static final String SQJL = "售前经理";
    private static final String RWFZR = "任务负责人";
    private static final String ZJSJ = "直接上级";
    private static final String ZERO = "0.00";

    @Override
    public DailyPaperEntryContactsVo getContacts(Long projectId, Long taskId) {
        DailyPaperEntryContactsVo dailyPaperEntryContactsVo = new DailyPaperEntryContactsVo();
        Long userId = SecurityUtils.getUser().getId();
        ProjectInfo projectInfo ;
        Long isNotInternalProject;
        // 设置销售经理、项目经理字段值
        if( projectId!=null){
            projectInfo = projectInfoMapper.selectById(projectId);
             isNotInternalProject = projectInfo.getIsNotInternalProject();
            if(Optional.ofNullable(projectInfo).isPresent()){
                dailyPaperEntryContactsVo.setManagerUserName(projectInfo.getManagerUserName());
                if(isNotInternalProject==2){
                    dailyPaperEntryContactsVo.setSalesmanUserName(projectInfo.getProjectSalesperson());
                }

            }
        }

        Optional.ofNullable(taskId).flatMap(tId -> Optional.ofNullable(pmsRetriever.getReviewerInfo(userId, tId))).ifPresent(r -> {
            List<String> auditorNames = new ArrayList<>();
            Set<TaskReviewerTypeEnum> types = ObjectUtils.defaultIfNull(r.getTypes(), ImmutableSet.of());

            // 判断主责审核人类型并设置字段值
            if (types.contains(TaskReviewerTypeEnum.DIRECT_LEADER) && Optional.ofNullable(r.getDirectLeaderUserId()).isPresent()) {
                auditorNames.add(ZJSJ + ": " + Optional.ofNullable(rosterMapper.selectById(r.getDirectLeaderUserId())).map(Roster::getAliasName).orElse(StrUtil.EMPTY));
            }
            if (types.contains(TaskReviewerTypeEnum.IRON_TRIANGLE)) {
                if (Optional.ofNullable(r.getProjectManagerUserId()).isPresent()) {
                    Optional.ofNullable(rosterMapper.selectById(r.getProjectManagerUserId())).ifPresent(roster -> auditorNames.add(XMJL + ": " + roster.getAliasName()));
                }
                if (Optional.ofNullable(r.getProjectPreSalesmanUserId()).isPresent()) {
                    Optional.ofNullable(rosterMapper.selectById(r.getProjectPreSalesmanUserId())).ifPresent(roster -> auditorNames.add(SQJL + ": " + roster.getAliasName()));
                }
                if (Optional.ofNullable(r.getProjectSalesmanUserId()).isPresent()) {
                    Optional.ofNullable(rosterMapper.selectById(r.getProjectSalesmanUserId())).ifPresent(roster -> auditorNames.add(XSJL + ": " + roster.getAliasName()));
                }
            }
            if (types.contains(TaskReviewerTypeEnum.TASK_LEADER) && CollUtil.isNotEmpty(r.getTaskLeaderUserIds())) {
                LambdaQueryWrapper<Roster> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.in(Roster::getId, r.getTaskLeaderUserIds());
                Optional.ofNullable(rosterMapper.selectList(queryWrapper)).ifPresent(rosters -> auditorNames.add(RWFZR + ": " + rosters.stream().map(Roster::getAliasName).collect(Collectors.joining("、"))));
            }
            dailyPaperEntryContactsVo.setAuditorNames(auditorNames);
        });

        return dailyPaperEntryContactsVo;
    }

    @Override
    public Page<DailyPaperEntryVO> findPage(PageRequest pageRequest, Map<String, Object> filter) {
        Page<DailyPaperEntry> poPage;
        List<DailyPaperEntry> poList;
        Map<Long, ProjectInDailyPaperEntry> projects;

        prepareParameterMap(filter);
        poPage = mapper.findList(new Page<>(pageRequest.getPageNumber(), pageRequest.getPageSize()), filter);
        poList = poPage.getRecords();
        if (poList.isEmpty()) {
            return new Page<>(pageRequest.getPageNumber(), pageRequest.getPageSize());
        }
        projects = projectInfoMapper.findByIdsForDailyPaperEntry(
                        poPage.getRecords()
                                .stream()
                                .map(DailyPaperEntry::getProjectId)
                                .distinct()
                                .collect(Collectors.toList())
                )
                .stream()
                .collect(Collectors.toMap(ProjectInDailyPaperEntry::getId, project -> project));

        return toVOPage(poPage, projects);
    }

    private void prepareParameterMap(Map<String, Object> filter) {
        Object startDateObj = filter.get("submissionDateStart");
        Object endDateObj = filter.get("submissionDateEnd");
        Object approvalStatusListObj = filter.get("approvalStatusList");
        List<Integer> approvalStatusList;
        final String datePattern;
        Object deptIdsObj = filter.get(FILTER_DEPT_IDS);
        List<String> deptIds;

        if (startDateObj != null && endDateObj != null) {
            if (startDateObj instanceof String && endDateObj instanceof String) {
                datePattern = "yyyy-MM-dd";
                try {
                    DateUtils.parseDateStrictly((String) startDateObj, datePattern);
                    DateUtils.parseDateStrictly((String) endDateObj, datePattern);
                } catch (ParseException e) {
                    throw new ServiceException("日期格式不合法");
                }
            } else {
                throw new ServiceException("请传入正确的日期数据类型");
            }
        }
        if (deptIdsObj != null) {
            if (deptIdsObj instanceof String) {
                deptIds = ImmutableList.copyOf(((String) deptIdsObj).split(","));
                if (deptIds.isEmpty()) {
                    filter.remove(FILTER_DEPT_IDS);

                    return;
                }
                filter.put(FILTER_DEPT_IDS, deptIds);
            } else {
                filter.remove(FILTER_DEPT_IDS);
            }
        }
        if (approvalStatusListObj != null) {
            if (approvalStatusListObj instanceof String) {
                approvalStatusList = ImmutableList.copyOf(((String) approvalStatusListObj).split(","))
                        .stream()
                        .map(Integer::parseInt)
                        .collect(Collectors.toList());
                filter.put("approvalStatusList", approvalStatusList);
            } else {
                filter.remove("approvalStatusList");
            }
        }
        filter.put("onlyActiveFlag", true);
    }

    private Page<DailyPaperEntryVO> toVOPage(Page<DailyPaperEntry> poPage, Map<Long, ProjectInDailyPaperEntry> projects) {
        Page<DailyPaperEntryVO> result = new Page<>();
        List<DailyPaperEntry> poList = poPage.getRecords();
        List<DailyPaperEntryVO> voList;

        if (poList.isEmpty() || ObjectUtil.isEmpty(projects)) {
            return result;
        }
        voList = poList
                .stream()
                .map(po -> new DailyPaperEntryVO(po, projects.get(po.getProjectId())))
                .collect(Collectors.toList());
        BeanUtils.copyProperties(poPage, result);
        result.setRecords(voList);

        return result;
    }

    @Override
    public List<DailyPaperEntry> findByDailyPaperId(Long dailyPaperId) {
        return mapper.findByDailyPaperId(dailyPaperId);
    }

    @Override
    public List<Object> exportExcel(
            LocalDate startDate,
            LocalDate endDate,
            Map<String, Object> filter
    ) {
        handleFilter(filter);
        filter.put("startDate", startDate);
        filter.put("endDate", endDate);

        // 添加权限限制
        Collection<String> projectIds = pmsRetriever.getProjectIdsAvailable()
                .stream()
                .map(String::valueOf)
                .collect(Collectors.toList());
        Object projectIdsDTO = filter.get(PARAM_PROJECT_IDS);

        if (projectIdsDTO instanceof Collection){
            projectIds = CollectionUtils.intersection(projectIds, (Collection<String>) projectIdsDTO);
        }
        filter.put(PARAM_PROJECT_IDS, projectIds);
        dealWithCollectionParamForExport(filter, PARAM_USER_IDS);
        dealWithCollectionParamForExport(filter, PARAM_TASK_IDS);

        List<DailyPaperEntryExcelVO> entries = mapper.queryAllAndWorkCode(filter);

        if (entries.isEmpty()) {
            if (NumberUtils.INTEGER_ONE.equals(filter.get("isNotInternalProject"))){
                return Lists.newArrayList(new DailyPaperEntryExcelVO());
            }else {
                return Lists.newArrayList(new ExternalDailyPaperEntryExcelVO());
            }
        }

        List<DeptCacheDto>  deptList  = remoteDeptService.getAllDeptList(false);
        Map<Long, SysDept> deptIdMap;
        if (com.gok.pboot.pms.Util.CollectionUtils.isEmpty(deptList)) {
            log.warn("未找到中台部门数据");
            deptIdMap = ImmutableMap.of();
        } else {
            deptIdMap = SysDeptUtils.mapBCPDeptCacheDtoToSysDept(deptList)
                    .stream()
                    .collect(Collectors.toMap(SysDept::getDeptId, dept -> dept));
        }
        List<Holiday> holidayList = holidayMapper.findHolidayList();
        Map<LocalDate, Holiday> holidayMap = holidayList.stream().filter(h-> h.getHolidayType()!=null).
                collect(Collectors.toMap(Holiday::getDayDate,a->a, (a, b) -> a));

        entries = entries.stream().map(entry -> {
            //项目状态值转换
            entry.setProjectStatusName(ProjectStatusEnum.getNameByStrVal(entry.getProjectStatus()));
            entry.setDeptName(SysDeptUtils.collectFullName(deptIdMap, entry.getUserDeptId()));
            entry.setUserStatusName(EnumUtils.getNameByValue(PersonnelStatusEnum.class, entry.getUserStatus()));
            entry.setSubmissionDateFormatted(DailyPaperDateUtils.asDateString(entry.getSubmissionDate()));
            Holiday holiday = holidayMap.getOrDefault(entry.getSubmissionDate(), null);
            if (Optional.ofNullable(holiday).isPresent()) {
                String holidayType = NumberUtils.INTEGER_ZERO.equals(holiday.getHolidayType()) ? "休" : "节";
                entry.setSubmissionDateFormatted(entry.getSubmissionDateFormatted()+holidayType);
            }
            // 转换成人天
            entry.setAddedHours(CommonUtils.unitConversion(entry.getAddedHours()));
            entry.setNormalHours(CommonUtils.unitConversion(entry.getNormalHours()));
            entry.setTotalHours(CommonUtils.unitConversion(entry.getTotalHours()));
            return entry;
        }).collect(Collectors.toList());
        List<Object> data = new ArrayList<>();
        List<DailyPaperEntryExcelVO> dailyPaperEntryExcelVOS = dailyPaperEntryListToExcelVOList(entries, startDate, endDate);
        if (NumberUtils.INTEGER_ONE.equals(filter.get("isNotInternalProject"))){
            data.addAll(dailyPaperEntryExcelVOS);
        }else {
            List<ExternalDailyPaperEntryExcelVO> externalExcelVO = dailyPaperEntryExcelVOS.stream().map(d -> {
                ExternalDailyPaperEntryExcelVO externalDailyPaperEntryExcelVO = BeanUtil.copyProperties(d, ExternalDailyPaperEntryExcelVO.class);
                return externalDailyPaperEntryExcelVO;
            }).collect(Collectors.toList());
            data.addAll(externalExcelVO);
        }
        return data;
    }

    private void dealWithCollectionParamForExport(Map<String, Object> filter, String paramName){
        String idsStr = MapUtils.getString(filter, paramName);
        if (StringUtils.isBlank(idsStr)){
            return;
        }
        List<Long> idsParam = Splitter.on(",")
                .omitEmptyStrings()
                .trimResults()
                .splitToList(idsStr.replaceAll("[\\[\\]]", ""))
                .stream()
                .map(s -> NumberUtils.toLong(s, -1L))
                .collect(Collectors.toList());
        filter.put(paramName, idsParam);
    }

    @Override
    public Page<DailyReviewProjectAuditPageVO> findBySubordinate(Page page, SubordinatesDailyPaperDTO dto) {

        //获取登录用户的直属下级
        Long managerId = SecurityUtils.getUser().getId();

        List<MemberDto> userAndUnderUserList = remoteUserService.getImmediateSubordinates(managerId).getData();
        List<Long> subUserIds;
        if (CollectionUtil.isEmpty(userAndUnderUserList)) {
            subUserIds = Collections.emptyList();
        } else {
            subUserIds = userAndUnderUserList.stream()
                    .map(MemberDto::getUserId)
                    .collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(subUserIds)) {
            return new Page<>();
        }
        dto.setUserIds(subUserIds);
        Page<DailyReviewProjectAuditPageVO> bySubordinate =
                mapper.findBySubordinate(Page.of(dto.getPageNumber(), dto.getPageSize()), dto);
        List<DailyReviewProjectAuditPageVO> records = bySubordinate.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            Set<Long> userIds = Sets.newHashSet();
            Set<Long> teskIds = Sets.newHashSet();
            for (DailyReviewProjectAuditPageVO record : records) {
                userIds.add(record.getUserId());
                teskIds.add(record.getTaskId());
            }
            //减少参数大小，只根据结果的用户id查询
            dto.setUserIds(userIds);
            dto.setTaskIds(teskIds);
            List<TomorrowPlanPaperEntry> planPaperList = planPaperEntryMapper.findByFilter(dto);
            Map<String, TomorrowPlanPaperEntry> map = planPaperList.stream().collect(Collectors.toMap(p -> "" + p.getTaskId() + p.getUserId() + p.getSubmissionDate(), p -> p, (existing, replacement) -> replacement
            ));
            //节假日
            List<Holiday> holidayList = holidayMapper.findHolidayList();
            LinkedHashMap<LocalDate, Integer>  holidayMap
                    = holidayList.stream()
                    .filter(h-> h.getHolidayType()!=null)
                    .collect(Collectors.toMap(Holiday::getDayDate, Holiday::getHolidayType, (v1, v2) -> v1, LinkedHashMap::new));
            for (DailyReviewProjectAuditPageVO record : records) {
                // 将获取的明日计划修改为昨日计划时间，SubmissionDate是当日提交时间，获取的既是明日计划，SubmissionDate-1天获取的既是昨日的明日计划
                LocalDate submissionDate = record.getSubmissionDate();
                LocalDate yesterdaySubmissionDate = submissionDate.minusDays(1);
                TomorrowPlanPaperEntry tomorrowPlanPaperEntry = map.get("" + record.getTaskId() + record.getUserId() + yesterdaySubmissionDate);
                record.setYesterdayPlan(tomorrowPlanPaperEntry != null ? tomorrowPlanPaperEntry.getDescription() : null);
                record.setWorkTypeName(WorkTypeEnum.getNameByVal(record.getWorkType()));
                record.setApprovalStatusName(ApprovalStatusEnum.getNameByVal(record.getApprovalStatus()));
                record.setFillingStateName(DailyPaperFillingStateEnum.getNameByVal(record.getFillingState()));
                record.setSubmissionDateFormatted(DailyPaperDateUtils.asDateString(record.getSubmissionDate()));
                record.setHolidayType(holidayMap.get(record.getSubmissionDate()));
            }
        }
        return bySubordinate;
    }

    @Override
    public SubordinatePaperEntryStaticStrVO findBySubordinateStatic(SubordinatesDailyPaperDTO dto) {

        //获取登录用户的直属下级
        Long managerId = SecurityUtils.getUser().getId();

        List<MemberDto> userAndUnderUserList = remoteUserService.getImmediateSubordinates(managerId).getData();
        List<Long> subUserIds;
        if (CollectionUtil.isEmpty(userAndUnderUserList)) {
            subUserIds = Collections.emptyList();
        } else {
            subUserIds = userAndUnderUserList.stream()
                    .map(MemberDto::getUserId)
                    .collect(Collectors.toList());
        }
        SubordinatePaperEntryStaticStrVO subordinatePaperEntryStaticStrVO = new SubordinatePaperEntryStaticStrVO();


        if (CollectionUtils.isEmpty(subUserIds)) {
            subordinatePaperEntryStaticStrVO.setAddedHours(ZERO);
            subordinatePaperEntryStaticStrVO.setNormalHours(ZERO);
            subordinatePaperEntryStaticStrVO.setWorkOvertimeHours(ZERO);
            subordinatePaperEntryStaticStrVO.setRestOvertimeHours(ZERO);
            subordinatePaperEntryStaticStrVO.setHolidayOvertimeHours(ZERO);
            subordinatePaperEntryStaticStrVO.setOmpensatoryHours(ZERO);
            subordinatePaperEntryStaticStrVO.setProjectShareHours(ZERO);
            return subordinatePaperEntryStaticStrVO;
        }
        BigDecimal normalHoursAll=BigDecimal.ZERO;
        BigDecimal addedHoursAll=BigDecimal.ZERO;
        BigDecimal workOvertimeHoursAll=BigDecimal.ZERO;
        BigDecimal restOvertimeHoursAll=BigDecimal.ZERO;
        BigDecimal holidayOvertimeHoursAll=BigDecimal.ZERO;
        BigDecimal ompensatoryHoursAll;

        dto.setUserIds(subUserIds);
        //日报数据
        List<SubordinatePaperEntryStaticVO> subordinatePaperEntryStaticVOList = mapper.findBySubordinateStatic(dto);
        //OA项目调休数据
        CompensatoryLeaveDTO compensatoryLeaveDTO = new CompensatoryLeaveDTO();
        BeanUtil.copyProperties(dto,compensatoryLeaveDTO);
        compensatoryLeaveDTO.setType("3");
        BigDecimal compensatoryHoursSum = compensatoryLeaveDataMapper.getCompensatoryHoursSum(compensatoryLeaveDTO);
        ompensatoryHoursAll=compensatoryHoursSum!=null?compensatoryHoursSum:BigDecimal.ZERO;
        //数据汇总
        if(CollUtil.isNotEmpty(subordinatePaperEntryStaticVOList)){
            Map<String, List<SubordinatePaperEntryStaticVO>> subordinatePaperEntryStaticVOMap = subordinatePaperEntryStaticVOList
                    .stream()
                    .collect(Collectors.groupingBy(a -> a.getUserId() +"-"+ a.getSubmissionDate()));

            Set<Map.Entry<String, List<SubordinatePaperEntryStaticVO>>> entries = subordinatePaperEntryStaticVOMap.entrySet();
            for (Map.Entry<String, List<SubordinatePaperEntryStaticVO>> entry : entries){
                List<SubordinatePaperEntryStaticVO> value = entry.getValue();
                for (SubordinatePaperEntryStaticVO subordinatePaperEntryStaticVO : value) {
                    normalHoursAll=normalHoursAll.add(subordinatePaperEntryStaticVO.getNormalHours());
                    addedHoursAll=addedHoursAll.add(subordinatePaperEntryStaticVO.getAddedHours());
                    workOvertimeHoursAll=workOvertimeHoursAll.add(subordinatePaperEntryStaticVO.getWorkOvertimeHours());
                    restOvertimeHoursAll=restOvertimeHoursAll.add(subordinatePaperEntryStaticVO.getRestOvertimeHours());
                    holidayOvertimeHoursAll=holidayOvertimeHoursAll.add(subordinatePaperEntryStaticVO.getHolidayOvertimeHours());
                }
            }
            //格式化
            subordinatePaperEntryStaticStrVO.setNormalHours(CommonUtils.unitConversion(normalHoursAll).setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP).toString());
            subordinatePaperEntryStaticStrVO.setAddedHours(CommonUtils.unitConversion(addedHoursAll).setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP).toString());
            subordinatePaperEntryStaticStrVO.setWorkOvertimeHours(CommonUtils.unitConversion(workOvertimeHoursAll).setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP).toString());
            subordinatePaperEntryStaticStrVO.setRestOvertimeHours(CommonUtils.unitConversion(restOvertimeHoursAll).setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP).toString());
            subordinatePaperEntryStaticStrVO.setHolidayOvertimeHours(CommonUtils.unitConversion(holidayOvertimeHoursAll).setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP).toString());
            //调休工时
            subordinatePaperEntryStaticStrVO.setOmpensatoryHours(CommonUtils.unitConversion(ompensatoryHoursAll).setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP).toString());
            //项目分摊工时：=正常工时+休息日加班+节假日加班+调休工时
            BigDecimal projectShareHours = normalHoursAll
                    .add(restOvertimeHoursAll)
                    .add(holidayOvertimeHoursAll)
                    .add(ompensatoryHoursAll);
            subordinatePaperEntryStaticStrVO.setProjectShareHours(CommonUtils.unitConversion(projectShareHours)
                    .setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP).toString());
        }
        return subordinatePaperEntryStaticStrVO;
    }

    @Override
    public Page<DailPanelPaperPageVO> findByPanel(Page page, SubordinatesDailyPaperDTO dto) {
        Long userId = SecurityUtils.getUser().getId();
        dto.setUserId(userId);
        Page<DailPanelPaperPageVO> bySubordinate = mapper.findByPanel(Page.of(dto.getPageNumber(), dto.getPageSize()), dto);
        List<DailPanelPaperPageVO> records = bySubordinate.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            List<Long> taskIds = Lists.newArrayList();
            List<Long> projectIds = Lists.newArrayList();
            List<DailyPaperEntryVO> voList = new ArrayList<>();
            for (DailPanelPaperPageVO record : records) {
                taskIds.add(record.getTaskId());
                projectIds.add(record.getProjectId());
                DailyPaperEntryVO dailyPaperEntryVO = new DailyPaperEntryVO();
                dailyPaperEntryVO.setId(record.getId());
                dailyPaperEntryVO.setUserId(userId);
                dailyPaperEntryVO.setTaskId(record.getTaskId());
                dailyPaperEntryVO.setProjectId(record.getProjectId());
                dailyPaperEntryVO.setSubmissionDate(record.getSubmissionDate());
                voList.add(dailyPaperEntryVO);
            }
            //查询明日计划
            dto.setTaskIds(taskIds);
            // 获取昨日计划
            Map<Long, DailyReviewProjectAuditPageVO> yesterdayPlanMap =
                    planPaperEntryMapper.getYesterdayPlanByIds(voList);
            List<TomorrowPlanPaperEntry> planPaperList = planPaperEntryMapper.findByFilter(dto);
//            Map<String, TomorrowPlanPaperEntry> planPapeMap = planPaperList.stream().collect(
//                    Collectors.toMap(p -> "" + p.getTaskId() + p.getSubmissionDate(), p -> p, (existing, replacement) -> replacement));
            //查询oa加班工时
            List<OvertimeLeaveData> overtimeDataListByUserId = overtimeLeaveDataMapper.getOvertimeDataListByUserId(LocalDateTimeUtil.parseDate(dto.getStartTime()), LocalDateTimeUtil.parseDate(dto.getEndTime()),
                    userId, Lists.newArrayList(LeaveStatusEnum.JB.getValue()));
            Map<String, List<OvertimeLeaveData>> overtimeDataMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(overtimeDataListByUserId)) {
                overtimeDataMap = overtimeDataListByUserId.stream().collect(Collectors.groupingBy(data -> data.getBelongdate() + StrPool.DASHED + data.getXmmc()));
            }
            //查询项目审核人
            List<Long> taskIdList = records.stream().map(DailPanelPaperPageVO::getTaskId).distinct().collect(Collectors.toList());
            if (CollUtil.isNotEmpty(taskIdList)) {
                Map<Long, TaskReviewerInfoBO> reviewerInfo = pmsRetriever.getReviewerInfo(userId, taskIdList);
                records.forEach(re -> {
                    TaskReviewerInfoBO r = reviewerInfo.get(re.getTaskId());
                    List<String> auditorNames = new ArrayList<>();
                    // 判断主责审核人类型并设置字段值
                    if (r.getTypes().contains(TaskReviewerTypeEnum.DIRECT_LEADER) && Optional.ofNullable(r.getDirectLeaderUserId()).isPresent()) {
                        auditorNames.add(ZJSJ + ": " + Optional.ofNullable(rosterMapper.selectById(r.getDirectLeaderUserId())).map(Roster::getAliasName).orElse(StrUtil.EMPTY));
                    }
                    if (r.getTypes().contains(TaskReviewerTypeEnum.IRON_TRIANGLE)) {
                        if (Optional.ofNullable(r.getProjectManagerUserId()).isPresent()) {
                            Optional.ofNullable(rosterMapper.selectById(r.getProjectManagerUserId())).ifPresent(roster -> auditorNames.add(XMJL + ": " + roster.getAliasName()));
                        }
                        if (Optional.ofNullable(r.getProjectPreSalesmanUserId()).isPresent()) {
                            Optional.ofNullable(rosterMapper.selectById(r.getProjectPreSalesmanUserId())).ifPresent(roster -> auditorNames.add(SQJL + ": " + roster.getAliasName()));
                        }
                        if (Optional.ofNullable(r.getProjectSalesmanUserId()).isPresent()) {
                            Optional.ofNullable(rosterMapper.selectById(r.getProjectSalesmanUserId())).ifPresent(roster -> auditorNames.add(XSJL + ": " + roster.getAliasName()));
                        }
                    }
                    if (r.getTypes().contains(TaskReviewerTypeEnum.TASK_LEADER) && CollUtil.isNotEmpty(r.getTaskLeaderUserIds())) {
                        LambdaQueryWrapper<Roster> queryWrapper = new LambdaQueryWrapper<>();
                        queryWrapper.in(Roster::getId, r.getTaskLeaderUserIds());
                        Optional.ofNullable(rosterMapper.selectList(queryWrapper)).ifPresent(rosters -> auditorNames.add(RWFZR + ": " + rosters.stream().map(Roster::getAliasName).collect(Collectors.joining("、"))));
                    }
                    re.setAuditorNames(auditorNames);
                });
            }
            List<Holiday> holidayList = holidayMapper.findHolidayList();
            LinkedHashMap<LocalDate, Integer>  holidayMap
                    = holidayList.stream()
                    .filter(h-> h.getHolidayType()!=null)
                    .collect(Collectors.toMap(Holiday::getDayDate, Holiday::getHolidayType, (v1, v2) -> v1, LinkedHashMap::new));
            for (DailPanelPaperPageVO record : records) {
//                TomorrowPlanPaperEntry tomorrowPlanPaperEntry = planPapeMap.get("" + record.getTaskId() + record.getSubmissionDate());
//                record.setYesterdayPlan(tomorrowPlanPaperEntry != null ? tomorrowPlanPaperEntry.getDescription() : null);
                record.setSubmissionDateFormatted(DailyPaperDateUtils.asDateString(record.getSubmissionDate()));
                record.setHolidayType(holidayMap.get(record.getSubmissionDate()));

                record.setYesterdayPlan(yesterdayPlanMap.getOrDefault(record.getId(), new DailyReviewProjectAuditPageVO()).getYesterdayPlan());
                record.setApprovalStatusName(ApprovalStatusEnum.getNameByVal(record.getApprovalStatus()));
                record.setNormalHours(CommonUtils.unitConversion(record.getNormalHours()));
                record.setAddedHours(CommonUtils.unitConversion(record.getAddedHours()));
                record.setOaAddedHours(CommonUtils.unitConversion(record.getOaAddedHours()));
                record.setSum(record.getNormalHours().add(record.getAddedHours()));
                List<OvertimeLeaveData> overtimeLeaveData = overtimeDataMap.get(record.getSubmissionDate() + StrPool.DASHED + record.getProjectId());
                if (overtimeLeaveData != null) {
                    BigDecimal oaHours = BigDecimal.ZERO;
                    for (OvertimeLeaveData overtimeLeaveDatum : overtimeLeaveData) {
                        oaHours = oaHours.add(overtimeLeaveDatum.getHourData());
                    }
                    record.setOaAddedHours(CommonUtils.unitConversion(oaHours));
                }
            }
        }
        return bySubordinate;
    }

    @Override
    public R<PanelProjectSituationAnalysisVO> analysisTotal(SubordinatesDailyPaperDTO dto) {
        dto.setUserId(SecurityUtils.getUser().getId());
        PanelProjectSituationAnalysisVO vo = mapper.panelSituationAnalysis(dto);
        if (ObjectUtil.isNotNull(vo)) {
            vo.setNormalHours(CommonUtils.unitConversion(vo.getNormalHours()));
            vo.setAddedHours(CommonUtils.unitConversion(vo.getAddedHours()));
            vo.setWorkOvertimeHours(CommonUtils.unitConversion(vo.getWorkOvertimeHours()));
            vo.setRestOvertimeHours(CommonUtils.unitConversion(vo.getRestOvertimeHours()));
            vo.setHolidayOvertimeHours(CommonUtils.unitConversion(vo.getHolidayOvertimeHours()));
            vo.setProjectHours(vo.getNormalHours().add(vo.getAddedHours()));
        }
        return R.ok(vo);
    }

    private void handleFilter(Map<String, Object> filter) {
        Object userIdsObj = filter.get(PARAM_USER_IDS);
        Object projectIds = filter.get(PARAM_PROJECT_IDS);
        Object taskIds = filter.get(PARAM_TASK_IDS);
        Object projectStatusList = filter.get(PROJECT_STATUS_LIST);
        Object projectTypeList = filter.get(PROJECT_TYPE_LIST);
        if (userIdsObj != null) {
            filter.put(PARAM_USER_IDS, ImmutableList.copyOf(((String) userIdsObj).split(",")));
        }
        if (projectIds != null) {
            filter.put(PARAM_PROJECT_IDS, ImmutableList.copyOf(((String) projectIds).split(",")));
        }
        if (taskIds != null) {
            filter.put(PARAM_TASK_IDS, ImmutableList.copyOf(((String) taskIds).split(",")));
        }
        if (projectStatusList != null) {
            filter.put(PROJECT_STATUS_LIST, ImmutableList.copyOf(((String) projectStatusList).split(",")));
        }
        if (projectTypeList != null) {
            filter.put(PROJECT_TYPE_LIST, ImmutableList.copyOf(((String) projectTypeList).split(",")));
        }
    }

    private List<DailyPaperEntryExcelVO> dailyPaperEntryListToExcelVOList(
            List<DailyPaperEntryExcelVO> entryList,
            LocalDate startDate,
            LocalDate endDate
    ) {
        Set<Long> userIds;
        Table<Long, LocalDate, Map<Long, BigDecimal>> userIdAndSubmissionDateAndOaAddedHourMap;
        List<DailyPaperEntryExcelVO> resultList;

        userIdAndSubmissionDateAndOaAddedHourMap = HashBasedTable.create();
        userIds = BaseEntityUtils.mapCollectionToSet(entryList, DailyPaperEntryExcelVO::getUserId);
        overtimeLeaveDataMapper.getOvertimeDataListByUserIds(
                startDate == null ? LocalDate.MIN : startDate,
                endDate == null ? LocalDate.MAX : endDate,
                userIds
        ).forEach(olData -> {
            Long projectId = olData.getXmmc();
            LocalDate belongdate = olData.getBelongdate();
            Map<Long, BigDecimal> userIdOverDataMap;

            if (projectId != null) {
                if (userIdAndSubmissionDateAndOaAddedHourMap.contains(projectId, belongdate)) {
                    userIdOverDataMap = userIdAndSubmissionDateAndOaAddedHourMap.get(projectId, belongdate);
                    userIdOverDataMap.put(olData.getOaId(), olData.getHourData());
                } else {
                    userIdOverDataMap = Maps.newHashMap();
                    userIdOverDataMap.put(olData.getOaId(), olData.getHourData());
                    userIdAndSubmissionDateAndOaAddedHourMap.put(projectId, belongdate, userIdOverDataMap);
                }
            }
        });
        resultList = BaseEntityUtils.mapCollectionToList(entryList, entry -> {
            Map<Long, BigDecimal> userIdAddedHourMap =
                    userIdAndSubmissionDateAndOaAddedHourMap.get(entry.getProjectId(), entry.getSubmissionDate());
            BigDecimal oaAddedHour = userIdAddedHourMap == null ?
                    null : userIdAddedHourMap.get(entry.getUserId());

            oaAddedHour = oaAddedHour == null ? BigDecimal.ZERO : oaAddedHour;
            entry.setOaAddedHours(CommonUtils.unitConversion(oaAddedHour));
            return entry;
        });

        return resultList;
    }

    @Override
    public Page<DailyPaperEntryVO> findPageByTaskId(PageRequest pageRequest, Map<String, Object> filter) {
        Object taskIdObj = filter.get(PARAM_TASK_ID);
        if (!Optional.ofNullable(taskIdObj).isPresent()) {
            throw new ServiceException("任务id不能为空");
        }
        Object hourTypeObj = filter.get(PARAM_HOUR_STATUS);
        if (!Optional.ofNullable(hourTypeObj).isPresent()) {
            throw new ServiceException("工时类型不能为空");
        }
        Long taskId = Long.valueOf(taskIdObj.toString());
        Integer hourType = Integer.valueOf(hourTypeObj.toString());
        Page<DailyPaperEntry> dailyPaperEntryPage;
        if (hourType.equals(0)) {
            dailyPaperEntryPage = mapper.findDSHPageByTaskIds(Page.of(pageRequest.getPageNumber(), pageRequest.getPageSize()), ImmutableList.of(taskId));
        } else {
            dailyPaperEntryPage = mapper.findInvalidPageByTaskIds(Page.of(pageRequest.getPageNumber(), pageRequest.getPageSize()), ImmutableList.of(taskId));
        }
        Page<DailyPaperEntryVO> dshDailyPaperEntryVoPage = new Page<>();
        dshDailyPaperEntryVoPage.setCurrent(dailyPaperEntryPage.getCurrent());
        dshDailyPaperEntryVoPage.setSize(dailyPaperEntryPage.getSize());
        dshDailyPaperEntryVoPage.setTotal(dailyPaperEntryPage.getTotal());
        List<DailyPaperEntry> records = dailyPaperEntryPage.getRecords();
        if (CollUtil.isEmpty(records)) {
            dshDailyPaperEntryVoPage.setRecords(ImmutableList.of());
            return dshDailyPaperEntryVoPage;
        }

        List<DailyPaperEntryVO> dshDailyPaperEntryVos = BeanUtil.copyToList(records, DailyPaperEntryVO.class);
        // 填充日期属性
        dshDailyPaperEntryVos.forEach(vo -> {
            LocalDate date = vo.getSubmissionDate();
            vo.setHolidayType(holidayMapper.findHolidayType(date));
            vo.setSubmissionDateFormatted(DailyPaperDateUtils.asDateString(date));
            // 填写工时类型描述
            vo.setWorkTypeTxt(EnumUtils.getNameByValue(WorkTypeEnum.class, vo.getWorkType()));
        });
        if (hourType.equals(0)) {
            dshDailyPaperEntryVos.forEach(d -> {
                d.setApprovalStatusName(EnumUtils.getNameByValue(ApprovalStatusEnum.class, d.getApprovalStatus()));
                setHourToManDay(d);
            });
        } else {
            dshDailyPaperEntryVos.forEach(d -> {
                if (d.getApprovalStatus().equals(ApprovalStatusEnum.YTH.getValue())) {
                    d.setApprovalStatusName("已退回但已归档");
                } else if (d.getApprovalStatus().equals(ApprovalStatusEnum.DSH.getValue())) {
                    d.setApprovalStatusName("待审核但已归档");
                } else if (d.getApprovalStatus().equals(ApprovalStatusEnum.BTG.getValue())) {
                    d.setApprovalStatusName("不通过但已归档");
                }
                setHourToManDay(d);
            });
        }
        dshDailyPaperEntryVoPage.setRecords(dshDailyPaperEntryVos);
        return dshDailyPaperEntryVoPage;
    }

    /**
     * 设置工时->人天
     *
     * @param d 日报
     */
    private void setHourToManDay(DailyPaperEntryVO d) {
        if (d.getNormalHours().compareTo(BigDecimal.ZERO) > 0) {
            d.setNormalHours(formatHours(d.getNormalHours()));
        } else {
            d.setNormalHours(null);
        }
        if (d.getAddedHours().compareTo(BigDecimal.ZERO) > 0) {
            d.setAddedHours(formatHours(d.getAddedHours()));
        } else {
            d.setAddedHours(null);
        }
    }

    /**
     * 格式化工时
     *
     * @param hour 工时
     * @return {@link BigDecimal}
     */
    private BigDecimal formatHours(BigDecimal hour) {
        DecimalFormat decimalFormat = new DecimalFormat("0.00");
        int newScale = 2;
        RoundingMode roundingMode = RoundingMode.HALF_UP;
        if (Optional.ofNullable(hour).isPresent()) {
            hour = hour.divide(BigDecimal.valueOf(7), newScale, roundingMode).stripTrailingZeros();
            return new BigDecimal(DecimalFormatUtil.setAndValidate(hour.toString(), newScale, roundingMode, decimalFormat)).stripTrailingZeros();
        }
        return null;
    }
}
