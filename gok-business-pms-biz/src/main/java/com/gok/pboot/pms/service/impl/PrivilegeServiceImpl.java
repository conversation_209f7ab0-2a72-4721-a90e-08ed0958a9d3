package com.gok.pboot.pms.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.Util.NewOldComparer;
import com.gok.pboot.pms.Util.PageAdapter;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.entity.Privilege;
import com.gok.pboot.pms.entity.domain.ProjectInfo;
import com.gok.pboot.pms.entity.domain.Roster;
import com.gok.pboot.pms.entity.dto.PrivilegeAddAndUpdateDTO;
import com.gok.pboot.pms.entity.dto.PrivilegeFindPageDTO;
import com.gok.pboot.pms.entity.vo.AdminConfigFindPageVo;
import com.gok.pboot.pms.entity.vo.PrivilegeUserInfoVO;
import com.gok.pboot.pms.entity.vo.SysDeptOutVO;
import com.gok.pboot.pms.entity.vo.SysUserDataScopeVO;
import com.gok.pboot.pms.enumeration.PrivilegeTypeEnum;
import com.gok.pboot.pms.enumeration.ProjectStatusEnum;
import com.gok.pboot.pms.handler.ProjectScopeHandle;
import com.gok.pboot.pms.mapper.PrivilegeMapper;
import com.gok.pboot.pms.mapper.ProjectInfoMapper;
import com.gok.pboot.pms.mapper.RosterMapper;
import com.gok.pboot.pms.service.IPrivilegeService;
import com.gok.pboot.pms.service.fegin.CenterDeptService;
import com.google.common.base.Strings;
import com.google.common.collect.HashMultimap;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * 部门审核人员及权限表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-29
 */
@Service
@Slf4j
@Transactional(readOnly = true, rollbackFor = Exception.class)
@AllArgsConstructor
public class PrivilegeServiceImpl implements IPrivilegeService {

    private final PrivilegeMapper mapper;
    private final ProjectInfoMapper projectInfoMapper;

//    private final CenterUserService centerUserService;
    private final CenterDeptService centerDeptService;
    private final ProjectScopeHandle scopeHandle;

    private final RosterMapper rosterMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApiResult save(PrivilegeAddAndUpdateDTO request) {
        Long projectId = request.getProjectId();
        List<Privilege> privileges = mapper.selectList(new QueryWrapper<Privilege>().eq("project_id", projectId).eq("del_flag", 0));
        if (CollUtil.isNotEmpty(privileges)) {
            return ApiResult.failure("该项目已有配置记录，请勿重复配置！");
        }

        // PMS人员列表校验
        List<Long> mhourAuditorIdList = CollUtil.isNotEmpty(request.getMhourAuditorIdList()) ? request.getMhourAuditorIdList() : new ArrayList<>();
        List<Long> operatorIdList = CollUtil.isNotEmpty(request.getOperatorIdList()) ? request.getOperatorIdList() : new ArrayList<>();
        // key-userId value-name
        Map<Long, String> idNameMap = getRosterNameMap(mhourAuditorIdList, operatorIdList);
        if (CollUtil.isEmpty(idNameMap)) {
            return ApiResult.failure("该项目管理人员不存在！");
        }

        ArrayList<Privilege> privilegeArrayList = new ArrayList<>();
        fillPrivilegeList(mhourAuditorIdList, PrivilegeTypeEnum.PROJECT_AUDITOR, projectId, idNameMap, privilegeArrayList);
        fillPrivilegeList(operatorIdList, PrivilegeTypeEnum.PROJECT_OPERATOR, projectId, idNameMap, privilegeArrayList);

        mapper.batchSave(privilegeArrayList);
        return ApiResult.success("操作成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApiResult update(PrivilegeAddAndUpdateDTO request) {
        Long projectId = request.getProjectId();
        List<Privilege> privileges = mapper.selectList(new QueryWrapper<Privilege>().eq("project_id", projectId).eq("del_flag", 0));

        // 获取新增/删除的人员id集合
        NewOldComparer newOldAuditorComparer = getNewOldComparer(privileges, request, PrivilegeTypeEnum.PROJECT_AUDITOR);
        NewOldComparer newOldOperatorComparer = getNewOldComparer(privileges, request, PrivilegeTypeEnum.PROJECT_OPERATOR);
        List<Long> insertAuditorIds = newOldAuditorComparer.insertIds();
        List<Long> deleteAuditorIds = newOldAuditorComparer.deleteIds();
        List<Long> insertOperatorIds = newOldOperatorComparer.insertIds();
        List<Long> deleteOperatorIds = newOldOperatorComparer.deleteIds();

        // 新增工时审核员和项目操作员
        Map<Long, String> idNameMap = getRosterNameMap(insertAuditorIds, insertOperatorIds);
        if (CollUtil.isNotEmpty(idNameMap)) {
            ArrayList<Privilege> privilegeArrayList = new ArrayList<>();
            fillPrivilegeList(insertAuditorIds, PrivilegeTypeEnum.PROJECT_AUDITOR, projectId, idNameMap, privilegeArrayList);
            fillPrivilegeList(insertOperatorIds, PrivilegeTypeEnum.PROJECT_OPERATOR, projectId, idNameMap, privilegeArrayList);
            if (!privilegeArrayList.isEmpty()) {
                mapper.batchSave(privilegeArrayList);
            }
        }

        // 根据 项目id , id集合 , 人员类型 进行逻辑删除
        if (CollUtil.isNotEmpty(deleteAuditorIds)) {
            mapper.batchDelByPjIdAndUserIdsAndType(projectId, deleteAuditorIds, PrivilegeTypeEnum.PROJECT_AUDITOR.getValue());
        }
        if (CollUtil.isNotEmpty(deleteOperatorIds)) {
            mapper.batchDelByPjIdAndUserIdsAndType(projectId, deleteOperatorIds, PrivilegeTypeEnum.PROJECT_OPERATOR.getValue());
        }

        // 同步菜单权限给中台

        return ApiResult.success("操作成功");
    }

    /**
     * 填充 privilegeArrayList
     *
     * @param ids                用户id列表
     * @param privilegeType      权限
     * @param projectId          项目id
     * @param idNameMap          用户id-name映射集合
     * @param privilegeArrayList 需要填充的列表
     */
    private static void fillPrivilegeList(
            List<Long> ids, PrivilegeTypeEnum privilegeType,
            Long projectId, Map<Long, String> idNameMap, List<Privilege> privilegeArrayList) {
        ids.forEach(a -> {
            Privilege privilege = new Privilege();
            privilege.setPrivilegeType(privilegeType.getValue());
            privilege.setProjectId(projectId);
            privilege.setUserId(a);
            privilege.setUserName(idNameMap.get(a));
            BaseBuildEntityUtil.buildInsert(privilege);
            privilegeArrayList.add(privilege);
        });
    }

    /**
     * 获取新旧id比较类
     *
     * @param privileges        当前审核人员信息集合[]
     * @param request           编辑审核人员请求实体
     * @param privilegeTypeEnum 审核人员类型
     * @return 新旧id比较类
     */
    private NewOldComparer getNewOldComparer(List<Privilege> privileges,
                                             PrivilegeAddAndUpdateDTO request,
                                             PrivilegeTypeEnum privilegeTypeEnum) {
        privileges = CollUtil.isNotEmpty(privileges) ? privileges : new ArrayList<>();
        // 获取旧审核人员id集合
        List<Long> oldIds = privileges.stream()
                .filter(p -> privilegeTypeEnum.getValue().equals(p.getPrivilegeType()))
                .map(Privilege::getUserId).collect(Collectors.toList());
        List<Long> newIds = PrivilegeTypeEnum.PROJECT_AUDITOR.equals(privilegeTypeEnum)
                ? request.getMhourAuditorIdList()
                : request.getOperatorIdList();
        NewOldComparer newOldComparer = new NewOldComparer();
        newOldComparer.NewOldComparer(newIds, oldIds);

        return newOldComparer;
    }

    /**
     * 获取审核人员对应用户名映射集合
     *
     * @param mhourAuditorIdList 工时审核员id集合
     * @param operatorIdList     项目操作员id集合
     * @return key-userId value-name
     */
    private Map<Long, String> getRosterNameMap(List<Long> mhourAuditorIdList, List<Long> operatorIdList) {
        // 整合用户id集合
        mhourAuditorIdList = Optional.ofNullable(mhourAuditorIdList).orElse(new ArrayList<>());
        operatorIdList = Optional.ofNullable(operatorIdList).orElse(new ArrayList<>());
        List<Long> userIds = Stream.concat(mhourAuditorIdList.stream(), operatorIdList.stream())
                .collect(Collectors.toList());
//        // PMS人员列表校验
//        // 远程调用 此处仅用到用户信息的用户id、name
//        UserPmsDTO userPmsDTO = new UserPmsDTO();
//        userPmsDTO.setUserIds(userIds);
//        Optional<List<SysUserOutVO>> sysUserOutVOList = centerUserService.getUserListByMultiParameterPms(userPmsDTO).unpack();
//        List<SysUserVO> sysUserVoList = new ArrayList<>();
//        if (sysUserOutVOList.isPresent() && !sysUserOutVOList.get().isEmpty()) {
//            for (SysUserOutVO sysUserOutVO : sysUserOutVOList.get()) {
//                SysUserVO sysUserVO = SysUserVO.from(sysUserOutVO);
//                sysUserVoList.add(sysUserVO);
//            }
//            return sysUserVoList.stream().collect(Collectors.toMap(SysUserVO::getUserId, Function.identity(), (a, b) -> a));
//        }
//        return new HashMap<>();
        // 获取用户id-姓名映射
        return rosterMapper.selectBatchIds(userIds)
                .stream().collect(Collectors.toMap(Roster::getId, Roster::getAliasName));
    }

    @Override
    @Transactional
    public void batchDel(List<Long> list) {
        mapper.batchDel(list);
    }

    @Override
    public Page<AdminConfigFindPageVo> findPage(PageRequest pageRequest, PrivilegeFindPageDTO privilegeFindPageDTO) {
        // 分页查询获取各项目管理人员配置信息
        Page<AdminConfigFindPageVo> page = new Page<>(pageRequest.getPageNumber(), pageRequest.getPageSize());
        SysUserDataScopeVO dataScope = scopeHandle.findDataScope();
        List<AdminConfigFindPageVo> adminConfigList =
                mapper.adminConfigFindPageVo(new PageAdapter(page), privilegeFindPageDTO, dataScope);

        // userIds记录所有项目操作员和工时审核员id
        List<Long> userIds = new ArrayList<>();
        if (CollUtil.isNotEmpty(adminConfigList)) {
            adminConfigList.forEach(vo -> {
                // 遍历获取每个项目操作员和工时审核员的user id
                List<PrivilegeUserInfoVO> userList = vo.getUserList();
                userList.forEach(userItem -> userIds.add(userItem.getUserId()));
            });
        }

        // 获取用户和部门映射集合 key-用户user id  value-用户当前部门全称
        Map<Long, String> deptMap = getUserIdDeptIdMap(userIds);
        // 补全响应字段
        assembleAdminConfigVoList(adminConfigList, deptMap);

        Map<String, Object> map = mapper.totalMap(privilegeFindPageDTO, dataScope);
        Long total = (Long) map.get("total");
        page.setRecords(adminConfigList);
        page.setTotal(total);
        return page;
    }

    @Override
    public Page<AdminConfigFindPageVo> findPage2(PageRequest pageRequest, PrivilegeFindPageDTO privilegeFindPageDTO) {
        Page<AdminConfigFindPageVo> page = new Page<>(
                pageRequest.getPageNumber(),
                pageRequest.getPageSize()
        );
        // 查询 项目列表
        Page<ProjectInfo> projectInfoPage = projectInfoMapper.findPageAboutPrivilege(page, privilegeFindPageDTO);
        List<AdminConfigFindPageVo> adminConfigFindRecords = new ArrayList<>();

        // 若空直接返回
        List<ProjectInfo> records = projectInfoPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return new Page<>();
        }

        // 构建基础返回信息
        toAdminConfigFindRecords(adminConfigFindRecords, records);

        // 通过项目id查询匹配的权限数据
        List<Long> projectIdList = adminConfigFindRecords.stream().map(AdminConfigFindPageVo::getProjectId).collect(Collectors.toList());
        List<Privilege> privilegeList = mapper.getByProjectId(projectIdList);

        // 构建完整返回信息：处理获得的权限人员、部门信息、创建时间、更新时间（取最大值）
        if (CollectionUtils.isNotEmpty(privilegeList)) {
            toCompleteAdminConfigFindRecords(adminConfigFindRecords, privilegeList);
        }

        // 封装返回值
        page.setTotal(projectInfoPage.getTotal());
        page.setRecords(adminConfigFindRecords);
        return page;
    }

    /**
     * 获取用户和部门映射集合 key-用户user id  value-用户当前部门全称
     *
     * @param userIds 用户id集合
     * @return map
     */
    private Map<Long, String> getUserIdDeptIdMap(Collection<Long> userIds) {
        Map<Long, String> deptMap = new HashMap<>();
        if (CollUtil.isNotEmpty(userIds)) {
//            // 根据用户id集合进行PMS用户查询
//            UserPmsDTO userPmsDTO = new UserPmsDTO();
//            userPmsDTO.setUserIds(userIds);
//            List<SysUserOutVO> sysUserOutVOList = centerUserService.getUserListByMultiParameterPms(userPmsDTO).unpack().orElse(new ArrayList<>());
//            List<SysDeptOutVO> sysDeptOutVOList = centerDeptService.getOrgStructList().unpack().orElse(new ArrayList<>());
//            Map<Long, String> deptNameMap = sysDeptOutVOList.stream().collect(Collectors.toMap(SysDeptOutVO::getDeptId, SysDeptOutVO::getName, (a, b) -> a));
//            deptMap.putAll(sysUserOutVOList
//                    .stream()
//                    .filter(s -> MapUtils.isNotEmpty(s.getDeptMap()))
//                    .collect(Collectors.toMap(
//                            SysUserOutVO::getUserId,
//                            a -> deptNameMap.get(a.getDeptMap().keySet().iterator().next()),
//                            (a, b) -> a
//                    )));
            // 获取用户id-部门id映射集合
            Map<Long, Long> userIdDeptIdMap = rosterMapper.selectBatchIds(userIds)
                    .stream().collect(Collectors.toMap(Roster::getId, Roster::getDeptId));
            // 获取部门id-部门名称映射集合
            List<SysDeptOutVO> sysDeptList = centerDeptService.getOrgStructList().unpack().orElse(ImmutableList.of());
            Map<Long, String> deptToNameMap = sysDeptList.stream()
                    .collect(Collectors.toMap(SysDeptOutVO::getDeptId, SysDeptOutVO::getName, (a, b) -> a));
            // 构造用户id-部门名称映射集合
            deptMap.putAll(userIdDeptIdMap.entrySet().stream()
                    .collect(Collectors.toMap(Map.Entry::getKey, entry -> deptToNameMap.get(entry.getValue()))));
        }
        return deptMap;
    }

    /**
     * 补全权限信息到返回值里
     *
     * @param adminConfigFindRecords 最终返回值 records
     * @param privilegeList          权限信息列表
     */
    private void toCompleteAdminConfigFindRecords(List<AdminConfigFindPageVo> adminConfigFindRecords, List<Privilege> privilegeList) {
        // 生成对应的map
        HashMultimap<Long, Privilege> multimap = HashMultimap.create();
        privilegeList.forEach(p -> multimap.put(p.getProjectId(), p));
        // 对于权限内容有信息的值进行添加

        // userIds记录所有项目操作员和工时审核员id
        Set<Long> userIds = privilegeList.stream().map(Privilege::getUserId).collect(Collectors.toSet());
        // 获取 用户和部门映射集合 key-用户user id  value-用户当前部门全称
        Map<Long, String> deptMap = getUserIdDeptIdMap(userIds);
        // 该有的 参数都有了，开始补全响应字段
        adminConfigFindRecords.forEach(a -> {
            if (multimap.containsKey(a.getProjectId())) {
                List<PrivilegeUserInfoVO> mhourAuditor = new ArrayList<>();
                List<PrivilegeUserInfoVO> projectOperator = new ArrayList<>();
                // 获取权限信息集合
                List<Privilege> privileges = Lists.newArrayList(multimap.get(a.getProjectId()));
                // 遍历集合，设置mhourAuditor，projectOperator的值
                privileges.forEach(b -> {
                    Long userId = b.getUserId();
                    Integer privilegeType = b.getPrivilegeType();
                    PrivilegeUserInfoVO infoVO = new PrivilegeUserInfoVO();
                    if (PrivilegeTypeEnum.PROJECT_AUDITOR.getValue().equals(privilegeType)) {
                        infoVO.setUserId(userId);
                        infoVO.setName(b.getUserName());
                        infoVO.setPrivilegeType(privilegeType);
                        infoVO.setDeptName(deptMap.getOrDefault(userId, StrUtil.EMPTY));
                        mhourAuditor.add(infoVO);
                    } else if (PrivilegeTypeEnum.PROJECT_OPERATOR.getValue().equals(privilegeType)) {
                        infoVO.setUserId(userId);
                        infoVO.setName(b.getUserName());
                        infoVO.setPrivilegeType(privilegeType);
                        infoVO.setDeptName(deptMap.getOrDefault(userId, StrUtil.EMPTY));
                        projectOperator.add(infoVO);
                    }
                });
                // 集合遍历结束，将值插入到有权限配置信息的项目里
                a.setMhourAuditor(mhourAuditor);
                a.setProjectOperator(projectOperator);
                // 补全创建时间和更新时间（创建时间取ctime和mtime最大值）
                privileges.stream()
                        .map(privilege -> Stream.of(privilege.getCtime(), privilege.getMtime()))
                        .flatMap(ts -> ts.filter(Objects::nonNull))
                        .max(Comparator.naturalOrder())
                        .ifPresent(a::setCtime);
                privileges.stream()
                        .map(Privilege::getMtime)
                        .filter(Objects::nonNull)
                        .max(Comparator.naturalOrder()).ifPresent(a::setMtime);
            }
            //// 补全项目状态信息
            //String projectStatusName = StrUtil.isNotBlank(a.getProjectStatusName())
            //        ? BusinessStatusEnum.getNameByVal(Integer.valueOf(a.getProjectStatusName()))
            //        : StrUtil.EMPTY;
            //a.setProjectStatusName(projectStatusName);
        });
    }

    /**
     * 将ProjectInfo对象转换为AdminConfigFindPageVo对象
     */
    private void toAdminConfigFindRecords(List<AdminConfigFindPageVo> adminConfigFindRecords, List<ProjectInfo> records) {
        records.forEach(r -> {
            AdminConfigFindPageVo resultVo = new AdminConfigFindPageVo();
            resultVo.setProjectId(r.getId());
            resultVo.setProjectCode(r.getItemNo());
            resultVo.setProjectName(r.getItemName());
            resultVo.setProjectStatusName(r.getProjectStatus());
            Optional.ofNullable(r.getSalesmanUserId()).ifPresent(resultVo::setSalesmanUserId);
            Optional.ofNullable(r.getManagerUserId()).ifPresent(resultVo::setManagerUserId);
            Optional.ofNullable(r.getPreSaleUserId()).ifPresent(resultVo::setPreSalesmanUserId);
            resultVo.setSalesmanUserName(Strings.nullToEmpty(r.getProjectSalesperson()));
            resultVo.setManagerUserName(Strings.nullToEmpty(r.getManagerUserName()));
            resultVo.setPreSalesmanUserName(Strings.nullToEmpty(r.getPreSaleUserName()));
            // 补全项目状态信息
            String projectStatusName = StrUtil.isNotBlank(resultVo.getProjectStatusName())
                    ? ProjectStatusEnum.getNameByStrVal(resultVo.getProjectStatusName())
                    : StrUtil.EMPTY;
            resultVo.setProjectStatusName(projectStatusName);
            adminConfigFindRecords.add(resultVo);
        });
    }


    /**
     * 封装审核员管理响应集合
     *
     * @param adminConfigList 分页获取的审核员管理Vo类集合
     * @param deptMap         人员部门映射集合
     * @return 补全字段的审核员管理Vo类集合
     */
    private List<AdminConfigFindPageVo> assembleAdminConfigVoList(List<AdminConfigFindPageVo> adminConfigList, Map<Long, String> deptMap) {
        if (CollUtil.isEmpty(adminConfigList)) {
            log.warn("管理员配置分页查询结果为空");
            return new ArrayList<>();
        }

        // 封装响应字段
        for (AdminConfigFindPageVo voItem : adminConfigList) {
            // 补全部门字段并区分人员审核类型
            List<PrivilegeUserInfoVO> userList = voItem.getUserList();
            if (CollUtil.isEmpty(userList)) {
                continue;
            }
            List<PrivilegeUserInfoVO> mhourAuditor = new ArrayList<>();
            List<PrivilegeUserInfoVO> projectOperator = new ArrayList<>();
            userList.forEach(u -> {
                // 补全部门字段
                String deptName = deptMap.getOrDefault(u.getUserId(), StrUtil.EMPTY);
                u.setDeptName(deptName);

                // 根据人员审核权限封装到不同字段
                if (PrivilegeTypeEnum.PROJECT_AUDITOR.getValue().equals(u.getPrivilegeType())) {
                    mhourAuditor.add(u);
                } else if (PrivilegeTypeEnum.PROJECT_OPERATOR.getValue().equals(u.getPrivilegeType())) {
                    projectOperator.add(u);
                }
            });
            voItem.setMhourAuditor(mhourAuditor);
            voItem.setProjectOperator(projectOperator);

            // 补全项目状态信息
            String projectStatusName = StrUtil.isNotBlank(voItem.getProjectStatusName())
                    ? ProjectStatusEnum.getNameByStrVal(voItem.getProjectStatusName())
                    : StrUtil.EMPTY;
            voItem.setProjectStatusName(projectStatusName);
        }

        return adminConfigList;
    }

}
