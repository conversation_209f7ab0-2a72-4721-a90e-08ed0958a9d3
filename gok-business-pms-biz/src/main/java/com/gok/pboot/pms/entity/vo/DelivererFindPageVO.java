package com.gok.pboot.pms.entity.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 交付人员表
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class DelivererFindPageVO {
    /**
     * 项目id
     */
    @ExcelIgnore
    private Long projectId;
    /**
     * 项目名称
     */
    @ExcelProperty("项目名称")
    private String projectName;
    /**
     * 人员名称
     */
    @ExcelProperty("人员名称")
    private String personName;
    /**
     * 出勤天数
     */
    @ExcelProperty("出勤天数")
    private Double attendanceDays;
    /**
     * 项目耗用工时（人天）
     */
    @ExcelIgnore
    private BigDecimal projectConsumed;
    /**
     * 工作日正常工时
     */
    @ExcelProperty("工作日正常工时（人天）")
    private BigDecimal  normalWorkDays;

    /**
     * 工作日调休工时（人天）
     */
    @ExcelProperty("工作日调休工时（人天）")
    private BigDecimal  ompensatoryDays;


    /**
     * 休息日加班工时（人天）
     */
    @ExcelProperty("休息日加班工时（人天）")
    private BigDecimal  restWorkDays;
    /**
     * 节假日加班工时（人天）
     */
    @ExcelProperty("节假日加班工时（人天）")
    private BigDecimal  holidaysWorkDays;

    /**
     * 备注
     */
    @ExcelProperty("备注")
    private String remark;
    /**
     * 导入人id
     */
    @ExcelIgnore
    private Long executorUserId;
    /**
     * 导入人
     */
    @ExcelProperty("导入人")
    private String executorUserRealName;
}
