package com.gok.pboot.pms.eval.enums;

import com.gok.pboot.pms.enumeration.ValueEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 工单评价状态枚举
 *
 * <AUTHOR>
 * @date 2025/05/08
 */
@AllArgsConstructor
@Getter
public enum EvalTaskStatusEnum implements ValueEnum<Integer> {

    /**
     * 待评价
     */
    DPJ(0, "待评价"),

    /**
     * 已评价
     */
    YPJ(1, "已评价");


    private final Integer value;

    private final String name;

}
