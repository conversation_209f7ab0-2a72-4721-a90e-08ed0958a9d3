package com.gok.pboot.pms.cost.entity.dto;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.util.List;

/**
 * 成本估算DTO类
 *
 * <AUTHOR>
 * @create 2025/01/10
 **/
@Data
public class CostManageCalculateDTO {

    /**
     * 估算结果明细ID
     * 编辑人工测算成本时使用
     */
    private Long estimatedResultId;

    /**
     * 成本科目ID
     */
    @NotNull(message = "成本科目ID不能为空")
    private Long accountId;

    /**
     * 成本科目OA ID
     */
    @NotNull(message = "成本科目OA ID不能为空")
    private Long accountOaId;

    /**
     * 成本科目名称
     */
    @NotBlank(message = "成本科目名称不能为空")
    private String accountName;

    /**
     * 成本科目类别ID
     */
    @NotNull(message = "成本科目类别ID不能为空")
    private Long accountCategoryId;

    /**
     * 成本科目类别名称
     */
    @NotBlank(message = "成本科目类别名称不能为空")
    private String accountCategoryName;

    /**
     * 成本估算类型
     * {@link com.gok.pboot.pms.cost.enums.CostBudgetTypeEnum}
     */
    @NotNull(message = "成本估算类型不能为空")
    @Min(value = 0, message = "成本估算类型非法")
    @Max(value = 2, message = "成本估算类型非法")
    private Integer costBudgetType;

    /**
     * 人工成本项明细
     */
    @Valid
    @NotEmpty(message = "人工成本项明细不能为空")
    List<CostManagePersonnelLevelDTO> personnelLevelEntries;

    /**
     * 自定义补贴明细集合
     */
    private List<CostManagePersonnelCustomDetailDto> customDetailEntries;

}
