package com.gok.pboot.pms.entity.dto;

import com.gok.pboot.pms.common.base.PageRequest;
import lombok.*;

/**
 * 工时审核 复用+交付 项目查询
 *
 * <AUTHOR>
 * @version 1.3.2
 */
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class DailyReviewReuseAndDeliveryProjectDTO extends PageRequest {

    /**
     * 审批状态 0待审批 1已审批
     * @see com.gok.pboot.enumeration.entity.YesOrNoEnum
     */
    private Integer auditStatus;

    /**
     * 项目名称
     */
    private String projectName;
}
