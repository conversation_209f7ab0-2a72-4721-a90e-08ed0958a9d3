package com.gok.pboot.pms.cost.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.cost.entity.domain.CostDeliverTask;
import com.gok.pboot.pms.cost.entity.dto.TaskStatQueryDTO;
import com.gok.pboot.pms.cost.entity.vo.*;
import com.gok.pboot.pms.entity.domain.ProjectInfo;
import com.gok.pboot.pms.entity.domain.Roster;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 多维度工单数据统计Mapper
 *
 * <AUTHOR>
 * @date 2025/05/15
 */
@Mapper
public interface CostTaskStatMapper {

    /**
     * 分页查询项目维度交付工单统计
     *
     * @param page  页
     * @param query 查询
     * @return {@link Page }<{@link ProjectProDimDeliverStatVO }>
     */
    Page<ProjectProDimDeliverStatVO> projectProDimDeliverStatPage(Page<ProjectProDimDeliverStatVO> page, @Param("query") TaskStatQueryDTO query);

    /**
     * 查询项目维度交付工单统计(不分页)
     *
     * @param query 查询
     * @return {@link List }<{@link ProjectProDimDeliverStatVO }>
     */
    List<ProjectProDimDeliverStatVO> projectProDimDeliverStatPage(@Param("query") TaskStatQueryDTO query);


    /**
     * 查询人员维度交付工单统计(不分页)
     *
     * @param query 查询
     * @return {@link List }<{@link ProjectPerDimDeliverStatVO }>
     */
    List<ProjectPerDimDeliverStatVO> projectPerDimDeliverStatList(@Param("query") TaskStatQueryDTO query);


    /**
     * 查询交付工单维度统计(不分页)
     *
     * @param query 查询
     * @return {@link List }<{@link ProjectTaskDimDeliverStatVO }>
     */
    List<ProjectTaskDimDeliverStatVO> projectTaskDimDeliverStatList(@Param("query") TaskStatQueryDTO query);


    /**
     * 分页查询项目维度售前工单统计
     *
     * @param page  页
     * @param query 查询
     * @return {@link List }<{@link ProjectProDimPreSaleStatVO }>
     */
    Page<ProjectProDimPreSaleStatVO> projectProDimPreSaleStatPage(Page<ProjectProDimPreSaleStatVO> page, @Param("query") TaskStatQueryDTO query);

    /**
     * 查询项目维度售前工单统计(不分页)
     *
     * @param query 查询
     * @return {@link List }<{@link ProjectProDimPreSaleStatVO }>
     */
    List<ProjectProDimPreSaleStatVO> projectProDimPreSaleStatPage(@Param("query") TaskStatQueryDTO query);


    /**
     * 查找个人维度售前工单统计
     *
     * @param query 查询
     * @return {@link List }<{@link ProjectPerDimPreSaleStatVO }>
     */
    List<ProjectPerDimPreSaleStatVO> projectPerDimPreSaleStatList(@Param("query") TaskStatQueryDTO query);

    /**
     * 查找工单维度售前工单统计
     *
     * @param query 查询
     * @return {@link List }<{@link ProjectTaskDimPreSaleStatVO }>
     */
    List<ProjectTaskDimPreSaleStatVO> projectTaskDimPreSaleStatList(@Param("query") TaskStatQueryDTO query);

    /**
     * 项目维度分页
     *
     * @param projectPage 项目页面
     * @param query       查询
     * @return {@link Page }<{@link ProjectInfo }>
     */
    Page<ProjectInfo> projectPage(Page<ProjectInfo> projectPage, @Param("query") TaskStatQueryDTO query);

    /**
     * 项目维度
     *
     * @param query 查询
     * @return {@link List }<{@link ProjectInfo }>
     */
    List<ProjectInfo> projectPage(@Param("query") TaskStatQueryDTO query);

    /**
     * 获取工单
     *
     * @param query 查询
     * @return {@link List }<{@link CostDeliverTask }>
     */
    List<CostDeliverTask> getCostTasks(@Param("query") TaskStatQueryDTO query);


    /**
     * 人员工单看板-项目维度售后交付工单统计
     *
     * @param page  页
     * @param query 查询
     * @return {@link Page }<{@link PersonnelPerDimDeliverStatVO }>
     */
    Page<PersonnelPerDimDeliverStatVO> personnelPerDimDeliverStatPage(Page<PersonnelPerDimDeliverStatVO> page, @Param("query") TaskStatQueryDTO query);

    /**
     * 人员工单看板-人员维度售后交付工单统计
     *
     * @param query 查询
     * @return {@link List }<{@link PersonnelPerDimDeliverStatVO }>
     */
    List<PersonnelPerDimDeliverStatVO> personnelPerDimDeliverStatPage(@Param("query") TaskStatQueryDTO query);

    /**
     * 人员工单看板-项目维度售后交付工单统计
     *
     * @param query 查询
     * @return {@link List }<{@link PersonnelProDimDeliverStatVO }>
     */
    List<PersonnelProDimDeliverStatVO> personnelProDimDeliverStatList(@Param("query") TaskStatQueryDTO query);

    /**
     * 花名册分页
     *
     * @param rosterPage 名册页面
     * @param query      查询
     * @return {@link Page }<{@link Roster }>
     */
    Page<Roster> rosterPage(Page<Roster> rosterPage, @Param("query") TaskStatQueryDTO query);

    /**
     * 花名册列表
     *
     * @param query 查询
     * @return {@link List }<{@link Roster }>
     */
    List<Roster> rosterPage(@Param("query") TaskStatQueryDTO query);

    /**
     * 人员工单看板-工单维度售后交付工单统计
     *
     * @param query 查询
     * @return {@link List }<{@link PersonnelTaskDimDeliverStatVO }>
     */
    List<PersonnelTaskDimDeliverStatVO> personnelTaskDimDeliverStatList(@Param("query") TaskStatQueryDTO query);

    /**
     * 人员工单看板-人员维度售前支撑工单统计
     *
     * @param page  页
     * @param query 查询
     * @return {@link Page }<{@link PersonnelPerDimPreSaleStatVO }>
     */
    Page<PersonnelPerDimPreSaleStatVO> personnelPerDimPreSaleStatPage(Page<PersonnelPerDimPreSaleStatVO> page, @Param("query") TaskStatQueryDTO query);

    /**
     * 人员工单看板-人员维度售前支撑工单统计
     *
     * @param query 查询
     * @return {@link List }<{@link PersonnelPerDimPreSaleStatVO }>
     */
    List<PersonnelPerDimPreSaleStatVO> personnelPerDimPreSaleStatPage(@Param("query") TaskStatQueryDTO query);

    /**
     * 人员工单看板-项目维度售前支撑工单统计
     *
     * @param query 查询
     * @return {@link List }<{@link PersonnelProDimPreSaleStatVO }>
     */
    List<PersonnelProDimPreSaleStatVO> personnelProDimPreSaleStatList(@Param("query") TaskStatQueryDTO query);

    /**
     * 人员工单看板-工单维度售前支撑工单统计
     *
     * @param query 查询
     * @return {@link List }<{@link PersonnelTaskDimPreSaleStatVO }>
     */
    List<PersonnelTaskDimPreSaleStatVO> personnelTaskDimPreSaleStatList(@Param("query") TaskStatQueryDTO query);
}
