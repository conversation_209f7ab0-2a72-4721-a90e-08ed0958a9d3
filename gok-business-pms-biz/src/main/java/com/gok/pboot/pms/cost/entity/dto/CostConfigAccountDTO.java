package com.gok.pboot.pms.cost.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CostConfigAccountDTO {

    /**
     * OA_ID
     */
    private Long oaId;

    /**
     * 成本科目类别ID
     */
    @NotNull(message = "科目类别ID不能为空")
    private Long accountCategoryId;

    /**
     * 成本科目类别名称
     */
    @NotBlank(message = "科目类别名称不能为空")
    private String accountCategoryName;

    /**
     * 科目名称
     */
    @NotBlank(message = "科目名称不能为空")
    private String accountName;

    /**
     * 科目代码
     */
    private String accountCode;

    /**
     * 对应成本类型（0=人工成本，1=费用报销，2=外采费用）
     */
    private Integer accountType;

    /**
     * 科目定义说明
     */
    private String accountDefinitionDesc;

    /**
     * 是否用于成本估算
     * {@link com.gok.pboot.pms.enumeration.YesOrNoEnum}
     */
    private Integer forEstimateFlag;

}