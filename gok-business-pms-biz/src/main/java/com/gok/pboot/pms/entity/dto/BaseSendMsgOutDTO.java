package com.gok.pboot.pms.entity.dto;

import com.gok.bcp.message.dto.BcpMessageTargetDTO;
import com.gok.bcp.message.entity.enums.MsgTypeEnum;
import com.gok.bcp.message.entity.enums.TargetTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.gok.pboot.pms.common.constant.PmsConstants.BCP_ADMIN_ID;
import static com.gok.pboot.pms.common.constant.PmsConstants.BCP_ADMIN_NAME;

/**
 * 基地发送消息 DTO
 *
 * <AUTHOR>
 * @date 2025/05/12
 */
@Data
@Accessors(chain = true)
public class BaseSendMsgOutDTO {

    /**
     * 发送人 ID
     */
    @NotNull(message = "发送人ID不能为空")
    private Long senderId;

    /**
     * 发送人
     */
    @NotBlank(message = "发送人不能为空")
    private String sender;

    /**
     * 项目ID
     */
    @NotBlank(message = "项目ID不能为空")
    private String projectId;

    /**
     * 目标列表
     */
    @NotEmpty(message = "目标列表不能为空")
    private List<BcpMessageTargetDTO> targetList;

    /**
     * 目标类型
     */
    @NotNull(message = "目标类型不能为空")
    private TargetTypeEnum targetTypeEnum;

    /**
     * 消息类型枚举
     */
    @NotNull(message = "消息类型不能为空")
    private MsgTypeEnum msgTypeEnum;


    /**
     * 标题
     */
    @NotBlank(message = "标题类型不能为空")
    private String title;

    /**
     * 内容
     */
    @NotBlank(message = "内容不能为空")
    private String content;

    /**
     * 填充发信人
     */
    public void populateSender() {
        this.setSenderId(BCP_ADMIN_ID);
        this.setSender(BCP_ADMIN_NAME);
    }

    /**
     * 指定一个目标用户
     *
     * @param targetUserId 目标用户 ID
     * @param targetUser   目标用户
     * @param phoneNum     电话 Num
     * @return {@link BaseSendMsgDTO }
     */
    public BaseSendMsgOutDTO toOneTarget(Long targetUserId, String targetUser, String phoneNum) {
        BcpMessageTargetDTO targetDTO = new BcpMessageTargetDTO(String.valueOf(targetUserId), Optional.of(targetUser).orElse("defaultName"), phoneNum);
        this.targetList = Collections.singletonList(targetDTO);
        return this;
    }
}
