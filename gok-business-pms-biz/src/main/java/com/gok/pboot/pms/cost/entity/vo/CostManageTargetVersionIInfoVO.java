package com.gok.pboot.pms.cost.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CostManageTargetVersionIInfoVO {

    /**
     * 版本ID
     */
    private Long id;

    /**
     * 版本名称
     */
    private String versionName;

    /**
     * 项目ID
     */
    private Long projectId;


    /**
     * 关联流程ID
     */
    private Long requestId;

    /**
     * 关联流程名称
     */
    private String requestName;

    /**
     * 关联流程状态（0=未提交，1=审批中，1=已归档）
     */
    private Integer requestStatus;

    /**
     * 关联流程状态名称
     */
    private String requestStatusName;

    /**
     * 状态（0=未确认，1=已确认，2=已拒绝）
     */
    private Integer status;

    /**
     * 状态名称（0=未确认，1=已确认，2=已拒绝）
     */
    private String statusName;


    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime ctime;

    /**
     * 操作人
     */
    private String creator;

}
