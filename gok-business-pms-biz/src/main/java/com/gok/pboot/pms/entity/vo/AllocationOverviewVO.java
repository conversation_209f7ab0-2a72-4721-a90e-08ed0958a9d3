package com.gok.pboot.pms.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 工时分摊表-概览
 *
 * <AUTHOR>
 * @date 2024/03/18
 */
@Data
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AllocationOverviewVO {

    /**
     * 总工时
     */
    private String totalHours;

    /**
     * 正常工时
     */
    private String normalHours;

    /**
     * 加班工时
     */
    private String addedHours;
    /**
     * 工作日加班工时
     */
    private String workOvertimeHours;
    /**
     * 休息日加班工时
     */
    private String restOvertimeHours;
    /**
     * 节假日加班工时
     */
    private String holidayOvertimeHours;

    /**
     * 调休工时
     */
    private String leaveHours;

    /**
     * 项目分摊工时
     */
    private String projectShareHours;

    /**
     * 实际出勤天数
     */
    private String cwActualAttendance;

    public AllocationOverviewVO() {
        this.totalHours = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP).toPlainString();
        this.normalHours = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP).toPlainString();
        this.addedHours = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP).toPlainString();
        this.workOvertimeHours = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP).toPlainString();
        this.restOvertimeHours = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP).toPlainString();
        this.holidayOvertimeHours = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP).toPlainString();
        this.leaveHours = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP).toPlainString();
        this.projectShareHours = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP).toPlainString();
        this.cwActualAttendance = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP).toPlainString();
    }
}
