package com.gok.pboot.pms.enumeration;

/**
 * 任务类型枚举
 **/
public enum TaskTypeEnum implements ValueEnum<Integer> {
    /**
     * 默认任务
     */
    MRRW(0, "默认任务"),
    /**
     * 手动添加
     */
    SDTJ(1, "手动添加"),
    /**
     * 后期维保
     */
    HQWB(2, "后期维保"),
    ;

    //值
    private Integer  value;
    //名称
    private String name;

    TaskTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    /**
     * 获取值
     *
     * @return Integer
     */
    @Override
    public Integer getValue() {
        return value;
    }

    /**
     * 获取名称
     *
     * @return String
     */
    @Override
    public String getName() {
        return name;
    }

    public static String getNameByVal(Integer value) {
        for (TaskTypeEnum statusEnum : TaskTypeEnum.values()) {
            if (statusEnum.value.equals(value)) {
                return statusEnum.name;
            }
        }
        return "";
    }
    public static Integer getValByName(String name) {
        for (TaskTypeEnum statusEnum : TaskTypeEnum.values()) {
            if (statusEnum.getName().equals(name)) {
                return statusEnum.getValue();
            }
        }
        return null;
    }
}
