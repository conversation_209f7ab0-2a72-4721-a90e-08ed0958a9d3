package com.gok.pboot.pms.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.entity.domain.ProjectWeekly;
import com.gok.pboot.pms.entity.domain.ProjectWeeklyReading;
import com.gok.pboot.pms.entity.vo.ProjectWeeklyVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 项目周报表
 *
 * <AUTHOR>
 * @date 2023-07-11 17:05:26
 */
@Mapper
public interface ProjectWeeklyMapper extends BaseMapper<ProjectWeekly> {

    /**
     * 查询项目周报列表
     *
     * @param page
     * @param filter
     * @return
     */
    Page<ProjectWeeklyVO> findListPage(Page<ProjectWeekly> page,
                                       @Param("filter") Map<String, Object> filter);

    /**
     * 查询 项目周报列表
     *
     * @param page
     * @param filter
     * @return
     */
    Page<ProjectWeeklyVO> findListAllPage(Page<ProjectWeekly> page,
                                          @Param("filter") Map<String, Object> filter);

    /**
     * 批量插入
     *
     * @Param: entityList 批量插入对象
     * @return: void
     */
    void batchSave(List<ProjectWeekly> entityList);

    /**
     * 查询 用户全部项目周报列表
     *
     * @param filter
     * @return {@link List<ProjectWeeklyVO>}
     */
    List<ProjectWeeklyVO> findAllList(@Param("filter") Map<String, Object> filter);

    /**
     * 查询用户全部已读项目周报列表
     *
     * @param userId
     * @return {@link List< ProjectWeeklyReading>}
     */
    List<ProjectWeeklyReading> findReadList(@Param("userId") Long userId);

    /**
     * 插入用户阅读的周报列表
     *
     * @param newReadList
     * @return
     */
    void addReadList(@Param("newReadList") List<ProjectWeeklyReading> newReadList);

    /**
     * 根据id查询周报详情
     *
     * @param id 周报id
     * @return {@link ProjectWeekly}
     */
    ProjectWeekly findById(@Param("id") Long id);

}
