package com.gok.pboot.pms.eval.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.module.file.entity.SysFile;
import com.gok.pboot.pms.eval.entity.domain.EvalCommendationLetter;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2025/05/16
 **/
public interface IEvalCommendationLetterService extends IService<EvalCommendationLetter> {

    /**
     * 上传表扬信
     *
     * @param projectId          项目ID
     * @param commendationLetter 表扬信
     */
    Long uploadCommendationLetters(Long projectId, String commendationLetter);

    /**
     * 获取表扬信
     *
     * @param projectId
     * @return
     */
    List<SysFile> getCommendationLetters(Long projectId);

}
