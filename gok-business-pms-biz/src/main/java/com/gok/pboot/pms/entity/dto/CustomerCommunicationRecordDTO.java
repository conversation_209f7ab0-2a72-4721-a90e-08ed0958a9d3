package com.gok.pboot.pms.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

/**
 * 客户沟通记录查询DTO
 *
 * <AUTHOR>
 * @date 2023/11/20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CustomerCommunicationRecordDTO {

    /**
     * 客户关键词
     */
    private String customerName;

    /**
     * 客户经理姓名
     */
    private String accountManager;

    /**
     * 开始时间
     */
    private LocalDate startTime;

    /**
     * 结束时间
     */
    private LocalDate endTime;

    /**
     * 登录用户的用户id
     */
    private Long userId;

    /**
     * 用户所能看到的客户沟通记录ID列表
     */
    private List<Long> ids;

}
