package com.gok.pboot.pms.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 字典项(PmsDictItem)表实体类
 *
 * <AUTHOR>
 * @since 2023-11-21 17:22:44
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("pms_dict_item")
public class PmsDictItem {

    /**
     * 字典类型ID
     */
    private Long dictId;

    /**
     * 字典类型
     */
    private String dictType;

    /**
     * key
     */
    private String itemValue;

    /**
     * value
     */
    private String label;


}