package com.gok.pboot.pms.eval.entity.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import lombok.*;

/**
 * 项目评价表扬信实体类
 *
 * <AUTHOR>
 * @create 2025/05/16
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("eval_commendation_letter")
public class EvalCommendationLetter extends BeanEntity<Long> {

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 表扬信文件id，多个逗号隔开
     */
    @TableField(fill = FieldFill.UPDATE)
    private String commendationLetter;

} 