package com.gok.pboot.pms.eval.entity.dto;

import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2025/5/8
 **/
@Data
public class EvalTaskListDTO {

    /**
     * 工单类型
     */
    private Integer taskType;

    /**
     * 评价状态
     */
    private Integer evaluationStatue;

    /**
     * 员工姓名
     */
    private String managerName;

    /**
     * 员工编号
     */
    private String managerNo;

    /**
     * 开始时间
     */
    private LocalDate startDate;

    /**
     * 结束时间
     */
    private LocalDate endDate;

    /**
     * 评价状态
     */
    private List<Integer> evaluationStatus;

    /**
     * 项目编号
     */
    private String projectNo;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 工单编号
     */
    private String taskNo;

    /**
     * 工单名称
     */
    private String taskName;

    // 权限
    /**
     * 客户端id
     */
    private Long clientId;

    /**
     * 菜单
     */
    private String menuCode;

    /**
     * 可查看范围的工单id
     */
    private List<Long> authTaskIdList;

    /**
     * 是否有全部权限
     */
    private Boolean authAll;
}
