package com.gok.pboot.pms.oa.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * OA部门月预算DTO
 *
 * <AUTHOR>
 * @date 2025/08/04
 */
@Data
public class OaBmyysbDTO {

    /**
     * id
     */
    private Long id;

    /**
     * 部门月预算编码
     */
    private String bmyysbm;
    /**
     * 预算部门
     */
    private Long ysbm;
    /**
     * 本月预算可用余额
     */
    private BigDecimal byyskyye;
    /**
     * 科目名称
     */
    private Integer hjkm;

    /**
     * 	科目代码
     */
    private String kmdm;

    /**
     * 费用项类别
     */
    private Integer fyxlb;

    /**
     * 预算年月
     */
    private String ysny;

    /**
     * 预算一级部门
     */
    private Long ysyjbm;

    /**
     * 预算二级部门
     */
    private Long ysejbm;
}
