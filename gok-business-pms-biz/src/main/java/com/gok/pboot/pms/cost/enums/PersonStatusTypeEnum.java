package com.gok.pboot.pms.cost.enums;

import com.gok.pboot.pms.enumeration.ValueEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum PersonStatusTypeEnum implements ValueEnum<Integer> {

    ZC(0, "在场"),
    YLC(1, "已离场");

    /**
     * 值
     */
    private final Integer value;

    /**
     * 名称
     */
    private final String name;

}