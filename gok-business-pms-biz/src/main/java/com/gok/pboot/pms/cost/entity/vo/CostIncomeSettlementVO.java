package com.gok.pboot.pms.cost.entity.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 收入结算列表VO
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CostIncomeSettlementVO {
    /**
     * id
     */
    @ExcelIgnore
    private Long id;

    /**
     * 项目id
     */
    @ExcelIgnore
    private Long projectId;

    /**
     * 结算开始日期
     */
    @ExcelProperty("结算开始日期")
    private LocalDate startDate;

    /**
     * 结算截止日期
     */
    @ExcelProperty("结算截止日期")
    private LocalDate endDate;

    /**
     * 收入测算明细id
     */
    @ExcelIgnore
    private Long costIncomeCalculationDetailId;

    /**
     * 测算含税金额
     */
    @ExcelProperty("测算含税金额")
    private BigDecimal estimatedInclusiveAmountTax;

    /**
     * 结算含税金额
     */
    @ExcelProperty("结算含税金额")
    private BigDecimal budgetAmountIncludedTax;

    /**
     * 税率OA字典ID
     */
    @ExcelIgnore
    private String taxRate;

    /**
     * 税率OA字典Txt
     */
    @ExcelProperty("税率")
    private String taxRateTxt;

    /**
     * 结算不含税金额
     */
    @ExcelProperty("结算不含税金额")
    private BigDecimal budgetAmountExcludingTax;

    /**
     * 备注说明
     */
    @ExcelProperty("备注说明")
    private String remarksDesc;

    /**
     * 归档日期
     */
    @ExcelProperty("归档日期")
    private String filingTime;

    /**
     * OA结算单流程id
     */
    @ExcelIgnore
    private String requestId;

    /**
     * OA结算单编号
     */
    @ExcelProperty("OA结算单编号")
    private String requestNumber;

    /**
     * oa结算单
     */
    @ExcelIgnore
    List<RequestVO> requestList;

    /**
     * 结算单编号
     */
    @ExcelProperty("结算单编号")
    private String settlementNumber;

    /**
     * 数据来源
     */
    @ExcelIgnore
    private Integer dataSources;

    /**
     * 数据来源Txt
     */
    @ExcelProperty("数据来源")
    private String dataSourcesTxt;

    /**
     * 审批状态
     */
    @ExcelIgnore
    private Integer approvalStatus;

    /**
     * 审批状态Txt
     */
    @ExcelProperty("审批状态")
    private String approvalStatusTxt;

    /**
     * 收入测算汇总ID
     */
    @ExcelIgnore
    private Long costIncomeCalculationId;

    /**
     * 是否可编辑
     */
    @ExcelIgnore
    private Boolean isEdit;
}
