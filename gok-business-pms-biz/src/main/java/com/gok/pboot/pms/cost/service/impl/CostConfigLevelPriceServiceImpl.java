package com.gok.pboot.pms.cost.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.StrPool;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.components.common.constant.SecurityConstants;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.common.exception.ServiceException;
import com.gok.pboot.pms.cost.entity.domain.CostConfigLevelPrice;
import com.gok.pboot.pms.cost.entity.domain.CostConfigVersion;
import com.gok.pboot.pms.cost.entity.dto.CalculateLaborCostDTO;
import com.gok.pboot.pms.cost.entity.dto.CostConfigLevelPriceDTO;
import com.gok.pboot.pms.cost.entity.dto.CostSalaryDTO;
import com.gok.pboot.pms.cost.entity.vo.CostConfigLevelPriceVO;
import com.gok.pboot.pms.cost.enums.CostConfigVersionTypeEnum;
import com.gok.pboot.pms.cost.mapper.CostConfigLevelPriceMapper;
import com.gok.pboot.pms.cost.service.ICostConfigLevelPriceService;
import com.gok.pboot.pms.cost.service.ICostConfigVersionService;
import com.gok.pboot.pms.service.IEhrService;
import com.gok.pboot.service.entity.hrm.vo.HrmStaffRosterVo;
import com.gok.pboot.service.feign.RemoteEhrService;
import lombok.AllArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static com.gok.pboot.pms.common.validate.validator.ManHourValidator.DAILY_NORMAL_WORKING_HOURS;

/**
 * <p>
 * 人员级别单价配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Service
@AllArgsConstructor
public class CostConfigLevelPriceServiceImpl extends ServiceImpl<CostConfigLevelPriceMapper, CostConfigLevelPrice> implements ICostConfigLevelPriceService {

    private final ICostConfigVersionService costConfigVersionService;

    private final IEhrService IEhrService;

    @Resource
    private RemoteEhrService remoteEhrService;


    /**
     * 取人员级别单价配置列表
     *
     * @return {@link Map }<{@link String }, {@link List }<{@link CostConfigLevelPriceVO }>>
     */
    @Override
    public List<CostConfigLevelPriceVO> getCostConfigLevelPriceList() {
        // 获取当前版本
        CostConfigVersion crrCostConfigVersion = costConfigVersionService.getCrrCostConfigVersion(CostConfigVersionTypeEnum.RYJBDJPZ);
        List<CostConfigLevelPriceVO> configLevelPriceVoList = Objects.nonNull(crrCostConfigVersion)
                ? baseMapper.getLevelPriceListByVersionIdPage(crrCostConfigVersion.getId())
                : Collections.emptyList();
        if (CollUtil.isEmpty(configLevelPriceVoList)) {
            return Collections.emptyList();
        }
        return handlePersonnelTypeToMap(configLevelPriceVoList);
    }

    /**
     * 编辑人员级别单价配置列表
     *
     * @param dtoList DTO 列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editCostConfigLevelPriceList(List<CostConfigLevelPriceDTO> dtoList) {
        if (CollUtil.isEmpty(dtoList)) {
            return;
        }
        // 生成版本号
        Long versionId = costConfigVersionService.generateVersionName(CostConfigVersionTypeEnum.RYJBDJPZ);
        List<CostConfigLevelPrice> configLevelPriceList = dtoList.stream()
                .map(item -> {
                    CostConfigLevelPrice costConfigLevelPrice =
                            BaseBuildEntityUtil.buildInsert(new CostConfigLevelPrice())
                                    .setVersionId(versionId)
                                    .setJobActivityId(item.getJobActivityId())
                                    .setRegion(item.getRegion())
                                    .setPersonnelLevel(item.getPersonnelLevel())
                                    .setBaseSalaryPrice(item.getBaseSalaryPrice())
                                    .setSalaryPerDay(item.getSalaryPerDay())
                                    .setSocialSecurityPerDay(item.getSocialSecurityPerDay())
                                    .setHousingFundPerDay(item.getHousingFundPerDay())
                                    // 残保金 = 工资 * 1.5% 保留两位小数
                                    .setDisabilityLevyPerDay(item.getSalaryPerDay()
                                            .multiply(new BigDecimal("0.015"))
                                            .setScale(2, RoundingMode.HALF_UP));

                    // 人员固定薪资单价=工资+社保+公积金+残保金
                    return costConfigLevelPrice.setLevelPrice(item.getSalaryPerDay()
                            .add(item.getSocialSecurityPerDay())
                            .add(item.getHousingFundPerDay())
                            .add(costConfigLevelPrice.getDisabilityLevyPerDay()));
                }).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(configLevelPriceList)) {
            this.saveBatch(configLevelPriceList);
        }
    }

    /**
     * 根据版本id获取差旅住宿标准配置
     *
     * @param versionId   版本 ID
     * @param pageRequest 页面请求
     * @return {@link Page }<{@link CostConfigLevelPriceVO }>
     */
    @Override
    public Page<CostConfigLevelPriceVO> getLevelPriceListByVersionIdPage(Long versionId, PageRequest pageRequest) {
        Page<CostConfigLevelPrice> page = Page.of(pageRequest.getPageNumber(), pageRequest.getPageSize());
        Page<CostConfigLevelPriceVO> configLevelPriceVoPage = baseMapper.getLevelPriceListByVersionIdPage(versionId, page);
        List<CostConfigLevelPriceVO> configLevelPriceVoList = configLevelPriceVoPage.getRecords();
        if (CollUtil.isEmpty(configLevelPriceVoPage.getRecords())) {
            return Page.of(pageRequest.getPageNumber(), pageRequest.getPageSize());
        }
        // 处理人员类型
        handlePersonnelTypeToMap(configLevelPriceVoList);
        return configLevelPriceVoPage.setRecords(configLevelPriceVoList);
    }


    /**
     * 处理要映射人员类型
     *
     * @param configLevelPriceVoList 配置级别 价格 VO 列表
     * @return {@link List }<{@link CostConfigLevelPriceVO }>
     */
    private List<CostConfigLevelPriceVO> handlePersonnelTypeToMap(List<CostConfigLevelPriceVO> configLevelPriceVoList) {
        Map<Long, String> jobActivityNameMap = IEhrService.getJobActivityNameMap();
        Map<Long, String> positionGradeNameMap = IEhrService.getPositionGradeNameMap();
        return configLevelPriceVoList.stream()
                .peek(item -> {
                    item.setPersonnelType(jobActivityNameMap.get(item.getJobActivityId()));
                    item.setPersonnelLevelStr(positionGradeNameMap.get(item.getPersonnelLevel()));
                })
                .collect(Collectors.toList());
    }
    @Override
    public Map<Long, CostSalaryDTO> batchCalculateLaborCostByConfigId(Collection<CalculateLaborCostDTO> dtoList) {
        if (CollUtil.isEmpty(dtoList)) {
            return Collections.emptyMap();
        }
        Set<Long> configIds = CollStreamUtil.toSet(dtoList, CalculateLaborCostDTO::getConfigId);
        List<CostConfigLevelPrice> configLevelPrices = listByIds(configIds);
        Map<Long, CostConfigLevelPriceVO> levelPriceVoMap = configLevelPrices.stream()
                .map(item -> BeanUtil.copyProperties(item, CostConfigLevelPriceVO.class))
                .collect(Collectors.toMap(CostConfigLevelPriceVO::getId, e -> e));
        Map<Long, CostSalaryDTO> resultMap = new HashMap<>(dtoList.size());
        dtoList.forEach(dto -> {
            dto.init();
            Long configId = dto.getConfigId();
            CostConfigLevelPriceVO priceVO = levelPriceVoMap.get(configId);
            if (priceVO == null) {
                throw new ServiceException("人员级别单价配置不存在");
            }
            CostConfigLevelPriceVO.initVo(priceVO);
            dto.setCostConfigLevelPrice(priceVO);
            resultMap.put(dto.getId(), calculate(dto));
        });
        return resultMap;
    }

    /**
     * 批量计算人工成本
     *
     * @param dtoList DTO 列表
     * @return {@link Map }<{@link Long }, {@link BigDecimal }>
     */
    @Override
    public Map<Long, CostSalaryDTO> batchCalculateLaborCost(Collection<CalculateLaborCostDTO> dtoList) {
        if (CollUtil.isEmpty(dtoList)) {
            return Collections.emptyMap();
        }
        Set<Long> userIds = CollStreamUtil.toSet(dtoList, CalculateLaborCostDTO::getUserId);
        // 避免查询所有人员导致超时
        userIds.add(0L);
        // 获取负责人职级信息
        List<HrmStaffRosterVo> staffVoList = remoteEhrService
                .getStaffRosterPageVoList(Collections.singletonMap("userIds", userIds), SecurityConstants.FROM_IN)
                .getData();
        Map<Long, HrmStaffRosterVo> staffVoMap = CollStreamUtil.toMap(staffVoList, HrmStaffRosterVo::getUserId, e -> e);
        // 获取人员级别单价配置
        Map<String, CostConfigLevelPriceVO> levelPriceVoMap = getCostConfigLevelPriceList().stream()
                .collect(Collectors.toMap(e ->
                                buildKey(e.getJobActivityId(), e.getPersonnelLevel()),
                        e -> e, (a, b) -> a));

        Map<Long, CostSalaryDTO> resultMap = new ConcurrentHashMap<>(dtoList.size());
        dtoList.parallelStream().forEach(dto -> {
            dto.init();
            HrmStaffRosterVo staffRosterVo = staffVoMap.get(dto.getUserId());
            if (staffRosterVo == null) {
                throw new ServiceException("未获取到人员的职务或职级信息~");
            }
            String key = buildKey(staffRosterVo.getJobActivityId(), staffRosterVo.getGradeId());

            CostConfigLevelPriceVO priceVO = levelPriceVoMap.get(key);
            if (priceVO == null) {
                throw new ServiceException(String.format("当前人员「%s」的职务或职级在人员级别单价配置表中无对应单价配置，请联系平台运营人员~", staffRosterVo.getAliasName()));
            }
            CostConfigLevelPriceVO.initVo(priceVO);
            dto.setCostConfigLevelPrice(priceVO);
            resultMap.put(dto.getId(), calculate(dto));
        });
        return resultMap;
    }

    @Override
    public Map<Long, BigDecimal> getHourlyWageMap(Collection<Long> userIds) {
        // 获取人员级别单价配置
        Map<String, CostConfigLevelPriceVO> levelPriceVoMap = getCostConfigLevelPriceList().stream()
                .collect(Collectors.toMap(e ->
                                buildKey(e.getJobActivityId(), e.getPersonnelLevel()),
                        e -> e, (a, b) -> a));
        // 获取负责人职级信息
        List<HrmStaffRosterVo> staffVoList = remoteEhrService
                .getStaffRosterPageVoList(Collections.singletonMap("userIds", userIds), SecurityConstants.FROM_IN)
                .getData();
        Map<Long, HrmStaffRosterVo> staffVoMap = CollStreamUtil.toMap(staffVoList, HrmStaffRosterVo::getUserId, e -> e);

        Map<Long, BigDecimal> hourlyWageMap = new HashMap<>(userIds.size());
        for (Long userId : userIds) {
            HrmStaffRosterVo staffRosterVo = staffVoMap.get(userId);
            if (staffRosterVo == null) {
                hourlyWageMap.put(userId, BigDecimal.ZERO);
                continue;
            }
            String key = buildKey(staffRosterVo.getJobActivityId(), staffRosterVo.getGradeId());

            CostConfigLevelPriceVO priceVO = levelPriceVoMap.get(key);
            if (priceVO == null) {
                hourlyWageMap.put(userId, BigDecimal.ZERO);
                continue;
            }
            // 工资 （元/时）
            BigDecimal salaryHourlyWage = calculateHourlyWages(priceVO.getSalaryPerDay(),6);
            // 社保 （元/时）
            BigDecimal socialSecurityHourlyWage = calculateHourlyWages(priceVO.getSocialSecurityPerDay(),6);
            // 公积金 （元/时）
            BigDecimal housingFundHourlyWage = calculateHourlyWages(priceVO.getHousingFundPerDay(),6);
            // 残保金 （元/时）
            BigDecimal disabilityFeeHourlyWage = calculateHourlyWages(priceVO.getDisabilityLevyPerDay(),6);
            // 计算出每天的时薪
            BigDecimal hourlyWage = salaryHourlyWage.add(socialSecurityHourlyWage).add(housingFundHourlyWage).add(disabilityFeeHourlyWage);
            hourlyWageMap.put(userId, hourlyWage);
        }
        return hourlyWageMap;
    }

    @Override
    public CostSalaryDTO calculateLaborCost(CalculateLaborCostDTO dto) {
        return batchCalculateLaborCost(Collections.singletonList(dto)).getOrDefault(dto.getId(), CostSalaryDTO.empty());
    }

    public static String buildKey(final Long jobActivityId, final Long personnelLevel) {
        return jobActivityId + StrPool.DASHED + personnelLevel;
    }

    /**
     * 计算人工成本
     *
     * @param dto DTO
     * @return {@link BigDecimal }
     */
    private static CostSalaryDTO calculate(CalculateLaborCostDTO dto) {
        // 获取负责人薪资信息
        CostConfigLevelPriceVO salaryData = dto.getCostConfigLevelPrice();
        // 正常工时 时
        BigDecimal normalHours = dto.getNormalHours();
        // 工作日加班工时 时
        BigDecimal workOvertimeHours = dto.getWorkOvertimeHours();
        // 休息日加班工时 时
        BigDecimal restOvertimeHours = dto.getRestOvertimeHours();
        // 节假日加班工时 时
        BigDecimal holidayOvertimeHours = dto.getHolidayOvertimeHours();
        // 工资（元/天）
        BigDecimal salaryPerDay = salaryData.getSalaryPerDay();
        // 社保（元/天）
        BigDecimal socialSecurityPerDay = salaryData.getSocialSecurityPerDay();
        // 公积金（元/天）
        BigDecimal housingFundPerDay = salaryData.getHousingFundPerDay();
        // 残保金（元/天）
        BigDecimal disabilityLevyPerDay = salaryData.getDisabilityLevyPerDay();
        // 固定薪资单价
        BigDecimal levelPrice = salaryData.getLevelPrice();
        // 基本工资日薪（元/天）
        BigDecimal monthlyBaseSalary = salaryData.getBaseSalaryPrice();

        // 平时工作工时 = 正常工时 + 工作日加班工时
        BigDecimal workHours = normalHours.add(workOvertimeHours);

        // 工资 （元/时）
        BigDecimal salaryHourlyWage = calculateHourlyWages(salaryPerDay);

        // 工资金额
        BigDecimal salaryPay = workHours.multiply(salaryHourlyWage).setScale(5, RoundingMode.HALF_UP);

        // 社保 （元/时）
        BigDecimal socialSecurityHourlyWage = calculateHourlyWages(socialSecurityPerDay);

        //  社保金额
        BigDecimal socialSecurityPay = workHours.multiply(socialSecurityHourlyWage).setScale(5, RoundingMode.HALF_UP);

        // 公积金 （元/时）
        BigDecimal housingFundHourlyWage = calculateHourlyWages(housingFundPerDay);

        // 公积金金额
        BigDecimal housingFundPay = workHours.multiply(housingFundHourlyWage).setScale(5, RoundingMode.HALF_UP);

        // 残保金 （元/时）
        BigDecimal disabilityFeeHourlyWage = calculateHourlyWages(disabilityLevyPerDay);

        // 残保金金额
        BigDecimal disabilityFeePay = workHours.multiply(disabilityFeeHourlyWage).setScale(5, RoundingMode.HALF_UP);

        // 正常成本和工作日成本
        BigDecimal normalAndWorkdayCost = salaryPay.add(socialSecurityPay).add(housingFundPay).add(disabilityFeePay);

        // 加班时薪以8小时计算
        BigDecimal eightHours = new BigDecimal(8);

        // 基本工资时薪
        BigDecimal baseHourlyWage = monthlyBaseSalary.divide(eightHours, 6, RoundingMode.HALF_UP);

        // 固定工资时薪
        BigDecimal hourlyWage = levelPrice.divide(eightHours, 6, RoundingMode.HALF_UP);

        // 计算休息日加班费
        BigDecimal weekendOvertimePay = getOvertimePay(restOvertimeHours, baseHourlyWage, 2, hourlyWage);

        // 计算节假日加班费
        BigDecimal holidayOvertimePay = getOvertimePay(holidayOvertimeHours, baseHourlyWage, 3, hourlyWage);

        // 返回总成本
        BigDecimal laborCost = normalAndWorkdayCost.add(weekendOvertimePay).add(holidayOvertimePay).setScale(2, RoundingMode.HALF_UP);

        return new CostSalaryDTO()
                .setRelateId(dto.getRelateId())
                .setRelateType(dto.getRelateTypeEnum().getValue())
                .setConfigLevelPriceId(salaryData.getId())
                .setLaborCost(laborCost)
                .setSalary(salaryPay)
                .setSocialSecurity(socialSecurityPay)
                .setHousingFund(housingFundPay)
                .setDisabilityFee(disabilityFeePay)
                .setWeekendOvertimePay(weekendOvertimePay)
                .setHolidayOvertimePay(holidayOvertimePay);
    }

    /**
     * 获得加班费
     *
     * @param overtimeHours  加班时间
     * @param baseHourlyWage 加班的基本工资时薪
     * @param magnification  倍率
     * @param hourlyWage     加班的固定时薪
     * @return {@link BigDecimal }
     */
    @NotNull
    private static BigDecimal getOvertimePay(BigDecimal overtimeHours, BigDecimal baseHourlyWage, int magnification, BigDecimal hourlyWage) {
        BigDecimal overtimePay = BigDecimal.ZERO;
        if (overtimeHours != null) {
            // 基本工资 * 倍率
            BigDecimal overtimeHourlyWage = baseHourlyWage.multiply(new BigDecimal(magnification)).setScale(6, RoundingMode.HALF_UP);
            // 基本工资 * 倍率 与 固定工资时薪 两个值对比，取较大的值
            overtimeHourlyWage = overtimeHourlyWage.compareTo(hourlyWage) > 0 ? overtimeHourlyWage : hourlyWage;
            // 计算加班费
            overtimePay = overtimePay.add(overtimeHours.multiply(overtimeHourlyWage).setScale(6, RoundingMode.HALF_UP));
        }
        return overtimePay;
    }

    /**
     * 计算工作日时薪
     *
     * @param perDay 每天
     * @return {@link BigDecimal }
     */
    private static BigDecimal calculateHourlyWages(BigDecimal perDay) {
        return perDay.divide(DAILY_NORMAL_WORKING_HOURS, 6, RoundingMode.HALF_UP);
    }
    private static BigDecimal calculateHourlyWages(BigDecimal perDay,int scale) {
        return perDay.divide(DAILY_NORMAL_WORKING_HOURS, scale, RoundingMode.HALF_UP);
    }
}
