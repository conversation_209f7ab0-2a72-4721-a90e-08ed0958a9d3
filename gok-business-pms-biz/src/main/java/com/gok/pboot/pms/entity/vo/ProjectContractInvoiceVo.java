package com.gok.pboot.pms.entity.vo;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 发票记录Vo
 * 
 * <AUTHOR>
 * @date 2024-02-26 14:58:58
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class ProjectContractInvoiceVo {

	/**
	 * ID id
	 */
	private Long id;
	/**
	 * oa流程id
	 */
	private Long requestid;
	/**
	 * 发票代码
	 */
	private String fpdm;
	/**
	 * 发票号码
	 */
	private String fphm;
	/**
	 * 发票类型
	 */
	private Integer kplx;
    /**
     * 发票类型Txt
     */
    private String kplxTxt;
	/**
	 * 发票状态
	 */
	private Integer fpzt;
    /**
     * 发票状态Txt
     */
    private String fpztTxt;
	/**
	 * 发票日期
	 */
	private Date fprq;
	/**
	 * 发票金额（含税）
	 */
	private BigDecimal fpje;
	/**
	 * 税率
	 */
	private Integer kpsl;
	/**
	 * 发票附件
	 */
	private Long fpsmj;
	/**
	 * 流程相关人id
	 */
	private Long nodeoperator;

}
