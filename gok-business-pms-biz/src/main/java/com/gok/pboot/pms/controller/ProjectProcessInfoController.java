package com.gok.pboot.pms.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.common.base.PropertyFilters;
import com.gok.pboot.pms.entity.vo.ProjectProcessInfoFindPageVO;
import com.gok.pboot.pms.service.IProjectProcessInfoService;
import com.gok.pboot.pms.service.IProjectSystemProcessInfoService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @description 项目动态 前端控制器
 * @menu 项目动态
 * @since 2023-07-14
 **/
@Slf4j
@RestController
@RequestMapping("/projectProcessInfo")
@RequiredArgsConstructor
@Api(tags = "项目动态")
public class ProjectProcessInfoController {

    private final IProjectProcessInfoService processInfoService;
    private final IProjectSystemProcessInfoService systemProcessService;

    /**
     * 分页查询 项目流程动态(OA数据)
     *
     * @param pageRequest 分页请求实体
     * @param request     {@link HttpServletRequest}
     * @return {@link ApiResult}{@link Page}{@link ProjectProcessInfoFindPageVO}
     */
    @GetMapping("/findPageFlow")
    public ApiResult<Page<ProjectProcessInfoFindPageVO>> findPageFlow(PageRequest pageRequest,
                                                                      HttpServletRequest request) {
        return ApiResult.success(processInfoService.findPage(pageRequest, PropertyFilters.get(request)));
    }

    /**
     * 分页查询 项目过程动态(系统数据)
     *
     * @param pageRequest 分页请求实体
     * @param request     {@link HttpServletRequest}
     * @return {@link ApiResult}{@link Page}{@link ProjectProcessInfoFindPageVO}
     */
    @GetMapping("/findPageProcess")
    public ApiResult<Page<ProjectProcessInfoFindPageVO>> findPageProcess(PageRequest pageRequest,
                                                                         HttpServletRequest request) {
        return ApiResult.success(systemProcessService.findPage(pageRequest, PropertyFilters.get(request)));
    }

    /**
     * 获取OA跳转Token
     *
     * @return OA鉴权Token
     */
    @GetMapping("/token")
    public ApiResult<String> getRedirectOaToken() {
        return ApiResult.success(systemProcessService.getRedirectOaToken());
    }

}
