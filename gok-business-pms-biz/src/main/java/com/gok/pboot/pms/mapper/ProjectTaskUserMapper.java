package com.gok.pboot.pms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.entity.domain.ProjectTaskUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

@Mapper
public interface ProjectTaskUserMapper extends BaseMapper<ProjectTaskUser> {

    void save(ProjectTaskUser projectTaskUser);

    void delete(Long taskId);

    List<ProjectTaskUser> findByTaskId(Long taskId);

    List<Long> findAllUserIdByProjectIds(@Param("projectIds") Collection<Long> projectIds);
}
