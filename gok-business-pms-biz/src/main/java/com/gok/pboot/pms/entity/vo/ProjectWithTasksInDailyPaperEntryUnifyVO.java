package com.gok.pboot.pms.entity.vo;

import com.gok.pboot.pms.entity.domain.ProjectInfo;
import lombok.*;

import java.util.List;
import java.util.Optional;

/**
 * 项目及其下的可用任务
 *
 * <AUTHOR>
 * @date 2024/01/24
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@ToString
public class ProjectWithTasksInDailyPaperEntryUnifyVO {

    /**
     * ID
     */
    private Long id;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 是否内部项目
     */
    private Integer isInsideProject;

    /**
     * 销售人员名
     */
    private String salesmanUserName;

    /**
     * 项目经理人名
     */
    private String managerUserName;

    /**
     * 任务列表
     */
    private List<TaskInDailyPaperEntryUnifyVO> tasks;

    /**
     * 收藏项目实体ID
     */
    private Long collectId;

    /**
     * 收藏人id
     */
    private Long collectUserId;

    /**
     * 收藏时间
     */
    private String collectTime;

    /**
     * vo构造器
     *
     * @param projectInfo 项目信息
     * @return {@link ProjectWithTasksInDailyPaperEntryUnifyVO}
     */
    public static ProjectWithTasksInDailyPaperEntryUnifyVO of(ProjectInfo projectInfo) {
        ProjectWithTasksInDailyPaperEntryUnifyVO unifyVO = new ProjectWithTasksInDailyPaperEntryUnifyVO();
        unifyVO.id = projectInfo.getId();
        unifyVO.projectName = projectInfo.getItemName();
        Optional.ofNullable(projectInfo.getIsNotInternalProject())
                .ifPresent(i -> unifyVO.isInsideProject = Math.toIntExact(projectInfo.getIsNotInternalProject()));
        unifyVO.salesmanUserName = projectInfo.getProjectSalesperson();
        unifyVO.managerUserName = projectInfo.getManagerUserName();
        return unifyVO;
    }

}
