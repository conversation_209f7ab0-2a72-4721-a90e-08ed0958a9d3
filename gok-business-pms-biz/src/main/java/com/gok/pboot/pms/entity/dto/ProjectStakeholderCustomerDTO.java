package com.gok.pboot.pms.entity.dto;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 项目干系人-客户dto
 *
 * <AUTHOR>
 * @date 2023-07-11 17:05:26
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectStakeholderCustomerDTO {

    /**
     * ID id
     */
    private Long id;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 联系人（手填）
     */
    private String contact;

    /**
     * 所在部门名（手填）
     */
    @Length(max = 50, message = "所在部门长度不能超过50")
    private String department;

    /**
     * 岗位/职位（手填）
     */
    @Length(max = 50, message = "职务长度不能超过50")
    private String position;

    /**
     * 项目影响度
     * {@link com.gok.pboot.pms.enumeration.ProjectImpactDegreeEnum}
     */
    @Min(0)
    @Max(2)
    @NotNull(message = "项目影响度不能为空")
    private Integer impactDegree;

    /**
     * 职责
     */
    @Length(max = 200, message = "职责长度不能超过200")
    private String duty;

    /**
     * 联系方式
     */
    private String contactPhone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 备注
     */
    @Length(max = 100, message = "备注长度不能超过100")
    private String remark;

    /**
     * 是否满意度调查（0=否,1=是）
     */
    @NotNull(message = "满意度调查不能为空")
    private Integer satisfactionSurvey;
}
