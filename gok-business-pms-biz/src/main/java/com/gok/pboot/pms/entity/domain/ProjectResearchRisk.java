package com.gok.pboot.pms.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 研发风险表（Oa项目项目台账-明细5）
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-17 11:11:28
 */
@Data
@TableName("project_research_risk")
public class ProjectResearchRisk extends BaseEntity<Long> {

	/**
	 * 项目ID
	 */
	private Long projectId;
	/**
	 * 风险类型
	 */
	private Integer riskType;
	/**
	 * 风险描述
	 */
	private String riskDescription;
	/**
	 * 风险原因
	 */
	private String cause;
	/**
	 * 发生概率
	 */
	private BigDecimal probability;
	/**
	 * 影响程度
	 */
	private Integer impactLevel;
	/**
	 * 应对措施
	 */
	private String mitigationStrategy;

}
