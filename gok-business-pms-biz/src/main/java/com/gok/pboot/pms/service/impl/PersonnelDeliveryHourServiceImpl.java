package com.gok.pboot.pms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.bcp.message.dto.BcpMessageBatchDTO;
import com.gok.bcp.message.dto.BcpMessageTargetBatchDTO;
import com.gok.bcp.message.dto.BcpMessageTargetDTO;
import com.gok.bcp.message.entity.enums.ChannelEnum;
import com.gok.bcp.message.entity.enums.MsgTypeEnum;
import com.gok.bcp.message.entity.enums.SourceEnum;
import com.gok.bcp.message.entity.enums.TargetTypeEnum;
import com.gok.bcp.message.entity.model.BcpMessageContentModel;
import com.gok.bcp.message.entity.model.WeComBatchModel;
import com.gok.bcp.message.entity.model.WeComModel;
import com.gok.bcp.message.feign.RemoteSendMsgService;
import com.gok.bcp.upms.dto.DeptCacheDto;
import com.gok.bcp.upms.feign.RemoteDeptService;
import com.gok.components.common.user.PigxUser;
import com.gok.module.excel.api.domain.vo.ErrorMessageVo;
import com.gok.pboot.common.core.constant.SecurityConstants;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.Util.BcpLoggerUtils;
import com.gok.pboot.pms.Util.SysDeptUtils;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.common.constant.FunctionConstants;
import com.gok.pboot.pms.cost.entity.domain.CostPersonnelInformation;
import com.gok.pboot.pms.cost.mapper.CostPersonnelInformationMapper;
import com.gok.pboot.pms.entity.PersonnelDeliveryHour;
import com.gok.pboot.pms.entity.SysDept;
import com.gok.pboot.pms.entity.domain.ProjectInfo;
import com.gok.pboot.pms.entity.dto.AttendanceHourImportDTO;
import com.gok.pboot.pms.entity.dto.PersonnelDeliveryHourImportDTO;
import com.gok.pboot.pms.entity.dto.PersonnelReuseUpdateDTO;
import com.gok.pboot.pms.entity.vo.PersonnelDeliveryHourPageVO;
import com.gok.pboot.pms.enumeration.*;
import com.gok.pboot.pms.mapper.FilingMapper;
import com.gok.pboot.pms.mapper.PersonnelDeliveryHourMapper;
import com.gok.pboot.pms.mapper.ProjectInfoMapper;
import com.gok.pboot.pms.service.IPersonnelDeliveryHourService;
import com.gok.pboot.pms.service.IProjectInfoService;
import com.google.common.collect.ImmutableMap;
import kotlin.text.Charsets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.BindingResult;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2023/2/9
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PersonnelDeliveryHourServiceImpl implements IPersonnelDeliveryHourService {

    private final ProjectInfoMapper projectInfoMapper;
    private final PersonnelDeliveryHourMapper personnelDeliveryHourMapper;
    private final FilingMapper filingMapper;
    private final IProjectInfoService projectInfoService;
    private final RemoteDeptService remoteDeptService;
    private final BcpLoggerUtils bcpLoggerUtils;
    private final RemoteSendMsgService remoteSendMsgService;
    private final CostPersonnelInformationMapper costPersonnelInformationMapper;

    /**
     * 应用ID
     */
    @Value("${pushMessage.clientId}")
    private Long clientId;
    @Value("${pushMessage.redirectPrefixUrl}")
    private String redirectPrefix;
    @Value("${pushMessage.personnelDeliveryHourUrlRedirect}")
    private String personnelDeliveryHourUrlRedirect;

    /**
     * 数据格式校验 - 检查是否为有效数字(正数且最多2位小数)
     *
     * @param data 数据
     * @return {@link Boolean}
     */
    public Boolean checkDataFormat(String data) {
        if (data == null) {
            return false;
        }
        return data.matches("^(0|[1-9]\\d*)(\\.\\d{1,2})?$");
    }

    /**
     * 考勤工时导入
     *
     * @param projectId     项目ID
     * @param excelList     数据列表
     * @param bindingResult 参数校验
     * @param file          文件
     * @return {@link ApiResult}
     */
    @Override
    public ApiResult attendanceHourImport(Long projectId, List<AttendanceHourImportDTO> excelList, BindingResult bindingResult, MultipartFile file) {
        // 通用校验获取失败的数据
        List<ErrorMessageVo> errorMessageList = (List<ErrorMessageVo>) bindingResult.getTarget();
        //模版
        String head = excelList.get(0).getProjectCode();
        String yearMonth = head.substring(0, 7).replace('年', '-') + "-01";
        // 数据补充
        ProjectInfo projectInfo = projectInfoMapper.selectById(projectId);
        for (int i = 2; i < excelList.size(); i++) {
            AttendanceHourImportDTO person = excelList.get(i);
            person.setProjectCode(projectInfo.getItemNo());
            person.setProjectCode(projectInfo.getItemNo());
            person.setProjectName(projectInfo.getItemName());
            person.setProjectStatus(ProjectStatusEnum.getNameByStrVal(projectInfo.getProjectStatus()));
            person.setRevenueDeptName(projectInfo.getBusinessDepartment());
        }
        List<PersonnelDeliveryHourImportDTO> personnelDeliveryHourImportList = BeanUtil.copyToList(excelList, PersonnelDeliveryHourImportDTO.class);
        // 复用
        return this.extractCode(yearMonth, personnelDeliveryHourImportList, errorMessageList, file);
    }

    /**
     * 导入交付人员工时
     *
     * @param excelVOList   列表
     * @param bindingResult 错误信息列表
     * @return R
     */
    @Override
    public ApiResult importUser(List<PersonnelDeliveryHourImportDTO> excelVOList, BindingResult bindingResult, MultipartFile file) {
        // 通用校验获取失败的数据
        List<ErrorMessageVo> errorMessageList = (List<ErrorMessageVo>) bindingResult.getTarget();
        //模版日
        String head = excelVOList.get(0).getProjectCode();
        String yearMonth = head.substring(0, 7).replace('年', '-') + "-01";
        return this.extractCode(yearMonth, excelVOList, errorMessageList, file);
    }

    /**
     * 校验导入数据
     *
     * @param excel 导入数据
     * @param projectMap 项目信息Map
     * @param userInfoMap 用户信息Map
     * @return 错误信息集合
     */
    private Set<String> validateImportData(PersonnelDeliveryHourImportDTO excel, 
                                         Map<String, ProjectInfo> projectMap,
                                         Map<String, CostPersonnelInformation> userInfoMap) {
        Set<String> errorMsg = new HashSet<>();
        
        // 校验项目编号是否存在
        ProjectInfo project = projectMap.get(excel.getProjectCode());
        if (null == project) {
            errorMsg.add(String.format("%s 项目编码未找到匹配项目", excel.getProjectCode()));
        } else {
            // 项目ID会在后续insertExcelUser中使用
            excel.setProjectName(project.getItemName());
        }
        
        // 校验工号是否存在
        CostPersonnelInformation user = userInfoMap.get(excel.getWorkCode());
        if (user == null) {
            errorMsg.add(String.format("工号 %s 在交付人员信息中不存在", excel.getWorkCode()));
        } else if (StringUtils.isNotEmpty(excel.getUserRealName()) && !user.getName().equals(excel.getUserRealName())) {
            errorMsg.add(String.format("工号 %s 对应的姓名应为 %s，而非 %s", 
                excel.getWorkCode(), user.getName(), excel.getUserRealName()));
        }
        
        return errorMsg;
    }

    /**
     * 代码抽取
     *
     * @param yearMonth        年月
     * @param excelVOList      Excel数据
     * @param errorMessageList 错误信息
     * @return {@link ApiResult}
     */
    public ApiResult extractCode(String yearMonth, List<PersonnelDeliveryHourImportDTO> excelVOList, List<ErrorMessageVo> errorMessageList, MultipartFile file) {
        int filedByDate = ObjectUtil.isNotEmpty(filingMapper.isFiledByDate(yearMonth)) ? filingMapper.isFiledByDate(yearMonth) : NumberUtils.INTEGER_ZERO;
        if (YesOrNoEnum.YES.getValue().equals(filedByDate)) {
            return ApiResult.failure("已归档、拒绝导入！");
        }

        // 个性化校验逻辑
        List<ProjectInfo> projectList = projectInfoMapper.selectList(null);
        Map<String, ProjectInfo> projectMap = projectList.stream()
                .collect(Collectors.toMap(ProjectInfo::getItemNo, x -> x, (x1, x2) -> x1));
        
        // 获取用户工号列表
        List<String> workCodes = excelVOList.stream().skip(2)
                .filter(dto -> StrUtil.isNotBlank(dto.getWorkCode()))
                .map(PersonnelDeliveryHourImportDTO::getWorkCode)
                .distinct()
                .collect(Collectors.toList());
        
        // 根据工号获取用户信息 
        Map<String, CostPersonnelInformation> workCodeToUserMap = new HashMap<>();
        if (CollUtil.isNotEmpty(workCodes)) {
            workCodeToUserMap = costPersonnelInformationMapper.findByWorkCodes(workCodes)
                    .stream().collect(Collectors.toMap(CostPersonnelInformation::getWorkCode, userInfo -> userInfo, (a, b) -> a));
        }
                
        Map<String, PersonnelDeliveryHourImportDTO> personnelDeliveryHourImportDTOMap = new HashMap<>(excelVOList.size());
        // 执行数据插入操作 组装 从数据行读取
        for (int i = 2; i < excelVOList.size(); i++) {
            PersonnelDeliveryHourImportDTO excel = excelVOList.get(i);
            String key = excel.getProjectCode() + excel.getWorkCode();

            // 校验数据
            Set<String> errorMsg = validateImportData(excel, projectMap, workCodeToUserMap);

            //校验yearMonth格式是否为yyyy-MM-dd
            if (!com.gok.pboot.pms.Util.DateUtil.isValidDate(yearMonth)) {
                Set<String> errorDateMsg = new HashSet<>();
                errorDateMsg.add("日期格式不正确");
                errorMessageList.add(new ErrorMessageVo((long) (2), errorDateMsg));
            }

            // 数据合法情况
            if (CollUtil.isEmpty(errorMsg)) {
                // 添加数据到Map
                if (personnelDeliveryHourImportDTOMap.containsKey(key)) {
                    personnelDeliveryHourImportDTOMap.replace(key, excel);
                }
                personnelDeliveryHourImportDTOMap.putIfAbsent(key, excel);
            } else {
                // 数据不合法情况
                errorMessageList.add(new ErrorMessageVo((long) (i + 2), errorMsg));
            }
        }
        
        if (CollUtil.isNotEmpty(errorMessageList)) {
            return ApiResult.builder().apiResultEnum(ApiResultEnum.IMPORT_VALID_FAIL).result(errorMessageList).build();
        }
        
        //获取该月份已导入的人才复用信息列表
        List<PersonnelDeliveryHour> personnelDeliveryHourList = personnelDeliveryHourMapper.selectListByReuseDate(yearMonth);
        Map<String, PersonnelDeliveryHour> personnelDeliveryHourMap = personnelDeliveryHourList.stream().collect(Collectors.toMap(x -> {
            return x.getReuseDate() + x.getProjectCode() + x.getWorkCode();
        }, x -> x));
        
        List<PersonnelDeliveryHourImportDTO> datas = new ArrayList<>(personnelDeliveryHourImportDTOMap.values());
        insertExcelUser(datas, yearMonth, personnelDeliveryHourMap, projectMap, workCodeToUserMap);
        
        //导入【文件名称】，数据【XX条】
        bcpLoggerUtils.log(FunctionConstants.DELIVERY_PERSONNEL_WORKING_HOURS_IMPORT, LogContentEnum.IMPORT,
                file.getOriginalFilename(), datas.size());
        
        return ApiResult.success("操作成功");
    }

    /**
     * 交付人员工时分页查询
     *
     * @param pageRequest 分页
     * @param projectName 项目名称
     * @param time        时间范围
     * @param status      审核状态
     * @param name        人员姓名
     * @return {@link ApiResult<PersonnelDeliveryHourPageVO>}
     */
    @Override
    public Page<PersonnelDeliveryHourPageVO> pagePersonnelDeliveryHour(Long projectId, PageRequest pageRequest, String projectName, LocalDate time, Integer status, String name) {
        Map<String, Object> filter = new HashMap<>(6);
        filter.put("projectName", projectName);
        filter.put("projectId", projectId);
        filter.put("time", time);
        filter.put("status", status);
        filter.put("name", name);
        Long id = SecurityUtils.getUser().getId();
        filter.put("userId", id);

        Page<PersonnelDeliveryHourPageVO> personnelDeliveryHourPageVOPage = personnelDeliveryHourMapper.findList(
                new Page<>(pageRequest.getPageNumber(), pageRequest.getPageSize()), filter
        );
        List<PersonnelDeliveryHourPageVO> personnelDeliveryHourPageVOList = personnelDeliveryHourPageVOPage.getRecords();

        // 填充收入归属部门名称
        List<DeptCacheDto> deptList = remoteDeptService.getAllDeptList(false);
        Map<Long, SysDept> deptIdMap;
        if (com.gok.pboot.pms.Util.CollectionUtils.isEmpty(deptList)) {
            deptIdMap = ImmutableMap.of();
        } else {
            deptIdMap = SysDeptUtils.mapBCPDeptCacheDtoToSysDept(deptList).stream().collect(Collectors.toMap(SysDept::getDeptId, dept -> dept));
        }
        personnelDeliveryHourPageVOList.forEach(personnelDeliveryHourPageVO -> {
            if (personnelDeliveryHourPageVO.getRevenueDeptId() != null) {
                personnelDeliveryHourPageVO.setRevenueDeptName(SysDeptUtils.collectFullName(deptIdMap, personnelDeliveryHourPageVO.getRevenueDeptId()));
            }
        });
        return personnelDeliveryHourPageVOPage;
    }

    /**
     * 编辑
     *
     * @param personnelReuseUpdateDTO
     * @return {@link ApiResult}
     */
    @Override
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public ApiResult<String> update(PersonnelReuseUpdateDTO personnelReuseUpdateDTO) {
        //导入人信息
        PigxUser user = SecurityUtils.getUser();
        personnelReuseUpdateDTO.setModifier(user.getName());
        personnelReuseUpdateDTO.setModifierId(user.getId());
        personnelReuseUpdateDTO.setMtime(new Timestamp(System.currentTimeMillis()));
        // 不通过的重新提交变成待审核
        if (ApprovalStatusEnum.BTG.getValue().equals(personnelReuseUpdateDTO.getApprovalStatus())) {
            personnelReuseUpdateDTO.setApprovalStatus(ApprovalStatusEnum.DSH.getValue());
        }
        personnelDeliveryHourMapper.updateById(personnelReuseUpdateDTO);
        return ApiResult.success("操作成功");
    }

    /**
     * 批量逻辑删除
     *
     * @param list 要删除的id集合[]
     * @return {@link ApiResult}
     */
    @Override
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public ApiResult<String> batchDel(List<Long> list) {
        personnelDeliveryHourMapper.batchDel(list);
        return ApiResult.success("操作成功");
    }

    /**
     * 考勤工时导入消息提醒
     *
     * @return {@link String}
     */
    @Override
    public Boolean messageAlert() {
        // 获取【交付形式】=人力外包、【项目状态】=在建项目ID
        List<Long> projectIds = projectInfoService.list(new LambdaQueryWrapper<ProjectInfo>()
                        .eq(ProjectInfo::getDeliverType, DeliverTypeEnum.HUMAN_OUTSOURCING.getValue())
                        .eq(ProjectInfo::getProjectStatus, ProjectStatusEnum.ZJ.getStrValue()))
                .stream().map(ProjectInfo::getId).distinct().collect(Collectors.toList());
        // 获取上月日期
        LocalDate date = LocalDate.now().minusMonths(NumberUtils.INTEGER_ONE).withDayOfMonth(NumberUtils.INTEGER_ONE);
        // 获取上月交付人员工时已导入项目ID
        List<Long> importProjectIds = personnelDeliveryHourMapper.getAlreadyImportProjectIds(date);
        // 获取消息提醒的项目ID
        projectIds.removeAll(importProjectIds);
        if (CollUtil.isEmpty(projectIds)) {
            return Boolean.FALSE;
        }
        // 获取消息提醒项目信息
        List<ProjectInfo> projectInfoList = projectInfoService.listByIds(projectIds);
        // 企微
        List<WeComModel> weComModelList = new ArrayList<>(projectInfoList.size());
        BcpMessageBatchDTO bcpMessageBatchDTO = new BcpMessageBatchDTO();
        Map<String, BcpMessageContentModel> bcpMessageContentModelMap = new HashMap<>();
        List<BcpMessageTargetBatchDTO> bcpMessageTargetList = new ArrayList<>();
        for (ProjectInfo projectInfo : projectInfoList) {
            // 封装企微消息对象
            WeComModel weComModel = new WeComModel();
            weComModel.setSource(SourceEnum.PROJECT.getValue());
            weComModel.setType(MsgTypeEnum.TEXT_MSG.getValue());
            weComModel.setTitle("交付人员工时导入提醒");
            weComModel.setSenderId(clientId);
            weComModel.setSender(SourceEnum.PROJECT.getName());
            weComModel.setTargetType(TargetTypeEnum.USERS.getValue());

            String redirectUrl = StrUtil.format(personnelDeliveryHourUrlRedirect, projectInfo.getId());
            weComModel.setRedirectUrl(redirectPrefix + Base64.encode(redirectUrl, Charsets.UTF_8));

            BcpMessageTargetDTO target = BcpMessageTargetDTO.builder()
                    .targetId(String.valueOf(projectInfo.getManagerUserId()))
                    .targetName(projectInfo.getManagerUserName())
                    .build();
            weComModel.setTargetList(Collections.singletonList(target));
            String content = StrUtil.format("【{}】交付人员工时导入已逾期，请及时导入交付人员工时~", projectInfo.getItemName());
            weComModel.setContent(content + "\n<a href=\"" + weComModel.getRedirectUrl() + "\">" + "查看详情</a>");
            weComModelList.add(weComModel);

            // 封装门户消息对象
            String contentId = UUID.randomUUID().toString();
            BcpMessageContentModel bcpMessageContentModel = new BcpMessageContentModel();
            bcpMessageContentModel.setTitle(weComModel.getTitle());
            bcpMessageContentModel.setContent(content);
            bcpMessageContentModel.setRedirectUrl(weComModel.getRedirectUrl());
            bcpMessageContentModel.setType(MsgTypeEnum.TEXT_MSG.getValue());
            bcpMessageContentModelMap.put(contentId, bcpMessageContentModel);

            BcpMessageTargetBatchDTO bcpMessageTargetBatchDTO = new BcpMessageTargetBatchDTO();
            bcpMessageTargetBatchDTO.setContentId(contentId);
            bcpMessageTargetBatchDTO.setTargetId(target.getTargetId());
            bcpMessageTargetBatchDTO.setTargetName(target.getTargetName());
            bcpMessageTargetList.add(bcpMessageTargetBatchDTO);
        }
        // 批量发送企微消息
        WeComBatchModel weComBatchModel = new WeComBatchModel();
        weComBatchModel.setData(weComModelList);
        remoteSendMsgService.sendWeComMsgBatch(SecurityConstants.FROM_IN, weComBatchModel);
        // 发送门户消息
        bcpMessageBatchDTO.setSource(SourceEnum.PROJECT.getValue());
        bcpMessageBatchDTO.setChannel(ChannelEnum.MAIL.getValue());
        bcpMessageBatchDTO.setSenderId(clientId);
        bcpMessageBatchDTO.setSender(SourceEnum.PROJECT.getName());
        bcpMessageBatchDTO.setTargetType(TargetTypeEnum.USERS.getValue());
        bcpMessageBatchDTO.setSendTime(DateUtil.formatTime(new Date()));
        bcpMessageBatchDTO.setContentMap(bcpMessageContentModelMap);
        bcpMessageBatchDTO.setTargetList(bcpMessageTargetList);
        remoteSendMsgService.sendMsgBatch(SecurityConstants.FROM_IN, bcpMessageBatchDTO);
        return Boolean.TRUE;
    }

    /**
     * 数据合法后操作
     *
     * @param datas 数据列表
     * @param yearMonths 年月日期
     * @param personnelReuseMap 已存在的人员信息Map
     * @param projectMap 项目信息Map
     * @param userInfoMap 用户信息Map
     */
    private void insertExcelUser(List<PersonnelDeliveryHourImportDTO> datas, String yearMonths, 
                                Map<String, PersonnelDeliveryHour> personnelReuseMap,
                                Map<String, ProjectInfo> projectMap,
                                Map<String, CostPersonnelInformation> userInfoMap) {

        ArrayList<PersonnelDeliveryHour> update = new ArrayList<>();
        ArrayList<PersonnelDeliveryHour> insert = new ArrayList<>();

        // 导入人信息
        PigxUser user = SecurityUtils.getUser();
        Long userId = user.getId();
        String userName = user.getName();

        // 日期格式转换
        LocalDate beginDateTime = LocalDate.parse(yearMonths, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        
        for (int i = 0; i < datas.size(); i++) {
            PersonnelDeliveryHourImportDTO data = datas.get(i);
            
            // 校验联合key是否重复
            String bigKey = beginDateTime + data.getProjectCode() + data.getWorkCode();
            PersonnelDeliveryHour personnelDeliveryHour = personnelReuseMap.get(bigKey);
            String remark = data.getRemark();
            
            // 获取项目和用户信息
            ProjectInfo projectInfo = projectMap.get(data.getProjectCode());
            CostPersonnelInformation userInfo = userInfoMap.get(data.getWorkCode());

            // 根据重复项数据判断该条数据的审核状态，"不通过"、"审核通过"数据不导入，"待审核"数据取覆盖操作
            if (personnelDeliveryHour != null && ApprovalStatusEnum.DSH.getValue().equals(personnelDeliveryHour.getApprovalStatus())) {
                // 设置项目信息
                if (projectInfo != null) {
                    personnelDeliveryHour.setProjectId(projectInfo.getId());
                    personnelDeliveryHour.setProjectCode(projectInfo.getItemNo());
                    personnelDeliveryHour.setProjectName(projectInfo.getItemName());
                    personnelDeliveryHour.setProjectStatus(Integer.valueOf(projectInfo.getProjectStatus()));
                    Long revenueDeptId = Optional.ofNullable(projectInfo.getSecondLevelDepartmentId()).orElse(projectInfo.getFirstLevelDepartmentId());
                    personnelDeliveryHour.setRevenueDeptId(revenueDeptId);
                }

                // 设置用户信息
                if (userInfo != null) {
                    personnelDeliveryHour.setUserRealName(userInfo.getName());
                    personnelDeliveryHour.setWorkCode(userInfo.getWorkCode());
                    personnelDeliveryHour.setDeptId(userInfo.getDeptId());
                    personnelDeliveryHour.setDeptName(userInfo.getDeptName());
                }

                // 更新
                personnelDeliveryHour.setRemark(remark == null ? "" : remark);
                personnelDeliveryHour.setCwDueAttendance(StrUtil.isEmpty(data.getCwDueAttendance()) ? BigDecimal.ZERO :
                        new BigDecimal(data.getCwDueAttendance()).setScale(2, RoundingMode.HALF_UP)
                );
                personnelDeliveryHour.setAttendanceDays(StrUtil.isEmpty(data.getAttendanceDays()) ? BigDecimal.ZERO :
                        new BigDecimal(data.getAttendanceDays()).setScale(2, RoundingMode.HALF_UP)
                );
                personnelDeliveryHour.setReuseDate(beginDateTime);
                personnelDeliveryHour.setExecutorUserId(userId);
                personnelDeliveryHour.setExecutorUserRealName(userName);
                
                // 工时导入
                personnelDeliveryHour.setRestWorkDays(StrUtil.isNotEmpty(data.getRestWorkDays()) ? new BigDecimal(data.getRestWorkDays()) : BigDecimal.ZERO);
                personnelDeliveryHour.setNormalWorkDays(StrUtil.isNotEmpty(data.getNormalWorkDays()) ? new BigDecimal(data.getNormalWorkDays()) : BigDecimal.ZERO);
                personnelDeliveryHour.setHolidaysWorkDays(StrUtil.isNotEmpty(data.getHolidaysWorkDays()) ? new BigDecimal(data.getHolidaysWorkDays()) : BigDecimal.ZERO);
                personnelDeliveryHour.setOmpensatoryDays(StrUtil.isNotEmpty(data.getOmpensatoryDays()) ? new BigDecimal(data.getOmpensatoryDays()) : BigDecimal.ZERO);
                
                // 计算项目分摊工时
                BigDecimal daysAll = personnelDeliveryHour.getRestWorkDays()
                        .add(personnelDeliveryHour.getNormalWorkDays())
                        .add(personnelDeliveryHour.getHolidaysWorkDays()
                        .add(personnelDeliveryHour.getOmpensatoryDays()));

                personnelDeliveryHour.setProjectConsumed(daysAll);
                
                // 更新创建人
                BaseBuildEntityUtil.buildUpdate(personnelDeliveryHour);
                update.add(personnelDeliveryHour);
            } else if (personnelDeliveryHour == null) {
                // 插入
                PersonnelDeliveryHour personnelDeliveryHourInsert = new PersonnelDeliveryHour();
                BeanUtil.copyProperties(data, personnelDeliveryHourInsert);
                // 设置项目信息
                if (projectInfo != null) {
                    personnelDeliveryHourInsert.setProjectId(projectInfo.getId());
                    personnelDeliveryHourInsert.setProjectCode(projectInfo.getItemNo());
                    personnelDeliveryHourInsert.setProjectName(projectInfo.getItemName());
                    personnelDeliveryHourInsert.setProjectStatus(Integer.valueOf(projectInfo.getProjectStatus()));
                    Long revenueDeptId = Optional.ofNullable(projectInfo.getSecondLevelDepartmentId()).orElse(projectInfo.getFirstLevelDepartmentId());
                    personnelDeliveryHourInsert.setRevenueDeptId(revenueDeptId);
                }

                // 设置用户信息
                if (userInfo != null) {
                    personnelDeliveryHourInsert.setUserRealName(userInfo.getName());
                    personnelDeliveryHourInsert.setWorkCode(userInfo.getWorkCode());
                    personnelDeliveryHourInsert.setDeptId(userInfo.getDeptId());
                    personnelDeliveryHourInsert.setDeptName(userInfo.getDeptName());
                }

                personnelDeliveryHourInsert.setRemark(remark == null ? "" : remark);
                personnelDeliveryHourInsert.setCwDueAttendance(StrUtil.isEmpty(data.getCwDueAttendance()) ? BigDecimal.ZERO :
                        new BigDecimal(data.getCwDueAttendance()).setScale(2, RoundingMode.HALF_UP)
                );
                personnelDeliveryHourInsert.setAttendanceDays(StrUtil.isEmpty(data.getAttendanceDays()) ? BigDecimal.ZERO :
                        new BigDecimal(data.getAttendanceDays()).setScale(2, RoundingMode.HALF_UP)
                );
                personnelDeliveryHourInsert.setReuseDate(beginDateTime);
                personnelDeliveryHourInsert.setExecutorUserId(userId);
                personnelDeliveryHourInsert.setExecutorUserRealName(userName);
                
                // 工时导入
                personnelDeliveryHourInsert.setRestWorkDays(StrUtil.isNotEmpty(data.getRestWorkDays()) ? new BigDecimal(data.getRestWorkDays()) : BigDecimal.ZERO);
                personnelDeliveryHourInsert.setNormalWorkDays(StrUtil.isNotEmpty(data.getNormalWorkDays()) ? new BigDecimal(data.getNormalWorkDays()) : BigDecimal.ZERO);
                personnelDeliveryHourInsert.setHolidaysWorkDays(StrUtil.isNotEmpty(data.getHolidaysWorkDays()) ? new BigDecimal(data.getHolidaysWorkDays()) : BigDecimal.ZERO);
                personnelDeliveryHourInsert.setOmpensatoryDays(StrUtil.isNotEmpty(data.getOmpensatoryDays()) ? new BigDecimal(data.getOmpensatoryDays()) : BigDecimal.ZERO);
                
                // 计算项目分摊工时
                BigDecimal daysAll = personnelDeliveryHourInsert.getRestWorkDays()
                        .add(personnelDeliveryHourInsert.getNormalWorkDays())
                        .add(personnelDeliveryHourInsert.getHolidaysWorkDays()
                        .add(personnelDeliveryHourInsert.getOmpensatoryDays()));

                personnelDeliveryHourInsert.setProjectConsumed(daysAll);

                BaseBuildEntityUtil.buildInsert(personnelDeliveryHourInsert);
                insert.add(personnelDeliveryHourInsert);
            }
        }

        if (update.size() > 0) {
            personnelDeliveryHourMapper.batchUpdate(update);
        }

        if (insert.size() > 0) {
            personnelDeliveryHourMapper.batchSave(insert);
        }
    }

}
