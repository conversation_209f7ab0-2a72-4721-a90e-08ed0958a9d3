package com.gok.pboot.pms.entity.vo;

import com.gok.pboot.pms.Util.BaseEntityUtils;
import com.gok.pboot.pms.entity.domain.ProjectTaskAttachment;
import com.google.common.base.Strings;
import com.google.common.io.Files;
import lombok.*;

import java.util.Collection;
import java.util.List;

/**
 * 项目任务附件
 *
 * <AUTHOR>
 * @version 1.1.0
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class ProjectTaskAttachmentVO {

    /**
     * 文件ID
     */
    private String fileId;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件URI
     */
    private String fileUri;

    /**
     * 是否可预览
     */
    private Boolean canPreview;

    @SuppressWarnings("all")
    public static ProjectTaskAttachmentVO from(ProjectTaskAttachment po){
        ProjectTaskAttachmentVO result = new ProjectTaskAttachmentVO();
        Long fileId = po.getFileId();
        String fileName = po.getFileName();
        String ext = Files.getFileExtension(fileName);
        boolean canPreview = "png".equalsIgnoreCase(ext) ||
                "jpg".equalsIgnoreCase(ext) ||
                "jpeg".equalsIgnoreCase(ext);

        result.setFileId(String.valueOf(fileId));
        result.setFileName(Strings.nullToEmpty(fileName));
        result.setCanPreview(canPreview);
        result.setFileUri(canPreview ? "/document/doc/previewImage/" + fileId : "");

        return result;
    }

    public static List<ProjectTaskAttachmentVO> batchFrom(Collection<ProjectTaskAttachment> pos){
        return BaseEntityUtils.mapCollectionToList(pos, ProjectTaskAttachmentVO::from);
    }
}
