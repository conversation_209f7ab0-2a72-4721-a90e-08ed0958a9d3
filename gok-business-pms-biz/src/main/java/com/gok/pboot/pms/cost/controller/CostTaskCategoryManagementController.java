package com.gok.pboot.pms.cost.controller;

import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.cost.entity.dto.CostTaskCategoryManagementDTO;
import com.gok.pboot.pms.cost.entity.vo.CostTaskCategoryManagementVO;
import com.gok.pboot.pms.cost.entity.vo.CostTaskCategorySortVO;
import com.gok.pboot.pms.cost.service.ICostTaskCategoryManagementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 类别管理表 控制器
 *
 * <AUTHOR>
 * @date 2024/03/26
 * @menu 类别管理
 */
@RestController
@RequiredArgsConstructor
@Api(tags = "工单类别管理")
@RequestMapping("/costTaskCategoryManagement")
public class CostTaskCategoryManagementController {

    private final ICostTaskCategoryManagementService costCategoryManagementService;

    /**
     * 获取工单类别列表
     *
     * @return {@link ApiResult}<{@link List}<{@link CostTaskCategoryManagementVO}>>
     */
    @GetMapping("/list")
    @ApiOperation("获取工单类别列表")
    public ApiResult<List<CostTaskCategoryManagementVO>> taskCategoryList() {
        List<CostTaskCategoryManagementVO> list = costCategoryManagementService.taskCategoryList();
        return ApiResult.success(list);
    }

    /**
     * 新增类别管理
     *
     * @param dto 类别管理DTO
     * @return {@link ApiResult}<{@link String}>
     */
    @PostMapping
    @ApiOperation("新增工单类别")
    public ApiResult<String> addTaskCategory(@RequestBody @Valid CostTaskCategoryManagementDTO dto) {
        costCategoryManagementService.addTaskCategory(dto);
        return ApiResult.successMsg("新增成功");
    }

    /**
     * 修改类别管理
     *
     * @param dto 类别管理DTO
     * @return {@link ApiResult}<{@link String}>
     */
    @PutMapping
    @ApiOperation("修改工单类别")
    public ApiResult<String> editTaskCategory(@RequestBody @Valid CostTaskCategoryManagementDTO dto) {
        costCategoryManagementService.editTaskCategory(dto);
        return ApiResult.successMsg("修改成功");
    }

    /**
     * 删除类别管理
     *
     * @param id 主键
     * @return {@link ApiResult}<{@link String}>
     */
    @DeleteMapping("/{id}")
    @ApiOperation("删除工单类别")
    public ApiResult<String> removeTaskCategory(@PathVariable("id") Long id) {
        costCategoryManagementService.removeTaskCategory(id);
        return ApiResult.successMsg("删除成功");
    }

    /**
     * 更新工单类别排序
     *
     * @param taskCategoryIds 任务类别 ID
     * @return {@link ApiResult}<{@link String}>
     */
    @PostMapping("/updateSort")
    @ApiOperation("更新工单类别排序")
    public ApiResult<String> updateTaskCategorySort(@RequestBody List<Long> taskCategoryIds) {
        costCategoryManagementService.updateTaskCategorySort(taskCategoryIds);
        return ApiResult.successMsg("排序更新成功");
    }

    /**
     * 获取工单类型字典值
     *
     * @return {@link ApiResult }<{@link List }<{@link CostTaskCategorySortVO }>>
     */
    @GetMapping("/dict")
    @ApiOperation("获取工单类型字典值")
    public ApiResult<List<CostTaskCategorySortVO>> getTaskCategoryDict() {
        List<CostTaskCategorySortVO> dict = costCategoryManagementService.getTaskCategoryDict();
        return ApiResult.success(dict);
    }
} 