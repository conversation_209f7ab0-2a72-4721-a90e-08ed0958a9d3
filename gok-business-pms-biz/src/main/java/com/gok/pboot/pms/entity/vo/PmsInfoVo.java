package com.gok.pboot.pms.entity.vo;

import com.google.common.collect.ImmutableSet;
import lombok.*;

import java.util.Set;

/**
 * PMS数据信息（用于前端权限判断）
 *
 * <AUTHOR>
 * @version 1.3.2
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class PmsInfoVo {

    /**
     * 所在的项目
     */
    private Set<Long> memberProjectIds;

    /**
     * 有任务负责人的项目
     */
    private Set<Long> chargeProjectIds;

    /**
     * 所在的任务
     */
    private Set<Long> memberTaskIds;

    /**
     * 负责的任务
     */
    private Set<Long> chargeTaskIds;

    /**
     * 空信息对象
     */
    public static PmsInfoVo EMPTY = of(ImmutableSet.of(), ImmutableSet.of(), ImmutableSet.of(), ImmutableSet.of());

    public static PmsInfoVo of(
            Set<Long> memberProjectIds, Set<Long> chargeProjectIds, Set<Long> memberTaskIds, Set<Long> chargeTaskIds
    ) {
        PmsInfoVo result = new PmsInfoVo();

        result.setMemberProjectIds(memberProjectIds);
        result.setChargeProjectIds(chargeProjectIds);
        result.setMemberTaskIds(memberTaskIds);
        result.setChargeTaskIds(chargeTaskIds);

        return result;
    }
}
