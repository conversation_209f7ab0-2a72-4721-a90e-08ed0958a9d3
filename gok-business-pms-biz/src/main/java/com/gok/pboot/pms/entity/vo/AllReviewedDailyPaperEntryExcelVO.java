package com.gok.pboot.pms.entity.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;



/**
 * 所有类型日报导出vo
 *
 * <AUTHOR>
 * @date 2023/10/9
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class AllReviewedDailyPaperEntryExcelVO {


    @ExcelProperty("项目编号")
    private String projectCode;

    @ExcelProperty("项目名称")
    private String projectName;

    @ExcelProperty("日期")
    private String submissionDate;

    @ExcelProperty("人员姓名")
    private String userRealName;

    @ExcelProperty("所属部门")
    private String projectDeptName;

    /**
     * 项目销售姓名
     */
    @ExcelProperty("客户经理")
    private String salesmanUserName;

    /**
     * 售前经理姓名
     */
    @ExcelProperty("售前经理")
    private String preSaleUserName;

    /**
     * 项目经理姓名
     */
    @ExcelProperty("项目经理")
    private String managerUserName;

    @ExcelProperty("任务负责人")
    private String auditPerson;

    /**
     * 对象构造方法
     *
     * @param request 请求对象
     * @return {@link AllReviewedDailyPaperEntryExcelVO}
     */
    public static AllReviewedDailyPaperEntryExcelVO of(UnReviewedDailyPaperEntryExcelVO request) {
        AllReviewedDailyPaperEntryExcelVO result = new AllReviewedDailyPaperEntryExcelVO();
        result.setProjectCode(request.getProjectCode());
        result.setProjectName(request.getProjectName());
        result.setSubmissionDate(request.getSubmissionDate().toString());
        result.setUserRealName(request.getUserRealName());
        result.setProjectDeptName(request.getProjectDeptName());
        result.setSalesmanUserName(request.getSalesmanUserName());
        result.setPreSaleUserName(request.getPreSaleUserName());
        result.setManagerUserName(request.getManagerUserName());
        result.setAuditPerson(request.getAuditPerson());
        return result;
    }
}
