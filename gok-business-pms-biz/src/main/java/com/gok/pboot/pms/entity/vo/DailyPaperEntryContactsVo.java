package com.gok.pboot.pms.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 日报条目联系人vo
 *
 * <AUTHOR>
 * @date 2024/01/30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DailyPaperEntryContactsVo {

    /**
     * 销售人员名
     */
    private String salesmanUserName;

    /**
     * 项目经理名
     */
    private String managerUserName;

    /**
     * 审核人员名列表
     */
    private List<String> auditorNames;

}
