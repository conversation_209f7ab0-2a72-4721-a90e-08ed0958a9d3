package com.gok.pboot.pms.entity.vo;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.gok.pboot.pms.Util.DecimalFormatUtil;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.Util.PmsDictUtil;
import com.gok.pboot.pms.Util.SysDeptUtils;
import com.gok.pboot.pms.entity.SysDept;
import com.gok.pboot.pms.entity.domain.PmsDictItem;
import com.gok.pboot.pms.entity.domain.ProjectInfo;
import com.gok.pboot.pms.enumeration.*;
import com.google.common.base.Strings;
import com.google.common.collect.HashMultimap;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 项目信息VO
 *
 * <AUTHOR>
 * @since 2023-07-14
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class ProjectInfoVO {

    /**
     * ID
     */
    private String id;

    /**
     * 项目编号
     */
    private String itemNo;

    /**
     * 项目名称
     */
    private String itemName;

    /**
     * 业务方向 （0=ICT，1=信息安全，2=软件开发，3=综合，4=数据治理）
     *
     * @see com.gok.pboot.pms.enumeration.BusinessDirectionEnum Y
     */
    private String businessDirection;

    /**
     * 业务方向名称
     */
    private String businessDirectionName;

    /**
     * 项目类型
     */
    private Integer projectType;

    /**
     * 项目类型（公司信息化、通用课程开发、自研产品研发、标准化解决方案打造、专项人才供应链构建、部门工作）
     *
     * @see InternalProjectTypeEnum 内部项目
     * @see ProjectTypeEnum 外部项目
     */
    private String projectTypeName;

    /**
     * 项目归属部门id
     */
    private Long projectDepartmentId;

    /**
     * 项目归属部门
     */
    private String projectDepartment;

    /**
     * 业务归属部门id
     */
    private Long businessDepartmentId;

    /**
     * 业务归属部门
     */
    private String businessDepartment;

    /**
     * 项目状态名称
     */
    private String projectStatusName;

    /**
     * 项目销售人员ID（客户经理id）
     */
    private String salesmanUserId;

    /**
     * 项目销售人员（客户经理）
     */
    private String projectSalesperson;

    /**
     * 项目经理人员ID
     */
    private String managerUserId;

    /**
     * 项目经理人员姓名
     */
    private String managerUserName;

    /**
     * 预计签单金额（元）
     */
    private String expectedOrderAmount;

    /**
     * 预计签单时间（预计完成时间）
     */
    private String expectedCompleteTime;

    /**
     * 商机报备转在建时间点
     */
    private String businessToProjectTime;

    /**
     * 是否签订合同（0=否，1=是，StringUtils.EMPTY）
     *
     * @see com.gok.pboot.pms.common.base.BaseConstants
     */
    private String signContract;

    /**
     * 是否签订合同名称
     */
    private String signContractName;

    /**
     * 立项日期
     */
    private String projectDate;
    /**
     * 项目所在地
     */
    private String projectLocation;

    /**
     * 是否内部项目（1=是，2=否，StringUtils.EMPTY）
     *
     * @see com.gok.pboot.pms.enumeration.IsNoInternalProjectEnum
     */
    private String isNotInternalProject;

    /**
     * 是否内部项目名称
     */
    private String isNotInternalProjectName;

    /**
     * 项目归档时间（结束日期）
     */
    private String projectFilingTime;

    /**
     * 业务归属一级部门id
     */
    private String firstLevelDepartmentId;

    /**
     * 业务归属一级部门
     */
    private String firstLevelDepartment;

    /**
     * 项目交付部门id
     */
    private String proDeliveryDepartmentId;

    /**
     * 项目交付部门
     */
    private String proDeliveryDepartment;

    /**
     * 业务类型 （0=教学相关，1=非教学相关）
     *
     * @see com.gok.pboot.pms.enumeration.BusinessTypeEnum
     */
    private String businessType;

    /**
     * 业务类型字典
     */
    private String businessTypeName;

    /**
     * 项目状态
     *
     * @see ProjectStatusEnum
     */
    private String projectStatus;

    /**
     * 项目阶段
     */
    private String projectStage;

    /**
     * 项目阶段字典内容
     */
    private String projectStageName;

    /**
     * 项目把握度
     *
     * @see com.gok.pboot.pms.enumeration.ProjectGraspDegreeEnum
     */
    private String projectGraspDegree;

    /**
     * 项目把握度字典
     */
    private String projectGraspDegreeName;

    /**
     * 招标方式
     *
     * @see com.gok.pboot.pms.enumeration.ZbfsEnum
     */
    private String zbfs;

    /**
     * 招标方式字典
     */
    private String zbfsName;

    /**
     * 国科优势分析
     */
    private String gkysfx;

    /**
     * 国科劣势分析
     */
    private String gklsfx;

    /**
     * 项目当前进展
     */
    private String currentProgress;

    /**
     * 遇到的问题及所需支持
     */
    private String yddwtjsxdzc;

    /**
     * 下一步推进计划
     */
    private String nextStepForwardPlan;

    /**
     * 备注
     */
    private String remark;

    /**
     * 售前经理id
     */
    private String preSaleUserId;


    /**
     * 售前人员姓名(售前经理)
     */
    private String preSaleUserName;

    /**
     * 项目区域主管姓名
     */
    private String regionUserName;

    /**
     * 项目市场总监姓名
     */
    private String directorUserName;

    /**
     * 预估毛利率
     */
    private String ygmll;

    /**
     * 资源通道
     *
     * @see com.gok.pboot.pms.enumeration.ResourceChannelEnum
     */
    private String resourceChannel;

    /**
     * 资源通道字典
     */
    private String resourceChannelName;

    /**
     * 是否需要招投标,0:是 1:否
     */
    private String sfzjqht;

    /**
     * 是否需要招投标字典
     */
    private String sfzjqhtName;

    /**
     * 项目整包预算
     */
    private BigDecimal proPackageBudget;

    /**
     * 签单金额评判依据
     *
     * @see com.gok.pboot.pms.enumeration.QdjeppyjEnum
     */
    private String qdjeppyj;

    /**
     * 签单金额评判依据字典
     */
    private String qdjeppyjName;

    /**
     * 成熟度
     *
     * @see com.gok.pboot.pms.enumeration.MaturityEnum
     */
    private String maturity;

    /**
     * 成熟度字典
     */
    private String maturityName;

    /**
     * 技术领域
     *
     * @see com.gok.pboot.pms.enumeration.TechnologyFieldEnum
     */
    private String technologyField;

    /**
     * 技术领域字典
     */
    private String technologyFieldName;

    /**
     * 客户名称
     */
    private String customerName;
    /**
     * 客户联系人
     */
    private String customerContact;
    /**
     * 客户联系方式
     */
    private String customerPhone;

    /**
     * 最终客户行业
     *
     * @see com.gok.pboot.pms.enumeration.EndCustomerIndustryEnum
     */
    private String endCustomerIndustry;

    /**
     * 最终客户行业字典
     */
    private String endCustomerIndustryName;

    /**
     * 项目背景及建设内容（项目目标及建设范围）
     */
    private String proConstructionScope;

    /**
     * 我司建设内容
     */
    private String wsjsnr;

    /**
     * 业务经理id
     */
    private Long businessManagerId;

    /**
     * 业务经理
     */
    private String businessManager;

    /**
     * 业务背景和现状
     */
    private String businessBackgroundAndSituation;

    /**
     * 业务需求
     */
    private String businessRequirements;

    /**
     * 业务目标
     */
    private String businessObjectives;

    /**
     * 进度要求
     */
    private String progressRequirements;

    /**
     * OA年度项目计划表文件名url
     */
    private String annualProjectPlanFileUrl;

    /**
     * OA项目业务需求文档文件名
     */
    private List<OaFileInfoVo> projectBusinessRequirementsOafileName;

    /**
     * 预估成本
     */
    private BigDecimal estimatedCost;

    /**
     * 项目交付成果和验收标准
     */
    private List<ProjectDeliverAndAcceptCriteriaVO> projectDeliverAndAcceptCriteriaVOList;

    /**
     * 项目预估成本表Vo
     */
    private List<ProjectEstimatedCostVO> ProjectEstimatedCostVOList;

    /**
     * 项目组成员表VO
     */
    private List<ProjectMemberVO> projectMemberVOList;

    /**
     * 研发风险表VO
     */
    private List<ProjectResearchRiskVO> projectResearchRiskVOList;

    /**
     * 是否关注 0-未关注 1-已关注
     */
    private Integer isAttention;

    /**
     * 交付形式
     *
     * @see com.gok.pboot.pms.enumeration.DeliverTypeEnum
     */
    private Integer deliverType;

    private String deliverTypeName;

    /**
     * 最终客户
     */
    private String endCustomer;

    /**
     * 最终客户分级
     */
    private String endCustomerGrade;

    /**
     * 签约客户
     */
    private String contractCustomer;

    /**
     * 签约客户分级
     */
    private String contractCustomerGrade;

    /**
     * 业务类型
     */
    private Integer secondaryBusinessType;

    private String secondaryBusinessTypeName;

    /**
     * 业务归属二级部门id
     */
    private Long secondLevelDepartmentId;

    /**
     * 业务归属二级部门
     */
    private String secondLevelDepartment;

    /**
     * 预计毛利率
     */
    private String ygmml;

    /**
     * 项目签约主体
     */
    private String contractEntity;

    /**
     * 客户集
     */
    private String customerCollection;
    private String customerCollectionName;

    /**
     * 收入类型
     *
     * @see ContractIncomeTypeEnum
     */
    private Integer incomeType;
    private String incomeTypeName;

    /**
     * 技术类型
     */
    private String technologyType;
    private String technologyTypeName;

    /**
     * 是否涉及外采
     */
    private String isExternalProcurement;

    /**
     * 商机里程碑
     */
    private String businessMilestone;

    /**
     * 商机阶段
     */
    private String businessStage;

    /**
     * 预计签约时间
     */
    private LocalDate expectedSignDate;

    /**
     * 合同结算方式
     *
     * @see ProjectContractSettlementMethodEnum
     */
    private Integer contractSettlementMethod;
    private String contractSettlementMethodName;

    /**
     * 是否要提前投入 0-是 1-否
     */
    private String isAdvanceInvestment;

    /**
     * 回款条件
     */
    private String paymentCondition;

    /**
     * 项目转在建依据及佐证
     */
    private Integer converseOngoingEvidence;
    private String converseOngoingEvidenceName;

    /**
     * 进度、质量等要
     */
    private String progressQualityRequirement;

    /**
     * 经营单元ID
     */
    private Long businessId;

    /**
     * 经营单元名称
     */
    private String businessUnitName;

    /**
     * 所属客户ID
     */
    private Long unitId;

    /**
     * 所属客户名称
     */
    private String unitName;

    /**
     * 项目归属二级部门ID
     */
    private Long xmgsbmejbmId;

    /**
     * 项目归属二级部门
     */
    private String xmgsbmejbm;

    /**
     * 收入板块
     */
    private String srlx;

    /**
     * 技术类型B表
     */
    private List<String> jslxbb;

    /**
     * 是否为涉密项目
     */
    private String sfwsmxm;

    /**
     * 项目需求
     */
    private String xmbj;

    /**
     * 相关详细说明文档
     */
    private List<OaFileVo> xgxxsmwd;

    /**
     * 交付物
     */
    private String jfw;

    /**
     * 交付地点
     */
    private String jfdd;

    /**
     * 交付期限
     */
    private String jfqx;

    /**
     * 质保期(月)
     */
    private String zbqy;

    /**
     * 保密要求
     */
    private String bmyq;

    /**
     * 售前报告是否移交
     */
    private String sqbgsfyj;

    /**
     * 其它售前移交资料
     */
    private List<OaFileVo> qtsqyjzl;

    /**
     * 计划交付开始时间
     */
    private LocalDate jhjfkssj;

    /**
     * 计划交付完成时间
     */
    private LocalDate jhjfwcsj;

    /**
     * 约定服务开始日期
     */
    private LocalDate ydfwksrq;

    /**
     * 约定服务结束日期
     */
    private LocalDate ydfwjsrq;

    /**
     * 总服务周期(月)
     */
    private String zfwzqy;

    /**
     * 预计每次结算金额
     */
    private String yjmcjsje;

    /**
     * 结束周期(月)
     */
    private String jszqy;

    /**
     * 预计回款账期(天)
     */
    private String yjhkzqy;

    /**
     * B表立项流程
     */
    private Long bblxfl;

    /**
     * 项目重启日期
     */
    private LocalDate xmcqrq;

    /**
     * 项目信息实体类转换项目信息VO类
     *
     * @param po          项目信息实体类
     * @param deptIdMap   部门信息Map集合
     * @param dictItemMap 字典信息Map集合
     * @return 项目信息VO类
     */
    public static ProjectInfoVO from(ProjectInfo po,
                                     Map<Long, SysDept> deptIdMap,
                                     HashMultimap<String, PmsDictItem> dictItemMap) {
        ProjectInfoVO result = new ProjectInfoVO();
        Long salesmanUserId = po.getSalesmanUserId();
        Long managerUserId = po.getManagerUserId();
        //是否内部项目
        // 业务归属一级部门id
        Long firstLevelDepartmentId = po.getFirstLevelDepartmentId();
        // 业务归属二级部门id
        Long secondLevelDepartmentId = po.getSecondLevelDepartmentId();
        // 预计签单金额
        BigDecimal expectedOrderAmount = po.getExpectedOrderAmount();
        // 是否签订合同
        String signContract = po.getSignContract();
        result.setId(String.valueOf(po.getId()));
        result.setItemNo(Strings.nullToEmpty(po.getItemNo()));
        result.setItemName(Strings.nullToEmpty(po.getItemName()));
        // 预计签约时间
        result.setExpectedSignDate(po.getExpectedSignDate());
        // 回款条件
        result.setPaymentCondition(po.getPaymentCondition());
        // 进度、质量等要求
        result.setProgressQualityRequirement(po.getProgressQualityRequirement());
        // 项目重启日期
        result.setXmcqrq(po.getXmcqrq());
        //业务方向
        String businessDirection = (Strings.nullToEmpty(po.getBusinessDirection()));
        result.setBusinessDirection(businessDirection);
        result.setBusinessDirectionName(StringUtils.isBlank(businessDirection) ?
                StringUtils.EMPTY : Strings.nullToEmpty(BusinessDirectionEnum.getNameByVal(Integer.parseInt(businessDirection))));

        result.setSalesmanUserId(salesmanUserId == null ? StringUtils.EMPTY : String.valueOf(salesmanUserId));
        result.setProjectSalesperson(Strings.nullToEmpty(po.getProjectSalesperson()));
        result.setManagerUserId(managerUserId == null ? StringUtils.EMPTY : String.valueOf(managerUserId));
        result.setManagerUserName(Strings.nullToEmpty(po.getManagerUserName()));
        result.setPreSaleUserId(po.getPreSaleUserId() == null ? StringUtils.EMPTY: String.valueOf(po.getPreSaleUserId()));
        result.setPreSaleUserName(Strings.nullToEmpty(po.getPreSaleUserName()));

        // 预计签单金额设置千分位及保留两位小数
        DecimalFormat decimalFormat = new DecimalFormat("#,##0.00");
        result.setExpectedOrderAmount(expectedOrderAmount == null
                ? StringUtils.EMPTY
                : DecimalFormatUtil.setAndValidate(expectedOrderAmount.toString(), 2, RoundingMode.DOWN, decimalFormat)
        );

        // 预计签单时间（预计完成时间）
        result.setExpectedCompleteTime(Strings.nullToEmpty(po.getExpectedCompleteTime()));
        // 商机报备转在建时间点
        result.setBusinessToProjectTime(Strings.nullToEmpty(po.getBusinessToProjectTime()));
        result.setSignContract(signContract);
        result.setSignContractName(Strings.nullToEmpty(EnumUtils.getNameByValue(SignContractEnum.class, signContract)));
        // 立项日期
        result.setProjectDate(Strings.nullToEmpty(po.getProjectDate()));
        //是否内部项目
        String notInternalProject = po.getIsNotInternalProject() == null ? StringUtils.EMPTY : String.valueOf(po.getIsNotInternalProject());
        result.setIsNotInternalProject(notInternalProject);
        result.setIsNotInternalProjectName(StringUtils.EMPTY.equals(notInternalProject) ? StringUtils.EMPTY :
                IsNoInternalProjectEnum.getNameByVal(Integer.parseInt(notInternalProject)));

        // 项目归档时间（结束日期）
        result.setProjectFilingTime(Strings.nullToEmpty(LocalDateTimeUtil.formatNormal(po.getProjectFilingTime())));

        //部门处理
        result.setFirstLevelDepartmentId(firstLevelDepartmentId == null ? StringUtils.EMPTY : String.valueOf(firstLevelDepartmentId));
        result.setFirstLevelDepartment(Strings.nullToEmpty(po.getFirstLevelDepartment()));
        result.setBusinessDepartmentId(Optional.ofNullable(secondLevelDepartmentId).orElse(firstLevelDepartmentId));
        result.setBusinessDepartment(po.getBusinessDepartmentId() == null
                ? po.getBusinessDepartment()
                : SysDeptUtils.collectFullName(deptIdMap, po.getBusinessDepartmentId()));
        result.setProDeliveryDepartment(po.getProDeliveryDepartmentId() == null
                ? StringUtils.EMPTY
                : SysDeptUtils.collectFullName(deptIdMap, po.getProDeliveryDepartmentId()));
        // 项目归属部门二级部门
        result.setXmgsbmejbmId(po.getXmgsbmejbmId());
        result.setXmgsbmejbm(po.getXmgsbmejbmId() == null
                ? StringUtils.EMPTY
                : SysDeptUtils.collectFullName(deptIdMap, po.getXmgsbmejbmId()));

        result.setBusinessId(po.getBusinessId());
        result.setUnitId(po.getUnitId());

        // 收入板块
        result.setSrlx(IncomeSegmentEnum.getNameByVal(po.getSrlx()));
        // 技术类型B表
        if (ObjectUtil.isNotEmpty(po.getJslxbb())) {
            List<String> jslxbbList = Arrays.asList(po.getJslxbb().split(","));
            result.setJslxbb(jslxbbList.stream()
                    .map(jslx -> TechnologyTypeEnum.getNameByVal(Integer.valueOf(jslx)))
                    .collect(Collectors.toList()));
        } else {
            result.setJslxbb(Collections.emptyList());
        }

        // 是否为涉密项目
        result.setSfwsmxm(po.getSfwsmxm() == null ? StringUtils.EMPTY : NoOrYesEnum.getNameByVal(po.getSfwsmxm()));

        // 项目交付要求
        result.setXmbj(po.getXmbj());
        result.setJfw(po.getJfw());
        result.setJfdd(po.getJfdd());
        result.setJfqx(po.getJfqx());
        result.setZbqy(po.getZbqy());
        result.setBmyq(po.getBmyq());

        // 售前资料
        result.setSqbgsfyj(po.getSqbgsfyj() == null ? StringUtils.EMPTY : NoOrYesEnum.getNameByVal(po.getSqbgsfyj()));
        result.setJhjfkssj(po.getJhjfkssj());
        result.setJhjfwcsj(po.getJhjfwcsj());

        // 人力外包计划信息
        result.setYdfwksrq(po.getYdfwksrq());
        result.setYdfwjsrq(po.getYdfwjsrq());
        result.setZfwzqy(po.getZfwzqy());
        result.setYjmcjsje(po.getYjmcjsje() == null
                ? StringUtils.EMPTY :
                DecimalFormatUtil.setAndValidate(po.getYjmcjsje().toString(), 2, RoundingMode.DOWN, decimalFormat));
        result.setJszqy(po.getJszqy());
        result.setYjhkzqy(po.getYjhkzqy());

        // B表立項流程
        result.setBblxfl(po.getBblxfl());

        //业务类型
        String businessType = Strings.nullToEmpty(po.getBusinessType());
        result.setBusinessType(businessType);
        result.setBusinessTypeName(StringUtils.EMPTY.equals(businessType)
                ? StringUtils.EMPTY
                : BusinessTypeEnum.getNameByVal(Integer.parseInt(businessType))
        );
        // 新业务类型
        result.setSecondaryBusinessType(po.getSecondaryBusinessType());
        result.setSecondaryBusinessTypeName(EnumUtils.getNameByValue(SecondaryBusinessTypeEnum.class, po.getSecondaryBusinessType()));

        //其他信息
        //项目阶段
        String projectStage = Strings.nullToEmpty(po.getProjectStage());
        String projectStageName = EnumUtils.getNameByValue(ProjectStageEnum.class, projectStage);
        result.setProjectStage(projectStage);
        result.setProjectStageName(StrUtil.nullToDefault(projectStageName, StringUtils.EMPTY));

        String projectGraspDegree = Strings.nullToEmpty(po.getProjectGraspDegree());
        result.setProjectGraspDegree(projectGraspDegree);
        result.setProjectGraspDegreeName(StringUtils.EMPTY.equals(projectGraspDegree) ? StringUtils.EMPTY :
                ProjectGraspDegreeEnum.getNameByVal(Integer.parseInt(projectGraspDegree)));

        //招标方式
        String zbfs = Strings.nullToEmpty(po.getZbfs());
        result.setZbfs(zbfs);
        result.setZbfsName(StringUtils.EMPTY.equals(zbfs) ? StringUtils.EMPTY :
                ProjectGraspDegreeEnum.getNameByVal(Integer.parseInt(zbfs)));

        result.setGkysfx(Strings.nullToEmpty(po.getGkysfx()));
        result.setGklsfx(Strings.nullToEmpty(po.getGklsfx()));
        //项目状态
        String projectStatus = Strings.nullToEmpty(po.getProjectStatus());
        result.setProjectStatus(projectStatus);
        result.setProjectStatusName(StringUtils.EMPTY.equals(projectStatus) ? StringUtils.EMPTY :
                ProjectStatusEnum.getNameByStrVal(projectStatus));
        result.setCurrentProgress(Strings.nullToEmpty(po.getCurrentProgress()));
        result.setYddwtjsxdzc(Strings.nullToEmpty(po.getYddwtjsxdzc()));
        result.setNextStepForwardPlan(Strings.nullToEmpty(po.getNextStepForwardPlan()));
        result.setRemark(Strings.nullToEmpty(po.getRemark()));



        result.setRegionUserName(Strings.nullToEmpty(po.getRegionUserName()));
        result.setDirectorUserName(Strings.nullToEmpty(po.getDirectorUserName()));

        result.setProjectLocation(Strings.nullToEmpty(po.getProjectLocation()));
        result.setYgmll(Strings.nullToEmpty(po.getYgmll()));
        //资源通道
        String resourceChannel = Strings.nullToEmpty(po.getResourceChannel());
        result.setResourceChannel(resourceChannel);
        result.setResourceChannelName(StringUtils.EMPTY.equals(resourceChannel) ? StringUtils.EMPTY :
                ResourceChannelEnum.getNameByVal(Integer.parseInt(resourceChannel)));

        //是否需要招标
        String sfzjqht = po.getSfzjqht() == null ? StringUtils.EMPTY : String.valueOf(po.getSfzjqht());
        result.setSfzjqht(sfzjqht);
        result.setSfzjqhtName(StringUtils.isBlank(projectGraspDegree) ? StringUtils.EMPTY : ("1".equals(sfzjqht) ? "否" : "是"));

        result.setProPackageBudget(po.getProPackageBudget());
        String qdjeppyj = Strings.nullToEmpty(po.getQdjeppyj());
        result.setQdjeppyj(qdjeppyj);
        result.setQdjeppyjName(StringUtils.EMPTY.equals(qdjeppyj) ? StringUtils.EMPTY :
                QdjeppyjEnum.getNameByVal(Integer.parseInt(qdjeppyj)));

        //技术领域
        String technologyField = Strings.nullToEmpty(po.getTechnologyField());
        result.setTechnologyField(technologyField);
        result.setTechnologyFieldName(StringUtils.EMPTY.equals(technologyField)
                ? StringUtils.EMPTY
                : TechnologyFieldEnum.getNameByVal(Integer.parseInt(technologyField))
        );
        //技术类型
        result.setTechnologyType(po.getTechnologyType());
        result.setTechnologyTypeName(ContractTechnologyTypeBTableEnum.getNameListStrByValueListStr(po.getTechnologyType()));

        result.setCustomerContact(Strings.nullToEmpty(po.getCustomerContact()));
        result.setCustomerName(Strings.nullToEmpty(po.getCustomerName()));
        result.setCustomerPhone(Strings.nullToEmpty(po.getCustomerPhone()));

        String endCustomerIndustry = Strings.nullToEmpty(po.getEndCustomerIndustry());
        result.setEndCustomerIndustry(endCustomerIndustry);
        result.setEndCustomerIndustryName(StringUtils.EMPTY.equals(endCustomerIndustry) ?
                StringUtils.EMPTY
                :
                EndCustomerIndustryEnum.getNameByVal(Integer.parseInt(endCustomerIndustry))
        );

        String maturity = po.getMaturity() == null ? StringUtils.EMPTY : String.valueOf(po.getMaturity());
        result.setMaturity(maturity);
        result.setMaturityName(StringUtils.EMPTY.equals(maturity) ? StringUtils.EMPTY :
                MaturityEnum.getNameByVal(Integer.parseInt(maturity)));

        result.setProConstructionScope(Strings.nullToEmpty(po.getProConstructionScope()));
        result.setWsjsnr(Strings.nullToEmpty(po.getWsjsnr()));
        //业务经理
        result.setBusinessManagerId(po.getBusinessManagerId());
        result.setBusinessManager(Strings.nullToEmpty(po.getBusinessManager()));
        //项目类型
        Integer projectType = po.getProjectType();
        if (null != projectType) {
            result.setProjectType(projectType);
            String projectTypeName = ObjectUtil.equal(1L, projectType)
                    ? InternalProjectTypeEnum.getNameByVal(projectType)
                    : EnumUtils.getNameByValue(ProjectTypeEnum.class, projectType);
            result.setProjectTypeName(projectTypeName);
        }
        //需求信息
        result.setBusinessBackgroundAndSituation(po.getBusinessBackgroundAndSituation());
        result.setBusinessRequirements(po.getBusinessRequirements());
        result.setBusinessObjectives(po.getBusinessObjectives());
        result.setProgressRequirements(po.getProgressRequirements());
        //客户信息
        result.setEndCustomer(po.getEndCustomer());
        result.setContractCustomer(po.getContractCustomer());
        result.setEndCustomerGrade(EnumUtils.getNameByValue(CustomerGradeEnum.class, po.getEndCustomerGrade()));
        result.setContractCustomerGrade(EnumUtils.getNameByValue(CustomerGradeEnum.class, po.getContractCustomerGrade()));
        //交付形式
        result.setDeliverType(po.getDeliverType());
        result.setDeliverTypeName(EnumUtils.getNameByValue(DeliverTypeEnum.class, po.getDeliverType()));
        //收入类型
        result.setIncomeType(po.getIncomeType());
        result.setIncomeTypeName(EnumUtils.getNameByValue(ContractIncomeTypeEnum.class, po.getIncomeType()));
        //合同结算方式
        result.setContractSettlementMethod(po.getContractSettlementMethod());
        result.setContractSettlementMethodName(EnumUtils.getNameByValue(ProjectContractSettlementMethodEnum.class, po.getContractSettlementMethod()));
        result.setEstimatedCost(po.getEstimatedCost());
        result.setYgmll(po.getYgmll());
        //项目转在建依据及佐证
        result.setConverseOngoingEvidence(po.getConverseOngoingEvidence());
        result.setConverseOngoingEvidenceName(EnumUtils.getNameByValue(ConverseOngoingEvidenceEnum.class, po.getConverseOngoingEvidence()));

        //字典值处理
        result.setContractEntity(PmsDictUtil.getLabelFromPmsDictItems(po.getContractEntity(), dictItemMap.get(PmsDictEnum.CONTRACT_ENTITY.getValue())));
        String isExternalProcurement = po.getIsExternalProcurement() != null ? String.valueOf(po.getIsExternalProcurement()) : StringUtils.EMPTY;
        result.setIsExternalProcurement(PmsDictUtil.getLabelFromPmsDictItems(isExternalProcurement, dictItemMap.get(PmsDictEnum.IS_EXTERNAL_PROCUREMENT.getValue())));
        String businessMilestone = po.getBusinessMilestone() != null ? String.valueOf(po.getBusinessMilestone()) : StringUtils.EMPTY;
        result.setBusinessMilestone(PmsDictUtil.getLabelFromPmsDictItems(businessMilestone, dictItemMap.get(PmsDictEnum.BUSINESS_MILESTONE.getValue())));
        String businessStage = po.getBusinessStage() != null ? String.valueOf(po.getBusinessStage()) : StringUtils.EMPTY;
        result.setBusinessStage(PmsDictUtil.getLabelFromPmsDictItems(businessStage, dictItemMap.get(PmsDictEnum.BUSINESS_STAGE.getValue())));
        String isAdvanceInvestment = po.getIsAdvanceInvestment() != null ? String.valueOf(po.getIsAdvanceInvestment()) : StringUtils.EMPTY;
        result.setIsAdvanceInvestment(PmsDictUtil.getLabelFromPmsDictItems(isAdvanceInvestment, dictItemMap.get(PmsDictEnum.ADVANCE_INVESTMENT.getValue())));
        result.setCustomerCollection(po.getCustomerCollection());
        result.setCustomerCollectionName(PmsDictUtil.getLabelFromPmsDictItems(po.getCustomerCollection(), dictItemMap.get(PmsDictEnum.CUSTOMER_COLLECTION.getValue())));

        return result;
    }

}
