package com.gok.pboot.pms.eval.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import com.gok.pboot.pms.enumeration.ProjectOperationRoleEnum;
import lombok.*;

/**
 * 项目评价用户角色实体类
 *
 * <AUTHOR>
 * @create 2025/05/12
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("eval_user_role")
public class EvalUserRole extends BeanEntity<Long> {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 角色
     * {@link ProjectOperationRoleEnum#getValue}
     */
    private Integer role;

} 