package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 技术类型枚举
 *
 * <AUTHOR>
 * @date 13/12/2023
 */
@Getter
@AllArgsConstructor
public enum ContractTechnologyTypeEnum implements ValueEnum<Integer> {

    /**
     * ICT集成
     */
    ICT_INTEGRATED(0, "ICT集成"),

    /**
     * 综合运维
     */
    COMPREHENSIVE_OPERATIONS(1, "综合运维"),

    /**
     * 安全服务
     */
    SECURITY_SERVICE(2, "安全服务"),

    /**
     * 软件开发
     */
    SOFTWARE_DEVELOPMENT(3, "软件开发"),

    /**
     * ERP交付
     */
    ERP_DELIVERY(4, "ERP交付"),

    /**
     * 数据治理
     */
    DATA_GOVERNANCE(5, "数据治理"),

    /**
     * 其他
     */
    OTHER(6, "其他");

    private final Integer value;

    private final String name;

}
