package com.gok.pboot.pms.cost.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 差旅住宿标准配置历史版本 vo
 *
 * <AUTHOR>
 * @date 2025/01/07
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class VersionHistoryVO {

    /**
     * 历史版本id
     */
    private Long versionId;

    /**
     * 历史版本
     */
    private String versionName;

    /**
     * 更新时间
     */
    private String mtime;

    /**
     * 操作人
     */
    private String operatorName;

    /**
     * 部门名称
     */
    private String operatorDeptName;
}
