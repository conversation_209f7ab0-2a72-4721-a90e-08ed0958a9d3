package com.gok.pboot.pms.entity.vo;

import com.gok.pboot.pms.Util.DailyPaperDateUtils;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.common.base.BaseConstants;
import com.gok.pboot.pms.entity.DailyPaper;
import com.gok.pboot.pms.entity.Holiday;
import com.gok.pboot.pms.enumeration.ApprovalStatusEnum;
import com.gok.pboot.pms.enumeration.PersonnelStatusEnum;
import com.google.common.base.Strings;
import lombok.*;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static com.gok.pboot.pms.enumeration.ApprovalStatusEnum.WTB;

/**
 * - 日报VO -
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/8/24 16:46
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@ToString
@Builder
public class DailyPaperVO {
    /**
     * ID
     */
    private Long id;
    /**
     * 人员姓名
     */
    private String userRealName;
    /**
     * 人员状态（0=正式，1=实习）
     */
    private Integer userStatus;
    /**
     * 人员状态名称
     */
    private String userStatusName;
    /**
     * 日期
     */
    private LocalDate submissionDate;
    /**
     * 日期（已格式化）
     */
    private String submissionDateFormatted;
    /**
     * 提交时间（已格式化）
     */
    private String ctimeFormatted;
    /**
     * 提交时间
     */
    private LocalDateTime ctime;
    /**
     * 最后提交（修改）时间
     */
    private LocalDateTime mtime;
    /**
     * 最后提交（修改）日期
     */
    private LocalDate mtimeDate;
    /**
     * 提交类型（是否为工作日）
     */
    private Integer workday;
    /**
     * 提交类型名
     */
    private String workdayName;
    /**
     * 项目数量
     */
    private Integer projectCount;
    /**
     * 任务数量
     */
    private Integer taskCount;
    /**
     * 正常工时
     */
    private BigDecimal dailyHourCount;
    /**
     * 调休OA
     */
    private BigDecimal compensatoryHourCount;

    /**
     * 加班工时
     */
    private BigDecimal addedHourCount;
    /**
     * 审核状态（0=未提交，1=已退回，2=待审核，3=不通过，4=已通过）
     */
    private Integer approvalStatus;

    /**
     * 审批状态集合
     */
    private String approvalStatusStrings;

    /**
     * 审批状态名称集合
     */
    private List<String> approvalStatusNameList;

    /**
     * 审核状态名称
     */
    private String approvalStatusName;

    /**
     * OA加班小时数
     */
    private BigDecimal hourData;

    /**
     * OA请假小时数（扣除销假）
     */
    private BigDecimal leaveHourData;

    /**
     * 调休工时
     */
    private BigDecimal leaveHours;

    /**
     * 日报是否归档
     */
    private Boolean ifFiled;

    /**
     * 是否是假期
     */
    private Boolean ifHoliday;

    /**
     * 假期类型：1-法定节假日 0-普通休息日
     */
    private Integer holidayType;

    /**
     * 是否是异常日报
     */
    private Boolean ifAbnormal;

    /**
     * 工时异常情况
     */
    private String abnormalMsg;

    /**
     * 总时长（正常+加班+请休假）
     */
    private BigDecimal totalHour;

    /**
     * 日报提交时间
     */
    private LocalDateTime submissionTime;

    public DailyPaperVO(DailyPaper dailyPaper){
        Timestamp mtime = dailyPaper.getMtime();

        this.id = dailyPaper.getId();
        this.submissionDate = dailyPaper.getSubmissionDate();
        this.submissionDateFormatted = DailyPaperDateUtils.asDateString(submissionDate);
        this.ctime = dailyPaper.getCtime().toLocalDateTime();
        this.ctimeFormatted =  dailyPaper.getSubmissionTime() == null ? "" : DailyPaperDateUtils.asDatetimeString(submissionTime);
        this.mtime = mtime == null ? ctime : mtime.toLocalDateTime();
        this.mtimeDate = this.mtime.toLocalDate();
        this.workday = dailyPaper.getWorkday();
        this.workdayName = BaseConstants.YES.equals(workday) && !WTB.getValue().equals(dailyPaper.getApprovalStatus()) ?
                "正常提交" : "无日报";
        this.projectCount = dailyPaper.getProjectCount();
        this.taskCount = dailyPaper.getTaskCount();
        this.dailyHourCount = dailyPaper.getDailyHourCount();
        this.addedHourCount = dailyPaper.getAddedHourCount();
        this.approvalStatus = dailyPaper.getApprovalStatus();
        this.approvalStatusName = EnumUtils.getNameByValue(ApprovalStatusEnum.class, approvalStatus);
        this.userStatus = dailyPaper.getUserStatus();
        this.userStatusName = EnumUtils.getNameByValue(PersonnelStatusEnum.class, this.userStatus);
        this.totalHour = dailyHourCount.add(addedHourCount);
    }

    public DailyPaperVO(DailyPaper dailyPaper, String userRealName, BigDecimal leaveHour, BigDecimal compensatoryLeave, Holiday holiday){
        this(dailyPaper);
        this.userRealName = Strings.nullToEmpty(userRealName);
        this.leaveHourData = !Optional.ofNullable(holiday).isPresent() ? leaveHour : BigDecimal.ZERO;
        this.totalHour = this.getTotalHour().add(leaveHour).add(compensatoryLeave);
        this.leaveHours = !Optional.ofNullable(holiday).isPresent() ? compensatoryLeave : BigDecimal.ZERO;
        this.holidayType = Optional.ofNullable(holiday).isPresent() ? holiday.getHolidayType() : null;
        this.setCompensatoryHourCount(!Optional.ofNullable(holiday).isPresent() ? compensatoryLeave : BigDecimal.ZERO);
    }
}
