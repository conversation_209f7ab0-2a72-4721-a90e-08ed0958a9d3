package com.gok.pboot.pms.service.impl;

import com.gok.components.common.user.PigxUser;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.entity.domain.ProjectTask;
import com.gok.pboot.pms.entity.domain.ProjectTaskProgress;
import com.gok.pboot.pms.entity.dto.ProjectTaskProgressAddDTO;
import com.gok.pboot.pms.enumeration.ProjectTaskProgressEnum;
import com.gok.pboot.pms.mapper.ProjectTaskMapper;
import com.gok.pboot.pms.mapper.ProjectTaskProgressFeedbackMapper;
import com.gok.pboot.pms.mapper.ProjectTaskProgressMapper;
import com.gok.pboot.pms.service.IProjectTaskProgressService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import javax.validation.ValidationException;

/**
 * 项目任务进展
 *
 * <AUTHOR>
 * @version 1.1.0
 */
@Service
@AllArgsConstructor
public class ProjectTaskProgressServiceImpl implements IProjectTaskProgressService {

    private final ProjectTaskProgressMapper mapper;
    private final ProjectTaskMapper taskMapper;
    private final ProjectTaskProgressFeedbackMapper feedbackMapper;

    @Override
    public void add(ProjectTaskProgressAddDTO dto) {
        ProjectTask task = validateAddDTOAndGetTask(dto);
        PigxUser user = SecurityUtils.getUser();
        ProjectTaskProgress po = ProjectTaskProgress.of(dto, task, user.getId(), user.getAvatar(), user.getNickname());

        BaseBuildEntityUtil.buildInsert(po);
        mapper.insert(po);
    }

    private ProjectTask validateAddDTOAndGetTask(ProjectTaskProgressAddDTO dto){
        ProjectTask task;

        if (!EnumUtils.existsEnumValue(dto.getProgress(), ProjectTaskProgressEnum.class)){
            throw new ValidationException("枚举值不合法");
        }
        task = taskMapper.selectById(dto.getTaskId());
        if (task == null){
            throw new ValidationException("没有找到指定任务");
        }

        return task;
    }

    @Override
    public void deleteById(Long id) {
        ProjectTaskProgress progress;

        if (feedbackMapper.existsByProgressId(id)){
            throw new ValidationException("该进展下有回复内容，无法删除");
        }
        progress = mapper.selectById(id);
        if (progress == null){
            throw new ValidationException("没有找到进展，无法删除");
        }
        if (!SecurityUtils.getUser().getId().equals(progress.getUserId())){
            throw new ValidationException("您不是该进展的提交人，无法删除");
        }
        mapper.deleteById(id);
    }
}
