package com.gok.pboot.pms.cost.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.bcp.admin.vo.DictKvVo;
import com.gok.components.common.user.PigxUser;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.Util.BaseEntityUtils;
import com.gok.pboot.pms.Util.DbApiUtil;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.common.RedisConstant;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.cost.entity.domain.CostIncomeCalculationDetail;
import com.gok.pboot.pms.cost.entity.domain.CostIncomeSettlement;
import com.gok.pboot.pms.cost.entity.domain.CostIncomeSettlementDetail;
import com.gok.pboot.pms.cost.entity.dto.CostIncomeCalculationDTO;
import com.gok.pboot.pms.cost.entity.dto.CostIncomeSettlementDetailsEditDTO;
import com.gok.pboot.pms.cost.entity.dto.CostIncomeSettlementDetailsImportDTO;
import com.gok.pboot.pms.cost.entity.dto.CostIncomeSettlementListDTO;
import com.gok.pboot.pms.cost.entity.vo.*;
import com.gok.pboot.pms.cost.enums.CostRequestStatusEnum;
import com.gok.pboot.pms.cost.enums.DataSourcesEnum;
import com.gok.pboot.pms.cost.enums.SettlementStatusEnum;
import com.gok.pboot.pms.cost.mapper.CostIncomeCalculationDetailMapper;
import com.gok.pboot.pms.cost.mapper.CostIncomeCalculationMapper;
import com.gok.pboot.pms.cost.mapper.CostIncomeSettlementDetailMapper;
import com.gok.pboot.pms.cost.mapper.CostIncomeSettlementMapper;
import com.gok.pboot.pms.cost.service.ICostIncomeSettlementDetailService;
import com.gok.pboot.pms.cost.service.ICostIncomeSettlementService;
import com.gok.pboot.pms.entity.vo.SysUserDataScopeVO;
import com.gok.pboot.pms.handler.ProjectScopeHandle;
import com.gok.pboot.pms.service.IDictService;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Table;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2025/02/18
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class CostIncomeSettlementDetailServiceImpl extends ServiceImpl<CostIncomeSettlementDetailMapper, CostIncomeSettlementDetail> implements ICostIncomeSettlementDetailService {
    private final CostIncomeSettlementMapper costIncomeSettlementMapper;
    private final ICostIncomeSettlementService incomeSettlementService;
    private final CostIncomeCalculationDetailMapper calculationDetailMapper;
    private final CostIncomeCalculationMapper calculationMapper;

    private final DbApiUtil dbApiUtil;
    private final IDictService idictService;
    private final StringRedisTemplate stringRedisTemplate;
    private final ProjectScopeHandle projectScopeHandle;
    private static final String menuCode = "DELIVERY_HUMAN_RESOURCE_SRJS";
    private static final String CONTENT_TYPE_SHEET = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
    private static final String PARAM_CONTENT_DISPOSITION = "Content-disposition";
    private static final String CONTENT_DISPOSITION = "attachment;filename*=utf-8''";
    private static final String XLSX_SUFFIX = ".xlsx";

    @Override
    public List<CostIncomeSettlementDetailVO> findDetailList(CostIncomeSettlementListDTO dto) {
        //查询数据权限
        SysUserDataScopeVO dataPermission = projectScopeHandle.getDeliverManagementDataPermission(menuCode, dto.getProjectId(), null);
        if (!Boolean.TRUE.equals(dataPermission.getIsAll())) {
            dto.setPurviewNames(dataPermission.getUserNameList());
        }

        List<CostIncomeSettlementDetailVO> detailVOList = baseMapper.selList(dto);
        if (CollectionUtil.isEmpty(detailVOList)) {
            return ListUtil.empty();
        }
        Map<String, String> taxRateMap = idictService.getDictKvList("税率").getData().stream()
                .collect(Collectors.toMap(DictKvVo::getValue, DictKvVo::getName));
        List<Long> requestIds = detailVOList.stream()
                .filter(version -> version != null && version.getRequestId() != null)
                .map(CostIncomeSettlementDetailVO::getRequestId)
                .collect(Collectors.toList());
        List<CostManageVersionVO> requestVersionList = dbApiUtil.getOaRequestStatusToObj(requestIds, CostManageVersionVO.class);
        Map<Long, CostManageVersionVO> requestMap = requestVersionList.stream().collect(Collectors.toMap(CostManageVersionVO::getRequestId, a -> a, (a, b) -> a));
        detailVOList.forEach(e -> {
            e.setTaxRateTxt(Optional.ofNullable(e.getTaxRate()).isPresent() ? taxRateMap.get(e.getTaxRate()) : null);
            e.setOperationSettlementDate(
                    Optional.ofNullable(e.getOperationSettlementDate()).isPresent()
                            ? LocalDateTime.parse(e.getOperationSettlementDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))
                            : null);
            e.setDataSourcesTxt(EnumUtils.getNameByValue(DataSourcesEnum.class, e.getDataSources()));
            e.setApprovalStatus(CostRequestStatusEnum.UNCOMMIT.getValue());
            e.setApprovalStatusTxt(CostRequestStatusEnum.UNCOMMIT.getName());
            if (null != e.getRequestId()) {
                CostManageVersionVO reqVersion = requestMap.getOrDefault(e.getRequestId(), null);
                if (Optional.ofNullable(reqVersion).isPresent()) {
                    Integer requestStatus = reqVersion.getRequestStatus();
                    e.setApprovalStatus(requestStatus);
                    e.setApprovalStatusTxt(CostRequestStatusEnum.getNameByNodeType(requestStatus));
                    e.setRequestNumber(reqVersion.getRequestNumber());
                    e.setFilingTime(CostRequestStatusEnum.FINISH.getCurrentNodeType().equals(requestStatus) ? reqVersion.getFilingTime() : null);
                }
            }
        });
        return detailVOList;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public List<CostIncomeSettlementDetail> addOrUpdate(List<CostIncomeSettlementDetailsEditDTO> dtoList) {
        List<CostIncomeSettlementDetail> costIncomeSettlementDetailList = new ArrayList<>();
        if (CollectionUtil.isEmpty(dtoList)) {
            return ListUtil.empty();
        }
        List<CostIncomeSettlement> costIncomeSettlementList = new ArrayList<>();
        // 获取税率字典
        Map<String, String> taxRateMap = idictService.getDictKvList("税率").getData().stream()
                .collect(Collectors.toMap(DictKvVo::getValue, DictKvVo::getName));
        dtoList.stream().forEach(d -> {
            //税率
            BigDecimal taxRate = Optional.ofNullable(d.getTaxRate()).isPresent() ?
                    new BigDecimal(taxRateMap.getOrDefault(d.getTaxRate(), null).replaceAll("%", "")).divide(new BigDecimal(100)) : BigDecimal.ZERO;
            //结算不含税金额
            BigDecimal budgetAmountExcludingTax = d.getBudgetAmountIncludedTax().divide(BigDecimal.ONE.add(taxRate), 2, BigDecimal.ROUND_HALF_UP);
            d.setBudgetAmountExcludingTax(budgetAmountExcludingTax);
        });
        List<CostIncomeSettlementDetailsEditDTO> updateList = dtoList.stream().filter(dto -> Optional.ofNullable(dto.getId()).isPresent()).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(updateList)) {
            List<Long> detailsIds = updateList.stream().map(CostIncomeSettlementDetailsEditDTO::getId).collect(Collectors.toList());
            Map<Long, CostIncomeSettlementDetail> detailMapById = baseMapper.selectList(Wrappers.<CostIncomeSettlementDetail>lambdaQuery()
                            .in(CostIncomeSettlementDetail::getId, detailsIds))
                    .stream().collect(Collectors.toMap(CostIncomeSettlementDetail::getId, a -> a));
            List<CostIncomeSettlementDetail> updateDetailList = updateList.stream().map(u -> {
                CostIncomeSettlementDetail detail = detailMapById.get(u.getId());
                BeanUtil.copyProperties(u, detail);

                BaseBuildEntityUtil.buildUpdate(detail);
                return detail;
            }).collect(Collectors.toList());
            settlementSummary(updateDetailList, costIncomeSettlementList);
            costIncomeSettlementDetailList.addAll(updateDetailList);
        }
        List<CostIncomeSettlementDetailsEditDTO> addList = dtoList.stream().filter(dto -> !Optional.ofNullable(dto.getId()).isPresent()).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(addList)) {
            // 结算单编号生成规则：识别号+年月日+5位识别号，例：JSD2025021900001；
            String settlementNumber = RedisConstant.SETTLEMENT_NUMBER_PREFIX + LocalDate.now().toString() +
                    String.format("%05d", stringRedisTemplate.opsForValue().increment(RedisConstant.SETTLEMENT_NUMBER));
            List<CostIncomeSettlementDetail> addDetailList = addList.stream().map(u -> {
                CostIncomeSettlementDetail detail = BeanUtil.copyProperties(u, CostIncomeSettlementDetail.class);
                // 结算明细编号生成规则：识别号+年月日+5位识别号，例：JSMX2025021900001；
                String settlementDetailsNumber = RedisConstant.SETTLEMENT_DETAILS_NUMBER_PREFIX + LocalDate.now().toString() +
                        String.format("%05d", stringRedisTemplate.opsForValue().increment(RedisConstant.SETTLEMENT_DETAILS_NUMBER));
                detail.setSettlementDetailsNumber(settlementDetailsNumber);
                detail.setSettlementNumber(settlementNumber);
                BaseBuildEntityUtil.buildInsert(detail);
                return detail;
            }).collect(Collectors.toList());
            settlementSummary(addDetailList, costIncomeSettlementList);
            costIncomeSettlementDetailList.addAll(addDetailList);
        }
        this.saveOrUpdateBatch(costIncomeSettlementDetailList);
        incomeSettlementService.saveOrUpdateBatch(costIncomeSettlementList);
        return costIncomeSettlementDetailList;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void export(HttpServletResponse response, CostIncomeSettlementListDTO dto) {

        try {
            List<Object> allList;
            List<Object> detailList = new ArrayList<>();
            List<CostIncomeSettlementDetailVO> detailVOList = findDetailList(dto);
            List<CostIncomeSettlementVO> settlementList = incomeSettlementService.findList(dto);
            if (CollectionUtil.isNotEmpty(detailVOList)) {
                detailList = BaseEntityUtils.mapCollectionToList(detailVOList, CostIncomeSettlementDetailExportVO::from);
            }
            if (CollectionUtil.isNotEmpty(settlementList)) {
                allList = BaseEntityUtils.mapCollectionToList(settlementList, CostIncomeSettlementExportVO::from);
            } else {
                //初始化表格
                List<CostIncomeSettlementVO> voList = new ArrayList<>();
                voList.add(CostIncomeSettlementVO.builder().build());
                allList = BaseEntityUtils.mapCollectionToList(voList, CostIncomeSettlementExportVO::from);
            }

            WriteSheet sheet1 = EasyExcel.writerSheet(0, "结算_汇总")
                    .head(CostIncomeSettlementExportVO.class)
                    .build();
            WriteSheet sheet2 = EasyExcel.writerSheet(1, "结算_明细")
                    .head(CostIncomeSettlementDetailExportVO.class)
                    .build();

            // 文件名
            String fileName = URLEncoder.encode("收入结算",
                    StandardCharsets.UTF_8.name()
            ).replaceAll("\\+", "%20");

            // 写入响应体信息
            response.setContentType(CONTENT_TYPE_SHEET);
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            response.setHeader(PARAM_CONTENT_DISPOSITION, CONTENT_DISPOSITION + fileName + XLSX_SUFFIX);
            // 设置sheet名
            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).autoCloseStream(Boolean.FALSE).build();
            // 写入excelWriter
            excelWriter.write(allList, sheet1);
            excelWriter.write(detailList, sheet2);
            // 关闭excelWriter
            excelWriter.finish();
            response.flushBuffer();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Override
    public ApiResult<String> importExcel(Long projectId, List<CostIncomeSettlementDetailsImportDTO> importDTOList) {
        List<CostIncomeSettlementDetail> costIncomeSettlementDetailList;
        List<CostIncomeSettlement> costIncomeSettlementList = new ArrayList<>();
        if (CollUtil.isEmpty(importDTOList)) {
            return ApiResult.success("无数据导入");
        }
        StringBuilder error = new StringBuilder();
        AtomicInteger i = new AtomicInteger(2);
        // 校验
        importDTOList.forEach(importDTO -> {
            this.checkImport(importDTO, error, i.getAndIncrement());
        });
        if (error.toString().contains("不")) {
            return ApiResult.failure("导入失败,原因是" + error);
        }
        // 获取税率字典
        Map<String, String> taxRateMap = idictService.getDictKvList("税率").getData().stream()
                .collect(Collectors.toMap(DictKvVo::getName,
                        DictKvVo::getValue));
        List<String> detailsNumbers = importDTOList.stream().map(CostIncomeSettlementDetailsImportDTO::getSettlementDetailsNumber).collect(Collectors.toList());
        Map<String, CostIncomeSettlementDetail> detailMapByDetailsNumber = baseMapper.selectList(Wrappers.<CostIncomeSettlementDetail>lambdaQuery()
                        .in(CostIncomeSettlementDetail::getSettlementDetailsNumber, detailsNumbers))
                .stream().collect(Collectors.toMap(CostIncomeSettlementDetail::getSettlementDetailsNumber, a -> a));
        // 结算单编号生成规则：识别号+年月日+5位识别号，例：JSD2025021900001；
        String settlementNumber = RedisConstant.SETTLEMENT_NUMBER_PREFIX + LocalDate.now().toString() +
                String.format("%05d", stringRedisTemplate.opsForValue().increment(RedisConstant.SETTLEMENT_NUMBER));

        costIncomeSettlementDetailList = importDTOList.stream().map(u -> {
            CostIncomeSettlementDetail detail = new CostIncomeSettlementDetail();
            CostIncomeSettlementDetail existsDetail = detailMapByDetailsNumber.get(u.getSettlementDetailsNumber());
            if (Optional.ofNullable(existsDetail).isPresent()) {
                BeanUtil.copyProperties(existsDetail, detail);
                BeanUtil.copyProperties(u, detail);
                BaseBuildEntityUtil.buildUpdate(detail);
            } else {
                detail = BeanUtil.copyProperties(u, CostIncomeSettlementDetail.class);
                detail.setId(IdWorker.getId());
                detail.setProjectId(projectId);
                // 结算明细编号生成规则：识别号+年月日+5位识别号，例：JSMX2025021900001；
                String settlementDetailsNumber = RedisConstant.SETTLEMENT_DETAILS_NUMBER_PREFIX + LocalDate.now().toString() +
                        String.format("%05d", stringRedisTemplate.opsForValue().increment(RedisConstant.SETTLEMENT_DETAILS_NUMBER));
                detail.setSettlementDetailsNumber(settlementDetailsNumber);
                detail.setSettlementNumber(settlementNumber);
                detail.setDataSources(NumberUtils.INTEGER_ONE);
                BaseBuildEntityUtil.buildInsert(detail);
            }
            //结算含税金额
            BigDecimal budgetAmountIncludedTax = new BigDecimal(u.getBudgetAmountIncludedTax());
            detail.setBudgetAmountIncludedTax(budgetAmountIncludedTax);
            //税率
            detail.setTaxRate(taxRateMap.getOrDefault(u.getTaxRate(), null));
            BigDecimal taxRate = new BigDecimal(u.getTaxRate().replaceAll("%", "")).divide(new BigDecimal(100));
            //结算不含税金额
            BigDecimal budgetAmountExcludingTax = budgetAmountIncludedTax.divide(BigDecimal.ONE.add(taxRate), 2, BigDecimal.ROUND_HALF_UP);
            detail.setBudgetAmountExcludingTax(budgetAmountExcludingTax);

            return detail;
        }).collect(Collectors.toList());
        settlementSummary(costIncomeSettlementDetailList, costIncomeSettlementList);
        this.saveOrUpdateBatch(costIncomeSettlementDetailList);
        incomeSettlementService.saveOrUpdateBatch(costIncomeSettlementList);
        return ApiResult.success("导入成功");
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public ApiResult<String> delete(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return ApiResult.success("无数据删除");
        }
        List<CostIncomeSettlementDetail> detailList = baseMapper
                .selectList(Wrappers.<CostIncomeSettlementDetail>lambdaQuery().in(CostIncomeSettlementDetail::getId, ids));
        Table<String, String, List<CostIncomeSettlementDetail>> detailTable = HashBasedTable.create();
        detailList.stream().forEach(d -> {
            List<CostIncomeSettlementDetail> exists = detailTable.get(d.getSettlementNumber(), d.getTaxRate());
            if (CollectionUtil.isEmpty(exists)) {
                detailTable.put(d.getSettlementNumber(), d.getTaxRate(), Lists.newArrayList(d));
            } else {
                exists.add(d);
            }
        });
        baseMapper.delByIds(ids);
        //删除结算汇总数据
        List<CostIncomeSettlement> incomeSettlementList = costIncomeSettlementMapper.selectList(Wrappers.<CostIncomeSettlement>lambdaQuery()
                .in(CostIncomeSettlement::getSettlementNumber, detailList.stream().map(CostIncomeSettlementDetail::getSettlementNumber).collect(Collectors.toList()))
                .in(CostIncomeSettlement::getTaxRate, detailList.stream().map(CostIncomeSettlementDetail::getTaxRate).collect(Collectors.toList())));
        Table<String, String, CostIncomeSettlement> settlementTable = HashBasedTable.create();
        incomeSettlementList.stream().forEach(d -> {
            settlementTable.put(d.getSettlementNumber(), d.getTaxRate(), d);
        });

        List<CostIncomeSettlement> settlementList = new ArrayList<>();
        detailTable.rowKeySet().stream().forEach(number -> {
            detailTable.columnKeySet().stream().forEach(taxRate -> {
                List<CostIncomeSettlementDetail> numberTaxRateDetailList = detailTable.get(number, taxRate);
                //结算含税金额合计
                BigDecimal budgetAmountIncludedTax = numberTaxRateDetailList.stream()
                        .map(e -> e.getBudgetAmountIncludedTax())
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                //结算不含税金额合计
                BigDecimal budgetAmountExcludingTax = numberTaxRateDetailList.stream()
                        .map(e -> e.getBudgetAmountExcludingTax())
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                CostIncomeSettlement settlement = settlementTable.get(number, taxRate);
                if (NumberUtils.INTEGER_ZERO.equals(budgetAmountIncludedTax.compareTo(settlement.getBudgetAmountIncludedTax()))) {
                    settlement.setDelFlag(NumberUtils.INTEGER_ONE);
                    BaseBuildEntityUtil.buildUpdate(settlement);
                } else {
                    settlement.setBudgetAmountExcludingTax(settlement.getBudgetAmountExcludingTax().subtract(budgetAmountExcludingTax));
                    settlement.setBudgetAmountIncludedTax(settlement.getBudgetAmountIncludedTax().subtract(budgetAmountIncludedTax));
                    BaseBuildEntityUtil.buildUpdate(settlement);
                }
                settlementList.add(settlement);
            });
        });

        if (CollectionUtil.isNotEmpty(settlementList)) {
            costIncomeSettlementMapper.batchUpdate(settlementList);
        }

        //修改收入测算-结算状态
        List<Long> calculationDetailIds = detailList.stream().map(CostIncomeSettlementDetail::getCostIncomeCalculationDetailId).collect(Collectors.toList());

        List<CostIncomeSettlementDetail> noDeleteDetailList = baseMapper.selectList(Wrappers.<CostIncomeSettlementDetail>lambdaQuery()
                .in(CostIncomeSettlementDetail::getCostIncomeCalculationDetailId, calculationDetailIds));
        Map<Long, List<CostIncomeSettlementDetail>> noDeleteDetailMap = noDeleteDetailList.stream().collect(Collectors.groupingBy(CostIncomeSettlementDetail::getCostIncomeCalculationDetailId));

        if (CollectionUtil.isNotEmpty(calculationDetailIds)) {
            List<CostIncomeCalculationDetail> calculationDetailList = CollUtil.emptyIfNull(calculationDetailMapper.selectBatchIds(calculationDetailIds)).stream()
                    .collect(Collectors.toList());
            List<CostIncomeCalculationDetail> updateCalculationDetailList = calculationDetailList.stream()
                    .filter(c -> noDeleteDetailMap.getOrDefault(c.getId(), ListUtil.empty()).size() == 0).collect(Collectors.toList());
            updateCalculationDetailList.forEach(item -> {
                item.setSettlementStatus(SettlementStatusEnum.AWAIT_SETTLEMENT.getValue());
                BaseBuildEntityUtil.buildUpdate(item);
            });
            if (CollectionUtil.isNotEmpty(updateCalculationDetailList)) {
                calculationDetailMapper.batchUpdate(updateCalculationDetailList);
                // 关联对应的汇总数据状态
                PigxUser user = SecurityUtils.getUser();
                Map<Long, Integer> calculationSettlementMap =
                        getCalculationStatusMap(updateCalculationDetailList.stream().map(CostIncomeCalculationDetail::getCalculationId).collect(Collectors.toList()));
                calculationMapper.batchUpdateStatusByDetails(calculationSettlementMap, NumberUtils.INTEGER_ONE, user.getId(), user.getName());
            }
        }

        return ApiResult.success("删除成功");
    }

    /**
     * 结算明细编辑同步修改结算汇总
     *
     * @param detailList
     * @param costIncomeSettlementList
     * @return
     */
    public List<CostIncomeSettlement> settlementSummary(List<CostIncomeSettlementDetail> detailList, List<CostIncomeSettlement> costIncomeSettlementList) {
        //根据结算单号分组
        Map<String, List<CostIncomeSettlementDetail>> detailMapBySettlementNumber = detailList.stream().collect(Collectors.groupingBy(CostIncomeSettlementDetail::getSettlementNumber));
        detailMapBySettlementNumber.keySet().stream().forEach(number -> {
            List<CostIncomeSettlementDetail> detailListBySettlementNumber = detailMapBySettlementNumber.getOrDefault(number, ListUtil.empty());
            if (CollectionUtil.isNotEmpty(detailListBySettlementNumber)) {
                Map<String, List<CostIncomeSettlementDetail>> detailMapByTaxRate = detailListBySettlementNumber.stream().collect(Collectors.groupingBy(CostIncomeSettlementDetail::getTaxRate));
                detailMapByTaxRate.keySet().stream().forEach(taxRate -> {
                    List<CostIncomeSettlementDetail> detailListByTaxRate = detailMapByTaxRate.get(taxRate);
                    CostIncomeSettlementDetail detail = detailListByTaxRate.get(NumberUtils.INTEGER_ZERO);
                    //测算含税金额合计
                    BigDecimal estimatedInclusiveAmountTax = detailListByTaxRate.stream()
                            .map(e -> e.getEstimatedInclusiveAmountTax())
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    //结算不含税金额合计
                    BigDecimal budgetAmountExcludingTax = detailListByTaxRate.stream()
                            .map(e -> e.getBudgetAmountExcludingTax())
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    //结算含税金额合计
                    BigDecimal budgetAmountIncludedTax = detailListByTaxRate.stream()
                            .map(e -> e.getBudgetAmountIncludedTax())
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    //获取最小开始日期
                    List<LocalDate> startDateList = detailListByTaxRate.stream().filter(x -> Optional.ofNullable(x.getStartDate()).isPresent()).map(CostIncomeSettlementDetail::getStartDate)
                            .distinct().collect(Collectors.toList());
                    LocalDate startDate = startDateList.stream().min(LocalDate::compareTo).get();
                    //获取最大结束日期
                    List<LocalDate> endDateList = detailListByTaxRate.stream().filter(x -> Optional.ofNullable(x.getEndDate()).isPresent()).map(CostIncomeSettlementDetail::getEndDate)
                            .distinct().collect(Collectors.toList());
                    LocalDate endDate = endDateList.stream().max(LocalDate::compareTo).get();
                    CostIncomeSettlement settlement = costIncomeSettlementMapper.selectOne(Wrappers.<CostIncomeSettlement>lambdaQuery()
                            .eq(CostIncomeSettlement::getSettlementNumber, number)
                            .eq(CostIncomeSettlement::getTaxRate, taxRate));
                    if (Optional.ofNullable(settlement).isPresent()) {
                        settlement.setStartDate(startDate);
                        settlement.setEndDate(endDate);
                        settlement.setBudgetAmountExcludingTax(budgetAmountExcludingTax);
                        settlement.setBudgetAmountIncludedTax(budgetAmountIncludedTax);
                        settlement.setTaxRate(taxRate);
                        BaseBuildEntityUtil.buildUpdate(settlement);
                        costIncomeSettlementList.add(settlement);
                    } else {
                        CostIncomeSettlement addSettlement = new CostIncomeSettlement();
                        addSettlement.setProjectId(detail.getProjectId());
                        addSettlement.setCostIncomeCalculationId(detail.getCostIncomeCalculationId());
                        addSettlement.setStartDate(startDate);
                        addSettlement.setEndDate(endDate);
                        addSettlement.setEstimatedInclusiveAmountTax(estimatedInclusiveAmountTax);
                        addSettlement.setBudgetAmountExcludingTax(budgetAmountExcludingTax);
                        addSettlement.setTaxRate(taxRate);
                        addSettlement.setBudgetAmountIncludedTax(budgetAmountIncludedTax);
                        addSettlement.setSettlementNumber(number);
                        addSettlement.setId(IdWorker.getId());
                        addSettlement.setDataSources(detail.getDataSources());
                        addSettlement.setOperationSettlementDate(detail.getOperationSettlementDate());
                        BaseBuildEntityUtil.buildInsert(addSettlement);
                        costIncomeSettlementList.add(addSettlement);
                    }
                });
            }
        });
        return costIncomeSettlementList;
    }

    /**
     * 校验导入参数
     *
     * @param dto      {@link CostIncomeSettlementDetailsImportDTO}
     * @param errorAll {@link StringBuilder}
     * @param row      行数
     */
    private void checkImport(CostIncomeSettlementDetailsImportDTO dto,
                             StringBuilder errorAll, Integer row) {
        StringBuilder error = new StringBuilder();
        error.append("第").append(row).append("行: ");
        if (CharSequenceUtil.isBlank(dto.getStartDate())) {
            error.append("结算开始日期不能为空!");
        } else {
            String regex = "^\\d{4}/(\\d{2}|\\d)/(\\d{2}|\\d)$";
            if (!dto.getStartDate().matches(regex)) {
                error.append("结算开始日期不符合日期格式!");
            }
        }
        if (CharSequenceUtil.isBlank(dto.getEndDate())) {
            error.append("结算截止日期不能为空!");
        } else {
            String regex = "^\\d{4}/(\\d{2}|\\d)/(\\d{2}|\\d)$";
            if (!dto.getEndDate().matches(regex)) {
                error.append("结算截止日期不符合日期格式!");
            }
        }
        if (CharSequenceUtil.isBlank(dto.getUserName())) {
            error.append("姓名不能为空!");
        }
        if (CharSequenceUtil.isBlank(dto.getWorkCode())) {
            error.append("工号不能为空!");
        }
        if (CharSequenceUtil.isBlank(dto.getBudgetAmountIncludedTax())) {
            error.append("结算含税金额不能为空!");
        }
        if (CharSequenceUtil.isBlank(dto.getTaxRate())) {
            error.append("税率不能为空!");
        }
        if (CharSequenceUtil.isNotBlank(dto.getSettlementDetailsNumber())) {
            String regex = "JSMX\\d{4}\\d{2}\\d{2}\\d{5}";
            if (!dto.getSettlementDetailsNumber().matches(regex)) {
                error.append("结算明细编号不符合生成规则!");
            }
        }
        if (error.toString().contains("不")) {
            error.append("<br/>");
            errorAll.append(error);
        }
    }


    /**
     * 获取明细对应汇总数据状态集合
     *
     * @param calculationIds 汇总ID集合
     * @return
     */
    public Map<Long, Integer> getCalculationStatusMap(List<Long> calculationIds) {
        List<CostIncomeCalculationDetail> detailList =
                Optional.ofNullable(calculationDetailMapper.findList(CostIncomeCalculationDTO.builder().calculationIds(calculationIds).build()))
                        .orElse(ListUtil.empty());
        Map<Long, Integer> calculationSettlementMap = detailList.stream().collect(Collectors.groupingBy(CostIncomeCalculationDetail::getCalculationId,
                Collectors.mapping(CostIncomeCalculationDetail::getSettlementStatus,
                        Collectors.collectingAndThen(Collectors.toList(), statuses -> {
                                    Set<Integer> uniqueValues = new HashSet<>(statuses);
                                    return uniqueValues.size() == 1 ? uniqueValues.iterator().next() : SettlementStatusEnum.PART_SETTLEMENT.getValue();
                                }
                        ))));
        return calculationSettlementMap;
    }
}
