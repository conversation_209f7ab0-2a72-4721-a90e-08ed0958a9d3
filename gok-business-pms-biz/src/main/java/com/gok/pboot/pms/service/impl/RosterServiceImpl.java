package com.gok.pboot.pms.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.bcp.upms.dto.DeptDetailDto;
import com.gok.bcp.upms.feign.RemoteDeptService;
import com.gok.pboot.common.core.constant.SecurityConstants;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.Util.BaseEntityUtils;
import com.gok.pboot.pms.entity.bo.RosterSelectionBO;
import com.gok.pboot.pms.entity.domain.Roster;
import com.gok.pboot.pms.entity.vo.RosterSelectionVO;
import com.gok.pboot.pms.mapper.RosterMapper;
import com.gok.pboot.pms.service.RosterService;
import com.gok.pboot.service.entity.employee.dto.HrmResourceDTO;
import com.gok.pboot.service.feign.RemoteEhrService;
import com.google.common.collect.ImmutableList;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 花名册服务
 *
 * <AUTHOR>
 * @version 1.3.4
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RosterServiceImpl extends ServiceImpl<RosterMapper, Roster> implements RosterService {

    private final RosterMapper rosterMapper;
    @Resource
    private RemoteDeptService remoteDeptService;
    @Resource
    private RemoteEhrService remoteEhrService;

    @Override
    public List<RosterSelectionVO> findSelectionList(String aliasName) {
        List<RosterSelectionBO> selectionBos;
        List<Long> deptIds;
        List<DeptDetailDto> deptList;
        Map<Long, String> deptIdAndNameMap;

        if (StringUtils.isBlank(aliasName)) {
            return ImmutableList.of();
        }
        selectionBos = rosterMapper.findSelectionByAliasNameLike(aliasName);
        if (selectionBos.isEmpty()) {
            return ImmutableList.of();
        }
        deptIds = selectionBos.stream()
                .map(RosterSelectionBO::getDeptId)
                .distinct()
                .collect(Collectors.toList());
        deptList = remoteDeptService.getDeptLeadersByDeptIds(deptIds).getData();
        if (CollectionUtils.isEmpty(deptList)) {
            log.error("请求中台数据失败");

            return ImmutableList.of();
        }
        deptIdAndNameMap = BaseEntityUtils.mapCollectionToMap(
                deptList,
                DeptDetailDto::getDeptId,
                DeptDetailDto::getName
        );

        return selectionBos.stream()
                .map(bo -> RosterSelectionVO.of(bo, deptIdAndNameMap.getOrDefault(bo.getDeptId(), StringUtils.EMPTY)))
                .collect(Collectors.toList());
    }

    @Override
    public Map<Long, Roster> findUserLeaderMap(Collection<Long> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            return Collections.emptyMap();
        }
        Map<Long, Roster> resMap = new HashMap<>(userIds.size());
        Map<Long, HrmResourceDTO> leaderMap = BaseBuildEntityUtil.apiResult(remoteEhrService.getUserLeaderMap(userIds, SecurityConstants.FROM_IN));
        leaderMap.forEach((userId, leader) ->
                resMap.put(userId, Roster.builder()
                        .id(leader.getId())
                        .aliasName(leader.getAliasName())
                        .startDate(leader.getStartDate())
                        .employeeStatus(leader.getEmployeeStatus())
                        .build()));
        return resMap;
    }

    @Override
    public Map<Long, List<Roster>> findUserUnderlingMap(Collection<Long> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            return Collections.emptyMap();
        }
        List<Roster> rosterList = rosterMapper.selectList(Wrappers.<Roster>lambdaQuery()
                .in(Roster::getLeaderId, userIds));

        return rosterList.stream().collect(Collectors.groupingBy(Roster::getLeaderId));
    }
}
