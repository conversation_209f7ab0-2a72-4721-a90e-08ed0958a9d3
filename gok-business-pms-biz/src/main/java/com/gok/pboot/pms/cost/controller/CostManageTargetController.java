package com.gok.pboot.pms.cost.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.cost.entity.dto.CostManageTargetDTO;
import com.gok.pboot.pms.cost.entity.vo.CostManageTargetVO;
import com.gok.pboot.pms.cost.entity.vo.CostManageTargetVersionIInfoVO;
import com.gok.pboot.pms.cost.service.ICostManageTargetService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>
 * 成本目标管理 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @menu 目标管理
 * @since 2025-01-07
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/costManageTarget")
public class CostManageTargetController {

    private final ICostManageTargetService costManageTargetService;

    /**
     * 添加项目目标信息
     *
     * @param costManageTargetDTO DTO信息
     * @return {@link ApiResult }<{@link String }>
     */
    @PostMapping("/insertCostManageTargetInfo")
    public ApiResult<String> insertCostManageTargetInfo(@RequestBody @Valid CostManageTargetDTO costManageTargetDTO) {
        costManageTargetService.insertCostManageTargetInfo(costManageTargetDTO);
        return ApiResult.success("添加成功");
    }

    /**
     * 查询项目目标信息
     *
     * @param versionId 版本ID
     * @param projectId 项目ID
     * @return {@link ApiResult }<{@link CostManageTargetVO }>
     */
    @GetMapping("/getCostManageTargetInfo")
    public ApiResult<CostManageTargetVO> getCostManageTargetInfo(@RequestParam(name = "versionId", defaultValue = "") Long versionId, @RequestParam("projectId") Long projectId) {
        CostManageTargetVO costManageTarget = costManageTargetService.getCostManageTargetInfo(versionId, projectId);
        return ApiResult.success(costManageTarget);
    }

    /**
     * 获取历史版本数据
     *
     * @param pageRequest 分页参数
     * @param projectId   项目ID
     * @return {@link ApiResult }<{@link Page}<{@link CostManageTargetVersionIInfoVO }>>
     */
    @GetMapping("/getCostManageTargetVersionInfo")
    public ApiResult<Page<CostManageTargetVersionIInfoVO>> getCostManageTargetVersionInfo(PageRequest pageRequest, @RequestParam("projectId") Long projectId) {
        return ApiResult.success(costManageTargetService.getCostManageTargetVersionInfo(pageRequest, projectId));
    }

    /**
     * 同步OA项目台账最新目标内容
     *
     * @return {@link String}
     */
    @PostMapping("/getProjectTargetInfo")
    public ApiResult<String> getProjectTargetInfo() {
        costManageTargetService.getProjectTargetInfo();
        return ApiResult.successMsg("同步OA项目台账最新目标信息成功");
    }

}
