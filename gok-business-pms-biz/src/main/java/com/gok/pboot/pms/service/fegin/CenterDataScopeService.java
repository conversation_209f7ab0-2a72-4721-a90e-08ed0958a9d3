package com.gok.pboot.pms.service.fegin;

import com.gok.pboot.pms.common.base.R;
import com.gok.pboot.pms.common.base.ServiceNameConstants;
import com.gok.pboot.pms.entity.vo.SysUserDataScopeVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 中台用户数据权限服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "centerDataScopeService", value = ServiceNameConstants.BCP_SERVICE)
public interface CenterDataScopeService {

    /**
     * 查询用户数据权限
     * @param clientId 客户端ID
     * @param userId 用户ID
     * @param permCode 权限标识
     * @return {@link R<SysUserDataScopeVO>}
     */
    @GetMapping("/out/role/roleData/getByClientIdAndRoleId")
    R<SysUserDataScopeVO> getByClientIdAndRoleId(
            @RequestParam("clientId") String clientId,
            @RequestParam("userId") Long userId,
            @RequestParam("permCode") String permCode
    );

}
