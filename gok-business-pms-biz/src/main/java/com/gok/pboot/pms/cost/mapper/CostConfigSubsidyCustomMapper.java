package com.gok.pboot.pms.cost.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.cost.entity.domain.CostConfigSubsidyCustom;
import com.gok.pboot.pms.cost.entity.vo.CostConfigSubsidyCustomVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 自定义补贴配置 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Mapper
public interface CostConfigSubsidyCustomMapper extends BaseMapper<CostConfigSubsidyCustom> {

    /**
     * 按版本 ID 获取补贴海关
     *
     * @param versionId 版本 ID
     * @return {@link List }<{@link CostConfigSubsidyCustomVO }>
     */
    List<CostConfigSubsidyCustomVO> getSubsidyCustomsByVersionId(Long versionId);
}
