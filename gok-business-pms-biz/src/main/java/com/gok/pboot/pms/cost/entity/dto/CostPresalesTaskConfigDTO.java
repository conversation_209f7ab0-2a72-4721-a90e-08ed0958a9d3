package com.gok.pboot.pms.cost.entity.dto;

import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.cost.entity.domain.CostPresalesTaskConfig;
import com.gok.pboot.pms.cost.entity.domain.CostTaskCategoryManagement;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 售前报工工单配置DTO
 *
 * <AUTHOR>
 * @date 2025/01/15
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class CostPresalesTaskConfigDTO {

    /**
     * 主键ID
     */
    private Long id;


    /**
     * 父级ID
     */
    private Long parentId;

    /**
     * 工单名称
     */
    @NotBlank(message = "工单名称不能为空")
    @Length(max = 25, message = "工单名称不能超过25个字")
    private String taskName;

    /**
     * 工单类别
     */
    @NotNull(message = "工单类别不能为空")
    private Integer taskCategory;

    /**
     * 工单级别（1一级，2二级，3三级）
     */
    @NotNull(message = "工单级别不能为空")
    private Integer taskLevel;

    /**
     * 工单描述
     */
    @Length(max = 200, message = "工单描述不能超过200个字")
    private String taskDesc;

    /**
     * 拆解类型（0=标准工单，1=总成工单）
     */
    @NotNull(message = "工单拆解类型不能为空")
    private Integer disassemblyType;

    /**
     * 工单负责人类型(0=项目角色，1=指定人)
     */
    @NotNull(message = "工单负责人类型不能为空")
    private Integer managerType;

    /**
     * 工单负责人角色(0=售前经理、1=项目经理、2=客户经理)
     */
    private Integer managerRole;

    /**
     * 工单负责人id
     */
    private Long managerId;

    /**
     * 工单负责人姓名
     */
    private String managerName;

    /**
     * 子工单列表
     */
    private List<CostPresalesTaskConfigDTO> children;

    public static CostPresalesTaskConfig buildDto(CostPresalesTaskConfigDTO dto, CostPresalesTaskConfig parentTaskConfig) {
        return BaseBuildEntityUtil.buildSave(parentTaskConfig)
                .setParentId(dto.getParentId())
                .setTaskName(dto.getTaskName())
                .setTaskCategory(dto.getTaskCategory())
                .setTaskLevel(dto.getTaskLevel())
                .setTaskDesc(dto.getTaskDesc())
                .setDisassemblyType(dto.getDisassemblyType())
                .setManagerType(dto.getManagerType())
                .setManagerRole(dto.getManagerRole())
                .setManagerId(dto.getManagerId())
                .setManagerName(dto.getManagerName());
    }
} 