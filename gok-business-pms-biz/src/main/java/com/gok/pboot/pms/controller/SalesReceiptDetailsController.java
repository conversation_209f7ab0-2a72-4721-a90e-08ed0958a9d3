package com.gok.pboot.pms.controller;

import com.gok.pboot.pms.common.base.R;
import com.gok.pboot.pms.entity.vo.SalesReceiptDetailsVO;
import com.gok.pboot.pms.service.ISalesReceiptDetailsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 销售收款明细
 *
 * <AUTHOR>
 * @menu 销售收款-催收计划
 * @since 2023-09-27
 */
@RestController
@RequestMapping("/sales-receipt-details")
@RequiredArgsConstructor
@Api(tags = "销售收款明细")
public class SalesReceiptDetailsController {

    private final ISalesReceiptDetailsService service;

    /**
     * 详情列表
     *
     * @param id id
     * @param contractId 合同id
     * @return detail
     */
    @GetMapping("/detail")
    @ApiOperation(value = "详情列表", notes = "详情列表")
    public R<SalesReceiptDetailsVO> detail(@RequestParam("id") Long id, @RequestParam("contractId") Long contractId){
        return R.ok(service.salesReceiptDetailList(id,contractId));
    }

}
