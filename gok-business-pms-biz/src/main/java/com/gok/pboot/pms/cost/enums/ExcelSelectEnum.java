package com.gok.pboot.pms.cost.enums;

import com.gok.pboot.pms.enumeration.ValueEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * excel select 枚举
 *
 * <AUTHOR>
 * @date 2024/12/04
 */
@Getter
@AllArgsConstructor
public enum ExcelSelectEnum {

    /**
     * 没有
     */
    NONE(null, null),
    /**
     * 报价方式
     */
    QUOTATION_TYPE(QuotationTypeEnum.class, null),
    /**
     * 人员属性
     */
    PERSON_ATTRIBUTES(PersonnelAttributeEnum.class, null),
    ;

    private final Class<?> enumClass;

    private final String[] source;

    public String[] getSource() {
        if (source != null) {
            return source;
        }
        if (enumClass != null) {
            ValueEnum[] values = (ValueEnum[]) enumClass.getEnumConstants();
            String[] result = new String[values.length];
            for (int i = 0; i < values.length; i++) {
                result[i] = values[i].getName();
            }
            return result;
        }


        return new String[0];
    }

}
