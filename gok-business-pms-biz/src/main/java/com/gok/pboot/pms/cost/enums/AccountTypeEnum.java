package com.gok.pboot.pms.cost.enums;

import com.gok.pboot.pms.enumeration.ValueEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 成本科目类型枚举类
 *
 * <AUTHOR>
 * @create 2025/01/07
 **/
@Getter
@AllArgsConstructor
public enum AccountTypeEnum implements ValueEnum<Integer> {

    /**
     * 差旅住宿
     */
    CLZS(0, "差旅住宿"),

    /**
     * 差旅补贴
     */
    CLBT(1, "差旅补贴"),

    /**
     * 自定义补贴
     */
    ZDYBT(2, "自定义补贴");

    private final Integer value;

    private final String name;

}
