package com.gok.pboot.pms.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.entity.domain.ProjectMeeting;
import com.gok.pboot.pms.entity.dto.ProjectMeetingDTO;
import com.gok.pboot.pms.entity.dto.ProjectMeetingImportExcelDTO;
import com.gok.pboot.pms.entity.vo.ProjectMeetingExportExcelVO;
import com.gok.pboot.pms.entity.vo.ProjectMeetingFindPageVO;
import com.gok.pboot.pms.entity.vo.ProjectMeetingVO;
import org.springframework.validation.BindingResult;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 项目会议纪要表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-13
 **/
public interface IProjectMeetingService {

    /**
     * 项目会议纪要分页查询
     *
     * @param pageRequest 分页请求对象
     * @param filter      查询参数
     * @return {@link Page}{@link ProjectMeetingFindPageVO}
     */
    Page<ProjectMeetingFindPageVO> findPageList(PageRequest pageRequest, Map<String, Object> filter);

    /**
     * 根据主键id获取会议详情
     *
     * @param id 项目会议纪要主键id
     * @return {@link ProjectMeetingVO}
     */
    ProjectMeetingVO getById(Long id);

    /**
     * 根据id查询获取对应实体类信息
     *
     * @param id 主键id
     * @return 实体类信息，未查询到则返回null
     */
    ProjectMeeting findById(Long id);

    /**
     * 新增项目会议纪要
     *
     * @param request 新增请求
     * @return 新增会议纪要主键id
     */
    Long save(ProjectMeetingDTO request);

    /**
     * 编辑项目会议纪要
     *
     * @param request 编辑请求
     * @return 编辑会议纪要主键id
     */
    Long update(ProjectMeetingDTO request);

    /**
     * 批量逻辑删除
     *
     * @param list 要删除的id集合[]
     * @return 被删除的id集合[]
     */
    List<Long> batchDel(List<Long> list);

    /**
     * 导出项目会议纪要
     *
     * @param pageRequest 分页请求对象
     * @param filter      查询参数
     * @return 导出所需数据集合
     */
    List<ProjectMeetingExportExcelVO> export(PageRequest pageRequest, Map<String, Object> filter);

    /**
     * 从Excel文件导入项目会议纪要
     *
     * @param projectId    项目id
     * @param excelDtoList excel文件解析数据集合
     * @param bindingResult 参数校验失败信息
     * @return {@link ApiResult}
     */
    ApiResult importByExcel(Long projectId, List<ProjectMeetingImportExcelDTO> excelDtoList, BindingResult bindingResult);

}
