package com.gok.pboot.pms.cost.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.cost.entity.domain.CostConfigTravelStay;
import com.gok.pboot.pms.cost.entity.domain.CostConfigVersion;
import com.gok.pboot.pms.cost.entity.dto.CostConfigTravelStayDTO;
import com.gok.pboot.pms.cost.entity.vo.CostConfigTravelStayVO;
import com.gok.pboot.pms.cost.enums.CostConfigVersionTypeEnum;
import com.gok.pboot.pms.cost.mapper.CostConfigTravelStayMapper;
import com.gok.pboot.pms.cost.service.ICostConfigTravelStayService;
import com.gok.pboot.pms.cost.service.ICostConfigVersionService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 差旅住宿标准配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Service
@AllArgsConstructor
public class CostConfigTravelStayServiceImpl extends ServiceImpl<CostConfigTravelStayMapper, CostConfigTravelStay> implements ICostConfigTravelStayService {

    public static final String STRING = "-1";
    private final ICostConfigVersionService costConfigVersionService;

    /**
     * 获取 差旅住宿标准配置
     *
     * @return {@link List }<{@link CostConfigTravelStayVO }>
     */
    @Override
    public List<CostConfigTravelStayVO> getCostConfigTravelStayList() {
        // 获取当前最大版本号
        CostConfigVersion crrMaxVersion = costConfigVersionService.getCrrCostConfigVersion(CostConfigVersionTypeEnum.CLZSBZ);
        // 获取最大版本号下的差旅住宿标准配置数据
        List<CostConfigTravelStayVO> configTravelStayVoList = Objects.nonNull(crrMaxVersion)
                ? baseMapper.getTravelStaysByVersionId(crrMaxVersion.getId())
                : Collections.emptyList();
        return handleCityNameToList(configTravelStayVoList);
    }

    /**
     * 编辑 差旅住宿标准配置
     *
     * @param dtoList DTO 列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editCostConfigTravelStayList(List<CostConfigTravelStayDTO> dtoList) {
        if (CollUtil.isEmpty(dtoList)){
            return;
        }
        // 获取最新版本名称
        Long latestVersionId = costConfigVersionService.generateVersionName(CostConfigVersionTypeEnum.CLZSBZ);
        List<CostConfigTravelStay> configTravelStayList = dtoList.stream()
                .map(item -> BaseBuildEntityUtil.buildInsert(new CostConfigTravelStay()
                        .setVersionId(latestVersionId)
                        .setCityStandards(item.getCityStandards())
                        .setDirectorAbovePrice(item.getDirectorAbovePrice())
                        .setDirectorBelowPrice(item.getDirectorBelowPrice())
                        .setGeneralOfficePrice(item.getGeneralOfficePrice())
                        .setCityIds(item.getCityIds())))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(configTravelStayList)){
            this.saveBatch(configTravelStayList);
        }
    }

    /**
     * 根据版本id获取差旅住宿标准配置
     *
     * @param versionId 版本 ID
     * @return {@link List }<{@link CostConfigTravelStayVO }>
     */
    @Override
    public List<CostConfigTravelStayVO> getCostConfigTravelStaysByVersionId(Long versionId) {
        List<CostConfigTravelStayVO> configTravelStayVoList = baseMapper.getTravelStaysByVersionId(versionId);
        if (CollUtil.isEmpty(configTravelStayVoList)){
            return Collections.emptyList();
        }
        return handleCityNameToList(configTravelStayVoList);
    }

    /**
     * 处理城市名称 获取差旅住宿标准配置数据
     *
     * @param configTravelStayVoList 配置 Travel Stay VO 列表
     * @return {@link List }<{@link CostConfigTravelStayVO }>
     */
    private List<CostConfigTravelStayVO> handleCityNameToList(List<CostConfigTravelStayVO> configTravelStayVoList) {
        if (CollUtil.isEmpty(configTravelStayVoList)){
            return Collections.emptyList();
        }
        return configTravelStayVoList.stream()
                .peek(item -> {
                    String cityIds = item.getCityIds();
                    String cityNames = StrUtil.isNotBlank(cityIds) ? getCityNames(cityIds) : StrUtil.EMPTY;
                    item.setCityName(cityNames);
                }).collect(Collectors.toList());
    }

    /**
     * 获取城市名称字符串
     *
     * @param cityIds 城市 ID
     * @return {@link String }
     */
    private static String getCityNames(String cityIds) {
        if (STRING.equals(cityIds)){
            return "除以上外";
        }
        return cityIds;
    }

}
