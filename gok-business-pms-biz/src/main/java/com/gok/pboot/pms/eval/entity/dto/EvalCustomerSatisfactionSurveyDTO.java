package com.gok.pboot.pms.eval.entity.dto;

import com.gok.pboot.pms.eval.enums.EvalSatisfactionResultEnum;
import com.gok.pboot.pms.eval.enums.EvalSatisfactionSurveyStatusEnum;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

/**
 * 客户满意度调查DTO
 *
 * <AUTHOR>
 * @date 2025/05/08
 */
@Data
@ApiModel("客户满意度调查DTO")
public class EvalCustomerSatisfactionSurveyDTO {

    /**
     * ID
     */
    private Long id;

    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空")
    private Long projectId;

    /**
     * 问题响应结果（5=非常满意,4=满意,3=一般,2=不满意,1=非常不满意,0=未提供服务）
     * @see EvalSatisfactionResultEnum
     */
    @NotNull(message = "问题响应结果不能为空")
    private Integer problemResponseResult;

    /**
     * 方案设计结果
     * @see EvalSatisfactionResultEnum
     */
    @NotNull(message = "方案设计结果不能为空")
    private Integer designSchemeResult;

    /**
     * 进度控制结果
     * @see EvalSatisfactionResultEnum
     */
    @NotNull(message = "进度控制结果不能为空")
    private Integer progressControlResult;

    /**
     * 质量控制结果
     * @see EvalSatisfactionResultEnum
     */
    @NotNull(message = "质量控制结果不能为空")
    private Integer qualityControlResult;

    /**
     * 服务态度与沟通结果
     * @see EvalSatisfactionResultEnum
     */
    @NotNull(message = "服务态度与沟通结果不能为空")
    private Integer serviceAttitudeCommunicationResult;

    /**
     * 其他建议
     */
    @Length(max = 10000, message = "其他建议长度不能超过10000")
    private String otherSuggestion;
} 