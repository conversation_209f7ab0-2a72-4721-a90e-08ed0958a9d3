package com.gok.pboot.pms.service.processor;

import org.springframework.data.util.Pair;

import javax.annotation.Nullable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collection;
import java.util.Map;

/**
 * 饱和度相关计算器
 *
 * <AUTHOR>
 * @version 1.1.0
 */
public interface SaturationCalculator {

    default Map<Long, Pair<BigDecimal, BigDecimal>> calcAttendance(LocalDate startDate, LocalDate endDate) {
        return calcAttendance(startDate, endDate, null);
    }

    /**
     * CALC出勤天数
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param userIds   用户 ID
     * @return 用户ID,(出勤天数,工资核算出勤天数)
     */
    Map<Long, Pair<BigDecimal, BigDecimal>> calcAttendance(LocalDate startDate, LocalDate endDate, @Nullable Collection<Long> userIds);

    /**
     * 用于计算饱和度的出勤天数
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param userIds   用户 ID
     * @return 用户ID,(出勤天数,工资核算出勤天数)
     */
    Map<Long, Pair<BigDecimal, BigDecimal>> saturationAttendance(LocalDate startDate, LocalDate endDate, @Nullable Collection<Long> userIds);

    /**
     *  预测出勤天数
     *
     * @param dateRanges 日期范围
     * @return {@link BigDecimal }
     */
    BigDecimal calcAttendanceDaysByHoliday(Collection<Pair<LocalDate, LocalDate>> dateRanges);
}
