package com.gok.pboot.pms.cost.entity.vo;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 收入结算导出VO
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CostIncomeSettlementExportVO {

    /**
     * 结算开始日期
     */
    @ExcelProperty("结算开始日期")
    private String startDate;

    /**
     * 结算截止日期
     */
    @ExcelProperty("结算截止日期")
    private String endDate;

    /**
     * 测算含税金额
     */
    @ExcelProperty("测算含税金额")
    private BigDecimal estimatedInclusiveAmountTax;

    /**
     * 结算含税金额
     */
    @ExcelProperty("结算含税金额")
    private BigDecimal budgetAmountIncludedTax;

    /**
     * 税率OA字典Txt
     */
    @ExcelProperty("税率")
    private String taxRateTxt;

    /**
     * 结算不含税金额
     */
    @ExcelProperty("结算不含税金额")
    private BigDecimal budgetAmountExcludingTax;

    /**
     * 备注说明
     */
    @ExcelProperty("备注说明")
    private String remarksDesc;

    /**
     * 归档日期
     */
    @ExcelProperty("归档日期")
    private String filingTime;

    /**
     * OA结算单编号
     */
    @ExcelProperty("OA结算单编号")
    private String requestNumber;

    /**
     * 结算单编号
     */
    @ExcelProperty("结算单编号")
    private String settlementNumber;

    /**
     * 数据来源Txt
     */
    @ExcelProperty("数据来源")
    private String dataSourcesTxt;

    /**
     * 审批状态Txt
     */
    @ExcelProperty("审批状态")
    private String approvalStatusTxt;

    public static CostIncomeSettlementExportVO from(CostIncomeSettlementVO detailVO){
        CostIncomeSettlementExportVO vo = new CostIncomeSettlementExportVO();
        BeanUtil.copyProperties(detailVO,vo);
        vo.setStartDate(detailVO.getStartDate().toString());
        vo.setEndDate(detailVO.getEndDate().toString());
        return vo;
    }
}
