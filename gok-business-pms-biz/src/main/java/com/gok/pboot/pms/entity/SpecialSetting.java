package com.gok.pboot.pms.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * - 工时特殊配置 -
 * 拥有该配置的用户不纳入异常日报统计
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(SpecialSetting.ALIAS)
@AllArgsConstructor
@NoArgsConstructor
public class SpecialSetting extends BeanEntity<Long> {

    public static final String ALIAS = "mhour_special_setting";

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 一级部门ID
     */
    private Long topDeptId;

    public static SpecialSetting from(Long userId, Long topDeptId){
        return new SpecialSetting(userId, topDeptId);
    }
}
