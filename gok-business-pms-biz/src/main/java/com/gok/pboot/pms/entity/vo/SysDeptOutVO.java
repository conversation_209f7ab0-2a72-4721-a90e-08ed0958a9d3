/*
 *
 *      Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the pig4cloud.com developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: lengleng (<EMAIL>)
 *
 */

package com.gok.pboot.pms.entity.vo;

import com.gok.pboot.pms.entity.SysDept;
import com.gok.pboot.pms.enumeration.DelFlag;
import com.google.common.base.Strings;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <p>
 * 部门管理
 * </p>
 *
 * <AUTHOR>
 * @since 2018-01-22
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SysDeptOutVO {
    private static final long serialVersionUID = 1L;

    private Long deptId;

    /**
     * 组织类型id
     */
    private Long deptCatId;

    /**
     * 部门名称
     */
    private String name;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 父级部门id
     */
    private Long parentId;
    /**
     * 级别
     */
    private Integer level;

    public SysDept toSysDept() {
        SysDept result = new SysDept();

        result.setDeptId(this.deptId);
        result.setParentId(this.parentId);
        result.setDelFlag(DelFlag.NORMAL.getValue().toString());
        result.setSortOrder(this.sortOrder);
        result.setCreateTime(this.createTime);
        result.setUpdateTime(this.updateTime);
        result.setName(Strings.nullToEmpty(this.name));

        return result;
    }

}
