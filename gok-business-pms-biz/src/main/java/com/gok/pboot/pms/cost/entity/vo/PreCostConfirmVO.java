package com.gok.pboot.pms.cost.entity.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2025/01/08
 * description: 售前成本确认查询VO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class PreCostConfirmVO {

    /**
     * 成本管理id
     */
    private Long id;

    /**
     * 当前状态
     */
    private Integer status;

    /**
     * 当前状态中文值
     */
    private String statusName;

    /**
     * 提交时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp ctime;

    /**
     * 确认时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp mtime;

    /**
     * 创建人
     */
    @TableField(exist = true)
    private String creator;

    /**
     * 确认人
     */
    private String operatorName;

    /**
     * 拒绝理由
     */
    private String refuseReason;

    /**
     * 客户经理id
     */
    private Long salesmanUserId;

    /**
     * 当前用户id
     */
    private Long userId;
}
