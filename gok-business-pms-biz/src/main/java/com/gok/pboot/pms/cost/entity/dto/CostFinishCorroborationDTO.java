package com.gok.pboot.pms.cost.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import java.math.BigDecimal;

/**
 * 完成佐证 DTO
 *
 * <AUTHOR>
 * @date 2025/01/14
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class CostFinishCorroborationDTO {


    /**
     * 交付说明
     */
    @Length(max = 500, message = "交付说明不能超过500个字")
    private String deliveryDesc;

    /**
     * 佐证附件id 多个逗号隔开
     */
    private String completeFiles;

    /**
     * 工作日加班
     */
    @DecimalMin(value = "0.00", message = "工作日加班时长不能小于0")
    @DecimalMax(value = "50.00", message = "工作日加班时长不能大于50小时")
    private BigDecimal workOverTimeHours;

    /**
     * 休息日加班
     */
    @DecimalMin(value = "0.00", message = "休息日加班时长不能小于0")
    @DecimalMax(value = "50.00", message = "休息日加班时长不能大于50小时")
    private BigDecimal restOverTimeHours;

    /**
     * 节假日加班
     */
    @DecimalMin(value = "0.00", message = "节假日时长不能小于0")
    @DecimalMax(value = "50.00", message = "节假日加班时长不能大于50小时")
    private BigDecimal holidayOverTimeHours;

    /**
     * 正常工时
     */
    private BigDecimal normalHours;

}
