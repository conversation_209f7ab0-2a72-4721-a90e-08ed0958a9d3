package com.gok.pboot.pms.service;

import com.gok.pboot.pms.enumeration.ProjectOperationModuleEnum;

import java.util.Collection;
import java.util.Map;

/**
 * 项目状态流转服务
 *
 * <AUTHOR>
 * @date 2025/07/02
 */
public interface IProjectStatusService {
    /**
     * 是否满足进入项目质保期的条件
     *
     * @param projectId 项目id
     * @return boolean
     */
    boolean isEnterProjectWarrantyPeriod(Long projectId);


    /**
     * 是否满足项目关闭的条件
     *
     * @param projectId 项目id
     * @return boolean
     */
    boolean isCloseProject(Long projectId);


    /**
     * 项目操作提醒查询
     *
     * @param projectId 项目id
     * @return {@link Map }<{@link String }, {@link Object }>
     */
    Map<String, Object> projectOperateRemind(Long projectId);


    /**
     * 项目状态流转消息提醒
     */
    void statusTransferRemind();

    void statusTransferCarbonCopy(Long projectId, Collection<Long> userIds, ProjectOperationModuleEnum moduleEnum);
}
