package com.gok.pboot.pms.mapper;

import com.gok.pboot.pms.entity.Holiday;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.util.Pair;

import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 非工作日 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-23
 */
@Mapper
public interface HolidayMapper {

    /**
     * 年份删除
     *
     * @param year
     * @return
     */
    int deleteByYear(@Param("year") String year);

    /**
     * 批量插入
     *
     * @param poList 实体集合
     */
    void batchSave(@Param("poList") List<Holiday> poList);

    /**
     * ~ 查询最接近指定日期的工作日 ~
     *
     * @param date date
     * @return java.time.LocalDate
     * <AUTHOR>
     * @date 2022/9/5 11:01
     */
    LocalDate findWorkDayClosestByDate(LocalDate date);

    /**
     * ~ 检查日期是否存在于表中 ~
     *
     * @param date 日期
     * @return boolean
     * <AUTHOR>
     * @date 2022/9/6 14:05
     */
    boolean exists(LocalDate date);

    /**
     * 查询是否非工作日
     *
     * @param date 日期
     * @return bool
     */
    Boolean ifHoliday(LocalDate date);

    /**
     * 查询是否普通休息日
     *
     * @param date 日期
     * @return bool
     */
    Boolean ifGeneralHoliday(LocalDate date);

    /**
     * 查询是否法定节假日
     *
     * @param date 日期
     * @return bool
     */
    Boolean ifLegalHoliday(LocalDate date);

    /**
     * 根据类型查询
     *
     * @param date 日期
     * @param type 0-普通休息日 1-法定节假日 null-全部假期
     * @return 日期数量
     */
    Boolean ifHolidayByType(@Param("date") LocalDate date, @Param("type") Integer type);

    /**
     * 查询时间范围内的非工作日数量
     *
     * @param startDate 开始日期
     * @param endDate   截止日期
     * @return 非工作日数量
     */
    Integer selectCountByDate(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * 查询时间范围内的普通休息日数量
     *
     * @param startDate 开始日期
     * @param endDate   截止日期
     * @return 普通休息日数量
     */
    Integer selectGeneralHolidayCountByDate(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * 查询时间范围内的法定节假日数量
     *
     * @param startDate 开始日期
     * @param endDate   截止日期
     * @return 法定节假日数量
     */
    Integer selectLegalHolidayCountByDate(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * 根据类型查询时间范围内的休息日数量
     *
     * @param startDate 开始日期
     * @param endDate   截止日期
     * @param type      0-普通休息日 1-法定节假日 null-全部假期
     * @return 休息日数量
     */
    Integer selectHolidayCountByDate(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate, @Param("type") Integer type);

    /**
     * 根据时间段列表查询非工作日数量
     *
     * @param datePairs 时间段列表
     * @return 非工作日数量
     */
    Integer selectCountByDatePairs(@Param("datePairs") Collection<Pair<LocalDate, LocalDate>> datePairs);

    /**
     * 根据时间段列表查询普通休息日数量
     *
     * @param datePairs 时间段列表
     * @return 普通休息日数量
     */
    Integer selectGeneralHolidayCountByDatePairs(@Param("datePairs") Collection<Pair<LocalDate, LocalDate>> datePairs);

    /**
     * 根据时间段列表查询法定节假日数量
     *
     * @param datePairs 时间段列表
     * @return 法定节假日数量
     */
    Integer selectLegalHolidayCountByDatePairs(@Param("datePairs") Collection<Pair<LocalDate, LocalDate>> datePairs);

    /**
     * 根据时间段列表查询数量
     *
     * @param datePairs 时间段列表
     * @param type      0-普通休息日 1-法定节假日 null-全部假期
     * @return 数量
     */
    Integer selectHolidayCountByDatePairs(@Param("datePairs") Collection<Pair<LocalDate, LocalDate>> datePairs, @Param("type") Integer type);


    /**
     * 根据日期范围查询所有非工作日（包括普通休息日和法定节假日）
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 假期数据列表
     */
    Set<LocalDate> findByDateRange(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * 根据日期范围查询所有普通休息日
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 假期数据列表
     */
    Set<LocalDate> findGeneralHolidayByDateRange(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * 根据日期范围查询法定节假日
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 假期日期列表
     */
    Set<LocalDate> findLegalHolidayByDateRange(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * 根据类型和日期范围查询休息日
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param type      0-普通休息日 1-法定节假日 null-全部假期
     * @return 休息日列表
     */
    Set<LocalDate> findHolidayByDateRange(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate, @Param("type") Integer type);

    /**
     * 查询所有日期数据
     *
     * @return 日期列表
     */
    Set<LocalDate> findAll();

    /**
     * 根据日期查询假期信息
     *
     * @param submissionDate
     * @return
     */
    Holiday selByDate(@Param("dayDate") LocalDate submissionDate);

    /**
     * 根据日期范围查询所有非工作日（包括普通休息日和法定节假日）
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 假期数据列表
     */
    List<Holiday> selByDateRange(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * 查询节假日日期数据
     *
     * @return 日期列表
     */
    List<Holiday> findHolidayList();


    /**
     * 查询假期类型
     *
     * @param date 日期
     * @return 0-普通休息日 1-法定节假日 null-工作日
     */
    Integer findHolidayType(LocalDate date);

    /**
     * 根据日期批量获取假期对象
     *
     * @param dates 日期列表
     * @return 假期对象列表
     */
    List<Holiday> findBatchHolidayType(@Param("dates") Collection<LocalDate> dates);
}
