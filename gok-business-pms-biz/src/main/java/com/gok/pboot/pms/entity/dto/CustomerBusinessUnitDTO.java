package com.gok.pboot.pms.entity.dto;

import com.gok.pboot.pms.entity.CustomerBusinessUnit;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
* <p>
* 客户经营单元-客户组成(关联客户)RO
* </p>
*
* <AUTHOR>
* @since 2024-10-12
*/
@Data
@Accessors(chain = true)
@ApiModel(value = "CustomerBusinessUnitURO对象", description = "客户经营单元-客户组成(关联客户)")
public class CustomerBusinessUnitDTO {

    /**
     * 客户列表
     */
    private List<CustomerBusinessUnit> units;

    /**
     * id列表
     */
    private List<Long> ids;

    /**
     * 来源（1从经营单元创建所属客户，2从所属客户创建客户）
     */
    private  Integer source;
}
