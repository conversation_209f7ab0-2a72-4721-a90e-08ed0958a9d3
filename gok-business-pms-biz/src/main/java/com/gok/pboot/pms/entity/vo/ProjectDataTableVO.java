package com.gok.pboot.pms.entity.vo;

import cn.hutool.core.util.StrUtil;
import com.gok.pboot.pms.Util.DecimalFormatUtil;
import com.gok.pboot.pms.entity.domain.ProjectData;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 项目核心数据表格数据
 *
 * <AUTHOR>
 * @date 2024-07-12
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ProjectDataTableVO {

    /**
     * 表名
     */
    private String tableTitle;

    /**
     * 收入总额(不含税)
     */
    private String srzebhs;

    /**
     * 成本总额(不含税)
     */
    private String cbzebhs;

    /**
     * 毛利额
     */
    private String mle;

    /**
     * 毛利率
     */
    private String mll;

    /**
     * 列表排序字段
     */
    private Integer sort;

    /**
     * ProjectData转换ProjectDataBudgetTableVO类
     *
     * @param po 项目核心数据实体类
     * @return
     */
    public static List<ProjectDataTableVO> of(ProjectData po) {
        List<ProjectDataTableVO> result = new ArrayList<>(2);
        DecimalFormat decimalFormat = new DecimalFormat("##,##0.00");

        // 毛利测算数据处理
        BigDecimal secondTotalBudgetRevenue = po.getSecondTotalBudgetRevenue();
        BigDecimal secondTotalBudgetCost = po.getSecondTotalBudgetCost();
        BigDecimal secondEstimatedGrossProfit = po.getSecondEstimatedGrossProfit();

        ProjectDataTableVO grossTableVo = new ProjectDataTableVO();
        //grossTableVo.setTableTitle("项目毛利测算");
        grossTableVo.setTableTitle("商业论证A表");
        grossTableVo.setSrzebhs(secondTotalBudgetRevenue == null ? "0.00" : decimalFormat.format(secondTotalBudgetRevenue));
        grossTableVo.setCbzebhs(secondTotalBudgetCost == null ? "0.00" : decimalFormat.format(secondTotalBudgetCost));
        grossTableVo.setMle(secondEstimatedGrossProfit == null ? "0.00" : decimalFormat.format(secondEstimatedGrossProfit));
        BigDecimal secondEstimatedGrossProfitRate = secondEstimatedGrossProfit != null
                && secondTotalBudgetRevenue != null
                && secondTotalBudgetRevenue.compareTo(BigDecimal.ZERO) != 0
                ? secondEstimatedGrossProfit.divide(secondTotalBudgetRevenue, 4, RoundingMode.HALF_UP)
                : null;
        String secondEstimatedGrossProfitRateStr = null;

        if (isShow(secondTotalBudgetRevenue, secondTotalBudgetCost, secondEstimatedGrossProfit, secondEstimatedGrossProfitRate)) {
            if (Optional.ofNullable(secondEstimatedGrossProfitRate).isPresent()) {
                secondEstimatedGrossProfitRateStr = secondEstimatedGrossProfitRate.multiply(BigDecimal.valueOf(100)).toString();
                secondEstimatedGrossProfitRateStr = DecimalFormatUtil.setAndValidate(secondEstimatedGrossProfitRateStr, 2, RoundingMode.DOWN, decimalFormat);
                if (StrUtil.isNotBlank(secondEstimatedGrossProfitRateStr)) {
                    secondEstimatedGrossProfitRateStr = secondEstimatedGrossProfitRateStr + "%";
                }
            }
            grossTableVo.setMll(secondEstimatedGrossProfitRateStr);
            grossTableVo.setSort(0);
            result.add(grossTableVo);
        }

        // A表数据处理
        BigDecimal abygsrzeBhs = po.getAbygsrzeBhs();
        BigDecimal abygcbzeBhs = po.getAbygcbzeBhs();
        BigDecimal abygmle = po.getAbygmle();

        ProjectDataTableVO aTableVo = new ProjectDataTableVO();
        aTableVo.setTableTitle("商业论证A表");
        aTableVo.setSrzebhs(abygsrzeBhs == null ? "0.00" : decimalFormat.format(abygsrzeBhs));
        aTableVo.setCbzebhs(abygcbzeBhs == null ? "0.00" : decimalFormat.format(abygcbzeBhs));
        aTableVo.setMle(abygmle == null ? "0.00" : decimalFormat.format(abygmle));
        BigDecimal abygmll = abygmle != null
                && abygsrzeBhs != null
                && abygsrzeBhs.compareTo(BigDecimal.ZERO) != 0 ? abygmle.divide(abygsrzeBhs, 4, RoundingMode.HALF_UP) : null;

        if (isShow(abygsrzeBhs, abygcbzeBhs, abygmle, abygmll)) {
            String abygmllStr = null;
            if (Optional.ofNullable(abygmll).isPresent()) {
                abygmllStr = abygmll.multiply(BigDecimal.valueOf(100)).toString();
                abygmllStr = DecimalFormatUtil.setAndValidate(abygmllStr, 2, RoundingMode.DOWN, decimalFormat);
                if (StrUtil.isNotBlank(abygmllStr)) {
                    abygmllStr = abygmllStr + "%";
                }
            }
            aTableVo.setMll(abygmllStr);
            aTableVo.setSort(1);
            result.add(aTableVo);
        }

        // B表数据处理
        BigDecimal bTableSrzebhs = po.getTotalBudgetRevenue();
        BigDecimal bTableCbzebhs = po.getTotalBudgetCost();
        BigDecimal bTableMle = po.getEstimatedGrossProfit();

        ProjectDataTableVO bTableVo = new ProjectDataTableVO();
        bTableVo.setTableTitle("项目立项B表");
        bTableVo.setSrzebhs(bTableSrzebhs == null ? "0.00" : decimalFormat.format(bTableSrzebhs));
        bTableVo.setCbzebhs(bTableCbzebhs == null ? "0.00" : decimalFormat.format(bTableCbzebhs));
        bTableVo.setMle(bTableMle == null ? "0.00" : decimalFormat.format(bTableMle));
        BigDecimal bTableMll = bTableMle != null
                && bTableSrzebhs != null
                && bTableSrzebhs.compareTo(BigDecimal.ZERO) != 0
                ? bTableMle.divide(bTableSrzebhs, 4, RoundingMode.HALF_UP)
                : null;

        if (isShow(bTableSrzebhs, bTableCbzebhs, bTableMle, bTableMll)) {
            String bTableMllStr = null;
            if (Optional.ofNullable(bTableMll).isPresent()) {
                bTableMllStr = bTableMll.multiply(BigDecimal.valueOf(100)).toString();
                bTableMllStr = DecimalFormatUtil.setAndValidate(bTableMllStr, 2, RoundingMode.DOWN, decimalFormat);
                if (StrUtil.isNotBlank(bTableMllStr)) {
                    bTableMllStr = bTableMllStr + "%";
                }
            }
            bTableVo.setMll(bTableMllStr);
            bTableVo.setSort(2);
            result.add(bTableVo);
        }

//        // C表数据处理
//        BigDecimal zyywsr = po.getZyywsr();
//        BigDecimal zyywcb = po.getZyywcb();
//        BigDecimal cbmle = po.getCbmle();
//
//        ProjectDataTableVO cTableVo = new ProjectDataTableVO();
//        cTableVo.setTableTitle("项目核算C表");
//        cTableVo.setSrzebhs(zyywsr == null ? "0.00" : decimalFormat.format(zyywsr));
//        cTableVo.setCbzebhs(zyywcb == null ? "0.00" : decimalFormat.format(zyywcb));
//        cTableVo.setMle(cbmle == null ? "0.00" : decimalFormat.format(cbmle));
//        BigDecimal cbmll = cbmle != null
//                && zyywsr != null
//                && zyywsr.compareTo(BigDecimal.ZERO) != 0 ? cbmle.divide(zyywsr, 4, RoundingMode.HALF_UP) : null;
//
//        if (isShow(zyywsr, zyywcb, cbmle, cbmll)) {
//            String cbmllStr = null;
//            if (Optional.ofNullable(cbmll).isPresent()) {
//                cbmllStr = cbmll.multiply(BigDecimal.valueOf(100)).toString();
//                cbmllStr = DecimalFormatUtil.setAndValidate(cbmllStr, 2, RoundingMode.DOWN, decimalFormat);
//                if (StrUtil.isNotBlank(cbmllStr)) {
//                    cbmllStr = cbmllStr + "%";
//                }
//            }
//            cTableVo.setMll(cbmllStr);
//            cTableVo.setSort(3);
//            result.add(cTableVo);
//        }
//
//        // D表数据处理
//        BigDecimal zyywsrCwkj = po.getZyywsrCwkj();
//        BigDecimal zyywcbCwkj = po.getZyywcbCwkj();
//        BigDecimal mle = po.getMle();
//
//        ProjectDataTableVO dTableVo = new ProjectDataTableVO();
//        dTableVo.setTableTitle("项目结项D表");
//        dTableVo.setSrzebhs(zyywsrCwkj == null ? "0.00" : decimalFormat.format(zyywsrCwkj));
//        dTableVo.setCbzebhs(zyywcbCwkj == null ? "0.00" : decimalFormat.format(zyywcbCwkj));
//        dTableVo.setMle(mle == null ? "0.00" : decimalFormat.format(mle));
//        BigDecimal mll = mle != null
//                && zyywsrCwkj != null
//                && zyywsrCwkj.compareTo(BigDecimal.ZERO) != 0 ? mle.divide(zyywsrCwkj, 4, RoundingMode.HALF_UP) : null;
//
//        if (isShow(zyywsrCwkj, zyywcbCwkj, mle, mll)) {
//            String mllStr = null;
//            if (Optional.ofNullable(mll).isPresent()) {
//                mllStr = mll.multiply(BigDecimal.valueOf(100)).toString();
//                mllStr = DecimalFormatUtil.setAndValidate(mllStr, 2, RoundingMode.DOWN, decimalFormat);
//                if (StrUtil.isNotBlank(mllStr)) {
//                    mllStr = mllStr + "%";
//                }
//            }
//            dTableVo.setMll(mllStr);
//            dTableVo.setSort(4);
//            result.add(dTableVo);
//        }

        return result
                .stream()
                .sorted(Comparator.comparing(ProjectDataTableVO::getSort))
                .collect(Collectors.toList());
    }

    /**
     * 判断列表数据是否需要展示
     *
     * @param srzebhs 收入总额(不含税)
     * @param cbzebhs 成本总额(不含税)
     * @param mle     毛利额
     * @param mll     毛利率
     * @return true-展示 false-不展示
     */
    public static boolean isShow(BigDecimal srzebhs, BigDecimal cbzebhs, BigDecimal mle, BigDecimal mll) {
        if (null == srzebhs
                && null == cbzebhs
                && null == mle
                && null == mll) {
            return false;
        }
        if ((null != srzebhs && srzebhs.compareTo(BigDecimal.ZERO) == 0)
                && (null != cbzebhs && cbzebhs.compareTo(BigDecimal.ZERO) == 0)
                && (null != mle && mle.compareTo(BigDecimal.ZERO) == 0)
                && (null == mll || (null != mll && mll.compareTo(BigDecimal.ZERO) == 0))) {
            return false;
        }
        return true;
    }
}