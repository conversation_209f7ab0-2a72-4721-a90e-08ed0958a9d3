package com.gok.pboot.pms.cost.enums;

import com.gok.pboot.pms.enumeration.ValueEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 成本科目类型枚举类
 *
 * <AUTHOR>
 * @create 2025/01/07
 **/
@Getter
@AllArgsConstructor
public enum CostManageSourceEnum implements ValueEnum<Integer> {

    /**
     * 新增成本项
     */
    XZCBX(0, "新增成本项"),

    /**
     * 人工成本测算
     */
    RGCBCS(1, "人工成本测算"),

    /**
     * 系统自动生成
     */
    XTZDSC(2, "系统自动生成");

    private final Integer value;

    private final String name;

}
