package com.gok.pboot.pms.Util;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.gok.pboot.pms.common.base.BaseConstants;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;

/**
 * @Auther chenhc
 * @Date 2022-08-23 15:06
 */
public class HolidayUtil {


    static BufferedReader in = null;

    public static void main(String[] args) throws FileNotFoundException, UnsupportedEncodingException {
        System.out.println(Arrays.toString(getDate1(2022).toArray()));
    }

    /**
     * @Author: csn
     * @Description: 建立API调用连接
     * @DateTime: 2022/4/20 14:14
     * @Params:
     * @Return 字符串（日期数据）
     */
    private static StringBuffer getConnect(String url) {
        URL url1;

        StringBuffer sb = new StringBuffer();
        try {
            url1 = new URL(url);
            HttpURLConnection connection = (HttpURLConnection) url1.openConnection();
            connection.setRequestMethod("GET");
            connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/61.0.3163.100 Safari/537.36)"); //防止报403错误。
            connection.connect();
            in = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8));
            String str;
            while ((str = in.readLine()) != null) {
                sb.append(str);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        close();
        return sb;
    }

    /**
     * @Author: chenhc
     * @Description: 调用第一种API
     * @DateTime: 2022/8/23
     * @Params:
     * @Return
     */
    public static JSONArray getDate1(Integer YEAR) {

        // API接口URL
        String url = "https://api.apihubs.cn/holiday/get?field=date&year="+YEAR+"&workday=2&cn=1&size=200";

        // 返回获取的字符串
        StringBuffer sb = getConnect(url);

        // 返回一个JSON对象（将JSON字符串转化为JSON对象）
        JSONObject jsonObject = JSONObject.parseObject(sb.toString());

        // 获取JSON字段为“data”的数据并将其返回为一个JSON对象
        JSONObject data = jsonObject.getJSONObject("data");

        // 返回JSON对象的一个（链表）对象
        JSONArray jsonArray = (JSONArray) data.get("list");
        // 遍历链表
//        for (int i = 0; i < jsonArray.size(); i++) {
//            // 获取时间（20220101）
//            String t_date = jsonArray.getJSONObject(i).getString("date");
//            // 截取月（01）
//            String mouth = t_date.substring(4, 6);
//            // 截取日（01）
//            String day = t_date.substring(6, 8);
//            // 将数据格式化
//            String date = YEAR + "-" + mouth + "-" + day;
//
//            System.out.println(date);
//
//        }
        return jsonArray;
    }

    /**
     * 获取所有非工作日或法定节假日
     *
     * @param year           年份
     * @param onlyLegalHoliday 是否仅查法定节假日
     * @return json数组
     */
    public static JSONArray getDate2(Integer year, Boolean onlyLegalHoliday) {

        // API接口URL
        StringBuilder url = new StringBuilder("https://api.apihubs.cn/holiday/get?field=date&workday=2&cn=1&size=200&year=").append(year);
        if (onlyLegalHoliday) {
            // 仅查询法定节假日
            url.append("&holiday_legal=").append(BaseConstants.YES);
        }

        // 返回获取的字符串
        StringBuffer sb = getConnect(url.toString());

        // 返回一个JSON对象（将JSON字符串转化为JSON对象）
        JSONObject jsonObject = JSONObject.parseObject(sb.toString());

        // 获取JSON字段为“data”的数据并将其返回为一个JSON对象
        JSONObject data = jsonObject.getJSONObject("data");

        // 返回JSON对象的一个（链表）对象
        JSONArray jsonArray = (JSONArray) data.get("list");
        return jsonArray;
    }

    /**
     * @Author: chenhc
     * @Description: 关闭io流
     * @DateTime: 2022/8/23
     * @Params:
     * @Return
     */
    public static void close() {
        if (in != null) {
            try {
                in.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

}
