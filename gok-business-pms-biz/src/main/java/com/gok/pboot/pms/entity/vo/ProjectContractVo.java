package com.gok.pboot.pms.entity.vo;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.Util.DecimalFormatUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.RoundingMode;
import java.text.DecimalFormat;

/**
 * 项目合同 Vo类
 *
 * <AUTHOR>
 * @date 2023/08/22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class ProjectContractVo {

    /**
     * 合同金额(含税合计)
     */
    private String contractPriceSum;

    /**
     * 合同金额(不含税合计)
     */
    private String contractPriceBhsSum;

    /**
     * 项目合同分页VO
     */
    Page<ProjectContractPageVo> projectContractPageVoPage;

    public void setScale(int newScale, RoundingMode roundingMode, DecimalFormat decimalFormat) {
        this.contractPriceSum = DecimalFormatUtil.setAndValidate(contractPriceSum, newScale, roundingMode, decimalFormat);
        this.contractPriceBhsSum = DecimalFormatUtil.setAndValidate(contractPriceBhsSum, newScale, roundingMode, decimalFormat);
    }

}
