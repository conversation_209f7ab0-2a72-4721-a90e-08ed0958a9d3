package com.gok.pboot.pms.entity.vo;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 合同台账列表汇总vo
 *
 * <AUTHOR>
 * @date 2024/2/22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class ContractLedgerSummaryVo {

    /**
     * 合同id
     */
    private Long id;
    /**
     * 合同细类
     */
    private Integer htxl;

    /**
     * 合同金额（含税）
     */
    private BigDecimal htje;

    /**
     * 预计收(付)款金额(含税)
     */
    private BigDecimal skje;

    /**
     * 合同状态
     */
    private Integer htzt;

    /**
     * 实际合同签订日期
     */
    private String sjhtqdrq;

    /**
     * 变更类型
     */
    private Integer bglx;

    /**
     * 已收款金额
     */
    private String  yskje;
}
