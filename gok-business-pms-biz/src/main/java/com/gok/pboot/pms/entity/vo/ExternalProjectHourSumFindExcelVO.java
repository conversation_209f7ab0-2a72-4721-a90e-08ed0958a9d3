package com.gok.pboot.pms.entity.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 外部项目工时汇总导出
 * @Auther chenhc
 * @Date 2022-08-24 14:52
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ExternalProjectHourSumFindExcelVO {

    @ExcelIgnore
    private Long id;

    /**
     * 项目名称
     */
    @ExcelProperty("项目名称")
    private String projectName;

    /**
     * 项目编号
     */
    @ExcelProperty("项目编号")
    private String code;


    /**
     * 项目状态值
     */
    @ExcelIgnore
    private Integer projectStatus;

    /**
     * 项目状态值
     */
    @ExcelProperty("项目状态")
    private String projectStatusName;

    /**
     * 是否内部项目
     */
    @ExcelProperty("是否内部项目")
    private String isNotInternalProject;

    /**
     * 项目类型
     */
    @ExcelProperty("项目类型")
    private String projectTypeName;

    /**
     * 收入归属部门编号
     */
    @ExcelIgnore
    private Long projectDeptId;
    /**
     * 收入归属部门
     */
    @ExcelProperty("收入归属部门")
    private String projectDeptName;

    /**
     * 项目正常工时
     */
    @ExcelProperty("项目正常工时（小时）")
    private BigDecimal normalHours;

    /**
     * 项目加班工时
     */
    @ExcelProperty("总加班工时（小时）")
    private BigDecimal addedHours;

    @ExcelProperty("工作日加班工时（小时）")
    private BigDecimal workOvertimeHours;

    @ExcelProperty("休息日加班工时（小时）")
    private BigDecimal restOvertimeHours;

    @ExcelProperty("节假日加班工时（小时）")
    private BigDecimal holidayOvertimeHours;

    /**
     * 调休工时
     */
    @ExcelProperty("调休工时（小时）")
    private BigDecimal leaveHours;

    /**
     * OA加班工时
     */
    @ExcelIgnore
    private BigDecimal hourData;

    /**
     * 项目分摊工时
     */
    @ExcelProperty("项目分摊工时（小时）")
    private BigDecimal projectShareHours;

    /**
     * 项目耗用工时
     */
    @ExcelIgnore
    private BigDecimal projectHours;

    /**
     * 售前工时
     */
    @ExcelProperty("售前工时（小时）")
    private BigDecimal preSaleHours;

    /**
     * 售后工时
     */
    @ExcelProperty("售后工时（小时）")
    private BigDecimal afterSaleHours;


    /**
     * 项目参与人数
     */
    @ExcelIgnore
    private Long userNum;


    public static ExternalProjectHourSumFindExcelVO from(UnReviewedDailyPaperEntryExcelVO findPageVO){
        ExternalProjectHourSumFindExcelVO vo = new ExternalProjectHourSumFindExcelVO();
        vo.setId(findPageVO.getId());
        vo.setCode(findPageVO.getProjectCode());
        vo.setProjectName(findPageVO.getProjectName());
        vo.setProjectStatusName(findPageVO.getProjectStatusName());
        vo.setProjectTypeName(findPageVO.getProjectTypeName());
        vo.setProjectDeptId(findPageVO.getProjectDeptId());
        vo.setProjectDeptName(findPageVO.getProjectDeptName());
        vo.setNormalHours(findPageVO.getNormalHours());
        vo.setAddedHours(findPageVO.getAddedHours());
        vo.setWorkOvertimeHours(findPageVO.getWorkOvertimeHours());
        vo.setRestOvertimeHours(findPageVO.getRestOvertimeHours());
        vo.setHolidayOvertimeHours(findPageVO.getHolidayOvertimeHours());
        vo.setLeaveHours(findPageVO.getLeaveHours());
        vo.setHourData(findPageVO.getHourData());
        vo.setProjectShareHours(findPageVO.getProjectShareHours());
        vo.setProjectHours(findPageVO.getProjectHours());
        vo.setPreSaleHours(findPageVO.getPreSaleHours());
        vo.setAfterSaleHours(findPageVO.getAfterSaleHours());
        vo.setIsNotInternalProject(findPageVO.getIsNotInternalProject());
        return vo;
    }
}
