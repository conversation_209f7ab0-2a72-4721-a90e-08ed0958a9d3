package com.gok.pboot.pms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.entity.domain.ProjectPaymentClaim;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 项目回款认领 Mapper
 *
 * <AUTHOR>
 * @since 2023-09-26
 */
@Mapper
public interface ProjectPaymentClaimMapper extends BaseMapper<ProjectPaymentClaim> {

    /**
     * 批量插入
     *
     * @param projectPaymentClaimList {@link List}<{@link ProjectPaymentClaim}>
     * @return {@code true} or {@code false}
     */
    boolean saveBatch(@Param("list") List<ProjectPaymentClaim> projectPaymentClaimList);
}