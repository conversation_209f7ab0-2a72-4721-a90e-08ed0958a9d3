package com.gok.pboot.pms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.entity.domain.SalesReceipt;
import com.gok.pboot.pms.entity.dto.SalesReceiptDTO;
import com.gok.pboot.pms.entity.vo.SalesReceiptInDetailVO;
import com.gok.pboot.pms.entity.vo.SalesReceiptPushVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 销售收款计划Mapper
 *
 * <AUTHOR>
 * @description 针对表【sales_receipt(销售收款计划)】的数据库操作Mapper
 * @createDate 2023-09-27 16:08:24
 * @Entity com.gok.pboot.financial.db.entity.SalesReceipt
 */
@Mapper
public interface SalesReceiptMapper extends BaseMapper<SalesReceipt> {

    /**
     * 分页查询
     *
     * @param page 分页条件
     * @param dto  查询条件
     * @return page
     */
    Page<SalesReceipt> querySalesReceiptPage(@Param("page") Page<SalesReceipt> page,
                                             @Param("dto") SalesReceiptDTO dto);

    /**
     * 查询全部的项款名称
     *
     * @return 名称列表
     */
    List<String> currentPaymentNameBox();

    /**
     * 查询全部的项目状态
     *
     * @return 状态列表
     */
    List<String> projectStatusBox();

    /**
     * 获取指定的列表
     *
     * @param dto dto
     * @return {@link List}<{@link SalesReceipt}>
     */
    List<SalesReceipt> querySalesReceiptList(@Param("dto") SalesReceiptDTO dto);

    /**
     * 根据id查找
     *
     * @param id id
     * @return {@link SalesReceiptInDetailVO}
     */
    SalesReceiptInDetailVO selectVoById(@Param("id") Long id);

    /**
     * 获取推送消息所需的信息
     *
     * @return {@link List}<{@link SalesReceiptPushVO}>
     */
    List<SalesReceiptPushVO> queryPushVo();

}