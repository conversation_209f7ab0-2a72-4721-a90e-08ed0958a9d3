package com.gok.pboot.pms.entity.vo;


import lombok.AllArgsConstructor;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 人员时间段内的项目调休
 *
 * <AUTHOR>
 * @since 2024-08-08
 */
@Data
@AllArgsConstructor
public class CompensatoryLeaveProjectSumVO {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 项目id
     */
    private Long projectId;
    /**
    * 小时数合计
    */
    private BigDecimal leaveSum;

}
