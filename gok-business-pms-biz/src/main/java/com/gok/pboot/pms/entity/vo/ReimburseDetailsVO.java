package com.gok.pboot.pms.entity.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gok.pboot.pms.Util.DecimalFormatUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.RoundingMode;
import java.text.DecimalFormat;

/**
 * 项目费用报销明细
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class ReimburseDetailsVO {
    /**
     * 流程名称
     */
    @ExcelProperty("流程名称")
    private String requestName;

    /**
     * 申请人ID
     */
    @ExcelIgnore
    private String bxr;

    /**
     * 申请人
     */
    @ExcelProperty("申请人")
    private String applicant;

    /**
     * 付款日期
     */
    @ExcelProperty("付款日期")
    private String bxrq;

    /**
     * 科目ID
     */
    @ExcelIgnore
    private Integer kmmc;

    /**
     * 科目名称
     */
    @ExcelProperty("科目名称")
    private String kmmcValue;

    /**
     * 费用项ID
     */
    @ExcelIgnore
    private Integer fyx;

    /**
     * 费用项
     */
    @ExcelProperty("费用项")
    private String fyxValue;

    /**
     * 费用项类别ID
     */
    @ExcelIgnore
    private Integer fyxlb;

    /**
     * 费用项类别
     */
    @ExcelProperty("费用项类别")
    private String fyxlbValue;

    /**
     * 报销金额
     */
    @ExcelProperty("报销金额")
    private String bxje;

    /**
     * 冲销金额
     */
    @ExcelProperty("冲销金额")
    private String cxje;

    /**
     * 付款金额
     */
    @ExcelProperty("付款金额")
    private String je;

    /**
     * 说明
     */
    @ExcelProperty("说明")
    private String memo;

    public void setScale(int newScale, RoundingMode roundingMode, DecimalFormat decimalFormat) {
        this.bxje = DecimalFormatUtil.setAndValidate(bxje, newScale, roundingMode, decimalFormat);
        this.cxje = DecimalFormatUtil.setAndValidate(cxje, newScale, roundingMode, decimalFormat);
        this.je = DecimalFormatUtil.setAndValidate(je, newScale, roundingMode, decimalFormat);
    }
}
