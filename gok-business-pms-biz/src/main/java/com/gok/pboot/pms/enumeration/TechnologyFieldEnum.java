package com.gok.pboot.pms.enumeration;

/**
 * 项目-技术领域枚举
 *
 * <AUTHOR>
 */
public enum TechnologyFieldEnum implements ValueEnum<Integer> {
    /**
     * 教学服务
     */
    teaching_services(0, "教学服务"),
    /**
     * 实验室建设
     */
    laboratory_construction(1, "实验室建设"),
    /**
     * 教学其他
     */
    teaching_others(2, "教学其他"),
    /**
     * 纯服务
     */
    pure_service(3, "纯服务"),
    /**
     * 综合
     */
    comprehensive(4, "综合");

    //值
    private Integer  value;
    //名称
    private String name;

    TechnologyFieldEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
    /**
     * 获取值
     *
     * @return Integer
     */
    @Override
    public Integer getValue() {
        return value;
    }

    /**
     * 获取名称
     *
     * @return String
     */
    @Override
    public String getName() {
        return name;
    }

    public static String getNameByVal(Integer value) {
        for (TechnologyFieldEnum technologyFieldEnum : TechnologyFieldEnum.values()) {
            if (technologyFieldEnum.value.equals(value)) {
                return technologyFieldEnum.name;
            }
        }
        return "";
    }
    public static Integer getValByName(String name) {
        for (TechnologyFieldEnum technologyFieldEnum : TechnologyFieldEnum.values()) {
            if (technologyFieldEnum.getName().equals(name)) {
                return technologyFieldEnum.getValue();
            }
        }
        return null;
    }

    public static TechnologyFieldEnum getTechnologyFieldEnum(Integer value) {
        for (TechnologyFieldEnum technologyFieldEnum : TechnologyFieldEnum.values()) {
            if (technologyFieldEnum.value.equals(value)) {
                return technologyFieldEnum;
            }
        }
        return null;
    }
}
