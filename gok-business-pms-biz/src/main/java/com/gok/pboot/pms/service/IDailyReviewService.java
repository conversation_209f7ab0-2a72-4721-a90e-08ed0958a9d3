package com.gok.pboot.pms.service;


import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.entity.dto.*;
import com.gok.pboot.pms.entity.vo.*;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 日报审核
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-31
 */
public interface IDailyReviewService {


    ApiResult<Page<DailyReviewDateVO>> dailyReviewFindPageVO(DailyReviewDTO dailyReviewDTO);

    ApiResult<Page<DailyProjectDetailsFindPageVO>> dailyProjectDetailsFindPageVO(DailyProjectDetailsFindPageDTO dailyProjectDetailsFindPageDTO);

    ApiResult<String> changeApprovalStatus(ChangeApprovalStatusDTO changeApprovalStatusDTO);

    ApiResult<String> dailyProjectDetailsOneAudit(DailyProjectDetailsFindPageDTO dailyProjectDetailsFindPageDTO);

    /**
     * 日报审核（项目维度）-分页
     * @param dto 查询条件
     * @return 分页数据
     */
    ApiResult<Page<DailyReviewProjectVO>> projectDimensionPage(DailyReviewDTO dto);

    /**
     * 日报审核（项目维度）-查看日报-统计
     * @param totalDto 项目id
     * @return 统计数据
     */
    ApiResult<DailyReviewProjectViewTotalVO> projectDimensionViewTotal(DailyReviewDTO totalDto);

    /**
     * 日报审核（项目维度）-查看日报-分页
     * @param dto 查询条件
     * @return 分页数据
     */
    ApiResult<Page<DailyReviewProjectViewVO>> projectDimensionViewPage(DailyReviewDTO dto);

    /**
     * 复用交付工时审核 - 分页
     * @param dto 查询参数
     * @return 分页数据
     */
    ApiResult<Page<DailyReviewReuseAndDeliveryProjectVO>> reuseAndDeliveryPage(DailyReviewReuseAndDeliveryProjectDTO dto);

    /**
     * 复用交付工时审核-查看日报-统计
     * @return {@link ApiResult<DailyReviewPageVO>}
     */
    ApiResult<DailyReviewReuseAndDeliveryViewTotalVO> reuseAndDeliveryViewTotal(DailyReviewTotalDTO dto);

    /**
     * 复用交付工时审核-查看日报-分页
     * @param dto 查询条件
     * @return 分页数据
     */
    Page<DailyReviewReuseAndDeliveryViewVO> reuseAndDeliveryViewPage(
            DailyReviewReuseAndDeliveryViewDTO dto
    );
    /**
     * 人才复用、交付人员工时一键审核
     * @param filter 审核的数据参数
     * @return 审核状态
     */
    ApiResult<String> reuseAndDeliveryOneAudit(DailyReviewReuseAndDeliveryViewDTO filter);
    /**
     * 复用交付工时审核-查看日报-一键审核
     * 能审核的审核，审核不了的就返回提示
     * @param dailyPaperEntryIdStr 日报id列表
     * @return 审核结果
     */
    ApiResult<String> projectDimensionOneAudit(String dailyPaperEntryIdStr);

    /**
     * 未审核数
     * 1 日报审核（日期维度）未审核数
     * 2 日报审核（项目维度）未审核数
     * 3 复用交付工时未审核数
     * @return {@link ApiResult<Map<Integer, Integer>>}
     */
    @Deprecated
    ApiResult<Map<String, Integer>> unauditedNum(Long userId);

    ApiResult<DailyProjectDetailsFindTotalVO> dailyProjectDetailsFindTotal(DailyProjectDetailsFindPageDTO dailyProjectDetailsFindPageDTO);

    /**
     * 查询工时审核 人员维度
     * @param dto 查询条件
     * @return 分页数据
     */
    ApiResult<Page<DailyReviewPersonnelViewVO>> dailyPersonnelViewFindPage(DailyReviewDTO dto);

    /**
     * 查询工时审核 人员维度统计
     * @param dto 查询条件
     * @return 统计数据
     */
    ApiResult<DailyReviewPersonnelTotalVO> dailyPersonnelFindTotal(DailyReviewDTO dto);

    /**
     * 查询获取用户部门树（工时审核人员维度）
     * @param dto 查询条件
     * @return 用户部门树
     */
    ApiResult<List<Tree<String>>> dailyPersonnelFindDeptUserTree(DailyReviewDTO dto);

    /**
     * 复用交付工时审核-查看日报-批量取消审核
     *
     * @param filter filter
     */
    void reuseAndDeliveryBatchCancelReview(DailyReviewReuseAndDeliveryViewDTO filter);
}
