package com.gok.pboot.pms.eval.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.eval.entity.domain.EvalProjectManager;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2025/05/08
 **/
@Mapper
public interface EvalProjectManagerMapper extends BaseMapper<EvalProjectManager> {

    /**
     * 总支撑官/PMO批量打分
     *
     * @param scoreEntries 评分实体类集合
     * @return
     */
    int batchScore(@Param("scoreEntries") List<EvalProjectManager> scoreEntries);

}
