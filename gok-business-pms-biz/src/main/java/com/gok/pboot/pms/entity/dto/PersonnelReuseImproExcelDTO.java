package com.gok.pboot.pms.entity.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Auther chenhc
 * @Date 2022-09-01 15:16
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PersonnelReuseImproExcelDTO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 项目ID
     */
    private Long projectId;


    /**
     * 项目编码
     */
    @ExcelProperty(value = "项目编码",index = 0)
    @NotBlank(message = "项目编码不能为空")
    private String projectCode;

    /**
     * 项目名称
     */
    @ExcelProperty(value = "项目名称",index = 1)
    @NotBlank(message = "项目名称不能为空")
    private String projectName;

    /**
     * 年级
     */
    @ExcelProperty(value = "年级",index = 2)
    @NotBlank(message = "年级不能为空")
    private String grade;

    /**
     * 人员名称
     */
    @ExcelProperty(value = "人员名称",index = 3)
    @NotBlank(message = "人员名称不能为空")
    private String userRealName;

    /**
     * 手机号码
     */
    @ExcelProperty(value = "手机号码",index = 4)
    @NotBlank(message = "手机号码不能为空")
    private String mobile;

    /**
     * 汇总工时（人天）
     */
    @ExcelProperty(value = "汇总工时（人天）",index = 5)
    @NotNull(message = "汇总工时（人天）不能为空")
    private String aggregatedDays;

    /**
     * 人才类型
     */
    @ExcelProperty(value = "人才类型",index = 6)
    @NotBlank(message = "人才类型不能为空")
    private String personnelType;

    /**
     * 院校名称
     */
    @ExcelProperty(value = "院校名称",index = 7)
    @NotBlank(message = "院校名称不能为空")
    private String schoolName;

    /**
     * 专业
     */
    @ExcelProperty(value = "专业",index = 8)
    @NotBlank(message = "专业不能为空")
    private String major;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注",index = 9)
    private String remark;

}
