package com.gok.pboot.pms.cost.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.components.common.str.StrUtils;
import com.gok.components.common.user.PigxUser;
import com.gok.module.file.entity.SysFile;
import com.gok.module.file.service.SysFileService;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.Util.BaseBuildEntityUtil;
import com.gok.pboot.pms.Util.DbApiUtil;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.Util.SysDeptUtils;
import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.cost.entity.domain.CostManageTarget;
import com.gok.pboot.pms.cost.entity.domain.CostManageVersion;
import com.gok.pboot.pms.cost.entity.dto.CostManageTargetDTO;
import com.gok.pboot.pms.cost.entity.dto.ProjectTargetInfoDTO;
import com.gok.pboot.pms.cost.entity.vo.CostManageTargetVO;
import com.gok.pboot.pms.cost.entity.vo.CostManageTargetVersionIInfoVO;
import com.gok.pboot.pms.cost.entity.vo.CostManageVersionVO;
import com.gok.pboot.pms.cost.enums.CostManageVersionEnum;
import com.gok.pboot.pms.cost.enums.CostRequestStatusEnum;
import com.gok.pboot.pms.cost.enums.RequestTypeEnum;
import com.gok.pboot.pms.cost.enums.VersionStatusEnum;
import com.gok.pboot.pms.cost.mapper.CostManageTargetMapper;
import com.gok.pboot.pms.cost.mapper.CostManageVersionMapper;
import com.gok.pboot.pms.cost.service.ICostBaselineVersionRecordService;
import com.gok.pboot.pms.cost.service.ICostManageTargetService;
import com.gok.pboot.pms.cost.service.ICostManageVersionService;
import com.gok.pboot.pms.entity.SysDept;
import com.gok.pboot.pms.enumeration.CostManageStatusEnum;
import com.gok.pboot.pms.handler.BcpDataHandler;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 成本目标管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Service
@RequiredArgsConstructor
public class CostManageTargetServiceImpl extends ServiceImpl<CostManageTargetMapper, CostManageTarget> implements ICostManageTargetService {

    private final BcpDataHandler bcpDataHandler;
    private final SysFileService sysFileService;
    private final ICostManageVersionService costManageVersionService;
    private final CostManageTargetMapper costManageTargetMapper;
    private final CostManageVersionMapper costManageVersionMapper;
    private final DbApiUtil dbApiUtil;
    private final ICostBaselineVersionRecordService baselineVersionRecordService;

    /**
     * 添加项目目标信息
     *
     * @param costManageTargetDTO DTO信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertCostManageTargetInfo(CostManageTargetDTO costManageTargetDTO) {
        // 成本目标管理
        CostManageTarget costManageTarget;
        // 成本管理版本记录
        CostManageVersion costManageVersion = new CostManageVersion();
        // 当前版本ID
        Long versionId = IdWorker.getId();
        // 获取部门map
        Map<Long, SysDept> deptIdMap = bcpDataHandler.getAllSysDeptMap();
        // 获取当前版本
        CostManageVersion currentCostManageVersion = costManageVersionService.getCurrentCostManageVersion(CostManageVersionEnum.MBGL, costManageTargetDTO.getProjectId());
        String versionName = Objects.nonNull(currentCostManageVersion) ? currentCostManageVersion.getVersionName() : StrUtils.EMPTY;
        // 更新为历史版本
        if (StrUtil.isNotBlank(versionName)) {
            BaseBuildEntityUtil.buildUpdate(currentCostManageVersion.setVersionStatus(VersionStatusEnum.HISTORY.getValue()));
            costManageVersionMapper.updateById(currentCostManageVersion);
        }
        // 获取新的版本名称
        String newVersionName = CostConfigVersionServiceImpl.getVersionName(versionName);
        // 设置版本记录
        costManageVersion.setProjectId(costManageTargetDTO.getProjectId())
                .setVersionType(CostManageVersionEnum.MBGL.getValue())
                .setVersionStatus(VersionStatusEnum.CURRENT.getValue())
                .setVersionName(newVersionName)
                .setId(versionId);
        BaseBuildEntityUtil.buildInsertNoId(costManageVersion);
        // 获取当前用户信息
        PigxUser user = SecurityUtils.getUser();
        if (Objects.nonNull(user)) {
            String deptName = SysDeptUtils.collectFullName(deptIdMap, user.getDeptId());
            costManageVersion.setOperatorId(user.getId()).setOperatorName(user.getName()).setOperatorDeptId(user.getDeptId()).setOperatorDeptName(deptName);
        }
        costManageVersionMapper.insert(costManageVersion);
        costManageTarget = BeanUtil.copyProperties(costManageTargetDTO, CostManageTarget.class);
        costManageTarget.setVersionId(versionId);
        BaseBuildEntityUtil.buildInsert(costManageTarget);
        costManageTargetMapper.insert(costManageTarget);
        // 同步基线版本记录
        baselineVersionRecordService.syncCostBaselineVersionRecord(costManageTargetDTO.getProjectId(), StrUtil.EMPTY, null);
    }

    /**
     * 查询项目目标信息
     *
     * @param versionId 版本ID
     * @param projectId 项目ID
     * @return {@link ApiResult }<{@link CostManageTargetVO }>
     */
    @Override
    public CostManageTargetVO getCostManageTargetInfo(Long versionId, Long projectId) {
        // 获取版本数据
        CostManageTargetVO costManageTarget = costManageTargetMapper.getCostManageTargetInfo(versionId, projectId);
        Optional.ofNullable(costManageTarget)
                .ifPresent(target -> {
                    if (target.getRequestId() != null) {
                        //查询OA流程状态
                        List<CostManageVersionVO> costManageVersionList = dbApiUtil.getOaRequestStatusToObj(Collections.singletonList(target.getRequestId()),CostManageVersionVO.class);
                        Integer currentNodeType = ObjectUtil.isEmpty(costManageVersionList) ? null : costManageVersionList.get(0).getRequestStatus();
                        target.setRequestStatus(CostRequestStatusEnum.getValByNodeType(currentNodeType));
                        target.setRequestStatusName(CostRequestStatusEnum.getNameByNodeType(currentNodeType));
                        // 若流程状态是已归档，目标状态设置为已确认
                        if(CostRequestStatusEnum.FINISH.getValue().equals(target.getRequestStatus())) {
                            target.setStatus(CostManageStatusEnum.CONFIRMED.getValue());
                        }

                    }
                    target.setStatusName(EnumUtils.getNameByValue(CostManageStatusEnum.class, target.getStatus()));
                });
        Optional.ofNullable(costManageTarget)
                .map(CostManageTargetVO::getDetailFiles)
                .filter(StringUtils::isNotBlank)
                .ifPresent(files -> {
                    List<String> fileIds = Arrays.asList(files.split(","));
                    List<SysFile> fileList = sysFileService.listByIds(fileIds);
                    costManageTarget.setFileList(fileList);
                });
        return costManageTarget;
    }

    /**
     * 获取历史版本数据
     *
     * @param pageRequest 分页参数
     * @param projectId   项目ID
     * @return {@link ApiResult }<{@link Page}<{@link CostManageTargetVersionIInfoVO }>>
     */
    @Override
    public Page<CostManageTargetVersionIInfoVO> getCostManageTargetVersionInfo(PageRequest pageRequest, Long projectId) {
        Page<CostManageTargetVersionIInfoVO> page = costManageVersionMapper.getCostManageTargetVersionInfo(Page.of(pageRequest.getPageNumber(), pageRequest.getPageSize()), projectId);
        // 查询OA流程状态
        List<Long> requestIds = page.getRecords().stream()
                .filter(version -> version != null && version.getRequestId() != null)
                .map(CostManageTargetVersionIInfoVO::getRequestId)
                .collect(Collectors.toList());
        List<CostManageVersionVO> costManageVersionList = dbApiUtil.getOaRequestStatusToObj(requestIds,CostManageVersionVO.class);
        page.getRecords().forEach(version -> {
            if (version != null) {
                costManageVersionList.forEach(costManageVersion -> {
                    if (costManageVersion.getRequestId().equals(version.getRequestId())) {
                        version.setRequestStatus(CostRequestStatusEnum.getValByNodeType(costManageVersion.getRequestStatus()));
                        version.setRequestStatusName(CostRequestStatusEnum.getNameByNodeType(costManageVersion.getRequestStatus()));
                        // 若流程状态是已归档，目标状态设置为已确认
                        if(CostRequestStatusEnum.FINISH.getValue().equals(version.getRequestStatus())) {
                            version.setStatus(CostManageStatusEnum.CONFIRMED.getValue());
                        }
                    }
                });
                version.setStatusName(EnumUtils.getNameByValue(CostManageStatusEnum.class, version.getStatus()));
            }
        });
        return page;
    }

    /**
     * 同步OA项目台账最新目标内容
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void getProjectTargetInfo() {
        // 版本ID
        Long versionId;
        // 存储版本信息
        List<CostManageVersion> costManageVersionList = new ArrayList<>();
        // 存储目标管理信息
        List<CostManageTarget> costManageTargetList;
        // 获取部门map
        Map<Long, SysDept> deptIdMap = bcpDataHandler.getAllSysDeptMap();
        // 获取从OA同步的最新数据
        List<ProjectTargetInfoDTO> projectTargetInfo = dbApiUtil.getProjectTargetInfo();
        // 转换OA同步数据
        for (ProjectTargetInfoDTO info : projectTargetInfo) {
            versionId = IdWorker.getId();
            CostManageVersion costManageVersion = new CostManageVersion();
            costManageVersion.setVersionType(CostManageVersionEnum.MBGL.getValue())
                    .setVersionStatus(VersionStatusEnum.CURRENT.getValue())
                    .setVersionName(CostConfigVersionServiceImpl.getVersionName(""))
                    .setProjectId(info.getProjectId())
                    .setRequestId(info.getRequestId())
                    .setRequestName(info.getRequestName())
                    .setRequestType(RequestTypeEnum.getValueByStr(info.getRequestName()))
                    .setId(versionId);
            BaseBuildEntityUtil.buildInsertNoId(costManageVersion);
            costManageVersionList.add(costManageVersion);
            info.setVersionId(versionId);
        }
        // 获取当前用户信息
        PigxUser user = SecurityUtils.getUser();
        if (Objects.nonNull(user)) {
            String deptName = SysDeptUtils.collectFullName(deptIdMap, user.getDeptId());
            costManageVersionList.forEach(info -> info.setOperatorId(user.getId())
                    .setOperatorName(user.getName())
                    .setOperatorDeptId(user.getDeptId())
                    .setOperatorDeptName(deptName));
        }
        // 数据迁移
        costManageTargetList = BeanUtil.copyToList(projectTargetInfo, CostManageTarget.class);
        costManageTargetList.forEach(BaseBuildEntityUtil::buildInsert);
        this.saveBatch(costManageTargetList);
        costManageVersionService.saveBatch(costManageVersionList);
    }

}
