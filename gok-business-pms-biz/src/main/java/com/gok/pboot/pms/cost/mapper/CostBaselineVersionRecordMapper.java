package com.gok.pboot.pms.cost.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.pms.cost.entity.domain.CostBaselineVersionRecord;
import com.gok.pboot.pms.cost.entity.vo.CostBaselineVersionRecordVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 成本基线版本记录映射器
 *
 * <AUTHOR>
 * @date 2025/01/15
 */
@Mapper
public interface CostBaselineVersionRecordMapper extends BaseMapper<CostBaselineVersionRecord> {

    /**
     * 获取版本记录分页列表
     *
     * @param projectId 项目 ID
     * @param page      页
     * @return {@link Page }<{@link CostBaselineVersionRecordVO }>
     */
    Page<CostBaselineVersionRecordVO> getVersionRecordPage(@Param("projectId") Long projectId, @Param("page") Page<CostBaselineVersionRecordVO> page);

    /**
     * 获取项目变更基线版本数据
     *
     * @param projectIds
     * @return
     */
    List<CostBaselineVersionRecord> getCostChangeByProjectIds(@Param("projectIds") List<Long> projectIds);

}




