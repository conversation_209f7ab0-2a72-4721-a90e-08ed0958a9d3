package com.gok.pboot.pms.eval.controller;

import com.gok.pboot.pms.common.base.ApiResult;
import com.gok.pboot.pms.eval.entity.vo.EvalUserRoleVO;
import com.gok.pboot.pms.eval.service.IEvalUserRoleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 项目评价用户角色前端控制器
 *
 * <AUTHOR>
 * @menu 项目评价
 * @date 2025/05/12
 */
@Slf4j
@Api(tags = "项目评价用户角色")
@RestController
@RequestMapping("/evalUserRole")
@RequiredArgsConstructor
public class EvalUserRoleController {

    private final IEvalUserRoleService evalUserRoleService;

    /**
     * 查询所有用户角色
     *
     * @return 用户角色列表
     */
    @ApiOperation("查询所有用户角色")
    @GetMapping("/list")
    public ApiResult<List<EvalUserRoleVO>> findAll() {
        return ApiResult.success(evalUserRoleService.findAll());
    }

} 