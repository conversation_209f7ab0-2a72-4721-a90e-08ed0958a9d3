package com.gok.pboot.pms.entity.vo;

import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.entity.domain.ProjectTask;
import com.gok.pboot.pms.enumeration.TaskWorkingState;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 负责人的项目任务vo
 *
 * <AUTHOR>
 * @date 2023/8/25
 */
@Data
public class ProjectTaskOfChargeVO {

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 任务标题
     */
    private String title;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 计划周期
     */
    private String expectedTime;

    /**
     * 任务进度（0=0%，...，10=100%）
     * @see com.gok.pboot.pms.enumeration.ProjectTaskProgressEnum
     */
    private String progress;

    /**
     * 状态（0=未开始，1=进行中，2=已完成）
     * @see com.gok.pboot.pms.enumeration.TaskWorkingState
     */
    private Integer state;

    /**
     * 状态文本描述
     *
     * @see com.gok.pboot.pms.enumeration.TaskWorkingState
     */
    private String stateText;


    public static ProjectTaskOfChargeVO of(ProjectTask request) {
        ProjectTaskOfChargeVO result = new ProjectTaskOfChargeVO();
        LocalDateTime expectedStartTime = request.getExpectedStartTime();
        LocalDateTime expectedEndTime = request.getExpectedEndTime();
        Integer state = request.getState();

        String expectedTime =
                expectedStartTime.toLocalDate().toString() + "~" + expectedEndTime.toLocalDate().toString();

        result.setTaskId(request.getId());
        result.setTitle(request.getTitle());
        result.setProjectId(request.getProjectId());
        result.setExpectedTime(expectedTime);
        result.setState(state);
        result.setStateText(EnumUtils.getNameByValue(TaskWorkingState.class, state));
        return result;
    }
}
