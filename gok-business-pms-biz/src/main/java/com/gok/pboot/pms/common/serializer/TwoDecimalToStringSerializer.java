package com.gok.pboot.pms.common.serializer;

import com.fasterxml.jackson.databind.annotation.JacksonStdImpl;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializerBase;

import java.text.DecimalFormat;

/**
 * <AUTHOR>
 * @create 2023/2/28
 * 返回前端时序列化，将小数转为两位小数返回，不足补0
 *
 * 使用：在所需要序列化的实体字段上面添加这个注解即可
 * @JsonSerialize(using = TwoDecimalToStringSerializer.class)
 *
 */
@JacksonStdImpl
public class TwoDecimalToStringSerializer extends ToStringSerializerBase {

    public TwoDecimalToStringSerializer() { super(Object.class); }

    @Override
    public String valueToString(Object value) {
        DecimalFormat decimalFormat = new DecimalFormat("0.00#");
        return decimalFormat.format(value);
    }
}
