package com.gok.pboot.pms.entity.vo;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.gok.pboot.pms.common.base.BaseEntity;
import com.gok.pboot.pms.entity.domain.ProjectTaskProgressFeedback;
import com.google.common.base.Strings;
import lombok.*;

import java.sql.Timestamp;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 项目进展回复
 *
 * <AUTHOR>
 * @version 1.1.0
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@ToString
public class ProjectTaskProgressFeedbackVO {

    /**
     * 主键ID
     */
    private String id;

    /**
     * 进展ID
     */
    private String progressId;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 用户头像链接
     */
    private String userAvatarUrl;

    /**
     * 回复内容
     */
    private String content;

    /**
     * 创建时间
     */
    private String ctime;

    /**
     * 是否可以删除
     */
    private Boolean canDelete;

    public static ProjectTaskProgressFeedbackVO of(ProjectTaskProgressFeedback po, Long userId){
        ProjectTaskProgressFeedbackVO result = new ProjectTaskProgressFeedbackVO();
        Timestamp ctime = po.getCtime();

        result.setId(String.valueOf(po.getId()));
        result.setProgressId(String.valueOf(po.getProgressId()));
        result.setUserName(Strings.nullToEmpty(po.getUserName()));
        result.setUserAvatarUrl(Strings.nullToEmpty(po.getUserAvatarUrl()));
        result.setContent(Strings.nullToEmpty(po.getContent()));
        result.setCtime(ctime == null ? "" : LocalDateTimeUtil.formatNormal(ctime.toLocalDateTime()));
        result.setCanDelete(userId.equals(po.getUserId()));

        return result;
    }

    public static List<ProjectTaskProgressFeedbackVO> batchOf(Collection<ProjectTaskProgressFeedback> pos, Long userId){
        return pos.stream()
                .sorted(Comparator.<ProjectTaskProgressFeedback>comparingLong(BaseEntity::getId).reversed())
                .map(po -> of(po, userId))
                .collect(Collectors.toList());
    }
}
