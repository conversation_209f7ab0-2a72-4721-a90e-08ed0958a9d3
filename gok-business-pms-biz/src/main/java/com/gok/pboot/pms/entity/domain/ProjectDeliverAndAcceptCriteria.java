package com.gok.pboot.pms.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目交付成果和验收标准实体类
 */
@Data
@TableName("project_deliver_and_accepte_criteria")
@EqualsAndHashCode(callSuper = true)
public class ProjectDeliverAndAcceptCriteria extends BaseEntity<Long> {


    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 交付成果要求
     */
    private String projectDeliverables;

    /**
     * 验收标准
     */
    private String acceptanceCriteria;
}