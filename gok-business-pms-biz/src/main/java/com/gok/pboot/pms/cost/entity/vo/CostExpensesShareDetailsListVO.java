package com.gok.pboot.pms.cost.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
    * 人力外包-费用分摊
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CostExpensesShareDetailsListVO{

    /**
    * 成本科目类别id
    */
    private Long accountCategoryId;

    /**
    * 费用项类别
    */
    private String accountCategoryName;

    /**
     * 费用分摊明细
     */
    private List<CostExpensesShareDetailsVO> detailsVOList;

}