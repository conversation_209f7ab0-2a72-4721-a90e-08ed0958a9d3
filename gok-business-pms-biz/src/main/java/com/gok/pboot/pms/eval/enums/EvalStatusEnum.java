package com.gok.pboot.pms.eval.enums;

import com.gok.pboot.pms.enumeration.ValueEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 评价状态枚举类
 *
 * <AUTHOR>
 * @create 2025/05/12
 **/
@Getter
@AllArgsConstructor
public enum EvalStatusEnum implements ValueEnum<Integer> {

    /**
     * 评价中
     */
    EVAL(0, "评价中"),

    /**
     * 校准
     */
    CALIBRATE(1, "校准"),

    /**
     * PMO审批
     */
    PMO_APPROVAL(2, "PMO审批"),

    /**
     * 已归档
     */
    ARCHIVE(3, "已归档");

    private final Integer value;
    private final String name;

}
