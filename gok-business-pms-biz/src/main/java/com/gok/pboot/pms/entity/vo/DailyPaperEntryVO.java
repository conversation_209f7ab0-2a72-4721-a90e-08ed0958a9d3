package com.gok.pboot.pms.entity.vo;

import cn.hutool.core.util.ObjectUtil;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.common.join.ProjectInDailyPaperEntry;
import com.gok.pboot.pms.entity.DailyPaperEntry;
import com.gok.pboot.pms.enumeration.ApprovalStatusEnum;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * - 日报条目VO -
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/8/24 17:27
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@ToString
public class DailyPaperEntryVO {
    /**
     * ID
     */
    private Long id;
    /**
     * 提交人ID
     */
    private Long userId;
    /**
     * 执行人
     */
    private String userRealName;
    /**
     * 部门全称
     */
    private String deptName;
    /**
     * 人员状态名称
     */
    private String personnelStatusName;
    /**
     * 人员状态
     */
    private Integer userStatus;
    /**
     * 项目ID
     */
    private Long projectId;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 销售人员名
     */
    private String salesmanUserName;

    /**
     * 项目经理名
     */
    private String managerUserName;

    /**
     * 审核人员名列表
     */
    private List<String> auditorNames;
    /**
     * 任务ID
     */
    private Long taskId;
    /**
     * 任务名称
     */
    private String taskName;
    /**
     * 日常工时
     */
    private BigDecimal normalHours;
    /**
     * 加班工时
     */
    private BigDecimal addedHours;

    /**
     * 工作日加班工时
     */
    private BigDecimal workOvertimeHours;

    /**
     * 休息日加班工时
     */
    private BigDecimal restOvertimeHours;

    /**
     * 节假日加班工时
     */
    private BigDecimal holidayOvertimeHours;

    /**
     * OA加班工时
     */
    private BigDecimal oaAddedHours;
    /**
     * 合计工时
     */
    private BigDecimal sumHours;
    /**
     * 工作内容
     */
    private String description;
    /**
     * 提交时间
     */
    private LocalDate submissionDate;

    /**
     * 日期+星期
     */
    private String submissionDateFormatted;

    /**
     * 日期类型 0-普通休息日 1-法定节假日 null-工作日
     */
    private Integer holidayType;

    /**
     * 审核状态（0=未提交，1=已退回，2=待审核，3=不通过，4=已通过）
     */
    private Integer approvalStatus;
    /**
     * 审核状态名称
     */
    private String approvalStatusName;
    /**
     * 审核不通过原因
     */
    private String approvalReason;

    private String modifier;

    /**
     * 工时类型（0=售前支撑，1=售后交付）
     */
    private Integer workType;

    /**
     * 工时类型（0=售前支撑，1=售后交付）
     */
    private String workTypeTxt;

    /**
     * 是否内部项目
     */
    private Integer isInsideProject;

    /**
     * 旧任务标识
     */
    private Integer oldTaskFlag;

    /**
     * 任务结束标识
     */
    private Boolean taskFinishFlag;

    /**
     * 日报ID
     */
    private Long dailyPaperId;

    /**
     * 提交人所属部门ID
     */
    private Long userDeptId;

    /**
     * 项目状态
     */
    private String projectStatus;

    /**
     * 项目状态
     * @param entry
     */
    private String projectStatusName;

    /**
     * 项目类型
     * @param entry
     */
    private String projectTypeName;




    public DailyPaperEntryVO(DailyPaperEntry entry){
        this.id = entry.getId();
        this.userRealName = entry.getUserRealName();
        this.projectId = entry.getProjectId();
        this.userId = entry.getUserId();
        this.projectName = entry.getProjectName();
        this.taskId = entry.getTaskId();
        this.taskName = entry.getTaskName();
        this.normalHours = entry.getNormalHours();
        this.addedHours = entry.getAddedHours();
        this.oaAddedHours = BigDecimal.ZERO;
        this.sumHours = normalHours.add(addedHours);
        this.description = entry.getDescription();
        this.submissionDate = entry.getSubmissionDate();
        this.approvalStatus = entry.getApprovalStatus();
        this.approvalStatusName = EnumUtils.getNameByValue(ApprovalStatusEnum.class, approvalStatus);
        this.approvalReason = entry.getApprovalReason();
        this.workType = entry.getWorkType();
        this.modifier = entry.getModifier();
        this.oldTaskFlag = entry.getOldTaskFlag();
    }

    public DailyPaperEntryVO(DailyPaperEntry entry, BigDecimal oaAddedHours){
        this(entry);
        this.oaAddedHours = oaAddedHours;
    }

    public DailyPaperEntryVO(DailyPaperEntry entry, ProjectInDailyPaperEntry project){
        this(entry);
        if (ObjectUtil.isNotNull(project)){
            this.salesmanUserName = project.getSalesmanUserName();
            this.managerUserName = project.getManagerUserName();
            this.auditorNames = project.getAuditorNames();
            this.isInsideProject = project.getIsInsideProject();
        }
    }
}
