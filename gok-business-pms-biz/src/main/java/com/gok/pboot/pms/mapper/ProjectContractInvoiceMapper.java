package com.gok.pboot.pms.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.entity.domain.ProjectContractInvoice;
import com.gok.pboot.pms.entity.vo.ContractInvoiceVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 发票记录（数仓同步）
 * 
 * <AUTHOR>
 * @date 2024-02-26 14:58:58
 */
@Mapper
public interface ProjectContractInvoiceMapper extends BaseMapper<ProjectContractInvoice> {
    /**
     * 通过合同ids批量查询发票
     * @param ids 合同id
     * @return
     */
    List<ContractInvoiceVo> getContractInvoiceVoListByContractIds(@Param("ids") List<Long> ids);
}
