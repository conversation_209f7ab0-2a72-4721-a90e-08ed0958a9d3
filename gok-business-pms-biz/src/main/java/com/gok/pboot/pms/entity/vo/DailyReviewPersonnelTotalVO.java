package com.gok.pboot.pms.entity.vo;

import lombok.*;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 日报审核 人员维度统计
 *
 * <AUTHOR>
 * @version 1.3.2
 */
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class DailyReviewPersonnelTotalVO {

    /**
     * 审核数
     */
    private Integer approvalNum;

    /**
     * 正常工时
     */
    private BigDecimal normalHours;


    /**
     * 总工时
     */
    private BigDecimal totalHours;

    /**
     * 加班工时
     */
    private BigDecimal addedHours;

    /**
     * 工作日加班工时合计
     */
    private String workOvertimeHours;

    /**
     * 休息日加班工时合计
     */
    private String restOvertimeHours;

    /**
     * 节假日加班工时合计
     */
    private String holidayOvertimeHours;

    private static final DailyReviewPersonnelTotalVO EMPTY =
            new DailyReviewPersonnelTotalVO(0, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO,
                    "0.00", "0.00", "0.00");

    public static DailyReviewPersonnelTotalVO empty() {
        return EMPTY;
    }

    public static DailyReviewPersonnelTotalVO of(Integer approvalNum, BigDecimal normalHours, BigDecimal addedHours,
                                                 BigDecimal workOvertimeHours,BigDecimal restOvertimeHours,BigDecimal holidayOvertimeHours) {
        DailyReviewPersonnelTotalVO result = new DailyReviewPersonnelTotalVO();

        result.setApprovalNum(approvalNum);
        result.setNormalHours(normalHours);
        result.setAddedHours(addedHours);
        result.setTotalHours(normalHours.add(addedHours));
        result.setHolidayOvertimeHours(holidayOvertimeHours.setScale(2, RoundingMode.HALF_UP).toString());
        result.setRestOvertimeHours(restOvertimeHours.setScale(2, RoundingMode.HALF_UP).toString());
        result.setWorkOvertimeHours(workOvertimeHours.setScale(2, RoundingMode.HALF_UP).toString());
        return result;
    }
}
