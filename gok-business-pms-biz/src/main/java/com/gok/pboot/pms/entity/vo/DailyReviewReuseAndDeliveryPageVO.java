package com.gok.pboot.pms.entity.vo;

import com.gok.pboot.pms.enumeration.FilingTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @desc 复用、交付工时审核VO
 * @createTime 2023/2/17 10:53
 */
@Deprecated
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@ApiModel("复用、交付工时审核VO")
public class DailyReviewReuseAndDeliveryPageVO {
    /**
     * 业务id
     */
    @ApiModelProperty(value = "业务id")
    private Long id;
    /**
     * 人员名称
     */
    @ApiModelProperty(value = "人员名称")
    private String userRealName;
    /**
     * 所属月份
     */
    @ApiModelProperty(value = "所属月份")
    private String month;
    /**
     * 汇总工时（人天）
     */
    @ApiModelProperty(value = "汇总工时（人天）")
    private BigDecimal aggregatedDays;
    /**
     * 类型 目前只有【人才复用】、【交付人员】两种
     */
    @ApiModelProperty(value = "类型")
    private String type;
    /**
     * 审核状态
     */
    @ApiModelProperty(value = "审核状态")
    private Integer approvalStatus;
    /**
     * 审核状态
     */
    @ApiModelProperty(value = "审核状态")
    private String approvalStatusName;
    /**
     * 审核人
     */
    @ApiModelProperty(value = "审核人")
    private String approvalName;

    /**
     * 归档状态
     * {@link FilingTypeEnum}
     */
    private Integer filed;
}
