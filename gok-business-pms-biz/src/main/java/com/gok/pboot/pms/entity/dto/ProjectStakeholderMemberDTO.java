package com.gok.pboot.pms.entity.dto;



import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

/**
 * 项目干系人-成员dto
 * 
 * <AUTHOR>
 * @date 2023-07-11 17:05:26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectStakeholderMemberDTO {

	/**
	 * ID id
	 */
	private Long id;
	/**
	 * 项目id
	 */
	private Long projectId;

	/**
	 * 成员id
	 */
	private Long memberId;

	/**
	 * 成员名
	 */
	private String memberName;
	/**
	 * 部门名
	 */
	private String deptName;
	/**
	 * 岗位/职位
	 */
	private String position;

	/**
	 * 角色类型（0=项目销售经理，1=项目售前经理，2=项目经理，3=项目操作助理，4=项目成员）
	 * {@link com.gok.pboot.pms.enumeration.RoleTypeEnum}
	 */
	private Integer roleType;

	/**
	 * 职责
	 */
	@Length(max = 200, message = "职责长度不能超过200")
	private String duty;
	/**
	 * 备注
	 */
	@Length(max = 500, message = "备注长度不能超过500")
	private String remark;

}
