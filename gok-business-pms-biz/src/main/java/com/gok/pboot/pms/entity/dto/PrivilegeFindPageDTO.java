package com.gok.pboot.pms.entity.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * PMS项目工时分摊表
 *
 * @Auther chenhc
 * @Date 2022-08-24 11:24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class PrivilegeFindPageDTO {

    /**
     * 员工姓名
     */
    private String name;

    /**
     * 项目编号
     */
    private String projectCode;

    /**
     * 项目名称
     */
    private String projectName;

}
