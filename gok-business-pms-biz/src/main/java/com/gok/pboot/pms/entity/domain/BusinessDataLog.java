package com.gok.pboot.pms.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.pms.common.base.BeanEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 商机数据日志（OA同步）(BusinessDataLog)表实体类
 *
 * <AUTHOR>
 * @since 2023-11-24 13:56:32
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("business_data_log")
public class BusinessDataLog extends BeanEntity<Long> {

    /**
     * 商机ID(项目ID)
     */
    private Long businessId;

    /**
     * 数据变更描述
     */
    private String dataChangeRecord;

    /**
     * 变更人员ID
     */
    private Long dataChangeUserId;

    /**
     * 变更人员姓名
     */
    private String dataChangeUserName;

    /**
     * 变更人员头像
     */
    private String dataChangeUserAvatar;

    /**
     * 变更人所在部门id
     */
    private Long dataChangeUserDeptId;

    /**
     * 变更人员所在部门
     */
    private String dataChangeUserDept;

    /**
     * 数据变更时间
     */
    private String dataChangeTime;

}

