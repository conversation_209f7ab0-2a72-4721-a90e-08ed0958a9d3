package com.gok.pboot.pms.eval.enums;

import com.gok.pboot.pms.enumeration.ValueEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 满意度结果枚举
 *
 * <AUTHOR>
 * @date 2025/05/08
 */
@Getter
@AllArgsConstructor
public enum EvalSatisfactionResultEnum implements ValueEnum<Integer> {

    /**
     * 未提供服务
     */
    NO_SERVICE(0, "未提供服务"),
    /**
     * 非常不满意
     */
    VERY_DISSATISFIED(1, "非常不满意"),
    /**
     * 不满意
     */
    DISSATISFIED(2, "不满意"),
    /**
     * 一般
     */
    NORMAL(3, "一般"),
    /**
     * 满意
     */
    SATISFIED(4, "满意"),
    /**
     * 非常满意
     */
    VERY_SATISFIED(5, "非常满意");

    private final Integer value;
    private final String name;
} 