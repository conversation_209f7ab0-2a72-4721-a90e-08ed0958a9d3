package com.gok.pboot.pms.handler;

import com.gok.pboot.pms.Util.BigDecimalUtils;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.cost.entity.domain.CostTaskDailyPaper;
import com.gok.pboot.pms.entity.DailyPaper;
import com.gok.pboot.pms.enumeration.ApprovalStatusEnum;
import com.gok.pboot.pms.enumeration.DailyPaperAbnormalEnum;
import com.gok.pboot.pms.enumeration.YesOrNoEnum;
import com.gok.pboot.pms.service.impl.DailyPaperServiceImpl;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 日报分析器
 *
 * <AUTHOR>
 * @version 1.3.4
 */
@Component
public class DailyPaperAnalyzerImpl implements DailyPaperAnalyzer {

    @Override
    public List<Pair<DailyPaperAbnormalEnum, String>> getDailyPaperAbnormalInfo(
            DailyPaper dailyPaper, BigDecimal leaveHour
    ) {
        return getAbnormalInfo(
                dailyPaper.getSubmissionDate(),
                dailyPaper.getApprovalStatus(),
                dailyPaper.getWorkday(),
                dailyPaper.getDailyHourCount(),
                leaveHour
        );
    }

    @Override
    public List<Pair<DailyPaperAbnormalEnum, String>> getAbnormalInfo(
            LocalDate submissionDate,
            Integer approvalStatus,
            Integer workDay,
            BigDecimal dailyHourCount,
            BigDecimal leaveHour
    ) {
        LocalDateTime now = LocalDateTime.now();
        LocalDate nowDate = now.toLocalDate();
        List<Pair<DailyPaperAbnormalEnum, String>> abnormalPairList = null;
        BigDecimal normalHour;

        if (nowDate.isBefore(submissionDate)) {
            return ImmutableList.of();
        }
        if (!DailyPaperServiceImpl.isLag(submissionDate, now)) {
            return ImmutableList.of();
        }


        // 识别待审核异常
        if (EnumUtils.valueEquals(approvalStatus, ApprovalStatusEnum.DSH)) {
            abnormalPairList = addAbnormalPair(
                    abnormalPairList, DailyPaperAbnormalEnum.WAITING_REVIEW, "日报待审核"
            );
        }
        // 识别提交异常
        if (EnumUtils.valueEquals(workDay, YesOrNoEnum.YES)) {
            normalHour = EnumUtils.valueEquals(approvalStatus, ApprovalStatusEnum.WTJ) ||
                    EnumUtils.valueEquals(approvalStatus, ApprovalStatusEnum.WTB) ||
                    EnumUtils.valueEquals(approvalStatus, ApprovalStatusEnum.YTH) ||
                    EnumUtils.valueEquals(approvalStatus, ApprovalStatusEnum.BTG) ?
                    BigDecimal.ZERO : dailyHourCount;
            //总工时
            normalHour=normalHour==null?BigDecimal.ZERO:normalHour;
            leaveHour=leaveHour==null?BigDecimal.ZERO:leaveHour;
            BigDecimal hourAll=normalHour.add(leaveHour);

            if (BigDecimal.ZERO.equals(leaveHour) && BigDecimal.ZERO.equals(normalHour)) {
                abnormalPairList = addAbnormalPair(abnormalPairList, DailyPaperAbnormalEnum.SUBMIT, "日报未提交");
            } else if (!EnumUtils.valueEquals(approvalStatus, ApprovalStatusEnum.WTB) && !BigDecimal.ZERO.equals(leaveHour) && BigDecimal.ZERO.equals(normalHour)
                    && BigDecimalUtils.SEVEN_DECIMAL.compareTo(hourAll) > 0) {
                abnormalPairList = addAbnormalPair(abnormalPairList, DailyPaperAbnormalEnum.SUBMIT, "日报未提交");
            } else if (BigDecimalUtils.SEVEN_DECIMAL.compareTo(hourAll) < 0) {
                abnormalPairList = addAbnormalPair(
                        abnormalPairList, DailyPaperAbnormalEnum.SUBMIT, "正常工时和请休假超过7小时"
                );
            } else if (BigDecimalUtils.SEVEN_DECIMAL.compareTo(hourAll) > 0) {
                abnormalPairList = addAbnormalPair(
                        abnormalPairList, DailyPaperAbnormalEnum.SUBMIT, "正常工时和请休假不足7小时"
                );
            }
        }
        // 识别节假日审核不通过、已退回提交异常
        if (EnumUtils.valueEquals(workDay, YesOrNoEnum.NO) &&
                (EnumUtils.valueEquals(approvalStatus, ApprovalStatusEnum.YTH) ||
                        EnumUtils.valueEquals(approvalStatus, ApprovalStatusEnum.BTG))) {
            abnormalPairList = addAbnormalPair(abnormalPairList, DailyPaperAbnormalEnum.SUBMIT, "日报未提交");
        }

        return ObjectUtils.defaultIfNull(abnormalPairList, ImmutableList.of());
    }

    @Override
    public List<Pair<DailyPaperAbnormalEnum, String>> getDailyPaperAbnormalInfo(CostTaskDailyPaper dailyPaper, BigDecimal leaveHour) {
        return getAbnormalInfo(
                dailyPaper.getSubmissionDate(),
                dailyPaper.getApprovalStatus(),
                dailyPaper.getWorkday(),
                dailyPaper.getDailyHourCount(),
                leaveHour
        );
    }

    /**
     * 将异常信息填充入list
     *
     * @param list         异常信息list，为null时创建
     * @param abnormalEnum 异常枚举
     * @param prompt       异常信息文本
     * @return list
     */
    private List<Pair<DailyPaperAbnormalEnum, String>> addAbnormalPair(
            @Nullable List<Pair<DailyPaperAbnormalEnum, String>> list,
            DailyPaperAbnormalEnum abnormalEnum,
            String prompt
    ) {
        if (list == null) {
            list = Lists.newArrayList(Pair.of(abnormalEnum, prompt));
        } else {
            list.add(Pair.of(abnormalEnum, prompt));
        }

        return list;
    }

}
