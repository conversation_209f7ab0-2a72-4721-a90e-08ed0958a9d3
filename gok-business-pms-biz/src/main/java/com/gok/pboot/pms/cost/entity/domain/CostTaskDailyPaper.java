package com.gok.pboot.pms.cost.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gok.pboot.pms.common.base.BeanEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDate;

/**
 * 工单日报
 *
 * <AUTHOR>
 * @date 2024/04/16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("cost_task_daily_paper")
public class CostTaskDailyPaper extends BeanEntity<Long> {

    /**
     * 员工ID
     */
    private Long userId;

    /**
     * 员工类型（0=正式，1=实习）
     */
    private Integer userStatus;

    /**
     * 员工部门ID
     */
    private Long userDeptId;

    /**
     * 提交日期
     */
    private LocalDate submissionDate;

    /**
     * 是否工作日（0=否，1=是）
     */
    private Integer workday;

    /**
     * 填报状态（0=正常，1=滞后）
     */
    private Integer fillingState;

    /**
     * 审核状态（0=未提交，1=已退回，2=待审核，3=不通过，4=已通过）
     */
    private Integer approvalStatus;

    /**
     * 项目数量
     */
    private Integer projectCount;

    /**
     * 任务数量
     */
    private Integer taskCount;

    /**
     * 日常工时数量
     */
    private BigDecimal dailyHourCount;

    /**
     * 加班工时数量
     */
    private BigDecimal addedHourCount;

    /**
     * 日报提交时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp submissionTime;
} 