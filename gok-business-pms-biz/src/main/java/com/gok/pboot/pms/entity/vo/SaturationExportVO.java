package com.gok.pboot.pms.entity.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @desc 工时饱和度审核工时总数+滞后提交人天总数的详细数据查看导出
 * @createTime 2023/2/15 11:09
 */
@Data
public class SaturationExportVO {
    /**
     * 项目名称
     */
    @ApiModelProperty("项目名称")
    @ExcelProperty("项目名称")
    private String projectName;
    /**
     * 任务名称
     */
    @ApiModelProperty("任务名称")
    @ExcelProperty("任务名称")
    private String taskName;
    /**
     * 提交人姓名
     */
    @ApiModelProperty("提交人姓名")
    @ExcelProperty("姓名")
    private String userRealName;

    /**
     * 填报日期
     */
    @ApiModelProperty("填报日期")
    @ExcelProperty("填报日期")
    private LocalDate submissionDate;
    /**
     * 提交日期
     */
    @ApiModelProperty("提交日期")
    @ExcelProperty("提交日期")
    private LocalDate commitDate;
    /**
     * 正常工时
     */
    @ApiModelProperty("正常工时")
    @ExcelProperty("正常工时（人天）")
    private Double normalHours;
    /**
     * 加班工时
     */
    @ApiModelProperty("加班工时（人天）")
    @ExcelProperty("加班工时（人天）")
    private Double addedHours;
    /**
     * 工作内容
     */
    @ApiModelProperty("工作内容")
    @ExcelProperty("工作内容")
    private String description;
    /**
     * 昨日计划
     */
    @ApiModelProperty("昨日计划")
    @ExcelProperty("昨日计划")
    private String yesterdayPlan;
    /**
     * 提交人id
     */
    @ApiModelProperty("提交人id")
    @ExcelIgnore
    private Long userId;
    /**
     * 任务id
     */
    @ExcelIgnore
    @ApiModelProperty(value = "任务id")
    private Long taskId;
}
