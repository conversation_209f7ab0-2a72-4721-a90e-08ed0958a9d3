package com.gok.pboot.pms.enumeration;

import lombok.AllArgsConstructor;

/**
 * 任务工作状态
 *
 * <AUTHOR>
 * @version 1.1.0
 */
@AllArgsConstructor
public enum TaskWorkingState implements ValueEnum<Integer> {

    NOT_START(0, "未开始"),
    WORKING(1, "进行中"),
    FINISHED(2, "已完成"),
    // 查询传参用，不入库
    DELAY(51, "已延期"),
    DEAD_LINE(52, "今日到期"),
    // 甘特图标识用，不入库
    UNFINISHED_IN_TIME(53, "未完成，未超期"),
    UNFINISHED_OUT_TIME(54, "未完成，已超期"),
    FINISHED_IN_TIME(55, "按时完成"),
    FINISHED_OUT_TIME(56, "超期完成")
    ;

    private final Integer value;

    private final String name;

    @Override
    public Integer getValue() {
        return value;
    }

    @Override
    public String getName() {
        return name;
    }
}
