package com.gok.pboot.pms.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.pms.entity.domain.ContractLedgerDetail;
import com.gok.pboot.pms.entity.vo.ContractDetailSumVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 合同台账明细表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-02-23 10:46:40
 */
@Mapper
public interface ContractLedgerDetailMapper extends BaseMapper<ContractLedgerDetail> {

    /**
     * 收款金额汇总数据
     * @return
     */
    List<ContractDetailSumVo> selSkjeSum();

    /**
     * 质保金收款金额
     * @return
     */
    List<ContractDetailSumVo> selZbjSkjeSum();
}
