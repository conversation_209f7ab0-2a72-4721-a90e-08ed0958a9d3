package com.gok.pboot.pms.entity.vo;

import com.gok.pboot.pms.common.base.PageRequest;
import com.gok.pboot.pms.entity.bo.DailyReviewReuseAndDeliveryViewTotalBO;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * 复用交付工时审核-查看日报- 统计
 * <AUTHOR>
 * @version 1.3.2
 */
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class DailyReviewReuseAndDeliveryViewTotalVO extends PageRequest {

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 审核人数
     */
    private Integer approvalNum;

    /**
     * 汇总工时（人天）
     */
    private BigDecimal aggregatedDays;

    private static final DailyReviewReuseAndDeliveryViewTotalVO EMPTY = new DailyReviewReuseAndDeliveryViewTotalVO(
            StringUtils.EMPTY, StringUtils.EMPTY, 0, BigDecimal.ZERO
    );

    public static DailyReviewReuseAndDeliveryViewTotalVO empty() {
        return EMPTY;
    }

    public static DailyReviewReuseAndDeliveryViewTotalVO from(DailyReviewReuseAndDeliveryViewTotalBO bo) {
        DailyReviewReuseAndDeliveryViewTotalVO result = new DailyReviewReuseAndDeliveryViewTotalVO();

        result.setProjectId(String.valueOf(bo.getProjectId()));
        result.setProjectName(bo.getProjectName());
        result.setApprovalNum(Math.max(bo.getApprovalNum(), 0));
        result.setAggregatedDays(bo.getAggregatedDays());

        return result;
    }
}
