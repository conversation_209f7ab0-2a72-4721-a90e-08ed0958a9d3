<?xml version="1.0" encoding="UTF-8"?>

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.gok.business.cloud</groupId>
        <artifactId>gok-business-cloud</artifactId>
        <version>1.0.160</version>
    </parent>

    <artifactId>gok-business-pms</artifactId>
    <packaging>pom</packaging>
    <version>1.0.160</version>
    <description>业务一体化服务</description>

    <modules>
        <module>gok-business-pms-biz</module>
        <module>gok-business-pms-api</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>com.gok.components</groupId>
            <artifactId>gok-components-common</artifactId>
            <version>1.0.41</version>
        </dependency>
    </dependencies>

    <repositories>
        <repository>
            <id>gok-public</id>
            <url>https://nexus-dev.goktech.cn/repository/maven-public/</url>
        </repository>
    </repositories>
</project>

